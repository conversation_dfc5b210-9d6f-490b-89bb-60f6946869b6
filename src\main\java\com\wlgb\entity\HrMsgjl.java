package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: hr_msgjl
 * @Author: jeecg-boot
 * @Date:   2021-09-17
 * @Version: V1.0
 */
@Data
@Table(name = "hr_msgjl")
public class HrMsgjl {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
	/**面试信息订单id*/
    private java.lang.String mszid;
	/**面试官姓名*/
    private java.lang.String msgxm;
	/**面试官id*/
    private java.lang.String msgid;
	/**发送面试通知时间*/
    private java.lang.String fsmssj;
	/**工具类型：pc：0，手机：1*/
    private java.lang.String gjlx;
}
