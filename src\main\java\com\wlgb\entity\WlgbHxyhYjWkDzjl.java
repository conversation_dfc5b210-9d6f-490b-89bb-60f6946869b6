package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Date 2022/12/11 21:11
 * @Version 1.0
 */
@Data
@Table(name = "wlgb_hxyh_yjwkdzjl")
public class WlgbHxyhYjWkDzjl {
    /**主键*/
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**创建日期*/
    private java.util.Date createTime;
    /**更新日期*/
    private java.util.Date updateTime;
    /**未知*/
    private String totalDiscount;
    /**入账的时间，格式 "yyyyMMdd"*/
    private String accountDate;
    /**该笔订单的手续费总 和(参考)，单位为 RMB-Yuan。取值范围 为 [0.01 ， *********.00]，精确 到小数点后两位。*/
    private Double fee;
    /**签名字符串，Base64 编码*/
    private String sign;
    /**未知*/
    private String isDiscount;
    /**通知类型*/
    private String notifyType;
    /**该笔订单的合作方手 续费(参考)，单位为 RMB-Yuan。取值范围 为 [0.01 ， *********.00]，精确 到小数点后两位。*/
    private Double partnerFee;
    /**交易目前所处的状 态。成功状态的值： TRADE_SUCCESS|TRAD E_CLOSED 等具体详 情看下文中的交易状 态详解*/
    private String tradeStatus;
    /**未知*/
    private String totalDiscountFee;
    /**签名类型*/
    private String signType;
    /**发送请求的时间，格 式 "yyyy-MM-dd HH:mm:ss"*/
    private String notifyTime;
    /**该笔订单的收款方手 续费(参考)，单位为 RMB-Yuan。取值范围 为 [0.01 ， *********.00]，精确 到小数点后两位。*/
    private Double payeeFee;
    /**公用回传参数*/
    private String extraCommonParam;
    /**未知*/
    private String cardType;
    /**支付宝用户 Uid*/
    private String buyerUserId;
    /**银盛支付合作商户网 站唯一订单号。*/
    private String outTradeNo;
    /**该笔订单的资金总 额 ， 单 位 为 RMB-Yuan。取值范围 为 [0.01 ， *********.00]，精确 到小数点后两位。*/
    private Double totalAmount;
    /**该交易在银盛支付系 统中的交易流水号。*/
    private String tradeNo;
    /**结算金额*/
    private Double settlementAmount;
    /**支付网关编号*/
    private String paygateNo;
    /**支付宝账户*/
    private String buyerLogonId;
    /**该笔订单的付款方手 续费(参考)，单位为 RMB-Yuan。取值范围 为 [0.01 ， *********.00]，精确 到小数点后两位。*/
    private String payerFee;
    /**渠道返回流水号*/
    private String channelRecvSn;
    /**发往渠道流水号*/
    private String channelSendSn;
    /**下单订单编号*/
    private String ddbh;
    /**是否录入金蝶*/
    private Integer sflrjd;
}
