package com.wlgb.service.impl;

import com.wlgb.entity.WlgbJdDjLsjlb;
import com.wlgb.entity.WlgbJdDjbbd;
import com.wlgb.mapper.WlgbJdDjbbdMapper;
import com.wlgb.service.WlgbJdDjbbdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月17日 20:46
 */
@Service
@Slf4j
public class WlgbJdDjbbdServiceImpl implements WlgbJdDjbbdService {
    @Resource
    private WlgbJdDjbbdMapper wlgbJdDjbbdMapper;

    @Override
    public Integer queryCountByWybs(String wybs) {
        Example example = new Example(WlgbJdDjbbd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("djwybs", wybs);
        return wlgbJdDjbbdMapper.selectCountByExample(example);
    }

    @Override
    public void save(WlgbJdDjbbd wlgbJdDjbbd) {
        wlgbJdDjbbd.setCreateTime(new Date());
        boolean cfTest = false;
        try {
            wlgbJdDjbbdMapper.insertSelective(wlgbJdDjbbd);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbJdDjbbdMapper.insertSelective(wlgbJdDjbbd);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，insert：参数" + wlgbJdDjbbd.toString());
                    }
                }
            }
        }
    }

    @Override
    public void updateById(WlgbJdDjbbd wlgbJdDjbbd) {
        wlgbJdDjbbd.setUpdateTime(new Date());
        boolean cfTest = false;
        try {
            wlgbJdDjbbdMapper.updateByPrimaryKeySelective(wlgbJdDjbbd);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbJdDjbbdMapper.updateByPrimaryKeySelective(wlgbJdDjbbd);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，update：参数" + wlgbJdDjbbd.toString());
                    }
                }
            }
        }
    }

    @Override
    public WlgbJdDjbbd queryByLshAndFormId(String lsh, String formInstId) {
        Example example = new Example(WlgbJdDjbbd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lsh", lsh);
        criteria.andEqualTo("formInstId", formInstId);
        return wlgbJdDjbbdMapper.selectOneByExample(example);
    }

    @Override
    public Integer queryCountByLshAndFormId(String lsh, String formInstId) {
        Example example = new Example(WlgbJdDjbbd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lsh", lsh);
        criteria.andEqualTo("formInstId", formInstId);
        return wlgbJdDjbbdMapper.selectCountByExample(example);
    }

    @Override
    public WlgbJdDjbbd queryByFormIdAndDjLx(String formId, Integer djlx) {
        Example example = new Example(WlgbJdDjbbd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("djlx", djlx);
        criteria.andEqualTo("formInstId", formId);
        return wlgbJdDjbbdMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdDjbbd> queryByLshAndSfSc(String lsh) {
        Example example = new Example(WlgbJdDjbbd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lsh", lsh);
        criteria.andEqualTo("sfsc", 0);
        return wlgbJdDjbbdMapper.selectByExample(example);
    }

    @Override
    public List<WlgbJdDjbbd> queryByLshAndSfScAndDjLx(String lsh, Integer djLx) {
        Example example = new Example(WlgbJdDjbbd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lsh", lsh);
        criteria.andEqualTo("sfsc", 0);
        criteria.andEqualTo("djlx", djLx);
        return wlgbJdDjbbdMapper.selectByExample(example);
    }

    @Override
    public List<WlgbJdDjbbd> queryByLshAndSfScAndSfXd(String lsh, Integer sfxd) {
        Example example = new Example(WlgbJdDjbbd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lsh", lsh);
        criteria.andEqualTo("sfsc", 0);
        criteria.andEqualTo("sfxd", sfxd);
        return wlgbJdDjbbdMapper.selectByExample(example);
    }

    @Override
    public WlgbJdDjbbd queryByLshAndSkBhAndSfScAndDjLx(String lsh, String skBh, Integer sfSc, Integer djLx) {
        Example example = new Example(WlgbJdDjbbd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lsh", lsh);
        criteria.andEqualTo("skbh", skBh);
        criteria.andEqualTo("sfsc", sfSc);
        criteria.andEqualTo("djlx", djLx);
        return wlgbJdDjbbdMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdDjbbd> queryByDdBhAndSfSc(String ddbh) {
        Example example = new Example(WlgbJdDjbbd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddbh", ddbh);
        criteria.andEqualTo("sfsc", 0);
        return wlgbJdDjbbdMapper.selectByExample(example);
    }

    @Override
    public List<WlgbJdDjbbd> queryDjYxdWxgByLsh(String lsh) {
        Example example = new Example(WlgbJdDjbbd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lsh", lsh);
        criteria.andEqualTo("sfxd", 0);
        return wlgbJdDjbbdMapper.selectByExample(example);
    }
}
