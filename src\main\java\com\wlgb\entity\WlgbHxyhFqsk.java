package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月22日 18:39
 */
@Data
@Table(name = "wlgb_hxyh_fqsk")
public class WlgbHxyhFqsk {
    /**主键*/
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
    /**创建人*/
    private java.lang.String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
    /**更新人*/
    private java.lang.String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
    /**所属部门*/
    private java.lang.String sysOrgCode;
    /**订单编号*/
    private java.lang.String ddbh;
    /**费用名称*/
    private java.lang.String fymc;
    /**金额*/
    private java.lang.Double je;
    /**付款方式*/
    private java.lang.String fkfs;
    /**提交人*/
    private java.lang.String tjr;
    /**提交人id*/
    private java.lang.String tjrid;
    /**银盛编号*/
    private java.lang.String yxbh;
    /**提交时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date tjsj;
    /**二维码链接*/
    private java.lang.String ewm;
    /**微信支付appId*/
    private java.lang.String appid;
    /**微信支付随机时间戳*/
    private java.lang.String timestamp;
    /**微信支付随机字符串*/
    private java.lang.String noncestr;
    /**微信支付订单详情扩展字符串*/
    private java.lang.String package1;
    /**微信支付签名方式*/
    private java.lang.String signtype;
    /**微信支付签名*/
    private java.lang.String paysign;
    /**支付状态*/
    private java.lang.String zfzt;
    /**定金类型*/
    private java.lang.String djlx;
}
