package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "fwq_dy_yq")
public class FwqDyYq {
    @Id
    private java.lang.Integer id;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
    private java.lang.String verifytoken;
    private java.lang.String orderid;
    private java.lang.String encryptedcode;
    private java.lang.String expiretime;
    private java.lang.String skutitle;
    private java.lang.String skugroupontype;
    private java.lang.Double originalamount;
    private java.lang.Double listmarketamount;
    private java.lang.Double payamount;
    private java.lang.Double couponpayamount;
    private java.lang.String sfsc;
    private java.lang.String zt;
}
