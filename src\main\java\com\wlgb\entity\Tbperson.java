package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: tb_person
 */
@Data
@Table(name = "tb_person")
public class Tbperson {
    @Id
    @KeySql(useGeneratedKeys = true)
    private String pid;
    private String ptx;
    private String pname;
    private String psfid;
    private String pgsid;
    private String pfqid;
    private String pgw;
    private Integer pqx;
    private String dduserid;
    private String quanxianzpd;
}
