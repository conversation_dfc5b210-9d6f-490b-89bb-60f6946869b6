package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmQbkhYjjl;
import com.wlgb.mapper.CrmQbkhYjjlMapper;
import com.wlgb.service2.CrmQbkhYjjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 18:09
 */
@Service
@DS(value = "second")
public class CrmQbkhYjjlServiceImpl implements CrmQbkhYjjlService {
    @Resource
    private CrmQbkhYjjlMapper crmQbkhYjjlMapper;

    @Override
    public void save(CrmQbkhYjjl crmQbkhYjjl) {
        crmQbkhYjjlMapper.insertSelective(crmQbkhYjjl);
    }

    @Override
    public void updateById(CrmQbkhYjjl crmQbkhYjjl) {
        crmQbkhYjjlMapper.updateByPrimaryKeySelective(crmQbkhYjjl);
    }

    @Override
    public CrmQbkhYjjl queryCrmQbKhYjJlByCrmQbKhYjJl(CrmQbkhYjjl crmQbkhYjjl) {
        return crmQbkhYjjlMapper.selectOne(crmQbkhYjjl);
    }
}
