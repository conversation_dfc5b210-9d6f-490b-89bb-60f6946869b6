package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdBsmcXgjl;
import com.wlgb.mapper.WlgbJdBsmcXgjlMapper;
import com.wlgb.service.WlgbJdBsmcXgjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 22:07
 */
@Service
public class WlgbJdBsmcXgjlServiceImpl implements WlgbJdBsmcXgjlService {
    @Resource
    private WlgbJdBsmcXgjlMapper wlgbJdBsmcXgjlMapper;

    @Override
    public WlgbJdBsmcXgjl queryByYnameAndSfSc(String yname, Integer sfsc) {
        Example example = new Example(WlgbJdBsmcXgjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yname", yname);
        criteria.andEqualTo("sfsc", sfsc);
        return wlgbJdBsmcXgjlMapper.selectOneByExample(example);
    }

    @Override
    public WlgbJdBsmcXgjl queryByVidAndSfSc(String vid, Integer sfsc) {
        Example example = new Example(WlgbJdBsmcXgjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("vid", vid);
        criteria.andEqualTo("sfsc", sfsc);
        return wlgbJdBsmcXgjlMapper.selectOneByExample(example);
    }

    @Override
    public void save(WlgbJdBsmcXgjl wlgbJdBsmcXgjl) {
        wlgbJdBsmcXgjl.setId(IdConfig.uuId());
        wlgbJdBsmcXgjl.setCreateTime(new Date());
        wlgbJdBsmcXgjlMapper.insertSelective(wlgbJdBsmcXgjl);
    }

    @Override
    public void updateById(WlgbJdBsmcXgjl wlgbJdBsmcXgjl) {
        wlgbJdBsmcXgjl.setUpdateTime(new Date());
        wlgbJdBsmcXgjlMapper.updateByPrimaryKeySelective(wlgbJdBsmcXgjl);
    }
}
