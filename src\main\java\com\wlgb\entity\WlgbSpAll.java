package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/6 11:54
 */
@Data
@Table(name = "wlgb_sp_all")
public class WlgbSpAll {
    /**主键*/
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
    /**创建人*/
    private java.lang.String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
    /**更新人*/
    private java.lang.String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
    /**所属部门*/
    private java.lang.String sysOrgCode;
    /**实际发起人姓名*/
    private java.lang.String sjfqrname;
    /**实际发起人userid*/
    private java.lang.String sjfqruserid;
    /**抄送给关注人姓名*/
    private java.lang.String csrname;
    /**抄送人userid*/
    private java.lang.String csruserid;
    /**审批类型*/
    private java.lang.String splx;
    /**审批状态*/
    private java.lang.String spzt;
    /**发起时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date fqsj;
    /**费用使用方*/
    private java.lang.String fysyf;
    /**实际金额*/
    private java.lang.Double sjje;
    /**费用类别*/
    private java.lang.String fylb;
    /**审批编号*/
    private java.lang.String spbh;

    /**审批链接*/
    private java.lang.String spUrl;
    /**考勤打卡人*/
    private java.lang.String kqDkr;
    /**考勤打卡人id*/
    private java.lang.String kqDkrid;
    /**考勤打卡时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date kqDksj;
    /**考勤打卡地点*/
    private java.lang.String kqDkdd;

    /**是否删除*/
    private java.lang.Integer sfsc;
}
