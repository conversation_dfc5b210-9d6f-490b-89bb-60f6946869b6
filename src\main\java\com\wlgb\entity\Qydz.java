package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "ldt_qydz")
public class Qydz {
	/**
	 *
	 */
	@Id
	@KeySql(useGeneratedKeys = true)
	private Integer id;

	/**
	 * 用户id
	 */
	private String userid;

	/**
	 * 姓名
	 */
	private String xm;

	/**
	 * 大区
	 */
	private String dq;

	/**
	 * 区域
	 */
	private String qy;

}