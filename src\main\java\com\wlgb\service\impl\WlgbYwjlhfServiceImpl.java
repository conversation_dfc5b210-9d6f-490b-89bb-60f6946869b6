package com.wlgb.service.impl;

import com.wlgb.entity.WlgbYwjlhf;
import com.wlgb.mapper.WlgbYwjlhfMapper;
import com.wlgb.service.WlgbYwjlhfService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/22 15:54
 */
@Service
public class WlgbYwjlhfServiceImpl implements WlgbYwjlhfService {
    @Resource
    private WlgbYwjlhfMapper wlgbYwjlhfMapper;

    @Override
    public void save(WlgbYwjlhf wlgbYwjlhf) {
        wlgbYwjlhfMapper.insertSelective(wlgbYwjlhf);
    }

    @Override
    public void removeById(Integer id) {
        wlgbYwjlhfMapper.deleteByPrimaryKey(id);
    }

    @Override
    public WlgbYwjlhf queryByDdbh(String ddbh) {
        Example example = new Example(WlgbYwjlhf.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddbh", ddbh);
        return wlgbYwjlhfMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbYwjlhf> queryListByDdbh(String ddbh) {
        Example example = new Example(WlgbYwjlhf.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddbh", ddbh);
        return wlgbYwjlhfMapper.selectByExample(example);
    }
}
