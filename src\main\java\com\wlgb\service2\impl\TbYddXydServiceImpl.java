package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbYddXyd;
import com.wlgb.mapper.TbYddXydMapper;
import com.wlgb.service2.TbYddXydService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/27 15:23
 */
@Service
@DS(value = "second")
public class TbYddXydServiceImpl implements TbYddXydService {
    @Resource
    private TbYddXydMapper tbYddXydMapper;

    @Override
    public void save(TbYddXyd tbYddXyd) {
        tbYddXyd.setCreateTime(new Date());
        tbYddXydMapper.insertSelective(tbYddXyd);
    }

    @Override
    public void updateById(TbYddXyd tbYddXyd) {
        tbYddXyd.setUpdateTime(new Date());
        tbYddXydMapper.updateByPrimaryKeySelective(tbYddXyd);
    }

    @Override
    public void deleteByTbYddXyd(TbYddXyd tbYddXyd) {
        tbYddXyd.setDeleteTime(new Date());
        tbYddXydMapper.updateByPrimaryKeySelective(tbYddXyd);
    }

    @Override
    public TbYddXyd queryTbYddXydByTbYddXyd(TbYddXyd tbYddXyd) {
        return tbYddXydMapper.selectOne(tbYddXyd);
    }

    @Override
    public TbYddXyd queryTbYddXydById(String id) {
        return tbYddXydMapper.selectByPrimaryKey(id);
    }
}
