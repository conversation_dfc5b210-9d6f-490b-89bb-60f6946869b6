package com.wlgb.config;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMediaUploadRequest;
import com.dingtalk.api.response.OapiMediaUploadResponse;
import com.taobao.api.FileItem;

import java.io.File;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/1 10:34
 */
public class DingPan {

    /**
     * 上传图片至钉盘
     *
     * @param file    需要上传的文件
     * @param dingkey 钉钉配置
     */
    public static String dingpan(File file, Dingkey dingkey) throws Exception {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/media/upload");
        OapiMediaUploadRequest request = new OapiMediaUploadRequest();
        request.setType("image");
        request.setMedia(new FileItem(file.getPath()));
        OapiMediaUploadResponse response = client.execute(request, DingToken.token(dingkey));
        System.out.println(response.getMediaId());

        return response.getMediaId();
    }
}
