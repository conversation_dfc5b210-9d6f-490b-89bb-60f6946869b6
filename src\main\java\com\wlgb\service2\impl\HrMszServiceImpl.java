package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.HrMsz;
import com.wlgb.mapper.HrMszMapper;
import com.wlgb.service2.HrMszService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/16 16:29
 */
@Service
@DS(value = "second")
public class HrMszServiceImpl implements HrMszService {
    @Resource
    private HrMszMapper hrMszMapper;

    @Override
    public void save(HrMsz hrMsz) {
        hrMszMapper.insertSelective(hrMsz);
    }

    @Override
    public void updateById(HrMsz hrMsz) {
        hrMszMapper.updateByPrimaryKeySelective(hrMsz);
    }

    @Override
    public Integer queryXySfCz(String tel) {
        HrMsz hrMsz = new HrMsz();
        hrMsz.setSfsc("0");
        hrMsz.setTel(tel);
        return hrMszMapper.selectCount(hrMsz);
    }

    @Override
    public HrMsz queryOneByHrMsz(HrMsz hrMsz) {
        return hrMszMapper.selectOne(hrMsz);
    }
}
