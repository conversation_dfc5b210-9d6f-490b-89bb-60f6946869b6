package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "fwq_dy_account")
public class FwqDyAccount {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    private String accountid;
    private String bz;
    private String clientkey;
    private String clientsecret;



}
