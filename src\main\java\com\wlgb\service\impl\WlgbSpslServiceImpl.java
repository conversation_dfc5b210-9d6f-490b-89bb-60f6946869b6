package com.wlgb.service.impl;

import com.wlgb.entity.WlgbSpsl;
import com.wlgb.mapper.WlgbSpslMapper;
import com.wlgb.service.WlgbSpslService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/18 19:24
 */
@Service
public class WlgbSpslServiceImpl implements WlgbSpslService {
    @Resource
    private WlgbSpslMapper wlgbSpslMapper;

    @Override
    public void save(WlgbSpsl wlgbSpsl) {
        wlgbSpsl.setCreateTime(new Date());
        wlgbSpslMapper.insertSelective(wlgbSpsl);
    }

    @Override
    public void removeById(String id) {
        wlgbSpslMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<WlgbSpsl> queryByDdbh(String ddbh) {
        Example example = new Example(WlgbSpsl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddbh", ddbh);
        criteria.andEqualTo("bz", "商品购买");
        return wlgbSpslMapper.selectByExample(example);
    }
}
