package com.wlgb.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.wlgb.entity.TbXyd;
import com.wlgb.entity.*;
import com.wlgb.mapper.WlgbJdDjLsjlbMapper;
import com.wlgb.mapper1.WeiLianDdXcxMapper;
import com.wlgb.service.WlgbJdDjLsjlbService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/12/11 19:57
 * @Version 1.0
 */
@Service
@Slf4j
public class WlgbJdDjLsjlbServiceImpl implements WlgbJdDjLsjlbService {
    @Resource
    private WlgbJdDjLsjlbMapper wlgbJdDjLsjlbMapper;
    @Resource
    private WeiLianDdXcxMapper weiLianDdXcxMapper;

    @Override
    public void save(WlgbJdDjLsjlb wlgbJdDjLsjlb) {
        wlgbJdDjLsjlb.setCreateTime(new Date());
        boolean cfTest = false;
        try {
            wlgbJdDjLsjlbMapper.insertSelective(wlgbJdDjLsjlb);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbJdDjLsjlbMapper.insertSelective(wlgbJdDjLsjlb);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，insert：参数" + wlgbJdDjLsjlb.toString());
                    }
                }
            }
        }
    }

    @Override
    public WlgbJdDjLsjlb queryBySkBhAndSfSc(String skbh, Integer sfsc) {
        Example example = new Example(WlgbJdDjLsjlb.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("skbh", skbh);
        criteria.andEqualTo("djlx", 1);
        if (sfsc != null) {
            criteria.andEqualTo("sfsc", sfsc);
        }
        return wlgbJdDjLsjlbMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbHxyhDzjl> queryDzWtb() {
        return weiLianDdXcxMapper.queryDzWtb();
    }

    @Override
    public void updateById(WlgbJdDjLsjlb wlgbJdDjLsjlb) {
        wlgbJdDjLsjlb.setUpdateTime(new Date());
        boolean cfTest = false;
        try {
            wlgbJdDjLsjlbMapper.updateByPrimaryKeySelective(wlgbJdDjLsjlb);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbJdDjLsjlbMapper.updateByPrimaryKeySelective(wlgbJdDjLsjlb);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，update：参数" + wlgbJdDjLsjlb.toString());
                    }
                }
            }
        }
    }

    @Override
    public List<Map<String, Object>> queryXdWxgZt() {
        return weiLianDdXcxMapper.queryXdWxgZt();
    }

    @Override
    public TbXyd queryXydByDdBh(String ddbh) {
        return weiLianDdXcxMapper.queryXydByDdBh(ddbh);
    }

    @Override
    public Integer queryQrdSfCz(String qxydid) {
        return weiLianDdXcxMapper.queryQrdSfCz(qxydid);
    }

    @Override
    public Double queryQrdGsSr(String qxydid) {
        return weiLianDdXcxMapper.queryQrdGsSr(qxydid);
    }

    @Override
    public List<WlgbMtLog> queryXsDjWlr() {
        return weiLianDdXcxMapper.queryXsDjWlr();
    }

    @Override
    public String queryYqJl(String qm) {
        return weiLianDdXcxMapper.queryYqJl(qm);
    }

    @Override
    public JSONObject queryMtMd(WlgbMtLog wlgbMtLog) {
        return weiLianDdXcxMapper.queryMtMd(wlgbMtLog);
    }

    @Override
    public WlgbHxyhDzjl queryXxDjBySkBh(String skbh) {
        return weiLianDdXcxMapper.queryXxDjBySkBh(skbh);
    }

    @Override
    public WlgbMtLog queryXsDjByQh(String yqqm) {
        return weiLianDdXcxMapper.queryXsDjByQh(yqqm);
    }

    @Override
    public List<WlgbJdDjbbd> queryDjYxdWxgByLsh(String lsh) {
        return weiLianDdXcxMapper.queryDjYxdWxgByLsh(lsh);
    }

    @Override
    public void updateCrmXxZt(String crmbh) {
        weiLianDdXcxMapper.updateCrmXxZt(crmbh);
    }

    @Override
    public void updateBatchById(List<WlgbJdDjLsjlb> list) {
        list.forEach(l -> {
            l.setUpdateTime(new Date());
            boolean cfTest = false;
            try {
                wlgbJdDjLsjlbMapper.updateByPrimaryKeySelective(l);
            } catch (Exception e) {
                e.printStackTrace();
                cfTest = true;
            }
            if (cfTest) {
                for (int i = 0; i < 5; i++) {
                    boolean cfJs = true;
                    try {
                        wlgbJdDjLsjlbMapper.updateByPrimaryKeySelective(l);
                    } catch (Exception e) {
                        e.printStackTrace();
                        cfJs = false;
                    }
                    if (cfJs) {
                        break;
                    } else {
                        if (i == 4) {
                            log.info("**************重复四次还是失败**************，update：参数" + l.toString());
                        }
                    }
                }
            }
        });
    }

    @Override
    public List<WlgbJdDjLsjlb> queryListByKhDhAndSfSc(String khdh, Integer sfsc) {
        Example example = new Example(WlgbJdDjLsjlb.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("khdh", khdh);
        criteria.andEqualTo("sfsc", sfsc);
        return wlgbJdDjLsjlbMapper.selectByExample(example);
    }

    @Override
    public List<WlgbJdDjLsjlb> queryListByKhDhAndSfScAndSfDz(String khdh, Integer sfsc, Integer sfDz, Integer yxq) {
        Example example = new Example(WlgbJdDjLsjlb.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("khdh", khdh);
        criteria.andEqualTo("sfsc", sfsc);
        criteria.andEqualTo("sfdz", sfDz);
        //发起时间超过1小时就不在查询了
        if (yxq > 0) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.HOUR_OF_DAY, -1);
            criteria.andGreaterThan("fqsj", calendar.getTime());
        }
        return wlgbJdDjLsjlbMapper.selectByExample(example);
    }

    @Override
    public List<WlgbJdDjLsjlb> queryListByCrmBhAndSfScAndSfDz(String crmBh, Integer sfSc, Integer sfDz, String uuid) {
        Example example = new Example(WlgbJdDjLsjlb.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("crmbh", crmBh);
        criteria.andEqualTo("sfsc", sfSc);
        criteria.andEqualTo("sfdz", sfDz);
        criteria.andEqualTo("uuid", uuid);
        return wlgbJdDjLsjlbMapper.selectByExample(example);
    }

    @Override
    public List<WlgbJdDjLsjlb> queryListByWlgbJdDjLsjlb(WlgbJdDjLsjlb wlgbJdDjLsjlb) {
        return wlgbJdDjLsjlbMapper.select(wlgbJdDjLsjlb);
    }

    @Override
    public WlgbJdDjLsjlb queryByLshAndSfScAndDjLxAndSfDz(String lsh, Integer sfsc, Integer djLx, Integer sfDz) {
        Example example = new Example(WlgbJdDjLsjlb.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("lsh", lsh);
        criteria.andEqualTo("sfsc", sfsc);
        criteria.andEqualTo("djlx", djLx);
        criteria.andEqualTo("sfdz", sfDz);
        return wlgbJdDjLsjlbMapper.selectOneByExample(example);
    }
}
