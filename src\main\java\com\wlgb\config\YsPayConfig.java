package com.wlgb.config;

import com.alibaba.fastjson.JSONObject;
import com.eptok.yspay.opensdkjava.fund.MercFundApi;
import com.eptok.yspay.opensdkjava.orderpay.OrderQueryApi;
import com.eptok.yspay.opensdkjava.orderpay.RefundApi;
import com.eptok.yspay.opensdkjava.orderpay.ScanCodePayApi;
import com.eptok.yspay.opensdkjava.pojo.vo.OnlineReqDataVo;

import java.io.File;
import java.io.FileNotFoundException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 16:00
 */
public class YsPayConfig {

    /**
     * 查询订单
     */
    public static JSONObject queryDd(JSONObject jsonObject) throws FileNotFoundException {

        //商户号
        jsonObject.put("partnerId", "***************");
        String sy = PathUtil.getClassResources() + "static\\wlgb.pfx";
        String gy = PathUtil.getClassResources() + "static\\businessgate.cer";
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");

        /**1、组装调用SDK查询订单状态，OrderQueryApi.singleOrderQuery需要的参数*/
        OnlineReqDataVo reqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        reqDataVo.setReqUrl("https://qrcode.ysepay.com/gateway.do");

        //商户在银盛支付平台开设的用户号[商户号]
        reqDataVo.setPartnerId(jsonObject.getString("partnerId"));
        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        reqDataVo.setPrivateKeyFilePath(jsonObject.getString("privateKeyFilePath"));

        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        reqDataVo.setPrivateKeyPassword(jsonObject.getString("privateKeyPassword"));

        //银盛公钥证书路径: 证书入网申请后随邮件发放
        reqDataVo.setYsPublicKeyFilePath(jsonObject.getString("publicKeyFilePath"));

        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        bizContent.put("out_trade_no", jsonObject.getString("out_trade_no"));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
//        bizContent.put("shopdate", jsonObject.getString("out_trade_no").substring(8));
        bizContent.put("shopdate", jsonObject.getString("shopdate"));
        //银盛交易号 特殊可选
        bizContent.put("trade_no", jsonObject.getString("trade_no"));
        reqDataVo.setParamData(bizContent);

        /** 2、OrderQueryApi.singleOrderQuery方法 */
        String result = null;
        try {

            result = OrderQueryApi.orderAndDetailQuery(reqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(result);
        return jsonObject1;
    }

    /**
     * 二维码支付
     */
    public static JSONObject ewmPay(JSONObject jsonObject, String url) {

        //商户号
        jsonObject.put("partnerId", "***************");
        //到账主动通知地址
        jsonObject.put("notifyUrl", url + "/ysdjtb/wlgb/money/djDz");
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");
        //收款方银盛支付用户号
        jsonObject.put("sellerId", "826551770110064");
        //收款方银盛支付客户名
        jsonObject.put("sellerName", "长沙威廉酒店管理有限公司");
        //业务代码
        jsonObject.put("businessCode", "00510030");

        OnlineReqDataVo onlineReqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        onlineReqDataVo.setReqUrl("https://qrcode.ysepay.com/gateway.do");
        //商户号
        onlineReqDataVo.setPartnerId(jsonObject.getString("partnerId"));
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        onlineReqDataVo.setNotifyUrl(jsonObject.getString("notifyUrl"));
        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        onlineReqDataVo.setPrivateKeyFilePath(jsonObject.getString("privateKeyFilePath"));
        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        onlineReqDataVo.setPrivateKeyPassword(jsonObject.getString("privateKeyPassword"));
        //银盛公钥证书路径: 证书入网申请后随邮件发放
        onlineReqDataVo.setYsPublicKeyFilePath(jsonObject.getString("publicKeyFilePath"));
        //交易类型，说明：1或者空：即时到账，2：担保交易
        onlineReqDataVo.setTranType("1");
        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //商品的标题/交易标题/订单标题/订单关键字等。该参数最长为250个汉字
        bizContent.put("subject", jsonObject.getString("skmc") != null ? jsonObject.getString("skmc") : "测试");
        //该笔订单的资金总额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
        bizContent.put("total_amount", jsonObject.getString("je"));
        //支持币种：CNY(人民币)、HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)
        //注:不填默认是CNY(人民币)，如是非跨境商户，币种不能填HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)中的任意一种外币币种
        bizContent.put("currency", "CNY");
        //收款方银盛支付用户号
        bizContent.put("seller_id", jsonObject.getString("sellerId"));
        //收款方银盛支付客户名
        bizContent.put("seller_name", jsonObject.getString("sellerName"));
        //设置未付款交易的超时时间，一旦超时，该笔交易就会自动被关闭。
        //(需申请业务权限，权限未开通情况下该参数不生效，默认未付款交易的超时时间为7d)取值范围：1m～15d。m-分钟，h-小时，d-天。该参数数值不接受小数点，如1.5h，可转换为90m。
        //注意：设置了未付款交易超时时间的情况下，若我司在限定时间内没有收到成功支付通知，则会关闭交易，关闭后该笔交易若付款方支付成功的情况下，会自动原路退款至付款方
        bizContent.put("timeout_express", "1h");
        //公用回传参数
        bizContent.put("extra_common_param", "extra_common_param");
        //业务代码，反扫D0为********
        bizContent.put("business_code", jsonObject.getString("businessCode"));
        //二维码行别，微信-1902000 支付宝-1903000 QQ扫码-1904000 银联扫码-9001002 招商银行 -3085840
        bizContent.put("bank_type", jsonObject.getString("fkfs"));
        //设置业务参数到biz_content
        onlineReqDataVo.setParamData(bizContent);

        String result = null;
        try {
            result = ScanCodePayApi.qrcodepay(onlineReqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(result);

        return jsonObject1;
    }

    /**
     * 二维码支付
     */
    public static JSONObject ewmPayYjAndWk(JSONObject jsonObject, String url) {

        //商户号
        jsonObject.put("partnerId", "***************");
        //到账主动通知地址
        jsonObject.put("notifyUrl", url + "/ysdjtb/wlgb/pay/yjAndWkDz");
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");
        //收款方银盛支付用户号
        jsonObject.put("sellerId", jsonObject.getString("home"));
        //收款方银盛支付客户名
        jsonObject.put("sellerName", "湖南价值跳动品牌管理有限公司");
        //业务代码
        jsonObject.put("businessCode", "00510030");

        OnlineReqDataVo onlineReqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        onlineReqDataVo.setReqUrl("https://qrcode.ysepay.com/gateway.do");
        //商户号
        onlineReqDataVo.setPartnerId(jsonObject.getString("partnerId"));
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        onlineReqDataVo.setNotifyUrl(jsonObject.getString("notifyUrl"));
        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        onlineReqDataVo.setPrivateKeyFilePath(jsonObject.getString("privateKeyFilePath"));
        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        onlineReqDataVo.setPrivateKeyPassword(jsonObject.getString("privateKeyPassword"));
        //银盛公钥证书路径: 证书入网申请后随邮件发放
        onlineReqDataVo.setYsPublicKeyFilePath(jsonObject.getString("publicKeyFilePath"));
        //交易类型，说明：1或者空：即时到账，2：担保交易
        onlineReqDataVo.setTranType("1");
        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //商品的标题/交易标题/订单标题/订单关键字等。该参数最长为250个汉字
        bizContent.put("subject", jsonObject.getString("skmc") != null ? jsonObject.getString("skmc") : "测试");
        //该笔订单的资金总额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
        bizContent.put("total_amount", jsonObject.getString("je"));
        //支持币种：CNY(人民币)、HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)
        //注:不填默认是CNY(人民币)，如是非跨境商户，币种不能填HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)中的任意一种外币币种
        bizContent.put("currency", "CNY");
        //收款方银盛支付用户号
        bizContent.put("seller_id", jsonObject.getString("sellerId"));
        //收款方银盛支付客户名
        bizContent.put("seller_name", jsonObject.getString("sellerName"));
        //设置未付款交易的超时时间，一旦超时，该笔交易就会自动被关闭。
        //(需申请业务权限，权限未开通情况下该参数不生效，默认未付款交易的超时时间为7d)取值范围：1m～15d。m-分钟，h-小时，d-天。该参数数值不接受小数点，如1.5h，可转换为90m。
        //注意：设置了未付款交易超时时间的情况下，若我司在限定时间内没有收到成功支付通知，则会关闭交易，关闭后该笔交易若付款方支付成功的情况下，会自动原路退款至付款方
        bizContent.put("timeout_express", "1h");
        //公用回传参数
        bizContent.put("extra_common_param", "extra_common_param");
        //业务代码，反扫D0为********
        bizContent.put("business_code", jsonObject.getString("businessCode"));
        //二维码行别，微信-1902000 支付宝-1903000 QQ扫码-1904000 银联扫码-9001002 招商银行 -3085840
        bizContent.put("bank_type", jsonObject.getString("fkfs"));
        //设置业务参数到biz_content
        onlineReqDataVo.setParamData(bizContent);

        String result = null;
        try {
            result = ScanCodePayApi.qrcodepay(onlineReqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(result);

        return jsonObject1;
    }

    /**
     * 微信支付
     */
    public static JSONObject weiXinPayYjAndWk(JSONObject jsonObject, String url) {
        //商户号
        jsonObject.put("partnerId", "***************");
        //到账主动通知地址
        jsonObject.put("notifyUrl", url + "/ysdjtb/wlgb/pay/yjAndWkDz");
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        //私钥证书路径
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");
        //收款方银盛支付用户号
        jsonObject.put("sellerId", jsonObject.getString("home"));
        //收款方银盛支付客户名
        jsonObject.put("sellerName", "湖南价值跳动品牌管理有限公司");
        //业务代码
        jsonObject.put("businessCode", "00510030");
        //公众号appId
        jsonObject.put("appId", "wx50fbc8ba4f887894");
        //交易失败通知地址
        jsonObject.put("failNotifyUrl", "http://wlgbsk.qianquan888.com/weilian-dingdingdzxcx" + "/wlgb/hxyh/hxYh4");
        /**1、组装调用SDK微信公众号，小程序支付ScanCodePayApi.weixinpay需要的参数*/
        OnlineReqDataVo reqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        reqDataVo.setReqUrl("https://qrcode.ysepay.com/gateway.do");

        //商户在银盛支付平台开设的用户号[商户号]
        reqDataVo.setPartnerId(jsonObject.getString("partnerId"));
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        reqDataVo.setNotifyUrl(jsonObject.getString("notifyUrl"));

        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        reqDataVo.setPrivateKeyFilePath(jsonObject.getString("privateKeyFilePath"));

        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        reqDataVo.setPrivateKeyPassword(jsonObject.getString("privateKeyPassword"));

        //银盛公钥证书路径: 证书入网申请后随邮件发放
        reqDataVo.setYsPublicKeyFilePath(jsonObject.getString("publicKeyFilePath"));

        //交易类型，说明：1或者空：即时到账，2：担保交易
        reqDataVo.setTranType("1");

        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //商品的标题/交易标题/订单标题/订单关键字等。该参数最长为250个汉字
        bizContent.put("subject", jsonObject.getString("skmc") != null ? jsonObject.getString("skmc") : "测试");
        //该笔订单的资金总额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
        bizContent.put("total_amount", jsonObject.getString("je"));
        //支持币种：CNY(人民币)、HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)
        //注:不填默认是CNY(人民币)，如是非跨境商户，币种不能填HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)中的任意一种外币币种
        bizContent.put("currency", "CNY");
        //收款方银盛支付用户号
        bizContent.put("seller_id", jsonObject.getString("sellerId"));
        //收款方银盛支付客户名
        bizContent.put("seller_name", jsonObject.getString("sellerName"));
        //设置未付款交易的超时时间，一旦超时，该笔交易就会自动被关闭。
        //(需申请业务权限，权限未开通情况下该参数不生效，默认未付款交易的超时时间为7d)取值范围：1m～15d。m-分钟，h-小时，d-天。该参数数值不接受小数点，如1.5h，可转换为90m。
        //注意：设置了未付款交易超时时间的情况下，若我司在限定时间内没有收到成功支付通知，则会关闭交易，关闭后该笔交易若付款方支付成功的情况下，会自动原路退款至付款方
        bizContent.put("timeout_express", "1h");
        //公用回传参数
        bizContent.put("extra_common_param", "extra_common_param");
        //业务代码
        bizContent.put("business_code", jsonObject.getString("businessCode"));
        //微信用户所关注商家公众号的openid
        bizContent.put("sub_openid", jsonObject.getString("openid"));
        //小程序支付，值为1，表示小程序支付；不传或值为2，表示公众账号内支付
        bizContent.put("is_minipg", "2");
        //当发起公众号支付时，值是微信公众平台基本配置中的AppID(应用ID)；当发起小程序支付时，值是对应小程序的AppID
        bizContent.put("appid", jsonObject.getString("appId"));
        //订单所属省编号（省市编号必须同时为空或者同时非空、并且需要符合层级关系）（特殊可空）
        bizContent.put("province", null);
        //订单所属市编号（省市编号必须同时为空或者同时非空、并且需要符合层级关系）（特殊可空）
        bizContent.put("city", null);
        //该笔订单的商户自主营销优惠金额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
//        bizContent.put("mer_amount","0.01");
        //是否限制信用卡。值为1表示禁用信用卡，0或为空表示不限制
        bizContent.put("limit_credit_pay", "0");
        //是否允许多次支付,Y：允许;N：不允许（参数为空或者Y时，默认该笔订单交易状态非成功状态时，支持选择其他支付方式继续付款，适用于收银台模式。 当为N时表示该笔订单交易状态为失败状态时，不支持选择其他支付方式继续付款，收银台模式下建议该参数为空）
        bizContent.put("allow_repeat_pay", "N");
        //失败通知地址（交易状态为失败时，银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3）注：只有不允许重复支付的交易才会通知
        bizContent.put("fail_notify_url", jsonObject.getString("failNotifyUrl"));


//        bizContent.put("submer_ip", "***************");//子商户ip

        //设置业务参数到biz_content
        reqDataVo.setParamData(bizContent);

        /** 2、调用ScanCodePayApi.weixinpay方法 */
        String result = null;
        try {
            result = ScanCodePayApi.weixinpay(reqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(result);

        return jsonObject1;
    }
    /**
     * 二维码支付
     */
    public static JSONObject ewmPayMdXf(JSONObject jsonObject, String url) {

        //商户号
        jsonObject.put("partnerId", "***************");
        //到账主动通知地址
        jsonObject.put("notifyUrl", url + "/ysdjtb/wlgb/pay/mdXfDz");
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");
        //收款方银盛支付用户号
        jsonObject.put("sellerId", jsonObject.getString("home"));
        //收款方银盛支付客户名
        jsonObject.put("sellerName", "湖南价值跳动品牌管理有限公司");
        //业务代码
        jsonObject.put("businessCode", "00510030");

        OnlineReqDataVo onlineReqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        onlineReqDataVo.setReqUrl("https://qrcode.ysepay.com/gateway.do");
        //商户号
        onlineReqDataVo.setPartnerId(jsonObject.getString("partnerId"));
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        onlineReqDataVo.setNotifyUrl(jsonObject.getString("notifyUrl"));
        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        onlineReqDataVo.setPrivateKeyFilePath(jsonObject.getString("privateKeyFilePath"));
        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        onlineReqDataVo.setPrivateKeyPassword(jsonObject.getString("privateKeyPassword"));
        //银盛公钥证书路径: 证书入网申请后随邮件发放
        onlineReqDataVo.setYsPublicKeyFilePath(jsonObject.getString("publicKeyFilePath"));
        //交易类型，说明：1或者空：即时到账，2：担保交易
        onlineReqDataVo.setTranType("1");
        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //商品的标题/交易标题/订单标题/订单关键字等。该参数最长为250个汉字
        bizContent.put("subject", jsonObject.getString("skmc") != null ? jsonObject.getString("skmc") : "测试");
        //该笔订单的资金总额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
        bizContent.put("total_amount", jsonObject.getString("je"));
        //支持币种：CNY(人民币)、HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)
        //注:不填默认是CNY(人民币)，如是非跨境商户，币种不能填HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)中的任意一种外币币种
        bizContent.put("currency", "CNY");
        //收款方银盛支付用户号
        bizContent.put("seller_id", jsonObject.getString("sellerId"));
        //收款方银盛支付客户名
        bizContent.put("seller_name", jsonObject.getString("sellerName"));
        //设置未付款交易的超时时间，一旦超时，该笔交易就会自动被关闭。
        //(需申请业务权限，权限未开通情况下该参数不生效，默认未付款交易的超时时间为7d)取值范围：1m～15d。m-分钟，h-小时，d-天。该参数数值不接受小数点，如1.5h，可转换为90m。
        //注意：设置了未付款交易超时时间的情况下，若我司在限定时间内没有收到成功支付通知，则会关闭交易，关闭后该笔交易若付款方支付成功的情况下，会自动原路退款至付款方
        bizContent.put("timeout_express", "1h");
        //公用回传参数
        bizContent.put("extra_common_param", "extra_common_param");
        //业务代码，反扫D0为********
        bizContent.put("business_code", jsonObject.getString("businessCode"));
        //二维码行别，微信-1902000 支付宝-1903000 QQ扫码-1904000 银联扫码-9001002 招商银行 -3085840
        bizContent.put("bank_type", jsonObject.getString("fkfs"));
        //设置业务参数到biz_content
        onlineReqDataVo.setParamData(bizContent);

        String result = null;
        try {
            result = ScanCodePayApi.qrcodepay(onlineReqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(result);

        return jsonObject1;
    }

    /**
     * 微信支付
     */
    public static JSONObject weiXinPayMdXf(JSONObject jsonObject, String url) {
        //商户号
        jsonObject.put("partnerId", "***************");
        //到账主动通知地址
        jsonObject.put("notifyUrl", url + "/ysdjtb/wlgb/pay/mdXfDz");
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        //私钥证书路径
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");
        //收款方银盛支付用户号
        jsonObject.put("sellerId", jsonObject.getString("home"));
        //收款方银盛支付客户名
        jsonObject.put("sellerName", "湖南价值跳动品牌管理有限公司");
        //业务代码
        jsonObject.put("businessCode", "00510030");
        //公众号appId
        jsonObject.put("appId", "wx50fbc8ba4f887894");
        //交易失败通知地址
        jsonObject.put("failNotifyUrl", "http://wlgbsk.qianquan888.com/weilian-dingdingdzxcx" + "/wlgb/hxyh/hxYh4");
        /**1、组装调用SDK微信公众号，小程序支付ScanCodePayApi.weixinpay需要的参数*/
        OnlineReqDataVo reqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        reqDataVo.setReqUrl("https://qrcode.ysepay.com/gateway.do");

        //商户在银盛支付平台开设的用户号[商户号]
        reqDataVo.setPartnerId(jsonObject.getString("partnerId"));
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        reqDataVo.setNotifyUrl(jsonObject.getString("notifyUrl"));

        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        reqDataVo.setPrivateKeyFilePath(jsonObject.getString("privateKeyFilePath"));

        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        reqDataVo.setPrivateKeyPassword(jsonObject.getString("privateKeyPassword"));

        //银盛公钥证书路径: 证书入网申请后随邮件发放
        reqDataVo.setYsPublicKeyFilePath(jsonObject.getString("publicKeyFilePath"));

        //交易类型，说明：1或者空：即时到账，2：担保交易
        reqDataVo.setTranType("1");

        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //商品的标题/交易标题/订单标题/订单关键字等。该参数最长为250个汉字
        bizContent.put("subject", jsonObject.getString("skmc") != null ? jsonObject.getString("skmc") : "测试");
        //该笔订单的资金总额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
        bizContent.put("total_amount", jsonObject.getString("je"));
        //支持币种：CNY(人民币)、HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)
        //注:不填默认是CNY(人民币)，如是非跨境商户，币种不能填HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)中的任意一种外币币种
        bizContent.put("currency", "CNY");
        //收款方银盛支付用户号
        bizContent.put("seller_id", jsonObject.getString("sellerId"));
        //收款方银盛支付客户名
        bizContent.put("seller_name", jsonObject.getString("sellerName"));
        //设置未付款交易的超时时间，一旦超时，该笔交易就会自动被关闭。
        //(需申请业务权限，权限未开通情况下该参数不生效，默认未付款交易的超时时间为7d)取值范围：1m～15d。m-分钟，h-小时，d-天。该参数数值不接受小数点，如1.5h，可转换为90m。
        //注意：设置了未付款交易超时时间的情况下，若我司在限定时间内没有收到成功支付通知，则会关闭交易，关闭后该笔交易若付款方支付成功的情况下，会自动原路退款至付款方
        bizContent.put("timeout_express", "1h");
        //公用回传参数
        bizContent.put("extra_common_param", "extra_common_param");
        //业务代码
        bizContent.put("business_code", jsonObject.getString("businessCode"));
        //微信用户所关注商家公众号的openid
        bizContent.put("sub_openid", jsonObject.getString("openid"));
        //小程序支付，值为1，表示小程序支付；不传或值为2，表示公众账号内支付
        bizContent.put("is_minipg", "2");
        //当发起公众号支付时，值是微信公众平台基本配置中的AppID(应用ID)；当发起小程序支付时，值是对应小程序的AppID
        bizContent.put("appid", jsonObject.getString("appId"));
        //订单所属省编号（省市编号必须同时为空或者同时非空、并且需要符合层级关系）（特殊可空）
        bizContent.put("province", null);
        //订单所属市编号（省市编号必须同时为空或者同时非空、并且需要符合层级关系）（特殊可空）
        bizContent.put("city", null);
        //该笔订单的商户自主营销优惠金额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
//        bizContent.put("mer_amount","0.01");
        //是否限制信用卡。值为1表示禁用信用卡，0或为空表示不限制
        bizContent.put("limit_credit_pay", "0");
        //是否允许多次支付,Y：允许;N：不允许（参数为空或者Y时，默认该笔订单交易状态非成功状态时，支持选择其他支付方式继续付款，适用于收银台模式。 当为N时表示该笔订单交易状态为失败状态时，不支持选择其他支付方式继续付款，收银台模式下建议该参数为空）
        bizContent.put("allow_repeat_pay", "N");
        //失败通知地址（交易状态为失败时，银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3）注：只有不允许重复支付的交易才会通知
        bizContent.put("fail_notify_url", jsonObject.getString("failNotifyUrl"));


//        bizContent.put("submer_ip", "***************");//子商户ip

        //设置业务参数到biz_content
        reqDataVo.setParamData(bizContent);

        /** 2、调用ScanCodePayApi.weixinpay方法 */
        String result = null;
        try {
            result = ScanCodePayApi.weixinpay(reqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(result);

        return jsonObject1;
    }

    /**
     * 二维码支付---预定单
     */
    public static JSONObject ewmPayYdd(JSONObject jsonObject, String url) {

        //商户号
        jsonObject.put("partnerId", "***************");
        //到账主动通知地址
        jsonObject.put("notifyUrl", url + "/ysdjtb/wlgb/xyd/djDz");
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");
        //收款方银盛支付用户号
        jsonObject.put("sellerId", "826551770110064");
        //收款方银盛支付客户名
        jsonObject.put("sellerName", "长沙威廉酒店管理有限公司");
        //业务代码
        jsonObject.put("businessCode", "00510030");

        OnlineReqDataVo onlineReqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        onlineReqDataVo.setReqUrl("https://qrcode.ysepay.com/gateway.do");
        //商户号
        onlineReqDataVo.setPartnerId(jsonObject.getString("partnerId"));
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        onlineReqDataVo.setNotifyUrl(jsonObject.getString("notifyUrl"));
        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        onlineReqDataVo.setPrivateKeyFilePath(jsonObject.getString("privateKeyFilePath"));
        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        onlineReqDataVo.setPrivateKeyPassword(jsonObject.getString("privateKeyPassword"));
        //银盛公钥证书路径: 证书入网申请后随邮件发放
        onlineReqDataVo.setYsPublicKeyFilePath(jsonObject.getString("publicKeyFilePath"));
        //交易类型，说明：1或者空：即时到账，2：担保交易
        onlineReqDataVo.setTranType("1");
        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //商品的标题/交易标题/订单标题/订单关键字等。该参数最长为250个汉字
        bizContent.put("subject", jsonObject.getString("skmc") != null ? jsonObject.getString("skmc") : "测试");
        //该笔订单的资金总额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
        bizContent.put("total_amount", jsonObject.getString("je"));
        //支持币种：CNY(人民币)、HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)
        //注:不填默认是CNY(人民币)，如是非跨境商户，币种不能填HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)中的任意一种外币币种
        bizContent.put("currency", "CNY");
        //收款方银盛支付用户号
        bizContent.put("seller_id", jsonObject.getString("sellerId"));
        //收款方银盛支付客户名
        bizContent.put("seller_name", jsonObject.getString("sellerName"));
        //设置未付款交易的超时时间，一旦超时，该笔交易就会自动被关闭。
        //(需申请业务权限，权限未开通情况下该参数不生效，默认未付款交易的超时时间为7d)取值范围：1m～15d。m-分钟，h-小时，d-天。该参数数值不接受小数点，如1.5h，可转换为90m。
        //注意：设置了未付款交易超时时间的情况下，若我司在限定时间内没有收到成功支付通知，则会关闭交易，关闭后该笔交易若付款方支付成功的情况下，会自动原路退款至付款方
        bizContent.put("timeout_express", "1h");
        //公用回传参数
        bizContent.put("extra_common_param", "extra_common_param");
        //业务代码，反扫D0为********
        bizContent.put("business_code", jsonObject.getString("businessCode"));
        //二维码行别，微信-1902000 支付宝-1903000 QQ扫码-1904000 银联扫码-9001002 招商银行 -3085840
        bizContent.put("bank_type", jsonObject.getString("fkfs"));
        //设置业务参数到biz_content
        onlineReqDataVo.setParamData(bizContent);

        String result = null;
        try {
            result = ScanCodePayApi.qrcodepay(onlineReqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(result);

        return jsonObject1;
    }

    /**
     * 二维码支付
     */
    public static JSONObject ewmPay1(JSONObject jsonObject) {
        //商户号
        jsonObject.put("partnerId", "***************");
        //到账主动通知地址
        jsonObject.put("notifyUrl", "http://wlgbsk.qianquan888.com/weilian-dingdingdzxcx" + "/wlgb/hxyh/hxYhDzHkTz");
        String sy = "D:\\cer\\wlgb.pfx";
        String gy = "D:\\cer\\businessgate.cer";
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");
        //收款方银盛支付用户号
        jsonObject.put("sellerId", "826551770110064");
        //收款方银盛支付客户名
        jsonObject.put("sellerName", "长沙威廉酒店管理有限公司");
        //业务代码
        jsonObject.put("businessCode", "00510030");

        OnlineReqDataVo onlineReqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        onlineReqDataVo.setReqUrl("https://qrcode.ysepay.com/gateway.do");
        //商户号
        onlineReqDataVo.setPartnerId(jsonObject.getString("partnerId"));
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        onlineReqDataVo.setNotifyUrl(jsonObject.getString("notifyUrl"));
        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        onlineReqDataVo.setPrivateKeyFilePath(jsonObject.getString("privateKeyFilePath"));
        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        onlineReqDataVo.setPrivateKeyPassword(jsonObject.getString("privateKeyPassword"));
        //银盛公钥证书路径: 证书入网申请后随邮件发放
        onlineReqDataVo.setYsPublicKeyFilePath(jsonObject.getString("publicKeyFilePath"));
        //交易类型，说明：1或者空：即时到账，2：担保交易
        onlineReqDataVo.setTranType("1");
        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //商品的标题/交易标题/订单标题/订单关键字等。该参数最长为250个汉字
        bizContent.put("subject", jsonObject.getString("skmc") != null ? jsonObject.getString("skmc") : "测试");
        //该笔订单的资金总额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
        bizContent.put("total_amount", jsonObject.getString("je"));
        //支持币种：CNY(人民币)、HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)
        //注:不填默认是CNY(人民币)，如是非跨境商户，币种不能填HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)中的任意一种外币币种
        bizContent.put("currency", "CNY");
        //收款方银盛支付用户号
        bizContent.put("seller_id", jsonObject.getString("sellerId"));
        //收款方银盛支付客户名
        bizContent.put("seller_name", jsonObject.getString("sellerName"));
        //设置未付款交易的超时时间，一旦超时，该笔交易就会自动被关闭。
        //(需申请业务权限，权限未开通情况下该参数不生效，默认未付款交易的超时时间为7d)取值范围：1m～15d。m-分钟，h-小时，d-天。该参数数值不接受小数点，如1.5h，可转换为90m。
        //注意：设置了未付款交易超时时间的情况下，若我司在限定时间内没有收到成功支付通知，则会关闭交易，关闭后该笔交易若付款方支付成功的情况下，会自动原路退款至付款方
        bizContent.put("timeout_express", "1h");
        //公用回传参数
        bizContent.put("extra_common_param", "extra_common_param");
        //业务代码，反扫D0为********
        bizContent.put("business_code", jsonObject.getString("businessCode"));
        //二维码行别，微信-1902000 支付宝-1903000 QQ扫码-1904000 银联扫码-9001002 招商银行 -3085840
        bizContent.put("bank_type", jsonObject.getString("fkfs"));
        //设置业务参数到biz_content
        onlineReqDataVo.setParamData(bizContent);

        String result = null;
        try {
            result = ScanCodePayApi.qrcodepay(onlineReqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(result);

        return jsonObject1;
    }

    /**
     * 微信支付
     */
    public static JSONObject weiXinPay(JSONObject jsonObject, String url) {
        //商户号
        jsonObject.put("partnerId", "***************");
        //到账主动通知地址
        jsonObject.put("notifyUrl", url + "/ysdjtb/wlgb/money/djDz");
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        //私钥证书路径
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");
        //收款方银盛支付用户号
        jsonObject.put("sellerId", "826551770110064");
        //收款方银盛支付客户名
        jsonObject.put("sellerName", "长沙威廉酒店管理有限公司");
        //业务代码
        jsonObject.put("businessCode", "00510030");
        //公众号appId
        jsonObject.put("appId", "wx3136bf5ad7ad7bd6");
        //交易失败通知地址
        jsonObject.put("failNotifyUrl", "http://wlgbsk.qianquan888.com/weilian-dingdingdzxcx" + "/wlgb/hxyh/hxYh4");
        /**1、组装调用SDK微信公众号，小程序支付ScanCodePayApi.weixinpay需要的参数*/
        OnlineReqDataVo reqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        reqDataVo.setReqUrl("https://qrcode.ysepay.com/gateway.do");

        //商户在银盛支付平台开设的用户号[商户号]
        reqDataVo.setPartnerId(jsonObject.getString("partnerId"));
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        reqDataVo.setNotifyUrl(jsonObject.getString("notifyUrl"));

        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        reqDataVo.setPrivateKeyFilePath(jsonObject.getString("privateKeyFilePath"));

        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        reqDataVo.setPrivateKeyPassword(jsonObject.getString("privateKeyPassword"));

        //银盛公钥证书路径: 证书入网申请后随邮件发放
        reqDataVo.setYsPublicKeyFilePath(jsonObject.getString("publicKeyFilePath"));

        //交易类型，说明：1或者空：即时到账，2：担保交易
        reqDataVo.setTranType("1");

        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //商品的标题/交易标题/订单标题/订单关键字等。该参数最长为250个汉字
        bizContent.put("subject", jsonObject.getString("skmc") != null ? jsonObject.getString("skmc") : "测试");
        //该笔订单的资金总额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
        bizContent.put("total_amount", jsonObject.getString("je"));
        //支持币种：CNY(人民币)、HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)
        //注:不填默认是CNY(人民币)，如是非跨境商户，币种不能填HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)中的任意一种外币币种
        bizContent.put("currency", "CNY");
        //收款方银盛支付用户号
        bizContent.put("seller_id", jsonObject.getString("sellerId"));
        //收款方银盛支付客户名
        bizContent.put("seller_name", jsonObject.getString("sellerName"));
        //设置未付款交易的超时时间，一旦超时，该笔交易就会自动被关闭。
        //(需申请业务权限，权限未开通情况下该参数不生效，默认未付款交易的超时时间为7d)取值范围：1m～15d。m-分钟，h-小时，d-天。该参数数值不接受小数点，如1.5h，可转换为90m。
        //注意：设置了未付款交易超时时间的情况下，若我司在限定时间内没有收到成功支付通知，则会关闭交易，关闭后该笔交易若付款方支付成功的情况下，会自动原路退款至付款方
        bizContent.put("timeout_express", "1h");
        //公用回传参数
        bizContent.put("extra_common_param", "extra_common_param");
        //业务代码
        bizContent.put("business_code", jsonObject.getString("businessCode"));
        //微信用户所关注商家公众号的openid
        bizContent.put("sub_openid", jsonObject.getString("openid"));
        //小程序支付，值为1，表示小程序支付；不传或值为2，表示公众账号内支付
        bizContent.put("is_minipg", "2");
        //当发起公众号支付时，值是微信公众平台基本配置中的AppID(应用ID)；当发起小程序支付时，值是对应小程序的AppID
        bizContent.put("appid", jsonObject.getString("appId"));
        //订单所属省编号（省市编号必须同时为空或者同时非空、并且需要符合层级关系）（特殊可空）
        bizContent.put("province", null);
        //订单所属市编号（省市编号必须同时为空或者同时非空、并且需要符合层级关系）（特殊可空）
        bizContent.put("city", null);
        //该笔订单的商户自主营销优惠金额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
//        bizContent.put("mer_amount","0.01");
        //是否限制信用卡。值为1表示禁用信用卡，0或为空表示不限制
        bizContent.put("limit_credit_pay", "0");
        //是否允许多次支付,Y：允许;N：不允许（参数为空或者Y时，默认该笔订单交易状态非成功状态时，支持选择其他支付方式继续付款，适用于收银台模式。 当为N时表示该笔订单交易状态为失败状态时，不支持选择其他支付方式继续付款，收银台模式下建议该参数为空）
        bizContent.put("allow_repeat_pay", "N");
        //失败通知地址（交易状态为失败时，银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3）注：只有不允许重复支付的交易才会通知
        bizContent.put("fail_notify_url", jsonObject.getString("failNotifyUrl"));


//        bizContent.put("submer_ip", "***************");//子商户ip

        //设置业务参数到biz_content
        reqDataVo.setParamData(bizContent);

        /** 2、调用ScanCodePayApi.weixinpay方法 */
        String result = null;
        try {
            result = ScanCodePayApi.weixinpay(reqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(result);

        return jsonObject1;
    }

    /**
     * 微信支付
     */
    public static JSONObject weiXinPayYdd(JSONObject jsonObject, String url) {
        //商户号
        jsonObject.put("partnerId", "***************");
        //到账主动通知地址
        jsonObject.put("notifyUrl", url + "/ysdjtb/wlgb/xyd/djDz");
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        //私钥证书路径
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");
        //收款方银盛支付用户号
        jsonObject.put("sellerId", "826551770110064");
        //收款方银盛支付客户名
        jsonObject.put("sellerName", "长沙威廉酒店管理有限公司");
        //业务代码
        jsonObject.put("businessCode", "00510030");
        //公众号appId
        jsonObject.put("appId", "wx3136bf5ad7ad7bd6");
        //交易失败通知地址
        jsonObject.put("failNotifyUrl", "http://wlgbsk.qianquan888.com/weilian-dingdingdzxcx" + "/wlgb/hxyh/hxYh4");
        /**1、组装调用SDK微信公众号，小程序支付ScanCodePayApi.weixinpay需要的参数*/
        OnlineReqDataVo reqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        reqDataVo.setReqUrl("https://qrcode.ysepay.com/gateway.do");

        //商户在银盛支付平台开设的用户号[商户号]
        reqDataVo.setPartnerId(jsonObject.getString("partnerId"));
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        reqDataVo.setNotifyUrl(jsonObject.getString("notifyUrl"));

        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        reqDataVo.setPrivateKeyFilePath(jsonObject.getString("privateKeyFilePath"));

        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        reqDataVo.setPrivateKeyPassword(jsonObject.getString("privateKeyPassword"));

        //银盛公钥证书路径: 证书入网申请后随邮件发放
        reqDataVo.setYsPublicKeyFilePath(jsonObject.getString("publicKeyFilePath"));

        //交易类型，说明：1或者空：即时到账，2：担保交易
        reqDataVo.setTranType("1");

        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //商品的标题/交易标题/订单标题/订单关键字等。该参数最长为250个汉字
        bizContent.put("subject", jsonObject.getString("skmc") != null ? jsonObject.getString("skmc") : "测试");
        //该笔订单的资金总额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
        bizContent.put("total_amount", jsonObject.getString("je"));
        //支持币种：CNY(人民币)、HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)
        //注:不填默认是CNY(人民币)，如是非跨境商户，币种不能填HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)中的任意一种外币币种
        bizContent.put("currency", "CNY");
        //收款方银盛支付用户号
        bizContent.put("seller_id", jsonObject.getString("sellerId"));
        //收款方银盛支付客户名
        bizContent.put("seller_name", jsonObject.getString("sellerName"));
        //设置未付款交易的超时时间，一旦超时，该笔交易就会自动被关闭。
        //(需申请业务权限，权限未开通情况下该参数不生效，默认未付款交易的超时时间为7d)取值范围：1m～15d。m-分钟，h-小时，d-天。该参数数值不接受小数点，如1.5h，可转换为90m。
        //注意：设置了未付款交易超时时间的情况下，若我司在限定时间内没有收到成功支付通知，则会关闭交易，关闭后该笔交易若付款方支付成功的情况下，会自动原路退款至付款方
        bizContent.put("timeout_express", "1h");
        //公用回传参数
        bizContent.put("extra_common_param", "extra_common_param");
        //业务代码
        bizContent.put("business_code", jsonObject.getString("businessCode"));
        //微信用户所关注商家公众号的openid
        bizContent.put("sub_openid", jsonObject.getString("openid"));
        //小程序支付，值为1，表示小程序支付；不传或值为2，表示公众账号内支付
        bizContent.put("is_minipg", "2");
        //当发起公众号支付时，值是微信公众平台基本配置中的AppID(应用ID)；当发起小程序支付时，值是对应小程序的AppID
        bizContent.put("appid", jsonObject.getString("appId"));
        //订单所属省编号（省市编号必须同时为空或者同时非空、并且需要符合层级关系）（特殊可空）
        bizContent.put("province", null);
        //订单所属市编号（省市编号必须同时为空或者同时非空、并且需要符合层级关系）（特殊可空）
        bizContent.put("city", null);
        //该笔订单的商户自主营销优惠金额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
//        bizContent.put("mer_amount","0.01");
        //是否限制信用卡。值为1表示禁用信用卡，0或为空表示不限制
        bizContent.put("limit_credit_pay", "0");
        //是否允许多次支付,Y：允许;N：不允许（参数为空或者Y时，默认该笔订单交易状态非成功状态时，支持选择其他支付方式继续付款，适用于收银台模式。 当为N时表示该笔订单交易状态为失败状态时，不支持选择其他支付方式继续付款，收银台模式下建议该参数为空）
        bizContent.put("allow_repeat_pay", "N");
        //失败通知地址（交易状态为失败时，银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3）注：只有不允许重复支付的交易才会通知
        bizContent.put("fail_notify_url", jsonObject.getString("failNotifyUrl"));


//        bizContent.put("submer_ip", "***************");//子商户ip

        //设置业务参数到biz_content
        reqDataVo.setParamData(bizContent);

        /** 2、调用ScanCodePayApi.weixinpay方法 */
        String result = null;
        try {
            result = ScanCodePayApi.weixinpay(reqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(result);

        return jsonObject1;
    }

    /**
     * 微信支付
     */
    public static JSONObject weixinPay1(JSONObject jsonObject) {
        //商户号
        jsonObject.put("partnerId", "***************");
        //到账主动通知地址
        jsonObject.put("notifyUrl", "http://wlgbsk.qianquan888.com/weilian-dingdingdzxcx" + "/wlgb/hxyh/hxYhDzHkTz");
        String sy = "D:\\cer\\wlgb.pfx";
        String gy = "D:\\cer\\businessgate.cer";
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");
        //收款方银盛支付用户号
        jsonObject.put("sellerId", "826551770110064");
        //收款方银盛支付客户名
        jsonObject.put("sellerName", "长沙威廉酒店管理有限公司");
        //业务代码
        jsonObject.put("businessCode", "00510030");
        //公众号appId
        jsonObject.put("appId", "wx3136bf5ad7ad7bd6");
        //交易失败通知地址
        jsonObject.put("failNotifyUrl", "http://wlgbsk.qianquan888.com/weilian-dingdingdzxcx" + "/wlgb/hxyh/hxYh4");
        /**1、组装调用SDK微信公众号，小程序支付ScanCodePayApi.weixinpay需要的参数*/
        OnlineReqDataVo reqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        reqDataVo.setReqUrl("https://qrcode.ysepay.com/gateway.do");

        //商户在银盛支付平台开设的用户号[商户号]
        reqDataVo.setPartnerId(jsonObject.getString("partnerId"));
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        reqDataVo.setNotifyUrl(jsonObject.getString("notifyUrl"));

        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        reqDataVo.setPrivateKeyFilePath(jsonObject.getString("privateKeyFilePath"));

        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        reqDataVo.setPrivateKeyPassword(jsonObject.getString("privateKeyPassword"));

        //银盛公钥证书路径: 证书入网申请后随邮件发放
        reqDataVo.setYsPublicKeyFilePath(jsonObject.getString("publicKeyFilePath"));

        //交易类型，说明：1或者空：即时到账，2：担保交易
        reqDataVo.setTranType("1");

        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //商品的标题/交易标题/订单标题/订单关键字等。该参数最长为250个汉字
        bizContent.put("subject", jsonObject.getString("skmc") != null ? jsonObject.getString("skmc") : "测试");
        //该笔订单的资金总额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
        bizContent.put("total_amount", jsonObject.getString("je"));
        //支持币种：CNY(人民币)、HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)
        //注:不填默认是CNY(人民币)，如是非跨境商户，币种不能填HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)中的任意一种外币币种
        bizContent.put("currency", "CNY");
        //收款方银盛支付用户号
        bizContent.put("seller_id", jsonObject.getString("sellerId"));
        //收款方银盛支付客户名
        bizContent.put("seller_name", jsonObject.getString("sellerName"));
        //设置未付款交易的超时时间，一旦超时，该笔交易就会自动被关闭。
        //(需申请业务权限，权限未开通情况下该参数不生效，默认未付款交易的超时时间为7d)取值范围：1m～15d。m-分钟，h-小时，d-天。该参数数值不接受小数点，如1.5h，可转换为90m。
        //注意：设置了未付款交易超时时间的情况下，若我司在限定时间内没有收到成功支付通知，则会关闭交易，关闭后该笔交易若付款方支付成功的情况下，会自动原路退款至付款方
        bizContent.put("timeout_express", "1h");
        //公用回传参数
        bizContent.put("extra_common_param", "extra_common_param");
        //业务代码
        bizContent.put("business_code", jsonObject.getString("businessCode"));
        //微信用户所关注商家公众号的openid
        bizContent.put("sub_openid", jsonObject.getString("openid"));
        //小程序支付，值为1，表示小程序支付；不传或值为2，表示公众账号内支付
        bizContent.put("is_minipg", "2");
        //当发起公众号支付时，值是微信公众平台基本配置中的AppID(应用ID)；当发起小程序支付时，值是对应小程序的AppID
        bizContent.put("appid", jsonObject.getString("appId"));
        //订单所属省编号（省市编号必须同时为空或者同时非空、并且需要符合层级关系）（特殊可空）
        bizContent.put("province", null);
        //订单所属市编号（省市编号必须同时为空或者同时非空、并且需要符合层级关系）（特殊可空）
        bizContent.put("city", null);
        //该笔订单的商户自主营销优惠金额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
//        bizContent.put("mer_amount","0.01");
        //是否限制信用卡。值为1表示禁用信用卡，0或为空表示不限制
        bizContent.put("limit_credit_pay", "0");
        //是否允许多次支付,Y：允许;N：不允许（参数为空或者Y时，默认该笔订单交易状态非成功状态时，支持选择其他支付方式继续付款，适用于收银台模式。 当为N时表示该笔订单交易状态为失败状态时，不支持选择其他支付方式继续付款，收银台模式下建议该参数为空）
        bizContent.put("allow_repeat_pay", "N");
        //失败通知地址（交易状态为失败时，银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3）注：只有不允许重复支付的交易才会通知
        bizContent.put("fail_notify_url", jsonObject.getString("failNotifyUrl"));


//        bizContent.put("submer_ip", "***************");//子商户ip

        //设置业务参数到biz_content
        reqDataVo.setParamData(bizContent);

        /** 2、调用ScanCodePayApi.weixinpay方法 */
        String result = null;
        try {
            result = ScanCodePayApi.weixinpay(reqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject1 = JSONObject.parseObject(result);

        return jsonObject1;
    }

    public static void main(String[] args) throws FileNotFoundException {
        JSONObject jsonObject = new JSONObject();
        //商户号
        jsonObject.put("partnerId", "***************");
        //到账主动通知地址
//        jsonObject.put("notifyUrl", "http://wlgbsk.qianquan888.com/weilian-dingdingdzxcx" + "/wlgb/hxyh/hxYhDzHkTz");
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        //私钥证书路径
        jsonObject.put("privateKeyFilePath", sy);
        //公钥证书路径
        jsonObject.put("publicKeyFilePath", gy);
        //私钥密钥
        jsonObject.put("privateKeyPassword", "FWQwlgb8888");
        //收款方银盛支付用户号
        jsonObject.put("sellerId", "826551770110064");
        //收款方银盛支付客户名
        jsonObject.put("sellerName", "长沙威廉酒店管理有限公司");
        OnlineReqDataVo onlineReqDataVo = new OnlineReqDataVo();

        onlineReqDataVo.setPrivateKeyFilePath(sy);
        onlineReqDataVo.setYsPublicKeyFilePath(gy);
        onlineReqDataVo.setPrivateKeyPassword("FWQwlgb8888");
        onlineReqDataVo.setTranType("1");
        onlineReqDataVo.setPartnerId("***************");


        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", "20240301204647988ZFB");
        bizContent.put("trade_no", "263231497210240301602001330616");
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //商品的标题/交易标题/订单标题/订单关键字等。该参数最长为250个汉字
        bizContent.put("subject", jsonObject.getString("skmc") != null ? jsonObject.getString("skmc") : "测试");
        //该笔订单的资金总额，单位为RMB-Yuan。取值范围为[0.01，100000000.00]，精确到小数点后两位。Number(10,2)指10位长度，2位精度
        bizContent.put("total_amount", jsonObject.getString("je"));
        //支持币种：CNY(人民币)、HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)
        //注:不填默认是CNY(人民币)，如是非跨境商户，币种不能填HKD(港币)、USD(美元)、EUR(欧元)、JPY(日元)中的任意一种外币币种
        bizContent.put("currency", "CNY");
        //收款方银盛支付用户号
        bizContent.put("seller_id", jsonObject.getString("sellerId"));
        //收款方银盛支付客户名
        bizContent.put("seller_name", jsonObject.getString("sellerName"));
        //公用回传参数
        bizContent.put("extra_common_param", "extra_common_param");
        onlineReqDataVo.setNotifyUrl("http://jiuyun2.qianquan888.com/upload/test/fkewm_1709297207904.jpg");
        onlineReqDataVo.setReturnUrl("http://jiuyun2.qianquan888.com/upload/test/fkewm_1709297207904.jpg");
        onlineReqDataVo.setParamData(bizContent);
        String tradeRefund = null;
        try {
            tradeRefund = RefundApi.tradeRefund(onlineReqDataVo);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(tradeRefund);
    }

    /**
     * 退款
     */
    public static void tk(String url, JSONObject jsonObject) {
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        OnlineReqDataVo onlineReqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        onlineReqDataVo.setReqUrl("https://openapi.ysepay.com/gateway.do");
        //商户号
        onlineReqDataVo.setPartnerId("***************");
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        onlineReqDataVo.setNotifyUrl(url + "/ysdjtb/wlgb/pay/tkCgTz");
        //同步通知地址
        onlineReqDataVo.setReturnUrl(url + "/ysdjtb/wlgb/pay/tkCgTz");
        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        onlineReqDataVo.setPrivateKeyFilePath(sy);
        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        onlineReqDataVo.setPrivateKeyPassword("FWQwlgb8888");
        //银盛公钥证书路径: 证书入网申请后随邮件发放
        onlineReqDataVo.setYsPublicKeyFilePath(gy);
        //交易类型，说明：1或者空：即时到账，2：担保交易
        onlineReqDataVo.setTranType("1");
        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        bizContent.put("trade_no", jsonObject.getString("ysbh"));
        bizContent.put("refund_amount", jsonObject.getString("tkje"));
        bizContent.put("refund_reason", jsonObject.getString("tkmc"));
        bizContent.put("out_request_no", df1.format(new Date()));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //设置业务参数到biz_content
        onlineReqDataVo.setParamData(bizContent);

        String result = null;
        try {
            System.out.println("退款-调用sdk接口barcodepay请求入参为:" + JSONObject.toJSONString(onlineReqDataVo));
            result = RefundApi.tradeRefund(onlineReqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            System.out.println("退款-失败:" + e.getCause().getMessage());
            e.printStackTrace();
        }
        System.out.println("退款-调用sdk接口barcodepay返回结果为:" + result);
    }

    /**
     * 退款---押金
     */
    public static void tkYj(String url, JSONObject jsonObject) {
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        OnlineReqDataVo onlineReqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        onlineReqDataVo.setReqUrl("https://openapi.ysepay.com/gateway.do");
        //商户号
        onlineReqDataVo.setPartnerId("***************");
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        onlineReqDataVo.setNotifyUrl(url + "/ysdjtb/wlgb/pay/tkYjTz");
        //同步通知地址
        onlineReqDataVo.setReturnUrl(url + "/ysdjtb/wlgb/pay/tkYjTz");
        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        onlineReqDataVo.setPrivateKeyFilePath(sy);
        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        onlineReqDataVo.setPrivateKeyPassword("FWQwlgb8888");
        //银盛公钥证书路径: 证书入网申请后随邮件发放
        onlineReqDataVo.setYsPublicKeyFilePath(gy);
        //交易类型，说明：1或者空：即时到账，2：担保交易
        onlineReqDataVo.setTranType("1");
        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        bizContent.put("trade_no", jsonObject.getString("ysbh"));
        bizContent.put("refund_amount", jsonObject.getString("tkje"));
        bizContent.put("refund_reason", jsonObject.getString("tkmc"));
        bizContent.put("out_request_no", df1.format(new Date()));
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //设置业务参数到biz_content
        onlineReqDataVo.setParamData(bizContent);

        String result = null;
        try {
            System.out.println("退款-调用sdk接口barcodepay请求入参为:" + JSONObject.toJSONString(onlineReqDataVo));
            result = RefundApi.tradeRefund(onlineReqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            System.out.println("退款-失败:" + e.getCause().getMessage());
            e.printStackTrace();
        }
        System.out.println("退款-调用sdk接口barcodepay返回结果为:" + result);
    }

    /**
     * 一般消费户退款接口
     */
    public static void ybxfhtk(String url, JSONObject jsonObject) {
        String sy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "wlgb.pfx");
        String gy = FileConfig.getFileAbsolutePath2("static" + File.separator + "cer" + File.separator + "businessgate.cer");
        OnlineReqDataVo onlineReqDataVo = new OnlineReqDataVo();
        //请求路径,具体见文档
        onlineReqDataVo.setReqUrl("https://openapi.ysepay.com/gateway.do");
        //商户号
        onlineReqDataVo.setPartnerId("***************");
        //银盛支付服务器主动通知商户网站里指定的页面http路径，支持多个url进行异步通知，多个url用分隔符“,”分开，格式如：url1,url2,url3
        onlineReqDataVo.setNotifyUrl(url + "/ysdjtb/wlgb/pay/tkCgTz");
        //同步通知地址
        onlineReqDataVo.setReturnUrl(url + "/ysdjtb/wlgb/pay/tkCgTz");
        //客户端私钥证书路径: 证书是在入网流程中自己申请的
        onlineReqDataVo.setPrivateKeyFilePath(sy);
        //客户端私钥密钥: 私钥密钥在入网流程中自己申请私钥证书时填写的
        onlineReqDataVo.setPrivateKeyPassword("FWQwlgb8888");
        //银盛公钥证书路径: 证书入网申请后随邮件发放
        onlineReqDataVo.setYsPublicKeyFilePath(gy);
        //交易类型，说明：1或者空：即时到账，2：担保交易
        onlineReqDataVo.setTranType("1");
        /** -----------组装业务参数,并设置到入参里面reqDataVo.setParamData(bizContentMap): 业务参数说明详细见接口文档 -------------*/
        Map<String, Object> bizContent = new HashMap<>();
        //商户生成的订单号，生成规则前8位必须为交易日期，如20180525，范围跨度支持包含当天在内的前后一天，且只能由大小写英文字母、数字、下划线及横杠组成
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMdd");
        bizContent.put("out_trade_no", jsonObject.getString("ddbh"));
        bizContent.put("trade_no", jsonObject.getString("ysbh"));
        bizContent.put("refund_amount", jsonObject.getString("tkje"));
        bizContent.put("refund_reason", jsonObject.getString("tkmc"));
        bizContent.put("out_request_no", df1.format(new Date()));
        //原交易是否参与分账（01或空代表是，02代表否） 示例值：02
        bizContent.put("is_division", "02");
        //商户日期(该参数做交易与查询时需要一致) 该日期需在当日的前后一天时间范围之内
        bizContent.put("shopdate", df2.format(new Date()));
        //设置业务参数到biz_content
        onlineReqDataVo.setParamData(bizContent);

        String result = null;
        try {
            System.out.println("一般消费户退款-调用sdk接口请求入参为:" + JSONObject.toJSONString(onlineReqDataVo));
            result = MercFundApi.refundGeneralAccount(onlineReqDataVo);
            //根据返回结果处理自己的业务逻辑,result内容详见接口文档
        } catch (Exception e) {
            System.out.println("退款-失败:" + e.getCause().getMessage());
            e.printStackTrace();
        }
        System.out.println("退款-调用sdk接口barcodepay返回结果为:" + result);
    }
}
