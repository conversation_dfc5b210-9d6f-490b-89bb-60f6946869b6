package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbWzycgdzcpk;
import com.wlgb.mapper.WlgbWzycgdzcpkMapper;
import com.wlgb.service.WlgbWzycgdzcpkService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/10 16:27
 */
@Service
public class WlgbWzycgdzcpkServiceImpl implements WlgbWzycgdzcpkService {
    @Resource
    private WlgbWzycgdzcpkMapper wlgbWzycgdzcpkMapper;

    @Override
    public void save(WlgbWzycgdzcpk wlgbWzycgdzcpk) {
        wlgbWzycgdzcpk.setId(IdConfig.uuId());
        wlgbWzycgdzcpk.setCreateTime(new Date());
        wlgbWzycgdzcpkMapper.insertSelective(wlgbWzycgdzcpk);
    }

    @Override
    public void updateById(WlgbWzycgdzcpk wlgbWzycgdzcpk) {
        wlgbWzycgdzcpk.setUpdateTime(new Date());
        wlgbWzycgdzcpkMapper.updateByPrimaryKeySelective(wlgbWzycgdzcpk);
    }
}
