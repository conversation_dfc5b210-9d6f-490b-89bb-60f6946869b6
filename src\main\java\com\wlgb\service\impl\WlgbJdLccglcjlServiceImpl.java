package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdLccglcjl;
import com.wlgb.mapper.WlgbJdLccglcjlMapper;
import com.wlgb.service.WlgbJdLccglcjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/28 12:53
 */
@Service
public class WlgbJdLccglcjlServiceImpl implements WlgbJdLccglcjlService {
    @Resource
    private WlgbJdLccglcjlMapper wlgbJdLccglcjlMapper;

    @Override
    public void save(WlgbJdLccglcjl wlgbJdLccglcjl) {
        wlgbJdLccglcjl.setId(IdConfig.uuId());
        wlgbJdLccglcjl.setCreateTime(new Date());
        wlgbJdLccglcjlMapper.insertSelective(wlgbJdLccglcjl);
    }

    @Override
    public void updateById(WlgbJdLccglcjl wlgbJdLccglcjl) {
        wlgbJdLccglcjl.setUpdateTime(new Date());
        wlgbJdLccglcjlMapper.updateByPrimaryKeySelective(wlgbJdLccglcjl);
    }

    @Override
    public Integer queryCountBySpBh(String spBh) {
        Example example = new Example(WlgbJdLccglcjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        return wlgbJdLccglcjlMapper.selectCountByExample(example);
    }

    @Override
    public WlgbJdLccglcjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd) {
        Example example = new Example(WlgbJdLccglcjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdLccglcjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdLccglcjl> queryListByWlgbJdLccglcjl(WlgbJdLccglcjl wlgbJdLccglcjl) {
        return wlgbJdLccglcjlMapper.select(wlgbJdLccglcjl);
    }
}
