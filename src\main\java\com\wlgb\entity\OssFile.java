package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/11/27 13:38
 * @Version 1.0
 */
@Table(name = "oss_file")
@Data
public class OssFile {
    @Id
    @KeySql(useGeneratedKeys = true)
    private String id;
    @Column(name = "file_name")
    private String fileName;
    private String url;
    @Column(name = "create_time")
    private Date createTime;
}
