package com.wlgb.config;

import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiSmartworkHrmEmployeeListRequest;
import com.dingtalk.api.response.OapiSmartworkHrmEmployeeListResponse;
import com.taobao.api.ApiException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/09/25 11:39
 */
public class DingHmc {

    /**
     * 根据id获取花名册所有信息
     *
     * @param userid  用户id
     * @param dingkey 配置
     * @return 结果
     */
    public static JSONObject hqHmcByUserId(String userid, Dingkey dingkey) {
        JSONObject jsonObject = new JSONObject();
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/smartwork/hrm/employee/list");
            OapiSmartworkHrmEmployeeListRequest req = new OapiSmartworkHrmEmployeeListRequest();
            req.setUseridList(userid);
            req.setAgentid(Long.parseLong(dingkey.getAgentId()));
            OapiSmartworkHrmEmployeeListResponse rsp = client.execute(req, DingToken.token(dingkey));
            jsonObject = JSONObject.parseObject(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return jsonObject;
    }
}
