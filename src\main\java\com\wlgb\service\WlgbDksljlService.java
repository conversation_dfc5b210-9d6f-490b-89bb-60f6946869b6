package com.wlgb.service;

import com.wlgb.entity.WlgbDksljl;

import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月17日 15:49
 */
public interface WlgbDksljlService {
    Integer queryCountByBzAndXid(String bz, String xid);

    WlgbDksljl queryByBzAndXid(String bz, String xid);

    void save(WlgbDksljl wlgbDksljl);

    List<WlgbDksljl> queryByXid(String xid);

    void removeById(String id);
}
