package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.FwqDyAccount;
import com.wlgb.entity.FwqDyPoi;
import com.wlgb.entity.FwqDyToken;
import com.wlgb.entity.FwqDyYq;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.FwqDyAccountService;
import com.wlgb.service2.FwqDyPoiService;
import com.wlgb.service2.FwqDyTokenService;
import com.wlgb.service2.FwqDyYqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

import static com.wlgb.config.Tools.isEmpty;

/**
 * <AUTHOR> 抖音接口
 * @version 1.0
 * @date 2023/07/31 09:47
 */
@RestController
@RequestMapping(value = "/wlgb/dy")
public class DyController {

    @Autowired
    private FwqDyPoiService fwqDyPoiService;
    @Autowired
    private FwqDyAccountService fwqDyAccountService;
    @Autowired
    private FwqDyYqService fwqDyYqService;
    @Autowired
    private FwqDyTokenService fwqDyTokenService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Value("${hanshujisuan.douyinurl}")
    private String douyinurl;

    /**
     * 如果过期就自动更新
     *
     * @param accountid 抖音本地生活商家账号ID
     * @return
     */
    public String getToken(String accountid) {
        List<FwqDyToken> list = fwqDyTokenService.selectByAccountid(accountid);
        boolean flag = true;
        String str = "";
        for (FwqDyToken fwqDyToken : list) {
            int result = fwqDyToken.getSxTime().compareTo(new Date());
            if (result > 0) {
                flag = false;
                str = fwqDyToken.getToken();
            } else {
                fwqDyTokenService.deleteByPrimaryKey(fwqDyToken);
            }
        }
        if (flag) {
            FwqDyAccount fwqDyAccount = fwqDyAccountService.selectByAccountid(accountid).get(0);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("client_key", fwqDyAccount.getClientkey());
            jsonObject.put("client_secret", fwqDyAccount.getClientsecret());
            jsonObject.put("grant_type", "client_credential");
            try {
                JSONObject json = (JSONObject) JSONObject.parse(HttpClientUtil.postdy(douyinurl + "/oauth/client_token/", jsonObject.toString(), null));
                if ("success".equals(json.getString("message"))) {
                    JSONObject data = json.getJSONObject("data");
                    str = data.getString("access_token");
                    FwqDyToken fwqDyToken = new FwqDyToken();
                    fwqDyToken.setCreateTime(new Date());
                    fwqDyToken.setToken(str);
                    fwqDyToken.setSxTime(new Date(System.currentTimeMillis() + 7200 * 1000));
                    fwqDyTokenService.save(fwqDyToken);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return str;
    }

    /**
     * 更新门店信息，先批量删除，后批量添加
     *
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "gxmdlist")
    public Result gxmdlist() {
        //先全部删除，再重新添加
        List<FwqDyPoi> ll = fwqDyPoiService.selectAll();
        ll.forEach(i -> {
            fwqDyPoiService.deleteByPrimaryKey(i);
        });

        List<FwqDyAccount> lll = fwqDyAccountService.selectAll();
        lll.forEach(l -> {
            String str = getToken(l.getAccountid());
            for (int i = 1; i < 3; i++) {
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("account_id", l.getAccountid());
                paramMap.put("page", String.valueOf(i));
                paramMap.put("size", "100");
                JSONObject json = null;
                try {
                    json = (JSONObject) JSONObject.parse(HttpClientUtil.getdy(douyinurl + "/goodlife/v1/shop/poi/query/", paramMap, str));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                JSONObject jsonextra = (JSONObject) json.get("extra");
                if ("0".equals(jsonextra.getString("error_code"))) {
                    JSONObject jsondata = (JSONObject) json.get("data");
                    int total = jsondata.getInteger("total");
                    if (total <= 100) {
                        JSONArray jsonpoisArray = (JSONArray) jsondata.get("pois");
                        if (jsonpoisArray != null) {
                            for (Object o : jsonpoisArray) {
                                JSONObject jsonpois = (JSONObject) o;
                                JSONObject jsonpoi = jsonpois.getJSONObject("poi");
                                FwqDyPoi fwqDyPoi = new FwqDyPoi();
                                fwqDyPoi.setAddress(jsonpoi.getString("address"));
                                fwqDyPoi.setAccountid(l.getAccountid());
                                fwqDyPoi.setBz(l.getBz());
                                fwqDyPoi.setPid(jsonpoi.getString("poi_id"));
                                fwqDyPoi.setPoiname(jsonpoi.getString("poi_name"));
                                fwqDyPoi.setSfsc("0");
                                fwqDyPoiService.save(fwqDyPoi);
                            }
                        }

                    }
                }
            }
        });
        return Result.ok();
    }

    /**
     * 查找门店
     *
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "selectmdlist")
    public Result selectmdlist() throws IOException {
        List<FwqDyPoi> ll = fwqDyPoiService.selectAll();
        return Result.ok(ll);
    }

    /**
     * 查找门店 _阿里云百炼平台测试
     *
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "selectmdlist2")
    public ResultBL selectmdlist2() throws IOException {
        List<FwqDyPoi> ll = fwqDyPoiService.selectAll();
        return ResultBL.ok(ll);
    }

    /**
     * 验券准备，抖音验券分两步，必须要调用两个接口才可以完成验券
     *
     * @param request code和encrypteddata 只能传一个，传code只能验一张券，传encrypteddata可以验多张券
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "prepareAndverify")
    public Result prepare(HttpServletRequest request) throws IOException {

        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);

        String code = jsonObject.getString("code");
        String encrypteddata = jsonObject.getString("encrypted_data");
        String poiid = jsonObject.getString("pid");

        String userId = request.getParameter("userId");

        if (isEmpty(code) && isEmpty(encrypteddata)) {
            return Result.error("参数不能为空");
        }
        if (isEmpty(poiid)) {
            return Result.error("参数不能为空");
        }

        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");

        String dingToken = null;
        try {
            dingToken = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        //抖音主体账号
        List<FwqDyAccount> lll = fwqDyAccountService.selectAll();
        String finalDingToken = dingToken;
        lll.forEach(l -> {
            String str = getToken(l.getAccountid());
            List<String> enlist = new ArrayList<>();
            Map<String, String> paramMap = new HashMap<>();
            //code：抖音券码  （encrypted_data/code必须二选一）
            paramMap.put("code", code);
            paramMap.put("encrypted_data", encrypteddata);
            JSONObject json = null;
            try {
                json = (JSONObject) JSONObject.parse(HttpClientUtil.getdy(douyinurl + "/goodlife/v1/fulfilment/certificate/prepare/", paramMap, str));
            } catch (IOException e) {
                e.printStackTrace();
            }
            JSONObject jsonextra = (JSONObject) json.get("extra");

            System.out.println(jsonextra + "------------验券结果的返回--------------");
            //工作通知需要使用参数
            boolean b = false;
            double je = 0.0;
            if ("0".equals(jsonextra.getString("error_code"))) {
                JSONObject jsondata = (JSONObject) json.get("data");
                String errcode = jsondata.getString("error_code");
                if ("0".equals(errcode)) {
                    JSONArray jsoncerArray = jsondata.getJSONArray("certificates");
                    String orderid = jsondata.getString("order_id");
                    //删掉该订单的所有 已经验券准备成功，但未验券的券号
                    List<FwqDyYq> ll = fwqDyYqService.selectByOrderid(orderid);
                    ll.forEach(i -> {
                        fwqDyYqService.deleteByPrimaryKey(i);
                    });
                    //重新循环添加验券准备
                    for (Object o : jsoncerArray) {
                        JSONObject cerjson = (JSONObject) o;
                        String encryptedcode = cerjson.getString("encrypted_code");
                        FwqDyYq fwqDyYq = new FwqDyYq();
                        fwqDyYq.setCreateTime(new Date());
                        String verifytoken = jsondata.getString("verify_token");
                        fwqDyYq.setVerifytoken(verifytoken);
                        fwqDyYq.setOrderid(orderid);
                        enlist.add(encryptedcode);
                        fwqDyYq.setEncryptedcode(encryptedcode);
                        fwqDyYq.setExpiretime(cerjson.getString("expire_time"));
                        JSONObject skujson = cerjson.getJSONObject("sku");
                        fwqDyYq.setSkutitle(skujson.getString("title"));
                        fwqDyYq.setSkugroupontype(skujson.getString("groupon_type"));
                        JSONObject amountjson = cerjson.getJSONObject("amount");
                        fwqDyYq.setOriginalamount(amountjson.getDouble("original_amount") / 100.00);
                        fwqDyYq.setListmarketamount(amountjson.getDouble("list_market_amount") / 100.00);
                        //pay_amount 用户实付金额  公司要求以券码实际金额为准
                        fwqDyYq.setPayamount(amountjson.getDouble("pay_amount") / 100.00);
                        //coupon_pay_amount 券码实际金额
                        fwqDyYq.setCouponpayamount(amountjson.getDouble("coupon_pay_amount") / 100.00);
                        fwqDyYq.setSfsc("0");
                        fwqDyYq.setZt("0");
                        fwqDyYqService.save(fwqDyYq);
                        String[] arr = enlist.toArray(new String[0]);
                        try {
                            b = verify(verifytoken, poiid, arr, str, datas, userId, finalDingToken, ding, dingkey, ydAppkey);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                        je = fwqDyYq.getCouponpayamount();
                    }
                }
            }

            String text = "您提交的抖音验券，'"+l.getBz()+"' 验券" + (b ? "完成" : "失败") + "了！";
            text += "\n\n券码：" + code;
            if (b) {
                text += "\n金额：" + je + "(元)";
                text += "\n代码：抖音";
            }
            text += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userId, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        });
        return Result.OK("");
    }

    /**
     * 验券，必须包含参数verify_token(唯一标识)，poi_id(门店id，随便一个门店都可以直接验券)
     *
     * @return
     * @throws IOException
     */
    public boolean verify(String verifytoken, String poiid, String[] encryptedcodes, String token, String datas, String userId, String dingToken, DingdingEmployee ding, Dingkey dingkey, YdAppkey ydAppkey) throws IOException {
        boolean b = false;
        if (isEmpty(verifytoken) || isEmpty(poiid)) {
            return b;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("verify_token", verifytoken);
        jsonObject.put("poi_id", poiid);
        jsonObject.put("encrypted_codes", encryptedcodes);
        JSONObject json = (JSONObject) JSONObject.parse(HttpClientUtil.postdy(douyinurl + "/goodlife/v1/fulfilment/certificate/verify/", jsonObject.toString(), token));

        JSONObject jsonextra = (JSONObject) json.get("extra");
        if ("0".equals(jsonextra.getString("error_code"))) {
            JSONObject jsondata = (JSONObject) json.get("data");
            String errcode = jsondata.getString("error_code");
            if ("0".equals(errcode)) {
                JSONArray jsonverArray = jsondata.getJSONArray("verify_results");
                for (Object o : jsonverArray) {
                    JSONObject verjson = (JSONObject) o;
                    String result = verjson.getString("result");
                    if ("0".equals(result)) {
                        FwqDyYq fwqDyYq = fwqDyYqService.selectByOrderidAndZt(verjson.getString("order_id"), verifytoken, verjson.getString("code"));
                        fwqDyYq.setZt("1");
                        fwqDyYqService.update(fwqDyYq);
                        System.out.println("抖音验券完成/客户已履约");

                        //前端传过来的，需要放入定金表单
                        JSONObject jsonObject1 = JSONObject.parseObject(datas);
                        String crmbh = jsonObject1.getString("crmbh");
                        String code = jsonObject1.getString("code");
                        String khdh = jsonObject1.getString("khdh");
                        String lsh = jsonObject1.getString("lsh");
                        Integer sfbjdjwk = jsonObject1.getInteger("sfbjdjwk");
                        Integer sfbjdj = jsonObject1.getInteger("sfbjdj");

                        JSONObject jsonObject2 = new JSONObject();
                        jsonObject2.put("lsh", lsh);
                        jsonObject2.put("je", fwqDyYq.getCouponpayamount());
                        jsonObject2.put("fqr", userId);
                        jsonObject2.put("fqrId", userId);
                        jsonObject2.put("fqrName", ding != null ? ding.getName() : null);
                        jsonObject2.put("fqsj", new Date());
                        jsonObject2.put("djlx", 2);
                        jsonObject2.put("sspt", 3);
                        jsonObject2.put("mtmd", poiid);
                        jsonObject2.put("hqqm", code);
                        jsonObject2.put("yqsfwc", 1);
                        jsonObject2.put("yqjg", 1);
                        jsonObject2.put("yqqm", code);
                        jsonObject2.put("yqsj", new Date());
                        jsonObject2.put("dm", "抖音");
                        jsonObject2.put("qmsj", 1);
                        jsonObject2.put("yqr", userId);
                        jsonObject2.put("yqrId", userId);
                        jsonObject2.put("yqrName", ding != null ? ding.getName() : null);
                        jsonObject2.put("sfsc", 0);
                        jsonObject2.put("sftk", 0);
                        jsonObject2.put("sfsddj", 0);
                        jsonObject2.put("sfscdj", 0);
                        jsonObject2.put("khdh", khdh);
                        jsonObject2.put("crmbh", crmbh);
                        jsonObject2.put("sfbjdj", sfbjdj != null ? sfbjdj : 0);
                        jsonObject2.put("sfxd", 0);
                        jsonObject2.put("ytje", 0);
                        if (sfbjdjwk != null) {
                            jsonObject2.put("sfbjdjwk", 1);
                            jsonObject2.put("sfxyld", 1);
                        } else {
                            jsonObject2.put("sfbjdjwk", 0);
                            jsonObject2.put("sfxyld", 0);
                        }
                        //定金表唯一标识
                        jsonObject2.put("djwybs", IdConfig.uuId());
                        GatewayResult gatewayResult = null;
                        for (int j = 0; j < 5; j++) {
                            try {
                                gatewayResult = DingBdLcConfig.xzBdSl(dingToken, ydAppkey, jsonObject2.toJSONString(), userId, "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1");
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                                if (j == 4) {
                                    try {
                                        String context1 = "抖音验券成功同步表单出错了，券码：" + code + "，错误原因：" + gatewayResult.toString();
                                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                    } catch (ApiException e) {
                                        e.printStackTrace();
                                    }
                                }
                            } else {
                                break;
                            }
                        }

                        b = true;
                    }
                }
            }
        }

        return b;
    }
}
