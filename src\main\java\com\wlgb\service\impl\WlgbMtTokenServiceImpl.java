package com.wlgb.service.impl;

import com.wlgb.entity.WlgbMtToken;
import com.wlgb.mapper.WlgbMtTokenMapper;
import com.wlgb.service.WlgbMtTokenService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月17日 14:31
 */
@Service
public class WlgbMtTokenServiceImpl implements WlgbMtTokenService {
    @Resource
    private WlgbMtTokenMapper wlgbMtTokenMapper;

    @Override
    public List<WlgbMtToken> queryAllList() {
        return wlgbMtTokenMapper.selectAll();
    }

    @Override
    public List<WlgbMtToken> queryAllListById(Map<String, Object>... map) {
        Example example = new Example(WlgbMtToken.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sfsc", "0");
        // 遍历所有传入的Map
        for (Map<String, Object> singleMap : map) {
            Set<Entry<String, Object>> entries = singleMap.entrySet();
            // 遍历单个Map中的所有键值对
            for (Entry<String, Object> entry : entries) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (!key.isEmpty()) {
                    criteria.andEqualTo(key, value);
                }
            }
        }
        return wlgbMtTokenMapper.selectByExample(example);
    }

    @Override
    public WlgbMtToken queryById(WlgbMtToken wlgbMtToken) {
        return wlgbMtTokenMapper.selectOne(wlgbMtToken);
    }

    @Override
    public void updateById(WlgbMtToken wlgbMtToken) {
        wlgbMtTokenMapper.updateByPrimaryKeySelective(wlgbMtToken);
    }
}
