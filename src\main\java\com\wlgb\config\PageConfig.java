package com.wlgb.config;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/14 14:45
 */
public class PageConfig {

    /**
     * 分页帮助
     * @param pageHelp 页码
     * */
    public static PageHelpUtil pages(PageHelp pageHelp){
        PageHelpUtil pageHelpUtil = new PageHelpUtil(pageHelp.getPage(), pageHelp.getPageSize());
        return pageHelpUtil;
    }

    /**
     * 设置默认页码
     * @param pageHelp 页码
     * */
    public static PageHelp pageHelp(PageHelp pageHelp){
        if (pageHelp == null) {
            pageHelp = new PageHelp();
            pageHelp.setPage(1);
            pageHelp.setPageSize(10);
        } else {
            if (pageHelp.getPage() == null || "".equals(pageHelp.getPage())) {
                pageHelp.setPage(1);
            }
            if (pageHelp.getPageSize() == null || "".equals(pageHelp.getPageSize())) {
                pageHelp.setPageSize(10);
            }
        }
        return pageHelp;
    }


    /**
     * 设置返回
     * @param pageHelpUtil 返回类
     * @param pageHelp 页码
     * */
    public static PageHelpUtil pageHelpUtil(PageHelpUtil pageHelpUtil, PageHelp pageHelp){
        pageHelpUtil.setPageNum(pageHelp.getPage());
        pageHelpUtil.setPageSize(pageHelp.getPageSize());
        return pageHelpUtil;
    }



}
