package com.wlgb.service.impl;

import com.wlgb.entity.WlgbJdYhk;
import com.wlgb.mapper.WlgbJdYhkMapper;
import com.wlgb.service.WlgbJdYhkService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 22:02
 */
@Service
public class WlgbJdYhkServiceImpl implements WlgbJdYhkService {
    @Resource
    private WlgbJdYhkMapper wlgbJdYhkMapper;

    @Override
    public WlgbJdYhk querySfScAndLikeBz(Integer sfsc, String bz) {
        Example example = new Example(WlgbJdYhk.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andLike("bz", bz);
        criteria.andEqualTo("sfsc", sfsc);
        return wlgbJdYhkMapper.selectOneByExample(example);
    }
}
