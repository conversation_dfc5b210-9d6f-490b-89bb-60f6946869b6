package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "crm_xgjlb")
public class CrmXgjlb {

    /**qid*/
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
    /**qid*/
    private java.lang.Integer qid;
    /**房东姓名，（负责人）*/
    private java.lang.String qfzr;
    /**客户微信名*/
    private java.lang.String qkhwxm;
    /**客户微信号*/
    private java.lang.String qkhwxh;
    /**客户电话*/
    private java.lang.String qkhdh;
    /**业务电话编码*/
    private java.lang.String qdhbm;
    /**团队人数*/
    private java.lang.String qtdrs;
    /**聚会类型*/
    private java.lang.String qjhlx;
    /**城市*/
    private java.lang.String qcs;
    /**渠道来源*/
    private java.lang.String qqdly;
    /**搜索关键词*/
    private java.lang.String qssgjc;
    /**客户状态(跟进客户，)*/
    private java.lang.String qkhzt;
    /**最近跟进记录*/
    private java.lang.String qzjgjjl;
    /**客户类别*/
    private java.lang.String qkhlb;
    /**创建人*/
    private java.lang.String qcjr;
    /**备注*/
    private java.lang.String qbz;
    /**协同人*/
    private java.lang.String qxtr;
    /**客户预定时间*/
    private java.lang.String qkhydsj;
    /**分配时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.lang.String qfpsj;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.lang.String qcjsj;
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.lang.String qgxsj;
    /**信息状态：0：正常客户。1：公海池。2：回收站。3：彻底删除不显示信息*/
    private java.lang.String qxxzt;
    /**修改人*/
    private java.lang.String qxgr;
    /**修改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.lang.String qxgsj;
    /**删除人*/
    private java.lang.String qscr;
    /**删除时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.lang.String qscsj;
    /**删除备注或原因*/
    private java.lang.String qscbz;
    /**负责人id*/
    private java.lang.String qfzrid;
    /**创建人id*/
    private java.lang.String qcjrid;
    /**创建人id*/
    private java.lang.String qxtrid;
    /**删除人id*/
    private java.lang.String qscrid;
    /**修改人id*/
    private java.lang.String qxgrid;
    /**捞取时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.lang.String qlqsj;
    /**分配userid*/
    private java.lang.String qfpuserid;
    /**移交时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.lang.String qyjsj;
    /**移交userid*/
    private java.lang.String qyjuserid;
    /**客户手机号*/
    private java.lang.String qkhsj;
    /**客户号码*/
    private java.lang.String qkhhm;
    /**创建时间拆分 年*/
    private java.lang.String qcjsj2;
    /**移交原因*/
    private java.lang.String qyjyy;
    /**是否暂无成交(0:否1:是)*/
    private java.lang.String qsfzwcj;
    /**是否周末场：0:非周末,1:周末场*/
    private java.lang.String qsfzmc;
    /**是否移交改为新增：0 否，1 是*/
    private java.lang.String qsfyjzx;
    /**分区名称*/
    private java.lang.String fqname;
    /**城市分区id*/
    private java.lang.String csfqid;
    /**校园合伙人*/
    private java.lang.String qxyhhr;
    /**小红书推广人*/
    private java.lang.String qxhstgr;
}
