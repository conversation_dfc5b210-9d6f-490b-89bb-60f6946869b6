package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 获取券码校验记录（存放券码的详细信息）
 */
@Data
@Table(name = "wlgb_mt_log")
public class WlgbMtLog {
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
    /**
     * 创建人
     */
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    private java.lang.String sysOrgCode;
    /**
     * 订单编号
     */
    private java.lang.String ddbh;
    /**
     * 订单id
     */
    private java.lang.String ddid;
    /**
     * 验券人id
     */
    private java.lang.String yqrid;
    /**
     * 验券人
     */
    private java.lang.String yqr;
    /**
     * 券号
     */
    private java.lang.String qh;
    /**
     * 商品名称
     */
    private java.lang.String spmc;
    /**
     * 验券时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private java.util.Date yqsj;
    /**
     * 验券门店
     */
    private java.lang.String yqmd;
    /**
     * 备注
     */
    private java.lang.String bz;
    /**
     * 验券城市
     */
    private java.lang.String yqcs;
    /**
     * 验券门店地址
     */
    private java.lang.String yqmddz;
    /**
     * 验券门店店名
     */
    private java.lang.String yqdm;
    /**
     * 商品市场价
     */
    private java.lang.Double spscj;
    /**
     * 商品售卖价
     */
    private java.lang.Double spsmj;
    /**
     * crm编号
     */
    private java.lang.String crmbh;
    /**
     * 客户电话
     */
    private java.lang.String khdh;
    /**
     * 宜搭验券记录实例id
     */
    private java.lang.String slid;

}
