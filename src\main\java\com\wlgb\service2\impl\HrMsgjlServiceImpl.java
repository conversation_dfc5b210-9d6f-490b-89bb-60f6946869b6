package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.HrMsgjl;
import com.wlgb.mapper.HrMsgjlMapper;
import com.wlgb.service2.HrMsgjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/31 20:27
 */
@Service
@DS(value = "second")
public class HrMsgjlServiceImpl implements HrMsgjlService {
    @Resource
    private HrMsgjlMapper hrMsgjlMapper;

    @Override
    public void save(HrMsgjl hrMsgjl) {
        hrMsgjlMapper.insertSelective(hrMsgjl);
    }
}
