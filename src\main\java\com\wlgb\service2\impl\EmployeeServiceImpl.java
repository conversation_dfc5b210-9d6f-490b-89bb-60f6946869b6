package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.Employee;
import com.wlgb.entity.FwqDyToken;
import com.wlgb.mapper.EmployeeMapper;
import com.wlgb.service2.EmployeeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/5 21:31
 */
@Service
@DS(value = "second")
public class EmployeeServiceImpl implements EmployeeService {
    @Resource
    private EmployeeMapper EmployeeMapper;

    @Override
    public void save(Employee Employee) {
        EmployeeMapper.insertSelective(Employee);
    }

    @Override
    public Employee selectBySjhAndName(String sjh, String name) {
        Employee employee = new Employee();
        employee.setName(name);
        employee.setMobile(sjh);
        return EmployeeMapper.selectOne(employee);
    }

    @Override
    public Employee queryCountByUserId(String userid) {
        return EmployeeMapper.selectByPrimaryKey(userid);
    }

    @Override
    public void updateById(Employee Employee) {
        EmployeeMapper.updateByPrimaryKeySelective(Employee);
    }

    @Override
    public void delete(Employee Employee) {
        EmployeeMapper.delete(Employee);
    }
}
