package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmQbkh;
import com.wlgb.mapper.CrmQbkhMapper;
import com.wlgb.service4.DhRjCrmQbkhService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 16:02
 */
@Service
@DS("fourth")
public class DhRjCrmQbkhServiceImpl implements DhRjCrmQbkhService {
    @Resource
    private CrmQbkhMapper crmQbkhMapper;

    @Override
    public void save(CrmQbkh crmQbkh) {
        crmQbkhMapper.insertSelective(crmQbkh);
    }

    @Override
    public void updateById(CrmQbkh crmQbkh) {
        crmQbkhMapper.updateByPrimaryKeySelective(crmQbkh);
    }

    @Override
    public CrmQbkh queryByCrmBh(String crmBh) {
        Example example = new Example(CrmQbkh.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("crmbh", crmBh);
        return crmQbkhMapper.selectOneByExample(example);
    }
}
