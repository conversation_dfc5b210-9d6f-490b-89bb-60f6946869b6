package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.service.*;
import com.wlgb.service2.TbXydService;
import com.wlgb.service2.TbYddXydService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/26 20:03
 */
@RestController
@RequestMapping(value = "/wlgb/pay")
public class YjAndWkController {
    @Autowired
    private WlgbYddYjWkJlService wlgbYddYjWkJlService;
    @Autowired
    private WlgbHxyhYjWkFqskService wlgbHxyhYjWkFqskService;
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private WlgbHxyhYjWkDzjlService wlgbHxyhYjWkDzjlService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private TbYddXydService tbYddXydService;
    @Autowired
    private WlgbYddYjwktkjlService wlgbYddYjwktkjlService;
    @Autowired
    private WlgbXydLogService wlgbXydLogService;
    @Autowired
    private WlgbYddMdxfjlService wlgbYddMdxfjlService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;

    /**
     * 押金判断付款方式
     */
    @RequestMapping(value = "yjPdFs")
    public void yjPdFs(@RequestParam(name = "ddbh", defaultValue = "") String ddbh,
                       HttpServletRequest request, HttpServletResponse response) {
        WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
        wlgbYddYjWkJl.setXddbh(ddbh);
        wlgbYddYjWkJl.setSfsc(0);
        wlgbYddYjWkJl.setSfyj(1);
        WlgbYddYjWkJl wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
        if (wlgbYddYjWkJl1 != null) {
            String skbh = wlgbYddYjWkJl1.getYjskbh();
            Double je = wlgbYddYjWkJl1.getYjje();
            String skmc = wlgbYddYjWkJl1.getXddbh() + "场地押金";
//            String userid = tbYddXyd1.getXfdid();
            WlgbHxyhYjWkFqsk wlgbHxyhFqsk = new WlgbHxyhYjWkFqsk();
            wlgbHxyhFqsk.setTjsj(new Date());
//            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
//            wlgbHxyhFqsk.setTjr(employee != null ? employee.getName() : null);
//            wlgbHxyhFqsk.setTjrid(employee != null ? employee.getUserid() : null);
            TbXyd tbXyd = tbXydService.queryByDdBh(ddbh);
            if (tbXyd != null) {
                wlgbHxyhFqsk.setXid(tbXyd.getXid());
                wlgbHxyhFqsk.setXddbh(tbXyd.getXddbh());
            }
            wlgbHxyhFqsk.setJetype("押金");
            String home = "826551873990079";
            wlgbHxyhFqsk.setHome(home);
            wlgbHxyhFqsk.setJe(je);
            wlgbHxyhFqsk.setSkbh(skbh);
            wlgbHxyhFqsk.setFymc(skmc);
            String userAgent = request.getHeader("user-agent");

            boolean b = true;
            int count = wlgbHxyhYjWkFqskService.queryByDdBhAndZfZt(skbh, "是");
            if (count > 0) {
                b = false;
            }
            if (b) {
                if (userAgent != null && userAgent.contains("MicroMessenger")) {
                    WlgbHxyhYjWkFqsk hxyhFqsk = wlgbHxyhYjWkFqskService.queryByDdBhAndFkFs(skbh, "微信");
                    if (hxyhFqsk == null) {
                        String redirectUrl = ddxturl + "/ysdjtb/wlgb/pay/yjWxHqCode?ddbh=" + wlgbYddYjWkJl1.getXddbh() + "&je=" + je +
                                "&skmc=" + skmc;
                        //这里要将你的授权回调地址处理一下，否则微信识别不了
                        //UTF-8编码
                        String redirectUri = null;
                        try {
                            redirectUri = URLEncoder.encode(redirectUrl, "UTF-8");
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                        //简单获取openid的话参数response_type与scope与state参数固定写死即可
                        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx50fbc8ba4f887894"
                                + "&redirect_uri="
                                + redirectUri + "&response_type=code&scope="
                                + "snsapi_base" + "&state=STATE#wechat_redirect";
                        wlgbHxyhFqsk.setEwm(url);
                        wlgbHxyhFqsk.setFkfs("微信");
                        wlgbHxyhYjWkFqskService.save(wlgbHxyhFqsk);
                        try {
                            response.sendRedirect(url);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    } else {
                        try {
                            response.sendRedirect(hxyhFqsk.getEwm());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    System.out.println("微信支付");

                } else if (userAgent != null && userAgent.contains("AlipayClient")) {
                    WlgbHxyhYjWkFqsk hxyhFqsk = wlgbHxyhYjWkFqskService.queryByDdBhAndFkFs(skbh, "支付宝");
                    if (hxyhFqsk == null) {
                        skbh = skbh + "ZFB";
                        System.out.println("支付宝支付");
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("ddbh", skbh);
                        jsonObject.put("je", je);
                        jsonObject.put("fkfs", "1903000");
                        jsonObject.put("skmc", skmc);
                        jsonObject.put("home", home);
                        JSONObject result = YsPayConfig.ewmPayYjAndWk(jsonObject, ddxturl);
                        wlgbHxyhFqsk.setFkfs("支付宝");
                        if (result != null) {
                            String sourceQrCodeUrl = result.getString("source_qr_code_url");
                            try {
                                response.sendRedirect(sourceQrCodeUrl);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                            wlgbHxyhFqsk.setEwm(sourceQrCodeUrl);
                            wlgbHxyhFqsk.setYsbh(result.getString("trade_no"));
                        }
                        wlgbHxyhYjWkFqskService.save(wlgbHxyhFqsk);
                    } else {
                        try {
                            response.sendRedirect(hxyhFqsk.getEwm());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    try {
                        response.setContentType("text/html; charset=UTF-8");
                        response.getWriter().print("<html><body><script type='text/javascript'>alert('请使用微信或支付宝扫码！');</script></body></html>");
                        response.getWriter().close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                try {
                    response.setContentType("text/html; charset=UTF-8");
                    response.getWriter().print("<html><body><script type='text/javascript'>alert('该订单已支付，请勿重复支付！');</script></body></html>");
                    response.getWriter().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            System.out.println(userAgent);
        } else {
            try {
                response.setContentType("text/html; charset=UTF-8");
                response.getWriter().print("<html><body><script type='text/javascript'>alert('该二维码已失效，请重新生成！');</script></body></html>");
                response.getWriter().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 押金判断付款方式
     */
    @RequestMapping(value = "mdXfPdFs")
    public void mdXfPdFs(@RequestParam(name = "ddbh", defaultValue = "") String ddbh,
                         HttpServletRequest request, HttpServletResponse response) {
        WlgbYddMdxfjl wlgbYddMdxfjl = new WlgbYddMdxfjl();
        wlgbYddMdxfjl.setXddbh(ddbh);
        wlgbYddMdxfjl.setSfsc(0);
        WlgbYddMdxfjl wlgbYddMdxfjl1 = wlgbYddMdxfjlService.queryWlgbYddMdxfjlByWlgbYddMdxfjl(wlgbYddMdxfjl);
        if (wlgbYddMdxfjl1 != null) {
            String skbh = wlgbYddMdxfjl1.getSkbh();
            Double je = wlgbYddMdxfjl1.getJe();
            String skmc = wlgbYddMdxfjl1.getXddbh() + "门店消费金额";
//            String userid = tbYddXyd1.getXfdid();
            WlgbHxyhYjWkFqsk wlgbHxyhFqsk = new WlgbHxyhYjWkFqsk();
            wlgbHxyhFqsk.setTjsj(new Date());
//            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
//            wlgbHxyhFqsk.setTjr(employee != null ? employee.getName() : null);
//            wlgbHxyhFqsk.setTjrid(employee != null ? employee.getUserid() : null);
            TbXyd tbXyd = tbXydService.queryByDdBh(ddbh);
            if (tbXyd != null) {
                wlgbHxyhFqsk.setXid(tbXyd.getXid());
                wlgbHxyhFqsk.setXddbh(tbXyd.getXddbh());
            }
            wlgbHxyhFqsk.setJetype("押金");
            String home = "826551873990079";
            wlgbHxyhFqsk.setHome(home);
            wlgbHxyhFqsk.setJe(je);
            wlgbHxyhFqsk.setSkbh(skbh);
            wlgbHxyhFqsk.setFymc(skmc);
            String userAgent = request.getHeader("user-agent");

            boolean b = true;
            int count = wlgbHxyhYjWkFqskService.queryByDdBhAndZfZt(skbh, "是");
            if (count > 0) {
                b = false;
            }
            if (b) {
                if (userAgent != null && userAgent.contains("MicroMessenger")) {
                    WlgbHxyhYjWkFqsk hxyhFqsk = wlgbHxyhYjWkFqskService.queryByDdBhAndFkFs(skbh, "微信");
                    if (hxyhFqsk == null) {
                        String redirectUrl = ddxturl + "/ysdjtb/wlgb/pay/mdXfWxHqCode?ddbh=" + wlgbYddMdxfjl1.getXddbh() + "&je=" + je +
                                "&skmc=" + skmc;
                        //这里要将你的授权回调地址处理一下，否则微信识别不了
                        //UTF-8编码
                        String redirectUri = null;
                        try {
                            redirectUri = URLEncoder.encode(redirectUrl, "UTF-8");
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                        //简单获取openid的话参数response_type与scope与state参数固定写死即可
                        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx50fbc8ba4f887894"
                                + "&redirect_uri="
                                + redirectUri + "&response_type=code&scope="
                                + "snsapi_base" + "&state=STATE#wechat_redirect";
                        wlgbHxyhFqsk.setEwm(url);
                        wlgbHxyhFqsk.setFkfs("微信");
                        wlgbHxyhYjWkFqskService.save(wlgbHxyhFqsk);
                        try {
                            response.sendRedirect(url);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    } else {
                        try {
                            response.sendRedirect(hxyhFqsk.getEwm());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    System.out.println("微信支付");

                } else if (userAgent != null && userAgent.contains("AlipayClient")) {
                    WlgbHxyhYjWkFqsk hxyhFqsk = wlgbHxyhYjWkFqskService.queryByDdBhAndFkFs(skbh, "支付宝");
                    if (hxyhFqsk == null) {
                        skbh = skbh + "ZFB";
                        System.out.println("支付宝支付");
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("ddbh", skbh);
                        jsonObject.put("je", je);
                        jsonObject.put("fkfs", "1903000");
                        jsonObject.put("skmc", skmc);
                        jsonObject.put("home", home);
                        JSONObject result = YsPayConfig.ewmPayMdXf(jsonObject, ddxturl);
                        wlgbHxyhFqsk.setFkfs("支付宝");
                        if (result != null) {
                            String sourceQrCodeUrl = result.getString("source_qr_code_url");
                            try {
                                response.sendRedirect(sourceQrCodeUrl);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                            wlgbHxyhFqsk.setEwm(sourceQrCodeUrl);
                            wlgbHxyhFqsk.setYsbh(result.getString("trade_no"));
                        }
                        wlgbHxyhYjWkFqskService.save(wlgbHxyhFqsk);
                    } else {
                        try {
                            response.sendRedirect(hxyhFqsk.getEwm());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    try {
                        response.setContentType("text/html; charset=UTF-8");
                        response.getWriter().print("<html><body><script type='text/javascript'>alert('请使用微信或支付宝扫码！');</script></body></html>");
                        response.getWriter().close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                try {
                    response.setContentType("text/html; charset=UTF-8");
                    response.getWriter().print("<html><body><script type='text/javascript'>alert('该订单已支付，请勿重复支付！');</script></body></html>");
                    response.getWriter().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            System.out.println(userAgent);
        } else {
            try {
                response.setContentType("text/html; charset=UTF-8");
                response.getWriter().print("<html><body><script type='text/javascript'>alert('该二维码已失效，请重新生成！');</script></body></html>");
                response.getWriter().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 尾款判断付款方式
     */
    @RequestMapping(value = "wkPdFs")
    public void wkPdFs(@RequestParam(name = "ddbh", defaultValue = "") String ddbh,
                       HttpServletRequest request, HttpServletResponse response) {
        WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
        wlgbYddYjWkJl.setXddbh(ddbh);
        wlgbYddYjWkJl.setSfsc(0);
        wlgbYddYjWkJl.setSfyj(1);
        WlgbYddYjWkJl wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
        if (wlgbYddYjWkJl1 != null) {
            String skbh = wlgbYddYjWkJl1.getWkskbh();
            Double je = wlgbYddYjWkJl1.getWkje();
            String skmc = wlgbYddYjWkJl1.getXddbh() + "场地尾款";
//            String userid = tbYddXyd1.getXfdid();
            WlgbHxyhYjWkFqsk wlgbHxyhFqsk = new WlgbHxyhYjWkFqsk();
            wlgbHxyhFqsk.setTjsj(new Date());
//            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
//            wlgbHxyhFqsk.setTjr(employee != null ? employee.getName() : null);
//            wlgbHxyhFqsk.setTjrid(employee != null ? employee.getUserid() : null);
            TbXyd tbXyd = tbXydService.queryByDdBh(ddbh);
            if (tbXyd != null) {
                wlgbHxyhFqsk.setXid(tbXyd.getXid());
                wlgbHxyhFqsk.setXddbh(tbXyd.getXddbh());
            }
            wlgbHxyhFqsk.setJetype("尾款");
            String home = "826551873990079";
            wlgbHxyhFqsk.setJe(je);
            wlgbHxyhFqsk.setSkbh(skbh);
            wlgbHxyhFqsk.setFymc(skmc);
            String userAgent = request.getHeader("user-agent");

            boolean b = true;
            int count = wlgbHxyhYjWkFqskService.queryByDdBhAndZfZt(skbh, "是");
            if (count > 0) {
                b = false;
            }
            if (b) {
                if (userAgent != null && userAgent.contains("MicroMessenger")) {
                    WlgbHxyhYjWkFqsk hxyhFqsk = wlgbHxyhYjWkFqskService.queryByDdBhAndFkFs(skbh, "微信");
                    if (hxyhFqsk == null) {
                        String redirectUrl = ddxturl + "/ysdjtb/wlgb/pay/wkWxHqCode?ddbh=" + wlgbYddYjWkJl1.getXddbh() + "&je=" + je +
                                "&skmc=" + skmc;
                        //这里要将你的授权回调地址处理一下，否则微信识别不了
                        //UTF-8编码
                        String redirectUri = null;
                        try {
                            redirectUri = URLEncoder.encode(redirectUrl, "UTF-8");
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                        //简单获取openid的话参数response_type与scope与state参数固定写死即可
                        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx50fbc8ba4f887894"
                                + "&redirect_uri="
                                + redirectUri + "&response_type=code&scope="
                                + "snsapi_base" + "&state=STATE#wechat_redirect";
                        wlgbHxyhFqsk.setEwm(url);
                        wlgbHxyhFqsk.setFkfs("微信");
                        wlgbHxyhYjWkFqskService.save(wlgbHxyhFqsk);
                        try {
                            response.sendRedirect(url);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    } else {
                        try {
                            response.sendRedirect(hxyhFqsk.getEwm());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    System.out.println("微信支付");

                } else if (userAgent != null && userAgent.contains("AlipayClient")) {
                    WlgbHxyhYjWkFqsk hxyhFqsk = wlgbHxyhYjWkFqskService.queryByDdBhAndFkFs(skbh, "支付宝");
                    if (hxyhFqsk == null) {
                        skbh = skbh + "ZFB";
                        System.out.println("支付宝支付");
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("ddbh", skbh);
                        jsonObject.put("je", je);
                        jsonObject.put("fkfs", "1903000");
                        jsonObject.put("skmc", skmc);
                        jsonObject.put("home", home);
                        JSONObject result = YsPayConfig.ewmPayYjAndWk(jsonObject, ddxturl);
                        wlgbHxyhFqsk.setFkfs("支付宝");
                        if (result != null) {
                            String sourceQrCodeUrl = result.getString("source_qr_code_url");
                            try {
                                response.sendRedirect(sourceQrCodeUrl);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                            wlgbHxyhFqsk.setEwm(sourceQrCodeUrl);
                            wlgbHxyhFqsk.setYsbh(result.getString("trade_no"));
                        }
                        wlgbHxyhYjWkFqskService.save(wlgbHxyhFqsk);
                    } else {
                        try {
                            response.sendRedirect(hxyhFqsk.getEwm());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    try {
                        response.setContentType("text/html; charset=UTF-8");
                        response.getWriter().print("<html><body><script type='text/javascript'>alert('请使用微信或支付宝扫码！');</script></body></html>");
                        response.getWriter().close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                try {
                    response.setContentType("text/html; charset=UTF-8");
                    response.getWriter().print("<html><body><script type='text/javascript'>alert('该订单已支付，请勿重复支付！');</script></body></html>");
                    response.getWriter().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            System.out.println(userAgent);
        } else {
            try {
                response.setContentType("text/html; charset=UTF-8");
                response.getWriter().print("<html><body><script type='text/javascript'>alert('该二维码已失效，请重新生成！');</script></body></html>");
                response.getWriter().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 用户扫码授权跳转发起支付订单(押金)
     */
    @RequestMapping(value = "yjWxHqCode")
    public void yjWxHqCode(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String code = request.getParameter("code");
        String ddbh = request.getParameter("ddbh");
        WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
        wlgbYddYjWkJl.setXddbh(ddbh);
        wlgbYddYjWkJl.setSfsc(0);
        wlgbYddYjWkJl.setSfyj(1);
        WlgbYddYjWkJl wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
        if (wlgbYddYjWkJl1 != null) {
            String je = request.getParameter("je");
            String skmc = request.getParameter("skmc");
            System.out.println("code：" + code);

            Map<String, String> params = new HashMap<>();
            params.put("secret", "8fae756f3927956f1981eaf54164e6d4");
            params.put("appid", "wx50fbc8ba4f887894");
            params.put("grant_type", "authorization_code");
            params.put("code", code);
            String result = HttpClientUtil.get("https://api.weixin.qq.com/sns/oauth2/access_token", params);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String openid = jsonObject.getString("openid");
            System.out.println(openid);
            WlgbHxyhYjWkFqsk hxyhFqsk = wlgbHxyhYjWkFqskService.queryByDdBhAndFkFs(wlgbYddYjWkJl1.getYjskbh(), "微信");
            boolean b = true;
            WlgbHxyhYjWkFqsk wlgbHxyhFqsk = new WlgbHxyhYjWkFqsk();
            if (hxyhFqsk != null) {
                if (hxyhFqsk.getAppid() != null && !"".equals(hxyhFqsk.getAppid())) {
                    String url = "https://yinshengdj.qianquan888.com/ysdjtb/wxPay.html?appId=" + hxyhFqsk.getAppid() +
                            "&timeStamp=" + hxyhFqsk.getTimestamp() +
                            "&nonceStr=" + hxyhFqsk.getNoncestr() +
                            "&package=" + hxyhFqsk.getPackage1() +
                            "&paySign=" + hxyhFqsk.getPaysign();
                    response.sendRedirect(url);
                    b = false;
                } else {
                    wlgbHxyhFqsk.setId(hxyhFqsk.getId());
                }

            }
            if (b) {
                String home = "826551873990079";
                wlgbHxyhFqsk.setHome(home);
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("ddbh", wlgbYddYjWkJl1.getYjskbh() + "WX");
                jsonObject1.put("je", je);
                jsonObject1.put("skmc", skmc);
                jsonObject1.put("openid", openid);
                jsonObject1.put("home", home);
                JSONObject jsonObject2;
                jsonObject2 = YsPayConfig.weiXinPayYjAndWk(jsonObject1, ddxturl);
                if (jsonObject2 != null && jsonObject2.size() > 0) {
                    JSONObject payInfo = jsonObject2.getJSONObject("jsapi_pay_info");
                    wlgbHxyhFqsk.setYsbh(jsonObject2.getString("trade_no"));
                    wlgbHxyhFqsk.setSkbh(wlgbYddYjWkJl1.getYjskbh());
                    if (payInfo != null && payInfo.size() > 0) {
                        String url = "https://yinshengdj.qianquan888.com/ysdjtb/wxPay.html?appId=" + payInfo.getString("appId") +
                                "&timeStamp=" + payInfo.getString("timeStamp") +
                                "&nonceStr=" + payInfo.getString("nonceStr") +
                                "&package=" + payInfo.getString("package") +
                                "&paySign=" + payInfo.getString("paySign");
                        wlgbHxyhFqsk.setAppid(payInfo.getString("appId"));
                        wlgbHxyhFqsk.setTimestamp(payInfo.getString("timeStamp"));
                        wlgbHxyhFqsk.setNoncestr(payInfo.getString("nonceStr"));
                        wlgbHxyhFqsk.setPackage1(payInfo.getString("package"));
                        wlgbHxyhFqsk.setPaysign(payInfo.getString("paySign"));
                        wlgbHxyhYjWkFqskService.updateById(wlgbHxyhFqsk);
                        response.sendRedirect(url);
                    } else {
                        wlgbHxyhYjWkFqskService.updateById(wlgbHxyhFqsk);
                    }
                }
            }
        } else {
            try {
                response.setContentType("text/html; charset=UTF-8");
                response.getWriter().print("<html><body><script type='text/javascript'>alert('该二维码已失效，请重新生成！');</script></body></html>");
                response.getWriter().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 用户扫码授权跳转发起支付订单(门店消费)
     */
    @RequestMapping(value = "mdXfWxHqCode")
    public void mdXfWxHqCode(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String code = request.getParameter("code");
        String ddbh = request.getParameter("ddbh");
        WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
        wlgbYddYjWkJl.setXddbh(ddbh);
        wlgbYddYjWkJl.setSfsc(0);
        wlgbYddYjWkJl.setSfyj(1);
        WlgbYddYjWkJl wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
        if (wlgbYddYjWkJl1 != null) {
            String je = request.getParameter("je");
            String skmc = request.getParameter("skmc");
            System.out.println("code：" + code);

            Map<String, String> params = new HashMap<>();
            params.put("secret", "8fae756f3927956f1981eaf54164e6d4");
            params.put("appid", "wx50fbc8ba4f887894");
            params.put("grant_type", "authorization_code");
            params.put("code", code);
            String result = HttpClientUtil.get("https://api.weixin.qq.com/sns/oauth2/access_token", params);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String openid = jsonObject.getString("openid");
            System.out.println(openid);
            WlgbHxyhYjWkFqsk hxyhFqsk = wlgbHxyhYjWkFqskService.queryByDdBhAndFkFs(wlgbYddYjWkJl1.getYjskbh(), "微信");
            boolean b = true;
            WlgbHxyhYjWkFqsk wlgbHxyhFqsk = new WlgbHxyhYjWkFqsk();
            if (hxyhFqsk != null) {
                if (hxyhFqsk.getAppid() != null && !"".equals(hxyhFqsk.getAppid())) {
                    String url = "https://yinshengdj.qianquan888.com/ysdjtb/wxPay.html?appId=" + hxyhFqsk.getAppid() +
                            "&timeStamp=" + hxyhFqsk.getTimestamp() +
                            "&nonceStr=" + hxyhFqsk.getNoncestr() +
                            "&package=" + hxyhFqsk.getPackage1() +
                            "&paySign=" + hxyhFqsk.getPaysign();
                    response.sendRedirect(url);
                    b = false;
                } else {
                    wlgbHxyhFqsk.setId(hxyhFqsk.getId());
                }

            }
            if (b) {
                String home = "826551873990079";
                wlgbHxyhFqsk.setHome(home);
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("ddbh", wlgbYddYjWkJl1.getYjskbh() + "WX");
                jsonObject1.put("je", je);
                jsonObject1.put("skmc", skmc);
                jsonObject1.put("openid", openid);
                jsonObject1.put("home", home);
                JSONObject jsonObject2;
                jsonObject2 = YsPayConfig.weiXinPayMdXf(jsonObject1, ddxturl);
                if (jsonObject2 != null && jsonObject2.size() > 0) {
                    JSONObject payInfo = jsonObject2.getJSONObject("jsapi_pay_info");
                    wlgbHxyhFqsk.setYsbh(jsonObject2.getString("trade_no"));
                    wlgbHxyhFqsk.setSkbh(wlgbYddYjWkJl1.getYjskbh());
                    if (payInfo != null && payInfo.size() > 0) {
                        String url = "https://yinshengdj.qianquan888.com/ysdjtb/wxPay.html?appId=" + payInfo.getString("appId") +
                                "&timeStamp=" + payInfo.getString("timeStamp") +
                                "&nonceStr=" + payInfo.getString("nonceStr") +
                                "&package=" + payInfo.getString("package") +
                                "&paySign=" + payInfo.getString("paySign");
                        wlgbHxyhFqsk.setAppid(payInfo.getString("appId"));
                        wlgbHxyhFqsk.setTimestamp(payInfo.getString("timeStamp"));
                        wlgbHxyhFqsk.setNoncestr(payInfo.getString("nonceStr"));
                        wlgbHxyhFqsk.setPackage1(payInfo.getString("package"));
                        wlgbHxyhFqsk.setPaysign(payInfo.getString("paySign"));
                        wlgbHxyhYjWkFqskService.updateById(wlgbHxyhFqsk);
                        response.sendRedirect(url);
                    } else {
                        wlgbHxyhYjWkFqskService.updateById(wlgbHxyhFqsk);
                    }
                }
            }
        } else {
            try {
                response.setContentType("text/html; charset=UTF-8");
                response.getWriter().print("<html><body><script type='text/javascript'>alert('该二维码已失效，请重新生成！');</script></body></html>");
                response.getWriter().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 用户扫码授权跳转发起支付订单(尾款)
     */
    @RequestMapping(value = "wkWxHqCode")
    public void wkWxHqCode(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String code = request.getParameter("code");
        String ddbh = request.getParameter("ddbh");
        WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
        wlgbYddYjWkJl.setXddbh(ddbh);
        wlgbYddYjWkJl.setSfsc(0);
        wlgbYddYjWkJl.setSfwk(1);
        WlgbYddYjWkJl wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
        if (wlgbYddYjWkJl1 != null) {
            String je = request.getParameter("je");
            String skmc = request.getParameter("skmc");
            System.out.println("code：" + code);

            Map<String, String> params = new HashMap<>();
            params.put("secret", "8fae756f3927956f1981eaf54164e6d4");
            params.put("appid", "wx50fbc8ba4f887894");
            params.put("grant_type", "authorization_code");
            params.put("code", code);
            String result = HttpClientUtil.get("https://api.weixin.qq.com/sns/oauth2/access_token", params);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String openid = jsonObject.getString("openid");
            System.out.println(openid);
            WlgbHxyhYjWkFqsk hxyhFqsk = wlgbHxyhYjWkFqskService.queryByDdBhAndFkFs(wlgbYddYjWkJl1.getWkskbh(), "微信");
            boolean b = true;
            WlgbHxyhYjWkFqsk wlgbHxyhFqsk = new WlgbHxyhYjWkFqsk();
            if (hxyhFqsk != null) {
                if (hxyhFqsk.getAppid() != null && !"".equals(hxyhFqsk.getAppid())) {
                    String url = "https://yinshengdj.qianquan888.com/ysdjtb/wxPay.html?appId=" + hxyhFqsk.getAppid() +
                            "&timeStamp=" + hxyhFqsk.getTimestamp() +
                            "&nonceStr=" + hxyhFqsk.getNoncestr() +
                            "&package=" + hxyhFqsk.getPackage1() +
                            "&paySign=" + hxyhFqsk.getPaysign();
                    response.sendRedirect(url);
                    b = false;
                } else {
                    wlgbHxyhFqsk.setId(hxyhFqsk.getId());
                }

            }
            if (b) {
                String home = "826551873990079";
                wlgbHxyhFqsk.setHome(home);
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("ddbh", wlgbYddYjWkJl1.getWkskbh() + "WX");
                jsonObject1.put("je", je);
                jsonObject1.put("skmc", skmc);
                jsonObject1.put("openid", openid);
                jsonObject1.put("home", home);
                JSONObject jsonObject2;
                jsonObject2 = YsPayConfig.weiXinPayYjAndWk(jsonObject1, ddxturl);
                if (jsonObject2 != null && jsonObject2.size() > 0) {
                    JSONObject payInfo = jsonObject2.getJSONObject("jsapi_pay_info");
                    wlgbHxyhFqsk.setYsbh(jsonObject2.getString("trade_no"));
                    wlgbHxyhFqsk.setSkbh(wlgbYddYjWkJl1.getWkskbh());
                    if (payInfo != null && payInfo.size() > 0) {
                        String url = "https://yinshengdj.qianquan888.com/ysdjtb/wxPay.html?appId=" + payInfo.getString("appId") +
                                "&timeStamp=" + payInfo.getString("timeStamp") +
                                "&nonceStr=" + payInfo.getString("nonceStr") +
                                "&package=" + payInfo.getString("package") +
                                "&paySign=" + payInfo.getString("paySign");
                        wlgbHxyhFqsk.setAppid(payInfo.getString("appId"));
                        wlgbHxyhFqsk.setTimestamp(payInfo.getString("timeStamp"));
                        wlgbHxyhFqsk.setNoncestr(payInfo.getString("nonceStr"));
                        wlgbHxyhFqsk.setPackage1(payInfo.getString("package"));
                        wlgbHxyhFqsk.setPaysign(payInfo.getString("paySign"));
                        wlgbHxyhYjWkFqskService.updateById(wlgbHxyhFqsk);
                        response.sendRedirect(url);
                    } else {
                        wlgbHxyhYjWkFqskService.updateById(wlgbHxyhFqsk);
                    }
                }
            }
        } else {
            try {
                response.setContentType("text/html; charset=UTF-8");
                response.getWriter().print("<html><body><script type='text/javascript'>alert('该二维码已失效，请重新生成！');</script></body></html>");
                response.getWriter().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 门第消费到账回调通知处理数据
     */
    @RequestMapping(value = "yjAndWkDz")
    public Result yjAndWkDz(SelectVo selectVo) {
        System.out.println("定金到账回调通知处理数据" + selectVo.getAccount_date());
        if (!"TRADE_SUCCESS".equals(selectVo.getTrade_status())) {
            System.out.println("不是支付成功的订单！");
            return Result.OK();
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", JSONObject.toJSONString(selectVo));
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/pay/yjAndWkDzTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 押金尾款到账处理异步
     */
    @RequestMapping(value = "yjAndWkDzTask")
    public void yjAndWkDzTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        SelectVo selectVo = JSONObject.toJavaObject(jsonObject, SelectVo.class);
        System.out.println(selectVo);
        WlgbHxyhYjWkDzjl wlgbHxyhDzjl = new WlgbHxyhYjWkDzjl();
        wlgbHxyhDzjl.setAccountDate(selectVo.getAccount_date());
        wlgbHxyhDzjl.setBuyerLogonId(selectVo.getBuyer_logon_id());
        wlgbHxyhDzjl.setBuyerUserId(selectVo.getBuyer_user_id());
        wlgbHxyhDzjl.setCardType(selectVo.getCard_type());
        wlgbHxyhDzjl.setChannelRecvSn(selectVo.getChannel_recv_sn());
        wlgbHxyhDzjl.setChannelSendSn(selectVo.getChannel_send_sn());
        wlgbHxyhDzjl.setExtraCommonParam(selectVo.getExtra_common_param());
        wlgbHxyhDzjl.setFee(selectVo.getFee());
        wlgbHxyhDzjl.setIsDiscount(selectVo.getIs_discount());
        wlgbHxyhDzjl.setNotifyTime(selectVo.getNotify_time());
        wlgbHxyhDzjl.setNotifyType(selectVo.getNotify_type());
        wlgbHxyhDzjl.setOutTradeNo(selectVo.getOut_trade_no());
        wlgbHxyhDzjl.setPartnerFee(selectVo.getPartner_fee());
        wlgbHxyhDzjl.setPayeeFee(selectVo.getPayee_fee());
        wlgbHxyhDzjl.setPayerFee(selectVo.getPayer_fee());
        wlgbHxyhDzjl.setPaygateNo(selectVo.getPaygate_no());
        wlgbHxyhDzjl.setSettlementAmount(selectVo.getSettlement_amount());
        wlgbHxyhDzjl.setSign(selectVo.getSign());
        wlgbHxyhDzjl.setSignType(selectVo.getSign_type());
        wlgbHxyhDzjl.setTotalAmount(selectVo.getTotal_amount());
        wlgbHxyhDzjl.setTotalDiscount(selectVo.getTotal_discount());
        wlgbHxyhDzjl.setTotalDiscountFee(selectVo.getTotal_discount_fee());
        wlgbHxyhDzjl.setTradeNo(selectVo.getTrade_no());
        wlgbHxyhDzjl.setTradeStatus(selectVo.getTrade_status());

        String outTradeNo = selectVo.getOut_trade_no();
        if (outTradeNo != null && outTradeNo.contains("ZFB")) {
            outTradeNo = outTradeNo.replace("ZFB", "");
        }
        if (outTradeNo != null && outTradeNo.contains("WX")) {
            outTradeNo = outTradeNo.replace("WX", "");
        }

        WlgbHxyhYjWkDzjl hxyhDzjl = wlgbHxyhYjWkDzjlService.queryByYsBhAndLsBh(selectVo.getTrade_no(),
                selectVo.getOut_trade_no());
        if (hxyhDzjl != null) {
            if (!(hxyhDzjl.getTradeStatus() != null ? hxyhDzjl.getTradeStatus() : "").equals(selectVo.getTrade_status())) {
                wlgbHxyhDzjl.setId(hxyhDzjl.getId());
                wlgbHxyhYjWkDzjlService.updateById(wlgbHxyhDzjl);
            }
            return;
        }
        wlgbHxyhYjWkDzjlService.save(wlgbHxyhDzjl);

        List<WlgbHxyhYjWkFqsk> list = wlgbHxyhYjWkFqskService.queryByDdBhAndYsBh(outTradeNo, wlgbHxyhDzjl.getTradeNo());
        WlgbHxyhYjWkFqsk hxyhFqsk = null;
        if (list != null && list.size() > 0) {
            hxyhFqsk = list.get(0);
        }
        if (hxyhFqsk != null) {
            list.forEach(l -> {
                WlgbHxyhYjWkFqsk wlgbHxyhFqsk = new WlgbHxyhYjWkFqsk();
                wlgbHxyhFqsk.setId(l.getId());
                wlgbHxyhFqsk.setZfzt("是");
                wlgbHxyhYjWkFqskService.updateById(wlgbHxyhFqsk);
            });

        }

        WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
        wlgbYddYjWkJl.setSfsc(0);
        wlgbYddYjWkJl.setYjskbh(outTradeNo);
        WlgbYddYjWkJl wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
        if (wlgbYddYjWkJl1 == null) {
            wlgbYddYjWkJl.setYjskbh(null);
            wlgbYddYjWkJl.setWkskbh(outTradeNo);
            wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
        }

        //修改记录表是否到账
        if (wlgbYddYjWkJl1 != null) {
            if (hxyhFqsk != null) {
                TbXyd tbXyd = tbXydService.queryById(wlgbYddYjWkJl1.getXid());
                Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
                TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
                WlgbYddYjWkJl wlgbYddYjWkJl2 = new WlgbYddYjWkJl();
                wlgbYddYjWkJl2.setId(wlgbYddYjWkJl1.getId());
                wlgbYddYjWkJl2.setShh(hxyhFqsk.getHome());
                if ("押金".equals(hxyhFqsk.getJetype())) {
                    wlgbYddYjWkJl2.setYjdztime(new Date());
                    wlgbYddYjWkJl2.setYjsfdz(1);
                    if (wlgbYddYjWkJl1.getFqrid() != null && !"".equals(wlgbYddYjWkJl1.getFqrid())) {
                        String text = "您发起的押金二维码收款已到账，请注意确认！";
                        text += "\n\n订单编号：" + wlgbYddYjWkJl1.getXddbh();
                        text += "\n门店：" + villa.getVname();
                        text += "\n进场时间：" + DateFormatConfig.df3(tbXyd.getXjctime());
                        text += "\n退场时间：" + DateFormatConfig.df3(tbXyd.getXtctime());
                        text += "\n到账金额：" + (wlgbHxyhDzjl.getTotalAmount() != null ? wlgbHxyhDzjl.getTotalAmount() : 0) + "元";
                        text += "\n发起收款时间：" + DateFormatConfig.df1(wlgbYddYjWkJl1.getYjsctime());
                        text += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
                        try {
                            DingDBConfig.sendGztzText(dingkey, wlgbYddYjWkJl1.getFqrid(), text);
                        } catch (ApiException apiException) {
                            apiException.printStackTrace();
                        }
                    }
                    WlgbXydLog wlgbXydLog = new WlgbXydLog();
                    wlgbXydLog.setXlid(IdConfig.uuId());
                    wlgbXydLog.setXltime(new Date());
                    wlgbXydLog.setXlxid(wlgbYddYjWkJl1.getXid());
                    wlgbXydLog.setXluserid("客户支付");
                    wlgbXydLog.setXlname("客户支付");
                    wlgbXydLog.setXltext("客户支付押金" + (wlgbHxyhDzjl.getTotalAmount() != null ? wlgbHxyhDzjl.getTotalAmount() : 0) + "元");
                    wlgbXydLogService.save(wlgbXydLog);
                }
                if ("尾款".equals(hxyhFqsk.getJetype())) {
                    wlgbYddYjWkJl2.setWkdztime(new Date());
                    wlgbYddYjWkJl2.setWksfdz(1);

                    if (wlgbYddYjWkJl1.getFqrid() != null && !"".equals(wlgbYddYjWkJl1.getFqrid())) {
                        String text = "您发起的尾款二维码收款已到账，请注意确认！";
                        text += "\n\n订单编号：" + wlgbYddYjWkJl1.getXddbh();
                        text += "\n门店：" + villa.getVname();
                        text += "\n进场时间：" + DateFormatConfig.df3(tbXyd.getXjctime());
                        text += "\n退场时间：" + DateFormatConfig.df3(tbXyd.getXtctime());
                        text += "\n到账金额：" + (wlgbHxyhDzjl.getTotalAmount() != null ? wlgbHxyhDzjl.getTotalAmount() : 0) + "元";
                        text += "\n发起收款时间：" + DateFormatConfig.df1(wlgbYddYjWkJl1.getWksctime());
                        text += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
                        try {
                            DingDBConfig.sendGztzText(dingkey, wlgbYddYjWkJl1.getFqrid(), text);
                        } catch (ApiException apiException) {
                            apiException.printStackTrace();
                        }
                    }
                    WlgbXydLog wlgbXydLog = new WlgbXydLog();
                    wlgbXydLog.setXlid(IdConfig.uuId());
                    wlgbXydLog.setXltime(new Date());
                    wlgbXydLog.setXlxid(wlgbYddYjWkJl1.getXid());
                    wlgbXydLog.setXluserid("客户支付");
                    wlgbXydLog.setXlname("客户支付");
                    wlgbXydLog.setXltext("客户支付尾款" + (wlgbHxyhDzjl.getTotalAmount() != null ? wlgbHxyhDzjl.getTotalAmount() : 0) + "元");
                    wlgbXydLogService.save(wlgbXydLog);
                }
                wlgbYddYjWkJlService.updateById(wlgbYddYjWkJl2);

                WlgbYddYjWkJl wlgbYddYjWkJl3 = wlgbYddYjWkJlService.queryById(wlgbYddYjWkJl1.getId());
                boolean b = true;
                if (wlgbYddYjWkJl3.getSfyj() == 1) {
                    if (wlgbYddYjWkJl3.getYjsfdz() == 0) {
                        b = false;
                    }
                }
                if (wlgbYddYjWkJl3.getSfwk() == 1) {
                    if (wlgbYddYjWkJl3.getWksfdz() == 0) {
                        b = false;
                    }
                }
                //同步流程表单----同步小程序（两笔都到账操作）等其他操作
                if (b) {
                    if (wlgbYddYjWkJl3.getFqrid() != null && !"".equals(wlgbYddYjWkJl3.getFqrid())) {
                        String text = "您发起的押金与尾款收款已全部到账，请注意待办完成后续进场流程操作！";
                        text += "\n\n订单编号：" + wlgbYddYjWkJl1.getXddbh();
                        text += "\n门店：" + villa.getVname();
                        text += "\n进场时间：" + DateFormatConfig.df3(tbXyd.getXjctime());
                        text += "\n退场时间：" + DateFormatConfig.df3(tbXyd.getXtctime());
                        text += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
                        try {
                            DingDBConfig.sendGztzText(dingkey, wlgbYddYjWkJl1.getFqrid(), text);
                        } catch (ApiException apiException) {
                            apiException.printStackTrace();
                        }
                    }
                    YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");

                    String token = null;
                    try {
                        token = DingToken.token(dingkey);
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                    GatewayResult gatewayResult = new GatewayResult();
                    try {
                        gatewayResult = YdConfig.querySpJl(token, ydAppkey, wlgbYddYjWkJl1.getSlid());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (gatewayResult.getSuccess()) {
                        JSONObject map = new JSONObject();
                        map.put("dateField_lt15rkkm", wlgbYddYjWkJl3.getYjdztime());
                        //支付时间
                        map.put("dateField_lt15rkkn", wlgbYddYjWkJl3.getWkdztime());
                        //支付后六位
                        map.put("xzzhlw", selectVo.getOut_trade_no());
                        JSONArray objects = JSONObject.parseArray(gatewayResult.getResult());
                        for (Object obj : objects) {
                            JSONObject jsonObject1 = (JSONObject) obj;
                            if ("TODO".equals(jsonObject1.getString("type")) && "客户支付尾款".equals(jsonObject1.getString("showName"))) {
                                System.out.println("查到任务");
                                long taskId = jsonObject1.getLong("taskId");
                                //准备执行任务
                                GatewayResult gatewayResult1 = null;
                                try {
                                    gatewayResult1 = YdConfig.zxYdSpJdRw(token, wlgbYddYjWkJl1.getSlid(), ydAppkey, "客户已支付押金尾款", map.toJSONString(), taskId, "012412221639786136545");
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                System.out.println(gatewayResult1);
                            }
                        }
                    }

                    TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(wlgbYddYjWkJl3.getXid());
                    TbYddXyd tbYddXyd1 = new TbYddXyd();
                    tbYddXyd1.setXid(wlgbYddYjWkJl3.getXid());
                    tbYddXyd1.setXstatu("已交尾款，消费中");
                    tbYddXydService.updateById(tbYddXyd1);
                    String post = null;
                    try {
                        Map<String, String> map = new HashMap<>();
                        map.put("ddbh", wlgbYddYjWkJl3.getXddbh());
                        map.put("zt", "已交尾款，消费中");
                        //总价
                        map.put("zj", tbYddXyd.getXhfyj() + tbYddXyd.getXztze() + tbYddXyd.getXdcze() + tbYddXyd.getXhpsfy() + tbYddXyd.getXbxzje() + "");
                        post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/b2e50678-62d2-4d65-a042-27427265a26f", map);
                    } catch (Exception e) {
                        e.printStackTrace();
                        try {
                            String context1 = "押金尾款到账将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，错误原因：" + e;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                    }
                    try {
                        if (post != null && !"".equals(post)) {
                            JSONObject jsonObject1 = JSONObject.parseObject(post);
                            String jg = jsonObject1.getString("jg");
                            if (!"ok".equalsIgnoreCase(jg)) {
                                try {
                                    String context1 = "押金尾款到账将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post;
                                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                } catch (ApiException e1) {
                                    e1.printStackTrace();
                                }
                            }
                        }
                    } catch (Exception e) {
                        try {
                            String context1 = "押金尾款到账将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                        e.printStackTrace();
                    }
                } else {
                    System.out.println("还有一笔未到账");
                }
            }
        }
    }

    /**
     * 门店消费到账回调通知处理数据
     */
    @RequestMapping(value = "mdXfDz")
    public Result mdXfDz(SelectVo selectVo) {
        System.out.println("定金到账回调通知处理数据" + selectVo.getAccount_date());
        if (!"TRADE_SUCCESS".equals(selectVo.getTrade_status())) {
            System.out.println("不是支付成功的订单！");
            return Result.OK();
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", JSONObject.toJSONString(selectVo));
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/pay/mdXfDzTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 门店消费到账处理异步
     */
    @RequestMapping(value = "mdXfDzTask")
    public void mdXfDzTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        SelectVo selectVo = JSONObject.toJavaObject(jsonObject, SelectVo.class);
        System.out.println(selectVo);
        WlgbHxyhYjWkDzjl wlgbHxyhDzjl = new WlgbHxyhYjWkDzjl();
        wlgbHxyhDzjl.setAccountDate(selectVo.getAccount_date());
        wlgbHxyhDzjl.setBuyerLogonId(selectVo.getBuyer_logon_id());
        wlgbHxyhDzjl.setBuyerUserId(selectVo.getBuyer_user_id());
        wlgbHxyhDzjl.setCardType(selectVo.getCard_type());
        wlgbHxyhDzjl.setChannelRecvSn(selectVo.getChannel_recv_sn());
        wlgbHxyhDzjl.setChannelSendSn(selectVo.getChannel_send_sn());
        wlgbHxyhDzjl.setExtraCommonParam(selectVo.getExtra_common_param());
        wlgbHxyhDzjl.setFee(selectVo.getFee());
        wlgbHxyhDzjl.setIsDiscount(selectVo.getIs_discount());
        wlgbHxyhDzjl.setNotifyTime(selectVo.getNotify_time());
        wlgbHxyhDzjl.setNotifyType(selectVo.getNotify_type());
        wlgbHxyhDzjl.setOutTradeNo(selectVo.getOut_trade_no());
        wlgbHxyhDzjl.setPartnerFee(selectVo.getPartner_fee());
        wlgbHxyhDzjl.setPayeeFee(selectVo.getPayee_fee());
        wlgbHxyhDzjl.setPayerFee(selectVo.getPayer_fee());
        wlgbHxyhDzjl.setPaygateNo(selectVo.getPaygate_no());
        wlgbHxyhDzjl.setSettlementAmount(selectVo.getSettlement_amount());
        wlgbHxyhDzjl.setSign(selectVo.getSign());
        wlgbHxyhDzjl.setSignType(selectVo.getSign_type());
        wlgbHxyhDzjl.setTotalAmount(selectVo.getTotal_amount());
        wlgbHxyhDzjl.setTotalDiscount(selectVo.getTotal_discount());
        wlgbHxyhDzjl.setTotalDiscountFee(selectVo.getTotal_discount_fee());
        wlgbHxyhDzjl.setTradeNo(selectVo.getTrade_no());
        wlgbHxyhDzjl.setTradeStatus(selectVo.getTrade_status());

        String outTradeNo = selectVo.getOut_trade_no();
        if (outTradeNo != null && outTradeNo.contains("ZFB")) {
            outTradeNo = outTradeNo.replace("ZFB", "");
        }
        if (outTradeNo != null && outTradeNo.contains("WX")) {
            outTradeNo = outTradeNo.replace("WX", "");
        }

        WlgbHxyhYjWkDzjl hxyhDzjl = wlgbHxyhYjWkDzjlService.queryByYsBhAndLsBh(selectVo.getTrade_no(),
                selectVo.getOut_trade_no());
        if (hxyhDzjl != null) {
            if (!(hxyhDzjl.getTradeStatus() != null ? hxyhDzjl.getTradeStatus() : "").equals(selectVo.getTrade_status())) {
                wlgbHxyhDzjl.setId(hxyhDzjl.getId());
                wlgbHxyhYjWkDzjlService.updateById(wlgbHxyhDzjl);
            }
            return;
        }
        wlgbHxyhYjWkDzjlService.save(wlgbHxyhDzjl);

        List<WlgbHxyhYjWkFqsk> list = wlgbHxyhYjWkFqskService.queryByDdBhAndYsBh(outTradeNo, wlgbHxyhDzjl.getTradeNo());
        WlgbHxyhYjWkFqsk hxyhFqsk = null;
        if (list != null && list.size() > 0) {
            hxyhFqsk = list.get(0);
        }
        if (hxyhFqsk != null) {
            list.forEach(l -> {
                WlgbHxyhYjWkFqsk wlgbHxyhFqsk = new WlgbHxyhYjWkFqsk();
                wlgbHxyhFqsk.setId(l.getId());
                wlgbHxyhFqsk.setZfzt("是");
                wlgbHxyhYjWkFqskService.updateById(wlgbHxyhFqsk);
            });

        }

        WlgbYddMdxfjl wlgbYddMdxfjl = new WlgbYddMdxfjl();
        wlgbYddMdxfjl.setSfsc(0);
        wlgbYddMdxfjl.setSkbh(outTradeNo);
        WlgbYddMdxfjl wlgbYddMdxfjl1 = wlgbYddMdxfjlService.queryWlgbYddMdxfjlByWlgbYddMdxfjl(wlgbYddMdxfjl);
        if (wlgbYddMdxfjl1 == null) {
            System.out.println("找不到对应的发起数据");
            return;
        }

        //修改记录表是否到账
        if (hxyhFqsk != null) {
            TbXyd tbXyd = tbXydService.queryById(wlgbYddMdxfjl1.getXid());
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
            WlgbYddMdxfjl wlgbYddMdxfjl2 = new WlgbYddMdxfjl();
            wlgbYddMdxfjl2.setId(wlgbYddMdxfjl1.getId());
            wlgbYddMdxfjl2.setShh(hxyhFqsk.getHome());
            wlgbYddMdxfjl2.setDztime(new Date());
            wlgbYddMdxfjl2.setSfdz(1);
            wlgbYddMdxfjl2.setYsbh(selectVo.getTrade_no());
            wlgbYddMdxfjl2.setShlsh(selectVo.getOut_trade_no());
            if (wlgbYddMdxfjl1.getUserid() != null && !"".equals(wlgbYddMdxfjl1.getUserid())) {
                String text = "您发起的门店消费二维码收款已到账，请注意确认！";
                text += "\n\n订单编号：" + tbXyd.getXddbh();
                text += "\n门店：" + villa.getVname();
                text += "\n进场时间：" + DateFormatConfig.df3(tbXyd.getXjctime());
                text += "\n退场时间：" + DateFormatConfig.df3(tbXyd.getXtctime());
                text += "\n到账金额：" + (wlgbHxyhDzjl.getTotalAmount() != null ? wlgbHxyhDzjl.getTotalAmount() : 0) + "元";
                text += "\n发起收款时间：" + DateFormatConfig.df1(wlgbYddMdxfjl1.getSctime());
                text += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
                try {
                    DingDBConfig.sendGztzText(dingkey, wlgbYddMdxfjl1.getUserid(), text);
                } catch (ApiException apiException) {
                    apiException.printStackTrace();
                }
            }
            WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
            wlgbYddYjWkJl.setSfsc(0);
            wlgbYddYjWkJl.setSfyj(1);
            wlgbYddYjWkJl.setYjsftk(0);
            wlgbYddYjWkJl.setYjsfdz(1);
            wlgbYddYjWkJl.setXddbh(tbXyd.getXddbh());
            WlgbYddYjWkJl wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
            if (wlgbYddYjWkJl1 != null) {
                try {
                    WlgbHxyhYjWkDzjl wlgbHxyhYjWkDzjl = new WlgbHxyhYjWkDzjl();
                    wlgbHxyhYjWkDzjl.setOutTradeNo(wlgbYddYjWkJl1.getYjskbh() + "WX");
                    WlgbHxyhYjWkDzjl wlgbHxyhYjWkDzjl1 = wlgbHxyhYjWkDzjlService.queryWlgbHxyhYjWkDzjlByWlgbHxyhYjWkDzjl(wlgbHxyhYjWkDzjl);
                    if (wlgbHxyhYjWkDzjl1 == null) {
                        wlgbHxyhYjWkDzjl.setOutTradeNo(wlgbYddYjWkJl1.getYjskbh() + "ZFB");
                        wlgbHxyhYjWkDzjl1 = wlgbHxyhYjWkDzjlService.queryWlgbHxyhYjWkDzjlByWlgbHxyhYjWkDzjl(wlgbHxyhYjWkDzjl);
                    }
                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("shh", wlgbYddYjWkJl1.getShh());
                    jsonObject1.put("ddbh", wlgbHxyhYjWkDzjl1.getOutTradeNo());
                    jsonObject1.put("ysbh", wlgbHxyhYjWkDzjl1.getTradeNo());
                    jsonObject1.put("tkje", wlgbYddYjWkJl1.getYjje());
                    jsonObject1.put("tkmc", wlgbYddYjWkJl1.getXddbh() + "押金退款");
                    YsPayConfig.tk(ddxturl, jsonObject1);

                    WlgbYddYjwktkjl wlgbYddYjwktkjl = new WlgbYddYjwktkjl();
                    wlgbYddYjwktkjl.setXddbh(tbXyd.getXddbh());
                    wlgbYddYjwktkjl.setSkbh(wlgbHxyhYjWkDzjl1.getOutTradeNo());
                    wlgbYddYjwktkjl.setYsbh(wlgbHxyhYjWkDzjl1.getTradeNo());
                    wlgbYddYjwktkjl.setTkje(wlgbHxyhYjWkDzjl1.getTotalAmount());
                    wlgbYddYjwktkjl.setType("押金");
                    wlgbYddYjwktkjlService.save(wlgbYddYjwktkjl);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            WlgbXydLog wlgbXydLog = new WlgbXydLog();
            wlgbXydLog.setXlid(IdConfig.uuId());
            wlgbXydLog.setXltime(new Date());
            wlgbXydLog.setXlxid(tbXyd.getXid());
            wlgbXydLog.setXluserid("客户支付");
            wlgbXydLog.setXlname("客户支付");
            wlgbXydLog.setXltext("客户支付门店消费金额" + (wlgbHxyhDzjl.getTotalAmount() != null ? wlgbHxyhDzjl.getTotalAmount() : 0) + "元");
            wlgbXydLogService.save(wlgbXydLog);
            wlgbYddMdxfjlService.updateById(wlgbYddMdxfjl2);

            YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");

            String token = null;
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            GatewayResult gatewayResult = new GatewayResult();
            try {
                gatewayResult = YdConfig.querySpJl(token, ydAppkey, wlgbYddMdxfjl1.getSlid());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (gatewayResult.getSuccess()) {
                JSONObject map = new JSONObject();
                //支付时间
                map.put("qzzsj", wlgbYddMdxfjl2.getDztime());
                //支付订单号
                map.put("textField_lw0skj6e", wlgbYddMdxfjl2.getShlsh());
                JSONArray objects = JSONObject.parseArray(gatewayResult.getResult());
                for (Object obj : objects) {
                    JSONObject jsonObject1 = (JSONObject) obj;
                    if ("TODO".equals(jsonObject1.getString("type")) && "支付门店消费金额".equals(jsonObject1.getString("showName"))) {
                        System.out.println("查到任务");
                        long taskId = jsonObject1.getLong("taskId");
                        //准备执行任务
                        GatewayResult gatewayResult1 = null;
                        try {
                            gatewayResult1 = YdConfig.zxYdSpJdRw(token, wlgbYddMdxfjl1.getSlid(), ydAppkey, "客户已支付门店消费金额", map.toJSONString(), taskId, "012412221639786136545");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        System.out.println(gatewayResult1);
                    }
                }
            }
        }
    }

    /**
     * 押金尾款退款
     */
    @RequestMapping(value = "tjYjWkTk")
    public Result tjYjWkTk(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        String tkje = request.getParameter("tkje");
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号不能为空");
        }
        if (tkje == null || "".equals(tkje)) {
            return Result.error("退款金额不能为空");
        }
        String type = request.getParameter("type");
        if (type == null || "".equals(type)) {
            return Result.error("退款类型不能为空");
        }
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXddbh(ddbh);
        TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd);
        if (tbYddXyd1 == null) {
            return Result.error("找不到对应的订单");
        }
        WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
        wlgbYddYjWkJl.setSfsc(0);
        wlgbYddYjWkJl.setXddbh(ddbh);
        WlgbYddYjWkJl wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
        if (wlgbYddYjWkJl1 == null) {
            return Result.error("找不到支付信息");
        }
        WlgbHxyhYjWkDzjl wlgbHxyhYjWkDzjl = new WlgbHxyhYjWkDzjl();
        if ("押金".equals(type)) {
            wlgbHxyhYjWkDzjl.setOutTradeNo(wlgbYddYjWkJl1.getYjskbh() + "WX");
        } else {
            wlgbHxyhYjWkDzjl.setOutTradeNo(wlgbYddYjWkJl1.getWkskbh() + "WX");
        }
        WlgbHxyhYjWkDzjl wlgbHxyhYjWkDzjl1 = wlgbHxyhYjWkDzjlService.queryWlgbHxyhYjWkDzjlByWlgbHxyhYjWkDzjl(wlgbHxyhYjWkDzjl);
        if (wlgbHxyhYjWkDzjl1 == null) {
            if ("押金".equals(type)) {
                wlgbHxyhYjWkDzjl.setOutTradeNo(wlgbYddYjWkJl1.getYjskbh() + "ZFB");
            } else {
                wlgbHxyhYjWkDzjl.setOutTradeNo(wlgbYddYjWkJl1.getWkskbh() + "ZFB");
            }
            wlgbHxyhYjWkDzjl1 = wlgbHxyhYjWkDzjlService.queryWlgbHxyhYjWkDzjlByWlgbHxyhYjWkDzjl(wlgbHxyhYjWkDzjl);
        }
        if (wlgbHxyhYjWkDzjl1 == null) {
            return Result.error("找不到对应的支付信息");
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("shh", wlgbYddYjWkJl1.getShh());
        jsonObject.put("ddbh", wlgbHxyhYjWkDzjl1.getOutTradeNo());
        jsonObject.put("ysbh", wlgbHxyhYjWkDzjl1.getTradeNo());
        jsonObject.put("tkje", tkje);
        jsonObject.put("tkmc", wlgbYddYjWkJl1.getXddbh() + type + "退款");
        YsPayConfig.tk(ddxturl, jsonObject);

        WlgbYddYjwktkjl wlgbYddYjwktkjl = new WlgbYddYjwktkjl();
        wlgbYddYjwktkjl.setXddbh(tbYddXyd1.getXddbh());
        wlgbYddYjwktkjl.setSkbh(wlgbHxyhYjWkDzjl1.getOutTradeNo());
        wlgbYddYjwktkjl.setYsbh(wlgbHxyhYjWkDzjl1.getTradeNo());
        wlgbYddYjwktkjl.setTkje(Double.parseDouble(tkje));
        wlgbYddYjwktkjl.setType(type);
        wlgbYddYjwktkjlService.save(wlgbYddYjwktkjl);
        return Result.OK();
    }

    @RequestMapping(value = "tkCgTz")
    public Result tkCgTz(TkVo tkVo) {
        System.out.println(tkVo);
        String trade_no = tkVo.getTrade_no();
        WlgbYddYjwktkjl wlgbYddYjwktkjl = new WlgbYddYjwktkjl();
        wlgbYddYjwktkjl.setYsbh(trade_no);
        wlgbYddYjwktkjl.setTkje(tkVo.getTotal_amount());
        WlgbYddYjwktkjl wlgbYddYjwktkjl1 = wlgbYddYjwktkjlService.queryWlgbYddYjwktkjlByWlgbYddYjwktkjl(wlgbYddYjwktkjl);
        if (wlgbYddYjwktkjl1 == null) {
            System.out.println("找不到对应的退款记录");
            return Result.OK();
        }
        WlgbYddYjwktkjl wlgbYddYjwktkjl2 = new WlgbYddYjwktkjl();
        wlgbYddYjwktkjl2.setTksfcg(1);
        wlgbYddYjwktkjl2.setTkcgje(tkVo.getTotal_amount());
        wlgbYddYjwktkjl2.setId(wlgbYddYjwktkjl1.getId());
        wlgbYddYjwktkjlService.updateById(wlgbYddYjwktkjl2);
        return Result.OK();
    }


    /**
     * 预定单押金尾款支付完成节点跳过问题处理
     */
    @RequestMapping(value = "yddJcLcJdCl")
    public Result yddJcLcJdCl(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        TbXyd tbXyd = tbXydService.queryByDdBh(ddbh);
        if (tbXyd == null) {
            return Result.OK("订单不存在");
        }
        TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(tbXyd.getXid());
        if (tbYddXyd == null) {
            return Result.OK("非预定单下单");
        }
        WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
        wlgbYddYjWkJl.setXddbh(tbXyd.getXddbh());
        wlgbYddYjWkJl.setSfsc(0);
        List<WlgbYddYjWkJl> list = wlgbYddYjWkJlService.queryListByWlgbYddYjWkJl(wlgbYddYjWkJl);
        WlgbYddYjWkJl wlgbYddYjWkJl1 = null;
        for (WlgbYddYjWkJl l : list) {
            boolean b = false;
            boolean b1 = false;
            if (l.getSfwk() == 1) {
                if (l.getWksfdz() == 1) {
                    b = true;
                }
            } else {
                b = true;
            }
            if (l.getSfyj() == 1) {
                if (l.getYjsfdz() == 1) {
                    b1 = true;
                }
            } else {
                b1 = true;
            }
            if (b && b1) {
                wlgbYddYjWkJl1 = l;
            }
        }
        if (wlgbYddYjWkJl1 == null) {
            return Result.OK("没有对应的支付记录");
        }

        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");

        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = new GatewayResult();
        try {
            gatewayResult = YdConfig.querySpJl(token, ydAppkey, wlgbYddYjWkJl1.getSlid());
        } catch (Exception e) {
            e.printStackTrace();
        }
        boolean b = true;
        if (gatewayResult.getSuccess()) {
            JSONObject map = new JSONObject();
            JSONArray objects = JSONObject.parseArray(gatewayResult.getResult());
            for (Object obj : objects) {
                JSONObject jsonObject1 = (JSONObject) obj;
                if ("TODO".equals(jsonObject1.getString("type")) && "客户支付尾款".equals(jsonObject1.getString("showName"))) {
                    b = false;
                    System.out.println("查到任务");
                    long taskId = jsonObject1.getLong("taskId");
                    //准备执行任务
                    GatewayResult gatewayResult1 = null;
                    try {
                        gatewayResult1 = YdConfig.zxYdSpJdRw(token, wlgbYddYjWkJl1.getSlid(), ydAppkey, "客户已支付押金尾款", map.toJSONString(), taskId, "012412221639786136545");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    System.out.println(gatewayResult1);
                }
            }
        }
        if (b) {
            return Result.OK("当前不在支付节点");
        }
        return Result.OK();
    }
}
