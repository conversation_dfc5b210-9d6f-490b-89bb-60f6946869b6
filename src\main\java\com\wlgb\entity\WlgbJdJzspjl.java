package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 借支审批记录
 * @Author: jeecg-boot
 * @Date:   2022-05-21
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_jzspjl")
public class WlgbJdJzspjl {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**审批编号*/
    private java.lang.String spbh;
	/**审批标题*/
    private java.lang.String spbt;
	/**申请时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date sqsj;
	/**审批结束时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date spjssj;
	/**申请人*/
    private java.lang.String sqr;
	/**申请人id*/
    private java.lang.String sqrid;
	/**申请人部门*/
    private java.lang.String sqrbm;
	/**申请人部门id*/
    private java.lang.String sqrbmid;
	/**借支使用方*/
    private java.lang.String jzsyf;
	/**费用类别*/
    private java.lang.String fylb;
	/**借支用途*/
    private java.lang.String jzyt;
	/**预计还款时间*/
    private java.lang.String yjhkrq;
	/**本人累计借支*/
    private java.lang.Double brljjz;
	/**本次借支金额*/
    private java.lang.Double bcjzje;
	/**收款帐号*/
    private java.lang.String skzh;
	/**开户行*/
    private java.lang.String khh;
	/**出纳转账时间*/
    private java.lang.String cnzzsj;
	/**出纳实际转账金额*/
    private java.lang.Double cnsjzzje;
	/**是否录入金蝶*/
    private java.lang.Integer sflrjd;
    /**审批实例id*/
    private java.lang.String slid;
}
