package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqSmile;
import com.wlgb.mapper.FwqSmileMapper;
import com.wlgb.service2.FwqSmileService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.wlgb.config.oss.oConvertUtils.isEmpty;

@Service
@DS(value = "second")
public class FwqSmileServiceImpl implements FwqSmileService {
    @Resource
    private FwqSmileMapper fwqSmileMapper;

    @Override
    public void save(FwqSmile fwqSmile) {
        fwqSmileMapper.insertSelective(fwqSmile);
    }

    @Override
    public FwqSmile selectJTOne(Map<String, Object> map) {
        Example example = new Example(FwqSmile.class);
        Example.Criteria criteria = example.createCriteria();
        //只查询某一个人
        if (!isEmpty(map.get("userid"))) {
            criteria.andEqualTo("userid", map.get("userid"));
        }
        //是否有效
        if (!isEmpty(map.get("sfyx"))) {
            criteria.andEqualTo("sfyx", map.get("sfyx"));
        }
        //上午还是下午
        if (!isEmpty(map.get("swxw"))) {
            criteria.andEqualTo("swxw", map.get("swxw"));
        }
        //日期
        if (!isEmpty(map.get("startOfToday"))) {
            criteria.andGreaterThanOrEqualTo("sctime", map.get("startOfToday")); // sctime >= 今天 00:00:00
            criteria.andLessThanOrEqualTo("sctime", map.get("endOfToday"));     // sctime <= 今天 23:59:59
        }
        return fwqSmileMapper.selectOneByExample(example);
    }

    @Override
    public List<FwqSmile> selectAllByUserid(String userid) {
        Example example = new Example(FwqSmile.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userid", userid);
        criteria.andEqualTo("sfyx", "0");
        // 新增：筛选最近10天的数据
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -10); // 当前日期减去10天
        Date tenDaysAgo = calendar.getTime();
        criteria.andGreaterThanOrEqualTo("sctime", tenDaysAgo); // 查询 sctime >= 十天前
        example.setOrderByClause("sctime DESC"); // 按时间降序排列
        return fwqSmileMapper.selectByExample(example);
    }
}
