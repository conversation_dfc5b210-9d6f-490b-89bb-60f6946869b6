package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdDyzsqjl;
import com.wlgb.mapper.WlgbJdDyzsqjlMapper;
import com.wlgb.service.WlgbJdDyzsqjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 22:43
 */
@Service
public class WlgbJdDyzsqjlServiceImpl implements WlgbJdDyzsqjlService {
    @Resource
    private WlgbJdDyzsqjlMapper wlgbJdDyzsqjlMapper;

    @Override
    public void save(WlgbJdDyzsqjl wlgbJdDyzsqjl) {
        wlgbJdDyzsqjl.setCreateTime(new Date());
        wlgbJdDyzsqjl.setId(IdConfig.uuId());
        wlgbJdDyzsqjlMapper.insertSelective(wlgbJdDyzsqjl);
    }

    @Override
    public void updateById(WlgbJdDyzsqjl wlgbJdDyzsqjl) {
        wlgbJdDyzsqjl.setUpdateTime(new Date());
        wlgbJdDyzsqjlMapper.updateByPrimaryKeySelective(wlgbJdDyzsqjl);
    }

    @Override
    public WlgbJdDyzsqjl queryBySpBhAndSfLrJd(String spbh, Integer sfLrJd) {
        Example example = new Example(WlgbJdDyzsqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spbh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdDyzsqjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdDyzsqjl> queryListWlgbJdDyzsqjl(WlgbJdDyzsqjl wlgbJdDyzsqjl) {
        return wlgbJdDyzsqjlMapper.select(wlgbJdDyzsqjl);
    }
}
