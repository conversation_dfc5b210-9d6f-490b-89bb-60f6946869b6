package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.config.IdConfig;
import com.wlgb.config.oss.CommonUtils;
import com.wlgb.config.oss.OssBootUtil;
import com.wlgb.entity.OssFile;
import com.wlgb.mapper.OssFileMapper;
import com.wlgb.service4.DhRjOssFileService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/20 18:13
 */
@Service
@DS("fourth")
public class DhRjOssFileServiceImpl implements DhRjOssFileService {

    @Resource
    private OssFileMapper ossFileMapper;

    @Override
    public String upload(MultipartFile multipartFile) {
        String fileName = multipartFile.getOriginalFilename();
        fileName = CommonUtils.getFileName(fileName);
        OssFile ossFile = new OssFile();
        ossFile.setFileName(fileName);
        String url = OssBootUtil.upload(multipartFile, "dhrj/xydtp");
        if(url != null && !"".equals(url)){
            url = url.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
        }
        ossFile.setId(IdConfig.uuId());
        ossFile.setUrl(url);
        ossFile.setCreateTime(new Date());
        ossFileMapper.insertSelective(ossFile);
        return url;
    }
}
