package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdTdjsqjl;
import com.wlgb.mapper.WlgbJdTdjsqjlMapper;
import com.wlgb.service.WlgbJdTdjsqjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/30 14:15
 */
@Service
public class WlgbJdTdjsqjlServiceImpl implements WlgbJdTdjsqjlService {
    @Resource
    private WlgbJdTdjsqjlMapper wlgbJdTdjsqjlMapper;

    @Override
    public void save(WlgbJdTdjsqjl wlgbJdTdjsqjl) {
        wlgbJdTdjsqjl.setId(IdConfig.uuId());
        wlgbJdTdjsqjl.setCreateTime(new Date());
        wlgbJdTdjsqjlMapper.insertSelective(wlgbJdTdjsqjl);
    }

    @Override
    public void updateById(WlgbJdTdjsqjl wlgbJdTdjsqjl) {
        wlgbJdTdjsqjl.setUpdateTime(new Date());
        wlgbJdTdjsqjlMapper.updateByPrimaryKeySelective(wlgbJdTdjsqjl);
    }

    @Override
    public WlgbJdTdjsqjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd) {
        Example example = new Example(WlgbJdTdjsqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdTdjsqjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdTdjsqjl> queryListByWlgbJdTdjsqjl(WlgbJdTdjsqjl wlgbJdTdjsqjl) {
        return wlgbJdTdjsqjlMapper.select(wlgbJdTdjsqjl);
    }
}
