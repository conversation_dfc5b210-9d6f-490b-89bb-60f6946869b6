package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.HrGwCity;
import com.wlgb.mapper.HrGwCityMapper;
import com.wlgb.service2.HrGwCityService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/31 20:10
 */
@Service
@DS(value = "second")
public class HrGwCityServiceImpl implements HrGwCityService {
    @Resource
    private HrGwCityMapper hrGwCityMapper;

    @Override
    public void save(HrGwCity hrGwCity) {
        hrGwCityMapper.insertSelective(hrGwCity);
    }

    @Override
    public void updateById(HrGwCity hrGwCity) {
        hrGwCityMapper.updateByPrimaryKeySelective(hrGwCity);
    }

    @Override
    public HrGwCity queryOneByHrGwCity(HrGwCity hrGwCity) {
        return hrGwCityMapper.selectOne(hrGwCity);
    }

    @Override
    public HrGwCity getById(String id) {
        return hrGwCityMapper.selectByPrimaryKey(id);
    }
}
