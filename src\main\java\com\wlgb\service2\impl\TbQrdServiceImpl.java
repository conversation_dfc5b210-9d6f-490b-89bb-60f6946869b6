package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbQrd;
import com.wlgb.mapper.TbQrdMapper;
import com.wlgb.service2.TbQrdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月14日 19:18
 */
@Service
@DS(value = "second")
@Slf4j
public class TbQrdServiceImpl implements TbQrdService {
    @Resource
    private TbQrdMapper tbQrdMapper;

    @Override
    public void updateById(TbQrd tbQrd) {
        boolean cfTest = false;
        try {
            tbQrdMapper.updateByPrimaryKeySelective(tbQrd);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    tbQrdMapper.updateByPrimaryKeySelective(tbQrd);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，update：参数" + tbQrd.toString());
                    }
                }
            }
        }
    }

    @Override
    public void save(TbQrd tbQrd) {
        boolean cfTest = false;
        try {
            tbQrdMapper.insertSelective(tbQrd);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    tbQrdMapper.insertSelective(tbQrd);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，insert：参数" + tbQrd.toString());
                    }
                }
            }
        }
    }

    @Override
    public TbQrd queryQxydIdAndQsfSc(String qxydId) {
        Example example = new Example(TbQrd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("qxydid", qxydId);
        criteria.andEqualTo("qsfsc", 0);
        return tbQrdMapper.selectOneByExample(example);
    }
}
