package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 审批——别墅分摊
 * @Author: jeecg-boot
 * @Date:   2022-04-16
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_sp_bsft")
public class WlgbJdSpBsft {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**审批编号*/
    private java.lang.String spbh;
	/**别墅名称*/
    private java.lang.String bsmc;
	/**别墅性质*/
    private java.lang.String bsxz;
	/**金额*/
    private java.lang.Double je;
	/**是否删除*/
    private java.lang.Integer sfsc;
	/**类型：1、分摊别墅，2、不分摊别墅*/
    private java.lang.Integer type;
	/**审批类型：0:报销，1：预支，2：费用付款，3：抵预支*/
    private java.lang.Integer statu;
}
