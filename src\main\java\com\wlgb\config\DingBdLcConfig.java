package com.wlgb.config;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkyida_1_0.Client;
import com.aliyun.dingtalkyida_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通过使用钉钉的sdk来调用宜搭的api
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/07/04 17:53
 */
@Slf4j
public class DingBdLcConfig {
    public static Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }

    /*****所有需要调用宜搭的接口都先经过这里，在这里可以重试、分析报错原因等****/
    public static GatewayResult ydbdAndlc(Map<String, Object> map) throws Exception {
        GatewayResult gr = new GatewayResult();
        log.info("*******进入ydbdAndlc方法*******{}", JSONObject.toJSONString(map));
        String token = (String) map.get("ddtoken");
        YdAppkey ydAppkey = (YdAppkey) map.get("ydAppkey");
        String userId = (String) map.get("userId");
        //是哪一类接口：bd:表单  lc：流程
        //是哪种操作：save:新增  batchSaveForm：批量新增 getForm：查询（根据实例id查询数据） search：查询（根据组件内容查询数据）  update：修改 delete:删除
        String type = (String) map.get("type");
        if (!type.isEmpty()) {
            if ("bd_save".equals(type)) {
                String json = (String) map.get("json");
                String formUuId = (String) map.get("formUuId");
                gr = xzBdSl(token, ydAppkey, json, userId, formUuId);
            } else if ("bd_batchSaveForm".equals(type)) {

            } else if ("bd_getForm".equals(type)) {

            } else if ("bd_search".equals(type)) {

            } else if ("bd_update".equals(type)) {
                String json = (String) map.get("json");
                String formInstanceId = (String) map.get("formInstanceId");
                gr = xgBdSl(token, ydAppkey, userId, formInstanceId, json);
                log.info("*******进入ydbdAndlc方法__gr*******{}", JSONObject.toJSONString(gr));
                boolean b = gr.getSuccess();
                if (!b) {
                    String code = gr.getErrorCode();
                    if ("503".equals(code) || "500".equals(code) || "504".equals(code)) {
                        gr = xgBdSl(token, ydAppkey, userId, formInstanceId, json);
                    }
                }
            } else if ("bd_delete".equals(type)) {

            } else if ("lc_save".equals(type)) {

            } else if ("lc_batchSaveForm".equals(type)) {

            } else if ("lc_getForm".equals(type)) {

            } else if ("lc_search".equals(type)) {

            } else if ("lc_delete".equals(type)) {

            } else if ("lc_update".equals(type)) {

            }
        } else {
            gr.setSuccess(false);
            gr.setErrorCode("400");
            gr.setErrorMsg("缺少参数");
        }
        return gr;
    }

    /**
     * 新增单个表单实例
     *
     * @param token    钉钉token
     * @param ydAppkey 宜搭应用配置
     * @param json     新增数据
     * @param userId   发起人
     * @param formUuId 表单id
     */
    public static GatewayResult xzBdSl(String token, YdAppkey ydAppkey, String json, String userId, String formUuId) throws Exception {
        log.info("*******新增表单实例的参数*******{}", json);
        Client client = createClient();
        SaveFormDataHeaders saveFormDataHeaders = new SaveFormDataHeaders();
        saveFormDataHeaders.xAcsDingtalkAccessToken = token;
        SaveFormDataRequest saveFormDataRequest = new SaveFormDataRequest()
                .setSystemToken(ydAppkey.getToken())
                .setFormUuid(formUuId)
                .setUserId(userId)
                .setAppType(ydAppkey.getAppkey())
                .setFormDataJson(json);

        GatewayResult gatewayResult = new GatewayResult();
        SaveFormDataResponse saveFormDataResponse = null;
        try {
            saveFormDataResponse = client.saveFormDataWithOptions(saveFormDataRequest, saveFormDataHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        }
        if (saveFormDataResponse != null) {
            gatewayResult.setSuccess(true);
            if (saveFormDataResponse.getBody() != null) {
                gatewayResult.setResult(saveFormDataResponse.getBody().getResult());
            }
        }
        System.out.println(gatewayResult);

        return gatewayResult;
    }

    /**
     * 批量新增表单实例
     *
     * @param token    钉钉token
     * @param ydAppkey 宜搭应用配置
     * @param json     新增数据
     * @param userId   发起人
     * @param formUuId 表单id
     */
    public static GatewayResult plxzBdSl(String token, YdAppkey ydAppkey, List<String> json, String userId, String formUuId) throws Exception {
        log.info("*******批量新增表单实例的参数*******{}", json);
        Client client = createClient();
        BatchSaveFormDataHeaders batchSaveFormDataHeaders = new BatchSaveFormDataHeaders();
        batchSaveFormDataHeaders.xAcsDingtalkAccessToken = token;
        BatchSaveFormDataRequest batchSaveFormDataRequest = new BatchSaveFormDataRequest()
                .setNoExecuteExpression(true)//是否触发服务注册接口
                .setFormUuid(formUuId)
                .setAppType(ydAppkey.getAppkey())
                .setAsynchronousExecution(false)//是否允许宜搭异步保存数据
                .setSystemToken(ydAppkey.getToken())
                .setKeepRunningAfterException(false)//如果发生异常是否跳过保存下一条
                .setUserId(userId)
                .setFormDataJsonList(json);
        GatewayResult gatewayResult = new GatewayResult();
        BatchSaveFormDataResponse bb = null;
        try {
            bb = client.batchSaveFormDataWithOptions(batchSaveFormDataRequest, batchSaveFormDataHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        }
        if (bb != null) {
            gatewayResult.setSuccess(true);
            if (bb.getBody() != null) {
                //插入成功的话返回成功的条数
                gatewayResult.setResult(bb.getBody().getResult().size() + "");
            }
        }
        return gatewayResult;
    }


    /**
     * 修改表单实例
     *
     * @param token          钉钉token
     * @param ydAppkey       宜搭应用配置
     * @param userId         修改人
     * @param formInstanceId 实例id
     * @param json           修改数据
     */
    public static GatewayResult xgBdSl(String token, YdAppkey ydAppkey, String userId, String formInstanceId, String json) throws Exception {
        log.info("*******修改表单实例的参数*******{}", json);
        Client client = createClient();
        UpdateFormDataHeaders updateFormDataHeaders = new UpdateFormDataHeaders();
        updateFormDataHeaders.xAcsDingtalkAccessToken = token;
        UpdateFormDataRequest updateFormDataRequest = new UpdateFormDataRequest()
                .setAppType(ydAppkey.getAppkey())
                .setSystemToken(ydAppkey.getToken())
                .setUserId(userId)
                .setLanguage("zh_CN")
                .setFormInstanceId(formInstanceId)
                .setUseLatestVersion(true)
                .setUpdateFormDataJson(json);

        GatewayResult gatewayResult = new GatewayResult();
        UpdateFormDataResponse updateFormDataResponse = null;
        try {
            updateFormDataResponse = client.updateFormDataWithOptions(updateFormDataRequest, updateFormDataHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        }
        if (updateFormDataResponse != null) {
            gatewayResult.setSuccess(true);
            if (updateFormDataResponse.getHeaders() != null) {
                gatewayResult.setResult(updateFormDataResponse.getHeaders().toString());
            }
        } else {
            try {
                updateFormDataResponse = client.updateFormDataWithOptions(updateFormDataRequest, updateFormDataHeaders, new RuntimeOptions());
            } catch (TeaException err) {
                gatewayResult.setSuccess(false);
                gatewayResult.setErrorCode(err.getCode());
                gatewayResult.setErrorMsg(err.getMessage());
            } catch (Exception _err) {
                TeaException err = new TeaException(_err.getMessage(), _err);
                gatewayResult.setSuccess(false);
                gatewayResult.setResult(err.getData().toString());
                gatewayResult.setErrorCode(err.getCode());
                gatewayResult.setErrorMsg(err.getMessage());
            }
            if (updateFormDataResponse != null) {
                gatewayResult.setSuccess(true);
                if (updateFormDataResponse.getHeaders() != null) {
                    gatewayResult.setResult(updateFormDataResponse.getHeaders().toString());
                }
            }
        }
        return gatewayResult;
    }

    /**
     * 删除表单实例
     *
     * @param token          钉钉token
     * @param ydAppkey       宜搭应用配置
     * @param userId         删除人
     * @param formInstanceId 实例id
     */
    public static GatewayResult scBdSl(String token, YdAppkey ydAppkey, String userId, String formInstanceId) throws Exception {
        log.info("*******删除表单实例的参数*******{}", formInstanceId);
        Client client = createClient();
        DeleteFormDataHeaders deleteFormDataHeaders = new DeleteFormDataHeaders();
        deleteFormDataHeaders.xAcsDingtalkAccessToken = token;
        DeleteFormDataRequest deleteFormDataRequest = new DeleteFormDataRequest()
                .setAppType(ydAppkey.getAppkey())
                .setSystemToken(ydAppkey.getToken())
                .setUserId(userId)
                .setLanguage("zh_CN")
                .setFormInstanceId(formInstanceId);

        GatewayResult gatewayResult = new GatewayResult();
        DeleteFormDataResponse deleteFormDataResponse = null;
        try {
            deleteFormDataResponse = client.deleteFormDataWithOptions(deleteFormDataRequest, deleteFormDataHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        }
        if (deleteFormDataResponse != null) {
            gatewayResult.setSuccess(true);
            if (deleteFormDataResponse.getHeaders() != null) {
                gatewayResult.setResult(deleteFormDataResponse.getHeaders().toString());
            }
        }
        return gatewayResult;
    }

    /**
     * 批量删除表单实例
     *
     * @param token    钉钉token
     * @param ydAppkey 宜搭应用配置
     * @param userId   删除人
     * @param formUuId 表单id
     */
    public static GatewayResult plscBdSl(String token, YdAppkey ydAppkey, String userId, String formUuId,List<String>  list) throws Exception {
        log.info("*******删除表单实例的参数*******{}", formUuId);
        Client client = createClient();
        BatchRemovalByFormInstanceIdListHeaders batchRemovalByFormInstanceIdListHeaders = new BatchRemovalByFormInstanceIdListHeaders();
        batchRemovalByFormInstanceIdListHeaders.xAcsDingtalkAccessToken = token;
        BatchRemovalByFormInstanceIdListRequest batchRemovalByFormInstanceIdListRequest = new BatchRemovalByFormInstanceIdListRequest()
                .setFormUuid(formUuId)
                .setAppType(ydAppkey.getAppkey())
                .setAsynchronousExecution(true)
                .setSystemToken(ydAppkey.getToken())
                .setFormInstanceIdList(list)
                .setUserId(userId)
                .setExecuteExpression(false);
        GatewayResult gatewayResult = new GatewayResult();
        BatchRemovalByFormInstanceIdListResponse byFormInstanceIdListResponse = null;
        try {
            byFormInstanceIdListResponse = client.batchRemovalByFormInstanceIdListWithOptions(batchRemovalByFormInstanceIdListRequest, batchRemovalByFormInstanceIdListHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        }
        if (byFormInstanceIdListResponse != null) {
            gatewayResult.setSuccess(true);
            if (byFormInstanceIdListResponse.getHeaders() != null) {
                gatewayResult.setResult(byFormInstanceIdListResponse.getHeaders().toString());
            }
        }
        return gatewayResult;
    }


    /**
     * 发起流程实例
     *
     * @param token    钉钉token
     * @param ydAppkey 宜搭应用配置
     * @param userId   发起人id
     * @param formUuId 表单id
     * @param code     流程code
     * @param json     发起数据
     */
    public static GatewayResult fqXzLcSl(String token, YdAppkey ydAppkey, String userId, String formUuId, String code, String json) throws Exception {
        log.info("*******新增流程表单实例的参数*******{}", json);
        Client client = createClient();
        StartInstanceHeaders startInstanceHeaders = new StartInstanceHeaders();
        startInstanceHeaders.xAcsDingtalkAccessToken = token;
        StartInstanceRequest startInstanceRequest = new StartInstanceRequest()
                .setAppType(ydAppkey.getAppkey())
                .setSystemToken(ydAppkey.getToken())
                .setUserId(userId)
                .setLanguage("zh_CN")
                .setFormUuid(formUuId)
                .setFormDataJson(json)
                .setProcessCode(code);

        GatewayResult gatewayResult = new GatewayResult();
        StartInstanceResponse startInstanceResponse = null;
        try {
            startInstanceResponse = client.startInstanceWithOptions(startInstanceRequest, startInstanceHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        }
        if (startInstanceResponse != null) {
            gatewayResult.setSuccess(true);
            if (startInstanceResponse.getBody() != null) {
                gatewayResult.setResult(startInstanceResponse.getBody().getResult());
            }
        }
        return gatewayResult;
    }

    /**
     * 查询流程实例
     *
     * @param token    钉钉token
     * @param ydAppkey 宜搭应用配置
     * @param formUuId 表单id
     * @param fqrid    发起人id
     * @param fqkstime 创建开始时间
     * @param fqjstime 创建结束时间
     * @param json     筛选条件
     * @return
     * @throws Exception
     */
    public static GatewayResult CxLcSl(String token, YdAppkey ydAppkey, String formUuId, String fqrid, String fqkstime, String fqjstime, String json) throws Exception {
        log.info("*******查询流程表单实例的参数*******{}", json);
        Client client = createClient();
        GetInstancesHeaders getInstancesHeaders = new GetInstancesHeaders();
        getInstancesHeaders.xAcsDingtalkAccessToken = token;
        GetInstancesRequest getInstancesRequest = new GetInstancesRequest()
                .setPageNumber(1)//分页页码
                .setPageSize(100)//分页大小
                .setAppType(ydAppkey.getAppkey())
                .setSystemToken(ydAppkey.getToken())
                .setUserId(fqrid)
                .setLanguage("zh_CN")
                .setFormUuid(formUuId)
                .setSearchFieldJson(json)//查询过滤条件，支持2种模式的过滤规则。模式1：根据组件值模糊匹配，示例：{"xx":"xx","xx":"K"} 模式2: 采用数据管理的查询过滤条件，匹配功能更强大，示例：[{"key":"x","value":"步凡","type":"TEXT","operator":"like","componentName":"xx”}]
                .setOriginatorId(fqrid)
                .setCreateFromTimeGMT(fqkstime)//流程创建开始时间
                .setCreateToTimeGMT(fqjstime)//流程创建结束时间
                .setInstanceStatus("RUNNING");

        GatewayResult gatewayResult = new GatewayResult();
        GetInstancesResponse getInstances = new GetInstancesResponse();
        try {
            getInstances = client.getInstancesWithOptions(getInstancesRequest, getInstancesHeaders, new com.aliyun.teautil.models.RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        }

        if (getInstances != null) {
            gatewayResult.setSuccess(true);
            if (getInstances.getBody() != null) {
                //返回符合筛选条件的数量
                gatewayResult.setResult(getInstances.getBody().getData().size() + "");
            }
        }
        return gatewayResult;
    }


    /**
     * 删除流程实例
     *
     * @param token             钉钉token
     * @param ydAppkey          宜搭应用配置
     * @param userId            用户id
     * @param processInstanceId 实例id
     */
    public static GatewayResult scLcSl(String token, YdAppkey ydAppkey, String userId, String processInstanceId) throws Exception {
        log.info("*******删除流程表单实例的参数*******{}", processInstanceId);
        Client client = createClient();
        DeleteInstanceHeaders deleteInstanceHeaders = new DeleteInstanceHeaders();
        deleteInstanceHeaders.xAcsDingtalkAccessToken = token;
        DeleteInstanceRequest deleteInstanceRequest = new DeleteInstanceRequest()
                .setAppType(ydAppkey.getAppkey())
                .setSystemToken(ydAppkey.getToken())
                .setUserId(userId)
                .setLanguage("zh_CN")
                .setProcessInstanceId(processInstanceId);

        DeleteInstanceResponse deleteInstanceResponse = null;
        GatewayResult gatewayResult = new GatewayResult();
        try {
            deleteInstanceResponse = client.deleteInstanceWithOptions(deleteInstanceRequest, deleteInstanceHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        }
        if (deleteInstanceResponse != null) {
            gatewayResult.setSuccess(true);
            if (deleteInstanceResponse.getHeaders() != null) {
                gatewayResult.setResult(deleteInstanceResponse.getHeaders().toString());
            }
        }
        return gatewayResult;
    }


    /**
     * 更新流程实例
     *
     * @param token    钉钉token
     * @param ydAppkey 宜搭配置
     * @
     */
    public static GatewayResult gxLcSl(String token, YdAppkey ydAppkey, String userId, String json, String processInstanceId) throws Exception {
        log.info("*******修改流程表单实例的参数*******{}", json);
        Client client = createClient();
        UpdateInstanceHeaders updateInstanceHeaders = new UpdateInstanceHeaders();
        updateInstanceHeaders.xAcsDingtalkAccessToken = token;
        UpdateInstanceRequest updateInstanceRequest = new UpdateInstanceRequest()
                .setAppType(ydAppkey.getAppkey())
                .setSystemToken(ydAppkey.getToken())
                .setUserId(userId)
                .setLanguage("zh_CN")
                .setProcessInstanceId(processInstanceId)
                .setUpdateFormDataJson(json);
        GatewayResult gatewayResult = new GatewayResult();

        UpdateInstanceResponse updateInstanceResponse = null;
        try {
            updateInstanceResponse = client.updateInstanceWithOptions(updateInstanceRequest, updateInstanceHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        }

        if (updateInstanceResponse != null) {
            gatewayResult.setSuccess(true);
            if (updateInstanceResponse.getHeaders() != null) {
                gatewayResult.setResult(updateInstanceResponse.getHeaders().toString());
            }
        }

        return gatewayResult;
    }

    /**
     * 获取表单修改记录
     *
     * @param token          钉钉token
     * @param ydAppkey       宜搭应用配置
     * @param formInstanceId 实例id
     * @param userId         发起人
     * @param formUuId       表单id
     */
    public static Map<String, ?> getBdxgjl(String token, YdAppkey ydAppkey, String userId, String formUuId, String formInstanceId) throws Exception {
        Client client = createClient();
        Map<String, ?> map = new HashMap<>();
        ListOperationLogsResponse listOperationLogsResponse = null;
        ListOperationLogsHeaders listOperationLogsHeaders = new ListOperationLogsHeaders();
        listOperationLogsHeaders.xAcsDingtalkAccessToken = token;
        ListOperationLogsRequest listOperationLogsRequest = new ListOperationLogsRequest()
                .setFormUuid(formUuId)
                .setAppType(ydAppkey.getAppkey())
                .setSystemToken(ydAppkey.getToken())
                .setFormInstanceIdList(java.util.Arrays.asList(formInstanceId))
                .setUserId(userId);
        try {
            listOperationLogsResponse = client.listOperationLogsWithOptions(listOperationLogsRequest, listOperationLogsHeaders, new RuntimeOptions());
            map = listOperationLogsResponse.getBody().getOperationLogMap();
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }
        return map;
    }

}
