package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 接单表——待跟单1
 * @Author: jeecg-boot
 * @Date:   2020-10-25
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jdb_one")
public class WlgbJdbOne {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
    /**是否添加客户微信(是:微信截图,否无法填写下一项)*/
    private java.lang.String sftjkhwx;
    /**微信截图(是)*/
    private java.lang.String wxtgjt;
    /** 是否规范备注*/
    private java.lang.String sfgfbzwx;
    /** 是否确认订单信息*/
    private java.lang.String sfqrddxx;
    /** 是否确认订单信息*/
    private java.lang.String sfsmzysx;
    /** 是否推销增值*/
    private java.lang.String sftxzz;
    /**订单是否需要修改*/
    private java.lang.String ddsfxyxg;
    /**附上事宜与截图(是)*/
    private java.lang.String fssyyjt;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date time;
	/**订单状态(1:待跟单2:入场准备3:接待入场4:现场服务5:退场6:对账7:反馈8:完成)*/
    private java.lang.Integer status;
	/**订单id*/
    private java.lang.String xid;
	/**接单用户id*/
    private java.lang.String userId;
	/**倒计时*/
    private java.util.Date timeOut;

    /**微信未通过截图*/
    private java.lang.String wxwtgjt;

    /**微信未通过原因*/
    private java.lang.String wxwtgyy;

    /**订单修改原因*/
    private java.lang.String ddxgyy;

    /**微信拉群截图*/
    private java.lang.String lqjt;
}
