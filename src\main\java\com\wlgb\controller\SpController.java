package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptException;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptor;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.jobs.Jobs;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.CrmBmxqService;
import com.wlgb.service2.CrmUserService;
import com.wlgb.service2.WeiLianService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.wlgb.config.oss.oConvertUtils.isEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 19:47
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/sp")
public class SpController {
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private CrmBmxqService crmBmxqService;
    @Autowired
    private CrmUserService crmUserService;

    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;

    /**
     * 费用科目下拉
     */
    @RequestMapping(value = "queryFyKmSelect")
    public Rest queryFyKmSelect() {
        List<Select> list = weiLianDdXcxService.queryFyKmSelect();

        return Rest.success(list);
    }

    /**
     * OA审批回调
     * <p>
     * 配置了钉钉的审批回调，在这里面设置审批类别的分类处理
     */
    @RequestMapping(value = "oaSpCl")
    public Map<String, String> oaSpCl(@RequestParam(value = "signature", required = false) String signature, @RequestParam(value = "timestamp", required = false) Long timestamp, @RequestParam(value = "nonce", required = false) String nonce, @RequestBody(required = false) JSONObject jsonObject) throws ApiException {
        DingTalkEncryptor dingTalkEncryptor = null;
        String CALLBACK_RESPONSE_SUCCESS = "success";
        try {
            //解密回调信息
            dingTalkEncryptor = new DingTalkEncryptor("lYVNgn2vaoDNx", "FoebXb7kcauRxYutNSrL8LGMRM2JPRukxNvAYlBklFb", "dingun51xbrmugkcysrr");
        } catch (DingTalkEncryptException e) {
            e.printStackTrace();
            CALLBACK_RESPONSE_SUCCESS = "error";
        }
        String encrypt = jsonObject.getString("encrypt");
        String plainText = null;
        try {
            //解密后的回调内容
            plainText = dingTalkEncryptor.getDecryptMsg(signature, timestamp.toString(), nonce, encrypt);
        } catch (DingTalkEncryptException e) {
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("plainText", plainText);
        paramMap.put("uuid", IdConfig.uuId());
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/sp/oaSpCl2", paramMap);
        } catch (IOException e) {
            log.info("*******审批回调********");
        }

        Map<String, String> encryptedMap = null;
        try {
            encryptedMap = dingTalkEncryptor.getEncryptedMap(CALLBACK_RESPONSE_SUCCESS, timestamp, nonce);
        } catch (DingTalkEncryptException e) {
            e.printStackTrace();
        }
        return encryptedMap;
    }

    @RequestMapping(value = "oaSpCl2")
    public void OaSpCl2(HttpServletRequest request) {
        String plainText = request.getParameter("plainText");
        JSONObject callBackContent = JSONObject.parseObject(plainText);
        String eventType = callBackContent.getString("EventType");
        if ("bpms_task_change".equals(eventType)) {
            String processCode = callBackContent.getString("processCode");
            String result = callBackContent.getString("result");
            if ("agree".equals(result)) {
                Integer count = weiLianDdXcxService.querySpLbCount(processCode);
                if (count > 0) {
                    //调用阿里云函数计算的异步函数
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put("data", callBackContent.toJSONString());
                    paramMap.put("uuid", IdConfig.uuId());
                    System.out.println("uuid====================" + paramMap.get("uuid"));
                    //在这里进行异步调用函数计算里面的函数即可
                    try {
                        HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/spTask/oaSpClTask", paramMap);
                    } catch (IOException e) {
                        log.info("*******审批回调********");
                    }
                }
            }
        } else if (("user_add_org".equals(eventType)) || ("user_modify_org".equals(eventType)) || ("user_leave_org".equals(eventType))) {
            //人员的变动 通讯录人员的新增、修改、删除
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("data", callBackContent.toJSONString());
            paramMap.put("uuid", IdConfig.uuId());
            System.out.println("uuid====================" + paramMap.get("uuid"));
            //在这里进行异步调用函数计算里面的函数即可
            try {
                HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/sp/crmuserAndbmxqTask", paramMap);
            } catch (IOException e) {
                log.info("*******人员变动回调********");
            }
        } else if (("org_dept_create".equals(eventType)) || ("org_dept_modify".equals(eventType)) || ("org_dept_remove".equals(eventType))) {
            //部门的变动 通讯录部门的新增、修改、删除
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("data", callBackContent.toJSONString());
            paramMap.put("uuid", IdConfig.uuId());
            System.out.println("uuid====================" + paramMap.get("uuid"));
            //在这里进行异步调用函数计算里面的函数即可
            try {
                HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/sp/getbmxqTask", paramMap);
            } catch (IOException e) {
                log.info("*******人员变动回调********");
            }
        } else if (("attendance_approve_status_change".equals(eventType))) {
            //请假、加班、出差、外出状态变更事件
            log.info("*******打印请假、加班、出差、外出状态变更事件的返回值********{}", callBackContent.toJSONString());
        }
    }

    /**
     * 通讯录回调事件——新增员工、员工被修改、员工离开组织架构
     *
     * @param request
     * @throws ApiException
     */
    @RequestMapping(value = "crmuserAndbmxqTask")
    public void crmuserAndbmxqTask(HttpServletRequest request) throws ApiException {
        Dingkey dingkey1 = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String data = request.getParameter("data");
        JSONObject callBackContent = JSONObject.parseObject(data);
        String eventType = callBackContent.getString("EventType");
        JSONArray userarr = callBackContent.getJSONArray("UserId");
        //添加用户和修改用户信息
        if (("user_add_org".equals(eventType)) || ("user_modify_org".equals(eventType))) {
            for (Object o : userarr) {
                //获取员工的详情（同时给三个表实体赋值）
                Map<String, Object> map = getYgxq(o.toString());
                Demployee dd = (Demployee) map.get("dd");
                log.info("***员工信息修改的回调********{}", dd);
                CrmUser cm = new CrmUser();
                cm.setName(dd.getName());
                cm.setUserid(o.toString());
                cm.setMobile(dd.getMobile());
                cm.setAvatar(dd.getAvatar());
                try {
                    //不一定是最新的用户信息，但是基本不会有影响。
                    List<CrmUser> cmlist = crmUserService.select(cm);
                    if (!cmlist.isEmpty()) {
                        crmUserService.update(cm);
                    } else {
                        crmUserService.save(cm);
                    }
                    CrmBmxq bmxq = new CrmBmxq();
                    bmxq.setUserid(o.toString());
                    bmxq.setName(dd.getName());
                    List<CrmBmxq> bmxqList = crmBmxqService.select(bmxq);
                    //获取所有的上级部门
                    bmxq = getBmxq(o.toString(), dd.getName());
                    List<List<Long>> lllist = getBmxq2(o.toString());
                    if (!lllist.isEmpty()) {
                        for (int i = 0; i < lllist.size(); i++) {
                            String deptIdsStr = listToString(lllist.get(i));
                            switch (i) {
                                case 0:
                                    bmxq.setBm1(deptIdsStr);
                                    bmxq.setBm1zg(getBmxq3(lllist.get(i)));
                                    break;
                                case 1:
                                    bmxq.setBm2(deptIdsStr);
                                    bmxq.setBm2zg(getBmxq3(lllist.get(i)));
                                    break;
                                case 2:
                                    bmxq.setBm3(deptIdsStr);
                                    bmxq.setBm3zg(getBmxq3(lllist.get(i)));
                                    break;
                                case 3:
                                    bmxq.setBm4(deptIdsStr);
                                    bmxq.setBm4zg(getBmxq3(lllist.get(i)));
                                    break;
                                case 4:
                                    bmxq.setBm5(deptIdsStr);
                                    bmxq.setBm5zg(getBmxq3(lllist.get(i)));
                                    break;
                                case 5:
                                    bmxq.setBm6(deptIdsStr);
                                    bmxq.setBm6zg(getBmxq3(lllist.get(i)));
                                    break;
                                case 6:
                                    bmxq.setBm7(deptIdsStr);
                                    bmxq.setBm7zg(getBmxq3(lllist.get(i)));
                                    break;
                                case 7:
                                    bmxq.setBm8(deptIdsStr);
                                    bmxq.setBm8zg(getBmxq3(lllist.get(i)));
                                    break;
                                case 8:
                                    bmxq.setBm9(deptIdsStr);
                                    bmxq.setBm9zg(getBmxq3(lllist.get(i)));
                                    break;
                                case 9:
                                    bmxq.setBm10(deptIdsStr);
                                    bmxq.setBm10zg(getBmxq3(lllist.get(i)));
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                    if (!bmxqList.isEmpty()) {
                        bmxq.setBid(bmxqList.get(0).getBid());
                        log.info("***员工信息修改的回调_部门********{}", bmxq);
                        crmBmxqService.update(bmxq);
                    } else {
                        log.info("***员工信息修改的回调_部门********{}", bmxq);
                        crmBmxqService.save(bmxq);
                    }
                } catch (Exception e) {
                    try {
                        DingQunSend.send("人员详情添加失败：" + e, dingkey1, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException apiException) {
                        apiException.printStackTrace();
                    }
                }
            }
            //调用阿里云函数计算的异步函数
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("data", callBackContent.toJSONString());
            paramMap.put("uuid", IdConfig.uuId());
            System.out.println("uuid====================" + paramMap.get("uuid"));
            //在这里进行异步调用函数计算里面的函数即可
            try {
                HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/sp/getygxqTask", paramMap);
            } catch (IOException e) {
                log.info("*******人员变动回调********");
            }
        } else if ("user_leave_org".equals(eventType)) {
            //删除用户
            for (Object o : userarr) {
                //获取员工的详情（同时给三个表实体赋值）
                Map<String, Object> map = getYgxq(o.toString());
                Demployee dd = (Demployee) map.get("dd");
                log.info("***删除员工信息*******{}", dd);
                weiLianService.delDingdingEmployee(dd.getUserid());
            }
        }
    }

    /**
     * 根据回调内容来更新部门详情，新增会同时在数据库、金蝶新增部门，
     *
     * @param request
     * @throws ApiException
     */
    @RequestMapping(value = "getbmxqTask")
    public void getbmxqTask(HttpServletRequest request) throws ApiException {
        String data = request.getParameter("data");
        JSONObject callBackContent = JSONObject.parseObject(data);
        log.info("********callBackContent*************{}", callBackContent);
        String eventType = callBackContent.getString("EventType");
        String corp_id = callBackContent.getString("CorpId");
        JSONArray deptarr = callBackContent.getJSONArray("DeptId");
        for (Object o : deptarr) {
            if ("org_dept_remove".equals(eventType)) {
                //删除部门
                weiLianService.delDingdingDeparTment(o.toString());
            } else {
                //获取部门详情，包括部门主管
                Department dt = getBmxq(Long.parseLong(o.toString()), corp_id);
                DingdingDepartment ddt = weiLianService.queryDingdingDeparTment(o.toString());
                //部门新建
                if (ddt == null) {
                    weiLianService.saveDingdingDeparTment(dt);
                    //在金蝶新增部门
                    Boolean bmCl = KingDeeConfig.bmCl(dt.getDingdingid(), dt.getDepartname());
                    if (bmCl) {
                        System.out.println("金蝶部门新增成功");
                    } else {
                        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
                        DingQunSend.send("金蝶部门新增失败, 部门名称:" + dt.getDepartname() + ", 部门id:" + dt.getDingdingid(), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    }
                } else {
                    dt.setId(ddt.getId());
                    weiLianService.updateDingdingDeparTment(dt);
                }
            }
        }
    }

    /**
     * 根据回调内容来更新员工详情
     *
     * @param request
     * @throws ApiException
     */
    @RequestMapping(value = "getygxqTask")
    public void getygxqTask(HttpServletRequest request) throws ApiException {
        String data = request.getParameter("data");
        JSONObject callBackContent = JSONObject.parseObject(data);
        JSONArray userarr = callBackContent.getJSONArray("UserId");
        for (Object o : userarr) {
            Map<String, Object> map = getYgxq(o.toString());
            Demployee dd = (Demployee) map.get("dd");
            Tbperson tp = (Tbperson) map.get("tp");
            SysUser su = (SysUser) map.get("su");
            DingdingEmployee ddd = weiLianService.queryDingDingById(o.toString());
            Tbperson tpp = weiLianService.queryTbpersonById(o.toString());
            SysUser suu = weiLianService.querySysUserById(o.toString());
            log.info("***员工信息修改的回调********{}", dd);
            if (ddd == null) {
                weiLianService.saveDingdingEmployee(dd);
            } else {
                dd.setDingid(ddd.getDingid());
                dd.setDepartid(ddd.getDepartid());
                dd.setName(ddd.getName());
                dd.setMobile(ddd.getMobile());
                weiLianService.updateDingdingEmployee(dd);
            }
            if (tpp == null) {
                weiLianService.saveTbperson(tp);
            } else {
                weiLianService.updateTbperson(tp);
            }
            if (suu == null) {
                weiLianService.saveSysUser(su);
            } else {
                weiLianService.updateSysUser(su);
            }
        }
    }

    /**
     * 一线、加盟采购报修同步数据
     */
    @RequestMapping(value = "cgSqTbJd")
    public Result cgSqTbJd(HttpServletRequest request) {
        String datas = request.getParameter("datas");

        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null) {
            return Result.error("数据空的");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/spTask/cgSqTbJdTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 采购审批通过录入资产
     */
    @RequestMapping("cglrcp")
    public Result cglrcp(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("数据不能为空！");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/spTask/cglrcpTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();

    }

    /**
     * 乐诚预支流程审批数据
     */
    @RequestMapping(value = "tjLcYzLcSpData")
    public Result tjLcYzLcSpData(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return Result.error("数据空的！");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/spTask/tjLcYzLcSpDataTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 乐诚流程采购审批数据
     */
    @RequestMapping(value = "tjLcCgLcSpData")
    public Result tjLcCgLcSpData(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return Result.error("数据空的！");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/spTask/tjLcCgLcSpDataTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * "提交退定金", notes = "从审批最后节点提交，将数据传入金蝶")
     */
    @RequestMapping(value = "tjTdj")
    public Result tjTdj(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null || jsonObject.size() == 0) {
            return Result.error("数据空的！");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("formInstId", formInstId);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/spTask/tjTdjTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    @RequestMapping(value = "tjTdj2")
    public Result tjTdj2(@RequestBody Map<String, Object> requestBody) {
        String datas = (String) requestBody.get("datas");
        String formInstId = (String) requestBody.get("formInstId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null || jsonObject.size() == 0) {
            return Result.error("数据空的！");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("formInstId", formInstId);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync("http://127.0.0.1:8082/ysdjtb/wlgb/spTask/tjTdjTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 审批回调-获取所有审批事件以及考勤打卡
     */
    @RequestMapping(value = "tjSpAllTest")
    public Map<String, String> tjBxSqAllTest(@RequestParam(value = "signature", required = false) String signature, @RequestParam(value = "timestamp", required = false) Long timestamp, @RequestParam(value = "nonce", required = false) String nonce, @RequestBody(required = false) JSONObject jsonObject) {
        DingTalkEncryptor dingTalkEncryptor = null;
        String CALLBACK_RESPONSE_SUCCESS = "success";
        try {
            dingTalkEncryptor = new DingTalkEncryptor("Uny2KXF", "mWT7Tftgs734yPuQHdloS229pVOyqjgctNfjL1xrmtI", "dingjrnvifk8bmoyzaai");
        } catch (DingTalkEncryptException e) {
            e.printStackTrace();
            CALLBACK_RESPONSE_SUCCESS = "error";
        }
        String encrypt = jsonObject.getString("encrypt");
        String plainText = null;
        try {
            plainText = dingTalkEncryptor.getDecryptMsg(signature, timestamp.toString(), nonce, encrypt);
        } catch (DingTalkEncryptException e) {
            e.printStackTrace();
            CALLBACK_RESPONSE_SUCCESS = "error";
        }
        JSONObject callBackContent = JSONObject.parseObject(plainText);
        String eventType = callBackContent.getString("EventType");
        //考勤记录新增
        if ("attendance_check_record".equals(eventType)) {
            //调用阿里云函数计算的异步函数
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("datas", JSONObject.toJSONString(callBackContent));
            paramMap.put("uuid", IdConfig.uuId());
            System.out.println("uuid====================" + paramMap.get("uuid"));
            //在这里进行异步调用函数计算里面的函数即可
            try {
                HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/spTask/tjSpAllTestKqJl", paramMap);
            } catch (IOException e) {
                e.printStackTrace();
            }

        } else if ("attendance_check_record".equals(eventType)) {
            //通讯录员工信息变更
        }

        //审批类型
        String type = callBackContent.getString("type");
        String result = callBackContent.getString("result");
        if ("agree".equals(result) || "start".equals(type)) {
            //调用阿里云函数计算的异步函数
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("datas", JSONObject.toJSONString(callBackContent));
            paramMap.put("uuid", IdConfig.uuId());
            System.out.println("uuid====================" + paramMap.get("uuid"));
            //在这里进行异步调用函数计算里面的函数即可
            try {
                HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/spTask/tjSpAllTestXzBd", paramMap);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        Map<String, String> encryptedMap = null;
        try {
            encryptedMap = dingTalkEncryptor.getEncryptedMap(CALLBACK_RESPONSE_SUCCESS, timestamp, nonce);
        } catch (DingTalkEncryptException e) {
            e.printStackTrace();
        }

        return encryptedMap;
    }

    /**
     * 总公司token
     *
     * @return
     * @throws ApiException
     */
    public String token() throws ApiException {
        DefaultDingTalkClient client1 = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest request1 = new OapiGettokenRequest();
        request1.setAppkey("ding291dcdb4d2ea030935c2f4657eb6378f");
        request1.setAppsecret("rwLkbETYd_YmO80p6thV5uRCXTspi9oaR9CXXKUj8rDmh0SVIdSpLPCnFyEiZN6A");
        request1.setHttpMethod("GET");
        OapiGettokenResponse response1 = client1.execute(request1);
        String token = response1.getAccessToken();

        return token;
    }

    /**
     * 获取指定用户的所有父部门列表
     */
    public CrmBmxq getBmxq(String userid, String name) throws ApiException {
        String token = token();
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/department/list_parent_depts");
        OapiDepartmentListParentDeptsRequest request = new OapiDepartmentListParentDeptsRequest();
        request.setUserId(userid);
        request.setHttpMethod("GET");
        OapiDepartmentListParentDeptsResponse response = client.execute(request, token);
        log.info("********返回指定用户所有的父部门id*************{}", response.getDepartment());
        CrmBmxq bmxq = new CrmBmxq();
        DingdingDepartment dt = new DingdingDepartment();
        try {
            if (response.getDepartment() != null) {
                String bumen = response.getDepartment().replace("[", "");//去除[
                bumen = bumen.replace("]", "");//去除 ]
                bumen = bumen.replace(" ", "");//去除空格
                String[] bmid = bumen.trim().split(",");//将id分别存入String数组
                List<String> bmidl = Arrays.asList(bmid);
                HashSet<String> h = new HashSet<>(bmidl);
                List<String> bmidlist = new ArrayList<>();
                bmidlist.addAll(h);
                bmxq.setUserid(userid);
                bmxq.setName(name);
                log.info("********返回指定用户所有的父部门id111111111*************{}", bmidlist);
                if (!bmidlist.isEmpty()) {
                    bmxq.setBmid(bmidlist.get(0));
                }
                if (bmidlist.size() > 1) {
                    bmxq.setBmid2(bmidlist.get(1));
                }
                if (bmidlist.size() > 2) {
                    bmxq.setBmid3(bmidlist.get(2));
                }
                if (bmidlist.size() > 3) {
                    bmxq.setBmid4(bmidlist.get(3));
                }
                if (bmidlist.size() > 4) {
                    bmxq.setBmid5(bmidlist.get(4));
                }
                if (bmidlist.size() > 5) {
                    bmxq.setBmid6(bmidlist.get(5));
                }
                if (bmidlist.size() > 6) {
                    bmxq.setBmid7(bmidlist.get(6));
                }
                if (bmidlist.size() > 7) {
                    bmxq.setBmid8(bmidlist.get(7));
                }
                if (bmidlist.size() > 8) {
                    bmxq.setBmid9(bmidlist.get(8));
                }
                if (bmidlist.size() > 9) {
                    bmxq.setBmid10(bmidlist.get(9));
                }
                if (bmidlist.size() > 10) {
                    bmxq.setBmid11(bmidlist.get(10));
                }
                if (bmidlist.size() > 11) {
                    bmxq.setBmid12(bmidlist.get(11));
                }
                if (bmidlist.size() > 12) {
                    bmxq.setBmid13(bmidlist.get(12));
                }
                if (bmidlist.size() > 13) {
                    bmxq.setBmid14(bmidlist.get(13));
                }
                if (bmidlist.size() > 14) {
                    bmxq.setBmid15(bmidlist.get(14));
                }
                return bmxq;
            }
        } catch (Exception e) {
            e.printStackTrace();
            Dingkey dingkey1 = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            DingQunSend.send("部门详情查询失败：" + e, dingkey1, "chate5bd183f6a0ebdd18e057550c487e5cc");
        }
        return bmxq;
    }

    /**
     * 获取部门详情，会返回部门主管
     */
    public Department getBmxq(long deptId, String corp_id) throws ApiException {
        Department dt = new Department();
        String token = token();
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/get");
        OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
        req.setDeptId(deptId);
        req.setLanguage("zh_CN");
        OapiV2DepartmentGetResponse rsp = client.execute(req, token);
        log.info("********获取部门详情*************{}", rsp.getBody());
        JSONObject bodyjson = JSONObject.parseObject(rsp.getBody());
        String errmsg = bodyjson.getString("errmsg");
        if ("ok".equals(errmsg)) {
            JSONObject resultObject = JSONObject.parseObject(bodyjson.getString("result"));
            //部门ID
            long did = resultObject.getLongValue("dept_id");
            //部门主管id数组
            // 获取部门主管id数组
            List<String> deptManagerUseridList = new ArrayList<>();
            if (resultObject.containsKey("dept_manager_userid_list")) {
                deptManagerUseridList = resultObject.getJSONArray("dept_manager_userid_list").toJavaList(String.class);
            } else {
                deptManagerUseridList = Collections.emptyList(); // 默认空列表
            }
            if (did == deptId) {
                dt.setId(IdConfig.uuId());
                dt.setDingdingid(did + "");
                dt.setDepartname(resultObject.getString("name"));
                dt.setParentid(resultObject.getLongValue("parent_id") + "");
                dt.setCorpid(corp_id);
                // 如果有部门主管就直接更新主管id，可能是多个主管
                if (!isEmpty(deptManagerUseridList)) {
                    // 将数组转换为逗号分隔的字符串
                    String deptManagerUseridStr = String.join(",", deptManagerUseridList);
                    // 你可以在这里进一步处理 deptManagerUseridStr
                    log.info("部门主管用户ID列表: {}", deptManagerUseridStr);
                    dt.setZgid(deptManagerUseridStr);
                }
            }
        }
        return dt;
    }

    /**
     * 获取所有父部门，返回一级主管数组、二级主管数组、三级主管数组、、、、、
     *
     * @param userid
     * @return
     * @throws ApiException
     */
    public List<List<Long>> getBmxq2(String userid) throws ApiException {
        String token = token();
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listparentbyuser");
        OapiV2DepartmentListparentbyuserRequest req = new OapiV2DepartmentListparentbyuserRequest();
        req.setUserid(userid);
        OapiV2DepartmentListparentbyuserResponse rsp = client.execute(req, token);
        System.out.println(rsp.getBody());
        log.info("********返回指定用户所有的父部门id*************{}", rsp.getBody());

        List<List<Long>> parentDeptIdsByLevel = new ArrayList<>();
        // 获取result对象
        OapiV2DepartmentListparentbyuserResponse.DeptListParentByUserResponse od = rsp.getResult();
        // 获取parent_list数组
        List<OapiV2DepartmentListparentbyuserResponse.DeptParentResponse> pare = od.getParentList();
        // 遍历parent_list数组
        for (int i = 0; i < pare.size(); i++) {
            OapiV2DepartmentListparentbyuserResponse.DeptParentResponse dp = pare.get(i);
            List<Long> parentDeptIdList = dp.getParentDeptIdList();
            // 确保parentDeptIdsByLevel有足够的层级
            while (parentDeptIdsByLevel.size() < parentDeptIdList.size()) {
                parentDeptIdsByLevel.add(new ArrayList<>());
            }
            // 将parent_dept_id_list中的部门ID按层级分类
            for (int j = 0; j < parentDeptIdList.size(); j++) {
                Long deptId = parentDeptIdList.get(j);
                if (!parentDeptIdsByLevel.get(j).contains(deptId)) { // 检查是否已存在
                    parentDeptIdsByLevel.get(j).add(deptId); // 不存在则添加
                }
            }
        }
        return parentDeptIdsByLevel;
    }

    /**
     * 获取每一个部门的所有主管id
     *
     * @param listL
     * @return
     * @throws ApiException
     */
    public String getBmxq3(List<Long> listL) throws ApiException {
        Set<String> zgidsSet = new HashSet<>();
        for (int i = 0; i < listL.size(); i++) {
            Long l = listL.get(i);
            DingdingDepartment dd = weiLianService.queryDingdingDeparTment(l.toString());
            if (dd != null && !isEmpty(dd.getZgid())) {
                zgidsSet.add(dd.getZgid());
            }
        }
        return String.join(",", zgidsSet);
    }

    /**
     * 获取员工详情
     */
    public Map<String, Object> getYgxq(String userid) throws ApiException {
        Map<String, Object> hm = new HashMap<>();
        Demployee dd = new Demployee();
        Tbperson tp = new Tbperson();
        SysUser su = new SysUser();
        String token = token();
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userid);
        req.setLanguage("zh_CN");
        OapiV2UserGetResponse rsp = client.execute(req, token);
        System.out.println(rsp.getBody());
        JSONObject bodyjson = JSONObject.parseObject(rsp.getBody());
        String errmsg = bodyjson.getString("errmsg");
        if ("ok".equals(errmsg)) {
            JSONObject resultObject = JSONObject.parseObject(bodyjson.getString("result"));
            String uid = resultObject.getString("userid");
            if (uid.equals(userid)) {
                dd.setUserid(userid);
                dd.setName(resultObject.getString("name"));
                String dept_id_list = resultObject.getString("dept_id_list").replace("[", "").replace("]", "");
                String bmid = dept_id_list.replace(",", "|");
                dd.setDepartid(bmid);
                dd.setActive(1);
                dd.setAvatar(resultObject.getString("avatar"));
                dd.setPosition(resultObject.getString("title"));
                dd.setMobile(resultObject.getString("mobile"));
                dd.setWorkplace(resultObject.getString("work_place"));
                dd.setRemark(resultObject.getString("remark"));
                dd.setEmail(resultObject.getString("email"));
                dd.setJobnumber(resultObject.getString("job_number"));
                dd.setDingid("");

                tp.setPid(uid);
                tp.setPname(resultObject.getString("name"));
                tp.setPgw("6");
                tp.setPqx(6);
                tp.setDduserid(uid);

                su.setUser_id(uid);
                su.setUsername(resultObject.getString("mobile"));
                su.setPassword("e10adc3949ba59abbe56e057f20f883e1");
                su.setName(resultObject.getString("name"));
                su.setRole_id("99b87e96ef8643c7a335c24830c8bc69");
                su.setStatus("0");
                su.setBz(resultObject.getString("name"));
                su.setSkin("default");
                su.setNumber(resultObject.getString("mobile"));
                su.setPhone(resultObject.getString("mobile"));
                su.setDepartment_id(bmid);
                su.setCorpid("ding291dcdb4d2ea030935c2f4657eb6378f");

                hm.put("dd", dd);
                hm.put("tp", tp);
                hm.put("su", su);
            }
        }
        return hm;
    }

    /**
     * 静态方法来将List<Long>转换为字符串
     *
     * @param list
     * @return
     */
    public static String listToString(List<Long> list) {
        return String.join(",", list.stream().map(String::valueOf).collect(Collectors.toList()));
    }


}
