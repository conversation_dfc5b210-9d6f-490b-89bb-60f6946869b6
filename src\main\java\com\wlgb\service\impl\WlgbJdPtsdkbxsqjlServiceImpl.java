package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdPtsdkbxsqjl;
import com.wlgb.mapper.WlgbJdPtsdkbxsqjlMapper;
import com.wlgb.service.WlgbJdPtsdkbxsqjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 23:36
 */
@Service
public class WlgbJdPtsdkbxsqjlServiceImpl implements WlgbJdPtsdkbxsqjlService {
    @Resource
    private WlgbJdPtsdkbxsqjlMapper wlgbJdPtsdkbxsqjlMapper;

    @Override
    public void save(WlgbJdPtsdkbxsqjl wlgbJdPtsdkbxsqjl) {
        wlgbJdPtsdkbxsqjl.setCreateTime(new Date());
        wlgbJdPtsdkbxsqjl.setId(IdConfig.uuId());
        wlgbJdPtsdkbxsqjlMapper.insertSelective(wlgbJdPtsdkbxsqjl);
    }

    @Override
    public void updateById(WlgbJdPtsdkbxsqjl wlgbJdPtsdkbxsqjl) {
        wlgbJdPtsdkbxsqjl.setUpdateTime(new Date());
        wlgbJdPtsdkbxsqjlMapper.updateByPrimaryKeySelective(wlgbJdPtsdkbxsqjl);
    }

    @Override
    public WlgbJdPtsdkbxsqjl queryBySpBhAndSfLrJd(String spbh, Integer sfLrJd) {
        Example example = new Example(WlgbJdPtsdkbxsqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spbh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdPtsdkbxsqjlMapper.selectOneByExample(example);
    }
}
