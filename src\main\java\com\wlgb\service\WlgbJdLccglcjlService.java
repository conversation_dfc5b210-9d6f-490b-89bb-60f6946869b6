package com.wlgb.service;

import com.wlgb.entity.WlgbJdLccglcjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/28 12:50
 */
public interface WlgbJdLccglcjlService {
    void save(WlgbJdLccglcjl wlgbJdLccglcjl);

    void updateById(WlgbJdLccglcjl wlgbJdLccglcjl);

    Integer queryCountBySpBh(String spBh);

    WlgbJdLccglcjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd);

    List<WlgbJdLccglcjl> queryListByWlgbJdLccglcjl(WlgbJdLccglcjl wlgbJdLccglcjl);
}
