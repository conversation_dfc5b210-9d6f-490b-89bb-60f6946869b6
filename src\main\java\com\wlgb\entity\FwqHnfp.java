package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "fwq_hnfp")
public class FwqHnfp {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    private String scrname;
    private String scrtel;
    private java.util.Date time;
    private String time2;
    private String zytype;
    private String zyurl;
    private String zyurlfengmian;
    private Integer xiaoquid;
    private String xiaoquname;
    private String biaodiname;
    private String biaodiurl;
    private String zysize;
    private String sfsc;
    private String miaoshu;
}
