import{o as e,c as t,w as i,a as s,n as o,b as a,d as r,e as l,i as u,f as n,g,r as d,h,t as c,j as f,u as p,k as m,s as y,l as x,S,m as T,p as w,q as b,v as H,x as F,y as _,z,A as C,B as v,C as k,D as M,E as L,F as $,G as P,H as R,I as B,J as N,K as D,L as j,M as A,N as I,O,P as V}from"./index-BrM5EEkK.js";const q=(e,t)=>{const i=e.__vccOpts||e;for(const[s,o]of t)i[s]=o;return i};const U=q({name:"gui-image",props:{src:{type:String,default:""},width:{type:Number,default:300},height:{type:Number,default:0},timer:{type:Number,default:200},borderRadius:{type:String,default:"0rpx"},mode:{type:String,default:"widthFix"}},data:()=>({isLoading:!0,imgHeight:180,opacity:0,animate:!1,failed:!1}),methods:{imgLoad:function(e){var t=e.detail.width/e.detail.height;"widthFix"==this.mode?this.imgHeight=this.width/t:this.imgHeight=this.height,this.animate=!0,setTimeout((()=>{this.isLoading=!1,this.opacity=1}),this.timer)},error:function(){this.isLoading=!1,this.failed=!0}}},[["render",function(d,h,c,f,p,m){const y=u,x=n,S=g;return e(),t(S,{class:"gui-img gui-bg-gray gui-dark-bg-level-3",style:o({width:c.width+"rpx",height:0==c.height?p.imgHeight+"rpx":c.height+"rpx",borderRadius:c.borderRadius})},{default:i((()=>[s(y,{src:c.src,onLoad:m.imgLoad,onError:m.error,mode:c.mode,style:o({width:c.width+"rpx",height:p.imgHeight+"rpx",borderRadius:c.borderRadius,opacity:p.opacity})},null,8,["src","onLoad","onError","mode","style"]),p.isLoading?(e(),t(x,{key:0,class:a(["gui-img-loading gui-icons gui-color-gray",[p.animate?"gui-fade-out":""]]),style:o({width:c.width+"rpx",height:0==c.height?p.imgHeight+"rpx":c.height+"rpx",lineHeight:0==c.height?p.imgHeight+"rpx":c.height+"rpx",borderRadius:c.borderRadius})},{default:i((()=>[r("")])),_:1},8,["class","style"])):l("",!0),p.failed?(e(),t(x,{key:1,class:a(["gui-img-loading gui-icons gui-color-gray",[p.animate?"gui-fade-out":""]]),style:o({width:c.width+"rpx",height:0==c.height?p.imgHeight+"rpx":c.height+"rpx",lineHeight:0==c.height?p.imgHeight+"rpx":c.height+"rpx",borderRadius:c.borderRadius})},{default:i((()=>[r("")])),_:1},8,["class","style"])):l("",!0)])),_:1},8,["style"])}],["__scopeId","data-v-b6ed81c6"]]);function Y(e,t){return"string"==typeof e?t:e}const J=q({name:"gui-page-loading",props:{},data:()=>({isLoading:!1,BindingXObjs:[null,null,null],AnimateObjs:[null,null,null],animateTimer:800,intervalID:null}),watch:{},methods:{stopfun:function(e){return e.stopPropagation(),null},open:function(){this.isLoading=!0},close:function(){setTimeout((()=>{this.isLoading=!1}),100)},getRefs:function(e,t,i){if(t>=50)return i(this.$refs[e]),!1;var s=this.$refs[e];s?i(s):(t++,setTimeout((()=>{this.getRefs(e,t,i)}),100))}}},[["render",function(o,a,r,u,n,c){const f=g;return n.isLoading?(e(),t(f,{key:0,class:"gui-page-loading gui-flex gui-nowrap gui-align-items-center gui-justify-content-center gui-page-loading-bg",onClick:h(c.stopfun,["stop"]),onTouchmove:h(c.stopfun,["stop","prevent"])},{default:i((()=>[s(f,{class:"gui-column"},{default:i((()=>[s(f,{class:"gui-page-loading-point gui-flex gui-rows gui-justify-content-center"},{default:i((()=>[s(f,{class:"gui-page-loading-points animate1 gui-page-loading-color"}),s(f,{class:"gui-page-loading-points animate2 gui-page-loading-color"}),s(f,{class:"gui-page-loading-points animate3 gui-page-loading-color"})])),_:1}),s(f,{class:"gui-row gui-justify-content-center"},{default:i((()=>[d(o.$slots,"default",{},void 0,!0)])),_:3})])),_:3})])),_:3},8,["onClick","onTouchmove"])):l("",!0)}],["__scopeId","data-v-17f65621"]]);const E=q({name:"gui-refresh",props:{refreshText:{type:Array,default:function(){return["继续下拉刷新","松开手指开始刷新","数据刷新中","数据已刷新"]}},customClass:{type:Array,default:function(){return[["gui-color-gray"],["gui-color-gray"],["gui-primary-text"],["gui-color-green"]]}},refreshFontSize:{type:Number,default:28},triggerHeight:{type:Number,default:50}},data:()=>({reScrollTop:0,refreshHeight:0,refreshY:0,refreshStatus:0,refreshTimer:0}),methods:{touchstart:function(e){this.reScrollTop>10||(this.refreshY=e.changedTouches[0].pageY)},touchmove:function(e){if(this.refreshStatus>=1)return null;if(!(this.reScrollTop>10)){var t=e.changedTouches[0].pageY-this.refreshY;(t/=2)>=this.triggerHeight&&(t=this.triggerHeight,this.refreshStatus=1),t>15&&(this.refreshHeight=t)}},touchend:function(e){if(!(this.reScrollTop>10))return this.refreshStatus<1?this.resetFresh():void(1==this.refreshStatus&&(this.refreshStatus=2,this.$emit("reload")))},scroll:function(e){this.reScrollTop=e.detail.scrollTop},endReload:function(){this.refreshStatus=3,setTimeout((()=>{this.resetFresh()}),1e3)},resetFresh:function(){return this.refreshHeight=0,this.refreshStatus=0,null},rotate360:function(){var e=this.$refs.loadingIcon;animation.transition(e,{styles:{transform:"rotate(7200deg)",transformOrigin:"center"},duration:2e4,timingFunction:"linear",needLayout:!1,delay:0})}},emits:["reload"]},[["render",function(u,d,h,f,p,m){const y=n,x=g;return e(),t(x,{class:"gui-page-refresh gui-flex gui-column gui-justify-content-center",style:o({height:p.refreshHeight+"px"})},{default:i((()=>[s(x,{style:o({height:h.refreshFontSize+"rpx"}),class:"gui-flex gui-row gui-justify-content-center gui-align-items-center"},{default:i((()=>[0==p.refreshStatus||1==p.refreshStatus?(e(),t(y,{key:0,class:a(["gui-icons gui-block",h.customClass[p.refreshStatus]]),style:o({fontSize:h.refreshFontSize+"rpx",width:h.refreshFontSize+"rpx",height:h.refreshFontSize+"rpx",lineHeight:h.refreshFontSize+"rpx"})},{default:i((()=>[r("")])),_:1},8,["class","style"])):l("",!0),2==p.refreshStatus?(e(),t(y,{key:1,ref:"loadingIcon",class:a(["gui-icons gui-block gui-text-center gui-rotate360",h.customClass[p.refreshStatus]]),style:o({fontSize:h.refreshFontSize+"rpx",width:h.refreshFontSize+"rpx",height:h.refreshFontSize+"rpx",lineHeight:h.refreshFontSize+"rpx"})},{default:i((()=>[r("")])),_:1},8,["class","style"])):l("",!0),3==p.refreshStatus?(e(),t(y,{key:2,class:a(["gui-icons",h.customClass[p.refreshStatus]]),style:o({fontSize:h.refreshFontSize+"rpx",width:h.refreshFontSize+"rpx",height:h.refreshFontSize+"rpx",lineHeight:h.refreshFontSize+"rpx"})},{default:i((()=>[r("")])),_:1},8,["class","style"])):l("",!0),s(y,{style:o([{"margin-left":"12rpx"},{fontSize:h.refreshFontSize+"rpx"}]),class:a(["gui-page-refresh-text gui-block",h.customClass[p.refreshStatus]])},{default:i((()=>[r(c(h.refreshText[p.refreshStatus]),1)])),_:1},8,["class","style"])])),_:1},8,["style"])])),_:1},8,["style"])}],["__scopeId","data-v-e3c03afa"]]);const Z=q({name:"gui-loadmore",props:{loadMoreText:{type:Array,default:function(){return["","数据加载中","已加载全部数据","暂无数据"]}},customClass:{type:Array,default:function(){return["gui-color-gray"]}},loadMoreFontSize:{type:String,default:"26rpx"},status:{type:Number,default:0}},data:()=>({loadMoreStatus:0,hidden:!1}),created:function(){this.loadMoreStatus=this.status,this.status},methods:{loading:function(){this.loadMoreStatus=1},stoploadmore:function(){this.loadMoreStatus=0},stopLoadmore:function(){this.loadMoreStatus=0},nomore:function(){this.loadMoreStatus=2},empty:function(){this.loadMoreStatus=3},hide:function(){this.hidden=!0},rotate360:function(){var e=this.$refs.loadingiconforloadmore;animation.transition(e,{styles:{transform:"rotate(7200deg)"},duration:2e4,timingFunction:"linear",needLayout:!1,delay:0})},tapme:function(){0==this.loadMoreStatus&&this.$emit("tapme")}},emits:["tapme"]},[["render",function(u,d,f,p,m,y){const x=n,S=g;return e(),t(S,{class:"gui-load-more gui-flex gui-row gui-align-items-center gui-justify-content-center",onClick:h(y.tapme,["stop","prevent"])},{default:i((()=>[0==m.loadMoreStatus?(e(),t(S,{key:0},{default:i((()=>[s(x,{class:"gui-block",style:o({height:f.loadMoreFontSize})},null,8,["style"])])),_:1})):l("",!0),1==m.loadMoreStatus?(e(),t(S,{key:1,class:"gui-load-more-icon",ref:"loadingiconforloadmore"},{default:i((()=>[s(x,{class:a([f.customClass,"gui-icons gui-rotate360 gui-block"]),style:o({fontSize:f.loadMoreFontSize})},{default:i((()=>[r("")])),_:1},8,["class","style"])])),_:1},512)):l("",!0),s(x,{class:a(["gui-block",f.customClass]),style:o({fontSize:f.loadMoreFontSize})},{default:i((()=>[r(c(f.loadMoreText[m.loadMoreStatus]),1)])),_:1},8,["class","style"])])),_:1},8,["onClick"])}],["__scopeId","data-v-0a5aca14"]]);const G=q({name:"gui-iphone-bottom",props:{height:{type:String,default:"60rpx"},isSwitchPage:{type:Boolean,default:!1},customClass:{type:Array,default:function(){return["gui-bg-transparent"]}}},data:()=>({need:!1}),created:function(){if(!this.isSwitchPage){var e=f();if(e.model){e.model=e.model.replace(" ",""),e.model=e.model.toLowerCase();var t=e.model.indexOf("iphonex");t>5&&(t=-1);var i=e.model.indexOf("iphone1");i>5&&(i=-1),-1==t&&-1==i||(this.need=!0)}}}},[["render",function(i,s,r,u,n,d){const h=g;return n.need?(e(),t(h,{key:0,style:o({height:r.height}),class:a(r.customClass)},null,8,["style","class"])):l("",!0)}]]);const K=q({name:"gui-page",props:{fullPage:{type:Boolean,default:!1},customHeader:{type:Boolean,default:!1},headerClass:{type:Array,default:function(){return[]}},isHeaderSized:{type:Boolean,default:!0},statusBarClass:{type:Array,default:function(){return[]}},customFooter:{type:Boolean,default:!1},footerClass:{type:Array,default:function(){return[]}},footerSpaceClass:{type:Array,default:function(){return["gui-bg-gray","gui-dark-bg-level-2"]}},customFooterHeight:{type:Number,default:100},pendantClass:{type:Array,default:function(){return[]}},isLoading:{type:Boolean,default:!1},isSwitchPage:{type:Boolean,default:!1},fixedTopClass:{type:Array,default:function(){return[]}},refresh:{type:Boolean,default:!1},refreshText:{type:Array,default:function(){return["继续下拉刷新","松开手指开始刷新","数据刷新中","数据已刷新"]}},refreshClasses:{type:Array,default:function(){return[["gui-color-gray"],["gui-color-gray"],["gui-primary-text"],["gui-color-green"]]}},refreshFontSize:{type:Number,default:26},loadmore:{type:Boolean,default:!1},loadMoreText:{type:Array,default:function(){return["","数据加载中","已加载全部数据","暂无数据"]}},loadMoreClass:{type:Array,default:function(){return["gui-color-gray"]}},loadMoreFontSize:{type:String,default:"26rpx"},loadMoreStatus:{type:Number,default:1},apiLoadingStatus:{type:Boolean,default:!1},reFreshTriggerHeight:{type:Number,default:50}},data:()=>({pageStatus:!1,footerHeight:50,statusBarHeight:44,headerHeight:44,headerTapNumber:0,fixedTop:0,loadMoreTimer:null,fixedTopMargin:0,scrollTop:0,srcollTimer:null,refreshing:!1,pullingdownVal:0,topTagID:"no"}),watch:{isLoading:function(e){e?this.pageLoadingOpen():this.pageLoadingClose()}},mounted:function(){this.customFooter&&(this.footerHeight=p(this.customFooterHeight)),this.isLoading&&this.pageLoadingOpen();try{var e=f();e.model&&(this.statusBarHeight=e.statusBarHeight)}catch(t){return null}this.customFooter&&setTimeout((()=>{this.getDomSize("guiPageFooter",(e=>{this.footerHeight=e.height}),0)}),200),this.customHeader?setTimeout((()=>{this.getDomSize("guiPageHeader",(e=>{this.headerHeight=e.height,this.$nextTick((()=>{this.pageStatus=!0}))}),0)}),200):this.pageStatus=!0,this.customHeader?setTimeout((()=>{this.getDomSize("guiPageHeader",(e=>{this.fixedTop=e.height}),0)}),200):this.fixedTop=44,setTimeout((()=>{this.getDomSize("guiPageFixedTop",(e=>{this.fixedTopMargin=e.height}),0)}),200)},methods:{onpullingdown:function(e){if(this.apiLoadingStatus)return!1;e.changedTouches=[{pageY:e.pullingDistance}],this.$refs.guiPageRefresh.touchmove(e)},onrefresh:function(){if(this.apiLoadingStatus)return!1;this.refreshing=!0,this.$refs.guiPageRefresh.refreshStatus=2,setTimeout((()=>{this.$refs.guiPageRefresh.rotate360()}),200),this.$emit("reload")},pageLoadingOpen:function(){this.getRefs("guipageloading",0,(e=>{e.open()}))},pageLoadingClose:function(){this.getRefs("guipageloading",0,(e=>{e.close()}))},touchstart:function(e){return!!this.refresh&&(!this.apiLoadingStatus&&void this.$refs.guiPageRefresh.touchstart(e))},touchmove:function(e){return!!this.refresh&&(!this.apiLoadingStatus&&void this.$refs.guiPageRefresh.touchmove(e))},touchend:function(e){return!!this.refresh&&(!this.apiLoadingStatus&&void this.$refs.guiPageRefresh.touchend(e))},scroll:function(e){null!=this.srcollTimer&&clearTimeout(this.srcollTimer),this.srcollTimer=setTimeout((()=>{this.$refs.guiPageRefresh.scroll(e),this.$emit("scroll",e),this.scrollTop=e.detail.scrollTop}),500)},toTop:function(){this.topTagID="guiPageBodyTopTag",setTimeout((()=>{this.topTagID="no"}),500)},endReload:function(){this.$refs.guiPageRefresh.endReload(),this.refreshing=!1},reload:function(){if(this.apiLoadingStatus)return!1;this.$emit("reload"),this.loadmore&&this.$refs.guipageloadmore.stoploadmore()},getDomSize:function(e,t,i){if(i||(i=1),i>=50)return t({width:0,height:0}),!1;m().in(this).select("#"+e).boundingClientRect().exec((s=>null==s[0]||null==s[0].height?(i+=1,void setTimeout((()=>{this.getDomSize(e,t,i)}),50)):void t(s[0])))},stopfun:function(e){return e.stopPropagation(),null},headerTap:function(){this.headerTapNumber++,this.headerTapNumber>=2?(this.$emit("gotoTop"),this.headerTapNumber=0):setTimeout((()=>{this.headerTapNumber=0}),1e3)},getRefs:function(e,t,i){if(t>=50)return i(this.$refs[e]),!1;var s=this.$refs[e];s?i(s):(t++,setTimeout((()=>{this.getRefs(e,t,i)}),100))},loadmorefun:function(){return!!this.loadmore&&(!this.apiLoadingStatus&&(null!=this.loadMoreTimer&&clearTimeout(this.loadMoreTimer),void(this.loadMoreTimer=setTimeout((()=>{if(0!=this.$refs.guipageloadmore.loadMoreStatus)return null;this.$refs.guipageloadmore.loading(),this.$emit("loadmorefun")}),80))))},stopLoadmore:function(){this.$refs.guipageloadmore.stoploadmore()},stoploadmore:function(){this.$refs.guipageloadmore.stoploadmore()},nomore:function(){this.$refs.guipageloadmore.nomore()},empty:function(){this.$refs.guipageloadmore.empty()},toast:function(e){y({title:e,icon:"none"})},resetFooterHeight:function(){this.customFooter&&setTimeout((()=>{this.getDomSize("guiPageFooter",(e=>{this.footerHeight=e.height}),0)}),500)},isIphoneBottom:function(){var e=f();if(e.model){e.model=e.model.replace(" ",""),e.model=e.model.toLowerCase();var t=e.model.indexOf("iphonex");t>5&&(t=-1);var i=e.model.indexOf("iphone1");if(i>5&&(i=-1),-1!=t||-1!=i)return!0}return!1}},emits:["scroll","reload","loadmorefun","gotoTop"]},[["render",function(r,u,n,c,f,p){const m=g,y=Y(x("gui-refresh"),E),T=Y(x("gui-loadmore"),Z),w=S,b=Y(x("gui-iphone-bottom"),G),H=Y(x("gui-page-loading"),J);return e(),t(m,{style:o({opacity:f.pageStatus?1:0}),class:a(["gui-sbody gui-flex gui-column",[n.fullPage||n.refresh||n.loadmore?"gui-flex1":""]])},{default:i((()=>[n.customHeader?(e(),t(m,{key:0,class:a(["gui-header gui-transition-all",n.headerClass]),id:"guiPageHeader",ref:"guiPageHeader"},{default:i((()=>[s(m,{class:a(["gui-page-status-bar",n.statusBarClass]),style:o({height:f.statusBarHeight+"px"})},{default:i((()=>[d(r.$slots,"gStatusBar")])),_:3},8,["class","style"]),s(m,{class:"gui-flex gui-column gui-justify-content-center",onClick:h(p.headerTap,["stop"])},{default:i((()=>[d(r.$slots,"gHeader")])),_:3},8,["onClick"])])),_:3},8,["class"])):l("",!0),n.customHeader&&n.isHeaderSized?(e(),t(m,{key:1,style:o({height:f.headerHeight+"px"})},null,8,["style"])):l("",!0),n.refresh||n.loadmore?l("",!0):(e(),t(m,{key:2,class:a(["gui-flex gui-column gui-relative",[n.fullPage?"gui-flex1":""]]),id:"guiPageBody",ref:"guiPageBody"},{default:i((()=>[d(r.$slots,"gBody")])),_:3},8,["class"])),n.refresh||n.loadmore?(e(),t(m,{key:3,class:"gui-flex1 gui-relative",id:"guiPageBody",ref:"guiPageBody",style:o({marginTop:f.fixedTopMargin+"px"})},{default:i((()=>[s(w,{class:"gui-absolute-full","scroll-y":!0,"show-scrollbar":!1,onTouchstart:p.touchstart,onTouchmove:p.touchmove,onTouchend:p.touchend,onScroll:p.scroll,"scroll-into-view":f.topTagID,"scroll-with-animation":!1,onScrolltolower:p.loadmorefun},{default:i((()=>[s(m,{id:"guiPageBodyTopTag"},{default:i((()=>[s(y,{ref:"guiPageRefresh",onReload:p.reload,refreshText:n.refreshText,customClass:n.refreshClasses,triggerHeight:n.reFreshTriggerHeight,refreshFontSize:n.refreshFontSize},null,8,["onReload","refreshText","customClass","triggerHeight","refreshFontSize"])])),_:1}),d(r.$slots,"gBody"),n.loadmore?(e(),t(m,{key:0},{default:i((()=>[s(T,{ref:"guipageloadmore",status:n.loadMoreStatus,loadMoreText:n.loadMoreText,customClass:n.loadMoreClass,loadMoreFontSize:n.loadMoreFontSize},null,8,["status","loadMoreText","customClass","loadMoreFontSize"])])),_:1})):l("",!0)])),_:3},8,["onTouchstart","onTouchmove","onTouchend","onScroll","scroll-into-view","onScrolltolower"])])),_:3},8,["style"])):l("",!0),n.customFooter?(e(),t(m,{key:4,style:o({height:f.footerHeight+"px"})},null,8,["style"])):l("",!0),n.customFooter?(e(),t(m,{key:5,class:a(["gui-page-footer gui-border-box",n.footerClass]),id:"guiPageFooter",ref:"guiPageFooter"},{default:i((()=>[d(r.$slots,"gFooter"),s(b,{need:!n.isSwitchPage,customClass:n.footerSpaceClass},null,8,["need","customClass"])])),_:3},8,["class"])):l("",!0),s(m,{class:a(["gui-page-pendant",n.pendantClass])},{default:i((()=>[d(r.$slots,"gPendant")])),_:3},8,["class"]),s(m,{class:"gui-page-fixed-top",ref:"guiPageFixedTop",id:"guiPageFixedTop",style:o({top:f.fixedTop+"px"})},{default:i((()=>[d(r.$slots,"gFixedTop")])),_:3},8,["style"]),s(H,{ref:"guipageloading"},null,512)])),_:3},8,["style","class"])}]]);const W=q({name:"gui-tags",props:{width:{type:Number,default:0},text:{type:String,default:""},size:{type:Number,default:26},lineHeight:{type:Number,default:1.8},padding:{type:Number,default:15},margin:{type:Number,default:15},customClass:{type:Array,default:function(){return["gui-bg-primary","gui-color-white"]}},borderRadius:{type:Number,default:6},data:{type:Array,default:function(){return[]}},borderColor:{type:String,default:"rgba(255,255,255,0)"}},data:()=>({tapping:!1}),methods:{tapme:function(){this.$emit("tapme",this.data)}},emits:["tapme"]},[["render",function(s,l,u,g,d,h){const f=n;return e(),t(f,{class:a(["gui-block gui-tags gui-ellipsis gui-border",u.customClass]),style:o({width:0==u.width?"":u.width+"rpx",paddingLeft:u.padding+"rpx",paddingRight:u.padding+"rpx",lineHeight:u.size*u.lineHeight+"rpx",height:u.size*u.lineHeight+"rpx",fontSize:u.size+"rpx",borderRadius:u.borderRadius+"rpx",marginRight:u.margin+"rpx",marginBottom:u.margin+"rpx",borderColor:u.borderColor+" !important"}),onClick:h.tapme},{default:i((()=>[r(c(u.text),1)])),_:1},8,["class","style","onClick"])}],["__scopeId","data-v-c6572af6"]]);const X=function(e,t,i,s){e.count||(e.count=1),e.sizeType||(e.sizeType=["original","compressed"]),e.sourceType||(e.sourceType=["album","camera"]),M({count:e.count,sizeType:e.sizeType,sourceType:e.sourceType,success:e=>{console.log("先打印一下",e),t(e.tempFilePaths,e.tempFiles)},fail:e=>{i&&i(e)},complete:e=>{s&&s(e)}})};const Q=q({data:()=>({shouldShowView:!1,shouldShowView2:!1,sjc:"",userid:"",imgUri:"http://jiuyun2.qianquan888.com/upload/smile/shili.jpg?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxlZGl0b3JpYWwtZmVlZHwxOTR8fHxlbnwwfHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",imgUri2:"",message:"",reminderMessage:"笑容值如果低于80分,请重新拍照了,只有露出牙齿、嘴角上扬才会有笑容值哦!"}),async onLoad(e){const{userid:t,sjc:i}=e;if(t){const e=(new Date).getTime(),s=parseInt(i,10);if(i&&s>=e-6e5&&s<=e){const e=await this.selectJTOne(t);if(console.log(e,null==e.result.id,"++查询在照的接口返回数据+++"),null==e.result.id||null==e.result.id)this.shouldShowView=!0,this.userid=t,this.sjc=i;else{this.shouldShowView2=!0,this.imgUri2=e.result.url;let t="1"==e.result.swxw?"上午":"下午";this.message="今日"+t+"的笑容+工装检测照片已上传,不能重复上传",console.log(e.result.url,"++已经拍过照了+++")}}else this.shouldShowView=!1,y({title:"当前页面已过期，请从钉钉内部重新打开",icon:"none",duration:1e4})}else y({title:"当前用户ID不存在，请从钉钉内部打开",icon:"none",duration:5e3})},methods:{chooseImg(e){j({title:"提示",content:this.reminderMessage,showCancel:!1,success:t=>{if(t.confirm){let t=this;X({count:1,sizeType:["compressed"],sourceType:["camera"]},(function(i,s){const o=i[0],a=s[0].name;new File([o],a,{type:s[0].type}),t.openLoading(),A({url:"https://yinshengdj.qianquan888.com/ysdjtb/wlgb/smile/uploadtptooss",filePath:o,name:"file",formData:{userid:e},success:e=>{t.closeLoading();let i=JSON.parse(e.data);if(200==i.code){t.shouldShowView=!1,y({title:"已上传成功",icon:"none",duration:2e3}),t.shouldShowView2=!0,t.shouldShowView2=!0,t.imgUri2=i.result.url;let e="1"==i.result.swxw?"上午":"下午";t.message="今日"+e+"的笑容+工装检测照片已上传,不能重复上传"}else j({title:"提示",content:i.message,showCancel:!1,success:e=>{}})},fail:e=>{console.error("Upload Error:",e)}})}))}}})},openLoading:function(){this.$refs.guipageloading.open()},closeLoading:function(){this.$refs.guipageloading.close()},async selectJTOne(e){try{return(await this.fetchData(e)).data}catch(t){throw console.error("请求失败:",t),t}},fetchData:e=>new Promise(((t,i)=>{_({url:"https://yinshengdj.qianquan888.com/ysdjtb/wlgb/smile/selectJTOne",data:{userid:e},success:e=>{t(e)},fail:e=>{i(e)}})}))}},[["render",function(o,a,u,d,h,c){const f=n,p=g,m=Y(x("gui-image"),U),y=V,S=Y(x("gui-page-loading"),J),T=Y(x("gui-page"),K),w=Y(x("gui-tags"),W);return e(),I(O,null,[h.shouldShowView?(e(),t(p,{key:0,class:"content"},{default:i((()=>[s(T,null,{gBody:i((()=>[s(p,{class:"gui-padding-x"},{default:i((()=>[s(f,{class:"gui-h6 gui-color-gray"},{default:i((()=>[r("请露出牙齿、嘴角上扬至少15°，露出服装。参考示例图:")])),_:1})])),_:1}),s(p,{class:"gui-bg-white gui-dark-bg-level-3 gui-padding gui-margin-top"},{default:i((()=>[s(m,{src:h.imgUri,borderRadius:"10rpx"},null,8,["src"]),s(p,{class:"gui-h6 gui-color-gray"},{default:i((()=>[r("心情:开心")])),_:1}),s(p,{class:"gui-h6 gui-color-gray"},{default:i((()=>[r("笑容值:80分")])),_:1})])),_:1}),s(p,{class:"gui-bg-white gui-dark-bg-level-3 gui-padding gui-margin-top"},{default:i((()=>[s(y,{style:{"background-color":"beige"},class:"",onClick:a[0]||(a[0]=e=>c.chooseImg(h.userid))},{default:i((()=>[r("点我拍照")])),_:1})])),_:1}),s(S,{ref:"guipageloading"},{default:i((()=>[s(f,{class:"gui-block gui-text-small gui-text-center gui-color-gray gui-italic",style:{"padding-top":"10rpx"}},{default:i((()=>[r("上传图片中 ...")])),_:1})])),_:1},512)])),_:1})])),_:1})):l("",!0),h.shouldShowView2?(e(),t(p,{key:1,class:"content"},{default:i((()=>[s(T,null,{gBody:i((()=>[s(p,{class:"gui-bg-white gui-dark-bg-level-3 gui-padding gui-margin-top"},{default:i((()=>[s(m,{src:h.imgUri2,borderRadius:"10rpx"},null,8,["src"])])),_:1}),s(p,{style:{"margin-top":"66rpx"},class:"gui-padding-x"},{default:i((()=>[s(w,{text:` ${h.message}`,width:o.auto,lineHeight:3.5,size:20},null,8,["text","width"])])),_:1})])),_:1})])),_:1})):l("",!0)],64)}]]);export{Q as default};
