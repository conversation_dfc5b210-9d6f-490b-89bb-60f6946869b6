<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="executableClient" class="com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient"
          factory-method="getInstance" init-method="init" destroy-method="destroy">
        <property name="protocal" value="https"/>
        <property name="domainName" value="s-api.alibaba-inc.com"/>
        <property name="accessKey" value="changshaweilianjiudian-F67nm2m"/>
        <property name="secretKey" value="LF2lib16ErOHAV8z8344qhGK2kuFrxMs8EB0K11a"/>
    </bean>

    <bean class="org.springframework.aop.framework.autoproxy.BeanNameAutoProxyCreator">
        <property name="beanNames">
            <list>
                <value>entityDefineService</value>
                <value>mdcEntityDataCallService</value>
            </list>
        </property>
        <property name="interceptorNames">
            <list>
                <value>epaasAdvice</value>
            </list>
        </property>
    </bean>
</beans>
