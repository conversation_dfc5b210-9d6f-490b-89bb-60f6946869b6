package com.wlgb.service.impl;

import com.wlgb.entity.WlgbHxyhYjWkFqsk;
import com.wlgb.mapper.WlgbHxyhYjWkFqskMapper;
import com.wlgb.service.WlgbHxyhYjWkFqskService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/26 22:05
 */
@Service
public class WlgbHxyhYjWkFqskServiceImpl implements WlgbHxyhYjWkFqskService {
    @Resource
    private WlgbHxyhYjWkFqskMapper wlgbHxyhYjWkFqskMapper;

    @Override
    public void save(WlgbHxyhYjWkFqsk wlgbHxyhFqsk) {
        wlgbHxyhFqsk.setCreateTime(new Date());
        wlgbHxyhYjWkFqskMapper.insertSelective(wlgbHxyhFqsk);
    }

    @Override
    public void updateById(WlgbHxyhYjWkFqsk wlgbHxyhFqsk) {
        wlgbHxyhFqsk.setUpdateTime(new Date());
        wlgbHxyhYjWkFqskMapper.updateByPrimaryKeySelective(wlgbHxyhFqsk);
    }

    @Override
    public WlgbHxyhYjWkFqsk queryByDdBhAndFkFs(String ddbh, String fkfs) {
        WlgbHxyhYjWkFqsk wlgbHxyhYjWkFqsk = new WlgbHxyhYjWkFqsk();
        wlgbHxyhYjWkFqsk.setSkbh(ddbh);
        wlgbHxyhYjWkFqsk.setFkfs(fkfs);
        return wlgbHxyhYjWkFqskMapper.selectOne(wlgbHxyhYjWkFqsk);
    }

    @Override
    public Integer queryByDdBhAndZfZt(String ddbh, String zfzt) {
        WlgbHxyhYjWkFqsk wlgbHxyhYjWkFqsk = new WlgbHxyhYjWkFqsk();
        wlgbHxyhYjWkFqsk.setSkbh(ddbh);
        wlgbHxyhYjWkFqsk.setZfzt(zfzt);
        return wlgbHxyhYjWkFqskMapper.selectCount(wlgbHxyhYjWkFqsk);
    }

    @Override
    public List<WlgbHxyhYjWkFqsk> queryByDdBhAndYsBh(String ddBh, String ysBh) {
        WlgbHxyhYjWkFqsk wlgbHxyhYjWkFqsk = new WlgbHxyhYjWkFqsk();
        wlgbHxyhYjWkFqsk.setSkbh(ddBh);
        wlgbHxyhYjWkFqsk.setYsbh(ysBh);
        return wlgbHxyhYjWkFqskMapper.select(wlgbHxyhYjWkFqsk);
    }
}
