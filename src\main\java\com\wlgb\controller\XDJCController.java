package com.wlgb.controller;

import com.alibaba.fastjson.JSONObject;
import com.meituan.sdk.internal.exceptions.MtSdkException;
import com.wlgb.config.*;
import com.wlgb.entity.FwqXdjcDzmrlc;
import com.wlgb.entity.FwqXdjcDzmrlcFsjl;
import com.wlgb.entity.vo.TbVillaVo;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.WeiLianService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.wlgb.config.Tools.isEmpty;


/**
 * @Description: Class  巡店检查的接口
 * @author: fwq
 * @date: 2024年10月16日 09:56
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/xdjc")
public class XDJCController {
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;
    @Value("${hanshujisuan.douyinurl}")
    private String douyinurl;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;

    /**
     * 是否发起店长每日流程
     *
     * @param request
     * @return
     * @throws IOException
     * @throws MtSdkException
     */
    @RequestMapping(value = "/sffqdzmrlc")
    public Result sffqdzmrlc(HttpServletRequest request) throws IOException {
        String userid = request.getParameter("userid");
        String username = request.getParameter("username");
        String kaiguan = request.getParameter("kaiguan");
        if (isEmpty(userid) || isEmpty(username) || isEmpty(kaiguan)) {
            return Result.error("所有参数都不能为空");
        }
        int kg = Integer.parseInt(kaiguan);

        FwqXdjcDzmrlc fc = new FwqXdjcDzmrlc();
        fc.setUserid(userid);
        fc.setUsername(username);
        fc.setTime(new Date());
        fc.setKaiguan(kg);
        weiLianService.saveFwqXdjcDzmrlc(fc);
        return Result.OK("添加成功");
    }

    /**
     * 查询所有记录
     *
     * @param request
     * @return
     * @throws IOException
     * @throws MtSdkException
     */
    @RequestMapping(value = "/queryFwqXdjcDzmrlc")
    public Result queryFwqXdjcDzmrlc(HttpServletRequest request) throws IOException {
        List<FwqXdjcDzmrlc> fclist = weiLianService.queryFwqXdjcDzmrlc();
        return Result.OK(fclist);
    }


    /**
     * 发起店长流程  巡店检查里面的店长每日流程
     *
     * @return
     * @throws Exception
     */
    @PostMapping(value = "plfqdzlc")
    public Result plfqdzlc() throws Exception {
        List<FwqXdjcDzmrlc> fclist = weiLianService.queryFwqXdjcDzmrlc();
        if (!fclist.isEmpty()) {
            FwqXdjcDzmrlc fc = fclist.get(0);
            //kaiguan：1 可以发送  0：不能发送
            if (fc.getKaiguan() == 1) {
                System.out.println("发送");
                //发起人id
                String fqrid = "012412221639786136545";
                //表单id
                String formUuid = "FORM-057A7BB26235476DA90C2C7D3B99A5A118DF";
                //流程id
                String codeid = "TPROC--20B66Y81RUPOUJDJCDE57BVA9D3S27BBJIU1MK";
                YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("巡店检查");
                //获取钉钉key
                Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
                String token = DingToken.token(dingkey);
                //获取需要发起审批的人
                Map<String, Object> map = new HashMap<>();
                map.put("vxz", "直营");
                List<TbVillaVo> list = weiLianService.queryBsListXz(map);
                FwqXdjcDzmrlcFsjl fcDzmrlcFsjl = new FwqXdjcDzmrlcFsjl();
                for (TbVillaVo tv : list) {
                    JSONObject jsonObject = new JSONObject();
                    //流程执行人id,测试的时候可以直接用这个"15349026426046931"，上线后可以用这个tv.getPid()
                    String lczxr = tv.getPid();
                    jsonObject.put("lczxr", lczxr);
                    Calendar calendar = Calendar.getInstance();  // 创建Calendar实例
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");  // 定义日期格式
                    calendar.add(Calendar.DAY_OF_YEAR, 0);
                    Date today = calendar.getTime();  // 获取今天的日期
                    String todayStr = formatter.format(today);  // 格式化今天的日期
                    calendar.add(Calendar.DAY_OF_YEAR, 1);  // 减去1天
                    Date today2 = calendar.getTime();  // 获取明天的日期
                    String todayStr2 = formatter.format(today2);
                    Map<String,Object> mmap=new HashMap<>();
                    mmap.put("userid", lczxr);
                    mmap.put("stime", todayStr);
                    mmap.put("etime", todayStr2);
                    //查询是否已经发送过
                    List<FwqXdjcDzmrlcFsjl> fwqXdjcFsjllist = weiLianService.queryFwqXdjcDzmrlcFsjl(mmap);
                    //为空：没有发送过   不为空：发送过了
                    if (fwqXdjcFsjllist.isEmpty()) {
                        //唯一标识
                        jsonObject.put("wybs", UUID.randomUUID());
                        //提交日期
                        jsonObject.put("tjrq", System.currentTimeMillis());
                        //发送
                        GatewayResult gatewayResult = DingBdLcConfig.fqXzLcSl(token, ydAppkey, fqrid, formUuid, codeid, jsonObject.toJSONString());
                        Boolean isSuccessful = gatewayResult.getSuccess();
                        fcDzmrlcFsjl.setUserid(lczxr);
                        fcDzmrlcFsjl.setUsername(tv.getZbdz());
                        if (!isSuccessful) {
                            fcDzmrlcFsjl.setFsjg("发送失败");
                        } else {
                            fcDzmrlcFsjl.setFsjg("发送成功");
                        }
                        System.out.println(isSuccessful);
                    } else {
                        System.out.println("已经发送过了");
                    }
                    fcDzmrlcFsjl.setTime(new Date());
                    weiLianService.saveFwqXdjcDzmrlcFsjl(fcDzmrlcFsjl);
                }
            } else {
                System.out.println("不发送");
            }
        }
        return Result.OK("发送完成");
    }


    /**
     * 查询所有发送记录
     *
     * @param request
     * @return
     * @throws IOException
     * @throws MtSdkException
     */
    @RequestMapping(value = "/queryFwqXdjcDzmrlcfsjl")
    public Result queryFwqXdjcDzmrlcfsjl(HttpServletRequest request) throws IOException {
        List<FwqXdjcDzmrlcFsjl> fclist = weiLianService.queryFwqXdjcDzmrlcFsjl(null);
        return Result.OK(fclist);
    }
}
