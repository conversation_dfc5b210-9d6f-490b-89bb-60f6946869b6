package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月19日 16:21
 */
@Data
@Table(name = "wlgb_mt_md")
public class WlgbMtMd {
    @Id
    @KeySql(useGeneratedKeys = true)
    /**主键*/
    private java.lang.String id;
    /**
     * 创建人
     */
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    private java.lang.String sysOrgCode;
    /**
     * 美团门店id
     */
    private java.lang.String openShopUuid;
    /**
     * 门店名称
     */
    private java.lang.String shopname;
    /**
     * 门店地区
     */
    private java.lang.String branchname;
    /**
     * 门店地址
     */
    private java.lang.String shopaddress;
    /**
     * 门店城市
     */
    private java.lang.String cityname;
    /**
     * 账号
     */
    private java.lang.String zh;
    /**
     * 是否删除
     */
    private java.lang.Integer sfsc;
    /**
     * 对应宜搭的实例id
     */
    private java.lang.String slid;
    /**
     * 美团token，美团合作平台的token是每一个门店都有单独的token
     */
    private java.lang.String mtaccesstoken;
    /**
     * 刷新token需要用到的
     */
    private java.lang.String mtrefreshtoken;
    /**
     * 对应的美团开发者id
     */
    private java.lang.String mtdeveloperid;
    /**
     * 对应的美团开发者singkey
     */
    private java.lang.String mtsingkey;
    /**
     * 美团门店id
     */
    private java.lang.String opbizcode;
    /**
     * 美团门店名称
     */
    private java.lang.String opbizname;
    /**
     * token过期时间
     */
    private java.util.Date endtime;

    /**
     * 美团门店专属id
     */
    private java.lang.String mtmdzzid;
}
