package com.wlgb.service;

import com.wlgb.entity.WlgbJdLcyzjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/28 12:12
 */
public interface WlgbJdLcyzjlService {
    void save(WlgbJdLcyzjl wlgbJdLcyzjl);

    void updateById(WlgbJdLcyzjl wlgbJdLcyzjl);

    WlgbJdLcyzjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd);

    List<WlgbJdLcyzjl> queryListBySfLrJd(Integer sfLrJd);

    Integer queryCountBySpBh(String spBh);
}
