package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月22日 18:39
 */
@Data
@Table(name = "wlgb_hxyh_yjwkfqsk")
public class WlgbHxyhYjWkFqsk {
    /**主键*/
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
    /**协议单id*/
    private String xid;
    /**协议单订单编号*/
    private String xddbh;
    /**订单编号*/
    private String skbh;
    /**费用名称*/
    private String fymc;
    /**金额*/
    private Double je;
    /**付款方式*/
    private String fkfs;
    /**提交人*/
    private String tjr;
    /**提交人id*/
    private String tjrid;
    /**银盛编号*/
    private String ysbh;
    /**提交时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date tjsj;
    /**二维码链接*/
    private String ewm;
    /**微信支付appId*/
    private String appid;
    /**微信支付随机时间戳*/
    private String timestamp;
    /**微信支付随机字符串*/
    private String noncestr;
    /**微信支付订单详情扩展字符串*/
    private String package1;
    /**微信支付签名方式*/
    private String signtype;
    /**微信支付签名*/
    private String paysign;
    /**支付状态*/
    private String zfzt;
    /**支付状态*/
    private String home;
    /**金额类型*/
    private String jetype;
}
