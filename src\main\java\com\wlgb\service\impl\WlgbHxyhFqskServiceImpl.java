package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbHxyhFqsk;
import com.wlgb.mapper.WlgbHxyhFqskMapper;
import com.wlgb.service.WlgbHxyhFqskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月22日 18:41
 */
@Service
@Slf4j
public class WlgbHxyhFqskServiceImpl implements WlgbHxyhFqskService {
    @Resource
    private WlgbHxyhFqskMapper wlgbHxyhFqskMapper;

    @Override
    public void save(WlgbHxyhFqsk wlgbHxyhFqsk) {
        wlgbHxyhFqsk.setCreateTime(new Date());
        wlgbHxyhFqsk.setId(IdConfig.uuId());
        boolean cfTest = false;
        try {
            wlgbHxyhFqskMapper.insertSelective(wlgbHxyhFqsk);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbHxyhFqskMapper.insertSelective(wlgbHxyhFqsk);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，insert：参数" + wlgbHxyhFqsk.toString());
                    }
                }
            }
        }
    }

    @Override
    public void updateById(WlgbHxyhFqsk wlgbHxyhFqsk) {
        wlgbHxyhFqsk.setUpdateTime(new Date());
        boolean cfTest = false;
        try {
            wlgbHxyhFqskMapper.updateByPrimaryKeySelective(wlgbHxyhFqsk);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbHxyhFqskMapper.updateByPrimaryKeySelective(wlgbHxyhFqsk);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，update：参数" + wlgbHxyhFqsk.toString());
                    }
                }
            }
        }
    }

    @Override
    public WlgbHxyhFqsk queryByDdBhAndFkFs(String ddbh, String fkfs) {
        Example example = new Example(WlgbHxyhFqsk.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddbh", ddbh);
        criteria.andEqualTo("fkfs", fkfs);
        return wlgbHxyhFqskMapper.selectOneByExample(example);
    }

    @Override
    public Integer queryByDdBhAndZfZt(String ddbh, String zfzt) {
        Example example = new Example(WlgbHxyhFqsk.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddbh", ddbh);
        criteria.andEqualTo("zfzt", zfzt);
        return wlgbHxyhFqskMapper.selectCountByExample(example);
    }

    @Override
    public List<WlgbHxyhFqsk> queryByDdBhAndYsBh(String ddBh, String ysBh) {
        Example example = new Example(WlgbHxyhFqsk.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddbh", ddBh);
        criteria.andEqualTo("yxbh", ysBh);
        return wlgbHxyhFqskMapper.selectByExample(example);
    }
}
