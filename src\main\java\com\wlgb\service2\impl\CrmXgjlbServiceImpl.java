package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmXgjlb;
import com.wlgb.mapper.CrmXgjlbMapper;
import com.wlgb.service2.CrmXgjlbService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/26 16:49
 */
@Service
@DS(value = "second")
public class CrmXgjlbServiceImpl implements CrmXgjlbService {
    @Resource
    private CrmXgjlbMapper crmXgjlbMapper;

    @Override
    public void save(CrmXgjlb crmXgjlb) {
        crmXgjlbMapper.insertSelective(crmXgjlb);
    }
}
