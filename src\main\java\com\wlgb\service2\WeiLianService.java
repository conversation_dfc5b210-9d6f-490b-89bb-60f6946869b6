package com.wlgb.service2;

import com.alibaba.fastjson.JSONObject;
import com.wlgb.config.*;
import com.wlgb.entity.vo.FwqBbb;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.*;

import java.util.List;
import java.util.Map;

/**
 * weilian数据库通用
 */
public interface WeiLianService {
    List<TbVilla> queryTodayWcMd(Map<String, Object> map);

    Integer queryCountXydSfZd(TbXyd tbXyd);

    Integer queryCountXydSfZd2(TbXyd tbXyd);

    Integer queryCountWlgbYlxdSfZd(WlgbYlxd wlgbYlxd);

    Integer queryCountTbYddXydSfZd(TbYddXyd tbYddXyd);

    Integer queryPdCc(Map<String, Object> map);

    Double queryKdjZe(Map<String, Object> map);

    VillaIDsUtil queryIdsByVid(String vid);

    TbKhlyxz queryKhLyXzByLidOrLname(String khxz);

    FwqBbb2 queryBbb2ByXm(String xm);

    FwqBbb queryBbbByXfd(String xfd);

    void zxZjCc(String xid);

    String queryKdjZy();

    Map<String, Object> queryKdjXx(Map<String, Object> map);

    Integer queryXydMothCountByXfd(String xfd);

    Integer queryXydDateCountByXfd(String xfd);

    Integer queryXydYesCountByXfd(String xfd);

    Double queryXdYjByXfd(String xfd);

    void zxczgc();

    FwqBbb3 queryBmYj(FwqBbb2 fwqBbb2);

    Double queryKdjJzj(Map<String, Object> map);

    List<String> queryHpsAt(String cs);

    Integer queryXfdXdZfTs(String xfd);

    Integer queryQrdSjZfTs(String xfd);

    Integer queryBsSfZn(String xfd);

    Integer queryFdZn(String xfd);

    TbQrd queryQrdByXidAndSfSc(String xid);

    FwqQunid queryQun(String city);

    DingdingEmployee queryDingDingById(String id);

    DingdingEmployee queryDingDingByName(String name);

    Integer queryBsUserIdSfJms(String userId);

    List<Select> queryXdBsXlJms(String userId);

    List<Select> queryXdBsXl();

    List<Select> queryXdLyOrXzXl(Integer type);

    List<Select> queryXdHdCh();

    PageHelpUtil queryYldList(Map<String, Object> map);

    List<Select> queryVillaCity();

    PageHelpUtil queryScXydList(Map<String, Object> map);

    PageHelpUtil queryScQrdList(Map<String, Object> map);

    QyUtil queryQyUtil(Map<String, Object> map);

    List<DkBbJqr> queryDkBb(Map<String, Object> map);

    TbDk queryDkXq(String dkddbh);

    ZwAndDz queryZwAndDz(String vid);

    List<BsZw> queryBsMcIdZw(Map<String, Object> map);

    TbVilla queryVillaByVname(String vname);

    List<TbVilla> queryKyQnMdList(Map<String, Object> map);

    Integer queryKyQnMdCount(Map<String, Object> map);

    List<CrmCs> queryCrmCsList();

    List<CrmKhzt> queryCrmKhZtList(Integer lxid);

    Integer queryCrmDhSfcz(Map<String, Object> map);

    Integer queryCrmWxhCount(String qkhwxh);

    Integer queryCrmHmCount(String dhhm);

    List<CrmCsfq> queryCrmCsSfqByCsName(String csName);

    String queryCrmBbh();

    List<CrmQbkh> queryCrmDataImport(Map<String, Object> map);

    PageHelpUtil queryCrmData(Map<String, Object> map);

    Integer queryCmrCsFqIdByFqName(String fqName);

    List<CrmBmxq> queryCrmBmCxByBmDa(String bmDa);

    CrmBmxq queryCrmBmCxByUserid(String userid);

    List<CrmQbkh> queryCrmCcSjByCcData(String cc);

    List<CrmCsfq> queryCrmCsFqList();

    List<Map<String, Object>> querySxDzZbDz();

    List<Map<String, Object>> queryYxbXdWzf();

    List<DingdingEmployee> queryDingdingEmployeeList();

    List<HrMsz> queryHrYcSj();

    List<CrmUser> queryCrmUserList();

    void qkCrmBmXqTable();

    void qkCrmBmTable();

    void qkCrmUserBackupsTable();

    void bfCrmUserInBackups();

    void qkCrmUserCopyTable();

    void saveCrmUserCopy(CrmUser crmUser);

    void delCrmUserCopy();

    void qkCrmUserTable();

    void saveCrmUser();

    void saveDingdingEmployee(Demployee de);

    void updateDingdingEmployee(Demployee de);

    void delDingdingEmployee(String userid);

    Tbperson queryTbpersonById(String id);

    void saveTbperson(Tbperson tp);

    void updateTbperson(Tbperson tp);

    SysUser querySysUserById(String id);

    void saveSysUser(SysUser su);

    void updateSysUser(SysUser su);

    Integer queryFsDzbdSl();

    Integer querySyDdSl();

    Integer queryHrJrDmByYyName(String name);

    Integer queryHrJrTgByYyName(String name);

    List<Select> queryKdjVilla(Map<String, Object> map);

    TbKdj queryTbKdjByBsAndJcAndTc(Map<String, Object> map);

    List<TbKdj> queryYyKdj(Map<String, Object> map);

    Integer querySfMs(String vid);

    Map<String, Object> zxKdjPlXg(Map<String, Object> map);

    List<Select> queryCsFqSelect();

    List<Select> queryCsJqSelect();

    List<Select> queryBbCsSelect();

    List<Select> queryFgsSelect();

    PageHelpUtil queryBsWhList(Map<String, Object> map);

    List<TbVillaVo> queryBsListXz(Map<String, Object> map);

    PageHelpUtil queryFqWhList(Map<String, Object> map);

    PageHelpUtil queryJqWhList(Map<String, Object> map);

    List<JSONObject> queryXhsAndDyXdYj(Map<String, Object> map);

    List<JSONObject> queryXhsAndDyXFYj(Map<String, Object> map);

    List<Select> queryGwLb();

    List<Select> queryGw(String id);

    List<Select> queryQd();

    List<Select> queryWx();

    List<Select> queryJlJd();

    PageHelpUtil queryXyGw(Map<String, Object> map);

    PageHelpUtil queryZtGw(Map<String, Object> map);

    Integer queryJrDm(String name);

    Integer queryJrTg(String name);

    List<Map<String, Object>> queryBmZpQk(Map<String, Object> map);

    List<Map<String, Object>> queryBmFqSg(Map<String, Object> map);

    Map<String, Object> queryBmZpQkByFzr(Map<String, Object> map);

    Map<String, Object> queryBmFqSgByFzr(Map<String, Object> map);

    List<JSONObject> queryXydXxByVidThreeDay(Map<String, Object> map);

    JSONObject queryCyQyOneByQyName(String qy);

    List<Hpsl> queryHpSlCgMonth();

    DingdingDepartment queryDingdingDeparTment(String bmid);

    void saveDingdingDeparTment(Department de);

    void updateDingdingDeparTment(Department de);

    void delDingdingDeparTment(String dingdingid);

    Integer queryXdSd(String xddh, String xdxm);

    List<FwqWnl> queryWnl(String nian, String yue);

    List<FwqThbD9c> queryDyDataMonth();

    void saveFwqXdjcDzmrlc(FwqXdjcDzmrlc fc);

    List<FwqXdjcDzmrlc> queryFwqXdjcDzmrlc();

    void saveFwqXdjcDzmrlcFsjl(FwqXdjcDzmrlcFsjl fc);

    List<FwqXdjcDzmrlcFsjl> queryFwqXdjcDzmrlcFsjl(Map<String, Object> map);


}
