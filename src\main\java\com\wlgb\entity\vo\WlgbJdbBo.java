package com.wlgb.entity.vo;

import com.wlgb.entity.WlgbJdb;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/3 10:53
 */
@Data
public class WlgbJdbBo extends WlgbJdb {
    /** 进场度数*/
    private java.lang.Double xjcds;
    /** 退场度数*/
    private java.lang.Double xtcds;
    /**微信未通过截图*/
    private java.lang.String wxwtgjt;

    /**微信未通过原因*/
    private java.lang.String wxwtgyy;

    /**订单修改原因*/
    private java.lang.String ddxgyy;
    /**增值图片(是)*/
    private java.lang.String szztp;

    /**是否确认客户入场时间*/
    private java.lang.Integer sfqrkhrcsj;

    /**确认图片(是)*/
    private java.lang.String qrtp;

    /**场地费尾款金额*/
    private java.lang.Double cdfwkje;

    /**增值尾款金额*/
    private java.lang.Double zzwkje;

    /**押金*/
    private java.lang.Double qyj;
}
