package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmBm;
import com.wlgb.mapper.CrmBmMapper;
import com.wlgb.service4.DhRjCrmBmService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 21:15
 */
@Service
@DS("fourth")
public class DhRjCrmBmServiceImpl implements DhRjCrmBmService {
    @Resource
    private CrmBmMapper crmBmMapper;

    @Override
    public void save(CrmBm crmBm) {
        crmBmMapper.insertSelective(crmBm);
    }
}
