package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.DhrjXyd;
import com.wlgb.mapper.DhrjXydMapper;
import com.wlgb.service4.DhrjXydService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 12:02
 */
@Service
@DS("fourth")
public class DhrjXydServiceImpl implements DhrjXydService {
    @Resource
    private DhrjXydMapper dhrjXydMapper;

    @Override
    public void save(DhrjXyd dhrjXyd) {
        dhrjXydMapper.insertSelective(dhrjXyd);
    }

    @Override
    public void updateById(DhrjXyd dhrjXyd) {
        dhrjXydMapper.updateByPrimaryKeySelective(dhrjXyd);
    }

    @Override
    public DhrjXyd queryDhRjXydByDhRjXyd(DhrjXyd dhrjXyd) {
        return dhrjXydMapper.selectOne(dhrjXyd);
    }

    @Override
    public List<DhrjXyd> queryList(DhrjXyd dhrjXyd) {
        return dhrjXydMapper.select(dhrjXyd);
    }
}
