package com.wlgb.service;

import com.wlgb.entity.WlgbJdLcxmcgdfksqjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/20 0:04
 */
public interface WlgbJdLcxmcgdfksqjlService {
    void save(WlgbJdLcxmcgdfksqjl wlgbJdLcxmcgdfksqjl);

    void updateById(WlgbJdLcxmcgdfksqjl wlgbJdLcxmcgdfksqjl);

    WlgbJdLcxmcgdfksqjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd);

    List<WlgbJdLcxmcgdfksqjl> queryListByWlgbJdLcxmcgdfksqjl(WlgbJdLcxmcgdfksqjl wlgbJdLcxmcgdfksqjl);
}
