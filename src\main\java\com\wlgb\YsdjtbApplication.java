package com.wlgb;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;
import tk.mybatis.spring.annotation.MapperScan;

//@SpringBootApplication
@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
@MapperScan("com.wlgb.mapper")
@ImportResource("classpath:applicationContext.xml")
@org.mybatis.spring.annotation.MapperScan(basePackages = {"com.wlgb.mapper1"})
public class YsdjtbApplication {

    public static void main(String[] args) {
        SpringApplication.run(YsdjtbApplication.class, args);
    }

}
