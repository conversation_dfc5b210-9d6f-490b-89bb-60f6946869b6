package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqThbD3c;
import com.wlgb.entity.FwqThbD9c;
import com.wlgb.mapper.FwqThbD3cMapper;
import com.wlgb.mapper.FwqThbD9cMapper;
import com.wlgb.mapper1.WeiLianMapper;
import com.wlgb.service2.FwqThbD3cService;
import com.wlgb.service2.FwqThbD9cService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
@DS(value = "second")
public class FwqThbD9cServiceImpl implements FwqThbD9cService {
    @Resource
    private FwqThbD9cMapper fwqThbD9cMapper;
    @Resource
    private WeiLianMapper weiLianMapper;

    @Override
    public void save(FwqThbD9c fwqThbD9c) {
        fwqThbD9cMapper.insertSelective(fwqThbD9c);
    }


    @Override
    public FwqThbD9c selectOne(FwqThbD9c fwqThbD9c) {
        return fwqThbD9cMapper.selectOne(fwqThbD9c);
    }

    @Override
    public void updateById(FwqThbD9c fwqThbD9c) {
        fwqThbD9cMapper.updateByPrimaryKeySelective(fwqThbD9c);
    }

    @Override
    public void update(Map<String, Object> map) {
        weiLianMapper.updateFwqThbD3cByLsh(map);
    }

    @Override
    public void delete(Map<String, Object> map) {
        weiLianMapper.deleteFwqThbD3cByLsh(map);
    }
}
