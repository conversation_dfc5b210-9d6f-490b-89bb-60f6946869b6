package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmCcjl;
import com.wlgb.mapper.CrmCcjlMapper;
import com.wlgb.service4.DhRjCrmCcJlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 18:28
 */
@Service
@DS("fourth")
public class DhRjCrmCcJlServiceImpl implements DhRjCrmCcJlService {
    @Resource
    private CrmCcjlMapper crmCcjlMapper;

    @Override
    public void save(CrmCcjl crmCcjl) {
        crmCcjlMapper.insertSelective(crmCcjl);
    }
}
