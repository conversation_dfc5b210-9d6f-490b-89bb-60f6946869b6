package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.config.oss.CommonUtils;
import com.wlgb.config.oss.OssBootUtil;
import com.wlgb.entity.OssFile;
import com.wlgb.mapper.OssFileMapper;
import com.wlgb.service.OssFileService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/11/27 13:42
 * @Version 1.0
 */
@Service
public class OssFileServiceImpl implements OssFileService {
    @Resource
    private OssFileMapper ossFileMapper;

    @Override
    public String upload(MultipartFile multipartFile) {
        String fileName = multipartFile.getOriginalFilename();
        System.out.println("---------------进入上传oss文件方法-----------+++++++++" + fileName);
        fileName = CommonUtils.getFileName(fileName);
        OssFile ossFile = new OssFile();
        ossFile.setFileName(fileName);
        String url = OssBootUtil.upload(multipartFile, "upload/test");
        if (url != null && !"".equals(url)) {
            url = url.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
        }
        ossFile.setId(IdConfig.uuId());
        ossFile.setUrl(url);
        ossFile.setCreateTime(new Date());
        ossFileMapper.insertSelective(ossFile);
        System.out.println("---------------上传oss文件方法已结束-----------+++++++++");
        return url;
    }

    /**
     * 保存店长打卡的微笑照片
     *
     * @param multipartFile
     * @return
     */
    @Override
    public String uploadSmile(MultipartFile multipartFile) {
        String fileName = multipartFile.getOriginalFilename();
        System.out.println("---------------进入上传oss文件方法-----------+++++++++" + fileName);
        fileName = CommonUtils.getFileName(fileName);
        OssFile ossFile = new OssFile();
        ossFile.setFileName(fileName);
        String url = OssBootUtil.upload(multipartFile, "upload/smile");
        if (url != null && !"".equals(url)) {
            url = url.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
        }
        ossFile.setId(IdConfig.uuId());
        ossFile.setUrl(url);
        ossFile.setCreateTime(new Date());
        ossFileMapper.insertSelective(ossFile);
        System.out.println("---------------上传oss文件方法已结束-----------+++++++++");
        return url;
    }
}
