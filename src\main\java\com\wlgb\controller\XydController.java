package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkyida_1_0.models.GetOperationRecordsResponseBody;
import com.google.gson.internal.LinkedTreeMap;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.*;
import com.wlgb.service.*;
import com.wlgb.service2.*;
import com.wlgb.service3.DzxydService;
import com.wlgb.service3.WeiLianDaiBanService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.wlgb.config.Tools.isEmpty;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月13日 22:57
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/xyd")
public class XydController {
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WlgbYlxdService wlgbYlxdService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private TbQrdService tbQrdService;
    @Autowired
    private TbKdjZkService tbKdjZkService;
    @Autowired
    private WlgbXydLogService wlgbXydLogService;
    @Autowired
    private WlgbDzdbjlService wlgbDzdbjlService;
    @Autowired
    private DzxydService dzxydService;
    @Autowired
    private TbDkService tbDkService;
    @Autowired
    private WlgbCustomerLogService wlgbCustomerLogService;
    @Autowired
    private WlgbDksljlService wlgbDksljlService;
    @Autowired
    private WlgbBdjlService wlgbBdjlService;
    @Autowired
    private WeiLianDaiBanService weiLianDaiBanService;
    @Autowired
    private WlgbDdhfService wlgbDdhfService;
    @Autowired
    private WlgbYwjlhfService wlgbYwjlhfService;
    @Autowired
    private TbVillaService tbVillaService;
    @Autowired
    private WlgbChcbdjjlService wlgbChcbdjjlService;
    @Autowired
    private WlgbJdbOneService wlgbJdbOneService;
    @Autowired
    private WlgbJdbTwoService wlgbJdbTwoService;
    @Autowired
    private WlgbJdbService wlgbJdbService;
    @Autowired
    private CrmQbkhService crmQbkhService;
    @Autowired
    private TbYddXydService tbYddXydService;
    @Autowired
    private OssFileService ossFileService;
    @Autowired
    private WlgbHxyhFqskService wlgbHxyhFqskService;
    @Autowired
    private WlgbYddYjWkJlService wlgbYddYjWkJlService;
    @Autowired
    private WlgbYddMdxfjlService wlgbYddMdxfjlService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;

    /**
     * 撞单判断
     */
    @RequestMapping(value = "sfZd")
    public Result sfZd(HttpServletRequest request) {
        String bsid = request.getParameter("bsid");
        String xddbh = request.getParameter("ddbh");
        String xid = request.getParameter("xid") == null ? "" : request.getParameter("xid");
        String jc = request.getParameter("jc");
        String tc = request.getParameter("tc");
        if (isEmpty(bsid) || isEmpty(xddbh) || isEmpty(jc) || isEmpty(tc)) {
            return Result.error("bsid、xddbh、jc、tc四个参数一个都不能少");
        }
        log.info("*****参数*****{}", xid);
        TbXyd tbXyd = new TbXyd();
        //进场时间
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(Long.parseLong(jc));
        tbXyd.setXjctime(c.getTime());
        //退场时间
        c.setTimeInMillis(Long.parseLong(tc));
        tbXyd.setXtctime(c.getTime());
        if (tbXyd.getXjctime().getTime() >= tbXyd.getXtctime().getTime()) {
            return Result.error("退场时间不能早于进场时间！");
        }

        Pattern pattern = Pattern.compile("[\u4E00-\u9FA5]");
        Matcher matcher = pattern.matcher(bsid);
        //如果返回false：不包含汉字（就是别墅id），返回true：包含汉字（是别墅名称）
        boolean isbsid = matcher.find();
        if (isbsid) {
            TbVilla villa = weiLianDdXcxService.queryTbVillaByIdOrVname(bsid);
            //重新赋值一遍，防止bsid是别墅名称而不是id
            bsid = villa.getVid();
        }
        tbXyd.setXbsmc(bsid);
//        tbXyd.setXddbh(xddbh);
//        tbXyd.setXid(xid);
//        //查询结果>0：撞单，否则不撞单
//        Integer countXydSfZd = weiLianService.queryCountXydSfZd(tbXyd);
//        if (countXydSfZd > 0) {
//            return Result.error("该门店此时间段协议单撞单了！");
//        }

        TbXyd tbXyd1 = tbXydService.queryByDdBh(xddbh);
        if (tbXyd1 != null) {
            if (!tbXyd.getXbsmc().equals(tbXyd1.getXbsmc())) {
                tbXyd.setXid(null);
            } else {
                tbXyd.setXid(tbXyd1.getXid());
            }
            tbXyd.setXsfsc(tbXyd1.getXsfsc());
        } else {
            tbXyd.setXsfsc("0");
        }
        if ("0".equals(tbXyd.getXsfsc())) {
            //结果大于0说明存在相同订单（撞单）
            Integer countXydSfZd = weiLianService.queryCountXydSfZd(tbXyd);
            System.out.println(countXydSfZd);
            if (countXydSfZd > 0) {
                return Result.error("该门店此时间段协议单撞单了！");
            }
        }
        //预留撞单
        WlgbYlxd wlgbYlxd = new WlgbYlxd();
        wlgbYlxd.setXbsmc(bsid);
        wlgbYlxd.setXjctime(tbXyd.getXjctime());
        wlgbYlxd.setXtctime(tbXyd.getXtctime());
//        wlgbYlxd.setXid(xid);
//        //预留单判断撞单
//        Integer countWlgbYlxdSfZd = weiLianService.queryCountWlgbYlxdSfZd(wlgbYlxd);
//        System.out.println(countWlgbYlxdSfZd);
//        if (countWlgbYlxdSfZd > 0) {
//            return Result.error("该门店此时间段预留单撞单了！");
//        }

        //预留单判断撞单
        WlgbYlxd wlgbYlxd1 = wlgbYlxdService.queryByDdBhAndSfSc(xddbh, 0);
        if (wlgbYlxd1 != null) {
            if (!wlgbYlxd.getXbsmc().equals(wlgbYlxd1.getXbsmc())) {
                wlgbYlxd.setXid(null);
            } else {
                wlgbYlxd.setXid(wlgbYlxd1.getXid());
            }
            wlgbYlxd.setSfsc(wlgbYlxd1.getSfsc());
        } else {
            wlgbYlxd.setSfsc(0);
        }
        if (wlgbYlxd.getSfsc() == 0) {
            Integer countWlgbYlxdSfZd = weiLianService.queryCountWlgbYlxdSfZd(wlgbYlxd);
            System.out.println(countWlgbYlxdSfZd);
            if (countWlgbYlxdSfZd > 0) {
                return Result.error("该门店此时间段预留单撞单了！");
            }
        }

        return Result.OK();
    }

    /**
     * 下单与改单客单价判断
     */
    @RequestMapping(value = "tjCcPd")
    public Result tjCcPd(HttpServletRequest request) {
        String xhfyj = request.getParameter("xhfyj");
        String xbsmc = request.getParameter("xbsmc");
        String jc = request.getParameter("jc");
        String tc = request.getParameter("tc");
        String userid = request.getParameter("userid");
        Map<String, Object> map = new HashMap<>();
        if (jc != null && !"".equals(jc)) {
            Calendar c = Calendar.getInstance();
            c.setTimeInMillis(Long.parseLong(jc));
            map.put("jcsj", c.getTime());
        } else {
            return Result.error("进场时间不能为空！");
        }
        if (tc != null && !"".equals(tc)) {
            Calendar c = Calendar.getInstance();
            c.setTimeInMillis(Long.parseLong(tc));
            map.put("tcsj", c.getTime());
        } else {
            return Result.error("退场时间不能为空！");
        }
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(xbsmc);
        if (villa == null) {
            villa = weiLianService.queryVillaByVname(xbsmc);
            if (villa != null) {
                xbsmc = villa.getVid();
            }
        }
        map.put("vid", xbsmc);
        Integer pdCc = weiLianService.queryPdCc(map);
        Double zk = 0.0;
        TbKdjZk kdjZk = tbKdjZkService.queryByCcAndSfSc(pdCc, "0");
        zk = kdjZk != null ? kdjZk.getZk() / 10 : 1;
        Double kdjZe = weiLianService.queryKdjZe(map);
        double kdjje = kdjZe * zk;
        if (Double.parseDouble(xhfyj) < kdjje) {
            //调用阿里云函数计算的异步函数
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("cdfje", xhfyj);
            paramMap.put("kdjje", kdjje + "");
            paramMap.put("bsname", villa != null ? villa.getVname() : "");
            paramMap.put("uuid", IdConfig.uuId());
            paramMap.put("jcsj", DateFormatConfig.df1(map.get("jcsj")));
            paramMap.put("tcsj", DateFormatConfig.df1(map.get("tcsj")));
            paramMap.put("userid", userid);
            System.out.println("uuid====================" + paramMap.get("uuid"));
            //在这里进行异步调用函数计算里面的函数即可
            try {
                HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/tjCcPdTask", paramMap);
            } catch (IOException e) {
                e.printStackTrace();
            }
            return Result.error("价格不达标");
        }
        return Result.OK();
    }

    /**
     * 宜搭在调用xydSave接口时会先用get请求一次，用来校验ssrf，这个接口会返回宜搭一个ok
     *
     * @param request
     * @return
     */
    @GetMapping(value = "xydSave")
    public Result xydSaveForGet(HttpServletRequest request) {
        // 获取必要参数，如 datas
        String datas = request.getParameter("datas");
        // 如果 datas 为空，返回成功但提示无法验证（根据实际情况定制错误信息）
        if (datas == null) {
            return Result.ok("无法验证，datas为空");
        }
        // 进行ssrt（或其他需要的）校验逻辑
        // ...

        // 如果校验通过，返回成功
        return Result.OK();
    }

    /**
     * 直接下单
     */
    @PostMapping(value = "xydSave")
    public Result xydSave(HttpServletRequest request) {
        String datas = request.getParameter("datas");

        if (datas == null) {
            return Result.error("datas空的");
        }
        //实例ID
        String formInstId = request.getParameter("formInstId");
        JSONObject jsonObject = JSONObject.parseObject(datas);

        TbXyd tbXyd = JSONObject.toJavaObject(jsonObject, TbXyd.class);

        if (tbXyd.getXjctime().getTime() >= tbXyd.getXtctime().getTime()) {
            return Result.error("退场时间不能早于进场时间！");
        }
        Integer countXydSfZd = weiLianService.queryCountXydSfZd(tbXyd);
        System.out.println("是否撞单======" + countXydSfZd);
        if (countXydSfZd > 0) {
            return Result.error("该门店此时间段撞单了！");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("formInstId", formInstId);
        paramMap.put("uuid", IdConfig.uuId() + "_" + formInstId);
        paramMap.put("sfxyd", "1");
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/xydsavetask/xydSaveTask", paramMap);
        } catch (IOException e) {
            System.out.println("/ysdjtb/wlgb/xydedittask/xydSaveTask异步调用失败了");
        }

        return Result.OK();
    }

    /**
     * 宜搭在调用editXyd接口时会先用get请求一次，用来校验ssrf，这个接口会返回宜搭一个ok
     *
     * @param request
     * @return
     */
    @GetMapping(value = "editXyd")
    public Result editXydForGet(HttpServletRequest request) {
        // 获取必要参数，如 datas
        String datas = request.getParameter("datas");
        // 如果 datas 为空，返回成功但提示无法验证（根据实际情况定制错误信息）
        if (datas == null) {
            return Result.ok("无法验证，datas为空");
        }
        // 进行ssrt（或其他需要的）校验逻辑
        // ...

        // 如果校验通过，返回成功
        return Result.OK();
    }

    /**
     * 修改协议单
     */
    @PostMapping(value = "editXyd")
    public Result editXyd(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        String datasName = request.getParameter("datasName");
        if (datasName != null && !"".equals(datasName)) {
            datasName = datasName.replace(" ", "");
        }
        //实例ID
        String formInstId = request.getParameter("formInstId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject.getString("xid") == null || "".equals(jsonObject.getString("xid"))) {
            return Result.error("请选择要修改的协议单！");
        }
        TbXyd tbXyd1 = tbXydService.queryById(jsonObject.getString("xid"));
        if (tbXyd1 == null) {
            return Result.error("订单不存在！");
        }
        TbXyd xyd = JSONObject.toJavaObject(jsonObject, TbXyd.class);
        Integer countXydSfZd = weiLianService.queryCountXydSfZd(xyd);
        if (countXydSfZd > 0) {
            System.out.println("撞单了");
            return Result.error("该门店此时间段撞单了！");
        }
        System.out.println(jsonObject.toString() + "------------------");
        System.out.println(tbXyd1.getSfdyjk() + "--------tbXyd1.getSfdyjk()----------");
        //只有本地数据库是1才不需要触发宜搭的修改事件,其他情况下都需要调用接口
        boolean bb = (tbXyd1.getSfdyjk() == 1);
        if (bb) {
            //修改本地数据库的sfdyjk值
            TbXyd tbXyd = new TbXyd();
            tbXyd.setXid(tbXyd1.getXid());
            tbXyd.setSfdyjk(0);
            //重新创建对象，以免多个数据修改
            tbXydService.updateById(tbXyd);
            return Result.OK();
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userId", datasName);
        paramMap.put("formInstId", formInstId);
        paramMap.put("sfxyd", "1");
        paramMap.put("uuid", IdConfig.uuId() + "_" + jsonObject.getString("xid"));
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/xydedittask/xydeditTask", paramMap);
        } catch (IOException e) {
            System.out.println("/ysdjtb/wlgb/xydedittask/xydeditTask异步调用失败");
        }
        return Result.OK();
    }

    /**
     * 删掉校验
     */
    @RequestMapping(value = "scXydJy")
    public Result scXydJy(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String datasXid = request.getParameter("datasXid");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        TbXyd xyd = JSONObject.toJavaObject(jsonObject, TbXyd.class);
        TbXyd tbXyd = tbXydService.queryById(xyd.getXid() != null && !"".equals(xyd.getXid()) ? xyd.getXid() : datasXid);
        if (tbXyd == null) {
            System.out.println("数据不存在");
            return Result.error("数据不存在！");
        }
        if ("1".equals(tbXyd.getXsfsc())) {
            System.out.println("此单为删除单！");
            return Result.OK("已经删除");
        }
        TbQrd tbQrd = weiLianService.queryQrdByXidAndSfSc(tbXyd.getXid());
        if (tbQrd != null) {
            if (tbQrd.getQrsj() != null) {
                return Result.error("已经提交对账，不允许删单！");
            }
        }
        return Result.OK();
    }

    /**
     * 获取表单被修改记录
     */
    @RequestMapping(value = "getxgjl")
    public Result getxgjl(HttpServletRequest request) throws Exception {
        String formInstanceId = request.getParameter("formInstanceId");
        log.info("********formInstanceId=====*********{}", formInstanceId);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        Map<String, ?> map = DingBdLcConfig.getBdxgjl(token, ydAppkey, "15349026426046931",
                "FORM-0KYJ7URV0JWMWJ5OYJY4U11XWV4R1YLX5Q4KK6", formInstanceId);
        log.info("*******map*******{}", map);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"); // 假设时间字符串格式为"yyyy-MM-dd HH:mm:ss"
        LocalDateTime now = LocalDateTime.now();
        List<LinkedTreeMap> list = (List<LinkedTreeMap>) map.get(formInstanceId);
        long diffMinutes = 100000000000l;
        String displayName = "鬼";
        Map<String, Object> mapp = new HashMap<>();
        String gmtModifiedStr = (String) list.get(0).get("gmtModified");
        LocalDateTime gmtModifiedTime = LocalDateTime.parse(gmtModifiedStr, formatter);
        Duration duration = Duration.between(gmtModifiedTime, now);
        log.info("时间差：{} 分钟", duration.toMinutes());
        if (duration.toMinutes() < diffMinutes) {
            diffMinutes = duration.toMinutes();
            displayName = (String) ((LinkedTreeMap) list.get(0).get("operator")).get("displayName");
        }
        mapp.put("diffMinutes", diffMinutes);
        mapp.put("xgr", displayName);
        return Result.OK(mapp);
    }

    /**
     * 删除订单
     */
    @RequestMapping(value = "scDd")
    public Result scDd(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("datasName");
        String datasXid = request.getParameter("datasXid");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        userid = userid.replace(" ", "");
        TbXyd xyd = JSONObject.toJavaObject(jsonObject, TbXyd.class);
        TbXyd tbXyd = tbXydService.queryById(xyd.getXid() != null && !"".equals(xyd.getXid()) ? xyd.getXid() : datasXid);
        if (tbXyd == null) {
            System.out.println("数据不存在");
            return Result.error("数据不存在！");
        }
        if ("1".equals(tbXyd.getXsfsc())) {
            System.out.println("此单为删除单！");
            return Result.OK("已经删除");
        }
        TbQrd tbQrd = weiLianService.queryQrdByXidAndSfSc(tbXyd.getXid());
        if (tbQrd != null) {
            if (tbQrd.getQrsj() != null) {
                return Result.error("已经提交对账，不允许删单！");
            }
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userid", userid);
        paramMap.put("datasXid", datasXid);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/xydscAndhftask/scDdTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 下单别墅下拉
     */
    @RequestMapping(value = "queryVillaSelect")
    public Rest queryVillaSelect(HttpServletRequest request) {
        String userid = request.getParameter("userid");
        if (userid != null && !"".equals(userid)) {
            Integer count1 = weiLianService.queryBsUserIdSfJms(userid);
            if (count1 > 0) {
                List<Select> list = weiLianService.queryXdBsXlJms(userid);
                return Rest.success(list);
            }
        }

        List<Select> selects = weiLianService.queryXdBsXl();

        return Rest.success(selects);
    }

    /**
     * 下单团队性质和客户来源下拉
     */
    @RequestMapping(value = "queryKhLyAndXzByType/{type}")
    public Rest queryKhLyAndXzByType(@PathVariable("type") Integer type) {
        List<Select> list = weiLianService.queryXdLyOrXzXl(type);
        return Rest.success(list);
    }

    /**
     * 下单活动策划下拉
     */
    @RequestMapping(value = "queryXdHdCh")
    public Rest queryXdHdCh() {
        List<Select> list = weiLianService.queryXdHdCh();

        return Rest.success(list);
    }

    /**
     * 预留单
     */
    @RequestMapping("yldSave")
    public Result yldSave(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        if (datas == null || "".equals(datas)) {
            return Result.OK("数据不能为空！");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        WlgbYlxd wlgbYlxd = JSONObject.toJavaObject(jsonObject, WlgbYlxd.class);
        if (wlgbYlxd.getXjctime().getTime() >= wlgbYlxd.getXtctime().getTime()) {
            return Result.error("退场时间不能早于进场时间！");
        }
        Integer countWlgbYlxdSfZd = weiLianService.queryCountWlgbYlxdSfZd(wlgbYlxd);
        if (countWlgbYlxdSfZd > 0) {
            return Result.error("该门店此时间段撞单了！");
        }
        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(wlgbYlxd.getXbsmc());
        if (tbVilla == null) {
            return Result.error("没找到对应的别墅");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("formInstId", formInstId);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/yldSaveTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 预留单删除
     */
    @RequestMapping("ylSc")
    public Result ylSc(HttpServletRequest request) {
        String xid = request.getParameter("xid");
        if (xid == null || "".equals(xid)) {
            return Result.error("xid不能为空！");
        }
        String scrid = request.getParameter("scrid");
        if (scrid == null || "".equals(scrid)) {
            return Result.error("scrid不能为空！");
        }

        WlgbYlxd wlgbYlxd = wlgbYlxdService.queryByXid(xid);
        if (wlgbYlxd == null) {
            return Result.error("找不到预留单数据");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("xid", xid);
        paramMap.put("scrid", scrid);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/ylScTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }


    /**
     * 预留单查询
     */
    @RequestMapping("yldList/{type}")
    public Result yldList(HttpServletRequest request,
                          @PathVariable Integer type) {
        String xbsname = request.getParameter("xbsname");
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        PageHelp pageHelp = new PageHelp(Integer.valueOf((currentPage != null && !"".equals(currentPage) ? currentPage : "1")), Integer.valueOf((pageSize != null && !"".equals(pageSize) ? pageSize : "10")));
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        if (xbsname != null && !"".equals(xbsname) && xbsname.length() > 0) {
            map.put("xbsname", xbsname);
        }
        map.put("type", type == 1 ? 0 : 1);
        PageHelpUtil helpUtil = weiLianService.queryYldList(map);
        helpUtil = PageConfig.pageHelpUtil(helpUtil, pageHelp);
        return Result.OK(helpUtil);
    }

    /**
     * 对账员提交对账表单
     */
    @RequestMapping(value = "dzyTjQrd")
    public Result dzyTjQrd(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        //实例ID
        String userid = request.getParameter("datasName");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        TbQrd tbQrd = JSONObject.toJavaObject(jsonObject, TbQrd.class);
        if (tbQrd == null) {
            return Result.error("数据不能为空！");
        }
        //判断全部为-1直接截断

        if ((tbQrd.getQgsr() != null ? tbQrd.getQgsr() : -1) == -1 && (tbQrd.getQgzc() != null ? tbQrd.getQgzc() : -1) == -1 && (tbQrd.getQgsys() != null ? tbQrd.getQgsys() : -1) == -1) {
            return Result.OK();
        }
        TbXyd tbXyd = tbXydService.queryById(tbQrd.getQxydid());
        if (tbXyd == null) {
            return Result.error("找不到协议单！");
        }
        TbQrd tbQrd1 = tbQrdService.queryQxydIdAndQsfSc(tbQrd.getQxydid());
        if (tbQrd1 != null) {
            //只有本地数据库是1才不需要触发宜搭的修改事件,其他情况下都需要调用接口
            boolean bb = (tbQrd1.getSfdyjk() == 1);
            if (bb) {
                //修改本地数据库的sfdyjk值
                TbQrd tbQrd2 = new TbQrd();
                tbQrd2.setQid(tbQrd1.getQid());
                tbQrd2.setSfdyjk(0);
                //重新创建对象，以免多个数据修改
                tbQrdService.updateById(tbQrd2);
                return Result.OK();
            }
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userid", userid);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/dzyTjQrdTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 获取别墅全部城市下拉
     */
    @GetMapping(value = "queryCity")
    public Rest queryCity() {
        List<Select> list = weiLianService.queryVillaCity();
        return Rest.success(list);
    }

    /**
     * 跟单提交（下单及跟单与二次跟单）
     */
    @RequestMapping(value = "followUp/{status}")
    public Result followUp(@PathVariable("status") Integer status, HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        WlgbJdbBo wlgbJdb = JSONObject.toJavaObject(jsonObject, WlgbJdbBo.class);
        if (StringUtils.isBlank(wlgbJdb.getXid())) {
            return Result.error("没有xid！！！");
        }
        Integer[] arr = {0, 1};
        Boolean b = false;
        for (int i = 0; i < arr.length; i++) {
            if (status.equals(arr[i])) {
                b = true;
            }
        }
        if (!b) {
            return Result.error("status错误！");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("status", status.toString());
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/followUpTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }


    /**
     * 恢复订单效验
     */
    @RequestMapping(value = "hfDdXy")
    public Result hfDdXy(HttpServletRequest request) {
        String xid = request.getParameter("xid");
        TbXyd tbXyd = tbXydService.queryById(xid);
        if (tbXyd == null) {
            return Result.error("数据不存在！");
        }
        if ("0".equals(tbXyd.getXsfsc())) {
            return Result.error("不是删除的数据！");
        }
        if (tbXyd.getXjctime().getTime() >= tbXyd.getXtctime().getTime()) {
            return Result.error("退场时间不能早于进场时间！");
        }
        Integer countXydSfZd = weiLianService.queryCountXydSfZd(tbXyd);
        System.out.println(countXydSfZd);
        if (countXydSfZd > 0) {
            return Result.error("该门店此时间段撞单了！");
        }
        WlgbYlxd wlgbYlxd = new WlgbYlxd();
        wlgbYlxd.setXjctime(tbXyd.getXjctime());
        wlgbYlxd.setXtctime(tbXyd.getXtctime());
        wlgbYlxd.setXbsmc(tbXyd.getXbsmc());
        if (wlgbYlxd.getXjctime().getTime() >= wlgbYlxd.getXtctime().getTime()) {
            return Result.error("退场时间不能早于进场时间！");
        }
        Integer countXydSfZd1 = weiLianService.queryCountWlgbYlxdSfZd(wlgbYlxd);
        if (countXydSfZd1 > 0) {
            return Result.error("该门店此时间段撞单了！");
        }
        return Result.OK();
    }


    /**
     * 恢复订单
     */
    @RequestMapping(value = "hfDd")
    public Result hfDd(HttpServletRequest request) {
        String userid = request.getParameter("userid");
        String xid = request.getParameter("xid");
        TbXyd tbXyd = tbXydService.queryById(xid);
        if (tbXyd == null) {
            return Result.error("数据不存在！");
        }
        if ("0".equals(tbXyd.getXsfsc())) {
            return Result.error("不是删除的数据！");
        }
        if (tbXyd.getXjctime().getTime() >= tbXyd.getXtctime().getTime()) {
            return Result.error("退场时间不能早于进场时间！");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("jcsj", tbXyd.getXjctime());
        map.put("tcsj", tbXyd.getXtctime());
        map.put("vid", tbXyd.getXbsmc());
        Integer pdCc = weiLianService.queryPdCc(map);
        Double zk = 0.0;
        TbKdjZk kdjZk = tbKdjZkService.queryByCcAndSfSc(pdCc, "0");
        zk = kdjZk != null ? kdjZk.getZk() / 10 : 1;
        Double kdjZe = weiLianService.queryKdjZe(map);
        double v = kdjZe * zk;
        if (tbXyd.getXhfyj() < v) {
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            String text = "恢复订单失败了";
            text += "\n\n原因：场地费金额低于客单价";
            text += "\n您的场地费金额：" + tbXyd.getXhfyj();
            text += "\n客单价场地费金额：" + v;
            text += "\n订单编号：" + tbXyd.getXddbh();
            text += "\n客户姓名：" + tbXyd.getXzk();
            text += "\n送达时间：" + DateFormatConfig.df1(new Date());
            text += "\n\n请联系客单价专员进行处理！";
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            return Result.error("价格不达标");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("xid", xid);
        paramMap.put("userid", userid);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/xydscAndhftask/hfDdTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 恢复订单客单价校验
     */
    @RequestMapping(value = "HfDdKdjXy")
    public Result HfDdKdjXy(HttpServletRequest request) {
        String userid = request.getParameter("userid");
        String xid = request.getParameter("xid");
        TbXyd tbXyd = tbXydService.queryById(xid);
        if (tbXyd == null) {
            return Result.error("数据不存在！");
        }
        if ("0".equals(tbXyd.getXsfsc())) {
            return Result.error("不是删除的数据！");
        }
        if (tbXyd.getXjctime().getTime() >= tbXyd.getXtctime().getTime()) {
            return Result.error("退场时间不能早于进场时间！");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("jcsj", tbXyd.getXjctime());
        map.put("tcsj", tbXyd.getXtctime());
        map.put("vid", tbXyd.getXbsmc());
        Integer pdCc = weiLianService.queryPdCc(map);
        Double zk = 0.0;
        TbKdjZk kdjZk = tbKdjZkService.queryByCcAndSfSc(pdCc, "0");
        zk = kdjZk != null ? kdjZk.getZk() / 10 : 1;
        Double kdjZe = weiLianService.queryKdjZe(map);
        double v = kdjZe * zk;
        if (tbXyd.getXhfyj() < v) {
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            String text = "恢复订单失败了";
            text += "\n\n原因：场地费金额低于客单价";
            text += "\n您的场地费金额：" + tbXyd.getXhfyj();
            text += "\n客单价场地费金额：" + v;
            text += "\n订单编号：" + tbXyd.getXddbh();
            text += "\n客户姓名：" + tbXyd.getXzk();
            text += "\n送达时间：" + DateFormatConfig.df1(new Date());
            text += "\n\n请联系客单价专员进行处理！";
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            return Result.error("价格不达标");
        }


        return Result.OK();
    }

    @RequestMapping(value = "order")
    public Result order(HttpServletRequest request) {
        String status = request.getParameter("status");
        if (status == null) {
            return Result.error("status呢？？？");
        }
        String datas = request.getParameter("datas");
        String datasName = request.getParameter("datasName");
        String formInstId = request.getParameter("formInstId");

        JSONObject jsonObject = JSONObject.parseObject(datas);
        String xid = jsonObject.getString("xid");
        if (xid == null || "".equals(xid)) {
            return Result.error("没有xid！！！");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("datasName", datasName);
        paramMap.put("status", status);
        paramMap.put("formInstId", formInstId);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/orderTaSk", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }


    /**
     * 接单表-操作订单
     * 接单表 status 2:待入场（收款） 3:巡场服务 4:退场
     */
    @RequestMapping(value = "order2And4")
    public Result order2And4(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String datasName = request.getParameter("datasName");
        String formInstId = request.getParameter("formInstId");

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("status", "2");
        paramMap.put("datasName", datasName);
        paramMap.put("formInstId", formInstId);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/xyd/order", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        //调用阿里云函数计算的异步函数
        paramMap.put("status", "4");
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/xyd/order", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 客户确认单提交
     */
    @PostMapping(value = "qrdTj")
    public Result qrdTj(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String userid = jsonObject.getString("userid");
        userid = userid != null ? !"".equals(userid) ? userid : "012412221639786136545" : "012412221639786136545";
        log.info("*****参数***jsonObjectjsonObjectjsonObject**{}", jsonObject);
        TbQrd tbQrd = JSONObject.toJavaObject(jsonObject, TbQrd.class);
        log.info("*****参数***tbQrdtbQrdtbQrd**{}", tbQrd);
        if (tbQrd.getQxydid() == null || "".equals(tbQrd.getQxydid())) {
            tbQrd.setQxydid(jsonObject.getString("xid"));
        }
        if (tbQrd.getQxydid() == null || "".equals(tbQrd.getQxydid())) {
            return Result.error("没有协议单id！！！");
        }
        TbXyd xyd = tbXydService.queryById(tbQrd.getQxydid());
        if (xyd == null) {
            return Result.error("协议单不存在！");
        }

        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXlxid(xyd.getXid());
        wlgbXydLog.setXltime(new Date());
        wlgbXydLog.setXltext("客户确认单");
        wlgbXydLogService.save(wlgbXydLog);

        return Result.OK();
    }


    /**
     * 进场及消费对账
     */
    @RequestMapping(value = "jcJxfDz")
    public Result jcJxfDz(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String userid = jsonObject.getString("userid");
        userid = userid != null ? !"".equals(userid) ? userid : "012412221639786136545" : "012412221639786136545";
        TbQrd qrd = JSONObject.toJavaObject(jsonObject, TbQrd.class);
        if (qrd.getQxydid() == null || "".equals(qrd.getQxydid())) {
            qrd.setQxydid(jsonObject.getString("xid"));
        }
        TbXyd xyd = tbXydService.queryById(qrd.getQxydid());
        if (xyd == null) {
            return Result.error("找不到相应的数据！");
        }
        TbXyd tbXyd = new TbXyd();
        tbXyd.setStatus(10);
        tbXyd.setXstatu(1);
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        tbXyd.setXid(xyd.getXid());
        tbXydService.updateById(tbXyd);
        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXlxid(tbXyd.getXid());
        wlgbXydLog.setXltime(new Date());
        wlgbXydLog.setXltext("对账");
        wlgbXydLogService.save(wlgbXydLog);

        return Result.OK();
    }


    /**
     * 进场及消费转账(同步至对账表单)
     */
    @PostMapping(value = "zz")
    public Result jcJXfZz(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的!");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String userid = jsonObject.getString("userid");
        userid = userid != null ? !"".equals(userid) ? userid : "012412221639786136545" : "012412221639786136545";
        TbQrd qrd = JSONObject.toJavaObject(jsonObject, TbQrd.class);
        if (qrd.getQxydid() == null || "".equals(qrd.getQxydid())) {
            qrd.setQxydid(jsonObject.getString("xid"));
        }
        if (qrd.getQxydid() == null || "".equals(qrd.getQxydid())) {
            return Result.error("没有协议单id！！！");
        }
        TbXyd xyd1 = tbXydService.queryById(qrd.getQxydid());
        if (xyd1 == null) {
            return Result.error("协议单错误！");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userid", userid);
        paramMap.put("uuid", IdConfig.uuId());
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/jcJXfZzTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }


    /**
     * 上传电表
     */
    @RequestMapping(value = "dbSc")
    public Result dbSc(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        WlgbDzdbjl wlgbDzdbjl = JSONObject.toJavaObject(jsonObject, WlgbDzdbjl.class);
        wlgbDzdbjl.setId(IdConfig.uuId());
        wlgbDzdbjl.setTime(new Date());
        //电表图地址
        if (wlgbDzdbjl.getDbt() != null && !"".equals(wlgbDzdbjl.getDbt()) && wlgbDzdbjl.getDbt().length() > 0) {
            wlgbDzdbjl.setDbt(YdConfig.ydTpScJq(wlgbDzdbjl.getDbt()));
        }
        wlgbDzdbjlService.save(wlgbDzdbjl);

        return Result.OK();
    }


    /**
     * 协议单已删列表
     */
    @RequestMapping(value = "queryScXydList")
    public Result queryScXydList(HttpServletRequest request) {
        String search = request.getParameter("search");
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        PageHelp pageHelp = new PageHelp(Integer.valueOf((currentPage != null && !"".equals(currentPage) ? currentPage : "1")), Integer.valueOf((pageSize != null && !"".equals(pageSize) ? pageSize : "10")));
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        if (search != null && !"".equals(search) && search.length() > 0) {
            map.put("search", search);
        }
        String url = "http://weilianxsx1.wlgbpd.cn:8080/weilian3/";
        map.put("help", pageHelpUtil);
        map.put("url", url);
        PageHelpUtil pageHelpUtil1 = weiLianService.queryScXydList(map);
        List<TbXyd> list = pageHelpUtil1.getList();
        String finalUrl = url;
        list.forEach(l -> {
            Dzxyd dzxyd = dzxydService.queryByXid(l.getXid());
            if (dzxyd != null) {
                l.setXimagepath(dzxyd.getUrl());
            } else {
                l.setXimagepath(finalUrl + l.getXimagepath());
            }
        });

        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);
        return Result.OK(pageHelpUtil1);
    }


    /**
     * 确认单已删列表
     */
    @RequestMapping(value = "queryQrdScList")
    public Result queryQrdScList(HttpServletRequest request) {
        String search = request.getParameter("search");
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        PageHelp pageHelp = new PageHelp(Integer.valueOf((currentPage != null && !"".equals(currentPage) ? currentPage : "1")), Integer.valueOf((pageSize != null && !"".equals(pageSize) ? pageSize : "10")));
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        if (search != null && !"".equals(search) && search.length() > 0) {
            map.put("search", search);
        }
        map.put("help", pageHelpUtil);
        String url = "http://weilianxsx1.wlgbpd.cn:8080/weilian3/";
        PageHelpUtil pageHelpUtil1 = weiLianService.queryScQrdList(map);
        List<TbQrdVo> list = pageHelpUtil1.getList();
        String finalUrl = url;
        list.forEach(l -> {
            TbVilla villa = weiLianDdXcxService.queryTbVillaById(l.getXbsmc());
            if (villa != null) {
                l.setXbsmc(villa.getVname());
            }
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(l.getXscz());
            if (employee != null) {
                l.setXscz(employee.getName());
            }
            Dzxyd dzxyd = dzxydService.queryByXid(l.getXid());
            if (dzxyd != null) {
                l.setXimagepath(dzxyd.getUrl());
            } else {
                l.setXimagepath(finalUrl + l.getXimagepath());
            }
        });
        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);


        return Result.OK(pageHelpUtil1);
    }


    /**
     * 修改删除订单
     */
    @RequestMapping(value = "xgScDd")
    public Result xgScDd(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("datasName");
        userid = userid.replace(" ", "");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        TbXyd xyd = JSONObject.toJavaObject(jsonObject, TbXyd.class);
        if (xyd == null) {
            return Result.error("数据不能为空！");
        }


        TbXyd tbXyd = tbXydService.queryById(xyd.getXid());
        if (tbXyd == null) {
            return Result.error("找不到对于的订单");
        }


        return Result.OK();
    }


    /**
     * 发布带看
     */
    @RequestMapping(value = "dkXz")
    public Result dkXz(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        JSONObject object = JSONObject.parseObject(datas);
        TbDk tbDk = JSON.toJavaObject(object, TbDk.class);
        if (tbDk == null) {
            return Result.error("空数据！");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("formInstId", formInstId);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/dkXzTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 带看抢单
     */
    @RequestMapping(value = "dkQd")
    public Result dkQd(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        String userid = request.getParameter("userid");
        TbDk tbDk = weiLianService.queryDkXq(ddbh);
        if (tbDk == null) {
            return Result.error("找不到数据！");
        }
        if (tbDk.getSfjd() != null && !"".equals(tbDk.getSfjd()) && "0".equals(tbDk.getSfjd())) {
            return Result.error("手慢了，该订单已经被抢啦~");
        }
        TbDk tbDk1 = new TbDk();
        tbDk1.setQduser(userid);
        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        tbDk1.setQdusername(employee != null ? employee.getName() : null);
        tbDk1.setId(tbDk.getId());
        tbDk1.setQdtime(new Date());
        tbDk1.setSfjd("0");
        tbDkService.updateById(tbDk1);
        WlgbCustomerLog wlgbCustomerLog = new WlgbCustomerLog();
        wlgbCustomerLog.setCltime(new Date());
        wlgbCustomerLog.setClid(IdConfig.uuId());
        wlgbCustomerLog.setClcid(tbDk.getDkddbh());
        wlgbCustomerLog.setCltext("抢单");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        wlgbCustomerLog.setCluserid(ding != null ? ding.getUserid() : null);
        wlgbCustomerLog.setClname(ding != null ? ding.getName() : null);
        wlgbCustomerLogService.save(wlgbCustomerLog);
        Map<String, Object> map = new HashMap<>();
        map.put("ecgdzxr", userid != null && !"".equals(userid) ? userid : null);
        map.put("dkddbh", tbDk.getDkddbh() != null && !"".equals(tbDk.getDkddbh()) ? tbDk.getDkddbh() : null);
        map.put("dkzt", tbDk.getDkzt() != null && !"".equals(tbDk.getDkzt()) ? tbDk.getDkzt() : null);
        map.put("qduser", userid != null && !"".equals(userid) ? userid : null);
        map.put("city", tbDk.getCity() != null && !"".equals(tbDk.getCity()) ? tbDk.getCity() : null);
        map.put("dkbsmc", tbDk.getDkbsmc() != null && !"".equals(tbDk.getDkbsmc()) ? tbDk.getDkbsmc() : null);
        map.put("dktime", tbDk.getDktime());
        map.put("khname", tbDk.getKhname() != null && !"".equals(tbDk.getKhname()) ? tbDk.getKhname() : null);
        map.put("khtel", tbDk.getKhtel() != null && !"".equals(tbDk.getKhtel()) ? tbDk.getKhtel() : null);
        map.put("dknum", tbDk.getDknum() != null && !"".equals(tbDk.getDknum()) ? tbDk.getDknum() : null);
        map.put("zdj", tbDk.getZdj() != null && !"".equals(tbDk.getZdj()) ? tbDk.getZdj() : null);
        map.put("zgj", tbDk.getZgj() != null && !"".equals(tbDk.getZgj()) ? tbDk.getZgj() : null);
        map.put("bz", tbDk.getBz() != null && !"".equals(tbDk.getBz()) ? tbDk.getBz() : null);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid("带看流程", tbDk.getId().toString());
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("带看应用");
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("带看流程-新");

        if (dksljl != null) {
            TbXyd tbXyd = new TbXyd();
            tbXyd.setXddbh(tbDk.getDkddbh());
            tbXyd.setXfd(tbDk.getFbusername());
            //修改流程容错
//            queryYdLcXgRc(tbXyd, dksljl.getBz(), map, dksljl.getSlid(), userid != null ? userid : "012412221639786136545", dingkey);
            PtLcCL.gxLcSl(userid != null ? userid : "012412221639786136545", map, ydAppkey, dksljl.getSlid(), token);
        } else {
            TbXyd tbXyd = new TbXyd();
            tbXyd.setXddbh(tbDk.getDkddbh());
            tbXyd.setXfd(tbDk.getFbusername());
            GatewayResult gatewayResult = PtLcCL.lcid(tbDk.getFbuser() != null ? tbDk.getFbuser() : "012412221639786136545", map, ydAppkey, ydBd, token);
//            GatewayResult gatewayResult = queryYdLcXzRc(tbXyd, "带看流程", map, "带看流程", tbDk.getFbuser() != null ? tbDk.getFbuser() : "012412221639786136545", dingkey);
            WlgbDksljl wlgbDksljl = new WlgbDksljl();
            wlgbDksljl.setId(IdConfig.uuId());
            wlgbDksljl.setTjsj(new Date());
            if (gatewayResult != null) {
                wlgbDksljl.setSlid(gatewayResult.getResult());
            }
            wlgbDksljl.setBz("带看流程");
            wlgbDksljl.setDdid(tbDk.getId().toString());
            wlgbDksljl.setJsr(userid);
            DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbDk.getFbuser());
            if (employee1 != null) {
                wlgbDksljl.setTjrid(employee1.getUserid());
                wlgbDksljl.setTjr(employee1.getName());
            }
            wlgbDksljlService.save(wlgbDksljl);
        }
        WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(tbDk.getDkddbh(), "带看表单提交");
        if (wlgbBdjl != null) {
            JSONObject map1 = new JSONObject();
            map1.put("sfjd", "0");
            map1.put("qduser", userid);
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, wlgbBdjl.getSlid(), map1.toJSONString());
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                e.printStackTrace();
            }
            System.out.println(gatewayResult);
        }
        Map<String, Object> map3 = new HashMap<>();
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbDk.getDkbsid());
        map3.put("jid", villa.getJid());
        QyUtil qyUtil = weiLianService.queryQyUtil(map3);
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String context = "客户姓名：" + tbDk.getKhname() + "\n\n踩点区域：" + (qyUtil != null ? qyUtil.getJname() : null) + "-" + tbDk.getDkbsmc() + "\n\n踩点时间：" + df2.format(tbDk.getDktime()) + "\n\n抢单人：" + (employee != null ? employee.getName() : "") + "\n\n请前往钉钉“待办”提交带看结果";

        Map<String, Object> map1 = new HashMap<>();
        String s = tbDk.getCity() != null && !"".equals(tbDk.getCity()) ? tbDk.getCity() : null;
        map1.put("city", s);
        List<DkBbJqr> list = weiLianService.queryDkBb(map1);

        list.forEach(l -> DingDingUtil.sendMsg(l != null ? l.getWebhook() : null, l != null ? l.getSecret() : null, context, null, false));


        return Result.OK();
    }

    /**
     * 带看抢单
     */
    @RequestMapping(value = "dkQd1")
    public Result dkQd1(@RequestBody(required = false) String param) {
        JSONObject jsonObject = JSONObject.parseObject(param);
        String ddbh = jsonObject.getString("ddbh");
        String userid = jsonObject.getString("userid");
        TbDk tbDk = weiLianService.queryDkXq(ddbh);
        if (tbDk == null) {
            return Result.error("找不到数据！");
        }
        if (tbDk.getSfjd() != null && !"".equals(tbDk.getSfjd()) && "0".equals(tbDk.getSfjd())) {
            return Result.error("手慢了，该订单已经被抢啦~");
        }
        TbDk tbDk1 = new TbDk();
        tbDk1.setQduser(userid);
        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        tbDk1.setQdusername(employee != null ? employee.getName() : null);
        tbDk1.setId(tbDk.getId());
        tbDk1.setQdtime(new Date());
        tbDk1.setSfjd("0");
        tbDkService.updateById(tbDk1);
        WlgbCustomerLog wlgbCustomerLog = new WlgbCustomerLog();
        wlgbCustomerLog.setCltime(new Date());
        wlgbCustomerLog.setClid(IdConfig.uuId());
        wlgbCustomerLog.setClcid(tbDk.getDkddbh());
        wlgbCustomerLog.setCltext("抢单");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        wlgbCustomerLog.setCluserid(ding != null ? ding.getUserid() : null);
        wlgbCustomerLog.setClname(ding != null ? ding.getName() : null);
        wlgbCustomerLogService.save(wlgbCustomerLog);
        Map<String, Object> map = new HashMap<>();
        map.put("ecgdzxr", userid != null && !"".equals(userid) ? userid : null);
        map.put("dkddbh", tbDk.getDkddbh() != null && !"".equals(tbDk.getDkddbh()) ? tbDk.getDkddbh() : null);
        map.put("dkzt", tbDk.getDkzt() != null && !"".equals(tbDk.getDkzt()) ? tbDk.getDkzt() : null);
        map.put("qduser", userid != null && !"".equals(userid) ? userid : null);
        map.put("city", tbDk.getCity() != null && !"".equals(tbDk.getCity()) ? tbDk.getCity() : null);
        map.put("dkbsmc", tbDk.getDkbsmc() != null && !"".equals(tbDk.getDkbsmc()) ? tbDk.getDkbsmc() : null);
        map.put("dktime", tbDk.getDktime());
        map.put("khname", tbDk.getKhname() != null && !"".equals(tbDk.getKhname()) ? tbDk.getKhname() : null);
        map.put("khtel", tbDk.getKhtel() != null && !"".equals(tbDk.getKhtel()) ? tbDk.getKhtel() : null);
        map.put("dknum", tbDk.getDknum() != null && !"".equals(tbDk.getDknum()) ? tbDk.getDknum() : null);
        map.put("zdj", tbDk.getZdj() != null && !"".equals(tbDk.getZdj()) ? tbDk.getZdj() : null);
        map.put("zgj", tbDk.getZgj() != null && !"".equals(tbDk.getZgj()) ? tbDk.getZgj() : null);
        map.put("bz", tbDk.getBz() != null && !"".equals(tbDk.getBz()) ? tbDk.getBz() : null);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid("带看流程", tbDk.getId().toString());


        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("带看应用");
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("带看流程-新");

        if (dksljl != null) {
            TbXyd tbXyd = new TbXyd();
            tbXyd.setXddbh(tbDk.getDkddbh());
            tbXyd.setXfd(tbDk.getFbusername());
            //修改流程容错
//            queryYdLcXgRc(tbXyd, dksljl.getBz(), map, dksljl.getSlid(), userid != null ? userid : "012412221639786136545", dingkey);
            PtLcCL.gxLcSl(userid != null ? userid : "012412221639786136545", map, ydAppkey, dksljl.getSlid(), token);
        } else {
            TbXyd tbXyd = new TbXyd();
            tbXyd.setXddbh(tbDk.getDkddbh());
            tbXyd.setXfd(tbDk.getFbusername());
            GatewayResult gatewayResult = PtLcCL.lcid(tbDk.getFbuser() != null ? tbDk.getFbuser() : "012412221639786136545", map, ydAppkey, ydBd, token);
//            GatewayResult gatewayResult = queryYdLcXzRc(tbXyd, "带看流程", map, "带看流程", tbDk.getFbuser() != null ? tbDk.getFbuser() : "012412221639786136545", dingkey);
            WlgbDksljl wlgbDksljl = new WlgbDksljl();
            wlgbDksljl.setId(IdConfig.uuId());
            wlgbDksljl.setTjsj(new Date());
            if (gatewayResult != null) {
                wlgbDksljl.setSlid(gatewayResult.getResult());
            }
            wlgbDksljl.setBz("带看流程");
            wlgbDksljl.setDdid(tbDk.getId().toString());
            wlgbDksljl.setJsr(userid);
            DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbDk.getFbuser());
            if (employee1 != null) {
                wlgbDksljl.setTjrid(employee1.getUserid());
                wlgbDksljl.setTjr(employee1.getName());
            }
            wlgbDksljlService.save(wlgbDksljl);
        }
        WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(tbDk.getDkddbh(), "带看表单提交");
        if (wlgbBdjl != null) {
            JSONObject map1 = new JSONObject();
            map1.put("sfjd", "0");
            map1.put("qduser", userid);
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, wlgbBdjl.getSlid(), map1.toJSONString());
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                e.printStackTrace();
            }
            System.out.println(gatewayResult);
        }
        Map<String, Object> map3 = new HashMap<>();
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbDk.getDkbsid());
        map3.put("jid", villa.getJid());
        QyUtil qyUtil = weiLianService.queryQyUtil(map3);
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String context = "客户姓名：" + tbDk.getKhname() + "\n\n踩点区域：" + (qyUtil != null ? qyUtil.getJname() : null) + "-" + tbDk.getDkbsmc() + "\n\n踩点时间：" + df2.format(tbDk.getDktime()) + "\n\n抢单人：" + (employee != null ? employee.getName() : "") + "\n\n请前往钉钉“待办”提交带看结果";

        Map<String, Object> map1 = new HashMap<>();
        String s = tbDk.getCity() != null && !"".equals(tbDk.getCity()) ? tbDk.getCity() : null;
        map1.put("city", s);
        List<DkBbJqr> list = weiLianService.queryDkBb(map1);

        list.forEach(l -> DingDingUtil.sendMsg(l != null ? l.getWebhook() : null, l != null ? l.getSecret() : null, context, null, false));


        return Result.OK();
    }

    /**
     * 带看流程提交带看结果
     */
    @RequestMapping(value = "tjDkLc")
    public Result tjDkLc(HttpServletRequest request) {
        System.out.println("提交了");
        String datas = request.getParameter("datas");
        JSONObject object = JSONObject.parseObject(datas);
        TbDk tbDk = JSON.toJavaObject(object, TbDk.class);
        if (tbDk == null) {
            return Result.error("数据是空的！");
        }
        TbDk tbDk1 = weiLianService.queryDkXq(tbDk.getDkddbh());
        if (tbDk1 == null) {
            return Result.error("数据不存在！");
        }
        if (tbDk.getDksfcg() == null || "".equals(tbDk.getDksfcg())) {
            return Result.error("没有带看结果！");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/tjDkLcTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }


        return Result.OK();
    }


    /**
     * 删除带看
     */
    @RequestMapping(value = "scDk")
    public Result scDk(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        String userid = request.getParameter("userid");
        TbDk tbDk = weiLianService.queryDkXq(ddbh);
        if (tbDk == null) {
            return Result.error("该带看已经删除或不存在!");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("ddbh", ddbh);
        paramMap.put("userid", userid);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/scDkTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 修改带看
     */
    @RequestMapping(value = "xgDk")
    public Result xgDk(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("datasName");
        if (userid != null && !"".equals(userid)) {
            userid = userid.replace(" ", "");
        }
        JSONObject object = JSONObject.parseObject(datas);
        TbDk tbDk = JSON.toJavaObject(object, TbDk.class);
        if (tbDk == null) {
            return Result.error("数据空的！");
        }
        TbDk tbDk1 = weiLianService.queryDkXq(tbDk.getDkddbh());
        if (tbDk1 == null) {
            return Result.error("数据找不到！");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userid", userid);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/xgDkTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }


    /**
     * 根据别墅id获取别墅值班店长及政委
     */
    @RequestMapping(value = "queryZwAndZbdz")
    public Result queryZwAndZbdz(HttpServletRequest request) {
        String vid = request.getParameter("q");
        ZwAndDz queryZwAndDz = null;
        System.out.println("vid:" + vid);
        if (vid != null && !"".equals(vid) && vid.length() > 0 && !"undefined".equals(vid)) {
            queryZwAndDz = weiLianService.queryZwAndDz(vid);
            TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(vid);
            if (tbVilla != null) {
                if ("长沙".equals(tbVilla.getCity())) {
                    queryZwAndDz.setCity(tbVilla.getCszq() != null ? tbVilla.getCszq() : "长沙河西");
                }
                if ("成都".equals(tbVilla.getCity())) {
                    queryZwAndDz.setCity(tbVilla.getCszq() != null ? tbVilla.getCszq() : "成都温江");
                }
            }
        }

        return Result.OK(queryZwAndDz);
    }

    /**
     * 带看别墅下拉
     */
    @RequestMapping(value = "queryBsZw")
    public Rest queryBsZw(HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        //宜搭默认参数名
        String q = request.getParameter("q");
        if (q != null && !"".equals(q) && q.length() > 0) {
            map.put("search", q);
        }
        List<BsZw> list = weiLianService.queryBsMcIdZw(map);

        return Rest.success(list);
    }

    /**
     * 一线下单群业绩播报
     */
    @RequestMapping(value = "xhsAndDyYjBbJqr")
    public Result xhsAndDyYjBbJqr(@RequestBody(required = false) JSONObject json) {
        if (json == null) {
            return Result.error("空的");
        }
        String text = json.getJSONObject("text").getString("content");
        text = text.replaceAll(" ", "");

        String content = "";
        Map<String, Object> map1 = new HashMap<>();
        List<String> list1 = new ArrayList<>();
        if ("小红书个人下单业绩".equals(text)) {
            map1.put("type", 1);
            map1.put("grOrBm", 2);
            list1.add("80ca5a42c2774390bcc314e02c6a034d");
            list1.add("a33c818bda9d4793b07fbc92e7a55f2e");
            map1.put("ly", list1);
            List<JSONObject> list = weiLianService.queryXhsAndDyXdYj(map1);
            content = "本月个人小红书/抖音下单业绩播报\n\n";
            if (list.size() > 0) {
                content += "姓名\t\t部门\t\t\t业绩\n";
                for (JSONObject l : list) {
                    String xfd = l.getString("xfd");
                    content += xfd + (xfd.length() > 2 ? "\t" : "\t\t") + l.getString("bmmc") + "\t\t" + l.getDouble("zje") + "\n";
                }
            } else {
                content += "下单量：0\t\t总业绩：0.0";
            }
        } else if ("短租个人下单业绩".equals(text)) {
            map1.put("type", 1);
            map1.put("grOrBm", 2);
            list1.add("30197965afd34fb687ab7fe0ed70fa7c");
            map1.put("ly", list1);
            List<JSONObject> list = weiLianService.queryXhsAndDyXdYj(map1);
            content = "本月个人短租下单业绩播报\n\n";
            if (list.size() > 0) {
                content += "姓名\t\t部门\t\t\t业绩\n";
                for (JSONObject l : list) {
                    String xfd = l.getString("xfd");
                    content += xfd + (xfd.length() > 2 ? "\t" : "\t\t") + l.getString("bmmc") + "\t\t" + l.getDouble("zje") + "\n";
                }
            } else {
                content += "下单量：0\t\t总业绩：0.0";
            }
        } else if ("小红书个人消费业绩".equals(text)) {
            map1.put("grOrBm", 2);
            list1.add("80ca5a42c2774390bcc314e02c6a034d");
            list1.add("a33c818bda9d4793b07fbc92e7a55f2e");
            map1.put("ly", list1);
            List<JSONObject> list = weiLianService.queryXhsAndDyXFYj(map1);
            content = "本月个人小红书/抖音消费业绩播报\n\n";
            if (list.size() > 0) {
                content += "姓名\t\t部门\t\t\t业绩\n";
                for (JSONObject l : list) {
                    String xfd = l.getString("xfd");
                    content += xfd + (xfd.length() > 2 ? "\t" : "\t\t") + l.getString("bmmc") + "\t\t" + l.getDouble("zje") + "\n";
                }
            } else {
                content += "无";
            }
        } else if ("短租个人消费业绩".equals(text)) {
            map1.put("grOrBm", 2);
            list1.add("30197965afd34fb687ab7fe0ed70fa7c");
            map1.put("ly", list1);
            List<JSONObject> list = weiLianService.queryXhsAndDyXFYj(map1);
            content = "本月个人短租消费业绩播报\n\n";
            if (list.size() > 0) {
                content += "姓名\t\t部门\t\t\t业绩\n";
                for (JSONObject l : list) {
                    String xfd = l.getString("xfd");
                    content += xfd + (xfd.length() > 2 ? "\t" : "\t\t") + l.getString("bmmc") + "\t\t" + l.getDouble("zje") + "\n";
                }
            } else {
                content += "无";
            }
        } else if ("小红书区域下单业绩".equals(text)) {
            map1.put("type", 1);
            map1.put("grOrBm", 1);
            list1.add("80ca5a42c2774390bcc314e02c6a034d");
            list1.add("a33c818bda9d4793b07fbc92e7a55f2e");
            map1.put("ly", list1);
            List<JSONObject> list = weiLianService.queryXhsAndDyXdYj(map1);
            content = "本月区域小红书/抖音下单业绩播报\n\n";
            if (list.size() > 0) {
                content += "部门\t\t\t业绩\n";
                for (JSONObject l : list) {
                    content += l.getString("bmmc") + "\t\t" + l.getDouble("zje") + "\n";
                }
            } else {
                content += "下单量：0\t\t总业绩：0.0";
            }
        } else if ("短租区域下单业绩".equals(text)) {
            map1.put("type", 1);
            map1.put("grOrBm", 1);
            list1.add("30197965afd34fb687ab7fe0ed70fa7c");
            map1.put("ly", list1);
            List<JSONObject> list = weiLianService.queryXhsAndDyXdYj(map1);
            content = "本月区域短租下单业绩播报\n\n";
            if (list.size() > 0) {
                content += "部门\t\t\t业绩\n";
                for (JSONObject l : list) {
                    content += l.getString("bmmc") + "\t\t" + l.getDouble("zje") + "\n";
                }
            } else {
                content += "下单量：0\t\t总业绩：0.0";
            }
        } else if ("小红书区域消费业绩".equals(text)) {
            map1.put("grOrBm", 1);
            list1.add("80ca5a42c2774390bcc314e02c6a034d");
            list1.add("a33c818bda9d4793b07fbc92e7a55f2e");
            map1.put("ly", list1);
            List<JSONObject> list = weiLianService.queryXhsAndDyXFYj(map1);
            content = "本月区域小红书/抖音消费业绩播报\n\n";
            if (list.size() > 0) {
                content += "部门\t\t\t业绩\n";
                for (JSONObject l : list) {
                    content += l.getString("bmmc") + "\t\t" + l.getDouble("zje") + "\n";
                }
            } else {
                content += "无";
            }
        } else if ("短租区域消费业绩".equals(text)) {
            map1.put("grOrBm", 1);
            list1.add("30197965afd34fb687ab7fe0ed70fa7c");
            map1.put("ly", list1);
            List<JSONObject> list = weiLianService.queryXhsAndDyXFYj(map1);
            content = "本月区域短租消费业绩播报\n\n";
            if (list.size() > 0) {
                content += "部门\t\t\t业绩\n";
                for (JSONObject l : list) {
                    content += l.getString("bmmc") + "\t\t" + l.getDouble("zje") + "\n";
                }
            } else {
                content += "无";
            }
        } else {
            content = "请发送“小红书个人下单业绩”,“小红书个人消费业绩”,“小红书区域下单业绩”,“小红书区域消费业绩”，“短租个人下单业绩”,“短租个人消费业绩”,“短租区域下单业绩”,“短租区域消费业绩”关键词即可获取！";
        }
        Map<String, Object> map = new HashMap<>();
        map.put("city", "小红书/抖音");
        List<DkBbJqr> jqrList = weiLianService.queryDkBb(map);
        DkBbJqr dkBbJqr = jqrList != null ? jqrList.size() > 0 ? jqrList.get(0) : null : null;
        DingDingUtil.sendMsg(dkBbJqr != null ? dkBbJqr.getWebhook() : null, dkBbJqr != null ? dkBbJqr.getSecret() : null, content, null, false);

        return Result.OK();
    }


    /**
     * 铁军回访列表
     */
    @RequestMapping(value = "queryNotKfList")
    public Result queryNotKfList(HttpServletRequest request) {
        String search = request.getParameter("search");
        String isKhsfwc = request.getParameter("isKhsfwc");
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        PageHelp pageHelp = new PageHelp(Integer.valueOf((currentPage != null && !"".equals(currentPage) ? currentPage : "1")), Integer.valueOf((pageSize != null && !"".equals(pageSize) ? pageSize : "10")));
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        if (search != null && !"".equals(search) && search.length() > 0) {
            map.put("search", search);
        }
        if (isKhsfwc != null && !"".equals(isKhsfwc) && isKhsfwc.length() > 0) {
            map.put("isKhsfwc", isKhsfwc);
        }
        map.put("help", pageHelpUtil);
        PageHelpUtil pageHelpUtil1 = weiLianDaiBanService.queryNotKfList(map);

        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);


        return Result.OK(pageHelpUtil1);
    }

    /**
     * 业务经理回访列表
     */
    @RequestMapping(value = "queryNotKfListYwjl")
    public Result queryNotKfListYwjl(HttpServletRequest request) {
        String search = request.getParameter("search");
        String isKhsfwc = request.getParameter("isKhsfwc");
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        String name = request.getParameter("name");
        String city = request.getParameter("city");
        PageHelp pageHelp = new PageHelp(Integer.valueOf((currentPage != null && !"".equals(currentPage) ? currentPage : "1")), Integer.valueOf((pageSize != null && !"".equals(pageSize) ? pageSize : "10")));
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        if (search != null && !"".equals(search) && search.length() > 0) {
            map.put("search", search);
        }
        if (isKhsfwc != null && !"".equals(isKhsfwc) && isKhsfwc.length() > 0) {
            map.put("isKhsfwc", isKhsfwc);
        }
        if (name != null && !"".equals(name) && name.length() > 0) {
            map.put("name", name);
        }
        if (city != null && !"".equals(city) && city.length() > 0) {
            List<String> list = Arrays.asList(city.split(","));
            if (list.size() > 0) {
                map.put("list", list);
            }
        }
        map.put("help", pageHelpUtil);
        PageHelpUtil pageHelpUtil1 = weiLianDaiBanService.queryNotKfListYwjl(map);

        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);


        return Result.OK(pageHelpUtil1);
    }

    /**
     * 铁军回访提交
     */
    @RequestMapping(value = "tjKhHf")
    public Result tjKhHf(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userId = request.getParameter("userId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String xddbh = jsonObject.getString("xddbh");
        TbXyd tbXyd = tbXydService.queryByDdBh(xddbh);
        if (tbXyd != null) {
            WlgbDdhf wlgbDdhf = wlgbDdhfService.queryByXidAndSfSc(tbXyd.getXid());
            if (wlgbDdhf != null) {
                WlgbDdhf wlgbDdhf1 = new WlgbDdhf();
                wlgbDdhf1.setId(wlgbDdhf.getId());
                wlgbDdhf1.setKhsfhf(1);
                wlgbDdhf1.setKhhfsj(new Date());
                wlgbDdhfService.updateById(wlgbDdhf1);
                YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
                String form = "FORM-N4A66IB1QG1SX7LSYZCQX2AVK8GB2YEB2CERKM4";
                Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
                String token = null;
                try {
                    token = DingToken.token(dingkey);
                } catch (ApiException e) {
                    e.printStackTrace();
                }
                GatewayResult gatewayResult = null;
                try {
                    gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObject.toJSONString(), userId != null && !"".equals(userId) ? userId : "012412221639786136545", form);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //修改记录里面的回访状态，以便查看
                if (gatewayResult.getSuccess()) {
                    String sftss = jsonObject.getString("sftss");
                    if ("是".equals(sftss)) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("xid", tbXyd.getXid());
                        map.put("zbdz", jsonObject.getString("dzs"));
                        map.put("time", jsonObject.getDate("hfsjs") != null ? jsonObject.getDate("hfsjs") : new Date());
                        weiLianDdXcxService.insertCpJl(map);
                    }
                    weiLianDaiBanService.updateHfZt(tbXyd.getXid());
                    return Result.OK();
                }
            }
        }
        return Result.error("回访提交失败了");
    }

    /**
     * 根据订单编号获取订单消费信息
     */
    @RequestMapping(value = "queryDdByDdBh")
    public Result queryDdByDdBh(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        TbXyd tbXyd = tbXydService.queryByDdBh(ddbh);
        if (tbXyd == null) {
            return Result.error("找不到订单！");
        }
        TbQrd tbQrd = tbQrdService.queryQxydIdAndQsfSc(tbXyd.getXid());
        if (tbQrd == null) {
            return Result.error("数据不对！");
        }
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        Map<String, Object> map = new HashMap<>();
        //城市
        String city = villa.getCszq() != null ? !"".equals(villa.getCszq()) ? villa.getCszq() : villa.getCity() : villa.getCity();
        map.put("quyu", city);
        map.put("bsmc", villa.getVname());
        map.put("xjctime", tbXyd.getXjctime());
        map.put("xtctime", tbXyd.getXtctime());

        map.put("xfd", tbXyd.getXfd());
        if (tbQrd != null) {
            map.put("zbdz", tbQrd.getQzbdz());
            Double sum = (tbQrd.getQsjcdf() != null ? tbQrd.getQsjcdf() : 0) + (tbQrd.getQdf() != null ? tbQrd.getQdf() : 0) + (tbQrd.getQspxf() != null ? tbQrd.getQspxf() : 0) + (tbQrd.getQdcxf() != null ? tbQrd.getQdcxf() : 0) + (tbQrd.getQsktc() != null ? tbQrd.getQsktc() : 0) + (tbQrd.getQccrsfy() != null ? tbQrd.getQccrsfy() : 0) + (tbQrd.getQcfsyf() != null ? tbQrd.getQcfsyf() : 0) + (tbQrd.getQwsf() != null ? tbQrd.getQwsf() : 0) + (tbQrd.getQbcf() != null ? tbQrd.getQbcf() : 0) + (tbXyd.getXbxzje() != null ? tbXyd.getXbxzje() : 0) + (tbXyd.getXhpsfy() != null ? tbXyd.getXhpsfy() : 0) + (tbQrd.getQchfw() != null ? tbQrd.getQchfw() : 0) + (tbXyd.getXjbszje() != null ? tbXyd.getXjbszje() : 0) - (tbQrd.getQpjkz() != null ? tbQrd.getQpjkz() : 0) - (tbQrd.getQkhkz() != null ? tbQrd.getQkhkz() : 0) + (tbQrd.getQdgsr() != null ? tbQrd.getQdgsr() : 0) + (tbQrd.getQcdzs() != null ? tbQrd.getQcdzs() : 0);
            map.put("chfw", (tbQrd.getQchfw() != null ? tbQrd.getQchfw() : 0));
            map.put("cfsyf", tbQrd.getQcfsyf() != null ? tbQrd.getQcfsyf() : 0);
            map.put("spxf", tbQrd.getQspxf() != null ? tbQrd.getQspxf() : 0);
            map.put("df", tbQrd.getQdf() != null ? tbQrd.getQdf() : 0);
            map.put("dcxf", (tbQrd.getQdcxf() != null ? tbQrd.getQdcxf() : 0) + (tbQrd.getQsktc() != null ? tbQrd.getQsktc() : 0));
            map.put("wsf", (tbQrd.getQwsf() != null ? tbQrd.getQwsf() : 0) + (tbQrd.getQbcf() != null ? tbQrd.getQbcf() : 0) + (tbQrd.getQysf() != null ? tbQrd.getQysf() : 0));
            map.put("sum", sum);
        }
        map.put("sfxccj", tbXyd.getXisxzcj() != null ? tbXyd.getXisxzcj() == 0 ? "是" : "否" : "否");
        map.put("xccjr", tbXyd.getXisxzcj() != null ? tbXyd.getXisxzcj() == 0 ? tbXyd.getXcjr() : "" : "");
        map.put("xzk", tbXyd.getXzk());
        map.put("xzkdh", tbXyd.getXzkdh());

        map.put("hpsfy", (tbXyd.getXhpsfy() != null ? tbXyd.getXhpsfy() : 0) + (tbXyd.getXzrcsfy() != null ? tbXyd.getXzrcsfy() : 0));
        map.put("bxje", (tbXyd.getXbxzje() != null ? tbXyd.getXbxzje() : 0));
        map.put("jbsje", (tbXyd.getXjbszje() != null ? tbXyd.getXjbszje() : 0));
        map.put("hpsxm", tbXyd.getXhpsfy() != null ? tbXyd.getXhpsxm() != null ? tbXyd.getXhpsxm() : "" : "");

        return Result.OK(map);
    }

    /**
     * 业务经理回访提交
     */
    @RequestMapping(value = "tjYwJlFh")
    public Result tjYwJlFh(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userId = request.getParameter("userId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String xddbh = jsonObject.getString("ddbh");
        WlgbYwjlhf lhf = wlgbYwjlhfService.queryByDdbh(xddbh);
        if (lhf != null) {
            wlgbYwjlhfService.removeById(lhf.getId());
        }
        TbXyd tbXyd = tbXydService.queryByDdBh(xddbh);
        if (tbXyd != null) {
            WlgbDdhf wlgbDdhf = wlgbDdhfService.queryByXidAndSfSc(tbXyd.getXid());
            if (wlgbDdhf != null) {
                WlgbDdhf wlgbDdhf1 = new WlgbDdhf();
                wlgbDdhf1.setId(wlgbDdhf.getId());
                wlgbDdhf1.setKhsfhf(1);
                wlgbDdhf1.setKhhfsj(new Date());
                wlgbDdhfService.updateById(wlgbDdhf1);
                weiLianDaiBanService.updateHfZt(tbXyd.getXid());
                WlgbYwjlhf wlgbYwjlhf = new WlgbYwjlhf();
                wlgbYwjlhf.setXid(tbXyd.getXid());
                DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);
                wlgbYwjlhf.setTjrid(employee != null ? employee.getUserid() : null);
                wlgbYwjlhf.setTjr(employee != null ? employee.getName() : null);
                wlgbYwjlhf.setDdbh(tbXyd.getXddbh());
                wlgbYwjlhf.setTime(new Date());
                wlgbYwjlhf.setTcsj(tbXyd.getXtctime());
                wlgbYwjlhf.setJcsj(tbXyd.getXjctime());
                if (lhf == null) {
                    wlgbYwjlhf.setHfsj(jsonObject.getDate("hfsj"));
                } else {
                    wlgbYwjlhf.setHfsj(lhf.getHfsj());
                }
                wlgbYwjlhf.setSfyc(jsonObject.getString("sfyc"));
                wlgbYwjlhf.setYclx(StringUtils.strip(Arrays.toString(jsonObject.getJSONArray("yclx").toArray()), "[]"));
                wlgbYwjlhf.setBz(jsonObject.getString("bz"));
                JSONArray jsonArray = jsonObject.getJSONArray("imgs");
                if (jsonArray != null) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        if (jsonArray.size() > 0) {
                            wlgbYwjlhf.setImg1(jsonArray.getString(0));
                        }
                        if (jsonArray.size() > 1) {
                            wlgbYwjlhf.setImg2(jsonArray.getString(1));
                        }
                        if (jsonArray.size() > 2) {
                            wlgbYwjlhf.setImg3(jsonArray.getString(2));
                        }
                        if (jsonArray.size() > 3) {
                            wlgbYwjlhf.setImg4(jsonArray.getString(3));
                        }
                        if (jsonArray.size() > 4) {
                            wlgbYwjlhf.setImg5(jsonArray.getString(4));
                        }
                        if (jsonArray.size() > 5) {
                            wlgbYwjlhf.setImg6(jsonArray.getString(5));
                        }
                        if (jsonArray.size() > 6) {
                            wlgbYwjlhf.setImg7(jsonArray.getString(6));
                        }
                        if (jsonArray.size() > 7) {
                            wlgbYwjlhf.setImg8(jsonArray.getString(7));
                        }
                        if (jsonArray.size() > 8) {
                            wlgbYwjlhf.setImg9(jsonArray.getString(8));
                        }
                        if (jsonArray.size() > 9) {
                            wlgbYwjlhf.setImg10(jsonArray.getString(9));
                        }
                    }
                }
                wlgbYwjlhfService.save(wlgbYwjlhf);
            }
        }
        return Result.OK();
    }

    /**
     * 业务记录回访记录
     */
    @RequestMapping(value = "queryYwJlHfJl")
    public Result queryYwJlHfJl(HttpServletRequest request) {
        String search = request.getParameter("search");
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        String name = request.getParameter("name");
        PageHelp pageHelp = new PageHelp(Integer.valueOf((currentPage != null && !"".equals(currentPage) ? currentPage : "1")), Integer.valueOf((pageSize != null && !"".equals(pageSize) ? pageSize : "10")));
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        if (search != null && !"".equals(search) && search.length() > 0) {
            map.put("search", search);
        }
        if (name != null && !"".equals(name) && name.length() > 0) {
            map.put("name", name);
        }
        map.put("help", pageHelpUtil);
        PageHelpUtil pageHelpUtil1 = weiLianDdXcxService.queryYwJlHfJl(map);
        System.out.println(pageHelpUtil1);
        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);

        return Result.OK(pageHelpUtil1);
    }

    /**
     * 业务经理回访详情
     */
    @RequestMapping(value = "queryYwJlHfJlXq")
    public Result queryYwJlHfJlXq(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        List<WlgbYwjlhf> list = wlgbYwjlhfService.queryListByDdbh(ddbh);
        if (list.size() == 0) {
            return Result.error("该订单没有回访记录");
        }

        return Result.OK(list.get(0));
    }

    /**
     * 业务经理回访查询别墅信息
     */
    @RequestMapping(value = "ywjlcx")
    public Result ywjlcx(HttpServletRequest request) {
        String xddbh = request.getParameter("ddbh");
        TbXyd tbXyd = tbXydService.queryByDdBh(xddbh);
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        TbQrd qrd = tbQrdService.queryQxydIdAndQsfSc(tbXyd.getXid());
        Map<String, Object> map = new HashMap<>();
        map.put("bsmc", villa.getVname());
        if (tbXyd.getXjcflag() == 0) {
            map.put("cc", "白场");
        } else {
            map.put("cc", "晚场");
        }
        map.put("zbdz", qrd.getQzbdz());
        return Result.OK(map);
    }

    /**
     * 业务经理回访转交执行人
     */
    @RequestMapping(value = "zjDdHf")
    public Result zjDdHf(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null) {
            return Result.error("数据空的");
        }
        String jsr = jsonObject.getString("jsr");
        String ddbh = jsonObject.getString("ddbh");
        if (jsr == null || "".equals(jsr) || ddbh == null || "".equals(ddbh)) {
            return Result.error("参数不对");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("jsr", jsr);
        map.put("ddbh", ddbh);
        weiLianDaiBanService.zjZxr(map);
        return Result.OK();
    }


    /**
     * 近X天退场订单
     */
    @RequestMapping(value = "/queryXydXxByVidThreeDay")
    public Result queryXydXxByVidThreeDay(HttpServletRequest request) {
        String vid = request.getParameter("vid");
        if (vid == null || "".equals(vid)) {
            return Result.error("空的vid");
        }
        String sj = request.getParameter("sj");
        if (sj == null || "".equals(sj)) {
            return Result.error("空的sj");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("vid", vid);
        map.put("sj", sj);
        List<JSONObject> list = weiLianService.queryXydXxByVidThreeDay(map);

        return Result.OK(list);
    }

    /**
     * 根据别墅id获取所属区域
     */
    @RequestMapping(value = "queryCityByBsId")
    public Result queryCityByBsId(HttpServletRequest request) {
        String bsid = request.getParameter("bsid");
        TbVilla villa = tbVillaService.getById(bsid);
        if (villa == null) {
            return Result.error("找不到门店");
        }
        String city = villa.getCszq() != null ? !"".equals(villa.getCszq()) ? villa.getCszq() : villa.getCity() : villa.getCity();

        return Result.OK(city);
    }

    /**
     * 根据名字获取钉钉信息
     */
    @RequestMapping(value = "queryDingByName")
    public Result queryDingByName(HttpServletRequest request) {
        String username = request.getParameter("username");
        if (username == null || "".equals(username)) {
            return Result.error("username不能为空");
        }
        DingdingEmployee employee = weiLianService.queryDingDingByName(username);

        return Result.OK(employee);
    }

    /**
     * 根据别墅id获取全部别墅信息
     */
    @RequestMapping(value = "queryVillaByBsId")
    public Result queryVillaByBsId(HttpServletRequest request) {
        String bsid = request.getParameter("bsid");
        TbVilla villa = tbVillaService.getById(bsid);
        if (villa == null) {
            return Result.error("找不到门店");
        }

        return Result.OK(villa);
    }

    /**
     * 根据订单编号查询订单信息
     */
    @RequestMapping(value = "queryByDdBh")
    public Result queryByDdBh(@RequestParam(name = "ddbh", defaultValue = "") String ddbh) {
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号不能为空");
        }
        TbXyd tbXyd1 = new TbXyd();
        tbXyd1.setXddbh(ddbh);
        TbXyd tbXyd = tbXydService.queryByTbXyd(tbXyd1);
        if (tbXyd == null) {
            return Result.error("找不到订单");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("xdsj", tbXyd.getXkhtjtime());
        map.put("jcsj", tbXyd.getXjctime());
        map.put("tcsj", tbXyd.getXtctime());
        map.put("khxm", tbXyd.getXzk());
        map.put("bsid", tbXyd.getXbsmc());
        map.put("khly", tbXyd.getXkhly());
        map.put("qrsj", null);
        TbQrd tbQrd = tbQrdService.queryQxydIdAndQsfSc(tbXyd.getXid());
        Double sum = 0.0;
        if (tbQrd != null) {
            sum = (tbQrd.getQsjcdf() != null ? tbQrd.getQsjcdf() : 0) + (tbQrd.getQdf() != null ? tbQrd.getQdf() : 0) + (tbQrd.getQspxf() != null ? tbQrd.getQspxf() : 0) + (tbQrd.getQdcxf() != null ? tbQrd.getQdcxf() : 0) + (tbQrd.getQsktc() != null ? tbQrd.getQsktc() : 0) + (tbQrd.getQccrsfy() != null ? tbQrd.getQccrsfy() : 0) + (tbQrd.getQcfsyf() != null ? tbQrd.getQcfsyf() : 0) + (tbQrd.getQwsf() != null ? tbQrd.getQwsf() : 0) + (tbQrd.getQbcf() != null ? tbQrd.getQbcf() : 0) + (tbXyd.getXbxzje() != null ? tbXyd.getXbxzje() : 0) + (tbXyd.getXhpsfy() != null ? tbXyd.getXhpsfy() : 0) + (tbQrd.getQchfw() != null ? tbQrd.getQchfw() : 0) + (tbXyd.getXjbszje() != null ? tbXyd.getXjbszje() : 0) + (tbQrd.getQdgsr() != null ? tbQrd.getQdgsr() : 0) + (tbQrd.getQcdzs() != null ? tbQrd.getQcdzs() : 0) + (tbQrd.getQysf() != null ? tbQrd.getQysf() : 0) - (tbQrd.getXdfje() != null ? tbQrd.getXdfje() : 0);
            if (tbQrd.getQrsj() != null) {
                map.put("qrsj", tbQrd.getQrsj());
            }
        }
        map.put("kszxf", sum);
        DingdingEmployee employee = weiLianService.queryDingDingByName(tbXyd.getXfd());
        FwqBbb fwqBbb = weiLianService.queryBbbByXfd(tbXyd.getXfd());
        map.put("xfdid", employee != null ? employee.getUserid() : fwqBbb != null ? fwqBbb.getXddid() : null);
        map.put("xfd", employee != null ? employee.getName() : tbXyd.getXfd());
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        map.put("bsmc", villa != null ? villa.getVname() : "找不到别墅");
        map.put("bsxz", villa != null ? villa.getVxz() : "没有性质");
        map.put("tbXyd", tbXyd);
        return Result.OK(map);
    }


    @RequestMapping(value = "tjPcSj")
    public Result tjPcSj(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        if (datas == null || "".equals(datas)) {
            return Result.error("空的！！！");
        }
        JSONObject object = JSONObject.parseObject(datas);

        WlgbNotVilla wlgbNotVilla = JSON.toJavaObject(object, WlgbNotVilla.class);
        if (wlgbNotVilla.getStarTime() != null && wlgbNotVilla.getEndTime() != null) {
            if (wlgbNotVilla.getStarTime().getTime() > wlgbNotVilla.getEndTime().getTime()) {
                return Result.error("开始时间不能大于结束时间");
            }
        }
        TbVilla tbVilla = tbVillaService.getById(wlgbNotVilla.getVid());
        if (tbVilla == null) {
            return Result.error("找不到别墅！！！");
        }

        Integer gb = object.getInteger("gb");
        switch (gb) {
            case 1:
                wlgbNotVilla.setWc(0);
                wlgbNotVilla.setWcdb(1);
                break;
            case 2:
                wlgbNotVilla.setWc(1);
                wlgbNotVilla.setWcdb(0);
                break;
            default:
                wlgbNotVilla.setWc(0);
                wlgbNotVilla.setWcdb(0);
                break;
        }

        wlgbNotVilla.setVid(tbVilla.getVid());
        wlgbNotVilla.setVname(tbVilla.getVname());
        wlgbNotVilla.setTime(new Date());
        weiLianDdXcxService.insertSc(wlgbNotVilla);

        WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(wlgbNotVilla.getVid(), "无场隔离");
        if (wlgbBdjl == null) {
            WlgbBdjl wlgbBdjl1 = new WlgbBdjl();
            wlgbBdjl1.setId(IdConfig.uuId());
            wlgbBdjl1.setBz("无场隔离");
            wlgbBdjl1.setTime(new Date());
            wlgbBdjl1.setSlid(formInstId);
            wlgbBdjl1.setXid(wlgbNotVilla.getVid());
            wlgbBdjl1.setUserid("别墅无场隔离");
            wlgbBdjlService.save(wlgbBdjl1);
        }

        return Result.OK();
    }

    @RequestMapping(value = "deleteSc")
    public Result deleteSc(HttpServletRequest request) {
        String vid = request.getParameter("bsid");
        if (vid == null || "".equals(vid)) {
            return Result.error("数据空的！");
        }
        Integer count = weiLianDdXcxService.queryByVidCount(vid);
        if (count < 1) {
            return Result.error("找不到数据！");
        }

        weiLianDdXcxService.deleteSc(vid);
        WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(vid, "无场隔离");
        if (wlgbBdjl != null) {
            scBdSl(wlgbBdjl.getSlid());
            wlgbBdjlService.removeById(wlgbBdjl.getId());
        }

        return Result.OK();
    }

    @RequestMapping(value = "deleteScXy")
    public Result deleteScXy(HttpServletRequest request) {
        String vid = request.getParameter("bsid");
        if (vid == null || "".equals(vid)) {
            return Result.error("数据空的！");
        }
        Integer count = weiLianDdXcxService.queryByVidCount(vid);
        if (count < 1) {
            return Result.error("找不到数据！");
        }
        return Result.OK();
    }

    @RequestMapping(value = "updateSc")
    public Result updateSc(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("空的！！！");
        }
        JSONObject object = JSONObject.parseObject(datas);

        WlgbNotVilla wlgbNotVilla = JSON.toJavaObject(object, WlgbNotVilla.class);
        if (wlgbNotVilla.getStarTime() != null && wlgbNotVilla.getEndTime() != null) {
            if (wlgbNotVilla.getStarTime().getTime() > wlgbNotVilla.getEndTime().getTime()) {
                return Result.error("开始时间不能大于结束时间");
            }
        }
        TbVilla tbVilla = tbVillaService.getById(wlgbNotVilla.getVid());
        if (tbVilla == null) {
            return Result.error("找不到别墅！！！");
        }

        Integer gb = object.getInteger("gb");
        switch (gb) {
            case 1:
                wlgbNotVilla.setWc(0);
                wlgbNotVilla.setWcdb(1);
                break;
            case 2:
                wlgbNotVilla.setWc(1);
                wlgbNotVilla.setWcdb(0);
                break;
            default:
                wlgbNotVilla.setWc(0);
                wlgbNotVilla.setWcdb(0);
                break;
        }

        weiLianDdXcxService.updateSc(wlgbNotVilla);

        return Result.OK();
    }

    /**
     * 删除表单
     *
     * @param slid 实例id
     */
    public GatewayResult scBdSl(String slid) {
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.scBdSl(token, ydAppkey, "012412221639786136545", slid);
        } catch (Exception e) {
            gatewayResult = new GatewayResult();
            e.printStackTrace();
        }

        return gatewayResult;
    }

    /**
     * 提交策划成本登记
     */
    @RequestMapping(value = "saveChCbDj")
    public Result saveChCbDj(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }

        JSONObject jsonObject = JSONObject.parseObject(datas);
        String formInstId = request.getParameter("formInstId");
        String userId = request.getParameter("userId");

        WlgbChcbdjjl wlgbChcbdjjl = new WlgbChcbdjjl();
        wlgbChcbdjjl.setCreater(userId);
        wlgbChcbdjjl.setSlid(formInstId);

        String cgy = jsonObject.getString("employeeField_logla5y6");
        if (cgy != null && !"".equals(cgy)) {
            cgy = CommonConfig.ydRyZjJq(cgy);
            wlgbChcbdjjl.setCgy(cgy);
            DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(cgy);
            if (dingdingEmployee != null) {
                wlgbChcbdjjl.setCgy(dingdingEmployee.getName());
                wlgbChcbdjjl.setCgyid(dingdingEmployee.getUserid());
            }
        }
        String hpgw = jsonObject.getString("employeeField_logls3tb");
        if (hpgw != null && !"".equals(hpgw)) {
            hpgw = CommonConfig.ydRyZjJq(hpgw);
            wlgbChcbdjjl.setHpgw(hpgw);
            DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(hpgw);
            if (dingdingEmployee != null) {
                wlgbChcbdjjl.setHpgw(dingdingEmployee.getName());
                wlgbChcbdjjl.setHpgwid(dingdingEmployee.getUserid());
            }
        }
        String dz = jsonObject.getString("employeeField_logls3tc");
        if (dz != null && !"".equals(dz)) {
            dz = CommonConfig.ydRyZjJq(dz);
            wlgbChcbdjjl.setDz(dz);
            DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(dz);
            if (dingdingEmployee != null) {
                wlgbChcbdjjl.setDz(dingdingEmployee.getName());
                wlgbChcbdjjl.setDzid(dingdingEmployee.getUserid());
            }
        }
        String bm = jsonObject.getString("departmentSelectField_logls3ta");
        if (bm != null && !"".equals(bm)) {
            bm = CommonConfig.ydRyZjJq(bm);
            wlgbChcbdjjl.setBm(bm);
            DingdingDepartment dingdingDepartment = weiLianService.queryDingdingDeparTment(bm);
            if (dingdingDepartment != null) {
                wlgbChcbdjjl.setBm(dingdingDepartment.getDepartname());
                wlgbChcbdjjl.setBmid(dingdingDepartment.getDingdingid());
            }
        }
        wlgbChcbdjjl.setDdbh(jsonObject.getString("textField_logla5y7"));
        String bsmc = jsonObject.getString("selectField_logla5yc");
        if (bsmc != null && !"".equals(bsmc)) {
            wlgbChcbdjjl.setBsmc(bsmc);
            TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(bsmc);
            if (tbVilla != null) {
                wlgbChcbdjjl.setBsmc(tbVilla.getVname());
                wlgbChcbdjjl.setBsid(tbVilla.getVid());
            }
        }
        wlgbChcbdjjl.setZxtime(jsonObject.getDate("dateField_logla5y8"));
        wlgbChcbdjjl.setXz(jsonObject.getString("radioField_logls3ti"));
        wlgbChcbdjjl.setZztype(jsonObject.getString("radioField_logs28iu"));
        wlgbChcbdjjl.setCity(jsonObject.getString("radioField_logls3th"));
        wlgbChcbdjjl.setCc(jsonObject.getString("radioField_logla5ya"));
        wlgbChcbdjjl.setSfzm(jsonObject.getString("radioField_logla5yb"));
        wlgbChcbdjjl.setChjesr(jsonObject.getDouble("numberField_logls3td"));
        wlgbChcbdjjl.setCb(jsonObject.getDouble("numberField_logls3tf"));
        wlgbChcbdjjl.setLr(jsonObject.getDouble("numberField_logls3tg"));
        wlgbChcbdjjl.setGys(jsonObject.getString("textareaField_logls3te"));

        wlgbChcbdjjlService.save(wlgbChcbdjjl);

        return Result.OK();
    }

    /**
     * 修改策划成本登记
     */
    @RequestMapping(value = "editChCbDj")
    public Result editChCbDj(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }

        JSONObject jsonObject = JSONObject.parseObject(datas);
        String formInstId = request.getParameter("formInstId");
        String userId = request.getParameter("userId");

        WlgbChcbdjjl wlgbChcbdjjl1 = new WlgbChcbdjjl();
        wlgbChcbdjjl1.setSfsc(0);
        wlgbChcbdjjl1.setSlid(formInstId);
        WlgbChcbdjjl wlgbChcbdjjl2 = wlgbChcbdjjlService.queryWlgbChcbdjjlByWlgbChcbdjjl(wlgbChcbdjjl1);
        if (wlgbChcbdjjl2 == null) {
            return Result.error("数据不存在");
        }

        WlgbChcbdjjl wlgbChcbdjjl = new WlgbChcbdjjl();
        wlgbChcbdjjl.setId(wlgbChcbdjjl2.getId());
        wlgbChcbdjjl.setUpdater(userId);

        String cgy = jsonObject.getString("employeeField_logla5y6");
        if (cgy != null && !"".equals(cgy)) {
            cgy = CommonConfig.ydRyZjJq(cgy);
            wlgbChcbdjjl.setCgy(cgy);
            DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(cgy);
            if (dingdingEmployee != null) {
                wlgbChcbdjjl.setCgy(dingdingEmployee.getName());
                wlgbChcbdjjl.setCgyid(dingdingEmployee.getUserid());
            }
        }
        String hpgw = jsonObject.getString("employeeField_logls3tb");
        if (hpgw != null && !"".equals(hpgw)) {
            hpgw = CommonConfig.ydRyZjJq(hpgw);
            wlgbChcbdjjl.setHpgw(hpgw);
            DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(hpgw);
            if (dingdingEmployee != null) {
                wlgbChcbdjjl.setHpgw(dingdingEmployee.getName());
                wlgbChcbdjjl.setHpgwid(dingdingEmployee.getUserid());
            }
        }
        String dz = jsonObject.getString("employeeField_logls3tc");
        if (dz != null && !"".equals(dz)) {
            dz = CommonConfig.ydRyZjJq(dz);
            wlgbChcbdjjl.setDz(dz);
            DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(dz);
            if (dingdingEmployee != null) {
                wlgbChcbdjjl.setDz(dingdingEmployee.getName());
                wlgbChcbdjjl.setDzid(dingdingEmployee.getUserid());
            }
        }
        String bm = jsonObject.getString("departmentSelectField_logls3ta");
        if (bm != null && !"".equals(bm)) {
            bm = CommonConfig.ydRyZjJq(bm);
            wlgbChcbdjjl.setBm(bm);
            DingdingDepartment dingdingDepartment = weiLianService.queryDingdingDeparTment(bm);
            if (dingdingDepartment != null) {
                wlgbChcbdjjl.setBm(dingdingDepartment.getDepartname());
                wlgbChcbdjjl.setBmid(dingdingDepartment.getDingdingid());
            }
        }
        wlgbChcbdjjl.setDdbh(jsonObject.getString("textField_logla5y7"));
        String bsmc = jsonObject.getString("selectField_logla5yc");
        if (bsmc != null && !"".equals(bsmc)) {
            wlgbChcbdjjl.setBsmc(bsmc);
            TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(bsmc);
            if (tbVilla != null) {
                wlgbChcbdjjl.setBsmc(tbVilla.getVname());
                wlgbChcbdjjl.setBsid(tbVilla.getVid());
            }
        }
        wlgbChcbdjjl.setZxtime(jsonObject.getDate("dateField_logla5y8"));
        wlgbChcbdjjl.setXz(jsonObject.getString("radioField_logls3ti"));
        wlgbChcbdjjl.setZztype(jsonObject.getString("radioField_logs28iu"));
        wlgbChcbdjjl.setCity(jsonObject.getString("radioField_logls3th"));
        wlgbChcbdjjl.setCc(jsonObject.getString("radioField_logla5ya"));
        wlgbChcbdjjl.setSfzm(jsonObject.getString("radioField_logla5yb"));
        wlgbChcbdjjl.setChjesr(jsonObject.getDouble("numberField_logls3td"));
        wlgbChcbdjjl.setCb(jsonObject.getDouble("numberField_logls3tf"));
        wlgbChcbdjjl.setLr(jsonObject.getDouble("numberField_logls3tg"));
        wlgbChcbdjjl.setGys(jsonObject.getString("textareaField_logls3te"));

        wlgbChcbdjjlService.updateById(wlgbChcbdjjl);

        return Result.OK();
    }

    /**
     * 删除策划成本登记
     */
    @RequestMapping(value = "deleteChCbDj")
    public Result deleteChCbDj(HttpServletRequest request) {
        String formInstId = request.getParameter("formInstId");
        if (formInstId == null || "".equals(formInstId)) {
            return Result.error("实例id不能为空");
        }
        String userId = request.getParameter("userId");

        WlgbChcbdjjl wlgbChcbdjjl1 = new WlgbChcbdjjl();
        wlgbChcbdjjl1.setSfsc(0);
        wlgbChcbdjjl1.setSlid(formInstId);
        WlgbChcbdjjl wlgbChcbdjjl2 = wlgbChcbdjjlService.queryWlgbChcbdjjlByWlgbChcbdjjl(wlgbChcbdjjl1);
        if (wlgbChcbdjjl2 == null) {
            return Result.error("数据不存在");
        }

        WlgbChcbdjjl wlgbChcbdjjl = new WlgbChcbdjjl();
        wlgbChcbdjjl.setId(wlgbChcbdjjl2.getId());
        wlgbChcbdjjl.setDeleter(userId);

        wlgbChcbdjjlService.deleteById(wlgbChcbdjjl);

        return Result.OK();
    }

    @RequestMapping(value = "chcbData")
    public Result chcbData() {

        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_ISXER59LSVZ25LJL7JZ3");
        ydAppkey.setToken("KH766OB1MA2V3ILM4KLZL7VHAM8U22BKCYZVKRT1");

        String datas = YdConfig.getDatas(ydAppkey, "", "FORM-JD8668C1XFKFBVHVAP43B9K3UUW93MTQ9LGOLO5", "1");
        JSONObject jsonObject1 = JSONObject.parseObject(datas);
        JSONObject result = jsonObject1.getJSONObject("result");
        JSONArray data = result.getJSONArray("data");
        for (Object j : data) {
            JSONObject l = (JSONObject) j;
            JSONObject jsonObject = l.getJSONObject("formData");
            WlgbChcbdjjl wlgbChcbdjjl = new WlgbChcbdjjl();
            JSONObject originator = l.getJSONObject("originator");
            wlgbChcbdjjl.setCreater(originator.getString("userId"));
            wlgbChcbdjjl.setSlid(l.getString("formInstId"));

            String gmtCreate = l.getString("gmtCreate");
            Date date = new Date(gmtCreate.replace("-", "/"));
            wlgbChcbdjjl.setCreateTime(date);
            String cgy = jsonObject.getString("employeeField_logla5y6_id");
            if (cgy != null && !"".equals(cgy)) {
                cgy = CommonConfig.ydRyZjJq(cgy);
                wlgbChcbdjjl.setCgy(cgy);
                DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(cgy);
                if (dingdingEmployee != null) {
                    wlgbChcbdjjl.setCgy(dingdingEmployee.getName());
                    wlgbChcbdjjl.setCgyid(dingdingEmployee.getUserid());
                }
            }
            String hpgw = jsonObject.getString("employeeField_logls3tb_id");
            if (hpgw != null && !"".equals(hpgw)) {
                hpgw = CommonConfig.ydRyZjJq(hpgw);
                wlgbChcbdjjl.setHpgw(hpgw);
                DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(hpgw);
                if (dingdingEmployee != null) {
                    wlgbChcbdjjl.setHpgw(dingdingEmployee.getName());
                    wlgbChcbdjjl.setHpgwid(dingdingEmployee.getUserid());
                }
            }
            String dz = jsonObject.getString("employeeField_logls3tc_id");
            if (dz != null && !"".equals(dz)) {
                dz = CommonConfig.ydRyZjJq(dz);
                wlgbChcbdjjl.setDz(dz);
                DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(dz);
                if (dingdingEmployee != null) {
                    wlgbChcbdjjl.setDz(dingdingEmployee.getName());
                    wlgbChcbdjjl.setDzid(dingdingEmployee.getUserid());
                }
            }
            String bm = jsonObject.getString("departmentSelectField_logls3ta_id");
            if (bm != null && !"".equals(bm)) {
                bm = CommonConfig.ydRyZjJq(bm);
                wlgbChcbdjjl.setBm(bm);
                DingdingDepartment dingdingDepartment = weiLianService.queryDingdingDeparTment(bm);
                if (dingdingDepartment != null) {
                    wlgbChcbdjjl.setBm(dingdingDepartment.getDepartname());
                    wlgbChcbdjjl.setBmid(dingdingDepartment.getDingdingid());
                }
            }
            wlgbChcbdjjl.setDdbh(jsonObject.getString("textField_logla5y7"));
            String bsmc = jsonObject.getString("selectField_logla5yc_id");
            if (bsmc != null && !"".equals(bsmc)) {
                wlgbChcbdjjl.setBsmc(bsmc);
                TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(bsmc);
                if (tbVilla != null) {
                    wlgbChcbdjjl.setBsmc(tbVilla.getVname());
                    wlgbChcbdjjl.setBsid(tbVilla.getVid());
                }
            }
            Calendar c = Calendar.getInstance();
            c.setTimeInMillis(jsonObject.getLong("dateField_logla5y8"));
            wlgbChcbdjjl.setZxtime(c.getTime());
            wlgbChcbdjjl.setXz(jsonObject.getString("radioField_logls3ti"));
            wlgbChcbdjjl.setZztype(jsonObject.getString("radioField_logs28iu"));
            wlgbChcbdjjl.setCity(jsonObject.getString("radioField_logls3th"));
            wlgbChcbdjjl.setCc(jsonObject.getString("radioField_logla5ya"));
            wlgbChcbdjjl.setSfzm(jsonObject.getString("radioField_logla5yb"));
            wlgbChcbdjjl.setChjesr(jsonObject.getDouble("numberField_logls3td"));
            wlgbChcbdjjl.setCb(jsonObject.getDouble("numberField_logls3tf"));
            wlgbChcbdjjl.setLr(jsonObject.getDouble("numberField_logls3tg"));
            wlgbChcbdjjl.setGys(jsonObject.getString("textareaField_logls3te"));
            wlgbChcbdjjlService.save(wlgbChcbdjjl);
        }
        System.out.println(data);

        return Result.OK();
    }

    /**
     * 客户求助提醒
     */
    @RequestMapping(value = "khQzTx")
    public Result khQzTx(HttpServletRequest request) {
        String bsid = request.getParameter("bsid");
        String tjtime = request.getParameter("tjtime");
        String slid = request.getParameter("slid");
        String qznr = request.getParameter("qznr");
        String khhm = request.getParameter("khhm");
        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(bsid);
        if (tbVilla == null) {
            return Result.error("找不到别墅");
        }
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String contexts = "![](https://jiuyun2.qianquan888.com/upload/test/1_1703129727371.png)";
        contexts += "\n\n" + tbVilla.getVname() + "客户发起了求助/反馈，请您及时查看！";
        if (tjtime != null && !"".equals(tjtime)) {
            Calendar c = Calendar.getInstance();
            c.setTimeInMillis(Long.parseLong(tjtime));
            contexts += "\n\n客户发起时间：" + DateFormatConfig.df3(c.getTime());
        }
        contexts += "\n\n客户电话：" + khhm;
        contexts += "\n\n送达时间：";
        String tzr = "";
        if (tbVilla.getPid() != null && !"".equals(tbVilla.getPid()) && !"null".equalsIgnoreCase(tbVilla.getPid())) {
            tzr += tbVilla.getPid();
        }
        if (tbVilla.getCszjlid() != null && !"".equals(tbVilla.getCszjlid()) && !"null".equalsIgnoreCase(tbVilla.getCszjlid())) {
            if (!"".equals(tzr)) {
                if (!tzr.equals(tbVilla.getCszjlid())) {
                    tzr += "," + tbVilla.getCszjlid();
                }
            } else {
                tzr += tbVilla.getCszjlid();
            }
        }
        if (!"".equals(tzr)) {
            try {
                DingDBConfig.sendGztz1(tzr, dingkey, "客户求助提醒",
                        contexts, "https://jztdpp.aliwork.com/APP_RFI30XUL4ZN6LO6CN6EP/formDetail/FORM-68D6558A72A04E0F97D030B8E45528610VZ0?formInstId=" + slid);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }

        return Result.OK();
    }

    /**
     * 根据订单编号查询订单信息
     */
    @RequestMapping(value = "queryDdContentByDdBh")
    public Result queryDdContentByDdBh(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号不能为空！");
        }
        TbXyd tbXyd = tbXydService.queryByDdBh(ddbh);
        if (tbXyd == null) {
            return Result.error("找不到对应的协议单！");
        }
        JSONObject jsonObject1 = new JSONObject();
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(tbXyd));
        TbVilla tbVilla = tbVillaService.getById(tbXyd.getXbsmc());
        if (tbVilla != null) {
            jsonObject.put("vname", tbVilla.getVname());
        }
        if (tbXyd.getXimagepath() != null && !"".equals(tbXyd.getXimagepath())) {
            tbXyd.setXimagepath(tbXyd.getXimagepath().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
        }
        jsonObject1.put("xyd", jsonObject);
        WlgbJdbOne wlgbJdbOne = wlgbJdbOneService.queryByXid(tbXyd.getXid());
        if (wlgbJdbOne != null) {
            if (wlgbJdbOne.getWxtgjt() != null && !"".equals(wlgbJdbOne.getWxtgjt())) {
                wlgbJdbOne.setWxtgjt(wlgbJdbOne.getWxtgjt().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
            if (wlgbJdbOne.getWxwtgjt() != null && !"".equals(wlgbJdbOne.getWxwtgjt())) {
                wlgbJdbOne.setWxwtgjt(wlgbJdbOne.getWxwtgjt().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
        }
        WlgbJdbTwo wlgbJdbTwo = wlgbJdbTwoService.queryByXid(tbXyd.getXid());
        if (wlgbJdbTwo != null) {
            if (wlgbJdbTwo.getQrtp() != null && !"".equals(wlgbJdbTwo.getQrtp())) {
                wlgbJdbTwo.setQrtp(wlgbJdbTwo.getQrtp().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
        }
        WlgbJdb wlgbJdb = wlgbJdbService.queryByXid(tbXyd.getXid());
        if (wlgbJdb != null) {
            if (wlgbJdb.getRcSjxjdbt() != null && !"".equals(wlgbJdb.getRcSjxjdbt())) {
                wlgbJdb.setRcSjxjdbt(wlgbJdb.getRcSjxjdbt().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
            if (wlgbJdb.getRcSjxjdbt2() != null && !"".equals(wlgbJdb.getRcSjxjdbt2())) {
                wlgbJdb.setRcSjxjdbt2(wlgbJdb.getRcSjxjdbt2().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
            if (wlgbJdb.getCdfwktp1() != null && !"".equals(wlgbJdb.getCdfwktp1())) {
                wlgbJdb.setCdfwktp1(wlgbJdb.getCdfwktp1().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
            if (wlgbJdb.getCdfwktp2() != null && !"".equals(wlgbJdb.getCdfwktp2())) {
                wlgbJdb.setCdfwktp2(wlgbJdb.getCdfwktp2().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
            if (wlgbJdb.getTcSjdbt() != null && !"".equals(wlgbJdb.getTcSjdbt())) {
                wlgbJdb.setTcSjdbt(wlgbJdb.getTcSjdbt().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
            if (wlgbJdb.getTcSjdbt2() != null && !"".equals(wlgbJdb.getTcSjdbt2())) {
                wlgbJdb.setTcSjdbt2(wlgbJdb.getTcSjdbt2().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
        }
        jsonObject1.put("gdOne", wlgbJdbOne);
        jsonObject1.put("gdTwo", wlgbJdbTwo);
        jsonObject1.put("jcTask", wlgbJdb);
        TbQrd tbQrd = tbQrdService.queryQxydIdAndQsfSc(tbXyd.getXid());
        if (tbQrd != null) {
            if (tbQrd.getWsjzjt() != null && !"".equals(tbQrd.getWsjzjt())) {
                tbQrd.setWsjzjt(tbQrd.getWsjzjt().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
            if (tbQrd.getPcjzjt() != null && !"".equals(tbQrd.getPcjzjt())) {
                tbQrd.setPcjzjt(tbQrd.getPcjzjt().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
            if (tbQrd.getQdgfkjt() != null && !"".equals(tbQrd.getQdgfkjt())) {
                tbQrd.setQdgfkjt(tbQrd.getQdgfkjt().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
            if (tbQrd.getZztp1() != null && !"".equals(tbQrd.getZztp1())) {
                tbQrd.setZztp1(tbQrd.getZztp1().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
            if (tbQrd.getZztp2() != null && !"".equals(tbQrd.getZztp2())) {
                tbQrd.setZztp2(tbQrd.getZztp2().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
            }
        }
        jsonObject1.put("qrd", tbQrd);

        return Result.OK(jsonObject1);
    }

    /**
     * 提交/删除预定单
     */
    @RequestMapping(value = "saveYdd")
    public Result saveYdd(HttpServletRequest request) {
        String type = request.getParameter("type");
        String ddbh = request.getParameter("ddbh");
        if (type == null || "".equals(type)) {
            // type 1：添加 2：删除
            return Result.error("type不能为空");
        }
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号不能为空");
        }
        if ("1".equals(type)) {
            String khxm = request.getParameter("khxm");
            khxm = Tools.remove(khxm);
            String khdh = request.getParameter("khdh");
            String fddh = request.getParameter("fddh");
            String khsfz = request.getParameter("khsfz");
            String dwmc = request.getParameter("dwmc");
            String cdf = request.getParameter("cdf");
            String hfyj = request.getParameter("hfyj");
            String jcsj = request.getParameter("jcsj");
            String tcsj = request.getParameter("tcsj");
            String bsmc = request.getParameter("bsmc");
            String rs = request.getParameter("rs");
            String hps = request.getParameter("hps");
            String hpssl = request.getParameter("hpssl");
            String ch = request.getParameter("ch");
            String chsl = request.getParameter("chsl");
            String cy = request.getParameter("cy");
            String cysl = request.getParameter("cysl");
            String bx = request.getParameter("bx");
            String bxsl = request.getParameter("bxsl");
            String sy = request.getParameter("sy");
            String sysl = request.getParameter("sysl");
            String bxmc = request.getParameter("bxmc");
            String sysxmc = request.getParameter("sysxmc");
            String hpsmc = request.getParameter("hpsmc");
            String chmc = request.getParameter("chmc");
            String cymc = request.getParameter("cymc");
            String accountid = request.getParameter("accountId");
            String tjname = request.getParameter("tjname");
            if (khxm == null || "".equals(khxm)) {
                return Result.error("有空数据！");
            }
            if (khdh == null || "".equals(khdh)) {
                return Result.error("有空数据！");
            }
            CrmQbkh crmQbkh = new CrmQbkh();
            crmQbkh.setQkhdh(khdh);
            crmQbkh.setQxxzt("0");
            Date xjctime = new Date(jcsj.replaceAll("-", "/"));
            Date xtctime = new Date(tcsj.replaceAll("-", "/"));
            String bsid = bsmc;
            TbVilla villa = weiLianDdXcxService.queryTbVillaById(bsid);
            if (villa == null) {
                villa = weiLianService.queryVillaByVname(bsid);
                if (villa != null) {
                    bsid = villa.getVid();
                }
            }
            //协议单撞单判断
            TbXyd tbXyd = new TbXyd();
            tbXyd.setXbsmc(bsid);
            tbXyd.setXjctime(xjctime);
            tbXyd.setXtctime(xtctime);
            if (tbXyd.getXjctime().getTime() >= tbXyd.getXtctime().getTime()) {
                return Result.error("退场时间不能早于进场时间！");
            }
            Integer countXydSfZd = weiLianService.queryCountXydSfZd(tbXyd);
            if (countXydSfZd > 0) {
                return Result.error("该门店此时间段协议单撞单了！");
            }
            //预留撞单
            WlgbYlxd wlgbYlxd = new WlgbYlxd();
            wlgbYlxd.setXbsmc(bsid);
            wlgbYlxd.setXjctime(tbXyd.getXjctime());
            wlgbYlxd.setXtctime(tbXyd.getXtctime());
            Integer countWlgbYlxdSfZd = weiLianService.queryCountWlgbYlxdSfZd(wlgbYlxd);
            if (countWlgbYlxdSfZd > 0) {
                return Result.error("该门店此时间段预留单撞单了！");
            }
            //预定单撞单
            TbYddXyd tbYddXyd = new TbYddXyd();
            tbYddXyd.setXbsmc(bsid);
            tbYddXyd.setXjctime(tbXyd.getXjctime());
            tbYddXyd.setXtctime(tbXyd.getXtctime());
            Integer countTbYddXydSfZd = weiLianService.queryCountTbYddXydSfZd(tbYddXyd);
            if (countTbYddXydSfZd > 0) {
                return Result.error("该门店此时间段预定单撞单了！");
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("khxm", khxm);
            jsonObject.put("khdh", khdh);
            jsonObject.put("khsfz", khsfz);
            jsonObject.put("dwmc", dwmc);
            jsonObject.put("cdf", cdf);
            jsonObject.put("hfyj", hfyj);
            jsonObject.put("jcsj", jcsj);
            jsonObject.put("tcsj", tcsj);
            jsonObject.put("bsmc", bsmc);
            jsonObject.put("rs", rs);
            jsonObject.put("hps", hps);
            jsonObject.put("hpssl", hpssl);
            jsonObject.put("ch", ch);
            jsonObject.put("chsl", chsl);
            jsonObject.put("cy", cy);
            jsonObject.put("cysl", cysl);
            jsonObject.put("bx", bx);
            jsonObject.put("bxsl", bxsl);
            jsonObject.put("sy", sy);
            jsonObject.put("sysl", sysl);
            jsonObject.put("ddbh", ddbh);
            jsonObject.put("sysxmc", sysxmc);
            jsonObject.put("chmc", chmc);
            jsonObject.put("cymc", cymc);
            jsonObject.put("bxmc", bxmc);
            jsonObject.put("hpsmc", hpsmc);
            jsonObject.put("tjname", tjname);
            jsonObject.put("accountid", accountid);
            //调用阿里云函数计算的异步函数
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("datas", jsonObject.toJSONString());
            paramMap.put("uuid", IdConfig.uuId());
            System.out.println("uuid====================" + paramMap.get("uuid"));
            //在这里进行异步调用函数计算里面的函数即可
            try {
                HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/saveYddTask", paramMap);
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else if ("2".equals(type)) {
            TbYddXyd tbYddXyd = new TbYddXyd();
            tbYddXyd.setXsfsc(0);
            tbYddXyd.setXddbh(ddbh);
            TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd);
            if (tbYddXyd1 == null) {
                return Result.error("找不到对应的订单");
            }
            if ("已提交，客服处理中".equals(tbYddXyd1.getXstatu()) || "已处理，待交定金".equals(tbYddXyd1.getXstatu())) {
                //调用阿里云函数计算的异步函数
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("ddbh", ddbh);
                paramMap.put("uuid", IdConfig.uuId());
                System.out.println("uuid====================" + paramMap.get("uuid"));
                //在这里进行异步调用函数计算里面的函数即可
                try {
                    HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/deleteYddTask", paramMap);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } else {
                return Result.error("当前预定单状态不允许删除");
            }


        }

        return Result.OK();
    }


    /**
     * 取消预定单
     */
    @RequestMapping(value = "qxYdd")
    public Result qxYdd(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        String userId = request.getParameter("userId");
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号空的");
        }
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXsfsc(0);
        tbYddXyd.setXddbh(ddbh);
        TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd);
        if (tbYddXyd1 == null) {
            return Result.error("找不对应的订单！");
        }
        if ("已提交，客服处理中".equals(tbYddXyd1.getXstatu()) || "已处理，待交定金".equals(tbYddXyd1.getXstatu())) {
            //调用阿里云函数计算的异步函数
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("ddbh", ddbh);
            paramMap.put("userId", userId);
            paramMap.put("uuid", IdConfig.uuId());
            System.out.println("uuid====================" + paramMap.get("uuid"));
            //在这里进行异步调用函数计算里面的函数即可
            try {
                HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/qxYddTask", paramMap);
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            return Result.error("当前预定单状态不允许删除");
        }


        return Result.OK();
    }

    /**
     * 重新生成预定单定金二维码
     */
    @RequestMapping(value = "yddDjEwmSc")
    public Result yddDjEwmSc(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号空的");
        }
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXsfsc(0);
        tbYddXyd.setXddbh(ddbh);
        TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd);
        if (tbYddXyd1 == null) {
            return Result.error("找不到对应的订单");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("ddbh", ddbh);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/yddDjEwmScTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }


        return Result.OK();
    }

    /**
     * 业务员处理预定单生成二维码
     */
    @RequestMapping(value = "editYdd")
    public Result editYdd(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        TbYddXyd tbYddXyd = JSONObject.toJavaObject(jsonObject, TbYddXyd.class);

        TbYddXyd tbYddXyd1 = new TbYddXyd();
        tbYddXyd1.setXsfsc(0);
        tbYddXyd1.setXid(tbYddXyd.getXid());
        TbYddXyd tbYddXyd2 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd1);
        if (tbYddXyd2 == null) {
            return Result.error("找不到对应的数据！");
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/editYddTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 生成定金二维码
     *
     * @param url 二维码访问地址
     * @return 阿里云地址
     */
    public String scEwm(String url) throws Exception {
        String filePath = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "xcxdjewm.jpg");
        File file = new File(filePath);
        ZXingCode.drawLogoQRCode(null, file, url, "");

        FileInputStream fileInputStream = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
        fileInputStream.close();
        String upload = ossFileService.upload(multipartFile);
        if (upload != null && !"".equals(upload)) {
            upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
        }
        file.delete();
        return upload;
    }

    @RequestMapping(value = "yddPdFs")
    public void yddPdFs(@RequestParam(name = "yddBh", defaultValue = "") String yddBh,
                        HttpServletRequest request, HttpServletResponse response) {
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXskbh(yddBh);
        tbYddXyd.setXsfsc(0);
        TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd);
        if (tbYddXyd1 != null) {
            Double je = tbYddXyd1.getXysdj();
            String skmc = "场地定金";
            String userid = tbYddXyd1.getXfdid();
            WlgbHxyhFqsk wlgbHxyhFqsk = new WlgbHxyhFqsk();
            wlgbHxyhFqsk.setTjsj(new Date());
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
            wlgbHxyhFqsk.setTjr(employee != null ? employee.getName() : null);
            wlgbHxyhFqsk.setTjrid(employee != null ? employee.getUserid() : null);
            wlgbHxyhFqsk.setJe(je);
            wlgbHxyhFqsk.setDdbh(yddBh);
            wlgbHxyhFqsk.setFymc(skmc);
            String userAgent = request.getHeader("user-agent");

            boolean b = true;
            int count = wlgbHxyhFqskService.queryByDdBhAndZfZt(yddBh, "是");
            if (count > 0) {
                b = false;
            }
            if (b) {
                if (userAgent != null && userAgent.contains("MicroMessenger")) {
                    WlgbHxyhFqsk hxyhFqsk = wlgbHxyhFqskService.queryByDdBhAndFkFs(yddBh, "微信");
                    if (hxyhFqsk == null) {
                        yddBh = yddBh + "WX";
                        String redirectUrl = ddxturl + "/ysdjtb/wlgb/xyd/wxHqCode?ddbh=" + yddBh + "&je=" + je +
                                "&skmc=" + skmc;
                        //这里要将你的授权回调地址处理一下，否则微信识别不了
                        //UTF-8编码
                        String redirectUri = null;
                        try {
                            redirectUri = URLEncoder.encode(redirectUrl, "UTF-8");
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                        //简单获取openid的话参数response_type与scope与state参数固定写死即可
                        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx3136bf5ad7ad7bd6"
                                + "&redirect_uri="
                                + redirectUri + "&response_type=code&scope="
                                + "snsapi_base" + "&state=STATE#wechat_redirect";
                        wlgbHxyhFqsk.setEwm(url);
                        wlgbHxyhFqsk.setFkfs("微信");
                        wlgbHxyhFqskService.save(wlgbHxyhFqsk);
                        try {
                            response.sendRedirect(url);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    } else {
                        try {
                            response.sendRedirect(hxyhFqsk.getEwm());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    System.out.println("微信支付");

                } else if (userAgent != null && userAgent.contains("AlipayClient")) {
                    WlgbHxyhFqsk hxyhFqsk = wlgbHxyhFqskService.queryByDdBhAndFkFs(yddBh, "支付宝");
                    if (hxyhFqsk == null) {
                        yddBh = yddBh + "ZFB";
                        System.out.println("支付宝支付");
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("ddbh", yddBh);
                        jsonObject.put("je", je);
                        jsonObject.put("fkfs", "1903000");
                        jsonObject.put("skmc", yddBh + skmc);
                        JSONObject result = YsPayConfig.ewmPayYdd(jsonObject, ddxturl);
                        wlgbHxyhFqsk.setFkfs("支付宝");
                        if (result != null) {
                            String sourceQrCodeUrl = result.getString("source_qr_code_url");
                            try {
                                response.sendRedirect(sourceQrCodeUrl);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                            wlgbHxyhFqsk.setEwm(sourceQrCodeUrl);
                            wlgbHxyhFqsk.setYxbh(result.getString("trade_no"));
                        }
                        wlgbHxyhFqskService.save(wlgbHxyhFqsk);
                    } else {
                        try {
                            response.sendRedirect(hxyhFqsk.getEwm());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    try {
                        response.setContentType("text/html; charset=UTF-8");
                        response.getWriter().print("<html><body><script type='text/javascript'>alert('请使用微信或支付宝扫码！');</script></body></html>");
                        response.getWriter().close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                try {
                    response.setContentType("text/html; charset=UTF-8");
                    response.getWriter().print("<html><body><script type='text/javascript'>alert('该订单已支付，请勿重复支付！');</script></body></html>");
                    response.getWriter().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            System.out.println(userAgent);
        } else {
            try {
                response.setContentType("text/html; charset=UTF-8");
                response.getWriter().print("<html><body><script type='text/javascript'>alert('该二维码已失效，请重新生成！');</script></body></html>");
                response.getWriter().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 用户扫码授权跳转发起支付订单(定金与短租回款)
     */
    @RequestMapping(value = "wxHqCode")
    public void wxHqCode(HttpServletRequest request, HttpServletResponse response) throws IOException {

        String code = request.getParameter("code");
        String ddbh = request.getParameter("ddbh");
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXskbh(ddbh.replace("WX", ""));
        tbYddXyd.setXsfsc(0);
        TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd);
        if (tbYddXyd1 != null) {
            String je = request.getParameter("je");
            String skmc = request.getParameter("skmc");
            System.out.println("code：" + code);

            Map<String, String> params = new HashMap<>();
            params.put("secret", "681ca33470657c426e50847b0e60ab3a");
            params.put("appid", "wx3136bf5ad7ad7bd6");
            params.put("grant_type", "authorization_code");
            params.put("code", code);
            String result = HttpClientUtil.get("https://api.weixin.qq.com/sns/oauth2/access_token", params);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String openid = jsonObject.getString("openid");
            System.out.println(openid);
            WlgbHxyhFqsk hxyhFqsk = wlgbHxyhFqskService.queryByDdBhAndFkFs(ddbh.replace("WX", ""), "微信");
            boolean b = true;
            WlgbHxyhFqsk wlgbHxyhFqsk = new WlgbHxyhFqsk();
            if (hxyhFqsk != null) {
                if (hxyhFqsk.getAppid() != null && !"".equals(hxyhFqsk.getAppid())) {
                    String url = "https://yinshengdj.qianquan888.com/ysdjtb/wxPay.html?appId=" + hxyhFqsk.getAppid() +
                            "&timeStamp=" + hxyhFqsk.getTimestamp() +
                            "&nonceStr=" + hxyhFqsk.getNoncestr() +
                            "&package=" + hxyhFqsk.getPackage1() +
                            "&paySign=" + hxyhFqsk.getPaysign();
                    response.sendRedirect(url);
                    b = false;
                } else {
                    wlgbHxyhFqsk.setId(hxyhFqsk.getId());
                }

            }
            if (b) {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("ddbh", ddbh);
                jsonObject1.put("je", je);
                jsonObject1.put("skmc", skmc);
                jsonObject1.put("openid", openid);
                JSONObject jsonObject2;
                jsonObject2 = YsPayConfig.weiXinPayYdd(jsonObject1, ddxturl);
                if (jsonObject2 != null && jsonObject2.size() > 0) {
                    JSONObject payInfo = jsonObject2.getJSONObject("jsapi_pay_info");
                    wlgbHxyhFqsk.setYxbh(jsonObject2.getString("trade_no"));
                    wlgbHxyhFqsk.setDdbh(ddbh.replace("WX", ""));
                    if (payInfo != null && payInfo.size() > 0) {
                        String url = "https://yinshengdj.qianquan888.com/ysdjtb/wxPay.html?appId=" + payInfo.getString("appId") +
                                "&timeStamp=" + payInfo.getString("timeStamp") +
                                "&nonceStr=" + payInfo.getString("nonceStr") +
                                "&package=" + payInfo.getString("package") +
                                "&paySign=" + payInfo.getString("paySign");
                        wlgbHxyhFqsk.setAppid(payInfo.getString("appId"));
                        wlgbHxyhFqsk.setTimestamp(payInfo.getString("timeStamp"));
                        wlgbHxyhFqsk.setNoncestr(payInfo.getString("nonceStr"));
                        wlgbHxyhFqsk.setPackage1(payInfo.getString("package"));
                        wlgbHxyhFqsk.setPaysign(payInfo.getString("paySign"));
                        if (hxyhFqsk != null) {
                            wlgbHxyhFqskService.updateById(wlgbHxyhFqsk);
                        } else {
                            wlgbHxyhFqskService.save(wlgbHxyhFqsk);
                        }
                        response.sendRedirect(url);
                    } else {
                        if (hxyhFqsk != null) {
                            wlgbHxyhFqskService.updateById(wlgbHxyhFqsk);
                        } else {
                            wlgbHxyhFqskService.save(wlgbHxyhFqsk);
                        }
                    }
                }
            }
        } else {
            try {
                response.setContentType("text/html; charset=UTF-8");
                response.getWriter().print("<html><body><script type='text/javascript'>alert('该二维码已失效，请重新生成！');</script></body></html>");
                response.getWriter().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 定金到账回调通知处理数据
     */
    @RequestMapping(value = "djDz")
    public Result djDz(SelectVo selectVo) {
        System.out.println("定金到账回调通知处理数据" + selectVo.getAccount_date());
        if (!"TRADE_SUCCESS".equals(selectVo.getTrade_status())) {
            System.out.println("不是支付成功的订单！");
            return Result.OK();
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", JSONObject.toJSONString(selectVo));
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/djDzYddTaSk", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 根据订单编号获取预定单信息
     */
    @RequestMapping(value = "queryYddXxByDdbh")
    public Result queryYddXxByDdbh(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号空的");
        }
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXddbh(ddbh);
//        tbYddXyd.setXsfsc(0);
        TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd);
        if (tbYddXyd1 == null) {
            return Result.error("找不到对应的订单");
        }
        if (tbYddXyd1.getXdjurl() != null && !"".equals(tbYddXyd1.getXdjurl())) {
            tbYddXyd1.setXdjurl(tbYddXyd1.getXdjurl().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
        }
        TbYddXydVo tbYddXydVo = new TbYddXydVo();
        BeanUtils.copyProperties(tbYddXyd1, tbYddXydVo);
        tbYddXydVo.setSxtime(0L);
        if (tbYddXyd1.getXdjsxtime() != null) {
            long l = tbYddXyd1.getXdjsxtime().getTime() - new Date().getTime();
            tbYddXydVo.setSxtime(l >= 0 ? l : 0L);
        }
        return Result.OK(tbYddXydVo);
    }

    /**
     * 根据订单编号获取预定单押金和尾款信息
     */
    @RequestMapping(value = "queryYddYjAndWkByDdbh")
    public Result queryYddYjAndWkByDdbh(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号空的");
        }
        WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
        wlgbYddYjWkJl.setXddbh(ddbh);
        wlgbYddYjWkJl.setSfsc(0);
        WlgbYddYjWkJl wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
        if (wlgbYddYjWkJl1 == null) {
            return Result.error("找不到对应的支付订单");
        }
        if (wlgbYddYjWkJl1.getYjewm() != null && !"".equals(wlgbYddYjWkJl1.getYjewm())) {
            wlgbYddYjWkJl1.setYjewm(wlgbYddYjWkJl1.getYjewm().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
        }
        if (wlgbYddYjWkJl1.getWkewm() != null && !"".equals(wlgbYddYjWkJl1.getWkewm())) {
            wlgbYddYjWkJl1.setWkewm(wlgbYddYjWkJl1.getWkewm().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
        }
        WlgbYddYjWkJlVo wlgbYddYjWkJlVo = new WlgbYddYjWkJlVo();
        BeanUtils.copyProperties(wlgbYddYjWkJl1, wlgbYddYjWkJlVo);
        wlgbYddYjWkJlVo.setYjsx(0L);
        wlgbYddYjWkJlVo.setWksx(0L);
        if (wlgbYddYjWkJl1.getYjsxtime() != null) {
            long l = wlgbYddYjWkJl1.getYjsxtime().getTime() - new Date().getTime();
            wlgbYddYjWkJlVo.setYjsx(l >= 0 ? l : 0L);
        }
        if (wlgbYddYjWkJl1.getWksxtime() != null) {
            long l = wlgbYddYjWkJl1.getWksxtime().getTime() - new Date().getTime();
            wlgbYddYjWkJlVo.setWksx(l >= 0 ? l : 0L);
        }
        return Result.OK(wlgbYddYjWkJlVo);
    }


    /**
     * 根据订单编号获取预定单门店消费支付信息
     */
    @RequestMapping(value = "queryYddMdXfByDdbh")
    public Result queryYddMdXfByDdbh(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号空的");
        }
        WlgbYddMdxfjl wlgbYddMdxfjl = new WlgbYddMdxfjl();
        wlgbYddMdxfjl.setXddbh(ddbh);
        wlgbYddMdxfjl.setSfsc(0);
        WlgbYddMdxfjl wlgbYddMdxfjl1 = wlgbYddMdxfjlService.queryWlgbYddMdxfjlByWlgbYddMdxfjl(wlgbYddMdxfjl);
        if (wlgbYddMdxfjl1 == null) {
            return Result.error("找不到对应的支付订单");
        }
        if (wlgbYddMdxfjl1.getEwm() != null && !"".equals(wlgbYddMdxfjl1.getEwm())) {
            wlgbYddMdxfjl1.setEwm(wlgbYddMdxfjl1.getEwm().replace("http://jiuyun2.qianquan888.com", "https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com"));
        }
        WlgbYddMdxfjlVo wlgbYddMdxfjlVo = new WlgbYddMdxfjlVo();
        BeanUtils.copyProperties(wlgbYddMdxfjl1, wlgbYddMdxfjlVo);
        wlgbYddMdxfjlVo.setSx(0L);
        if (wlgbYddMdxfjl1.getSxtime() != null) {
            long l = wlgbYddMdxfjl1.getSxtime().getTime() - new Date().getTime();
            wlgbYddMdxfjlVo.setSx(l >= 0 ? l : 0L);
        }
        return Result.OK(wlgbYddMdxfjl1);
    }

    /**
     * 预定单提交至协议单
     */
    @RequestMapping(value = "yddSaveXyd")
    public Result yddSaveXyd(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userId = request.getParameter("userId");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的！");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userId", userId);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/yddSaveXydTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 提交券码进行验券
     */
    @RequestMapping(value = "tjYddQm")
    public Result tjYddQm(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号不能为空");
        }
        String qm = request.getParameter("qm");
        if (qm == null || "".equals(qm)) {
            return Result.error("订单编号不能为空");
        }
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXsfsc(0);
        tbYddXyd.setXddbh(ddbh);
        TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd);
        if (tbYddXyd1 == null) {
            return Result.error("找不到对应的订单");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("ddbh", ddbh);
        paramMap.put("qm", qm);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/tjYddQmTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }


    @RequestMapping(value = "cxfqjc")
    public Result cxFqJc(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号不能为空");
        }
        TbXyd l = tbXydService.queryByDdBh(ddbh);
        if (l == null) {
            return Result.error("找不到对应的订单");
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("进场及消费");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(l.getXbsmc());

        String xydUrl = null;
        try {
            xydUrl = dzXydCl(l);
        } catch (Exception e) {
            e.printStackTrace();
        }
        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
        WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid("进场及消费", l.getXid());
        if (dksljl != null) {
            String token = null;
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            GatewayResult gatewayResult = new GatewayResult();
            try {
                gatewayResult = DingBdLcConfig.scLcSl(token, ydAppkey, "012412221639786136545", dksljl.getSlid());
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println(gatewayResult);
            if (gatewayResult.getSuccess()) {
                wlgbDksljlService.removeById(dksljl.getId());
            }
        }

        WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
        wlgbYddYjWkJl.setXddbh(ddbh);
        wlgbYddYjWkJl.setSfsc(0);
        WlgbYddYjWkJl wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
        if (wlgbYddYjWkJl1 != null) {
            WlgbYddYjWkJl wlgbYddYjWkJl2 = new WlgbYddYjWkJl();
            wlgbYddYjWkJl2.setId(wlgbYddYjWkJl1.getId());
            wlgbYddYjWkJl2.setSfsc(1);
            wlgbYddYjWkJlService.updateById(wlgbYddYjWkJl2);
        }

        try {
            TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(l.getXid());
            WlgbDksljl wlgbDksljl = JcJXfLcConfig.jcJXfLc(l, villa, xydUrl, dingkey, employee, ydAppkey, ydBd, null, null, tbYddXyd != null ? "是" : "否");
            if (wlgbDksljl != null) {
                wlgbDksljlService.save(wlgbDksljl);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    @RequestMapping(value = "cxfqjc1")
    public Result cxFqJc1(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        if (ddbh == null || "".equals(ddbh)) {
            return Result.error("订单编号不能为空");
        }
        TbXyd l = tbXydService.queryByDdBh(ddbh);
        if (l == null) {
            return Result.error("找不到对应的订单");
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
//        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("进场及消费");
        YdBd ydBd = new YdBd();
        ydBd.setFormid("FORM-9A524E0AAF97481DA2C661621A5F3C40HW2H");
        ydBd.setCode("TPROC--GP666M71R58IDT3AF80CA8SS9EN63DIM9ARSL3");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(l.getXbsmc());

        String xydUrl = null;
        try {
            xydUrl = dzXydCl(l);
        } catch (Exception e) {
            e.printStackTrace();
        }
        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
        WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid("进场及消费", l.getXid());
        if (dksljl != null) {
            String token = null;
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            GatewayResult gatewayResult = new GatewayResult();
            try {
                gatewayResult = DingBdLcConfig.scLcSl(token, ydAppkey, "012412221639786136545", dksljl.getSlid());
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println(gatewayResult);
            if (gatewayResult.getSuccess()) {
                wlgbDksljlService.removeById(dksljl.getId());
            }
        }

        WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
        wlgbYddYjWkJl.setXddbh(ddbh);
        wlgbYddYjWkJl.setSfsc(0);
        WlgbYddYjWkJl wlgbYddYjWkJl1 = wlgbYddYjWkJlService.queryWlgbYddYjWkJlByWlgbYddYjWkJl(wlgbYddYjWkJl);
        if (wlgbYddYjWkJl1 != null) {
            WlgbYddYjWkJl wlgbYddYjWkJl2 = new WlgbYddYjWkJl();
            wlgbYddYjWkJl2.setId(wlgbYddYjWkJl1.getId());
            wlgbYddYjWkJl2.setSfsc(1);
            wlgbYddYjWkJlService.updateById(wlgbYddYjWkJl2);
        }

        try {
            TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(l.getXid());
            WlgbDksljl wlgbDksljl = JcJXfLcConfig.jcJXfLc(l, villa, xydUrl, dingkey, employee, ydAppkey, ydBd, null, null, tbYddXyd != null ? "是" : "否");
            if (wlgbDksljl != null) {
                wlgbDksljlService.save(wlgbDksljl);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 查询押金尾款支付记录
     */
    @RequestMapping(value = "queryYjWkZfJlByDdBh")
    public Result queryYjWkZfJlByDdBh(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        List<YjDzVo> list = weiLianDdXcxService.queryYjWkZfJlByDdBh(ddbh);

        return Result.OK(list);
    }

    /**
     * 生成门第消费二维码
     */
    @RequestMapping(value = "scMdXfEwm")
    public Result scMdXfEwm(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的!");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        TbQrd qrd = JSONObject.toJavaObject(jsonObject, TbQrd.class);
        if (qrd.getQxydid() == null || "".equals(qrd.getQxydid())) {
            qrd.setQxydid(jsonObject.getString("xid"));
        }
        if (qrd.getQxydid() == null || "".equals(qrd.getQxydid())) {
            return Result.error("没有协议单id！！！");
        }
        TbXyd xyd1 = tbXydService.queryById(qrd.getQxydid());
        if (xyd1 == null) {
            return Result.error("协议单错误！");
        }
        Double zzje = jsonObject.getDouble("zzje") != null ? jsonObject.getDouble("zzje") : 0.0;
        if (zzje > 0) {
            //调用阿里云函数计算的异步函数
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("datas", datas);
            paramMap.put("formInstId", formInstId);
            paramMap.put("uuid", IdConfig.uuId());
            System.out.println("uuid====================" + paramMap.get("uuid"));
            //在这里进行异步调用函数计算里面的函数即可
            try {
                HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/scMdXfEwmTask", paramMap);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }


        return Result.OK();
    }

    /**
     * 生成定金二维码
     *
     * @param url 二维码访问地址
     * @return 阿里云地址
     */
    public String scEwm1(String url, String name) throws Exception {
//        String filePath = PathUtil.getClassResources() + "static" + File.separator + "fkewm.jpg";
        String filePath = FileConfig.getFileAbsolutePath("static" + File.separator + "img", name +
                ".jpg");
        File file = new File(filePath);
        ZXingCode.drawLogoQRCode(null, file, url, "");

        FileInputStream fileInputStream = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
        if (fileInputStream != null) {
            fileInputStream.close();
        }
        String upload = ossFileService.upload(multipartFile);
        if (upload != null && !"".equals(upload)) {
            upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
        }
        file.delete();
//        file.delete();
        return upload;
    }

    /**
     * 电子协议单处理
     *
     * @param tbXyd 协议单对象
     * @return 协议单地址
     */
    public String dzXydCl(TbXyd tbXyd) {
        Dzxyd dzxyd = weiLianDdXcxService.queryDzXydByXid(tbXyd.getXid());
        if (dzxyd == null) {
            return "";
        }
        String upload = dzxyd.getUrl();

        return upload;
    }


}
