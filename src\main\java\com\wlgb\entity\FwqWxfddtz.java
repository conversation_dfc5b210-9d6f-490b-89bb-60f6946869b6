package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "fwq_wxfddtz")
public class FwqWxfddtz {
    @Id
    private Integer id;
    private String fstime;
    private String xddbh;
    private String xfd;
    private String zbdz;
}
