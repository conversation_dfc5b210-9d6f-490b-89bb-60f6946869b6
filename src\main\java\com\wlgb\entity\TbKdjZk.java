package com.wlgb.entity;

import java.io.Serializable;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: tb_kdj_zk
 * @Author: jeecg-boot
 * @Date:   2021-10-04
 * @Version: V1.0
 */
@Data
@Table(name = "tb_kdj_zk")
public class TbKdjZk {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
	/**场次数*/
    private java.lang.Integer cc;
	/**折扣*/
    private java.lang.Double zk;
	/**是否删除*/
    private java.lang.String sfsc;
	/**录入人id*/
    private java.lang.String lrrid;
	/**录入人*/
    private java.lang.String lrr;
	/**录入时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date lrsj;
	/**修改人id*/
    private java.lang.String xgrid;
	/**修改人*/
    private java.lang.String xgr;
	/**修改时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date xgsj;
	/**删除人id*/
    private java.lang.String scrid;
	/**删除人*/
    private java.lang.String scr;
	/**删除时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date scsj;
    /**实例id*/
    private java.lang.String slid;
}
