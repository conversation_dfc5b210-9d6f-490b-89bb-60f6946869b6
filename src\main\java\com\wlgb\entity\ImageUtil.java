package com.wlgb.entity; /**
 * @ClassName ImageUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/09/28 23:31
 * @Version 1.0
 */

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.ResourceLoader;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageOutputStream;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Hashtable;

import static com.google.zxing.client.j2se.MatrixToImageConfig.BLACK;
import static com.google.zxing.client.j2se.MatrixToImageConfig.WHITE;

/**
 * <AUTHOR>
 * @Description TODO 在图片上生成二维码并且在图片上添加文字 保存指定地址文件名
 * @Date 2020-09-30 10:42
 */
public class ImageUtil {

    /**
     * @param content  二维码内容
     * @param written  文字内容
     * @param fielPath 保存文件 例： d:/1.png
     * @param hbPath   海报图片地址 例： d:/1.png
     * @param logoPath 二维码logo
     * @return
     * <AUTHOR>
     * @Description TODO 在一张背景图上添加二维码
     * @Date 2020-09-28 23:59
     */
    public static void drawString(String content, String written, String fielPath, String hbPath, String logoPath) throws Exception {
        BufferedImage image = addWater(content, hbPath, logoPath);
        Graphics2D gd = image.createGraphics();
        // 3、设置对线段的锯齿状边缘处理
        gd.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);

        // 5、设置水印文字颜色
        gd.setColor(new Color(255, 255, 255));
        // 6、设置水印文字Font
        gd.setFont(new Font("黑体", Font.PLAIN, 12));
        // 8、第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
        gd.drawString("仅限微信扫码支付", 160, 150);


        // 5、设置水印文字颜色
        gd.setColor(new Color(153, 153, 153));
        // 6、设置水印文字Font
        gd.setFont(new Font("黑体", Font.BOLD, 14));
        // 8、第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
        gd.drawString("订单号YS20220115145926895", 40, 210);

        gd.drawString("2022-01-15 16:12", 250, 210);


        // 5、设置水印文字颜色
        gd.setColor(new Color(2, 191, 111));
        // 6、设置水印文字Font
        gd.setFont(new Font("微软雅黑", Font.BOLD, 26));
        // 8、第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
        gd.drawString("¥ 80.00", 180, 460);
        // 5、设置水印文字颜色
        gd.setColor(Color.LIGHT_GRAY);
        // 6、设置水印文字Font
        gd.setFont(new Font("华文细黑", Font.BOLD, 16));
        // 8、第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
        gd.drawString("应付", 130, 458);
        gd.dispose();

        ByteArrayOutputStream bs = new ByteArrayOutputStream();
        ImageOutputStream imOut = ImageIO.createImageOutputStream(bs);
        ImageIO.write(image, "png", imOut);
        InputStream inputStream = new ByteArrayInputStream(bs.toByteArray());

        OutputStream outStream = new FileOutputStream(fielPath);
        IOUtils.copy(inputStream, outStream);
        inputStream.close();
        outStream.close();

    }

    /**
     * @param content  二维码内容
     * @param fielPath 保存文件 例： d:/1.png
     * @param hbPath   海报图片地址 例： d:/1.png
     * @param logoPath 二维码logo
     * @return
     * <AUTHOR>
     * @Description TODO 在一张背景图上添加二维码
     * @Date 2020-09-28 23:59
     */
    public static void drawString1(String content, String fkfs, String ddbh, String dqTime, String je, String fielPath, String hbPath, String logoPath) throws Exception {
        BufferedImage image = addWater(content, hbPath, logoPath);
        Graphics2D gd = image.createGraphics();
        // 3、设置对线段的锯齿状边缘处理
        gd.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);

        // 5、设置水印文字颜色
        gd.setColor(new Color(255, 255, 255));
        // 6、设置水印文字Font
        gd.setFont(new Font("黑体", Font.PLAIN, 12));
        // 8、第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
        gd.drawString("仅限支付宝/微信扫码支付", 136, 150);


        // 5、设置水印文字颜色
        gd.setColor(new Color(153, 153, 153));
        // 6、设置水印文字Font
        gd.setFont(new Font("黑体", Font.BOLD, 14));
        // 8、第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
        gd.drawString("订单号" + ddbh, 40, 210);

        gd.drawString(dqTime, 250, 210);


        // 5、设置水印文字颜色
        gd.setColor(new Color(2, 191, 111));
        // 6、设置水印文字Font
        gd.setFont(new Font("微软雅黑", Font.BOLD, 26));
        // 8、第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
        gd.drawString("¥ " + je, 180, 460);
        // 5、设置水印文字颜色
        gd.setColor(Color.LIGHT_GRAY);
        // 6、设置水印文字Font
        gd.setFont(new Font("华文细黑", Font.BOLD, 16));
        // 8、第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
        gd.drawString("应付", 130, 458);
        gd.dispose();

        ByteArrayOutputStream bs = new ByteArrayOutputStream();
        ImageOutputStream imOut = ImageIO.createImageOutputStream(bs);
        ImageIO.write(image, "png", imOut);
        InputStream inputStream = new ByteArrayInputStream(bs.toByteArray());

        OutputStream outStream = new FileOutputStream(fielPath);
        IOUtils.copy(inputStream, outStream);
        inputStream.close();
        outStream.close();

    }


    /***
     * 在一张背景图上添加二维码
     */
    public static BufferedImage addWater(String content, String hbPath, String logoPath) throws Exception {
        // 读取原图片信息
        //得到文件
        File file = new File(hbPath);
        //文件转化为图片
        Image srcImg = ImageIO.read(file);
        //获取图片的宽
        int srcImgWidth = srcImg.getWidth(null);
        //获取图片的高
        int srcImgHeight = srcImg.getHeight(null);
        // 加水印
        BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = bufImg.createGraphics();
        g.drawImage(srcImg, 0, 0, srcImgWidth, srcImgHeight, null);
        Image image = createQrCode(content, 400, 400, logoPath);
        //将小图片绘到大图片上,500,300 .表示你的小图片在大图片上的位置。
        g.drawImage(image, 120, 240, 180, 180, null);
        //设置颜色。
        g.setColor(Color.RED);
        g.dispose();
//        file1.delete();
        return bufImg;
    }



    private static BufferedImage createQrCode(String content, int width, int height, String logoPath) throws IOException, WriterException {

        QrConfig config = new QrConfig(width, height);
        if (logoPath != null) {
            config.setForeColor(Color.black);
            config.setMargin(0);
            config.setRatio(4);
        }
        config.setErrorCorrection(ErrorCorrectionLevel.H);
        BufferedImage bufferedImage = QrCodeUtil.generate(content, config);
        return bufferedImage;
    }

    //去白边的话，调用这个方法
    private static BufferedImage deleteWhite(BitMatrix matrix) {
        int[] rec = matrix.getEnclosingRectangle();
        int resWidth = rec[2] + 1;
        int resHeight = rec[3] + 1;

        BitMatrix resMatrix = new BitMatrix(resWidth, resHeight);
        resMatrix.clear();
        for (int i = 0; i < resWidth; i++) {
            for (int j = 0; j < resHeight; j++) {
                if (matrix.get(i + rec[0], j + rec[1])) {
                    resMatrix.set(i, j);
                }
            }
        }

        int width = resMatrix.getWidth();
        int height = resMatrix.getHeight();
        BufferedImage image = new BufferedImage(width, height,
                BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, resMatrix.get(x, y) ? BLACK
                        : WHITE);
            }
        }
        return image;
    }


    public static InputStream resourceLoader(String fileFullPath) throws IOException {
        ResourceLoader resourceLoader = new DefaultResourceLoader();
        return resourceLoader.getResource(fileFullPath).getInputStream();
    }


}