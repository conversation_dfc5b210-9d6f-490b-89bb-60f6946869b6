package com.wlgb.service2;

import com.wlgb.entity.FwqThbD3c;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

public interface FwqThbD3cService {
    void save(FwqThbD3c fwqThbD3c);

    FwqThbD3c queryFwqThbD3cByLsh(String lsh);

    FwqThbD3c selectOne(FwqThbD3c fwqThbD3c);

    void update(Map<String, Object> map);

    void delete(Map<String, Object> map);

    void queryCCGCscthbd1c(String V_QRSJ_S1_14, String V_QRSJ_S2_14);
    void queryCCGCscthbd2c(String V_QRSJ_S1_14, String V_QRSJ_S2_14);

}
