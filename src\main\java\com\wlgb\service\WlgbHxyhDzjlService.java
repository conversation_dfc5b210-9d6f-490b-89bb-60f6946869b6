package com.wlgb.service;

import com.wlgb.entity.WlgbHxyhDzjl;

import java.util.List;

public interface WlgbHxyhDzjlService {
    void save(WlgbHxyhDzjl wlgbHxyhDzjl);

    void updateById(WlgbHxyhDzjl wlgbHxyhDzjl);

    WlgbHxyhDzjl queryByYsBhAndLsBh(String ysBh, String lsBh);

    List<WlgbHxyhDzjl> queryBdj();

    List<WlgbHxyhDzjl> queryListByWlgbHxyhDzjl(WlgbHxyhDzjl wlgbHxyhDzjl);

    WlgbHxyhDzjl queryById(String id);
}
