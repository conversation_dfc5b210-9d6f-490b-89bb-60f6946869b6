package com.wlgb.config;

import com.itextpdf.text.Document;
import com.itextpdf.text.RectangleReadOnly;
import com.itextpdf.text.pdf.PdfException;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerFontProvider;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.jpedal.PdfDecoder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class PDFUtil {
    /**
     * 预加载、预处理，节省生成PDF和img的时间
     */
    private static final XMLWorkerFontProvider FONT_PROVIDER = new XMLWorkerFontProvider(XMLWorkerFontProvider.DONTLOOKFORFONTS );

    static {
        // 预加载字体到字体提供者
        String fontPath = FileConfig.getFileAbsolutePath2("static" + File.separator + "SimSun.ttf");
        FONT_PROVIDER.register(fontPath);
    }


    /**
     * 使用给定的数据渲染模板，并返回渲染结果。
     *
     * @param data    要在模板中使用的数据（键值对）。
     * @param htmlTmp HTML模板的文件路径。
     * @return 渲染后的结果字符串。
     * @throws IOException                           读取模板文件或目录时发生错误。
     * @throws freemarker.template.TemplateException 处理模板时发生错误。
     */
    public static String freeMarkerRender(Map<String, Object> data, String htmlTmp) throws IOException, freemarker.template.TemplateException {
        Configuration cfg = new Configuration();
        cfg.setDefaultEncoding("UTF-8");
        // 创建一个StringWriter来存储渲染的输出。
        Writer out = new StringWriter();
        try {
            // 获取包含模板的目录。
            File templateDir = getTemplateDirectory(htmlTmp);
            // 设置用于加载模板的目录。
            cfg.setDirectoryForTemplateLoading(templateDir);
            // 从目录中加载特定模板。
            Template template = cfg.getTemplate(getTemplateName(htmlTmp));
            // 使用给定的数据处理模板，并将其写入StringWriter。
            template.process(data, out);
            // 确保所有内容已写入，刷新StringWriter。
            out.flush();
            // 返回渲染后的结果。
            return out.toString();
        } catch (Exception e) {
            // 在ERROR级别记录错误。
            log.error("*****进入渲染html请求  渲染模板时出错*******{}", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException ex) {
                // 记录关闭StringWriter时的错误。
                log.error("关闭StringWriter时出错: ", ex);
            }
        }
        return null;
    }

    /**
     * 从模板路径中获取包含模板的目录。
     *
     * @param htmlTmp HTML模板的文件路径。
     * @return 包含模板的目录File对象。
     * @throws IOException 读取目录时发生错误。
     */
    private static File getTemplateDirectory(String htmlTmp) throws IOException {
        int lastSlashIndex = htmlTmp.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            lastSlashIndex = htmlTmp.lastIndexOf('\\');
        }
        if (lastSlashIndex == -1) {
            log.error("无效的模板路径------------------------ ");
            throw new IOException("无效的模板路径: " + htmlTmp);
        }
        String dirPath = htmlTmp.substring(0, lastSlashIndex);
        File dir = new File(dirPath);
        if (!dir.exists() || !dir.isDirectory()) {
            log.error("模板目录不存在------------------------ ");
            throw new IOException("模板目录不存在: " + dirPath);
        }
        return dir;
    }

    /**
     * 从模板路径中提取模板文件名。
     *
     * @param htmlTmp HTML模板的文件路径。
     * @return 模板文件名。
     */
    private static String getTemplateName(String htmlTmp) {
        int lastSlashIndex = htmlTmp.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            lastSlashIndex = htmlTmp.lastIndexOf('\\');
        }
        return htmlTmp.substring(lastSlashIndex + 1);
    }

    public static void createPdf(String content, String dest,boolean isnew) throws Exception {
        log.info("*****进入createPdf方法*********{}", dest);
        // step 1: 初始化Document并设置页面大小
        RectangleReadOnly readOnly = new RectangleReadOnly(800F, 1150F);
        System.out.println(isnew+"宽度---------------"+readOnly.getWidth()+"------------------"+readOnly.getHeight());
        Document document = new Document();
        document.setPageSize(new com.itextpdf.text.Rectangle(readOnly)); // 动态设置页面大小

        // step 2: 创建PdfWriter实例并关联Document和输出流
        FileOutputStream fos = new FileOutputStream(dest);
        PdfWriter writer = PdfWriter.getInstance(document, fos);
        try {
            // step 3: 打开Document以开始写入
            document.open();
            // step 4: 使用预加载的字体解析并转换HTML内容到PDF
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8));
            XMLWorkerHelper xw = XMLWorkerHelper.getInstance();
            log.info("*****进入createPdf方法*xwxwxwxw*{}", xw);
            xw.parseXHtml(writer, document, byteArrayInputStream, null, StandardCharsets.UTF_8, FONT_PROVIDER);
            log.info("*****转换HTML到PDF完成*****");
        } catch (Exception e) {
            log.info("生成PDF文件失败了", e);
        } finally {
            // step 5: 关闭Document和Writer
            if (document.isOpen()) {
                document.close();
            }
            log.info("*****进入createPdf方法*******进入finally2222");
        }
    }


    /**
     * @param imagePath
     * @param pdfPath
     * @param pageNum   页数
     */
    public static void pdfToImg(String pdfPath, int pageNum, String imagePath) throws PdfException {
        log.info("*****进入pdfToImg方法***imagePath******{}", imagePath);
        // 创建PdfDecoder实例
        PdfDecoder pdfDecoder = new PdfDecoder(true);
        try {
            // 打开PDF文件并获取PdfDecoder对象
            pdfDecoder.openPdfFile(pdfPath);
            // 获取指定页码的PDF页面作为图像
            BufferedImage img = pdfDecoder.getPageAsImage(pageNum);
            // 将图像写入到指定的输出路径
            ImageIO.write(img, "png", new File(imagePath));
        } catch (IOException | org.jpedal.exception.PdfException e) {
            log.info("*****进入pdfToImg方法*****报错啦****{}", e);
        } finally {
            // 确保在所有情况下关闭PdfDecoder，避免资源泄露
            pdfDecoder.closePdfFile();
        }
    }

    public static void createPdfDhRJXyd(String content, String dest) throws Exception {
        // step 1
        Document document = new Document(new RectangleReadOnly(1000F, 1530F));
        // step 2
        PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(dest));
        // step 3
        document.open();
        // step 4
        XMLWorkerFontProvider fontImp = new XMLWorkerFontProvider(XMLWorkerFontProvider.DONTLOOKFORFONTS);
        String fileAbsolutePath2 = FileConfig.getFileAbsolutePath2("static" + File.separator + "SimSun.ttf");
        fontImp.register(fileAbsolutePath2);
        XMLWorkerHelper.getInstance().parseXHtml(writer, document,
                new ByteArrayInputStream(content.getBytes("utf8")), null, Charset.forName("utf8"), fontImp);
        // step 5
//
        document.close();
        writer.close();
    }


    public static void createPdf1(String content, String dest, Integer x, Integer y) throws Exception {
        // step 1
        Document document = new Document(new RectangleReadOnly(x, y));
        // step 2
        PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(dest));
        // step 3
        document.open();
        // step 4
        XMLWorkerFontProvider fontImp = new XMLWorkerFontProvider(XMLWorkerFontProvider.DONTLOOKFORFONTS);
        String fileAbsolutePath2 = FileConfig.getFileAbsolutePath2("static" + File.separator + "SimSun.ttf");
        fontImp.register(fileAbsolutePath2);
        XMLWorkerHelper.getInstance().parseXHtml(writer, document,
                new ByteArrayInputStream(content.getBytes("utf8")), null, Charset.forName("utf8"), fontImp);
        // step 5
//
        document.close();
        writer.close();
    }

}
