package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbKdjJzxCzjl;
import com.wlgb.mapper.TbKdjJzxCzjlMapper;
import com.wlgb.service2.TbKdjJzxCzjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27 16:04
 */
@Service
@DS(value = "second")
public class TbKdjJzxCzjlServiceImpl implements TbKdjJzxCzjlService {
    @Resource
    private TbKdjJzxCzjlMapper tbKdjJzxCzjlMapper;

    @Override
    public void save(TbKdjJzxCzjl tbKdjJzxCzjl) {
        tbKdjJzxCzjlMapper.insertSelective(tbKdjJzxCzjl);
    }
}
