package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 协议单操作日志
 * @Author: jeecg-boot
 * @Date:   2020-10-16
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_xyd_log")
public class WlgbXydLog {
	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String xlid;
	/**操作用户id*/
    private java.lang.String xluserid;
	/**操作用户姓名*/
    private java.lang.String xlname;
	/**操作记录*/
    private java.lang.String xltext;
	/**操作时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date xltime;
	/**协议单id*/
    private java.lang.String xlxid;
}
