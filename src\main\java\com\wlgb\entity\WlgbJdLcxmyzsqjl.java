package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 乐诚项目预支申请记录
 * @Author: jeecg-boot
 * @Date:   2022-07-31
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_lcxmyzsqjl")
public class WlgbJdLcxmyzsqjl {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**审批编号*/
    private java.lang.String spbh;
	/**审批标题*/
    private java.lang.String spbt;
	/**申请时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date sqsj;
	/**审批结束时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date spjssj;
	/**申请人*/
    private java.lang.String sqr;
	/**申请人id*/
    private java.lang.String sqrid;
	/**申请人部门*/
    private java.lang.String sqrbm;
	/**申请人部门id*/
    private java.lang.String sqrbmid;
	/**预支类型*/
    private java.lang.String yzlx;
	/**门店类型*/
    private java.lang.String mdlx;
	/**费用一级分类*/
    private java.lang.String fyyjfl;
	/**费用类别*/
    private java.lang.String fylb;
	/**预支款用途（原因）*/
    private java.lang.String yzkyt;
	/**预计抵预支时间*/
    private java.lang.String yjdyzsj;
	/**累积欠款*/
    private java.lang.Double ljqk;
	/**本次预支金额*/
    private java.lang.Double bcyzje;
	/**银行卡户主姓名*/
    private java.lang.String yhkhzxm;
	/**收款账号*/
    private java.lang.String skzh;
	/**开户行*/
    private java.lang.String khh;
	/**出纳转账时间*/
    private java.lang.String cnzzsj;
	/**出纳转账金额*/
    private java.lang.Double cnzzje;
	/**是否录入金蝶*/
    private java.lang.Integer sflrjd;
	/**实例id*/
    private java.lang.String slid;
}
