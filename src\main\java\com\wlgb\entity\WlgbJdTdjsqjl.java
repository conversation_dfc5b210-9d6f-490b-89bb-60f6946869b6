package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 退定金申请记录
 * @Author: jeecg-boot
 * @Date:   2022-04-08
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_tdjsqjl")
public class WlgbJdTdjsqjl {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private String id;
	/**创建人*/
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
	/**更新人*/
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
	/**所属部门*/
    private String sysOrgCode;
	/**审批编号*/
    private String spbh;
	/**费用类别*/
    private String fylb;
	/**别墅名称*/
    private String bsmc;
	/**协议单编号*/
    private String ddbh;
	/**定金类型*/
    private String djlx;
	/**申请人*/
    private String sqr;
	/**申请人id*/
    private String sqrid;
	/**申请人部门*/
    private String sqrbm;
	/**申请人部门*/
    private String sqrbmid;
	/**是否删单*/
    private String sfsd;
	/**收款客户姓名*/
    private String khmc;
	/**收款客户账号*/
    private String khskzh;
	/**是否删除（0：否，1：是）*/
    private Integer sfsc;
	/**是否录入金蝶（0：否，1：是）*/
    private Integer sflrjd;
	/**实例标题*/
    private String slbt;
	/**实例id*/
    private String slid;
	/**打款金额*/
    private Double dkje;
	/**打款日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date dkrq;
    /**科目名称*/
    private String kmmc;
    /**科目编码*/
    private String kmbm;
    /**账套名称*/
    private String ztmc;
    /**账套编码*/
    private String ztbm;
    /**定金表实例id*/
    private String sjslid;
    /**支付交易单号*/
    private String zfjydh;
    /**备注*/
    private java.lang.String bz;
}
