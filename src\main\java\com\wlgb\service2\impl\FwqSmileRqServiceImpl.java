package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqSmileRq;
import com.wlgb.mapper.FwqSmileRqMapper;
import com.wlgb.service2.FwqSmileRqService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@DS(value = "second")
public class FwqSmileRqServiceImpl implements FwqSmileRqService {
    @Resource
    private FwqSmileRqMapper fwqSmileRqMapper;

    @Override
    public void save(FwqSmileRq fwqSmilerq) {
        fwqSmileRqMapper.insertSelective(fwqSmilerq);
    }

    @Override
    public FwqSmileRq selectJTOne(String userid) {
        Example example = new Example(FwqSmileRq.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userid", userid);
        //是否有效
        criteria.andEqualTo("sfyx", "0");
        // 获取当前日期的开始时间（0点）
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date startDate = calendar.getTime();
        // 获取当前日期的结束时间（24点）
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date endDate = calendar.getTime();
        criteria.andBetween("sctime", startDate, endDate);
        return fwqSmileRqMapper.selectOneByExample(example);
    }

    @Override
    public List<FwqSmileRq> selectAllByUserid(String userid) {
        Example example = new Example(FwqSmileRq.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userid", userid);
        example.setOrderByClause("sctime DESC");
        return fwqSmileRqMapper.selectByExample(example);
    }
}
