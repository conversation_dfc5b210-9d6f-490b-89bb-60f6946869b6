package com.wlgb.service;

import com.wlgb.entity.WlgbJdSpCwlrjdsj;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 21:19
 */
public interface WlgbJdSpCwlrjdsjService {
    void save(WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj);

    void updateById(WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj);

    List<WlgbJdSpCwlrjdsj> queryByTypeAndSpBhAndSfScJd(Integer type, String spbh, Integer sfScJd);

    List<WlgbJdSpCwlrjdsj> queryListByWlgbJdSpCwlrjdsj(WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj);

    List<WlgbJdSpCwlrjdsj> queryByTypeAndSpBhAndSfScJdAndBm(Integer type, String spbh, Integer sfScJd, String bm);
}
