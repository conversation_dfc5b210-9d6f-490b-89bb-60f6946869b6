package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.service.*;
import com.wlgb.entity.WlgbJdDjLsjlb;
import com.wlgb.entity.WlgbJdDjbbd;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service.WlgbJdDjbbdService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.wlgb.config.Tools.isEmpty;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月17日 20:37
 */

@RestController
@RequestMapping(value = "/wlgb/money")
@Slf4j
public class MoneyController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private WlgbJdDjbbdService wlgbJdDjbbdService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private WlgbDjsljlccService wlgbDjsljlccService;
    @Autowired
    private OssFileService ossFileService;
    @Autowired
    private WlgbHxyhFqskService wlgbHxyhFqskService;
    @Autowired
    private WlgbJdDjLsjlbService wlgbJdDjLsjlbService;

    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;


    /**
     * 定金表单提交新增至数据库
     */
    @RequestMapping(value = "/djbBdSave")
    public Result djbBdSave(HttpServletRequest request) {
        logger.info("*****收到djbBdSave请求*******");
        String datas = request.getParameter("datas");
        if (!Optional.ofNullable(datas).isPresent()) {
            return Result.error("datas为空");
        }
        String formInstId = request.getParameter("formInstId");
        if (!Optional.ofNullable(formInstId).isPresent()) {
            return Result.error("formInstId为空");
        }
        JSONObject jsonObject = JSON.parseObject(datas);
        WlgbJdDjbbd wlgbJdDjbbd = jsonObject.toJavaObject(WlgbJdDjbbd.class);
        if (!Optional.ofNullable(wlgbJdDjbbd).isPresent()) {
            return Result.error("对象类型为空");
        }
        Double je = wlgbJdDjbbd.getJe();
        if (je < 0.0) {
            return Result.error("金额不能小于0");
        }
        if (wlgbJdDjbbd.getDjwybs() != null && !"".equals(wlgbJdDjbbd.getDjwybs())) {
            Integer count = wlgbJdDjbbdService.queryCountByWybs(wlgbJdDjbbd.getDjwybs());
            if (count > 0) {
                return Result.error("唯一标识的数据已存在，不允许重复");
            }
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        paramMap.put("formInstId", formInstId);
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/djbBdSave", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 定金表单修改
     */
    @RequestMapping(value = "/djbBdUpdate")
    public Result djbBdUpdate(HttpServletRequest request) {
        logger.info("*****收到djbBdUpdate请求*******");
        String datas = request.getParameter("datas");
        if (!Optional.ofNullable(datas).isPresent()) {
            return Result.error("datas为空");
        }
        String formInstId = request.getParameter("formInstId");
        if (!Optional.ofNullable(formInstId).isPresent()) {
            return Result.error("实例id为空");
        }
        String userid = request.getParameter("userid");
        if (userid == null || "".equals(userid)) {
            return Result.error("数据修改人为空");
        }
        JSONObject jsonObject = JSON.parseObject(datas);

        WlgbJdDjbbd wlgbDjbbd = jsonObject.toJavaObject(WlgbJdDjbbd.class);
        if (!Optional.ofNullable(wlgbDjbbd).isPresent()) {
            return Result.error("对象类型为空");
        }
//        Integer count = wlgbJdDjbbdService.queryCountByLshAndFormId(wlgbDjbbd.getLsh(), formInstId);
//        if (count == 0) {
//            return Result.error("数据库找不到数据！");
//        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        paramMap.put("formInstId", formInstId);
        paramMap.put("userid", userid);
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/djbBdUpdateTaSk", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 定金表数据显示
     */
    @RequestMapping(value = "djbList")
    public Result djbList(HttpServletRequest request) {
        String search = request.getParameter("searchKey");
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        String fqrId = request.getParameter("fqrId");
        String khdh = request.getParameter("khdh");
        String orderColumn = request.getParameter("orderColumn");
        String orderType = request.getParameter("orderType");
        PageHelp pageHelp = new PageHelp(Integer.valueOf((currentPage != null && !"".equals(currentPage) ? currentPage : "1")), Integer.valueOf((pageSize != null && !"".equals(pageSize) ? pageSize : "10")));
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        if (search != null && !"".equals(search) && search.length() > 0) {
            map.put("search", search);
        }
        if (Optional.ofNullable(orderColumn).isPresent()) {
            map.put("orderColumn", orderColumn);
        }
        if (Optional.ofNullable(orderType).isPresent()) {
            map.put("orderType", orderType);
        }
        if (khdh != null && !"".equals(khdh)) {
            map.put("khdh", khdh);
        }
        if (fqrId != null && !"".equals(fqrId)) {
            map.put("fqrid", fqrId);
        }
        PageHelpUtil helpUtil = weiLianDdXcxService.QueryMtXsList(map);
        helpUtil = PageConfig.pageHelpUtil(helpUtil, pageHelp);

        return Result.OK(helpUtil);
    }


    /**
     * 短租回款列表
     */
    @RequestMapping(value = "dzHkList")
    public Result dzHkList(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (!Optional.ofNullable(datas).isPresent()) {
            return Result.error("datas数据为空");
        }
        JSONObject jsonObject = JSON.parseObject(datas);
        JSONArray jsonArray = JSONArray.parseArray(jsonObject.getString("list"));
        if (!Optional.ofNullable(jsonArray).isPresent()) {
            return Result.error("数组数据为空");
        }


        Map<String, Object> map = new HashMap<>();
        map.put("list", jsonArray.size() != 0 ? jsonArray : null);
        List<WlgbJdDjbbd> list1 = weiLianDdXcxService.queryDjbDataByFormIdList(map);
        if (list1.size() == 0) {
            return Result.error("找不到正确的定金数据");
        }
        boolean b = false;
        StringBuilder djLsh = new StringBuilder();
        for (int i = 0; i < list1.size(); i++) {
            WlgbJdDjbbd wlgbJdDjbbd = list1.get(i);
            WlgbDjsljlcc wlgbDjsljlcc = wlgbDjsljlccService.queryByTypeAndDdBh(1, wlgbJdDjbbd.getLsh());
            if (wlgbDjsljlcc != null) {
                WlgbDjsljlcc wlgbDjsljlcc1 = new WlgbDjsljlcc();
                wlgbDjsljlcc1.setType("2");
                wlgbDjsljlcc1.setId(wlgbDjsljlcc.getId());
                wlgbDjsljlccService.updateById(wlgbDjsljlcc1);
            }

            djLsh.append(wlgbJdDjbbd.getLsh());
            if (i != list1.size() - 1) {
                djLsh.append(",");
            }

            if (wlgbJdDjbbd.getSfhk() == 1) {
                b = true;
            }
        }
        if (b) {
            return Result.error("选择的定金里面有完成回款，请重新选择");
        }

        String userid = jsonObject.getString("userid");
        if (!Optional.ofNullable(userid).isPresent()) {
            return Result.error("userid为空");
        }

        Double je = jsonObject.getDouble("je");
        if (je <= 0) {
            return Result.error("总金额不能小于等于0");
        }
        BigDecimal bigDecimal = new BigDecimal(je);
        bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
        je = bigDecimal.doubleValue();

        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        System.out.println(jsonArray);


        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Calendar cal = Calendar.getInstance();
        String format = df1.format(new Date());
        String url = "http://wlgbsk.qianquan888.com/weilian-dingdingdzxcx" + "/wlgb/wlgbJdDjbbd/DBsmFk?skbh=" + format;
        //收款二维码
        String ewm = null;
        try {
            ewm = scEwm1(url, null, format, df2.format(cal.getTime()), je.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        WlgbDjsljlcc wlgbDjsljlcc = new WlgbDjsljlcc();
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        wlgbDjsljlcc.setSkbh(format);
        wlgbDjsljlcc.setCzr(dingdingEmployee != null ? dingdingEmployee.getName() : null);
        wlgbDjsljlcc.setCzrid(dingdingEmployee != null ? dingdingEmployee.getUserid() : null);
        wlgbDjsljlcc.setSkje(je);
        wlgbDjsljlcc.setEwm(ewm);
        wlgbDjsljlcc.setType("1");
        wlgbDjsljlcc.setId(IdConfig.uuId());
        wlgbDjsljlcc.setSkmc("短租回款");
        wlgbDjsljlcc.setDdbh(djLsh.toString());

        wlgbDjsljlccService.save(wlgbDjsljlcc);

        List<JSONObject> finalEwm = YdConfig.setTpList(ewm, "短租回款" + format);
        String finalEwm1 = ewm;
        jsonArray.forEach(l -> {
            String formid = l.toString();
            WlgbJdDjbbd wlgbJdDjbbd = wlgbJdDjbbdService.queryByFormIdAndDjLx(formid, 2);
            if (wlgbJdDjbbd == null) {
                return;
            }
            wlgbJdDjbbd.setEwm(finalEwm1);
            wlgbJdDjbbd.setSkbh(format);
            wlgbJdDjbbdService.updateById(wlgbJdDjbbd);

            JSONObject jsonObject2 = new JSONObject();
            jsonObject2.put("ewm", finalEwm);
            jsonObject2.put("skbh", format);
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            String token = null;
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            for (int i = 0; i < 5; i++) {
                GatewayResult gatewayResult = null;
                try {
                    gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, "012412221639786136545", formid, jsonObject2.toJSONString());
                } catch (Exception e) {
                    gatewayResult = new GatewayResult();
                    e.printStackTrace();
                }
                if (!gatewayResult.getSuccess()) {
                    if (i == 4) {
                        try {
                            String context1 = "发起短租回款同步表单出错了，收款编号：" + format + "，错误原因：" + gatewayResult.toString();
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    break;
                }
            }
        });
        return Result.OK(ewm);
    }


    //短租回款扫码付款
    @RequestMapping(value = "dzHkSmFk")
    public void dzHkSmFk(@RequestParam(name = "skbh", defaultValue = "") String skbh,
                         HttpServletRequest request, HttpServletResponse response) {
        WlgbDjsljlcc djsljlcc = wlgbDjsljlccService.queryByTypeAndSkBh(1, skbh);
        if (djsljlcc != null) {
            Double je = djsljlcc.getSkje();
            String skmc = djsljlcc.getSkmc();
            String userid = djsljlcc.getCzrid();
            WlgbHxyhFqsk wlgbHxyhFqsk = new WlgbHxyhFqsk();
            wlgbHxyhFqsk.setTjsj(new Date());
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
            wlgbHxyhFqsk.setTjr(employee != null ? employee.getName() : null);
            wlgbHxyhFqsk.setTjrid(employee != null ? employee.getUserid() : null);
            wlgbHxyhFqsk.setJe(je);
            wlgbHxyhFqsk.setDdbh(skbh);
            wlgbHxyhFqsk.setFymc(skmc);
            String userAgent = request.getHeader("user-agent");

            boolean b = true;
            int count = wlgbHxyhFqskService.queryByDdBhAndZfZt(skbh, "是");
            if (count > 0) {
                b = false;
            }
            if (b) {
                if (userAgent != null && userAgent.contains("MicroMessenger")) {
                    WlgbHxyhFqsk hxyhFqsk = wlgbHxyhFqskService.queryByDdBhAndFkFs(skbh, "微信");
                    if (hxyhFqsk == null) {
                        skbh = skbh + "WX";
                        String redirectUrl = "http://wlgbsk.qianquan888.com/weilian-dingdingdzxcx" + "/wlgb" +
                                "/wlgbJdDjbbd/wxHqCode?ddbh=" + skbh + "&je=" + je + "&skmc=" + skmc + "&type=2";
                        //这里要将你的授权回调地址处理一下，否则微信识别不了
                        //UTF-8编码
                        String redirectUri = null;
                        try {
                            redirectUri = URLEncoder.encode(redirectUrl, "UTF-8");
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                        //简单获取openid的话参数response_type与scope与state参数固定写死即可
                        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx3136bf5ad7ad7bd6"
                                + "&redirect_uri="
                                + redirectUri + "&response_type=code&scope="
                                + "snsapi_base" + "&state=STATE#wechat_redirect";
                        wlgbHxyhFqsk.setEwm(url);
                        wlgbHxyhFqsk.setFkfs("微信");
                        wlgbHxyhFqskService.save(wlgbHxyhFqsk);
                        try {
                            response.sendRedirect(url);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    } else {
                        try {

                            response.sendRedirect(hxyhFqsk.getEwm());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    System.out.println("微信支付");

                } else if (userAgent != null && userAgent.contains("AlipayClient")) {
                    WlgbHxyhFqsk hxyhFqsk = wlgbHxyhFqskService.queryByDdBhAndFkFs(skbh, "支付宝");
                    if (hxyhFqsk == null) {
                        skbh = skbh + "ZFB";
                        System.out.println("支付宝支付");
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("ddbh", skbh);
                        jsonObject.put("je", je);
                        jsonObject.put("fkfs", "1903000");
                        jsonObject.put("skmc", skmc);
                        JSONObject result = YsPayConfig.ewmPay1(jsonObject);
                        wlgbHxyhFqsk.setFkfs("支付宝");
                        if (result != null) {
                            String sourceQrCodeUrl = result.getString("source_qr_code_url");
                            try {
                                response.sendRedirect(sourceQrCodeUrl);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                            wlgbHxyhFqsk.setEwm(sourceQrCodeUrl);
                            wlgbHxyhFqsk.setYxbh(result.getString("trade_no"));
                        }
                        wlgbHxyhFqskService.save(wlgbHxyhFqsk);
                    } else {
                        try {
                            response.sendRedirect(hxyhFqsk.getEwm());
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    try {
                        response.setContentType("text/html; charset=UTF-8");
                        response.getWriter().print("<html><body><script type='text/javascript'>alert('请使用微信或支付宝扫码！');</script></body></html>");
                        response.getWriter().close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }


            } else {
                try {
                    response.setContentType("text/html; charset=UTF-8");
                    response.getWriter().print("<html><body><script type='text/javascript'>alert('该订单已支付，请勿重复支付！');</script></body></html>");
                    response.getWriter().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            System.out.println(userAgent);
        } else {
            try {
                response.setContentType("text/html; charset=UTF-8");
                response.getWriter().print("<html><body><script type='text/javascript'>alert('该二维码已失效，请重新生成！');</script></body></html>");
                response.getWriter().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 用户扫码授权跳转发起支付订单(定金与短租回款)
     */
    @RequestMapping(value = "wxHqCode")
    public void wxHqCode(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String code = request.getParameter("code");
        String ddbh = request.getParameter("ddbh");
        String je = request.getParameter("je");
        String skmc = request.getParameter("skmc");
        String type = request.getParameter("type");
        System.out.println("code：" + code);

        Map<String, String> params = new HashMap<>();
        params.put("secret", "681ca33470657c426e50847b0e60ab3a");
        params.put("appid", "wx3136bf5ad7ad7bd6");
        params.put("grant_type", "authorization_code");
        params.put("code", code);
        String result = HttpClientUtil.get("https://api.weixin.qq.com/sns/oauth2/access_token", params);
        JSONObject jsonObject = JSONObject.parseObject(result);
        String openid = jsonObject.getString("openid");
        System.out.println(openid);
        WlgbHxyhFqsk hxyhFqsk = wlgbHxyhFqskService.queryByDdBhAndFkFs(ddbh.replace("WX", ""), "微信");
        boolean b = true;
        WlgbHxyhFqsk wlgbHxyhFqsk = new WlgbHxyhFqsk();
        if (hxyhFqsk != null) {
            if (hxyhFqsk.getAppid() != null && !"".equals(hxyhFqsk.getAppid())) {
                String url = "https://yinshengdj.qianquan888.com/ysdjtb/wxPay.html?appId=" + hxyhFqsk.getAppid() +
                        "&timeStamp=" + hxyhFqsk.getTimestamp() +
                        "&nonceStr=" + hxyhFqsk.getNoncestr() +
                        "&package=" + hxyhFqsk.getPackage1() +
                        "&paySign=" + hxyhFqsk.getPaysign();
                response.sendRedirect(url);
                b = false;
            } else {
                wlgbHxyhFqsk.setId(hxyhFqsk.getId());
            }

        }
        if (b) {
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("ddbh", ddbh);
            jsonObject1.put("je", je);
            jsonObject1.put("skmc", skmc);
            jsonObject1.put("openid", openid);
            JSONObject jsonObject2;
            if ("2".equals(type)) {
                jsonObject2 = YsPayConfig.weixinPay1(jsonObject1);
            } else {
                jsonObject2 = YsPayConfig.weiXinPay(jsonObject1, ddxturl);
            }
            if (jsonObject2 != null && jsonObject2.size() > 0) {
                JSONObject payInfo = jsonObject2.getJSONObject("jsapi_pay_info");
                wlgbHxyhFqsk.setYxbh(jsonObject2.getString("trade_no"));
                wlgbHxyhFqsk.setDdbh(ddbh.replace("WX", ""));
                if (payInfo != null && payInfo.size() > 0) {
                    String url = "https://yinshengdj.qianquan888.com/ysdjtb/wxPay.html?appId=" + payInfo.getString("appId") +
                            "&timeStamp=" + payInfo.getString("timeStamp") +
                            "&nonceStr=" + payInfo.getString("nonceStr") +
                            "&package=" + payInfo.getString("package") +
                            "&paySign=" + payInfo.getString("paySign");
                    wlgbHxyhFqsk.setAppid(payInfo.getString("appId"));
                    wlgbHxyhFqsk.setTimestamp(payInfo.getString("timeStamp"));
                    wlgbHxyhFqsk.setNoncestr(payInfo.getString("nonceStr"));
                    wlgbHxyhFqsk.setPackage1(payInfo.getString("package"));
                    wlgbHxyhFqsk.setPaysign(payInfo.getString("paySign"));
                    if (hxyhFqsk != null) {
                        wlgbHxyhFqskService.updateById(wlgbHxyhFqsk);
                    } else {
                        wlgbHxyhFqskService.save(wlgbHxyhFqsk);
                    }
                    response.sendRedirect(url);
                } else {
                    if (hxyhFqsk != null) {
                        wlgbHxyhFqskService.updateById(wlgbHxyhFqsk);
                    } else {
                        wlgbHxyhFqskService.save(wlgbHxyhFqsk);
                    }
                }
            }
        }
    }

    /**
     * 短租线下未回款列表
     */
    @RequestMapping("/dzWhkList")
    public Result dzWhkList(HttpServletRequest request) {
        String search = request.getParameter("searchKey");
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        String fqrId = request.getParameter("fqrId");
        if (!Optional.ofNullable(fqrId).isPresent()) {
            return Result.error("发起人id为空");
        }
        String orderColumn = request.getParameter("orderColumn");
        String orderType = request.getParameter("orderType");
        PageHelp pageHelp = new PageHelp(Integer.valueOf((currentPage != null && !"".equals(currentPage) ? currentPage : "1")), Integer.valueOf((pageSize != null && !"".equals(pageSize) ? pageSize : "10")));
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        if (search != null && !"".equals(search) && search.length() > 0) {
            map.put("searchKey", search);
        }
        if (Optional.ofNullable(orderColumn).isPresent()) {
            map.put("orderColumn", orderColumn);
        }
        if (Optional.ofNullable(orderType).isPresent()) {
            map.put("orderType", orderType);
        }
        map.put("fqrid", fqrId);
        PageHelpUtil helpUtil = weiLianDdXcxService.queryDzWhkList(map);
        helpUtil = PageConfig.pageHelpUtil(helpUtil, pageHelp);
        return Result.OK(helpUtil);
    }

    /**
     * 线上定金列表
     */
    @RequestMapping(value = "queryXsDj")
    public Result queryXsDj(HttpServletRequest request) {
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        String userid = request.getParameter("userid");
        String khdh = request.getParameter("khdh");
        String sfxyld = request.getParameter("sfxyld");
        String sfxd = request.getParameter("sfxd");
        String ddbh = request.getParameter("ddbh");
        String sspt = request.getParameter("sspt");
        String name = request.getParameter("name");
        PageHelp pageHelp = new PageHelp(Integer.valueOf((currentPage != null && !"".equals(currentPage) ? currentPage : "1")), Integer.valueOf((pageSize != null && !"".equals(pageSize) ? pageSize : "10")));
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        if (userid != null && !"".equals(userid)) {
            map.put("userid", userid);
        }
        if (name != null && !"".equals(name)) {
            map.put("name", name);
        }
        if (khdh != null && !"".equals(khdh)) {
            map.put("khdh", khdh);
        }
        if (sfxyld != null && !"".equals(sfxyld)) {
            map.put("sfxyld", sfxyld);
        }
        if (sfxd != null && !"".equals(sfxd)) {
            map.put("sfxd", sfxd);
        }
        if (ddbh != null && !"".equals(ddbh)) {
            map.put("ddbh", ddbh);
        }
        if (sspt != null && !"".equals(sspt)) {
            map.put("sspt", sspt);
        }
        PageHelpUtil helpUtil = weiLianDdXcxService.queryXsDj(map);
        helpUtil = PageConfig.pageHelpUtil(helpUtil, pageHelp);
        return Result.OK(helpUtil);
    }

    /**
     * 提交美团验券
     */
    @RequestMapping(value = "tjYq")
    public Result tjYq(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userId = request.getParameter("userId");
        String qmcd = request.getParameter("qmcd");
        String sfmt = request.getParameter("sfmt");
        if (isEmpty(datas) || isEmpty(userId)) {
            return Result.OK("参数不能为空");
        }
        log.info("------验券的参数----datas==={}", datas);
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String qm = jsonObject.getString("smqm");
        if (qm.length() > 13) {
            return Result.OK("券码长度不能大于13位");
        }
        String url = "/ysdjtb/wlgb/task/mtYqTaSk";
        if (!isEmpty(sfmt)) {
            url = "/ysdjtb/wlgb/mtyq/prepareDj";
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        paramMap.put("userId", userId);
        paramMap.put("qmcd", qmcd);
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + url, paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 提交美团验券----店长提交尾款
     */
    @RequestMapping(value = "tjYqBjDj")
    public Result tjYqBjDj(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        String userId = request.getParameter("userId");
        String qmcd = request.getParameter("qmcd");
        String sfmt = request.getParameter("sfmt");
        if (isEmpty(datas) || isEmpty(userId)) {
            return Result.OK("参数不能为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String qm = jsonObject.getString("smqm");
        if (qm.length() > 13) {
            return Result.OK("券码长度不能大于13位数字");
        }
        String url = "/ysdjtb/wlgb/task/mtYqBjDjTaSk";
        if (!isEmpty(sfmt)) {
            url = "/ysdjtb/wlgb/mtyq/prepareWk";
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        paramMap.put("userId", userId);
        paramMap.put("qmcd", qmcd);
        System.out.println("uuid========mtYqBjDjTaSk============" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + url, paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 页面点击生成线下定金二维码
     */
    @RequestMapping(value = "/ywySendSkDj")
    public Result ywySendSkDj(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (!Optional.ofNullable(datas).isPresent()) {
            return Result.error("datas为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        WlgbJdDjLsjlb wlgbJdDjLsjlb = jsonObject.toJavaObject(WlgbJdDjLsjlb.class);
        if (!Optional.ofNullable(wlgbJdDjLsjlb).isPresent()) {
            return Result.OK("临时定金表无数据");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/ywySendSkDjTaSk", paramMap);
        } catch (IOException e) {
            logger.info("****************页面点击生成线下定金二维码*************{}", e);
        }
        return Result.OK();
    }

    /**
     *定义一个接口，处理扫码后判断支付方式的逻辑
     */
    @RequestMapping(value = "pdFs")
    public void pdFs(@RequestParam(name = "skbh", defaultValue = "") String skbh,
                     HttpServletRequest request, HttpServletResponse response) {
        // 查询临时定金记录表中对应收款编号且未失效的数据
        WlgbJdDjLsjlb wlgbJdDjLsjlb = wlgbJdDjLsjlbService.queryBySkBhAndSfSc(skbh, 0);
        // 如果存在对应的定金记录，则继续处理
        if (wlgbJdDjLsjlb != null) {
            // 根据收款编号查询支付流水信息
            WlgbDjsljlcc djsljlcc = wlgbDjsljlccService.queryBySkBh(skbh);
            // 如果存在支付流水信息
            if (djsljlcc != null) {
                // 获取金额、收款名称、操作人ID
                Double je = djsljlcc.getSkje();
                String skmc = djsljlcc.getSkmc();
                String userid = djsljlcc.getCzrid();

                // 创建支付请求对象，并设置基本信息
                WlgbHxyhFqsk wlgbHxyhFqsk = new WlgbHxyhFqsk();
                wlgbHxyhFqsk.setTjsj(new Date()); // 设置提交时间

                // 根据用户ID查询钉钉员工信息
                DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
                // 设置提交人姓名和ID
                wlgbHxyhFqsk.setTjr(employee != null ? employee.getName() : null);
                wlgbHxyhFqsk.setTjrid(employee != null ? employee.getUserid() : null);

                // 设置支付金额、订单编号、付款描述
                wlgbHxyhFqsk.setJe(je);
                wlgbHxyhFqsk.setDdbh(skbh);
                wlgbHxyhFqsk.setFymc(skmc);

                // 获取客户端 User-Agent 判断浏览器类型
                String userAgent = request.getHeader("user-agent");

                boolean b = true;
                // 检查该订单是否已经完成支付
                int count = wlgbHxyhFqskService.queryByDdBhAndZfZt(skbh, "是");
                if (count > 0) {
                    b = false; // 已支付则不允许重复支付
                }

                // 如果允许支付
                if (b) {
                    // 如果是微信浏览器
                    if (userAgent != null && userAgent.contains("MicroMessenger")) {
                        // 查询是否已生成过微信支付链接
                        WlgbHxyhFqsk hxyhFqsk = wlgbHxyhFqskService.queryByDdBhAndFkFs(skbh, "微信");
                        // 如果没有生成过
                        if (hxyhFqsk == null) {
                            // 修改订单编号标识为微信支付（追加 WX）
                            skbh = skbh + "WX";
                            // 构造微信授权回调地址
                            String redirectUrl = ddxturl + "/ysdjtb/wlgb/money/wxHqCode?ddbh=" + skbh + "&je=" + je +
                                    "&skmc=" + skmc + "&type=1";

                            String redirectUri = null;
                            try {
                                // 对URL进行UTF-8编码
                                redirectUri = URLEncoder.encode(redirectUrl, "UTF-8");
                            } catch (UnsupportedEncodingException e) {
                                e.printStackTrace();
                            }

                            // 构造微信OAuth2授权URL
                            String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx3136bf5ad7ad7bd6"
                                    + "&redirect_uri="
                                    + redirectUri + "&response_type=code&scope="
                                    + "snsapi_base" + "&state=STATE#wechat_redirect";

                            // 设置支付链接和支付方式为微信
                            wlgbHxyhFqsk.setEwm(url);
                            wlgbHxyhFqsk.setFkfs("微信");
                            wlgbHxyhFqskService.save(wlgbHxyhFqsk); // 保存支付请求记录

                            try {
                                // 跳转到微信支付页面
                                response.sendRedirect(url);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        } else {
                            try {
                                // 如果已生成过微信支付链接，直接跳转至原链接
                                response.sendRedirect(hxyhFqsk.getEwm());
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                        System.out.println("微信支付");

                    }
                    // 如果是支付宝客户端
                    else if (userAgent != null && userAgent.contains("AlipayClient")) {
                        // 查询是否已生成过支付宝支付链接
                        WlgbHxyhFqsk hxyhFqsk = wlgbHxyhFqskService.queryByDdBhAndFkFs(skbh, "支付宝");
                        // 如果没有生成过
                        if (hxyhFqsk == null) {
                            // 修改订单编号标识为支付宝支付（追加 ZFB）
                            skbh = skbh + "ZFB";
                            System.out.println("支付宝支付");

                            // 构造支付参数JSON
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("ddbh", skbh);
                            jsonObject.put("je", je);
                            jsonObject.put("fkfs", "1903000"); // 支付宝支付标识码
                            jsonObject.put("skmc", skbh + skmc); // 支付描述

                            // 调用银盛支付接口生成二维码
                            JSONObject result = YsPayConfig.ewmPay(jsonObject, ddxturl);
                            wlgbHxyhFqsk.setFkfs("支付宝");

                            // 如果有返回结果
                            if (result != null) {
                                // 获取二维码地址
                                String sourceQrCodeUrl = result.getString("source_qr_code_url");
                                try {
                                    // 跳转到支付宝二维码页面
                                    response.sendRedirect(sourceQrCodeUrl);
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                                // 设置二维码地址和交易号
                                wlgbHxyhFqsk.setEwm(sourceQrCodeUrl);
                                wlgbHxyhFqsk.setYxbh(result.getString("trade_no"));
                            }
                            // 保存支付请求记录
                            wlgbHxyhFqskService.save(wlgbHxyhFqsk);
                        } else {
                            try {
                                // 如果已生成过支付宝支付链接，直接跳转至原链接
                                response.sendRedirect(hxyhFqsk.getEwm());
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    } else {
                        // 非微信或支付宝客户端时提示错误
                        try {
                            response.setContentType("text/html; charset=UTF-8");
                            response.getWriter().print("<html><body><script type='text/javascript'>alert('请使用微信或支付宝扫码！');</script></body></html>");
                            response.getWriter().close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    // 如果订单已支付，提示勿重复支付
                    try {
                        response.setContentType("text/html; charset=UTF-8");
                        response.getWriter().print("<html><body><script type='text/javascript'>alert('该订单已支付，请勿重复支付！');</script></body></html>");
                        response.getWriter().close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                System.out.println(userAgent); // 打印User-Agent
            } else {
                // 如果支付流水信息为空，提示重新生成二维码
                try {
                    response.setContentType("text/html; charset=UTF-8");
                    response.getWriter().print("<html><body><script type='text/javascript'>alert('该二维码存在问题，请重新生成！');</script></body></html>");
                    response.getWriter().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } else {
            // 如果二维码不存在或已失效，提示重新生成
            try {
                response.setContentType("text/html; charset=UTF-8");
                response.getWriter().print("<html><body><script type='text/javascript'>alert('该二维码已失效，请重新生成！');</script></body></html>");
                response.getWriter().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 定金到账回调通知处理数据
     */
    @RequestMapping(value = "djDz")
    public Result djDz(SelectVo selectVo) {
        System.out.println("定金到账回调通知处理数据" + selectVo.getAccount_date());
        if (!"TRADE_SUCCESS".equals(selectVo.getTrade_status())) {
            System.out.println("不是支付成功的订单！");
            return Result.OK();
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", JSONObject.toJSONString(selectVo));
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/djDzTaSk", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 根据流水号查询定金记录信息
     */
    @RequestMapping(value = "queryDjJl")
    public Result queryDjJl(HttpServletRequest request) {
        String lsh = request.getParameter("lsh");
        if (lsh == null || "".equals(lsh)) {
            return Result.error("流水号是空的");
        }
        WlgbJdDjbbd wlgbJdDjbbd = weiLianDdXcxService.queryDjJlByLsh(lsh);
        if (wlgbJdDjbbd == null) {
            return Result.error("数据空的");
        }


        return Result.OK(wlgbJdDjbbd);
    }

    /**
     * 手机号码验证
     */
    @RequestMapping(value = "regPhone")
    public Result regPhone(HttpServletRequest request) {
        String phone = request.getParameter("phone");
        String userid = request.getParameter("userid");
        if (!Optional.ofNullable(phone).isPresent()) {
            return Result.error("手机号码为空");
        }
        String sfz = request.getParameter("sfz");
        //判断身份信息是否为空
        if (!Optional.ofNullable(sfz).isPresent()) {
            return Result.error("身份证信息为空");
        }

        List<WlgbJdDjLsjlb> wlgbJdDjLsjlbList = wlgbJdDjLsjlbService.queryListByKhDhAndSfScAndSfDz(phone, 0, 0, null);
        if (wlgbJdDjLsjlbList.size() > 1) {
            return Result.error("出现重复记录");
        } else if (wlgbJdDjLsjlbList.size() == 0) {
            return Result.error("没有开单记录");
        }
        WlgbJdDjLsjlb wlgbJdDjLsjlb = wlgbJdDjLsjlbList.get(0);

        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("wybs", IdConfig.uuId());
        jsonObject1.put("khdh", phone);
        jsonObject1.put("khsfz", sfz);
        jsonObject1.put("textField_l42uptow", wlgbJdDjLsjlb.getSkbh());
        jsonObject1.put("textField_l42uptox", wlgbJdDjLsjlb.getLsh());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_J15SHV6W8MP3BI9SY7TL");
        ydAppkey.setToken("CFYJVHDVC9SM4CAD1K2F3BZP5NMA2Q1W6A2KKBC1");
        try {
            DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObject1.toJSONString(), userid != null && !"".equals(userid) ? userid : "012412221639786136545", "FORM-2O8661C1UMK0ZZY2BJ6SQAC1C4AF2IZUFOT3LN4");
        } catch (Exception e) {
            e.printStackTrace();
        }
        wlgbJdDjLsjlb.setSfz(sfz);
        wlgbJdDjLsjlbService.updateById(wlgbJdDjLsjlb);
        return Result.OK(wlgbJdDjLsjlb);
    }


    /**
     * 手机号码验证
     */
    @RequestMapping(value = "regPhone2")
    public Result regPhone2(HttpServletRequest request) {
        String phone = request.getParameter("phone");
        String userid = request.getParameter("userid");
        if (!Optional.ofNullable(phone).isPresent()) {
            return Result.error("手机号码为空");
        }
        String sfz = request.getParameter("sfz");
        //判断身份信息是否为空
        if (!Optional.ofNullable(sfz).isPresent()) {
            return Result.error("身份证信息为空");
        }
        List<WlgbJdDjLsjlb> list = new ArrayList<>();
        List<WlgbJdDjLsjlb> wlgbJdDjLsjlbList = wlgbJdDjLsjlbService.queryListByKhDhAndSfScAndSfDz(phone, 0, 0, 1);
        if (!wlgbJdDjLsjlbList.isEmpty()) {
            for (WlgbJdDjLsjlb wlgbJdDjLsjlb : wlgbJdDjLsjlbList) {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("wybs", IdConfig.uuId());
                jsonObject1.put("khdh", phone);
                jsonObject1.put("khsfz", sfz);
                jsonObject1.put("textField_l42uptow", wlgbJdDjLsjlb.getSkbh());
                jsonObject1.put("textField_l42uptox", wlgbJdDjLsjlb.getLsh());
                Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
                String token = null;
                try {
                    token = DingToken.token(dingkey);
                } catch (ApiException e) {
                    logger.info("*******获取token失败********{}", e);
                }
                YdAppkey ydAppkey = new YdAppkey();
                ydAppkey.setAppkey("APP_J15SHV6W8MP3BI9SY7TL");
                ydAppkey.setToken("CFYJVHDVC9SM4CAD1K2F3BZP5NMA2Q1W6A2KKBC1");
                try {
                    //提交验证表单实例
                    DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObject1.toJSONString(), userid != null && !"".equals(userid) ? userid : "012412221639786136545", "FORM-2O8661C1UMK0ZZY2BJ6SQAC1C4AF2IZUFOT3LN4");
                } catch (Exception e) {
                    logger.info("*******新增表单实例********{}", e);
                }
                wlgbJdDjLsjlb.setSfz(sfz);
                wlgbJdDjLsjlbService.updateById(wlgbJdDjLsjlb);
                list.add(wlgbJdDjLsjlb);
            }
            return Result.OK(list);
        } else {
            return Result.error("当前手机号不存在订单");
        }
    }


    /**
     * 根据流水号查询定金临时记录表
     */
    @RequestMapping(value = "queryDjLsJlByLsh")
    public Result queryDjLsJlByLsh(HttpServletRequest request) {
        String lsh = request.getParameter("lsh");
        if (!Optional.ofNullable(lsh).isPresent()) {
            return Result.error("流水号为空");
        }
        WlgbJdDjLsjlb wlgbJdDjLsjlb = wlgbJdDjLsjlbService.queryByLshAndSfScAndDjLxAndSfDz(lsh, 0, 1, 0);
        if (!Optional.ofNullable(wlgbJdDjLsjlb).isPresent()) {
            return Result.error("没有查询到该数据");
        }
        return Result.OK(wlgbJdDjLsjlb);
    }

    /**
     * 生成上传二维码
     *
     * @param url 二维码访问地址
     * @return 阿里云地址
     */
    public String scEwm1(String url, String fkfs, String ddbh, String dqTime, String je) throws Exception {
        File file = new File("D:\\tomcat9\\fkm.jpg");
        try {
            ImageUtil.drawString1(url, fkfs, ddbh, dqTime, je, file.getPath(), PathUtil.getClassResources() + "/static/view/img/fkm.png", PathUtil.getClassResources() + "/static/view/img/weilian_logo.jpg");
        } catch (Exception e) {
            e.printStackTrace();
        }

        FileInputStream fileInputStream = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
        if (fileInputStream != null) {
            fileInputStream.close();
        }
        String upload = ossFileService.upload(multipartFile);
        if (upload != null && !"".equals(upload)) {
            upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
        }
        File file1 = new File("D:\\tomcat9\\fkewm.jpg");
        if (file1.exists()) {
            file1.delete();
        }
        file.delete();
        return upload;
    }
}
