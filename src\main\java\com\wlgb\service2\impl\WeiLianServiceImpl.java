package com.wlgb.service2.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.config.*;
import com.wlgb.entity.vo.FwqBbb;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.*;
import com.wlgb.mapper1.WeiLianMapper;
import com.wlgb.service2.WeiLianService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月21日 17:07
 */
@Service
@DS(value = "second")
public class WeiLianServiceImpl implements WeiLianService {
    @Resource
    private WeiLianMapper weiLianMapper;

    @Override
    public List<TbVilla> queryTodayWcMd(Map<String, Object> map) {
        return weiLianMapper.queryTodayWcMd(map);
    }

    @Override
    public Integer queryCountXydSfZd(TbXyd tbXyd) {
        return weiLianMapper.queryCountXydSfZd(tbXyd);
    }

    @Override
    public Integer queryCountXydSfZd2(TbXyd tbXyd) {
        return weiLianMapper.queryCountXydSfZd2(tbXyd);
    }

    @Override
    public Integer queryCountWlgbYlxdSfZd(WlgbYlxd wlgbYlxd) {
        return weiLianMapper.queryCountWlgbYlxdSfZd(wlgbYlxd);
    }

    @Override
    public Integer queryCountTbYddXydSfZd(TbYddXyd tbYddXyd) {
        return weiLianMapper.queryCountTbYddXydSfZd(tbYddXyd);
    }

    @Override
    public Integer queryPdCc(Map<String, Object> map) {
        return weiLianMapper.queryPdCc(map);
    }

    @Override
    public Double queryKdjZe(Map<String, Object> map) {
        return weiLianMapper.queryKdjZe(map);
    }

    @Override
    public VillaIDsUtil queryIdsByVid(String vid) {
        return weiLianMapper.queryIdsByVid(vid);
    }

    @Override
    public TbKhlyxz queryKhLyXzByLidOrLname(String khxz) {
        return weiLianMapper.queryKhLyXzByLidOrLname(khxz);
    }

    @Override
    public FwqBbb2 queryBbb2ByXm(String xm) {
        return weiLianMapper.queryBbb2ByXm(xm);
    }

    @Override
    public FwqBbb queryBbbByXfd(String xfd) {
        return weiLianMapper.queryBbbByXfd(xfd);
    }

    @Override
    public void zxZjCc(String xid) {
        weiLianMapper.zxZjCc(xid);
    }

    @Override
    public String queryKdjZy() {
        return weiLianMapper.queryKdjZy();
    }

    @Override
    public Map<String, Object> queryKdjXx(Map<String, Object> map) {
        return weiLianMapper.queryKdjXx(map);
    }

    @Override
    public Integer queryXydMothCountByXfd(String xfd) {
        return weiLianMapper.queryXydMothCountByXfd(xfd);
    }

    @Override
    public Integer queryXydDateCountByXfd(String xfd) {
        return weiLianMapper.queryXydDateCountByXfd(xfd);
    }

    @Override
    public Integer queryXydYesCountByXfd(String xfd) {
        return weiLianMapper.queryXydYesCountByXfd(xfd);
    }

    @Override
    public Double queryXdYjByXfd(String xfd) {
        return weiLianMapper.queryXdYjByXfd(xfd);
    }

    @Override
    public void zxczgc() {
        weiLianMapper.zxczgc();
    }

    @Override
    public FwqBbb3 queryBmYj(FwqBbb2 fwqBbb2) {
        return weiLianMapper.queryBmYj(fwqBbb2);
    }

    @Override
    public Double queryKdjJzj(Map<String, Object> map) {
        return weiLianMapper.queryKdjJzj(map);
    }

    @Override
    public List<String> queryHpsAt(String cs) {
        return weiLianMapper.queryHpsAt(cs);
    }

    @Override
    public Integer queryXfdXdZfTs(String xfd) {
        return weiLianMapper.queryXfdXdZfTs(xfd);
    }

    @Override
    public Integer queryQrdSjZfTs(String xfd) {
        return weiLianMapper.queryQrdSjZfTs(xfd);
    }

    @Override
    public Integer queryBsSfZn(String xfd) {
        return weiLianMapper.queryBsSfZn(xfd);
    }

    @Override
    public Integer queryFdZn(String xfd) {
        return weiLianMapper.queryFdZn(xfd);
    }

    @Override
    public TbQrd queryQrdByXidAndSfSc(String xid) {
        return weiLianMapper.queryQrdByXidAndSfSc(xid);
    }

    @Override
    public FwqQunid queryQun(String city) {
        return weiLianMapper.queryQun(city);
    }

    @Override
    public DingdingEmployee queryDingDingByName(String name) {
        return weiLianMapper.queryDingDingByName(name);
    }

    @Override
    public DingdingEmployee queryDingDingById(String id) {
        return weiLianMapper.queryDingDingById(id);
    }

    @Override
    public Integer queryBsUserIdSfJms(String userId) {
        return weiLianMapper.queryBsUserIdSfJms(userId);
    }

    @Override
    public List<Select> queryXdBsXlJms(String userId) {
        return weiLianMapper.queryXdBsXlJms(userId);
    }

    @Override
    public List<Select> queryXdBsXl() {
        return weiLianMapper.queryXdBsXl();
    }

    @Override
    public List<Select> queryXdLyOrXzXl(Integer type) {
        return weiLianMapper.queryXdLyOrXzXl(type);
    }

    @Override
    public List<Select> queryXdHdCh() {
        return weiLianMapper.queryXdHdCh();
    }

    @Override
    public PageHelpUtil queryYldList(Map<String, Object> map) {
        List<WlgbYlxd> list = weiLianMapper.queryYldList(map);
        Integer count = weiLianMapper.queryYldCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public List<Select> queryVillaCity() {
        return weiLianMapper.queryVillaCity();
    }

    @Override
    public PageHelpUtil queryScXydList(Map<String, Object> map) {
        List<TbXyd> list = weiLianMapper.queryScXydList(map);
        Integer count = weiLianMapper.queryScXydCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public PageHelpUtil queryScQrdList(Map<String, Object> map) {
        List<TbQrdVo> list = weiLianMapper.queryScQrdList(map);
        Integer count = weiLianMapper.queryScQrdCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public QyUtil queryQyUtil(Map<String, Object> map) {
        return weiLianMapper.queryQyUtil(map);
    }

    @Override
    public List<DkBbJqr> queryDkBb(Map<String, Object> map) {
        return weiLianMapper.queryDkBb(map);
    }

    @Override
    public TbDk queryDkXq(String dkddbh) {
        return weiLianMapper.queryDkXq(dkddbh);
    }

    @Override
    public ZwAndDz queryZwAndDz(String vid) {
        return weiLianMapper.queryZwAndDz(vid);
    }

    @Override
    public List<BsZw> queryBsMcIdZw(Map<String, Object> map) {
        return weiLianMapper.queryBsMcIdZw(map);
    }

    @Override
    public TbVilla queryVillaByVname(String vname) {
        return weiLianMapper.queryVillaByVname(vname);
    }

    @Override
    public List<TbVilla> queryKyQnMdList(Map<String, Object> map) {
        return weiLianMapper.queryKyQnMdList(map);
    }

    @Override
    public Integer queryKyQnMdCount(Map<String, Object> map) {
        return weiLianMapper.queryKyQnMdCount(map);
    }

    @Override
    public List<CrmCs> queryCrmCsList() {
        return weiLianMapper.queryCrmCsList();
    }

    @Override
    public List<CrmKhzt> queryCrmKhZtList(Integer lxid) {
        return weiLianMapper.queryCrmKhZtList(lxid);
    }

    @Override
    public Integer queryCrmDhSfcz(Map<String, Object> map) {
        return weiLianMapper.queryCrmDhSfcz(map);
    }

    @Override
    public Integer queryCrmWxhCount(String qkhwxh) {
        return weiLianMapper.queryCrmWxhCount(qkhwxh);
    }

    @Override
    public Integer queryCrmHmCount(String dhhm) {
        return weiLianMapper.queryCrmHmCount(dhhm);
    }

    @Override
    public List<CrmCsfq> queryCrmCsSfqByCsName(String csName) {
        return weiLianMapper.queryCrmCsSfqByCsName(csName);
    }

    @Override
    public String queryCrmBbh() {
        return weiLianMapper.queryCrmBbh();
    }

    @Override
    public List<CrmQbkh> queryCrmDataImport(Map<String, Object> map) {
        return weiLianMapper.queryCrmDataImport(map);
    }

    @Override
    public PageHelpUtil queryCrmData(Map<String, Object> map) {
        List<CrmQbkh> list = weiLianMapper.queryCrmDataImport(map);
        Integer count = weiLianMapper.queryCrmDataImportCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public Integer queryCmrCsFqIdByFqName(String fqName) {
        return weiLianMapper.queryCmrCsFqIdByFqName(fqName);
    }

    @Override
    public List<CrmBmxq> queryCrmBmCxByBmDa(String bmDa) {
        return weiLianMapper.queryCrmBmCxByBmDa(bmDa);
    }
    @Override
    public CrmBmxq queryCrmBmCxByUserid(String userid) {
        return weiLianMapper.queryCrmBmCxByUserid(userid);
    }

    @Override
    public List<CrmQbkh> queryCrmCcSjByCcData(String cc) {
        return weiLianMapper.queryCrmCcSjByCcData(cc);
    }

    @Override
    public List<CrmCsfq> queryCrmCsFqList() {
        return weiLianMapper.queryCrmCsFqList();
    }

    @Override
    public List<Map<String, Object>> querySxDzZbDz() {
        return weiLianMapper.querySxDzZbDz();
    }

    @Override
    public List<Map<String, Object>> queryYxbXdWzf() {
        return weiLianMapper.queryYxbXdWzf();
    }

    @Override
    public List<DingdingEmployee> queryDingdingEmployeeList() {
        return weiLianMapper.queryDingdingEmployeeList();
    }

    @Override
    public List<HrMsz> queryHrYcSj() {
        return weiLianMapper.queryHrYcSj();
    }

    @Override
    public List<CrmUser> queryCrmUserList() {
        return weiLianMapper.queryCrmUserList();
    }

    @Override
    public void qkCrmBmXqTable() {
        weiLianMapper.qkCrmBmXqTable();
    }

    @Override
    public void qkCrmBmTable() {
        weiLianMapper.qkCrmBmTable();
    }

    @Override
    public void qkCrmUserBackupsTable() {
        weiLianMapper.qkCrmUserBackupsTable();
    }

    @Override
    public void bfCrmUserInBackups() {
        weiLianMapper.bfCrmUserInBackups();
    }

    @Override
    public void qkCrmUserCopyTable() {
        weiLianMapper.qkCrmUserCopyTable();
    }

    @Override
    public void saveCrmUserCopy(CrmUser crmUser) {
        weiLianMapper.saveCrmUserCopy(crmUser);
    }

    @Override
    public void delCrmUserCopy() {
        weiLianMapper.delCrmUserCopy();
    }

    @Override
    public void qkCrmUserTable() {
        weiLianMapper.qkCrmUserTable();
    }

    @Override
    public void saveCrmUser() {
        weiLianMapper.saveCrmUser();
    }

    @Override
    public void saveDingdingEmployee(Demployee de) {
        weiLianMapper.saveDingdingEmployee(de);
    }

    @Override
    public void updateDingdingEmployee(Demployee de) {
        weiLianMapper.updateDingdingEmployee(de);
    }

    @Override
    public void delDingdingEmployee(String userid) {
        weiLianMapper.delDingdingEmployee(userid);
    }

    @Override
    public Tbperson queryTbpersonById(String id) {
        return weiLianMapper.queryTbpersonById(id);
    }

    @Override
    public void saveTbperson(Tbperson tp) {
        weiLianMapper.saveTbperson(tp);
    }

    @Override
    public void updateTbperson(Tbperson tp) {
        weiLianMapper.updateTbperson(tp);
    }

    @Override
    public SysUser querySysUserById(String id) {
        return weiLianMapper.querySysUserById(id);
    }

    @Override
    public void saveSysUser(SysUser su) {
        weiLianMapper.saveSysUser(su);
    }

    @Override
    public void updateSysUser(SysUser su) {
        weiLianMapper.updateSysUser(su);
    }

    @Override
    public Integer queryFsDzbdSl() {
        return weiLianMapper.queryFsDzbdSl();
    }

    @Override
    public Integer querySyDdSl() {
        return weiLianMapper.querySyDdSl();
    }

    @Override
    public Integer queryHrJrDmByYyName(String name) {
        return weiLianMapper.queryHrJrDmByYyName(name);
    }

    @Override
    public Integer queryHrJrTgByYyName(String name) {
        return weiLianMapper.queryHrJrTgByYyName(name);
    }

    @Override
    public List<Select> queryKdjVilla(Map<String, Object> map) {
        return weiLianMapper.queryKdjVilla(map);
    }

    @Override
    public TbKdj queryTbKdjByBsAndJcAndTc(Map<String, Object> map) {
        return weiLianMapper.queryTbKdjByBsAndJcAndTc(map);
    }

    @Override
    public List<TbKdj> queryYyKdj(Map<String, Object> map) {
        return weiLianMapper.queryYyKdj(map);
    }

    @Override
    public Integer querySfMs(String vid) {
        return weiLianMapper.querySfMs(vid);
    }

    @Override
    public Map<String, Object> zxKdjPlXg(Map<String, Object> map) {
        return weiLianMapper.zxKdjPlXg(map);
    }

    @Override
    public List<Select> queryCsFqSelect() {
        return weiLianMapper.queryCsFqSelect();
    }

    @Override
    public List<Select> queryCsJqSelect() {
        return weiLianMapper.queryCsJqSelect();
    }

    @Override
    public List<Select> queryBbCsSelect() {
        return weiLianMapper.queryBbCsSelect();
    }

    @Override
    public List<Select> queryFgsSelect() {
        return weiLianMapper.queryFgsSelect();
    }

    @Override
    public PageHelpUtil queryBsWhList(Map<String, Object> map) {
        List<TbVillaVo> list = weiLianMapper.queryBsWhList(map);
        Integer count = weiLianMapper.queryBsWhListCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public List<TbVillaVo> queryBsListXz(Map<String, Object> map) {
        return weiLianMapper.queryBsListXz(map);
    }

    @Override
    public PageHelpUtil queryFqWhList(Map<String, Object> map) {
        List<TbCsfqVo> list = weiLianMapper.queryFqWhList(map);
        Integer count = weiLianMapper.queryFqWhListCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public PageHelpUtil queryJqWhList(Map<String, Object> map) {
        List<TbCsjqVo> list = weiLianMapper.queryJqWhList(map);
        Integer count = weiLianMapper.queryJqWhListCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public List<JSONObject> queryXhsAndDyXdYj(Map<String, Object> map) {
        return weiLianMapper.queryXhsAndDyXdYj(map);
    }

    @Override
    public List<JSONObject> queryXhsAndDyXFYj(Map<String, Object> map) {
        return weiLianMapper.queryXhsAndDyXFYj(map);
    }

    @Override
    public List<Select> queryGwLb() {
        return weiLianMapper.queryGwLb();
    }

    @Override
    public List<Select> queryGw(String id) {
        return weiLianMapper.queryGw(id);
    }

    @Override
    public List<Select> queryQd() {
        return weiLianMapper.queryQd();
    }

    @Override
    public List<Select> queryWx() {
        return weiLianMapper.queryWx();
    }

    @Override
    public List<Select> queryJlJd() {
        return weiLianMapper.queryJlJd();
    }

    @Override
    public PageHelpUtil queryXyGw(Map<String, Object> map) {
        List<Map<String, Object>> list = weiLianMapper.queryXyGw(map);
        Integer count = weiLianMapper.queryXyGwCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public PageHelpUtil queryZtGw(Map<String, Object> map) {
        List<Map<String, Object>> list = weiLianMapper.queryZtGw(map);
        Integer count = weiLianMapper.queryZtGwCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public Integer queryJrDm(String name) {
        return weiLianMapper.queryJrDm(name);
    }

    @Override
    public Integer queryJrTg(String name) {
        return weiLianMapper.queryJrTg(name);
    }

    @Override
    public List<Map<String, Object>> queryBmZpQk(Map<String, Object> map) {
        return weiLianMapper.queryBmZpQk(map);
    }

    @Override
    public List<Map<String, Object>> queryBmFqSg(Map<String, Object> map) {
        return weiLianMapper.queryBmFqSg(map);
    }

    @Override
    public Map<String, Object> queryBmZpQkByFzr(Map<String, Object> map) {
        return weiLianMapper.queryBmZpQkByFzr(map);
    }

    @Override
    public Map<String, Object> queryBmFqSgByFzr(Map<String, Object> map) {
        return weiLianMapper.queryBmFqSgByFzr(map);
    }

    @Override
    public List<JSONObject> queryXydXxByVidThreeDay(Map<String, Object> map) {
        return weiLianMapper.queryXydXxByVidThreeDay(map);
    }

    @Override
    public JSONObject queryCyQyOneByQyName(String qy) {
        return weiLianMapper.queryCyQyOneByQyName(qy);
    }

    @Override
    public List<Hpsl> queryHpSlCgMonth() {
        return weiLianMapper.queryHpSlCgMonth();
    }

    @Override
    public DingdingDepartment queryDingdingDeparTment(String bmid) {
        return weiLianMapper.queryDingdingDeparTment(bmid);
    }

    @Override
    public void saveDingdingDeparTment(Department de) {
        weiLianMapper.saveDingdingDeparTment(de);
    }

    @Override
    public void updateDingdingDeparTment(Department de) {
        weiLianMapper.updateDingdingDeparTment(de);
    }

    @Override
    public void delDingdingDeparTment(String dingdingid) {
        weiLianMapper.delDingdingDeparTment(dingdingid);
    }

    @Override
    public Integer queryXdSd(String xddh, String xdxm) {
        return weiLianMapper.queryXdSd(xddh, xdxm);
    }


    @Override
    public List<FwqWnl> queryWnl(String nian, String yue) {
        return weiLianMapper.queryWnl(nian, yue);
    }

    @Override
    public List<FwqThbD9c> queryDyDataMonth() {
        return weiLianMapper.queryDyDataMonth();
    }

    @Override
    public void saveFwqXdjcDzmrlc(FwqXdjcDzmrlc fc) {
        weiLianMapper.saveFwqXdjcDzmrlc(fc);
    }

    @Override
    public List<FwqXdjcDzmrlc> queryFwqXdjcDzmrlc() {
        return weiLianMapper.queryFwqXdjcDzmrlc();
    }

    @Override
    public void saveFwqXdjcDzmrlcFsjl(FwqXdjcDzmrlcFsjl fc) {
        weiLianMapper.saveFwqXdjcDzmrlcFsjl(fc);
    }

    @Override
    public List<FwqXdjcDzmrlcFsjl> queryFwqXdjcDzmrlcFsjl(Map<String, Object> map) {
        return weiLianMapper.queryFwqXdjcDzmrlcFsjl(map);
    }


}
