package com.wlgb.mapper1;

import com.wlgb.entity.Csry;
import com.wlgb.entity.vo.DdhfUtil;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WeiLianDaiBanMapper {
    Csry queryCsRyByUserId(@Param("userid") String userid);

    Csry queryCsRyJmByUserId(@Param("userid") String userid);

    Integer queryNotFsHf(Map<String, Object> map);

    void qkDbLb();

    /**
     * 发送回访列表
     *
     * @param map
     * @return
     */
    List<DdhfUtil> queryNotKfList(Map<String, Object> map);

    /**
     * 发送回访列表count
     *
     * @param map
     * @return
     */
    Integer queryNotKfListCount(Map<String, Object> map);

    /**
     * 发送回访列表
     *
     * @param map
     * @return
     */
    List<Map<String, Object>> queryNotKfListYwjl(Map<String, Object> map);

    /**
     * 发送回访列表count
     *
     * @param map
     * @return
     */
    Integer queryNotKfListCountYwjl(Map<String, Object> map);

    void updateHfZt(@Param("xid") String xid);

    void zjZxr(Map<String, Object> map);
}
