package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbKdjZk;
import com.wlgb.mapper.TbKdjZkMapper;
import com.wlgb.service2.TbKdjZkService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月13日 23:47
 */
@Service
@DS(value = "second")
public class TbKdjZkServiceImpl implements TbKdjZkService {
    @Resource
    private TbKdjZkMapper tbKdjZkMapper;

    @Override
    public TbKdjZk queryByCcAndSfSc(Integer cc, String sfsc) {
        Example example = new Example(TbKdjZk.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("cc", cc);
        criteria.andEqualTo("sfsc", sfsc);
        return tbKdjZkMapper.selectOneByExample(example);
    }
}
