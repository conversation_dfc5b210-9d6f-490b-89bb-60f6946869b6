package com.wlgb.service.impl;

import com.wlgb.entity.WlgbChcbdjjl;
import com.wlgb.mapper.WlgbChcbdjjlMapper;
import com.wlgb.service.WlgbChcbdjjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/1 20:32
 */
@Service
public class WlgbChcbdjjlServiceImpl implements WlgbChcbdjjlService {
    @Resource
    private WlgbChcbdjjlMapper wlgbChcbdjjlMapper;

    @Override
    public void save(WlgbChcbdjjl wlgbChcbdjjl) {
        if (wlgbChcbdjjl.getCreateTime() == null) {
            wlgbChcbdjjl.setCreateTime(new Date());
        }
        wlgbChcbdjjlMapper.insertSelective(wlgbChcbdjjl);
    }

    @Override
    public void updateById(WlgbChcbdjjl wlgbChcbdjjl) {
        wlgbChcbdjjl.setUpdateTime(new Date());
        wlgbChcbdjjlMapper.updateByPrimaryKeySelective(wlgbChcbdjjl);
    }

    @Override
    public void deleteById(WlgbChcbdjjl wlgbChcbdjjl) {
        wlgbChcbdjjl.setDeleteTime(new Date());
        wlgbChcbdjjl.setSfsc(1);
        wlgbChcbdjjlMapper.updateByPrimaryKeySelective(wlgbChcbdjjl);
    }

    @Override
    public WlgbChcbdjjl queryWlgbChcbdjjlByWlgbChcbdjjl(WlgbChcbdjjl wlgbChcbdjjl) {
        return wlgbChcbdjjlMapper.selectOne(wlgbChcbdjjl);
    }
}
