package com.wlgb.service;

import com.wlgb.entity.WlgbJdYzsqjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 22:25
 */
public interface WlgbJdYzsqjlService {
    void save(WlgbJdYzsqjl wlgbJdYzsqjl);

    void updateById(WlgbJdYzsqjl wlgbJdYzsqjl);

    WlgbJdYzsqjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd);

    List<WlgbJdYzsqjl> queryListWlgbJdYzsqjl(WlgbJdYzsqjl wlgbJdYzsqjl);
}
