package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 验券记录（包含验券结果是否成功）
 *
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月22日 19:39
 */
@Data
@Table(name = "wlgb_mt_jl")
public class WlgbMtJl {
    /**
     * 主键
     */
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
    /**
     * 创建人
     */
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    private java.lang.String sysOrgCode;
    /**
     * 验券人id
     */
    private java.lang.String yqr;
    /**
     * 验券人姓名
     */
    private java.lang.String yqrxm;
    /**
     * 订单编号
     */
    private java.lang.String ddbh;
    /**
     * 订单id
     */
    private java.lang.String ddid;
    /**
     * 美团券号
     */
    private java.lang.String mtqh;
    /**
     * 验券结果
     */
    private java.lang.String yqjg;
    /**
     * 抵扣金额
     */
    private java.lang.Double dkje;
    /**
     * 验券时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date yqsj;
    /**
     * 美团验券结果
     */
    private java.lang.String mtyqjg;
}
