package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 15:31
 */
@Data
@Table(name = "dhrj_villa")
public class DhrjVilla {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**门店名称*/
    private String vname;
    /**门店编号*/
    private String mdbh;
    /**城市*/
    private String city;
    /**值班管家*/
    private String zbdz;
    /**值班管家id*/
    private String zbdzid;
    /**备注*/
    private String bz;
    /**使用性质*/
    private String vxz;
    /**是否删除(0:否,1:是)*/
    private Integer vsfsc;
}
