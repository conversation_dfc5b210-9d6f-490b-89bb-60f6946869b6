package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlgb.config.PageConfig;
import com.wlgb.config.PageHelp;
import com.wlgb.config.PageHelpUtil;
import com.wlgb.config.Result;
import com.wlgb.entity.Fwqbbb;
import com.wlgb.entity.Fwqthbd1cjtcopy1;
import com.wlgb.service.WeiLianDdXcxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业财一体化接口
 *
 * <AUTHOR>
 * @Date 2025/05/07 11:08
 * @Version 1.0
 */
@RestController
@RequestMapping(value = "/wlgb/ycyth")
@Slf4j
public class YcythController {

    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;

    /**
     * 根据不同的类型查询所有审批
     * <p>
     * type：审批类型，pageNo：当前页数，pageSize：一页多少数据，spbh：审批编号，spbt：审批标题，sqr：申请人，sqrbm：申请人部门，fyssft：费用所属分摊，sflrjd：是否录入金蝶
     *
     * @throws IOException 1
     */
    @GetMapping(value = "querySpList")
    public Result querySpList(HttpServletRequest request) {
        String type = request.getParameter("type");
        String pageNo = request.getParameter("pageNo");
        String pageSize = request.getParameter("pageSize");
        String spbh = request.getParameter("spbh");
        String spbt = request.getParameter("spbt");
        String sqr = request.getParameter("sqr");
        String sqrbm = request.getParameter("sqrbm");
        String fyssft = request.getParameter("fyssft");
        String sflrjd = request.getParameter("sflrjd");
        PageHelp pageHelp = new PageHelp(pageNo != null ? Integer.parseInt(pageNo) : 1, pageSize != null ? Integer.parseInt(pageSize) : 10);
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        map.put("type", type);
        if (spbh != null && !"".equals(spbh)) {
            map.put("spbh", spbh);
        }
        if (spbt != null && !"".equals(spbt)) {
            map.put("spbt", spbt);
        }
        if (sqr != null && !"".equals(sqr)) {
            map.put("sqr", sqr);
        }
        if (sqrbm != null && !"".equals(sqrbm)) {
            map.put("sqrbm", sqrbm);
        }
        if (fyssft != null && !"".equals(fyssft)) {
            map.put("fyssft", fyssft);
        }
        if (sflrjd != null && !"".equals(sflrjd)) {
            map.put("sflrjd", sflrjd);
        }

        PageHelpUtil pageHelpUtil1 = weiLianDdXcxService.querySpList(map);
        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);


        return Result.OK(pageHelpUtil1);
    }


}
