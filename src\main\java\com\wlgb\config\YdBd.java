package com.wlgb.config;

import lombok.Data;

/**
 * @Description: 宜搭表单
 * @Author: jeecg-boot
 * @Date: 2021-01-23
 * @Version: V1.0
 */
@Data
public class YdBd {

    /**
     * 主键
     */
    private java.lang.String id;
    /**
     * 创建人
     */
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    private java.util.Date createTime;
    /**
     * 更新人
     */
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    private java.lang.String sysOrgCode;
    /**
     * 流程Code
     */
    private java.lang.String code;
    /**
     * 表单ID
     */
    private java.lang.String formid;
    /**
     * 备注
     */
    private java.lang.String bz;
}
