package com.wlgb.config;

import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.entity.TbXyd;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/01/10 14:52
 */
public class LcConfig {
    /**
     * 新增宜搭流程容错机制
     *
     * @param tbXyd       协议单对象
     * @param name        操作名称
     * @param formDatamap 数据
     * @param userid      操作人id
     * @param dingkey     钉钉key
     */
    protected static GatewayResult queryYdLcXzRc(TbXyd tbXyd, String name, Map<String, Object> formDatamap, String userid, Dingkey dingkey, YdAppkey ydAppkey, YdBd ydBd) {
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = null;
        for (int i = 0; i < 5; i++) {
            try {
                gatewayResult = lcid(userid, formDatamap, ydAppkey, ydBd, token);
            } catch (Exception e) {
                e.printStackTrace();
                gatewayResult = new GatewayResult();
            }
            if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                if (i == 4) {
                    try {
                        DingQunSend.send("正在" + name + "，新增流程出错了，订单编号：" + tbXyd.getXddbh() + "，错误原因：" + gatewayResult.toString(), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                break;
            }
        }
        return gatewayResult;
    }

    /**
     * 发送流程表单
     *
     * @param userid 收取人id
     * @param map    表单内容
     */
    protected static GatewayResult lcid(String userid, Map<String, Object> map, YdAppkey ydAppkey, YdBd ydBd, String token) {
        JSONObject json = new JSONObject(map);
        GatewayResult gatewayResult;
        try {
            gatewayResult = DingBdLcConfig.fqXzLcSl(token, ydAppkey, userid, ydBd.getFormid(), ydBd.getCode(), json.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
            gatewayResult = new GatewayResult();
        }
        return gatewayResult;
    }

    /**
     * 发送流程表单
     *
     * @param userid 收取人id
     * @param map    表单内容
     */
    public static GatewayResult gxLcSl(String userid, Map<String, Object> map, YdAppkey ydAppkey, String formId,
                                     String token) {
        JSONObject json = new JSONObject(map);
        GatewayResult gatewayResult;
        try {
            gatewayResult = DingBdLcConfig.gxLcSl(token, ydAppkey, userid,
                    json.toJSONString(), formId);
        } catch (Exception e) {
            e.printStackTrace();
            gatewayResult = new GatewayResult();
        }
        return gatewayResult;
    }
}
