package com.wlgb.service.impl;

import com.wlgb.entity.WlgbDzdbjl;
import com.wlgb.mapper.WlgbDzdbjlMapper;
import com.wlgb.service.WlgbDzdbjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/16 16:09
 */
@Service
public class WlgbDzdbjlServiceImpl implements WlgbDzdbjlService {
    @Resource
    private WlgbDzdbjlMapper wlgbDzdbjlMapper;

    @Override
    public void save(WlgbDzdbjl wlgbDzdbjl) {
        wlgbDzdbjl.setCreateTime(new Date());
        wlgbDzdbjlMapper.insertSelective(wlgbDzdbjl);
    }
}
