<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlgb.mapper1.WeiLianDdXcxMapper">
    <!-- 明天到岗 -->
    <select id="queryHrLsZy" resultType="com.wlgb.config.HrLsZy" parameterType="java.util.Map">
        SELECT
        NAME as name, tel as tel, zcity as gw, DaoGangdate as dgsj, MSGname as msg,beizhu as msjg, if(gender = '1', '男',
        '女') as sex, zhusu as zs
        FROM
        hr_msz m
        LEFT JOIN hr_gw_city c ON m.gwcity = c.zcityid
        <where>
            and DATE_FORMAT(DaoGangdate,"%Y%m%d") > DATE_FORMAT(curdate(),"%Y%m%d")
            <if test="gw != null and gw != ''">
                and YXGW = #{gw}
            </if>
            AND mianshi = '已通过'
            and m.sfsc = 0
            and m.jljd = 7
            <if test="xgw != null and xgw != ''">
                and c.zcityid = #{xgw}
            </if>
        </where>
        order by DaoGangdate asc
    </select>

    <!-- 今天面试记录 -->
    <select id="queryHrLsJl" resultType="com.wlgb.config.HrLsZy" parameterType="java.util.Map">
        SELECT
        name as name,c.zcity as gw, m.Msdata as dgsj, j.jljd as msg, mianshi as msjg, beizhu as tel, if(gender = '1',
        '男', '女') as sex, zhusu as zs
        FROM
        hr_msz m
        LEFT JOIN hr_gw_city c ON m.gwcity = c.zcityid
        left join hr_jljd j on m.jljd = j.genderjljd
        <where>
            and DATE_FORMAT(Msdata,"%Y%m%d") = DATE_FORMAT(curdate(),"%Y%m%d")
            <if test="gw != null and gw != ''">
                and YXGW = #{gw}
            </if>
            <if test="xgw != null and xgw != ''">
                and c.zcityid = #{xgw}
            </if>
        </where>
    </select>

    <select id="QueryMtXsList" resultType="com.wlgb.entity.vo.WlgbJdDjbbdVo" parameterType="java.util.Map">
        SELECT lsh,sum(je) as je ,
        REPLACE(if(RIGHT(if(left(GROUP_CONCAT(yqqm),1) =
        ',',substring(GROUP_CONCAT(yqqm),2,LENGTH(GROUP_CONCAT(yqqm))),GROUP_CONCAT(yqqm)),1) = ',',
        substring(if(left(GROUP_CONCAT(yqqm),1) =
        ',',substring(GROUP_CONCAT(yqqm),2,LENGTH(GROUP_CONCAT(yqqm))),GROUP_CONCAT(yqqm)),1,LENGTH(if(left(GROUP_CONCAT(yqqm),1)
        =
        ',',substring(GROUP_CONCAT(yqqm),2,LENGTH(GROUP_CONCAT(yqqm))),GROUP_CONCAT(yqqm)))-1),if(left(GROUP_CONCAT(yqqm),1)
        = ',',substring(GROUP_CONCAT(yqqm),2,LENGTH(GROUP_CONCAT(yqqm))),GROUP_CONCAT(yqqm))), ',,', ',') as yqqm
        ,if(djlx=1,DATE_FORMAT(dzsj,'%Y-%m-%d %H:%i:%s'),DATE_FORMAT(yqsj,'%Y-%m-%d %H:%i:%s')) as qrsj
        ,if(LENGTH(GROUP_CONCAT(DISTINCT djlx)) > 1, 3, GROUP_CONCAT(DISTINCT djlx)) as djlx,ifNULL(sum(IFNULL(qmsj,
        0)), 0) as qmsj,GROUP_CONCAT(form_inst_id) as
        formInstId,sspt,crmbh,khdh,sfxyld,sfbjdjwk,sfdgzz,sfbjdj,fqr_name as fqrName,sspt,sfdgzz
        FROM `wlgb_jd_djbbd`
        where sfsc=0 and sfxd=0 and (yqjg=1 or sfdz=1) and sfbjdj != 2
        <if test="fqrid != null and fqrid != ''">
            and (fqr_id =#{fqrid} )
        </if>
        <if test="khdh != null and khdh != ''">
            and khdh like concat('%', #{khdh}, '%')
        </if>
        <if test="search != null and search != ''">
            and (khdh like concat('%', #{search}, '%') or fqr_name like concat('%', #{search}, '%') )
        </if>
        group by lsh
        order by
        <if test="orderColumn!=null and orderColumn!=''">
            ${orderColumn} ${orderType}
        </if>
        <if test="orderColumn==null or orderColumn==''">
            create_time desc
        </if>
        limit #{help.pageNum},#{help.pageSize}
    </select>
    <select id="CoutMtXsList" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(1)
        from(
        SELECT lsh,sum(je) as je ,GROUP_CONCAT(yqqm) as yqqm,if(djlx=1,dzsj,yqsj) as dzsj
        ,djlx,qmsj,form_inst_id,sspt,crmbh,khdh
        FROM `wlgb_jd_djbbd`
        where sfsc=0 and sfxd=0 and (yqjg=1 or sfdz=1) and sfbjdj != 2
        <if test="fqrid != null and fqrid != ''">
            and fqr_id =#{fqrid}
        </if>
        <if test="khdh != null and khdh != ''">
            and khdh like concat('%', #{khdh}, '%')
        </if>
        <if test="search != null and search != ''">
            and (khdh like concat('%', #{search}, '%') or fqr_name like concat('%', #{search}, '%') )
        </if>
        group by lsh
        ) a
    </select>

    <select id="queryDjbDataByFormIdList" resultType="com.wlgb.entity.WlgbJdDjbbd" parameterType="java.util.Map">
        select * from wlgb_jd_djbbd
        <where>
            and sfsc = 0
            <if test="list != null and list.size() != 0">
                and form_inst_id in
                <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 短租未回款列表-->
    <select id="queryDzWhkList" resultType="com.wlgb.entity.WlgbJdDjLsjlb" parameterType="java.util.Map">
        select *
        from wlgb_jd_dj_lsjlb
        where sfsc=0 and sfhk=0 and djlx=1
        <if test="fqrid!='' and fqrid!=null">
            and fqr_id=#{fqrid}
        </if>
        <if test="searchKey!=null and searchKey!=''">
            and (khdh like concat('%', #{searchKey}, '%') )
        </if>
        group by lsh
        order by
        <if test="orderColumn!=null and orderColumn!=''">
            ${orderColumn} ${orderType}
        </if>
        <if test="orderColumn==null or orderColumn==''">
            create_time desc
        </if>
        limit #{help.pageNum},#{help.pageSize}
    </select>

    <select id="countDzWhkList" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(1)
        from(select *
        from wlgb_jd_dj_lsjlb
        where sfsc=0 and sfhk=0 and djlx=1
        <if test="fqrid!='' and fqrid!=null">
            and fqr_id=#{fqrid}
        </if>
        <if test="searchKey!=null and searchKey!=''">
            and (khdh like concat('%', #{searchKey}, '%') )
        </if>
        group by lsh
        order by
        <if test="orderColumn!=null and orderColumn!=''">
            ${orderColumn} ${orderType}
        </if>
        <if test="orderColumn==null or orderColumn==''">
            create_time desc
        </if>) a
    </select>

    <select id="queryXsDj" resultType="com.wlgb.entity.WlgbJdDjbbd" parameterType="java.util.Map">
        select
        lsh,IFNULL(sum(IFNULL(je,0)),0) je,GROUP_CONCAT(yqqm) yqqm,yqsj,sfxd,khdh,fqsj,IFNULL(sum(IFNULL(ytje,0)),0)
        ytje,sfxyld,djlx,fqr_id,fqr_name,fqr_id,fqr_name,IFNULL(sum(IFNULL(qmsj,0)),0) qmsj,ddbh,sspt,sfhk
        from
        wlgb_jd_djbbd
        where
        sfsc = 0
        and sfscdj = 0
        and djlx = 2
        <if test="userid != null and userid != ''">
            and fqr_id = #{userid}
        </if>
        <if test="name != null and name != ''">
            and fqr_name = #{name}
        </if>
        <if test="khdh != null and khdh != ''">
            and khdh = #{khdh}
        </if>
        <if test="sfxd != null and sfxd != ''">
            and sfxd = #{sfxd}
        </if>
        <if test="sfxyld != null and sfxyld != ''">
            and sfxyld = #{sfxyld}
        </if>
        <if test="ddbh != null and ddbh != ''">
            and ddbh = #{ddbh}
        </if>
        <if test="sspt != null and sspt != ''">
            and sspt = #{sspt}
        </if>
        GROUP BY lsh
        order by create_time desc
        limit #{help.pageNum},#{help.pageSize}
    </select>

    <select id="queryXsDjCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(1)
        from (select
        lsh,IFNULL(sum(IFNULL(je,0)),0) je,GROUP_CONCAT(yqqm) yqqm,yqsj,sfxd,khdh,fqsj,IFNULL(sum(IFNULL(ytje,0)),0)
        ytje,sfxyld,djlx
        from
        wlgb_jd_djbbd
        where
        sfsc = 0
        and sfscdj = 0
        and djlx = 2
        <if test="userid != null and userid != ''">
            and fqr_id = #{userid}
        </if>
        <if test="name != null and name != ''">
            and fqr_name = #{name}
        </if>
        <if test="khdh != null and khdh != ''">
            and khdh = #{khdh}
        </if>
        <if test="sfxd != null and sfxd != ''">
            and sfxd = #{sfxd}
        </if>
        <if test="sfxyld != null and sfxyld != ''">
            and sfxyld = #{sfxyld}
        </if>
        <if test="ddbh != null and ddbh != ''">
            and ddbh = #{ddbh}
        </if>
        <if test="sspt != null and sspt != ''">
            and sspt = #{sspt}
        </if>
        GROUP BY lsh) a
    </select>

    <select id="queryJmsBbByVid" resultType="com.wlgb.entity.vo.WlgbJmsbb" parameterType="java.lang.String">
        select * from wlgb_jmsbb where sfsc = 0 and vid = #{vid}
    </select>

    <select id="queryBsCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select
            count(1)
        from
            wlgb_not_lc
        where
            sfsc = 0
        and
            vid = #{vid}
    </select>
    <!-- 查询是否有修改权限 -->
    <select id="queryNotQx" parameterType="java.lang.String" resultType="java.lang.Integer">
        select
            count(1)
        from
            wlgb_xgqx
        where
            sfsc = 0
        and
            userid = #{userid}
    </select>

    <select id="querySfSqKdj" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1) FROM wlgb_kdj_sqjl
        WHERE
        sfsc = 0
        and vid = #{vid}
        and
        NOT (
        (date_format(tctime,'%Y-%m-%d %H:%i') &lt; date_format(#{jctime},'%Y-%m-%d %H:%i')
        OR ( date_format(jctime,'%Y-%m-%d %H:%i') > date_format(#{tctime},'%Y-%m-%d %H:%i'))
        )
        )
    </select>

    <select id="zxKdjHfGc" parameterType="java.util.Map">
        CALL KDJ_HFYJE(#{vid}, #{jctime}, #{tctime})
    </select>

    <select id="queryDdCz" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
            count(1)
        from
            wlgb_wxfdd
        where
            xid = #{xid}
        and
            sfsc = 0
    </select>

    <select id="queryByXidDzJlCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select
            count(1)
        from
            wlgb_qrd_dzjl
        where
            xid = #{xid}
    </select>

    <insert id="insertDzHfJl" parameterType="com.wlgb.entity.vo.WlgbQrdDzjl">
        insert into wlgb_qrd_dzjl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="xid != null and xid != ''">xid,</if>
            <if test="time != null">time,</if>
            <if test="sfcl != null">sfcl,</if>
        </trim>
        value
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="xid != null and xid != ''">#{xid},</if>
            <if test="time != null">#{time},</if>
            <if test="sfcl != null">#{sfcl},</if>
        </trim>
    </insert>

    <select id="queryFsHf" resultType="com.wlgb.entity.vo.WlgbQrdDzjl">
        select
            id, xid, time, sfcl
        from
            wlgb_qrd_dzjl
        where
            sfcl = 0
        and
            DATE_FORMAT(CURDATE(),'%Y-%m-%d') > DATE_FORMAT(time,'%Y-%m-%d')
        order by time asc
    </select>

    <update id="updateXgZt" parameterType="com.wlgb.entity.vo.WlgbQrdDzjl">
        update wlgb_qrd_dzjl set sfcl = #{sfcl}, cltime = #{cltime} where  xid = #{xid}
    </update>


    <select id="queryGqGl" resultType="com.wlgb.entity.vo.WlgbNotVilla">
        select
            *
        from
            wlgb_not_villa
        where
            sfsc = 0
        and
            curdate() > endTime
    </select>

    <!-- 查询全部流程播报机器人 -->
    <select id="queryLcBbJqr" resultType="com.wlgb.config.DkBbJqr">
        select
            *
        from
            wlgb_lcbb_jqr
        where
            sfsc = 0
    </select>

    <!-- 根据城市获取机器人艾特手机号码 -->
    <select id="queryJqrBbAt" resultType="java.lang.String" parameterType="java.lang.String">
        select
            moblie
        from
            wlgb_lcbb_at
        where
            sfsc = 0
        and
            city = #{city}
    </select>

    <select id="zxCdf">
        CALL CDFHS1()
    </select>

    <select id="zxCdf2" resultType="java.lang.String" >
        CALL CDFHS2(#{xddbh})
    </select>

    <select id="queryCdfList" resultType="com.alibaba.fastjson.JSONObject">
        select * from sj_cdf_new1
    </select>

    <!-- 根据商品类型查询商品信息 -->
    <select id="queryProductByTypeList" parameterType="java.util.Map" resultType="java.util.Map">
        select
        id,name,price,yhqjg,gg,cbj,kc,ms,img,type,bm,sfsc,spbh,txm,pp,0 as num,0 as je
        from
        wlgb_product
        <where>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            and sfsc = 0
            and bm = #{city}
            <if test="serch != null and serch != ''">
                and name like CONCAT('%',#{serch},'%')
            </if>
        </where>
        limit #{help.pageNum}, #{help.pageSize}
    </select>
    <select id="queryProductByTypeCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select
        count(1)
        from
        wlgb_product
        <where>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            and sfsc = 0
            and bm = #{city}
            <if test="serch != null and serch != ''">
                and name like CONCAT('%',#{serch},'%')
            </if>
        </where>
    </select>

    <!-- 查询审批是否是在内的 -->
    <select id="querySpLbCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT count(1) FROM `wlgb_jd_splb` where processcode = #{processcode}
    </select>

    <!-- 补定金数据 -->
    <select id="queryBdj" resultType="com.wlgb.entity.WlgbHxyhDzjl">
        SELECT * FROM `wlgb_hxyh_dzjl` where sflrjd = 0 ORDER BY create_time asc
    </select>

    <!-- 根据部门id查询产能设置区域 -->
    <select id="queryCnQyByBmId" parameterType="java.lang.String" resultType="com.alibaba.fastjson.JSONObject">
        SELECT * FROM `tjcnqyyj` where bmid = #{bmid}
    </select>

    <!-- 执行朋友圈播报数据 -->
    <select id="zxPyqBbSj">
        CALL PYQBBRY()
    </select>

    <!-- 朋友圈播报个人数据 -->
    <select id="queryBbSjRy" resultType="java.util.Map">
        SELECT name,bmmc,IFNULL(yzf,0) as yzf,IFNULL(sz, 0) as sz, truncate(IFNULL(IFNULL(sz, 0)/IFNULL(yzf,0), 0) * 100, 2) as lsl FROM `pyq_bb_ry` where sz > 0 or yzf > 0 order by sz desc
    </select>

    <!-- 朋友圈播报部门数据 -->
    <select id="queryBbSjBm" resultType="java.util.Map">
        SELECT bmmc, IFNULL(sum(IFNULL(yzf,0)),0) yzf, IFNULL(sum(IFNULL(sz,0)),0) as sz, truncate(IFNULL(IFNULL(sum(IFNULL(sz,0)),0)/IFNULL(sum(IFNULL(yzf,0)),0),0) * 100, 2) as lsl FROM `pyq_bb_ry` where yzf > 0 or sz > 0 GROUP BY bm ORDER BY sz desc
    </select>

    <!-- 查询别墅开业时间 -->
    <select id="queryBsKySjByBsMc" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT kysj FROM `wlgb_bskysj` where bsmc = #{bsmc}
    </select>

    <!-- 查询对公转账未上传金蝶数据 -->
    <select id="queryDgZzWscJdList" resultType="com.wlgb.entity.WlgbJdDjbbd">
        select
        *
        from
        wlgb_jd_djbbd
        <where>
            and sfsc = 0
            and sfscdj = 0
            and djlx = 1
            and sfdgzz = 1
            and sflrjd = 0
            and DATE_FORMAT(CURDATE(),'%Y-%m-%d') > DATE_FORMAT(dzsj,'%Y-%m-%d')
        </where>
    </select>

    <!-- 查询美团已核销未上传金蝶数据 -->
    <select id="queryYhxQm" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            id,ddbh,fqr_id,form_inst_id,date_format(yqsj,'%Y-%m-%d') as yqtime,TRUNCATE(je,2) as jsj,yqqm as qh,sspt
        FROM
            wlgb_jd_djbbd
        WHERE
            djlx = 2
            AND sspt in (1,3)
            and sflrjd = 0
            and sfsc = 0
            and yqsj BETWEEN date_format(DATE_SUB(CURDATE(), interval 7 day), '%Y-%m-%d') and date_format(DATE_SUB(CURDATE(), interval 6 day), '%Y-%m-%d')
        GROUP BY yqqm
        ORDER BY yqsj asc
    </select>

    <!-- 清空费用科目表 -->
    <select id="qkJdFyKmTable">
        TRUNCATE TABLE wlgb_jd_fykm
    </select>

    <!-- 费用科目下拉 -->
    <select id="queryFyKmSelect" resultType="com.wlgb.config.Select">
        SELECT
            bm as value, mc as text, false as defaultChecked
        FROM
            `wlgb_jd_fykm`
        WHERE
            sfsc = 0
        ORDER BY convert(`bm`  using gbk) asc
    </select>

    <!-- 查询接收人事日志人员 -->
    <select id="queryHrGzRzFsr" resultType="java.util.Map">
        select
            name, userid
        from
            hr_zpb
        where
            sffsrz = 0
    </select>

    <!-- 执行客单价数据存储过程 -->
    <select id="zxKdjSj" parameterType="java.lang.String">
        CALL PDB_KDJ(#{start}, #{end})
    </select>
    <!-- 执行客单价民宿数据存储过程 -->
    <select id="zxKdjSjMs" parameterType="java.lang.String">
        CALL PDB_KDJ_TS(#{start}, #{end})
    </select>

    <!-- 客单价价格查询 -->
    <select id="queryKdjZsZdj" resultType="java.util.Map">
        SELECT bsmc as day,XHFYJ as je,CONCAT_WS('-',DATE_FORMAT(rq,'%m.%d'),xq,cc) as lesson, if(sfkc ='是', if(zt = '异常', 'orange', if(zt = '上涨', 'red', if(zt = '下浮', 'green', 'purple'))), 'blue') as 'style_id', sfkc, fdj, zt, rq, cc, xq, bsid, zdj, jzj FROM `pdb_kdj`
    </select>

    <!-- 客单价价格别墅横坐标 -->
    <select id="queryBsHzbZdj" resultType="java.util.Map">
        SELECT bsmc as title,bsid as id FROM `pdb_kdj` GROUP BY bsid ORDER BY convert(`bsmc` using gbk) asc
    </select>

    <!-- 客单价价格别墅纵坐标 -->
    <select id="queryRqZzbZdj" resultType="java.util.Map">
        SELECT CONCAT_WS('-',DATE_FORMAT(rq,'%m.%d'),xq,cc) as lesson,xq,cc,rq FROM `pdb_kdj` GROUP BY rq,cc ORDER BY rq asc,convert(`cc` using gbk) asc
    </select>

    <!-- 根据流水号查询定金记录 -->
    <select id="queryDjJlByLsh" parameterType="java.lang.String" resultType="com.wlgb.entity.WlgbJdDjbbd">
        select
            lsh,IFNULL(sum(IFNULL(je,0)),0) je,GROUP_CONCAT(yqqm) as yqqm,yqsj,sfxd,khdh,fqsj,IFNULL(sum(IFNULL(ytje,0)),0) as ytje,sfxyld,djlx,fqr_id as fqrId,fqr_name as fqrName,IFNULL(sum(IFNULL(qmsj,0)),0) qmsj,ddbh
        from
            wlgb_jd_djbbd
        where
            sfsc = 0
        and
            sfxd in (0,1,2,5)
        and
            lsh = #{lsh}
    </select>

    <!-- 差评提交 -->
    <insert id="insertCpJl" parameterType="java.util.Map">
        insert into wlgb_cptj
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="zbdz != null and zbdz != ''">zbdz,</if>
            <if test="time != null">time,</if>
            <if test="xid != null and xid != ''">xid,</if>
        </trim>

        value

        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="zbdz != null and zbdz != ''">#{zbdz},</if>
            <if test="time != null">#{time},</if>
            <if test="xid != null and xid != ''">#{xid},</if>
        </trim>
    </insert>

    <!-- 业务经理回访记录 -->
    <select id="queryYwJlHfJl" resultType="com.wlgb.entity.vo.DdhfUtil" parameterType="java.util.Map">
        select
        xddbh, tjr as xfd, xzk, h.time, xzkdh,h.sfyc,h.yclx,h.bz,h.img1,h.img2,h.img3,h.img4,h.img5,h.img6,h.img7,h.img8,h.img9,h.img10,h.tcsj,h.jcsj,h.hfsj
        from
        wlgb_ywjlhf h
        LEFT JOIN weilian.tb_xyd x
        on h.xid = x.xid
        <where>
            <if test="search != null and search != ''">
                and
                (x.xzk like CONCAT('%',#{search},'%') or h.tjr like CONCAT('%',#{search},'%'))
            </if>
            <if test="name != null and name != ''">
                and h.tjr = #{name}
            </if>
        </where>
        ORDER BY h.time DESC
        limit #{help.pageNum}, #{help.pageSize}
    </select>
    <select id="queryYwJlHfJlCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
        count(1)
        from
        wlgb_ywjlhf h
        LEFT JOIN weilian.tb_xyd x
        on h.xid = x.xid
        <where>
            <if test="search != null and search != ''">
                and
                (x.xzk like CONCAT('%',#{search},'%') or h.tjr like CONCAT('%',#{search},'%'))
            </if>
            <if test="name != null and name != ''">
                and h.tjr = #{name}
            </if>
        </where>
    </select>

    <!-- 查询营销部门招聘通知 -->
    <select id="queryYxbTzLb" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
            count(1)
        from
            hr_yxbtzlx
        where
            sfsc = '0'
        and
            lbid = #{lbid}
    </select>

    <!-- 查询营销部门招聘通知人列表 -->
    <select id="queryYxBmHrTz" resultType="java.lang.String">
        select
            userid
        from
            hr_tz
        <where>
            and  sfsc = 0
        </where>
    </select>

    <!-- 查询招聘部人员id -->
    <select id="queryZpbRy" resultType="java.lang.String">
        select
            GROUP_CONCAT(userid)
        from
            hr_zpb
        <where>
            and
            sfsc = 0
        </where>
    </select>


    <select id="queryDtSj" resultType="java.util.Map">
        select name,0 as hz, 0 as tg, '0%' as tgl, CURDATE() as time from hr_zpb where sfzg = '0' and name not in (SELECT t.name FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where t.sfzg = '0' and DATE_FORMAT(Tjdate,'%Y-%m-%d') = DATE_FORMAT(CURDATE(),'%Y-%m-%d') and m.sfsc = 0 GROUP BY t.name) UNION select * from (select a.name,IFNULL(a.yy,0),IFNULL(b.tg,0),CONCAT(truncate(IFNULL(IFNULL(b.tg,0)/IFNULL(a.yy,0),0)*100,2),'%'), CURDATE() as time from (SELECT t.name,count(1) as yy FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where  t.sfzg = '0' and DATE_FORMAT(Tjdate,'%Y-%m-%d') = DATE_FORMAT(CURDATE(),'%Y-%m-%d') and m.sfsc = 0 GROUP BY t.name)a LEFT JOIN (SELECT t.name,count(1) as tg FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where t.sfzg = '0' and DATE_FORMAT(Tjdate,'%Y-%m-%d') = DATE_FORMAT(CURDATE(),'%Y-%m-%d') and m.sfsc = 0 and Mianshi = '已通过' GROUP BY t.name) b on a.`name` = b.`name`) a ORDER BY tg desc,hz desc,convert(name  using gbk) asc
    </select>
    <select id="queryDySj" resultType="java.util.Map">
        select name,0 as hz, 0 as tg, '0%' as tgl, DATE_FORMAT(CURDATE(),'%Y-%m') as time from hr_zpb where sfzg = '0' and name not in (SELECT t.name FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where t.sfzg = '0' and  DATE_FORMAT(Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0 GROUP BY t.name) UNION select * from (select a.name,IFNULL(a.yy,0),IFNULL(b.tg,0),CONCAT(truncate(IFNULL(IFNULL(b.tg,0)/IFNULL(a.yy,0),0)*100,2),'%'), DATE_FORMAT(CURDATE(),'%Y-%m') as time from (SELECT t.name,count(1) as yy FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where sfzg = '0' and  DATE_FORMAT(Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0 GROUP BY t.name)a LEFT JOIN (SELECT t.name,count(1) as tg FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where DATE_FORMAT(Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0 and Mianshi = '已通过' GROUP BY t.name) b on a.`name` = b.`name`) a ORDER BY tg desc,hz desc,convert(name  using gbk) asc
    </select>

    <select id="queryDzSj" resultType="java.util.Map">
        select name,0 as hz, 0 as tg, '0%' as tgl, CONCAT(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) DAY),'至',DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())-6 DAY)) as time from hr_zpb where sfzg = '0' and name not in (SELECT t.name FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where t.sfzg = '0' and YEARWEEK(date_format(TJdate,'%Y-%m-%d'),1) = YEARWEEK(CURDATE(),1) and m.sfsc = 0 GROUP BY t.name) UNION select * from (select a.name,IFNULL(a.yy,0),IFNULL(b.tg,0),CONCAT(truncate(IFNULL(IFNULL(b.tg,0)/IFNULL(a.yy,0),0)*100,2),'%'), CONCAT(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) DAY),'至',DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())-6 DAY)) as time from (SELECT t.name,count(1) as yy FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where  t.sfzg = '0' and YEARWEEK(date_format(TJdate,'%Y-%m-%d'),1) = YEARWEEK(CURDATE(),1) and m.sfsc = 0 GROUP BY t.name)a LEFT JOIN (SELECT t.name,count(1) as tg FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where t.sfzg = '0' and YEARWEEK(date_format(TJdate,'%Y-%m-%d'),1) = YEARWEEK(CURDATE(),1) and m.sfsc = 0 and Mianshi = '已通过' GROUP BY t.name) b on a.`name` = b.`name`) a ORDER BY tg desc,hz desc,convert(name  using gbk) asc
    </select>
    <select id="querySzSj" resultType="java.util.Map">
        select name,0 as hz, 0 as tg, '0%' as tgl, CONCAT(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())+7 DAY),'至',DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())+1 DAY)) as time from hr_zpb where sfzg = '0' and name not in (SELECT t.name FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where t.sfzg = '0' and (DATE_FORMAT(TJdate,'%Y-%m-%d') BETWEEN DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())+7 DAY),'%Y-%m-%d') and DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())+1 DAY),'%Y-%m-%d')) and m.sfsc = 0 GROUP BY t.name) UNION select * from (select a.name,IFNULL(a.yy,0),IFNULL(b.tg,0),CONCAT(truncate(IFNULL(IFNULL(b.tg,0)/IFNULL(a.yy,0),0)*100,2),'%'), CONCAT(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())+7 DAY),'至',DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())+1 DAY)) as time from (SELECT t.name,count(1) as yy FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where  t.sfzg = '0' and (DATE_FORMAT(TJdate,'%Y-%m-%d') BETWEEN DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())+7 DAY),'%Y-%m-%d') and DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())+1 DAY),'%Y-%m-%d')) and m.sfsc = 0 GROUP BY t.name)a LEFT JOIN (SELECT t.name,count(1) as tg FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where t.sfzg = '0' and (DATE_FORMAT(TJdate,'%Y-%m-%d') BETWEEN DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())+7 DAY),'%Y-%m-%d') and DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE())+1 DAY),'%Y-%m-%d')) and m.sfsc = 0 and Mianshi = '已通过' GROUP BY t.name) b on a.`name` = b.`name`) a ORDER BY tg desc,hz desc,convert(name  using gbk) asc
    </select>
    <select id="queryDyySj" resultType="java.util.Map">
        select name,0 as hz, 0 as tg, '0%' as tgl, DATE_FORMAT(CURDATE(),'%Y-%m') as time,'0%' as rzl,'0%' as dgl,0 as rzrs,0 as dgrs from hr_zpb where sfzg = '0' and name not in (SELECT t.name FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where t.sfzg = '0' and  DATE_FORMAT(Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0 GROUP BY t.name) UNION select * from ( select a.name,IFNULL(a.yy,0),IFNULL(b.tg,0),CONCAT(truncate(IFNULL(IFNULL(b.tg,0)/IFNULL(a.yy,0),0)*100,2),'%'), DATE_FORMAT(CURDATE(),'%Y-%m')  as time,CONCAT(truncate(IFNULL(IFNULL(c.rzrs,0)/(IFNULL(d.sgqn,0)+IFNULL(f.sgqqt,0)+IFNULL(e.sgqzl,0)+IFNULL(c.rzrs,0)),0)*100,2),'%') as rzl,CONCAT(truncate(IFNULL((IFNULL(e.sgqzl,0)+IFNULL(f.sgqqt,0)+IFNULL(d.sgqn,0)+IFNULL(c.rzrs,0))/IFNULL(b.tg,0),0)*100,2),'%') as dgl,IFNULL(c.rzrs,0) as rzrs,IFNULL(e.sgqzl,0)+IFNULL(f.sgqqt,0)+IFNULL(d.sgqn,0)+IFNULL(c.rzrs,0) as dgrs from (SELECT t.name,count(1) as yy FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where sfzg = '0' and  DATE_FORMAT(Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0 GROUP BY t.name)a LEFT JOIN (SELECT t.name,count(1) as tg FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where DATE_FORMAT(Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0 and Mianshi = '已通过' GROUP BY t.name) b on a.`name` = b.`name` LEFT JOIN (SELECT m.YYRname,count(1) as rzrs FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0 and m.JLJD = 12 GROUP BY m.YYRname)c on a.`name` = c.YYRname  LEFT JOIN (SELECT m.YYRname,count(1) as sgqn FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0 and m.JLJD = 11 GROUP BY m.YYRname)d on a.`name` = d.YYRname  LEFT JOIN (SELECT m.YYRname,count(1) as sgqzl FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0 and m.JLJD = 10 GROUP BY m.YYRname)e on a.`name` = e.YYRname LEFT JOIN (SELECT m.YYRname,count(1) as sgqqt FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0 and m.JLJD = 9 GROUP BY m.YYRname)f on a.`name` = f.YYRname ) a ORDER BY tg desc,hz desc,convert(name  using gbk) asc
    </select>
    <select id="queryOldyySj" resultType="java.util.Map">
        select name,0 as hz, 0 as tg, '0%' as tgl, DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL 1 month),'%Y-%m') as time,'0%' as rzl,'0%' as dgl,0 as rzrs,0 as dgrs from hr_zpb where sfzg = '0' and name not in (SELECT t.name FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where t.sfzg = '0' and  DATE_FORMAT(Tjdate,'%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL 1 month),'%Y-%m') and m.sfsc = 0 GROUP BY t.name) UNION select * from ( select a.name,IFNULL(a.yy,0),IFNULL(b.tg,0),CONCAT(truncate(IFNULL(IFNULL(b.tg,0)/IFNULL(a.yy,0),0)*100,2),'%'), DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL 1 month),'%Y-%m')  as time,CONCAT(truncate(IFNULL(IFNULL(c.rzrs,0)/(IFNULL(d.sgqn,0)+IFNULL(f.sgqqt,0)+IFNULL(e.sgqzl,0)+IFNULL(c.rzrs,0)),0)*100,2),'%') as rzl,CONCAT(truncate(IFNULL((IFNULL(e.sgqzl,0)+IFNULL(f.sgqqt,0)+IFNULL(d.sgqn,0)+IFNULL(c.rzrs,0))/IFNULL(b.tg,0),0)*100,2),'%') as dgl,IFNULL(c.rzrs,0) as rzrs,IFNULL(e.sgqzl,0)+IFNULL(f.sgqqt,0)+IFNULL(d.sgqn,0)+IFNULL(c.rzrs,0) as dgrs from (SELECT t.name,count(1) as yy FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where sfzg = '0' and  DATE_FORMAT(Tjdate,'%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL 1 month),'%Y-%m') and m.sfsc = 0 GROUP BY t.name)a LEFT JOIN (SELECT t.name,count(1) as tg FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where DATE_FORMAT(Tjdate,'%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL 1 month),'%Y-%m') and m.sfsc = 0 and Mianshi = '已通过' GROUP BY t.name) b on a.`name` = b.`name` LEFT JOIN (SELECT m.YYRname,count(1) as rzrs FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL 1 month),'%Y-%m') and m.sfsc = 0 and m.JLJD = 12 GROUP BY m.YYRname)c on a.`name` = c.YYRname  LEFT JOIN (SELECT m.YYRname,count(1) as sgqn FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL 1 month),'%Y-%m') and m.sfsc = 0 and m.JLJD = 11 GROUP BY m.YYRname)d on a.`name` = d.YYRname  LEFT JOIN (SELECT m.YYRname,count(1) as sgqzl FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL 1 month),'%Y-%m') and m.sfsc = 0 and m.JLJD = 10 GROUP BY m.YYRname)e on a.`name` = e.YYRname LEFT JOIN (SELECT m.YYRname,count(1) as sgqqt FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(),INTERVAL 1 month),'%Y-%m') and m.sfsc = 0 and m.JLJD = 9 GROUP BY m.YYRname)f on a.`name` = f.YYRname ) a ORDER BY tg desc,hz desc,convert(name  using gbk) asc
    </select>
    <select id="queryDnnSj" resultType="java.util.Map">
        select name,0 as hz, 0 as tg, '0%' as tgl, DATE_FORMAT(CURDATE(),'%Y') as time,'0%' as rzl,'0%' as dgl,0 as rzrs,0 as dgrs from hr_zpb where sfzg = '0' and name not in (SELECT t.name FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where t.sfzg = '0' and  DATE_FORMAT(Tjdate,'%Y') = DATE_FORMAT(CURDATE(),'%Y') and m.sfsc = 0 GROUP BY t.name) UNION select * from ( select a.name,IFNULL(a.yy,0),IFNULL(b.tg,0),CONCAT(truncate(IFNULL(IFNULL(b.tg,0)/IFNULL(a.yy,0),0)*100,2),'%'), DATE_FORMAT(CURDATE(),'%Y')  as time,CONCAT(truncate(IFNULL(IFNULL(c.rzrs,0)/(IFNULL(d.sgqn,0)+IFNULL(f.sgqqt,0)+IFNULL(e.sgqzl,0)+IFNULL(c.rzrs,0)),0)*100,2),'%') as rzl,CONCAT(truncate(IFNULL((IFNULL(e.sgqzl,0)+IFNULL(f.sgqqt,0)+IFNULL(d.sgqn,0)+IFNULL(c.rzrs,0))/IFNULL(b.tg,0),0)*100,2),'%') as dgl ,IFNULL(c.rzrs,0) as rzrs,IFNULL(e.sgqzl,0)+IFNULL(f.sgqqt,0)+IFNULL(d.sgqn,0)+IFNULL(c.rzrs,0) as dgrs from (SELECT t.name,count(1) as yy FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where sfzg = '0' and  DATE_FORMAT(Tjdate,'%Y') = DATE_FORMAT(CURDATE(),'%Y') and m.sfsc = 0 GROUP BY t.name)a LEFT JOIN (SELECT t.name,count(1) as tg FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where DATE_FORMAT(Tjdate,'%Y') = DATE_FORMAT(CURDATE(),'%Y') and m.sfsc = 0 and Mianshi = '已通过' GROUP BY t.name) b on a.`name` = b.`name` LEFT JOIN (SELECT m.YYRname,count(1) as rzrs FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y') = DATE_FORMAT(CURDATE(),'%Y') and m.sfsc = 0 and m.JLJD = 12 GROUP BY m.YYRname)c on a.`name` = c.YYRname  LEFT JOIN (SELECT m.YYRname,count(1) as sgqn FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y') = DATE_FORMAT(CURDATE(),'%Y') and m.sfsc = 0 and m.JLJD = 11 GROUP BY m.YYRname)d on a.`name` = d.YYRname  LEFT JOIN (SELECT m.YYRname,count(1) as sgqzl FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y') = DATE_FORMAT(CURDATE(),'%Y') and m.sfsc = 0 and m.JLJD = 10 GROUP BY m.YYRname)e on a.`name` = e.YYRname LEFT JOIN (SELECT m.YYRname,count(1) as sgqqt FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y') = DATE_FORMAT(CURDATE(),'%Y') and m.sfsc = 0 and m.JLJD = 9 GROUP BY m.YYRname)f on a.`name` = f.YYRname ) a ORDER BY tg desc,hz desc,convert(name  using gbk) asc
    </select>
    <select id="querySnnSj" resultType="java.util.Map">
        select name,0 as hz, 0 as tg, '0%' as tgl, DATE_FORMAT(DATE_SUB(CURDATE(),interval 1 YEAR),'%Y') as time,'0%' as rzl,'0%' as dgl,0 as rzrs,0 as dgrs from hr_zpb where sfzg = '0' and name not in (SELECT t.name FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where t.sfzg = '0' and  DATE_FORMAT(Tjdate,'%Y') = DATE_FORMAT(DATE_SUB(CURDATE(),interval 1 YEAR),'%Y') and m.sfsc = 0 GROUP BY t.name) UNION select * from ( select a.name,IFNULL(a.yy,0),IFNULL(b.tg,0),CONCAT(truncate(IFNULL(IFNULL(b.tg,0)/IFNULL(a.yy,0),0)*100,2),'%'), DATE_FORMAT(DATE_SUB(CURDATE(),interval 1 YEAR),'%Y')  as time,CONCAT(truncate(IFNULL(IFNULL(c.rzrs,0)/(IFNULL(d.sgqn,0)+IFNULL(f.sgqqt,0)+IFNULL(e.sgqzl,0)+IFNULL(c.rzrs,0)),0)*100,2),'%') as rzl,CONCAT(truncate(IFNULL((IFNULL(e.sgqzl,0)+IFNULL(f.sgqqt,0)+IFNULL(d.sgqn,0)+IFNULL(c.rzrs,0))/IFNULL(b.tg,0),0)*100,2),'%') as dgl ,IFNULL(c.rzrs,0) as rzrs,IFNULL(e.sgqzl,0)+IFNULL(f.sgqqt,0)+IFNULL(d.sgqn,0)+IFNULL(c.rzrs,0) as dgrs from (SELECT t.name,count(1) as yy FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where sfzg = '0' and  DATE_FORMAT(Tjdate,'%Y') = DATE_FORMAT(DATE_SUB(CURDATE(),interval 1 YEAR),'%Y') and m.sfsc = 0 GROUP BY t.name)a LEFT JOIN (SELECT t.name,count(1) as tg FROM `hr_zpb` t INNER JOIN weilian.hr_msz m ON t.name = m.YYRname where DATE_FORMAT(Tjdate,'%Y') = DATE_FORMAT(DATE_SUB(CURDATE(),interval 1 YEAR),'%Y') and m.sfsc = 0 and Mianshi = '已通过' GROUP BY t.name) b on a.`name` = b.`name` LEFT JOIN (SELECT m.YYRname,count(1) as rzrs FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y') = DATE_FORMAT(DATE_SUB(CURDATE(),interval 1 YEAR),'%Y') and m.sfsc = 0 and m.JLJD = 12 GROUP BY m.YYRname)c on a.`name` = c.YYRname  LEFT JOIN (SELECT m.YYRname,count(1) as sgqn FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y') = DATE_FORMAT(DATE_SUB(CURDATE(),interval 1 YEAR),'%Y') and m.sfsc = 0 and m.JLJD = 11 GROUP BY m.YYRname)d on a.`name` = d.YYRname  LEFT JOIN (SELECT m.YYRname,count(1) as sgqzl FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y') = DATE_FORMAT(DATE_SUB(CURDATE(),interval 1 YEAR),'%Y') and m.sfsc = 0 and m.JLJD = 10 GROUP BY m.YYRname)e on a.`name` = e.YYRname LEFT JOIN (SELECT m.YYRname,count(1) as sgqqt FROM weilian.hr_msz m where m.YYRname IN (SELECT `name` FROM hr_zpb WHERE sfzg = '0') and m.JLJD IN (SELECT genderjljd FROM weilian.hr_jljd) and  DATE_FORMAT(m.Tjdate,'%Y') = DATE_FORMAT(DATE_SUB(CURDATE(),interval 1 YEAR),'%Y') and m.sfsc = 0 and m.JLJD = 9 GROUP BY m.YYRname)f on a.`name` = f.YYRname ) a ORDER BY tg desc,hz desc,convert(name  using gbk) asc
    </select>
    <select id="queryNgwyx" resultType="java.util.Map">
        SELECT g.zcity,count(if(mianshi = '已通过',true,null)) as tg,count(if(jljd = '12',true,null)) as rz,count(1) as yy,CONCAT(truncate(IFNULL(IFNULL(count(1),0)/IFNULL((SELECT count(1) as yyrs FROM weilian.hr_msz m where  DATE_FORMAT(Tjdate,'%Y') = DATE_FORMAT(CURDATE(),'%Y') and m.sfsc = 0),0),0)*100,2),'%') as zb,CONCAT(truncate(IFNULL(IFNULL(count(if(mianshi = '已通过',true,null)),0)/IFNULL(count(if(sfms = 1,true,null)),0) ,0)*100,2),'%') as tgl FROM weilian.hr_gw_city g INNER  JOIN  weilian.hr_msz m ON g.zcityid = m.gwcity where  DATE_FORMAT(Tjdate,'%Y') = DATE_FORMAT(CURDATE(),'%Y') and m.sfsc = 0 GROUP BY m.gwcity  ORDER BY yy DESC,tg desc,rz DESC
    </select>
    <select id="queryYgwyx" resultType="java.util.Map">
        SELECT g.zcity,count(if(mianshi = '已通过',true,null)) as tg,count(if(jljd = '12',true,null)) as rz,count(1) as yy,CONCAT(truncate(IFNULL(IFNULL(count(1),0)/IFNULL((SELECT count(1) as yyrs FROM weilian.hr_msz m where  DATE_FORMAT(Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0),0),0)*100,2),'%') as zb,CONCAT(truncate(IFNULL(IFNULL(count(if(mianshi = '已通过',true,null)),0)/IFNULL(count(if(sfms = 1,true,null)),0) ,0)*100,2),'%') as tgl FROM weilian.hr_gw_city g INNER  JOIN  weilian.hr_msz m ON g.zcityid = m.gwcity where  DATE_FORMAT(Tjdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and m.sfsc = 0 GROUP BY m.gwcity  ORDER BY yy DESC,tg desc,rz DESC
    </select>
    <select id="queryGrmb" resultType="java.util.Map">
        SELECT
            hz.NAME,
            ifnull( zpdm, 0 ) AS zpdmrs,
            ifnull( hg.zpdmrs, 0 ) AS dms,
            ifnull( dqrzs, 0 ) AS rzrs,
            ifnull( hg.rzrs, 0 ) AS rzs,
        IF
            (
                ifnull( zpdm, 0 )= 0
                OR round( ifnull( mb.ytg, 0 )/ ifnull( zpdm, 0 ), 2 )= 0,
                0,
            CONCAT_WS( '',( TRUNCATE (( ifnull( mb.ytg, 0 )/ ifnull( zpdm, 0 ))* 100, 2 )), '%' )) AS mbctg,
            CONCAT( IFNULL( hg.mstgl, 0 ), '%' ) AS mstgl
        FROM
            hr_zpb hz
            LEFT JOIN (
            SELECT
                count( 1 ) AS zpdm,
                YYRname AS NAME,
                DATE_FORMAT( hm.TJdate, '%Y-%m' ) AS 'date',(
                SELECT
                    count( 1 )
                FROM
                    weilian.hr_msz hh
                WHERE
                    jljd = 12
                    AND DATE_FORMAT( hh.TJdate, '%Y-%m' )= DATE_FORMAT( CURDATE(), '%Y-%m' )
                    AND hh.YYRname = hm.YYRname
                GROUP BY
                    hh.YYRname
                ) AS dqrzs,
                (
                SELECT
                    count( 1 )
                FROM
                    weilian.hr_msz hh
                WHERE
                    mianshi = '已通过'
                    AND DATE_FORMAT( hh.TJdate, '%Y-%m' )= DATE_FORMAT( CURDATE(), '%Y-%m' )
                    AND hh.YYRname = hm.YYRname
                GROUP BY
                    hh.YYRname
                ) AS ytg
            FROM
                weilian.hr_msz hm
            WHERE
                DATE_FORMAT( hm.TJdate, '%Y-%m' )= DATE_FORMAT( CURDATE(), '%Y-%m' )
            GROUP BY
                YYRname,
                DATE_FORMAT( hm.TJdate, '%Y-%m' )
            ) mb ON hz.NAME = mb.
            NAME LEFT JOIN ( SELECT * FROM hr_grmb WHERE yf = DATE_FORMAT( CURDATE(), '%Y-%m' ) ) hg ON hz.NAME = hg.NAME
        WHERE
            hz.sfzg = 0
        ORDER BY
            CONVERT ( hz.NAME USING gbk ) ASC
    </select>
    <select id="querySgrmb" resultType="java.util.Map">
        SELECT
            hz.NAME,
            ifnull( zpdm, 0 ) AS zpdmrs,
            ifnull( hg.zpdmrs, 0 ) AS dms,
            ifnull( dqrzs, 0 ) AS rzrs,
            ifnull( hg.rzrs, 0 ) AS rzs,
        IF
            (
                ifnull( zpdm, 0 )= 0
                OR round( ifnull( mb.ytg, 0 )/ ifnull( zpdm, 0 ), 2 )= 0,
                0,
            CONCAT_WS( '',( TRUNCATE (( ifnull( mb.ytg, 0 )/ ifnull( zpdm, 0 ))* 100, 2 )), '%' )) AS mbctg,
            CONCAT( IFNULL( hg.mstgl, 0 ), '%' ) AS mstgl
        FROM
            hr_zpb hz
            LEFT JOIN (
            SELECT
                count( 1 ) AS zpdm,
                YYRname AS NAME,
                DATE_FORMAT( hm.TJdate, '%Y-%m' ) AS 'date',(
                SELECT
                    count( 1 )
                FROM
                    weilian.hr_msz hh
                WHERE
                    jljd = 12
                    AND DATE_FORMAT( hh.TJdate, '%Y-%m' )= DATE_FORMAT( DATE_SUB(curdate(), INTERVAL 1 MONTH), '%Y-%m' )
                    AND hh.YYRname = hm.YYRname
                GROUP BY
                    hh.YYRname
                ) AS dqrzs,
                (
                SELECT
                    count( 1 )
                FROM
                    weilian.hr_msz hh
                WHERE
                    mianshi = '已通过'
                    AND DATE_FORMAT( hh.TJdate, '%Y-%m' )= DATE_FORMAT( DATE_SUB(curdate(), INTERVAL 1 MONTH), '%Y-%m' )
                    AND hh.YYRname = hm.YYRname
                GROUP BY
                    hh.YYRname
                ) AS ytg
            FROM
                weilian.hr_msz hm
            WHERE
                DATE_FORMAT( hm.TJdate, '%Y-%m' )= DATE_FORMAT( DATE_SUB(curdate(), INTERVAL 1 MONTH), '%Y-%m' )
            GROUP BY
                YYRname,
                DATE_FORMAT( hm.TJdate, '%Y-%m' )
            ) mb ON hz.NAME = mb.
            NAME LEFT JOIN ( SELECT * FROM hr_grmb WHERE yf = DATE_FORMAT( DATE_SUB(curdate(), INTERVAL 1 MONTH), '%Y-%m' ) ) hg ON hz.NAME = hg.NAME
        WHERE
            hz.sfzg = 0
        ORDER BY
            CONVERT ( hz.NAME USING gbk ) ASC
    </select>
    <select id="queryBmmb" resultType="java.util.Map">
        select * from (select count(1) as zpdmrs,CONCAT(TRUNCATE(IFNULL((count(if(mianshi = '已通过',true,null))/count(1)),0)*100,2),'%') as mbctg,count(if(jljd = 12, true, null)) as rzrs from weilian.hr_msz where sfsc = 0 and YYRname in (select name from hr_zpb where sfzg = 0) and DATE_FORMAT(TJdate,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m')) a,(select IFNULL((select IFNULL(zpdmrs,0) from hr_bmmb where ydate = CONCAT(DATE_FORMAT(CURDATE(),'%Y-%m'),'')),0) as yyrs) b,(select IFNULL((select IFNULL(rzrs,0) from hr_bmmb where ydate = CONCAT(DATE_FORMAT(CURDATE(),'%Y-%m'),'')),0) as rzs)  c,(select IFNULL(CONCAT((select IFNULL(mstgl,0) from hr_bmmb where ydate = CONCAT(DATE_FORMAT(CURDATE(),'%Y-%m'),'')),'%'),0) as tgl) d
    </select>
    <select id="queryYbmmb" resultType="java.util.Map">
        select * from (select count(1) as zpdmrs,CONCAT(TRUNCATE(IFNULL((count(if(mianshi = '已通过',true,null))/count(1)),0)*100,2),'%') as mbctg,count(if(jljd = 12, true, null)) as rzrs from weilian.hr_msz where sfsc = 0 and YYRname in (select name from hr_zpb where sfzg = 0) and DATE_FORMAT(TJdate,'%Y-%m') = DATE_FORMAT(DATE_SUB(curdate(), INTERVAL 1 MONTH),'%Y-%m')) a,(select IFNULL((select IFNULL(zpdmrs,0) from hr_bmmb where ydate = CONCAT(DATE_FORMAT(DATE_SUB(curdate(), INTERVAL 1 MONTH),'%Y-%m'),'')),0) as yyrs) b,(select IFNULL((select IFNULL(rzrs,0) from hr_bmmb where ydate = CONCAT(DATE_FORMAT(DATE_SUB(curdate(), INTERVAL 1 MONTH),'%Y-%m'),'')),0) as rzs)  c,(select IFNULL(CONCAT((select IFNULL(mstgl,0) from hr_bmmb where ydate = CONCAT(DATE_FORMAT(DATE_SUB(curdate(), INTERVAL 1 MONTH),'%Y-%m'),'')),'%'),0) as tgl) d
    </select>

    <!-- 无场电表隔离添加 -->
    <insert id="insertSc" parameterType="com.wlgb.entity.vo.WlgbNotVilla">
        insert into wlgb_not_villa
        <trim prefix="(" suffix=")"  suffixOverrides="," >
            <if test="id != null and id != ''">id,</if>
            <if test="vid != null and vid != ''">vid,</if>
            <if test="vname != null and vname != ''">vname,</if>
            <if test="time != null">time,</if>
            <if test="starTime != null">starTime,</if>
            <if test="endTime != null">endTime,</if>
            <if test="wc != null">wc,</if>
            <if test="wcdb != null">wcdb,</if>
        </trim>
        value
        <trim prefix="(" suffix=")"  suffixOverrides="," >
            <if test="id != null and id != ''">#{id},</if>
            <if test="vid != null and vid != ''">#{vid},</if>
            <if test="vname != null and vname != ''">#{vname},</if>
            <if test="time != null">#{time},</if>
            <if test="starTime != null">#{starTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="wc != null">#{wc},</if>
            <if test="wcdb != null">#{wcdb},</if>
        </trim>
    </insert>

    <!-- 无场电表隔离删除 -->
    <update id="deleteSc" parameterType="java.lang.String">
        update wlgb_not_villa set sfsc = 1 where vid = #{vid} and sfsc = 0
    </update>

    <!-- 无场电表隔离查询 -->
    <select id="queryByVidCount" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1) from wlgb_not_villa where vid = #{vid} and sfsc = 0
    </select>

    <!-- 无场电表隔离修改 -->
    <update id="updateSc" parameterType="com.wlgb.entity.vo.WlgbNotVilla">
        update wlgb_not_villa
        <set>
            <if test="starTime != null">starTime = #{starTime},</if>
            <if test="endTime != null">endTime = #{endTime},</if>
            <if test="wc != null">wc = #{wc},</if>
            <if test="wcdb != null">wcdb = #{wcdb},</if>
        </set>
        where vid = #{vid} and sfsc = 0
    </update>

    <!-- 执行场地费 -->
    <select id="zxXCdf" parameterType="java.lang.String">
        CALL CDFHS(#{bsxz},#{xzkssj},#{xzjssj})
    </select>

    <!-- 查询场地费 -->
    <select id="queryDzyXCdf" resultType="java.util.Map">
        select
            *
        from
            sj_cdf_new
    </select>

    <!-- 餐饮消息不一致错误订单 -->
    <select id="queryCyJlByz" resultType="com.wlgb.entity.WlgbOrderCyjl">
        select * from wlgb_order_cyjl where csfsc = 0 and cysl * cyjg != cyje and DATE_FORMAT(create_time,'%Y-%m') >= DATE_FORMAT('2023-11-01','%Y-%m') GROUP BY ddbh
    </select>

    <!-- 餐饮对账不一致错误订单 -->
    <select id="queryCyJlDzByz" resultType="java.lang.String">
        select ddbh from wlgb_order_cyjl where csfsc = 0 and dztime is not null and DATE_FORMAT(dztime,'%Y-%m-%d') >= DATE_FORMAT(DATE_SUB(CURDATE(), interval 30 DAY), '%Y-%m-%d') and  DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') > DATE_FORMAT(dztime,'%Y-%m-%d %H:%i:%s') and cyje - cysjzcb != cysjlr GROUP BY ddbh
    </select>

    <!-- 根据流程id查询流程名称 -->
    <select id="queryProcessTypeByProcessCode" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT process_type FROM `wlgb_dingtalk_approval` where process_code = #{processCode}
    </select>

    <!-- 根据订单编号查询押金尾款支付记录 -->
    <select id="queryYjWkZfJlByDdBh" resultType="com.wlgb.entity.vo.YjDzVo" parameterType="java.lang.String">
        select jetype,d.notify_time as dzsj,d.trade_no as ysbh,d.out_trade_no as skbh from wlgb_hxyh_yjwkdzjl d LEFT JOIN wlgb_hxyh_yjwkfqsk f on d.trade_no = f.ysbh where trade_no in (SELECT ysbh FROM `wlgb_hxyh_yjwkfqsk` where xddbh = #{ddbh})
    </select>

    <!--插入数据到数据库 下载新版场地费记录-->
    <insert id="insertSjCdfNewJl" parameterType="com.wlgb.entity.SjCdfNewJl">
        insert into sj_cdf_new_jl
        <trim prefix="(" suffix=")"  suffixOverrides="," >
            <if test="creatime != null">creatime,</if>
            <if test="czrid != null and czrid != ''">czrid,</if>
            <if test="czrname != null">czrname,</if>
            <if test="xzkssj != null">xzkssj,</if>
            <if test="xzjssj != null">xzjssj,</if>
            <if test="url != null">url,</if>
            <if test="bsxz != null">bsxz,</if>
        </trim>
        value
        <trim prefix="(" suffix=")"  suffixOverrides="," >
            <if test="creatime != null">#{creatime},</if>
            <if test="czrid != null and czrid != ''">#{czrid},</if>
            <if test="czrname != null">#{czrname},</if>
            <if test="xzkssj != null">#{xzkssj},</if>
            <if test="xzjssj != null">#{xzjssj},</if>
            <if test="url != null">#{url},</if>
            <if test="bsxz != null">#{bsxz},</if>
        </trim>
    </insert>

    <select id="querySjCdfNewJl" resultType="com.wlgb.entity.SjCdfNewJl" parameterType="java.util.Map">
        SELECT *
        FROM `sj_cdf_new_jl`
        <where>
        <if test="search != null and search != ''">
            and (czrname like concat('%', #{search}, '%') or bsxz like concat('%', #{search}, '%') )
        </if>
        </where>
        order by creatime desc
        limit #{help.pageNum},#{help.pageSize}
    </select>

    <select id="CoutSjCdfNewJlList" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(1)
        from sj_cdf_new_jl
        <where>
            <if test="search != null and search != ''">
                and (czrname like concat('%', #{search}, '%') or bsxz like concat('%', #{search}, '%') )
            </if>
        </where>

    </select>


    <select id="querySpList" parameterType="java.util.Map" resultType="com.alibaba.fastjson.JSONObject">
        select
        *
        from
        <if test="type != null and type != ''">
            <if test="type == '报销'">
                wlgb_jd_bxspjl
            </if>
            <if test="type == '费用付款'">
                wlgb_jd_fyfksqjl
            </if>
            <if test="type == '预支'">
                wlgb_jd_yzsqjl
            </if>
            <if test="type == '抵预支'">
                wlgb_jd_dyzsqjl
            </if>
            <if test="type == '威廉币提现'">
                wlgb_jd_wlbtxjl
            </if>
            <if test="type == '宿舍退押金'">
                wlgb_jd_sstyjsqjl
            </if>
            <if test="type == '资金申请'">
                wlgb_jd_zjsqjl
            </if>
            <if test="type == '借支'">
                wlgb_jd_jzspjl
            </if>
            <if test="type == '平台刷单款报销'">
                wlgb_jd_ptsdkbxsqjl
            </if>
            <if test="type == '对账员专用'">
                wlgb_jd_dzyzysqjl
            </if>
            <if test="type == '退定金'">
                wlgb_jd_tdjsqjl
            </if>
            <if test="type == '一线采购/报修，加盟采购/报修'">
                wlgb_jd_yxcgsqjl
            </if>
            <if test="type == '乐诚项目预支'">
                wlgb_jd_lcxmyzsqjl
            </if>
            <if test="type == '乐诚项目采购'">
                wlgb_jd_lcxmcgdfksqjl
            </if>
            <if test="type == '乐诚采购流程'">
                wlgb_jd_lccglcjl
            </if>
            <if test="type == '乐诚预支流程'">
                wlgb_jd_lcyzjl
            </if>
            <if test="type == '乐诚项目报销'">
                wlgb_jd_lcxmbxsqjl
            </if>
            <if test="type == '乐诚项目抵预支'">
                wlgb_jd_lcxmdyzsqjl
            </if>
        </if>
        <where>
            <if test="sflrjd != null and sflrjd != ''">
                and sflrjd = #{sflrjd}
            </if>
            <if test="spbh != null and spbh != ''">
                and spbh like concat('%', #{spbh}, '%')
            </if>
            <if test="spbt != null and spbt != ''">
                and spbt like concat('%', #{spbt}, '%')
            </if>
            <if test="sqr != null and sqr != ''">
                and sqr like concat('%', #{sqr}, '%')
            </if>
            <if test="sqrbm != null and sqrbm != ''">
                and sqrbm like concat('%', #{sqrbm}, '%')
            </if>
            <if test="type != '预支' and type != '威廉币提现' and type != '宿舍退押金' and type != '资金申请' and type != '借支' and type != '对账员专用'">
                <if test="fyssft != null and fyssft != ''">
                    and fyssft like concat('%', #{fyssft}, '%')
                </if>
            </if>
        </where>
        order by DATE_FORMAT(spbh,'%Y-%m-%d %H:%i:%s') desc
        limit #{help.pageNum}, #{help.pageSize}
    </select>

    <select id="querySpListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select
        count(1)
        from
        <if test="type != null and type != ''">
            <if test="type == '报销'">
                wlgb_jd_bxspjl
            </if>
            <if test="type == '费用付款'">
                wlgb_jd_fyfksqjl
            </if>
            <if test="type == '预支'">
                wlgb_jd_yzsqjl
            </if>
            <if test="type == '抵预支'">
                wlgb_jd_dyzsqjl
            </if>
            <if test="type == '威廉币提现'">
                wlgb_jd_wlbtxjl
            </if>
            <if test="type == '宿舍退押金'">
                wlgb_jd_sstyjsqjl
            </if>
            <if test="type == '资金申请'">
                wlgb_jd_zjsqjl
            </if>
            <if test="type == '借支'">
                wlgb_jd_jzspjl
            </if>
            <if test="type == '平台刷单款报销'">
                wlgb_jd_ptsdkbxsqjl
            </if>
            <if test="type == '对账员专用'">
                wlgb_jd_dzyzysqjl
            </if>
            <if test="type == '退定金'">
                wlgb_jd_tdjsqjl
            </if>
            <if test="type == '一线采购/报修，加盟采购/报修'">
                wlgb_jd_yxcgsqjl
            </if>
            <if test="type == '乐诚项目预支'">
                wlgb_jd_lcxmyzsqjl
            </if>
            <if test="type == '乐诚项目采购'">
                wlgb_jd_lcxmcgdfksqjl
            </if>
            <if test="type == '乐诚采购流程'">
                wlgb_jd_lccglcjl
            </if>
            <if test="type == '乐诚预支流程'">
                wlgb_jd_lcyzjl
            </if>
            <if test="type == '乐诚项目报销'">
                wlgb_jd_lcxmbxsqjl
            </if>
            <if test="type == '乐诚项目抵预支'">
                wlgb_jd_lcxmdyzsqjl
            </if>
        </if>
        <where>
            <if test="sflrjd != null and sflrjd != ''">
                and sflrjd = #{sflrjd}
            </if>
            <if test="spbh != null and spbh != ''">
                and spbh like concat('%', #{spbh}, '%')
            </if>
            <if test="spbt != null and spbt != ''">
                and spbt like concat('%', #{spbt}, '%')
            </if>
            <if test="sqr != null and sqr != ''">
                and sqr like concat('%', #{sqr}, '%')
            </if>
            <if test="sqrbm != null and sqrbm != ''">
                and sqrbm like concat('%', #{sqrbm}, '%')
            </if>
            <if test="type != '预支' and type != '威廉币提现' and type != '宿舍退押金' and type != '资金申请' and type != '借支' and type != '对账员专用'">
                <if test="fyssft != null and fyssft != ''">
                    and fyssft like concat('%', #{fyssft}, '%')
                </if>
            </if>
        </where>
    </select>

</mapper>
