package com.wlgb.service3;

import com.wlgb.config.PageHelpUtil;
import com.wlgb.entity.Csry;

import java.util.Map;

public interface WeiLianDaiBanService {
    Csry queryCsRyByUserId(String userid);

    Csry queryCsRyJmByUserId(String userid);

    Integer queryNotFsHf(Map<String, Object> map);

    void qkDbLb();

    /**
     * 发送回访列表
     *
     * @param map
     * @return
     */
    PageHelpUtil queryNotKfList(Map<String, Object> map);

    PageHelpUtil queryNotKfListYwjl(Map<String, Object> map);

    void updateHfZt(String xid);

    void zjZxr(Map<String, Object> map);
}
