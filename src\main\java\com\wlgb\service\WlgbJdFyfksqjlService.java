package com.wlgb.service;

import com.wlgb.entity.WlgbJdFyfksqjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 22:32
 */
public interface WlgbJdFyfksqjlService {
    void save(WlgbJdFyfksqjl wlgbJdFyfksqjl);

    void updateById(WlgbJdFyfksqjl wlgbJdFyfksqjl);

    WlgbJdFyfksqjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd);

    List<WlgbJdFyfksqjl> queryListWlgbJdFyfksqjl(WlgbJdFyfksqjl wlgbJdFyfksqjl);
}
