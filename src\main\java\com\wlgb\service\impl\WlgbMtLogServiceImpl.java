package com.wlgb.service.impl;

import com.wlgb.entity.WlgbMtLog;
import com.wlgb.mapper.WlgbMtLogMapper;
import com.wlgb.service.WlgbMtLogService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月22日 19:49
 */
@Service
public class WlgbMtLogServiceImpl implements WlgbMtLogService {
    @Resource
    private WlgbMtLogMapper wlgbMtLogMapper;

    @Override
    public void save(WlgbMtLog wlgbMtLog) {
        wlgbMtLog.setCreateTime(new Date());
        wlgbMtLogMapper.insertSelective(wlgbMtLog);
    }

    @Override
    public List<WlgbMtLog> queryListByWlgbMtLog(WlgbMtLog wlgbMtLog) {
        return wlgbMtLogMapper.select(wlgbMtLog);
    }

    @Override
    public List<WlgbMtLog> queryByWlgbMtLog(WlgbMtLog wlgbMtLog) {
        Example example = new Example(WlgbMtLog.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("qh", wlgbMtLog.getQh());
        criteria.andEqualTo("ddbh", wlgbMtLog.getDdbh());
        return wlgbMtLogMapper.selectByExample(example);
    }
}
