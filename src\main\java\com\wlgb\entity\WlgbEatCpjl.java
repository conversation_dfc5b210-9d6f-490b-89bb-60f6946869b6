package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/13 18:43
 */
@Data
@Table(name = "wlgb_eat_cpjl")
public class WlgbEatCpjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**创建时间*/
    private Date createTime;
    /**修改时间*/
    private Date updateTime;
    /**订单编号*/
    private String ddbh;
    /**套餐编号*/
    private String tcbh;
    /**套餐名称*/
    private String tcmc;
    /**菜品编号*/
    private String cpbh;
    /**菜品名称*/
    private String cpmc;
    /**菜品类型*/
    private String cplx;
    /**集群名称*/
    private String jqmc;
    /**集群编号*/
    private String jqbh;
    /**成本合计*/
    private Double cbhj;
    /**建议售价*/
    private Double jysj;
    /**毛利率*/
    private Double mll;
    /**是否删除(0:否，1:是)*/
    private Integer sfsc;
}
