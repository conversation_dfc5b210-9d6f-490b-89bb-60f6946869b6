package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmBm;
import com.wlgb.mapper.CrmBmMapper;
import com.wlgb.service2.CrmBmService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/16 18:12
 */
@Service
@DS(value = "second")
public class CrmBmServiceImpl implements CrmBmService {
    @Resource
    private CrmBmMapper crmBmMapper;

    @Override
    public void save(CrmBm crmBm) {
        crmBmMapper.insertSelective(crmBm);
    }
}
