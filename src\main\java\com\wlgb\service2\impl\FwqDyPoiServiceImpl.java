package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqDyPoi;
import com.wlgb.mapper.FwqDyPoiMapper;
import com.wlgb.service2.FwqDyPoiService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Service
@DS(value = "second")
public class FwqDyPoiServiceImpl implements FwqDyPoiService {
    @Resource
    private FwqDyPoiMapper fwqDyPoiMapper;


    @Override
    public void save(FwqDyPoi fwqDyPoi) {
        fwqDyPoiMapper.insertSelective(fwqDyPoi);
    }

    @Override
    public List<FwqDyPoi> selectAll() {
        List<FwqDyPoi> ll = fwqDyPoiMapper.selectAll();
        return ll;
    }

    @Override
    public void deleteByPrimaryKey(FwqDyPoi fwqDyPoi) {
        fwqDyPoiMapper.deleteByPrimaryKey(fwqDyPoi);
    }
}
