package com.wlgb.filter;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Spring Boot 2.0 解决跨域问题 TODO 部署到阿里云时不能使用下面代码，否则宜搭跨域会报错
 *
 * <AUTHOR>
 */
//@Configuration
//public class WebMvcConfiguration implements WebMvcConfigurer {
//
//    @Bean
//    @Conditional(CorsFilterCondition.class)
//    public CorsFilter corsFilter() {
//        final UrlBasedCorsConfigurationSource urlBasedCorsConfigurationSource = new UrlBasedCorsConfigurationSource();
//        final CorsConfiguration corsConfiguration = new CorsConfiguration();
//        //是否允许请求带有验证信息
//        corsConfiguration.setAllowCredentials(true);
//        // 允许访问的客户端域名
//        corsConfiguration.addAllowedOriginPattern("*");
//        // 允许服务端访问的客户端请求头
//        corsConfiguration.addAllowedHeader("*");
//        // 允许访问的方法名,GET POST等
//        corsConfiguration.addAllowedMethod("*");
//        urlBasedCorsConfigurationSource.registerCorsConfiguration("/**", corsConfiguration);
//        return new CorsFilter(urlBasedCorsConfigurationSource);
//    }
//
//}
