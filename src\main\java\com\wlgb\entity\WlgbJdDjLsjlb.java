package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Date 2022/12/11 19:52
 * @Version 1.0
 */
@Data
@Table(name = "wlgb_jd_dj_lsjlb")
public class WlgbJdDjLsjlb {
    @Id
    @KeySql(useGeneratedKeys = true)
    private String id;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建日期
     */
    private java.util.Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新日期
     */
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    private String sysOrgCode;
    /**
     * 每个二维码专属uuid
     */
    private String uuid;
    /**
     * 流水号
     */
    private String lsh;
    /**
     * 金额
     */
    private Double je;
    /**
     * 收款编号
     */
    private String skbh;
    /**
     * CRM编号
     */
    private String crmbh;


    /**
     * 客户电话
     */
    private String khdh;
    /**
     * 发起人
     */
    private String fqrName;
    /**
     * 发起人id
     */
    private String fqrId;
    /**
     * 发起时间
     */
    private java.util.Date fqsj;
    /**
     * 二维码
     */
    private String ewm;

    /**
     * 定金类型(1:线下；2：线上)
     */
    private Integer djlx;

    /**
     * 是否到账(1:是；0：否)
     */
    private Integer sfdz;
    /**
     * 到账时间
     */
    private java.util.Date dzsj;


    /**
     * 所属平台(1:新美大；2：平台/短租)
     */
    private Integer sspt;
    /**
     * 美团门店
     */
    private String mtmd;
    /**
     * 获取券码
     */
    private String hqqm;
    /**
     * 验券是否完成（1：是；0：否）
     */
    private Integer yqsfwc;
    /**
     * 验券结果（1:成功；2：失败）
     */
    private Integer yqjg;


    /**
     * 验券券码
     */
    private String yqqm;
    /**
     * 验券时间（平台输入买券时间）
     */
    private java.util.Date yqsj;

    /**
     * 代码
     */
    private String dm;

    /**
     * 券码数量
     */
    private Integer qmsj;

    /**
     * 佣金
     */
    private Double yj;
    /**
     * 验券人
     */
    private String yqr;


    /**
     * 是否退款（1：是；0：否）
     */
    private Integer sftk;
    /**
     * 是否回款（1：是；0：否）
     */
    private Integer sfhk;
    /**
     * 是否刷单定金（1：是；0：否）
     */
    private Integer sfsddj;
    /**
     * 是否删除定金（1：是；0：否）
     */
    private Integer sfscdj;
    /**
     * 是否下单（1：是；0：否）
     */
    private Integer sfxd;
    /**
     * 是否对公转账（1：是；0：否）
     */
    private Integer sfdgzz;
    /**
     * 是否删除（1：是；0：否）
     */
    private Integer sfsc;
    /**
     * 订单编号
     */
    private String ddbh;
    /**
     * 表单实例id
     */
    private String formInstId;
    /**
     * 身份证
     */
    private String sfz;

    /**
     * 下单时间
     */
    private java.util.Date xdsj;
    /**
     * 是否下预留单  1是  0否
     */
    private java.lang.Integer sfxyld;
    /**
     * 是否补交定金尾款  1是  0否
     */
    private java.lang.Integer sfbjdjwk;
    /**
     * 是否补交定金  1是  0否
     */
    private java.lang.Integer sfbjdj;
}
