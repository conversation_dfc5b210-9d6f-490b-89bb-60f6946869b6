package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/13 18:43
 */
@Data
@Table(name = "wlgb_eat_scjl")
public class WlgbEatScjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**创建时间*/
    private Date createTime;
    /**修改时间*/
    private Date updateTime;
    /**订单编号*/
    private String ddbh;
    /**套餐编号*/
    private String tcbh;
    /**套餐名称*/
    private String tcmc;
    /**菜品编号*/
    private String cpbh;
    /**菜品名称*/
    private String cpmc;
    /**供应商*/
    private String gys;
    /**供应商编号*/
    private String gysbh;
    /**食材名称*/
    private String scmc;
    /**食材编号*/
    private String scbh;
    /**用量*/
    private Double ylNum;
    /**最小单价*/
    private Double minPrice;
    /**单位*/
    private String dw;
    /**单价*/
    private Double price;
    /**成本*/
    private Double cb;
    /**是否删除(0:否，1:是)*/
    private Integer sfsc;
}
