package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmXgjlb;
import com.wlgb.mapper.CrmXgjlbMapper;
import com.wlgb.service4.DhRjCrmXgjlbService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 16:05
 */
@Service
@DS("fourth")
public class DhRjCrmXgjlbServiceImpl implements DhRjCrmXgjlbService {
    @Resource
    private CrmXgjlbMapper crmXgjlbMapper;

    @Override
    public void save(CrmXgjlb crmXgjlb) {
        crmXgjlbMapper.insertSelective(crmXgjlb);
    }
}
