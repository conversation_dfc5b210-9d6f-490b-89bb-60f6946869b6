package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbXyd;
import com.wlgb.entity.WlgbJdSpCwlrjdsj;
import com.wlgb.mapper.TbXydMapper;
import com.wlgb.service2.TbXydService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月13日 23:00
 */
@Service
@DS(value = "second")
@Slf4j
public class TbXydServiceImpl implements TbXydService {
    @Resource
    private TbXydMapper tbXydMapper;

    @Override
    public void save(TbXyd tbXyd) {
        boolean cfTest = false;
        try {
            tbXydMapper.insertSelective(tbXyd);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    tbXydMapper.insertSelective(tbXyd);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，insert：参数" + tbXyd.toString());
                    }
                }
            }
        }
    }

    @Override
    public void updateById(TbXyd tbXyd) {
        boolean cfTest = false;
        try {
            tbXydMapper.updateByPrimaryKeySelective(tbXyd);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    tbXydMapper.updateByPrimaryKeySelective(tbXyd);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，update：参数" + tbXyd.toString());
                    }
                }
            }
        }
    }

    @Override
    public TbXyd queryByDdBh(String ddbh) {
        Example example = new Example(TbXyd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xddbh", ddbh);
        criteria.andEqualTo("xsfsc", 0);
        return tbXydMapper.selectOneByExample(example);
    }

    @Override
    public List<TbXyd> queryByJcTime() {
        Example example = new Example(TbXyd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xsfsc", "0");
        // 获取当前日期字符串，格式为YYYY-MM-DD
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = formatter.format(new Date());
        // 添加条件xjctime等于当前日期
        criteria.andLike("xjctime", currentDate + "%");
        return tbXydMapper.selectByExample(example);
    }

    @Override
    public TbXyd queryByDdBh2(String ddbh) {
        Example example = new Example(TbXyd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xddbh", ddbh);
        return tbXydMapper.selectOneByExample(example);
    }

    @Override
    public TbXyd queryById(String xid) {
        return tbXydMapper.selectByPrimaryKey(xid);
    }

    @Override
    public TbXyd queryByIdAndSfSc(String xid) {
        Example example = new Example(TbXyd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xid", xid);
        criteria.andEqualTo("xsfsc", 0);
        return tbXydMapper.selectOneByExample(example);
    }

    @Override
    public TbXyd queryByTbXyd(TbXyd tbXyd) {
        return tbXydMapper.selectOne(tbXyd);
    }
}
