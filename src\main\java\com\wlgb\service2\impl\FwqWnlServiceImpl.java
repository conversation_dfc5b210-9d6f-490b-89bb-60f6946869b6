package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqWnl;
import com.wlgb.mapper.FwqWnlMapper;
import com.wlgb.mapper1.WeiLianMapper;
import com.wlgb.service2.FwqWnlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@DS(value = "second")
public class FwqWnlServiceImpl implements FwqWnlService {
    @Resource
    private FwqWnlMapper fwqWnlMapper;
    @Resource
    private WeiLianMapper weiLianMapper;

    @Override
    public void save(FwqWnl fwqWnl) {
        fwqWnlMapper.insertSelective(fwqWnl);
    }

    @Override
    public List<FwqWnl> queryFwqWnlByNianAndYue(String nian, String yue) {
        return weiLianMapper.queryWnl(nian, yue);
    }


    @Override
    public FwqWnl selectOne(FwqWnl FwqWnl) {
        return fwqWnlMapper.selectOne(FwqWnl);
    }

    @Override
    public void update(Map<String, Object> map) {

    }

    @Override
    public void deleteWnlAll() {
        weiLianMapper.deleteWnlAll();
    }


}
