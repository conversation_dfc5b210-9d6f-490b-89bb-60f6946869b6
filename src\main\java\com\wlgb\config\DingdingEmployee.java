package com.wlgb.config;

import lombok.Data;

/**
 * @Description: dingding_employee
 * @Author: jeecg-boot
 * @Date:   2020-10-11
 * @Version: V1.0
 */
@Data
public class DingdingEmployee{

	/**钉钉对应的id*/
    private java.lang.String userid;
	/**钉钉姓名*/
    private java.lang.String name;
	/**部门id 如果员工属于多个部门则用|隔开*/
    private java.lang.String departid;
	/**active*/
    private java.lang.Integer active;
	/**avatar*/
    private java.lang.String avatar;
	/**position*/
    private java.lang.String position;
	/**mobile*/
    private java.lang.String mobile;
	/**tel*/
    private java.lang.String tel;
	/**workplace*/
    private java.lang.String workplace;
	/**remark*/
    private java.lang.String remark;
	/**email*/
    private java.lang.String email;
	/**jobnumber*/
    private java.lang.String jobnumber;
	/**dingid*/
    private java.lang.String dingid;
}
