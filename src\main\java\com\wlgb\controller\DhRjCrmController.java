package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service4.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/26 14:48
 */
@RestController
@RequestMapping(value = "/dhrj/crm")
public class DhRjCrmController {

    @Autowired
    private DhRjService dhRjService;
    @Autowired
    private DhRjCrmQbkhService crmQbkhService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private DhRjCrmCcJlService crmCcJlService;
    @Autowired
    private DhRjCrmCsService dhRjCrmCsService;
    @Autowired
    private DhRjCrmCsfqService dhRjCrmCsfqService;
    @Autowired
    private DhRjCrmKhZtService dhRjCrmKhZtService;
    @Autowired
    private DhRjCrmQbKhYjJlService dhRjCrmQbKhYjJlService;
    @Autowired
    private DingDingTokenService dingDingTokenService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;


    /**
     * 客户信息-城市下拉列表
     */
    @RequestMapping(value = "queryCsList")
    public Result queryCsList() {
        List<CrmCs> list = dhRjService.queryCrmCsList();

        return Result.OK(list);
    }

    /**
     * 客户信息-客户状态下拉列表
     */
    @RequestMapping(value = "queryKhztList")
    public Result queryKhztList() {
        List<CrmKhzt> khzts = dhRjService.queryCrmKhZtList(1);

        return Result.OK(khzts);
    }

    /**
     * 客户信息-来源渠道下拉列表
     */
    @RequestMapping(value = "queryKhqdList")
    public Result queryKhqdList() {
        List<CrmKhzt> khzts = dhRjService.queryCrmKhZtList(2);

        return Result.OK(khzts);
    }

    /**
     * 客户信息-客户类别下拉列表
     */
    @RequestMapping(value = "queryKhlbList")
    public Result queryKhlbList() {
        List<CrmKhzt> khzts = dhRjService.queryCrmKhZtList(3);

        return Result.OK(khzts);
    }

    /**
     * 客户信息-客户电话查重
     * dhhm: 需要查重的电话号码,id:咨询id(如为添加请用0代替)
     */
    @RequestMapping(value = "queryDhSfcz/{dhhm}/{id}")
    public Result queryDhSfcz(@PathVariable("dhhm") String dhhm, @PathVariable("id") String id) {
        Map<String, Object> map = new HashMap<>();
        map.put("dhhm", dhhm);
        map.put("qid", id);
        Integer sfcz = dhRjService.queryCrmDhSfcz(map);
        if (sfcz == null) {
            sfcz = 0;
        }
        return Result.OK(sfcz);
    }

    /**
     * crm新增
     */
    @RequestMapping(value = "saveCrmQbKh")
    public Result saveCrmQbKh(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String slid = request.getParameter("slid");
        if (datas == null) {
            return Result.error("无数据");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("slid", slid);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/dhrj/task/saveCrmQbKhTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }


    /**
     * crm信息修改，添加修改记录表
     */
    @RequestMapping(value = "updateCrmQbKh")
    public Result updateCrmQbKh(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return Result.error("无数据");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        //查询没有修改前的数据
        CrmQbkh crmQbkh = JSONObject.toJavaObject(jsonObject, CrmQbkh.class);
        CrmQbkh crmQbkh1 = crmQbkhService.queryByCrmBh(crmQbkh.getCrmbh());
        if (crmQbkh1 == null) {
            return Result.error("数据不存在");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/dhrj/task/updateCrmQbKhTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }


    /**
     * crm微信号查重
     */
    @RequestMapping(value = "selectWxh")
    public Result selectWxh(HttpServletRequest request) {
        String qkhwxh = request.getParameter("qkhwxh");
        if (qkhwxh != null) {
            Integer wxh = dhRjService.queryCrmWxhCount(qkhwxh);
            if (wxh > 0) {
                return Result.error("");
            }
        }
        return Result.OK();
    }

    /**
     * crm微信号查重
     */
    @RequestMapping(value = "selectWxh2")
    public Result selectWxh2(HttpServletRequest request) {
        String qkhwxh = request.getParameter("qkhwxh");
        if (qkhwxh != null) {
            Integer wxh = dhRjService.queryCrmWxhCount(qkhwxh);
            if (wxh > 0) {
                return Result.OK("1");
            }
        }
        return Result.OK("0");
    }

    /**
     * crm号码查重
     */
    @RequestMapping(value = "selectHm")
    public Result selectHm(HttpServletRequest request) {
        String dhhm = request.getParameter("hm");
        if (dhhm != null) {
            Integer hm = dhRjService.queryCrmHmCount(dhhm);
            if (hm > 0) {
                return Result.error("");
            }
        }
        return Result.OK();
    }

    @RequestMapping(value = "selectHm2")
    public Result selectHm2(HttpServletRequest request) {
        String dhhm = request.getParameter("hm");
        if (dhhm != null) {
            Integer hm = dhRjService.queryCrmHmCount(dhhm);
            if (hm > 0) {
                return Result.OK("1");
            }
        }
        return Result.OK("0");
    }


    /**
     * 根据城市名称查询分区
     */
    @RequestMapping(value = "queryCrmFqByCsName")
    public Result queryCrmFqByCsName(HttpServletRequest request) {
        String csname = request.getParameter("csname");
        List<CrmCsfq> crmCsfqs = dhRjService.queryCrmCsSfqByCsName(csname);

        return Result.OK(crmCsfqs);
    }

    //查询该城市分区所有的值
    @RequestMapping(value = "queryCsfq")
    public Result queryCsfq() {
        List<CrmCsfq> crmCsfqs = dhRjService.queryCrmCsFqList();

        return Result.OK(crmCsfqs);
    }


    /**
     * 查询crm版本号
     */
    @RequestMapping(value = "queryCrmBbh")
    public Result queryCrmBbh() {
        String bbh = dhRjService.queryCrmBbh();
        return Result.OK(bbh);
    }


    //查重号码
    @RequestMapping(value = "cchmwx")
    public Result cchmwx(HttpServletRequest request) {
        String khdh = request.getParameter("khdh");
        String khsj = request.getParameter("khsj");
        String khhm = request.getParameter("khhm");
        String khwxh = request.getParameter("khwxh");
        String str = "";
        if (khdh != null) {
            Integer khdh1 = dhRjService.queryCrmHmCount(khdh);
            if (khdh1 > 0) {
                str += "客户电话-";
            }
        }
        if (khsj != null) {
            Integer khsj1 = dhRjService.queryCrmHmCount(khsj);
            if (khsj1 > 0) {
                str += "客户电话2-";
            }
        }
        if (khhm != null) {
            Integer khhm1 = dhRjService.queryCrmHmCount(khhm);
            if (khhm1 > 0) {
                str += "客户电话3-";
            }
        }
        if (khwxh != null) {
            Integer khwxh1 = dhRjService.queryCrmWxhCount(khwxh);
            if (khwxh1 > 0) {
                str += "微信号";
            }
        }
        if (str.equals("")) {
            return Result.OK("0");
        }
        return Result.OK(str);
    }


    //Crm客户删除回收站
    @RequestMapping(value = "xghsz")
    public Result xghsz(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return Result.error("无数据");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String qscr = jsonObject.getString("qscr");
        String qscrid = jsonObject.getString("qscrid");
        String qscsj = jsonObject.getString("qscsj");
        String qxxzt = jsonObject.getString("qxxzt");
        String crmbh = jsonObject.getString("crmbh");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        CrmQbkh crm = crmQbkhService.queryByCrmBh(crmbh);
        crm.setQscr(qscr);
        crm.setQscrid(qscrid);
        crm.setQxxzt(qxxzt);
        //创建时间转换
        if (qscsj != null && !"".equals(qscsj)) {
            Long scsj = Long.parseLong(qscsj);  //获取当前时间戳
            String scsj1 = sdf.format(new Date(Long.parseLong(String.valueOf(scsj))));      // 时间戳转换成时间
            crm.setQscsj(scsj1);
        }
        crmQbkhService.updateById(crm);

        return Result.OK();
    }


    //导出数据
    @RequestMapping(value = "Dcsj")
    public Result exportXls(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String, Object> map = new HashMap<>();
        map.put("qcjr", jsonObject.getString("qcjr"));
        map.put("qkhdh", jsonObject.getString("qkhdh"));
        map.put("qkhwxm", jsonObject.getString("qkhwxm"));
        map.put("qkhwxh", jsonObject.getString("qkhwxh"));
        map.put("qdhbm", jsonObject.getString("qdhbm"));
        JSONArray qxgsj = jsonObject.getJSONArray("qxgsj");
        if (qxgsj.size() > 0) {
            Object[] qxgsj1 = qxgsj.toArray();
            for (int i = 0; i < qxgsj1.length; i++) {
                Object cjsj = qxgsj1[i];  //获取当前时间戳
                String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
                qxgsj1[i] = cjsjsd;
            }
            map.put("qxgsj1", qxgsj1[0]);
            map.put("qxgsj2", qxgsj1[1]);
        } else {
            map.put("qxgsj1", null);
            map.put("qxgsj2", null);
        }
        JSONArray qcjsj = jsonObject.getJSONArray("qcjsj");
        if (qcjsj.size() > 0) {
            Object[] qcjsj1 = qcjsj.toArray();
            for (int i = 0; i < qcjsj1.length; i++) {
                Long cjsj = (Long) qcjsj1[i];  //获取当前时间戳
                String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
                qcjsj1[i] = cjsjsd;
                System.out.println(qcjsj1[i]);
            }
            map.put("qcjsj1", qcjsj1[0]);
            map.put("qcjsj2", qcjsj1[1]);
            //查询移交记录
        } else {
            map.put("qcjsj1", null);
            map.put("qcjsj2", null);
        }
        JSONArray qkhydsj = jsonObject.getJSONArray("qkhydsj");
        if (qkhydsj.size() > 0) {
            Object[] qkhydsj1 = qkhydsj.toArray();
            for (int i = 0; i < qkhydsj1.length; i++) {
                Long cjsj = (Long) qkhydsj1[i];  //获取当前时间戳
                String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
                qkhydsj1[i] = cjsjsd;
            }
            map.put("qkhydsj1", qkhydsj1[0]);
            map.put("qkhydsj2", qkhydsj1[1]);
        } else {
            map.put("qkhydsj1", null);
            map.put("qkhydsj2", null);
        }
        map.put("qcs", jsonObject.getString("qcs"));
//        JSONArray qy = jsonObject.getJSONArray("fqname");
//        if (qy.size() > 0) {
//            Object[] qy1 = qy.toArray();
//            map.put("qy", qy1);
//        } else {
//            map.put("qy", null);
//        }
        JSONArray qqdly = jsonObject.getJSONArray("qqdly");
        if (qqdly.size() > 0) {
            Object[] qqdly1 = qqdly.toArray();
            map.put("qqdly", qqdly1);
        } else {
            map.put("qqdly", null);
        }

        map.put("qxxzt", jsonObject.getString("qxxzt"));
        JSONArray qfzr = jsonObject.getJSONArray("qfzr");
        Object[] fzr = qfzr.toArray();
        List<Object> lis = new ArrayList<>();
        Collections.addAll(lis, fzr);
        if (lis.size() != 0) {
            map.put("qfzr", lis);
        } else {
            map.put("qfzr", null);
        }

        map.put("page", null);
        List<CrmQbkh> list = dhRjService.queryCrmDataImport(map);

        return Result.OK(list);
    }

    //查询该城市分区名称查分区id
    @RequestMapping(value = "queryCsId")
    public Result queryCsId(HttpServletRequest request) {
        String fqname = request.getParameter("fqname");
        Integer id = dhRjService.queryCmrCsFqIdByFqName(fqname);

        return Result.OK(id);
    }


    //根据条件查询crm全部数据
    @RequestMapping(value = "selectQbsj")
    public Result selectQbsj(HttpServletRequest request) {
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        PageHelp pageHelp = new PageHelp(Integer.valueOf((currentPage != null && !"".equals(currentPage) ? currentPage : "1")), Integer.valueOf((pageSize != null && !"".equals(pageSize) ? pageSize : "10")));
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (jsonObject != null && jsonObject.size() > 0) {
            map.put("page", pageSize);
            map.put("qcjr", jsonObject.getString("qcjr"));
            map.put("qkhdh", jsonObject.getString("qkhdh"));
            map.put("qkhwxm", jsonObject.getString("qkhwxm"));
            map.put("qkhwxh", jsonObject.getString("qkhwxh"));
            map.put("qdhbm", jsonObject.getString("qdhbm"));
            JSONArray qxgsj = jsonObject.getJSONArray("qxgsj");
            if (qxgsj.size() > 0) {
                Object[] qxgsj1 = qxgsj.toArray();
                for (int i = 0; i < qxgsj1.length; i++) {
                    Object cjsj = qxgsj1[i];  //获取当前时间戳
                    String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
                    qxgsj1[i] = cjsjsd;
                }
                map.put("qxgsj1", qxgsj1[0]);
                map.put("qxgsj2", qxgsj1[1]);
            } else {
                map.put("qxgsj1", null);
                map.put("qxgsj2", null);
            }
            JSONArray qcjsj = jsonObject.getJSONArray("qcjsj");
            if (qcjsj.size() > 0) {
                Object[] qcjsj1 = qcjsj.toArray();
                for (int i = 0; i < qcjsj1.length; i++) {
                    Long cjsj = (Long) qcjsj1[i];  //获取当前时间戳
                    String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
                    qcjsj1[i] = cjsjsd;
                    System.out.println(qcjsj1[i]);
                }
                map.put("qcjsj1", qcjsj1[0]);
                map.put("qcjsj2", qcjsj1[1]);
            } else {
                map.put("qcjsj1", null);
                map.put("qcjsj2", null);
            }
            JSONArray qkhydsj = jsonObject.getJSONArray("qkhydsj");
            if (qkhydsj.size() > 0) {
                Object[] qkhydsj1 = qkhydsj.toArray();
                for (int i = 0; i < qkhydsj1.length; i++) {
                    Long cjsj = (Long) qkhydsj1[i];  //获取当前时间戳
                    String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
                    qkhydsj1[i] = cjsjsd;
                }
                map.put("qkhydsj1", qkhydsj1[0]);
                map.put("qkhydsj2", qkhydsj1[1]);
            } else {
                map.put("qkhydsj1", null);
                map.put("qkhydsj2", null);
            }
            map.put("qcs", jsonObject.getString("qcs"));
            JSONArray qy = jsonObject.getJSONArray("fqname");
            if (qy != null) {
                Object[] qy1 = qy.toArray();
                map.put("qy", qy1);
            } else {
                map.put("qy", null);
            }
            JSONArray qqdly = jsonObject.getJSONArray("qqdly");
            if (qqdly != null) {
                Object[] qqdly1 = qqdly.toArray();
                map.put("qqdly", qqdly1);
            } else {
                map.put("qqdly", null);
            }

            map.put("qxxzt", jsonObject.getString("qxxzt"));
            JSONArray qfzr = jsonObject.getJSONArray("qfzr");
            Object[] fzr = qfzr.toArray();
            List<Object> lis = new ArrayList();
            lis.addAll(Arrays.asList(fzr));
            if (lis.size() != 0) {
                map.put("qfzr", lis);
            } else {
                map.put("qfzr", null);
            }
//          map.put("bm", jsonObject.getString("bm"));
        }
        PageHelpUtil pageHelpUtil1 = dhRjService.queryCrmData(map);
        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);
        return Result.OK(pageHelpUtil1);
    }

    //根据部门查询钉钉id
    @RequestMapping(value = "selectDing")
    public Result selectDing(HttpServletRequest request) {
        String bmda = request.getParameter("bm");
        List<CrmBmxq> bmcx = dhRjService.queryCrmBmCxByBmDa(bmda);
        return Result.OK(bmcx);
    }

    //保存查重记录
    @RequestMapping(value = "InsertCcjl")
    public Result InsertCcjl(HttpServletRequest request) {
        String ccsj = request.getParameter("ccsj");
        String ccr = request.getParameter("ccr");
        String ccrid = request.getParameter("ccrid");
        if (ccsj == null) {
            return Result.error("请传入搜索条件");
        }
        List<CrmQbkh> selecccsj = dhRjService.queryCrmCcSjByCcData(ccsj);
        Date date = new Date();
        CrmCcjl ccjl = new CrmCcjl();
        ccjl.setCcsj(date);
        ccjl.setCcxx(ccsj);
        ccjl.setCzr(ccr);
        ccjl.setCzrid(ccrid);
        try {
            if (ccrid != null && !"".equals(ccrid)) {
                String userId1 = ccrid;
                //针对手机用户提交
                if (!ccrid.contains("$")) {
                    ccrid = "ding968d75cfe0d9045c4ac5d6980864d335$" + ccrid;
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(ccrid);
                if (dingdingEmployee != null) {
                    ccjl.setCzr(dingdingEmployee.getName());
                    ccjl.setCzrid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            ccjl.setCzr(dingdingEmployee2.getName());
                            ccjl.setCzrid(dingdingEmployee2.getUserid());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Integer hz = selecccsj.size();
        ccjl.setHz(hz.toString());
        crmCcJlService.save(ccjl);
        return Result.OK(selecccsj);
    }

    /**
     * 城市添加
     */
    @RequestMapping(value = "csSave")
    public Result csSave(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        CrmCs cs = JSONObject.toJavaObject(jsonObject, CrmCs.class);
        dhRjCrmCsService.save(cs);
        return Result.OK();
    }

    //城市修改
    @RequestMapping(value = "csEdit")
    public Result csEdit(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("data空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        CrmCs cs = JSONObject.toJavaObject(jsonObject, CrmCs.class);
        CrmCs crmCs1 = new CrmCs();
        crmCs1.setCsname(cs.getCsname());
        crmCs1.setCszt("0");
        CrmCs crmCs = dhRjCrmCsService.queryCrmCsByCrmCs(crmCs1);
        if (crmCs == null) {
            return Result.error("");
        }
        cs.setCsid(crmCs.getCsid());
        cs.setCszt("1");
        dhRjCrmCsService.updateById(cs);
        return Result.OK();
    }

    //城市分区添加
    @RequestMapping(value = "csFqSave")
    public Result csFqSave(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("data空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        CrmCsfq cs = JSONObject.toJavaObject(jsonObject, CrmCsfq.class);
        dhRjCrmCsfqService.save(cs);
        return Result.OK();
    }

    //城市分区修改
    @RequestMapping(value = "csFqEdit")
    public Result csFqEdit(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("data空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        CrmCsfq cs = JSONObject.toJavaObject(jsonObject, CrmCsfq.class);
        CrmCsfq crmCsfq = new CrmCsfq();
        crmCsfq.setFqname(cs.getFqname());
        crmCsfq.setSfsc("0");
        CrmCsfq crmCs = dhRjCrmCsfqService.queryCrmCsFqByCrmCsFq(crmCsfq);
        if (crmCs == null) {
            return Result.error("");
        }
        cs.setCsid(crmCs.getCsid());
        cs.setSfsc("1");
        dhRjCrmCsfqService.updateById(cs);
        return Result.OK();
    }

    //客户状态添加
    @RequestMapping(value = "khZtSave")
    public Result khZtSave(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("data空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        CrmKhzt cs = JSONObject.toJavaObject(jsonObject, CrmKhzt.class);
        dhRjCrmKhZtService.save(cs);
        return Result.OK();
    }

    //客户状修改
    @RequestMapping(value = "khZtEdit")
    public Result khZtEdit(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("data空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        CrmKhzt cs = JSONObject.toJavaObject(jsonObject, CrmKhzt.class);
        CrmKhzt crmKhzt = new CrmKhzt();
        crmKhzt.setZkhzt(cs.getZkhzt());
        crmKhzt.setSfsc("0");
        CrmKhzt crmCs = dhRjCrmKhZtService.queryCrmKhZtByCrmKhZt(crmKhzt);
        if (crmCs == null) {
            return Result.error("");
        }
        cs.setZid(crmCs.getZid());
        cs.setSfsc("1");
        dhRjCrmKhZtService.updateById(cs);
        return Result.OK();
    }

    //Crm客户移交记录
    @RequestMapping(value = "yjJlSave")
    public Result yjJlSave(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return Result.error("无数据");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        CrmQbkhYjjl yjjl = JSONObject.toJavaObject(jsonObject, CrmQbkhYjjl.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //创建时间转换
        if (yjjl.getCjsj() != null && !"".equals(yjjl.getCjsj())) {
            Long cjsj = Long.parseLong(yjjl.getCjsj());  //获取当前时间戳
            String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
            yjjl.setCjsj(cjsjsd);
        }
        dhRjCrmQbKhYjJlService.save(yjjl);

        return Result.OK();
    }

    //移交记录修改
    @RequestMapping("yjJlEdit")
    public Result yjJlEdit(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return Result.error("无数据");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        CrmQbkhYjjl yjjl = JSONObject.toJavaObject(jsonObject, CrmQbkhYjjl.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //创建时间转换
        if (yjjl.getCjsj() != null && !"".equals(yjjl.getCjsj())) {
            Long cjsj = Long.parseLong(yjjl.getCjsj());  //获取当前时间戳
            String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
            yjjl.setCjsj(cjsjsd);
        }
        //删除时间转换
        if (yjjl.getScsj() != null && !"".equals(yjjl.getScsj())) {
            Long scsj = Long.parseLong(yjjl.getScsj());  //获取当前时间戳
            String scsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(scsj))));      // 时间戳转换成时间
            yjjl.setScsj(scsjsd);
        }
        CrmQbkhYjjl crmQbkhYjjl = new CrmQbkhYjjl();
        crmQbkhYjjl.setYjbh(yjjl.getYjbh());
        CrmQbkhYjjl yj = dhRjCrmQbKhYjJlService.queryCrmQbKhYjJlByCrmQbKhYjJl(crmQbkhYjjl);
        if (yj == null) {
            return Result.error("数据库找不到对应的数据");
        }
        yjjl.setId(yj.getId());
        dhRjCrmQbKhYjJlService.updateById(yjjl);
        return Result.OK();
    }

    /**
     * crm客户信息修改触发api
     */
    @RequestMapping(value = "crmEditApi")
    public Result crmEditApi(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        if (formInstId == null || "".equals(formInstId)) {
            return Result.error("实例id空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_WGE8YPM32BUH1BPZD6N4");
        ydAppkey.setToken("E8866MB1GLNB3ME6AG8TX8LNXWV62CUEXQWIL22");
        DingDingToken dingDingToken = dhRjService.queryDingDingTokenByName("大户人家crm");
        if (dingDingToken != null) {
            if (new Date().getTime() > dingDingToken.getSxtime().getTime()) {
                String token = gxDingDingToken();
                dingDingToken.setToken(token);
            }
            //发送添加成功通知
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = YdConfig.xgBdSl(dingDingToken.getToken(), ydAppkey,"yida_pub_account", formInstId, jsonObject.toJSONString());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if(gatewayResult == null || !gatewayResult.getSuccess()){
                return Result.error("宜搭接口返回了失败");
            }
        }

        return Result.OK();
    }

    public String gxDingDingToken() {
        String token = null;
        Dingkey dingkey = dhRjService.queryDingKeyById("dgrhcrm");
        if (dingkey != null) {
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            System.out.println(token);
            DingDingToken dingDingToken = dhRjService.queryDingDingTokenByName(dingkey.getName());
            if (dingDingToken != null) {
                DingDingToken dingDingToken1 = new DingDingToken();
                dingDingToken1.setToken(token);
                dingDingToken1.setId(dingDingToken.getId());
                Date date = new Date();
                date.setMinutes(date.getMinutes() + 20);
                dingDingToken1.setSxtime(date);
                dingDingTokenService.updateById(dingDingToken1);
            } else {
                DingDingToken dingDingToken1 = new DingDingToken();
                BeanUtils.copyProperties(dingkey, dingDingToken1);
                dingDingToken1.setToken(token);
                dingDingTokenService.save(dingDingToken1);
            }
        }

        return token;
    }
}
