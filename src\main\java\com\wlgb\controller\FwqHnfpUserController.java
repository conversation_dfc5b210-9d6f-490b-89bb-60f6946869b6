package com.wlgb.controller;

import com.alibaba.fastjson.JSONObject;
import com.wlgb.config.Result;
import com.wlgb.entity.FwqHnfpUser;
import com.wlgb.service.OssFileService;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.FwqHnfpUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

import static com.wlgb.config.Tools.isEmpty;

/**
 * <AUTHOR>
 * @Date 2025/02/21 18:30
 * @Version 1.0
 */
@RestController
@Slf4j
@RequestMapping(value = "/hnfp/user")
public class FwqHnfpUserController {
    @Autowired
    private FwqHnfpUserService fwqHnfpUserService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private OssFileService ossFileService;


    /**
     * 添加用户
     */
    @RequestMapping(value = "saveuser")
    public Result saveuser(@RequestParam("name") String name, @RequestParam("tel") String tel) {
        if (isEmpty(name) || isEmpty(tel)) {
            return Result.OK("参数不能为空");
        }
        //查询用户是否已经存在了.不存在就保存
        FwqHnfpUser fwqHnfpUser = fwqHnfpUserService.selectOneByTelName(tel, name);
        if (fwqHnfpUser == null) {
            fwqHnfpUser = new FwqHnfpUser();
            fwqHnfpUser.setTel(tel);
            fwqHnfpUser.setName(name);
            fwqHnfpUser.setRztime(new Date());
            fwqHnfpUserService.save(fwqHnfpUser);
        }
        return Result.OK(fwqHnfpUser);
    }


    /**
     * 删除用户
     */
    @RequestMapping(value = "deuser")
    public Result deFiles(@RequestParam("tel") String tel) {
        if (isEmpty(tel)) {
            return Result.OK("参数不能为空");
        }
        //查询用户是否存在
        FwqHnfpUser fwqHnfpUser = fwqHnfpUserService.selectOneByTelName(tel, null);
        if (fwqHnfpUser != null) {
            fwqHnfpUserService.deleteByPrimaryKey(fwqHnfpUser);
        }
        return Result.OK("删除成功");
    }


    /**
     * 查询用户
     */
    @RequestMapping(value = "selectuser")
    public Result selectuser(@RequestParam("tel") String tel) {
        if (isEmpty(tel)) {
            return Result.OK("参数不能为空");
        }
        log.info("***************{}", tel);
        //查询用户是否存在
        FwqHnfpUser fwqHnfpUser = fwqHnfpUserService.selectOneByTelName(tel, null);
        if (fwqHnfpUser != null) {
            return Result.OK(fwqHnfpUser);
        }
        return Result.OK("当前账号不存在");
    }

}
