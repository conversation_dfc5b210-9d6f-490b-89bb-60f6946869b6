package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "crm_bmxq")
public class CrmBmxq {

    /**
     * bid
     */
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer bid;
    /**
     * userid
     */
    private String userid;
    /**
     * userid
     */
    private String name;
    /**
     * bmid
     */
    private String bmid;
    /**
     * bmid2
     */
    private String bmid2;
    /**
     * userid
     */
    private String bmid3;
    /**
     * bmid
     */
    private String bmid4;
    /**
     * bmid2
     */
    private String bmid5;
    /**
     * bmid6
     */
    private String bmid6;
    /**
     * bmid
     */
    private String bmid7;
    /**
     * bmid8
     */
    private String bmid8;
    /**
     * bmid9
     */
    private String bmid9;
    /**
     * bmid
     */
    private String bmid10;
    /**
     * bmid11
     */
    private String bmid11;
    /**
     * bmid12
     */
    private String bmid12;
    /**
     * bmid
     */
    private String bmid13;
    /**
     * bmid14
     */
    private String bmid14;
    /**
     * bmid15
     */
    private String bmid15;

    /**
     * 对应部门的主管id，可能存在多个主管，格式：主管1id，主管2id
     */
    private String bm1zg;
    private String bm2zg;
    private String bm3zg;
    private String bm4zg;
    private String bm5zg;
    private String bm6zg;
    private String bm7zg;
    private String bm8zg;
    private String bm9zg;
    private String bm10zg;

    private String bm1;
    private String bm2;
    private String bm3;
    private String bm4;
    private String bm5;
    private String bm6;
    private String bm7;
    private String bm8;
    private String bm9;
    private String bm10;
}
