package com.wlgb.config;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.taobao.api.ApiException;
import com.wlgb.entity.DingDingToken;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/30 15:05
 */
public class DingDBConfig {

    /**
     * 发送工作通知————网站版
     *
     * @param userList 发送的用户
     * @param dingkey  钉钉key
     * @param id       图片地址id
     * @param title    标题
     * @param context  内容
     * @param html     网址
     * @param uri      路径
     */
    public static void sendGztz(String uri, String userList, String id, Dingkey dingkey, String title, String context, String html) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");

        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setUseridList(userList);
        request.setAgentId((Long.parseLong(dingkey.getAgentId())));
        request.setToAllUser(false);

        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        msg.setActionCard(new OapiMessageCorpconversationAsyncsendV2Request.ActionCard());
        msg.getActionCard().setTitle(title);
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        msg.getActionCard().setMarkdown("### " + context + df.format(date));
        msg.getActionCard().setSingleTitle("点击查看详情");
        msg.getActionCard().setSingleUrl(uri + html + "?" + id);
        msg.setMsgtype("action_card");
        request.setMsg(msg);
        OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, DingToken.token(dingkey));
    }

    /**
     * 发送工作通知————链接版
     *
     * @param userList 发送的用户
     * @param dingkey  钉钉key
     * @param html     网址
     * @param title    标题
     * @param context  内容
     */
    public static void sendGztz1(String userList, Dingkey dingkey, String title, String context, String html) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");

        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setUseridList(userList);
        request.setAgentId((Long.parseLong(dingkey.getAgentId())));
        request.setToAllUser(false);

        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        msg.setActionCard(new OapiMessageCorpconversationAsyncsendV2Request.ActionCard());
        msg.getActionCard().setTitle(title);
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        msg.getActionCard().setMarkdown("### " + context + df.format(date));
        msg.getActionCard().setSingleTitle("点击查看详情");
        msg.getActionCard().setSingleUrl(html);
        msg.setMsgtype("action_card");
        request.setMsg(msg);
        OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, DingToken.token(dingkey));
    }

    /**
     * 发送工作通知————文字版
     *
     * @param userList 发送的用户
     * @param dingkey  钉钉key
     * @param text     内容
     */
    public static void sendGztzText(Dingkey dingkey, String userList, String text) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");

        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setUseridList(userList);
        request.setAgentId(Long.parseLong(dingkey.getAgentId()));
        request.setToAllUser(false);

        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        msg.setMsgtype("text");
        msg.setText(new OapiMessageCorpconversationAsyncsendV2Request.Text());
        msg.getText().setContent(text);
        request.setMsg(msg);

        OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, DingToken.token(dingkey));

    }

    /**
     * 发送工作通知————文字版(大户人家)
     *
     * @param userList 发送的用户
     * @param dingDingToken  钉钉key
     * @param text     内容
     */
    public static void sendDhRjGzTzText(DingDingToken dingDingToken, String userList, String text) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");

        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setUseridList(userList);
        request.setAgentId(Long.parseLong(dingDingToken.getAgentId()));
        request.setToAllUser(false);

        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        msg.setMsgtype("text");
        msg.setText(new OapiMessageCorpconversationAsyncsendV2Request.Text());
        msg.getText().setContent(text);
        request.setMsg(msg);

        OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, dingDingToken.getToken());

    }
}
