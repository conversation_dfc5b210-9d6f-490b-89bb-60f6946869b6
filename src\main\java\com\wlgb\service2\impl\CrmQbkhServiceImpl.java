package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmQbkh;
import com.wlgb.mapper.CrmQbkhMapper;
import com.wlgb.service2.CrmQbkhService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/26 16:38
 */
@Service
@DS(value = "second")
@Slf4j
public class CrmQbkhServiceImpl implements CrmQbkhService {
    @Resource
    private CrmQbkhMapper crmQbkhMapper;

    @Override
    public void save(CrmQbkh crmQbkh) {
        boolean cfTest = false;
        try {
            crmQbkhMapper.insertSelective(crmQbkh);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    crmQbkhMapper.insertSelective(crmQbkh);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，insert：参数" + crmQbkh.toString());
                    }
                }
            }
        }
    }

    @Override
    public void updateById(CrmQbkh crmQbkh) {
        boolean cfTest = false;
        try {
            crmQbkhMapper.updateByPrimaryKeySelective(crmQbkh);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    crmQbkhMapper.updateByPrimaryKeySelective(crmQbkh);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，update：参数" + crmQbkh.toString());
                    }
                }
            }
        }
    }

    @Override
    public CrmQbkh queryByCrmBh(String crmBh) {
        Example example = new Example(CrmQbkh.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("crmbh", crmBh);
        return crmQbkhMapper.selectOneByExample(example);
    }

    @Override
    public CrmQbkh queryCrmQbKhByCrmQbKh(CrmQbkh crmQbkh) {
        return crmQbkhMapper.selectOne(crmQbkh);
    }
}
