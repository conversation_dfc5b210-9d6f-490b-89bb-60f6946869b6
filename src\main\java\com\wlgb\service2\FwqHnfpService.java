package com.wlgb.service2;

import com.wlgb.entity.FwqHnfp;
import com.wlgb.entity.vo.FwqHnfpVo;

import java.util.List;

public interface FwqHnfpService {
    void save(FwqHnfp fwqHnfp);

    void deleteByPrimaryKey(FwqHnfp fwqHnfp);

    FwqHnfp selectOneByZyurl(String url);

    void update(FwqHnfp fwqHnfp);

    List<FwqHnfp> selectAllByXiaoquname(String xiaoquname);

    List<FwqHnfpVo> selectAllBySousuo(String sousuo, int pageNum, int pageSize);
}
