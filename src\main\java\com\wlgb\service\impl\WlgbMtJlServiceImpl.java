package com.wlgb.service.impl;

import com.wlgb.entity.WlgbJdSpCwlrjdsj;
import com.wlgb.entity.WlgbMtJl;
import com.wlgb.entity.WlgbMtLog;
import com.wlgb.entity.WlgbMtMd;
import com.wlgb.mapper.WlgbMtJlMapper;
import com.wlgb.service.WlgbMtJlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月22日 19:41
 */
@Service
public class WlgbMtJlServiceImpl implements WlgbMtJlService {
    @Resource
    private WlgbMtJlMapper wlgbMtJlMapper;

    @Override
    public void save(WlgbMtJl wlgbMtJl) {
        wlgbMtJl.setCreateTime(new Date());
        wlgbMtJlMapper.insertSelective(wlgbMtJl);
    }

    @Override
    public List<WlgbMtJl> queryListByWlgbMtJl(WlgbMtJl wlgbMtJl) {
        Example example = new Example(WlgbMtJl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddbh", wlgbMtJl.getDdbh());
        criteria.andEqualTo("mtqh", wlgbMtJl.getMtqh());
        criteria.andEqualTo("yqjg", wlgbMtJl.getYqjg());
        return wlgbMtJlMapper.selectByExample(example);
    }
}
