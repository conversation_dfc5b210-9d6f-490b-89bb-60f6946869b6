package com.wlgb.entity;


import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: crm_cs
 * @Author: jeecg-boot
 * @Date:   2020-12-02
 * @Version: V1.0
 */
@Data
@Table(name = "crm_cs")
public class CrmCs {
	/**城市id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer csid;
	/**城市名*/
    private java.lang.String csname;
	/**排序*/
    private java.lang.Integer cspx;
	/**正常：0，删除：1*/
    private java.lang.String cszt;
}
