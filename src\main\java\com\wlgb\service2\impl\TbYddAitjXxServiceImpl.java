package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbYddAitj;
import com.wlgb.entity.TbYddAitjXx;
import com.wlgb.mapper.TbYddAitjMapper;
import com.wlgb.mapper.TbYddAitjXxMapper;
import com.wlgb.mapper1.WeiLianMapper;
import com.wlgb.service2.TbYddAitjService;
import com.wlgb.service2.TbYddAitjXxService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@DS(value = "second")
public class TbYddAitjXxServiceImpl implements TbYddAitjXxService {
    @Resource
    private TbYddAitjXxMapper tbYddAitjxxMapper;
    @Resource
    private WeiLianMapper weiLianMapper;


    @Override
    public void save(TbYddAitjXx tbdAitjxx) {
        tbYddAitjxxMapper.insertSelective(tbdAitjxx);
    }

    @Override
    public TbYddAitjXx queryTbYddAitjXxByuuid(String uuid) {
        return weiLianMapper.queryTbYddAitjXxByuuid(uuid);
    }

    @Override
    public TbYddAitjXx queryTbYddAitjXxByuserid(String userid) {
        return weiLianMapper.queryTbYddAitjXxByuserid(userid);
    }

    @Override
    public TbYddAitjXx selectOne(TbYddAitjXx tbdAitjxx) {
        return tbYddAitjxxMapper.selectOne(tbdAitjxx);
    }
}
