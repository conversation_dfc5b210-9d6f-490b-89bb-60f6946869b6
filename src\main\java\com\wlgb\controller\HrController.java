package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.HrGwCity;
import com.wlgb.entity.HrMsgjl;
import com.wlgb.entity.HrMsz;
import com.wlgb.entity.HrSljl;
import com.wlgb.service.HrSljlService;
import com.wlgb.service.OssFileService;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.HrGwCityService;
import com.wlgb.service2.HrMsgjlService;
import com.wlgb.service2.HrMszService;
import com.wlgb.service2.WeiLianService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/08/30 14:50
 */
@RestController
@RequestMapping(value = "/wlgb/hr")
public class HrController {
    @Autowired
    private HrMszService hrMszService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private OssFileService ossFileService;
    @Autowired
    private HrMsgjlService hrMsgjlService;
    @Autowired
    private HrGwCityService hrGwCityService;
    @Autowired
    private HrSljlService hrSljlService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;

    /**
     * 岗位列表（父岗位）
     */
    @RequestMapping(value = "queryGwLb")
    public Rest queryGwLb() {
        List<Select> list = weiLianService.queryGwLb();

        return Rest.success(list);
    }

    /**
     * 根据父岗位id查询岗位列表
     */
    @RequestMapping(value = "queryGw")
    public Rest queryGw(HttpServletRequest request) {
        String id = request.getParameter("id");
        List<Select> list = weiLianService.queryGw(id);

        return Rest.success(list);
    }

    /**
     * 招聘渠道列表
     */
    @RequestMapping(value = "queryQd")
    public Rest queryQd() {
        List<Select> list = weiLianService.queryQd();

        return Rest.success(list);
    }

    /**
     * 招聘微信列表
     */
    @RequestMapping(value = "queryWx")
    public Rest queryWx() {
        List<Select> list = weiLianService.queryWx();

        return Rest.success(list);
    }

    /**
     * 简历进度列表
     */
    @RequestMapping(value = "queryJlJd")
    public Rest queryJlJd() {
        List<Select> list = weiLianService.queryJlJd();

        return Rest.success(list);
    }

    /**
     * 根据手机号码判断是否存在
     */
    @RequestMapping(value = "queryXySfCz")
    public Result queryXySfCz(HttpServletRequest request) {
        String tel = request.getParameter("tel");
        int count = hrMszService.queryXySfCz(tel);
        if (count > 0) {
            return Result.error("号码已存在");
        }

        return Result.OK();
    }

    /**
     * 求职者提交信息（同步招聘表单）
     */
    @RequestMapping(value = "qzzTjXx")
    public Result qzzTjXx(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("空数据");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/qzzTjXxTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 招聘表单提交
     */
    @RequestMapping(value = "tjMsJl")
    public Result tjMsJl(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("formInstId", formInstId);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/tjMsJlTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 修改招聘表单
     */
    @RequestMapping(value = "xgMsXx")
    public Result xgMsXx(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("userid");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        HrMsz hrMsz = JSON.toJavaObject(jsonObject, HrMsz.class);
        HrMsz hrMsz2 = new HrMsz();
        hrMsz2.setSfsc("0");
        hrMsz2.setMsbh(hrMsz.getMsbh());
        HrMsz hrMsz1 = hrMszService.queryOneByHrMsz(hrMsz2);
        if (hrMsz1 == null) {
            return Result.error("找不到数据");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userid", userid);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/xgMsXxTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 给面试官发送面试通知
     */
    @RequestMapping(value = "queryXxTz")
    public Result queryXxTz(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String mianshi = jsonObject.getString("mianshi");
        String msgid = "";
        if (!"已通过".equals(mianshi) && !"未通过".equals(mianshi)) {
            Object obj = JSON.parse(jsonObject.get("msgname").toString());
            if (obj instanceof JSONObject) {
                JSONObject jsonArray = jsonObject.getJSONObject("msgname");
                if (jsonArray != null && jsonArray.size() > 0) {
                    if (jsonArray.getString("value") != null && !"".equals(jsonArray.getString("value"))) {
                        Dingkey dingkey = new Dingkey();
                        dingkey.setAgentId("716099296");
                        dingkey.setAppkey("dinga4cqxwu9my5ybjhg");
                        dingkey.setAppsecret("3bgstQ9bJw96xDCV4wYNDe6L6JF52bBVGPYyD7NxqYGqG2Nl5MkJbiWE28uyYjq3");
                        String content = "紧急！您有一份面试任务需要尽快处理！\n\n送达时间：";
                        try {
                            DingDBConfig.sendGztz1(jsonArray.getString("value"), dingkey, "有一个面试需要您完成", content, "https://jztdpp.aliwork.com/APP_J7KY7FDIH52I23EK327R/workbench/FORM-CH766981FYNTUTH0W65QVKSD7ZLU1LJZKDMTKJ?formInstId=" + formInstId);
                            msgid = jsonArray.getString("value");
                        } catch (ApiException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }

            if (obj instanceof JSONArray) {
                JSONArray jsonArray = jsonObject.getJSONArray("msgname");
                if (jsonArray != null && jsonArray.size() > 0) {
                    JSONObject jsonObject1 = jsonArray.getJSONObject(0);
                    if (jsonObject1.getString("value") != null && !"".equals(jsonObject1.getString("value"))) {
                        Dingkey dingkey = new Dingkey();
                        dingkey.setAgentId("716099296");
                        dingkey.setAppkey("dinga4cqxwu9my5ybjhg");
                        dingkey.setAppsecret("3bgstQ9bJw96xDCV4wYNDe6L6JF52bBVGPYyD7NxqYGqG2Nl5MkJbiWE28uyYjq3");
                        String content = "紧急！您有一份面试任务需要尽快处理！\n\n送达时间：";
                        try {
                            DingDBConfig.sendGztz1(jsonObject1.getString("value"), dingkey, "有一个面试需要您完成", content, "https://jztdpp.aliwork.com/APP_J7KY7FDIH52I23EK327R/workbench/FORM-CH766981FYNTUTH0W65QVKSD7ZLU1LJZKDMTKJ?formInstId=" + formInstId);
                            msgid = jsonObject1.getString("value");
                        } catch (ApiException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
            String msbh = jsonObject.getString("msbh");
            HrMsz hrMsz1 = new HrMsz();
            hrMsz1.setSfsc("0");
            hrMsz1.setMsbh(msbh);
            HrMsz hrMsz = hrMszService.queryOneByHrMsz(hrMsz1);
            if (hrMsz != null) {
                DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(msgid);
                if (employee != null) {
                    HrMsgjl hrMsgjl = new HrMsgjl();
                    hrMsgjl.setMsgxm(employee.getName());
                    hrMsgjl.setMsgid(employee.getUserid());
                    hrMsgjl.setMszid(hrMsz.getId().toString());
                    SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    hrMsgjl.setFsmssj(df1.format(new Date()));
                    hrMsgjlService.save(hrMsgjl);
                }
            }
        }
        return Result.OK();
    }

    /**
     * 面试官提交通知招聘部
     */
    @RequestMapping(value = "queryTzZpz")
    public Result queryTzZpz(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        String userid = request.getParameter("userid");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String msbh = jsonObject.getString("msbh");
        HrMsz hrMsz3 = new HrMsz();
        hrMsz3.setSfsc("0");
        hrMsz3.setMsbh(msbh);
        HrMsz hrMsz1 = hrMszService.queryOneByHrMsz(hrMsz3);
        if (hrMsz1 == null) {
            return Result.error("不存在");
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        HrMsz hrMsz2 = new HrMsz();
        hrMsz2.setId(hrMsz1.getId());
        hrMsz2.setMsgtjsj(df.format(new Date()));
        hrMszService.updateById(hrMsz2);

        String zpbRy = weiLianDdXcxService.queryZpbRy();
        if (zpbRy != null && !"".equals(zpbRy)) {
            HrMsz hrMsz = JSONObject.toJavaObject(jsonObject, HrMsz.class);
            Dingkey dingkey = new Dingkey();
            dingkey.setAgentId("716099296");
            dingkey.setAppkey("dinga4cqxwu9my5ybjhg");
            dingkey.setAppsecret("3bgstQ9bJw96xDCV4wYNDe6L6JF52bBVGPYyD7NxqYGqG2Nl5MkJbiWE28uyYjq3");
            String content = "面试已完成！";
            content += "\n\n面试官：" + userid;
            content += "\n\n姓名：" + hrMsz1.getName();
            content += "\n\n住宿：" + hrMsz.getZhusu();
            content += "\n\n面试结果：" + hrMsz.getMianshi();
            content += "\n\n面试评价：" + hrMsz.getBeizhu();
            content += "\n\n送达时间：";
            try {
                DingDBConfig.sendGztz1(zpbRy, dingkey, "面试完成", content, "https://jztdpp.aliwork.com/APP_J7KY7FDIH52I23EK327R/formDetail/FORM-BY866R818HIQJA1I5VHXB5XQG28K3J2JRY7PKZ2?formInstId=" + formInstId);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }

        Integer lb = weiLianDdXcxService.queryYxbTzLb(hrMsz1.getYxgw());
        if (lb > 0) {
            List<String> list = weiLianDdXcxService.queryYxBmHrTz();
            if (list.size() > 0) {
                String zpzRy = "";
                for (int i = 0; i < list.size(); i++) {
                    zpzRy += list.get(i);
                    if (i != list.size() - 1) {
                        zpzRy += ",";
                    }
                }
                HrMsz hrMsz = JSONObject.toJavaObject(jsonObject, HrMsz.class);
                Dingkey dingkey = new Dingkey();
                dingkey.setAgentId("716099296");
                dingkey.setAppkey("dinga4cqxwu9my5ybjhg");
                dingkey.setAppsecret("3bgstQ9bJw96xDCV4wYNDe6L6JF52bBVGPYyD7NxqYGqG2Nl5MkJbiWE28uyYjq3");
                String content = "面试已完成！";
                content += "\n\n面试官：" + userid;
                content += "\n\n姓名：" + hrMsz1.getName();
                content += "\n\n住宿：" + hrMsz.getZhusu();
                content += "\n\n面试结果：" + hrMsz.getMianshi();
                content += "\n\n面试评价：" + hrMsz.getBeizhu();
                content += "\n\n送达时间：";
                try {
                    DingDBConfig.sendGztz1(zpzRy, dingkey, "面试完成", content, "https://jztdpp.aliwork.com/APP_J7KY7FDIH52I23EK327R/formDetail/FORM-BY866R818HIQJA1I5VHXB5XQG28K3J2JRY7PKZ2?formInstId=" + formInstId);
                } catch (ApiException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.OK();
    }


    /**
     * 现有岗位列表
     */
    @RequestMapping(value = "queryXyGw")
    public Result queryXyGw(HttpServletRequest request) {
        String pageNo = request.getParameter("pageNo");
        String pageSize = request.getParameter("pageSize");
        String search = request.getParameter("search");
        PageHelp pageHelp = new PageHelp(pageNo != null ? Integer.parseInt(pageNo) : 1, pageSize != null ? Integer.parseInt(pageSize) : 10);
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        if (search != null && !"".equals(search)) {
            map.put("search", search);
        }
        PageHelpUtil pageHelpUtil1 = weiLianService.queryXyGw(map);
        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);
        return Result.OK(pageHelpUtil1);
    }

    /**
     * 暂停岗位列表
     */
    @RequestMapping(value = "queryZtGw")
    public Result queryZtGw(HttpServletRequest request) {
        String pageNo = request.getParameter("pageNo");
        String pageSize = request.getParameter("pageSize");
        String search = request.getParameter("search");
        PageHelp pageHelp = new PageHelp(pageNo != null ? Integer.parseInt(pageNo) : 1, pageSize != null ? Integer.parseInt(pageSize) : 10);
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        if (search != null && !"".equals(search)) {
            map.put("search", search);
        }
        PageHelpUtil pageHelpUtil1 = weiLianService.queryZtGw(map);
        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);
        return Result.OK(pageHelpUtil1);
    }

    /**
     * 岗位修改
     */
    @RequestMapping(value = "gwXg")
    public Result gwXg(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("userid");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        Integer id = jsonObject.getInteger("id");
        HrGwCity gwCity = hrGwCityService.getById(jsonObject.getString("id"));
        if (gwCity == null) {
            return Result.error("");
        }
        HrGwCity hrGwCity = new HrGwCity();
        hrGwCity.setId(id);
        hrGwCity.setFgw(jsonObject.getString("fgw"));
        hrGwCity.setZcity(jsonObject.getString("zcity"));
        hrGwCityService.updateById(hrGwCity);
        HrSljl hrSljl1 = new HrSljl();
        hrSljl1.setBz("岗位");
        hrSljl1.setLid(jsonObject.getString("id"));
        HrSljl hrSljl = hrSljlService.queryOneByHrSljl(hrSljl1);
        if (hrSljl != null) {
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            String token = null;
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            YdAppkey ydAppkey = new YdAppkey();
            ydAppkey.setAppkey("APP_J7KY7FDIH52I23EK327R");
            ydAppkey.setToken("E1666M91QHIQ9M3Y37EYM88SNWFU20MQCX7PKW5");
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, hrSljl.getSlid(), jsonObject.toJSONString());
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                e.printStackTrace();
            }
            System.out.println(gatewayResult.toString());
        }

        return Result.OK();
    }

    /**
     * 开启招聘
     */
    @RequestMapping(value = "kqZp")
    public Result kqZp(HttpServletRequest request) {
        String userid = request.getParameter("userid");
        String id = request.getParameter("id");
        HrGwCity gwCity = hrGwCityService.getById(id);
        if (gwCity == null) {
            return Result.error("");
        }
        HrGwCity hrGwCity = new HrGwCity();
        hrGwCity.setId(gwCity.getId());
        hrGwCity.setSfsc("0");
        hrGwCityService.updateById(hrGwCity);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("fgw", gwCity.getFgw());
        jsonObject.put("zcity", gwCity.getZcity());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_J7KY7FDIH52I23EK327R");
        ydAppkey.setToken("E1666M91QHIQ9M3Y37EYM88SNWFU20MQCX7PKW5");
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObject.toJSONString(), userid, "FORM-FC866091QGIQB43Q5K3DNA4K96CP23T3LX7PKZ3");
        } catch (Exception e) {
            e.printStackTrace();
        }
        HrSljl hrSljl = new HrSljl();
        hrSljl.setBz("岗位");
        hrSljl.setLid(hrGwCity.getId().toString());
        hrSljl.setCjsj(new Date());
        if (gatewayResult != null) {
            hrSljl.setSlid(gatewayResult.getResult());
        }
        hrSljlService.save(hrSljl);
        return Result.OK();
    }

    /**
     * 暂停招聘
     */
    @RequestMapping(value = "ztZp")
    public Result ztZp(HttpServletRequest request) {
        String id = request.getParameter("id");
        HrGwCity gwCity = hrGwCityService.getById(id);
        if (gwCity == null) {
            return Result.error("");
        }
        HrGwCity hrGwCity = new HrGwCity();
        hrGwCity.setId(gwCity.getId());
        hrGwCity.setSfsc("1");
        hrGwCityService.updateById(hrGwCity);
        HrSljl hrSljl1 = new HrSljl();
        hrSljl1.setBz("岗位");
        hrSljl1.setLid(id);
        HrSljl hrSljl = hrSljlService.queryOneByHrSljl(hrSljl1);
        if (hrSljl != null) {
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            String token = null;
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            YdAppkey ydAppkey = new YdAppkey();
            ydAppkey.setAppkey("APP_J7KY7FDIH52I23EK327R");
            ydAppkey.setToken("E1666M91QHIQ9M3Y37EYM88SNWFU20MQCX7PKW5");
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = DingBdLcConfig.scBdSl(token, ydAppkey, "012412221639786136545", hrSljl.getSlid());
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                e.printStackTrace();
            }
            System.out.println(gatewayResult.toString());
            hrSljlService.removeById(hrSljl.getId());
        }
        return Result.OK();
    }

    /**
     * 查询面试结果
     */
    @RequestMapping(value = "cxMsJg")
    public Result cxMsJg(HttpServletRequest request) {
        String name = request.getParameter("name");
        String tel = request.getParameter("tel");
        HrMsz hrMsz = new HrMsz();
        hrMsz.setTel(tel);
        hrMsz.setName(name);
        hrMsz.setSfsc("0");
        HrMsz msz = hrMszService.queryOneByHrMsz(hrMsz);
        if (msz == null) {
            return Result.error("找不到面试结果，请检查后重试");
        }
        HrGwCity hrGwCity1 = new HrGwCity();
        hrGwCity1.setFgwid(msz.getYxgw());
        hrGwCity1.setZcityid(msz.getGwcity());
        HrGwCity hrGwCity = hrGwCityService.queryOneByHrGwCity(hrGwCity1);
        if (hrGwCity == null) {
            return Result.error("找不到面试结果，请检查后重试");
        }
        msz.setYxgw(hrGwCity.getFgw());
        msz.setGwcity(hrGwCity.getZcity());
        if (!"已通过".equals(msz.getMianshi()) && !"未通过".equals(msz.getMianshi())) {
            msz.setMianshi("您的面试结果正在加急确认");
        }

        return Result.OK(msz);
    }

    /**
     * 删除面试表单
     */
    @RequestMapping(value = "scMsz")
    public Result scMsz(HttpServletRequest request) {
        String msbh = request.getParameter("msbh");
        HrMsz hrMsz2 = new HrMsz();
        hrMsz2.setSfsc("0");
        hrMsz2.setMsbh(msbh);
        HrMsz hrMsz = hrMszService.queryOneByHrMsz(hrMsz2);
        if (hrMsz == null) {
            return Result.error("找不到面试资料");
        }
        HrMsz hrMsz1 = new HrMsz();
        hrMsz1.setId(hrMsz.getId());
        hrMsz1.setSfsc("1");
        hrMszService.updateById(hrMsz1);
        return Result.OK();
    }

    /**当天数据*/
    @RequestMapping(value = "queryDtSj")
    public Result queryDtSj(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.queryDtSj();

        return Result.OK(list);
    }

    /**当月数据*/
    @RequestMapping(value = "queryDySj")
    public Result queryDySj(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.queryDySj();
        double a = 0;
        double b = 0;
        for (int i = 0; i < list.size(); i++) {
            a += list.get(i).get("hz") != null ? Double.parseDouble(list.get(i).get("hz").toString()) : 0.0;
            b += list.get(i).get("tg") != null ? Double.parseDouble(list.get(i).get("tg").toString()) : 0;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("name", "总计");
        map.put("hz", a);
        map.put("tg", b);
        double tgl1 = ((b / a) * 100);
        String tgl = tgl1 + "";
        String substring = tgl.substring(tgl.indexOf(".") + 1);
        if (substring.length() > 2) {
            tgl = tgl.substring(0, tgl.indexOf(".") + 3);
        }
        map.put("tgl", tgl + "%");
        list.add(map);

        return Result.OK(list);
    }

    /**当周数据*/
    @RequestMapping(value = "queryDzSj")
    public Result queryDzSj(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.queryDzSj();
        double a = 0;
        double b = 0;
        for (int i = 0; i < list.size(); i++) {
            a += list.get(i).get("hz") != null ? Double.parseDouble(list.get(i).get("hz").toString()) : 0.0;
            b += list.get(i).get("tg") != null ? Double.parseDouble(list.get(i).get("tg").toString()) : 0;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("name", "总计");
        map.put("hz", a);
        map.put("tg", b);
        double tgl1 = ((b / a) * 100);
        String tgl = tgl1 + "";
        String substring = tgl.substring(tgl.indexOf(".") + 1);
        if (substring.length() > 2) {
            tgl = tgl.substring(0, tgl.indexOf(".") + 3);
        }
        map.put("tgl", tgl + "%");
        list.add(map);

        return Result.OK(list);
    }

    /**上周数据*/
    @RequestMapping(value = "querySzSj")
    public Result querySzSj(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.querySzSj();
        double a = 0;
        double b = 0;
        for (int i = 0; i < list.size(); i++) {
            a += list.get(i).get("hz") != null ? Double.parseDouble(list.get(i).get("hz").toString()) : 0.0;
            b += list.get(i).get("tg") != null ? Double.parseDouble(list.get(i).get("tg").toString()) : 0;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("name", "总计");
        map.put("hz", a);
        map.put("tg", b);
        double tgl1 = ((b / a) * 100);
        String tgl = tgl1 + "";
        String substring = tgl.substring(tgl.indexOf(".") + 1);
        if (substring.length() > 2) {
            tgl = tgl.substring(0, tgl.indexOf(".") + 3);
        }
        map.put("tgl", tgl + "%");
        list.add(map);

        return Result.OK(list);
    }


    @RequestMapping(value = "queryDyySj")
    public Result queryDyySj(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.queryDyySj();
        return Result.OK(list);
    }

    @RequestMapping(value = "queryOldyySj")
    public Result queryOldyySj(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.queryOldyySj();
        return Result.OK(list);
    }


    @RequestMapping(value = "queryDnnSj")
    public Result queryDnnSj(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.queryDnnSj();
        return Result.OK(list);
    }

    @RequestMapping(value = "querySnnSj")
    public Result querySnnSj(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.querySnnSj();
        return Result.OK(list);
    }

    @RequestMapping(value = "queryNgwyx")
    public Result queryNgwyx(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.queryNgwyx();
        return Result.OK(list);
    }

    @RequestMapping(value = "queryYgwyx")
    public Result queryYgwyx(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.queryYgwyx();
        return Result.OK(list);
    }

    @RequestMapping(value = "queryBmmb")
    public Result queryBmmb(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.queryBmmb();
        return Result.OK(list);
    }

    @RequestMapping(value = "queryGrmb")
    public Result queryGrmb(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.queryGrmb();
        return Result.OK(list);
    }

    @RequestMapping(value = "querySgrmb")
    public Result querySgrmb(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.querySgrmb();
        return Result.OK(list);
    }

    @RequestMapping(value = "queryYbmmb")
    public Result queryYbmmb(HttpServletRequest request) {
        List<Map<String, Object>> list = weiLianDdXcxService.queryYbmmb();
        return Result.OK(list);
    }

    @RequestMapping(value = "queryGrSj")
    public Result queryGrSj(HttpServletRequest request) {
        String name = request.getParameter("name");
        Integer dm = weiLianService.queryJrDm(name);
        Integer tg = weiLianService.queryJrTg(name);
        Map<String, Object> map = new HashMap<>();
        map.put("dm", dm);
        map.put("tg", tg);
        return Result.OK(map);
    }

    @RequestMapping(value = "queryBmZpQk")
    public Result queryBmZpQk(HttpServletRequest request) {
        String time = request.getParameter("time");
        Map<String, Object> map = new HashMap<>();
        if (time != null && !"".equals(time) && !"[]".equals(time)) {
            JSONObject jsonObject = JSONObject.parseObject(time);
            if (jsonObject != null && jsonObject.size() > 0) {
                if (jsonObject.getDate("start") != null) {
                    map.put("start", jsonObject.getDate("start"));
                }
                if (jsonObject.getDate("end") != null) {
                    map.put("end", jsonObject.getDate("end"));
                }
            }
        }
        List<Map<String, Object>> list = weiLianService.queryBmZpQk(map);
        List<Map<String, Object>> list1 = weiLianService.queryBmFqSg(map);
        Map<String, Object> map1 = new HashMap<>();
        map1.put("list", list);
        map1.put("list1", list1);
        return Result.OK(map1);
    }

    @RequestMapping(value = "queryMsBgByFzr")
    public Result queryMsBgByFzr(HttpServletRequest request) {
        String gwfzr = request.getParameter("gwfzr");
        String time = request.getParameter("time");
        Map<String, Object> map = new HashMap<>();
        map.put("gwfzr", gwfzr);
        if (time != null && !"".equals(time) && !"[]".equals(time)) {
            JSONObject jsonObject = JSONObject.parseObject(time);
            if (jsonObject != null && jsonObject.size() > 0) {
                if (jsonObject.getDate("start") != null) {
                    map.put("start", jsonObject.getDate("start"));
                }
                if (jsonObject.getDate("end") != null) {
                    map.put("end", jsonObject.getDate("end"));
                }
            }
        }
        Map<String, Object> map1 = weiLianService.queryBmFqSgByFzr(map);
        Map<String, Object> map2 = weiLianService.queryBmZpQkByFzr(map);

        Map<String, Object> map3 = new HashMap<>();
        map3.putAll(map1);
        map3.putAll(map2);

        return Result.OK(map3);
    }

}
