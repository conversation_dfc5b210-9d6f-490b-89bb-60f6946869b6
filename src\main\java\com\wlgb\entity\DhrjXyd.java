package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 11:51
 */
@Data
@Table(name = "dhrj_xyd")
public class DhrjXyd {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**
     * 订单编号
     */
    private String xddbh;
    /**
     * 门店编号
     */
    private String xbsmc;
    /**
     * 门店名称
     */
    private String xbsmc2;
    /**
     * 城市
     */
    private String city;
    /**
     * 进场日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date xjctime2;
    /**
     * 场次
     */
    private String cc;
    /**
     * 进场时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date xjctime;
    /**
     * 退场时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date xtctime;
    /**
     * 下单人
     */
    private String xfd;
    /**
     * 下单人id
     */
    private String xfdid;
    /*下单人电话*/
    private String xfddh;
    /**
     * 客户姓名
     */
    private String xzk;
    /**
     * 客户电话
     */
    private String xzkdh;
    /**
     * 客户身份证
     */
    private String xzksfz;
    /**
     * 聚会类型
     */
    private String xtdxz;
    /**
     * 聚会类型编号
     */
    private String xtdxzbh;
    /**
     * 单位名称
     */
    private String xdwmc;
    /**
     * 客户渠道
     */
    private String xkhly;
    /**
     * 客户渠道编号
     */
    private String xkhlybh;
    /**
     * 人数
     */
    private Integer xrs;
    /**
     * 桌数
     */
    private Integer xzs;
    /**
     * 人头单价
     */
    private Double xcudrfy;
    /**
     * 场地费优惠后价
     */
    private Double xhfyj;
    /**
     * 场地租赁费原价
     */
    private Double xqkzj;
    /**
     * 备注
     */
    private String xsxbz;
    /**
     * 协议单图片地址
     */
    private String ximagepath;
    /**
     * 协议单pdf地址
     */
    private String xpdfpath;
    /**
     * 提交人
     */
    private String xsender;
    /**
     * 下单时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date xsendtime;
    /**
     * 改单人
     */
    private String xediter;
    /**
     * 改单时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date xedittime;
    /**
     * 删单人
     */
    private String xdeler;
    /**
     * 删单时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date xdeltime;
    /**
     * 是否删除（0：否，1：是）
     */
    private Integer xsfsc;
    /**
     * 宜搭表单实例id
     */
    private String slid;
    /**
     * 是否现场成交
     */
    private String xisxzcj;
    /**
     * 是否泳池单
     */
    private String xsfycd;
    /**
     * 单点餐金额（订餐总额）
     */
    private Double xdcze;
    /**
     * 客户进场前台支付金额
     */
    private Double xkhjczfje;
    /**
     * 招待经理服务金额
     */
    private Double xzdjlfwje;
    /**
     * 策划布景金额
     */
    private Double xchbjje;
    /**
     * 延时费
     */
    private Double xysf;
    /**
     * 轰趴师主持人金额
     */
    private Double xhpsje;
    /**
     * 定做团建横幅金额
     */
    private Double xtjhfje;
    /**
     * 酒水商品套餐金额
     */
    private Double xjsspje;
    /**
     * 定金类型(1:线下,2:线上,3:线上线下同时缴纳)
     */
    private Integer xdjtype;
    /**
     * 已收定金
     */
    private Double xysdj;
    /**
     * 线上定金
     */
    private Double xxsdj;
    /**
     * 线下定金
     */
    private Double xxxdj;
    /**
     * 线下支付转账时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date xzzsj;
    /**
     * 线下支付订单后六位
     */
    private String xzzhlw;
    /**
     * 线上付款码（券码）
     */
    private String xfkm;
    /**
     * 线上验券时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date xyqrq;
    /**
     * 线上验券人
     */
    private String xyqr;
    /**
     * 线上代码
     */
    private String xdm;
    /**
     * 是否触发接口(0:否，1:是)
     */
    private Integer xapi;
}
