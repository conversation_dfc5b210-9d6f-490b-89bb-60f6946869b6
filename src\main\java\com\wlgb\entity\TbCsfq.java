package com.wlgb.entity;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 分区表
 * @Author: jeecg-boot
 * @Date:   2020-10-15
 * @Version: V1.0
 */
@Data
@Table(name = "tb_csfq")
public class TbCsfq{

	/**分区ID*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String fid;
	/**分区名称*/
    private java.lang.String fname;
	/**备注*/
    private java.lang.String fremark;
	/**公司ID*/
    private java.lang.String cid;
	/**代号*/
    private java.lang.Integer fno;
	/**顺序*/
    private java.lang.Integer flno;
}
