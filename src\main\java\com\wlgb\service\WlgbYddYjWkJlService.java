package com.wlgb.service;

import com.wlgb.entity.WlgbYddYjWkJl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/26 21:33
 */
public interface WlgbYddYjWkJlService {
    void save(WlgbYddYjWkJl wlgbYddYjWkJl);

    void updateById(WlgbYddYjWkJl wlgbYddYjWkJl);

    WlgbYddYjWkJl queryWlgbYddYjWkJlByWlgbYddYjWkJl(WlgbYddYjWkJl wlgbYddYjWkJl);

    WlgbYddYjWkJl queryById(Integer id);

    List<WlgbYddYjWkJl> queryListByWlgbYddYjWkJl(WlgbYddYjWkJl wlgbYddYjWkJl);
}
