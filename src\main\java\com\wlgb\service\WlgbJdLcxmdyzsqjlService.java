package com.wlgb.service;

import com.wlgb.entity.WlgbJdLcxmdyzsqjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/20 11:05
 */
public interface WlgbJdLcxmdyzsqjlService {
    void save(WlgbJdLcxmdyzsqjl wlgbJdLcxmdyzsqjl);

    void updateById(WlgbJdLcxmdyzsqjl wlgbJdLcxmdyzsqjl);

    WlgbJdLcxmdyzsqjl queryBySpBhAndSfLrJd(String spbh, Integer sfLrJd);

    List<WlgbJdLcxmdyzsqjl> queryListWlgbJdLcxmdyzsqjl(WlgbJdLcxmdyzsqjl wlgbJdLcxmdyzsqjl);
}
