package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.Hpsl;
import com.wlgb.mapper.HpslMapper;
import com.wlgb.service2.HpslService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/23 10:37
 */
@Service
@DS(value = "second")
public class HpslServiceImpl implements HpslService {
    @Resource
    private HpslMapper hpslMapper;

    @Override
    public void save(Hpsl hpsl) {
        hpslMapper.insertSelective(hpsl);
    }

    @Override
    public Hpsl queryHpSlByHpSl(Hpsl hpsl) {
        return hpslMapper.selectOne(hpsl);
    }

    @Override
    public void updateById(Hpsl hpsl) {
        hpslMapper.updateByPrimaryKeySelective(hpsl);
    }
}
