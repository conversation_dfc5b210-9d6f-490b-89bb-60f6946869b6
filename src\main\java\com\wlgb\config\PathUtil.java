package com.wlgb.config;

import java.io.File;
import java.io.FileNotFoundException;

public class PathUtil {
    public static String getClassResources() throws FileNotFoundException {
//        String path = ClassUtils.getDefaultClassLoader().getResource("static/view/").getPath();
        String path =  (String.valueOf(Thread.currentThread().getContextClassLoader().getResource(""))).replaceAll("file:/", "").replaceAll("%20", " ").trim();
//		String path="C:\\apache-tomcat-8.0.37-windows-x64\\apache-tomcat-8.0.37\\webapps\\weilian3\\uploadFiles\\file\\";
        if(path.indexOf(":") != 1){
            path = File.separator + path;
        }
        return path;
    }
}
