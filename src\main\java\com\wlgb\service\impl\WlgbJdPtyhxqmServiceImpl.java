package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdPtyhxqm;
import com.wlgb.mapper.WlgbJdPtyhxqmMapper;
import com.wlgb.service.WlgbJdPtyhxqmService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/16 19:11
 */
@Service
public class WlgbJdPtyhxqmServiceImpl implements WlgbJdPtyhxqmService {
    @Resource
    private WlgbJdPtyhxqmMapper wlgbJdPtyhxqmMapper;

    @Override
    public void save(WlgbJdPtyhxqm wlgbJdPtyhxqm) {
        wlgbJdPtyhxqm.setId(IdConfig.uuId());
        wlgbJdPtyhxqm.setCreateTime(new Date());
        wlgbJdPtyhxqmMapper.insertSelective(wlgbJdPtyhxqm);
    }
}
