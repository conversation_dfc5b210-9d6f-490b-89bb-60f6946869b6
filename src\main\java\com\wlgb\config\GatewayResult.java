package com.wlgb.config;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/27 19:10
 * @Version 1.0
 */
public class GatewayResult {
    private Boolean success;

    private String result;

    private String errorCode;

    private String errorMsg;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Boolean getSuccess(){
        return success;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }




    @Override
    public String toString() {
        return "GatewayResult{" +
                "success=" + success +
                ", result='" + result + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", errorMsg='" + errorMsg + '\'' +
                '}';
    }
}
