package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.service.*;
import com.wlgb.config.HttpClientUtil;
import com.wlgb.service.WlgbJdDjbbdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.wlgb.config.Tools.isEmpty;

/**
 * <AUTHOR>
 * @Date 2022/11/14 20:54
 * @Version 1.0
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/api")
public class ApiController {

    @Autowired
    private WlgbJdDjLsjlbService wlgbJdDjLsjlbService;
    @Autowired
    private WlgbJdDjbbdService wlgbJdDjbbdService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private OssFileService ossFileService;
    @Autowired
    private WlgbJdYhkService wlgbJdYhkService;
    @Autowired
    private WlgbHxyhDzjlService wlgbHxyhDzjlService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;


    /**
     * 判断项目是否正常运行
     */
    @RequestMapping(value = "fwqTest")
    public Result fwqTest() {

        return Result.OK();
    }

    @GetMapping(value = "queryYsDd")
    public Result queryYsDd(String ddbh) throws FileNotFoundException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("out_trade_no", ddbh);
        jsonObject.put("trade_no", ddbh);
        JSONObject jsonObject1 = YsPayConfig.queryDd(jsonObject);

        return Result.OK(jsonObject1);
    }

    @GetMapping(value = "demo")
    public Result demo() throws FileNotFoundException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("demo", "接口链接");

        return Result.OK(jsonObject);
    }

    /**
     * 开通固定ip后，不能在接口内访问外网，测试一下是否能访问
     *
     * @return
     * @throws FileNotFoundException
     */
    @GetMapping(value = "demohttpurl")
    public Result demohttpurl() throws IOException {
        //调用阿里云函数计算的异步函数
        String uuid = UUID.randomUUID().toString();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("uuid", uuid);
        //在这里进行异步调用函数计算里面的函数即可
        String str = HttpClientUtil.getAsync("http://wlgb.qianquan888.com/weilian-dingdingdzxcx/wlgb/yd/queryLikeVillaByVnameYd", paramMap);
        System.out.println("str===============" + str);
        return Result.OK(str);
    }

    /**
     * 解决：是金钱到银盛账户，但是未显示在定金列表
     *
     * @param request
     * @param response
     * @throws IOException
     */
    @GetMapping(value = "queryDzWtb")
    public void queryDzWtb(HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<WlgbHxyhDzjl> list = wlgbJdDjLsjlbService.queryDzWtb();
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("uuid", IdConfig.uuId());
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/api/tbDzXx", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        response.setContentType("text/html; charset=UTF-8");
        response.getWriter().print("<html><body><script type='text/javascript'>alert('" + (list.size() > 0 ? "共发现" + list.size() + "条未录入的定金，系统正在自动补充，请注意前往定金表单查询！" : "没有需要补充的数据！") + "');</script></body></html>");
        response.getWriter().close();
    }

    @RequestMapping(value = "tbDzXx")
    public Result tbDzXx() {
        List<WlgbHxyhDzjl> list = wlgbJdDjLsjlbService.queryDzWtb();
        list.forEach(l -> {
            WlgbHxyhDzjl wlgbHxyhDzjl = wlgbHxyhDzjlService.queryById(l.getId());
            String outTradeNo = wlgbHxyhDzjl.getOutTradeNo();
            if (outTradeNo != null && outTradeNo.contains("ZFB")) {
                outTradeNo = outTradeNo.replace("ZFB", "");
            }
            if (outTradeNo != null && outTradeNo.contains("WX")) {
                outTradeNo = outTradeNo.replace("WX", "");
            }
            WlgbJdDjLsjlb wlgbJdDjLsjlb = wlgbJdDjLsjlbService.queryBySkBhAndSfSc(outTradeNo, 0);
            if (wlgbJdDjLsjlb == null) {
                WlgbJdDjLsjlb wlgbJdDjLsjlb1 = wlgbJdDjLsjlbService.queryBySkBhAndSfSc(outTradeNo, 1);
                if (wlgbJdDjLsjlb1 != null) {
                    WlgbJdDjLsjlb wlgbJdDjLsjlb2 = new WlgbJdDjLsjlb();
                    wlgbJdDjLsjlb2.setSfsc(0);
                    wlgbJdDjLsjlb2.setId(wlgbJdDjLsjlb1.getId());
                    wlgbJdDjLsjlbService.updateById(wlgbJdDjLsjlb2);
                }
            }
            djbCl(outTradeNo, wlgbHxyhDzjl);
        });


        return Result.OK();
    }

    /**
     * 定金表处理
     *
     * @param outTradeNo   付款编号
     * @param wlgbHxyhDzjl 到账记录表
     */
    public void djbCl(String outTradeNo, WlgbHxyhDzjl wlgbHxyhDzjl) {

        WlgbJdDjLsjlb wlgbJdDjLsjlb1 = wlgbJdDjLsjlbService.queryBySkBhAndSfSc(outTradeNo, 0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        try {
            date = sdf.parse(wlgbHxyhDzjl.getNotifyTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        wlgbJdDjLsjlb1.setSfxyld(0);
        wlgbJdDjLsjlb1.setSfbjdjwk(0);
        wlgbJdDjLsjlb1.setSfdz(1);
        wlgbJdDjLsjlb1.setDzsj(date);
        wlgbJdDjLsjlb1.setSfhk(1);

        List<WlgbJdDjbbd> list = wlgbJdDjbbdService.queryByLshAndSfScAndDjLx(wlgbJdDjLsjlb1.getLsh(), 1);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        wlgbJdDjLsjlb1.setSfbjdj(0);

        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }

        if (list.size() > 0) {
            wlgbJdDjLsjlb1.setSfxyld(1);
            wlgbJdDjLsjlb1.setSfbjdjwk(1);
            String finalToken = token;
            list.forEach(l -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("sfxyld", 1);
                jsonObject.put("sfbjdjwk", 1);
                for (int i = 0; i < 5; i++) {
                    GatewayResult gatewayResult;
                    try {
                        gatewayResult = DingBdLcConfig.xgBdSl(finalToken, ydAppkey, l.getFqrId(), l.getFormInstId(), jsonObject.toJSONString());
                    } catch (Exception e) {
                        e.printStackTrace();
                        gatewayResult = new GatewayResult();
                    }
                    Boolean success = gatewayResult.getSuccess();
                    if (success != null && !success) {
                        if (i == 4) {
                            try {
                                String context1 = "定金是补交到账出错，收款编号：" + outTradeNo + "，错误原因：" + gatewayResult.toString();
                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
//                                    DingDBConfig.sendGztzText(dingkey, "159909317438346", context1);
                            } catch (ApiException e) {
                                e.printStackTrace();
                            }
                        }
                    } else {
                        break;
                    }
                }
            });
            wlgbJdDjLsjlb1.setSfbjdj(1);
        }
        wlgbJdDjLsjlb1.setSkbh(wlgbHxyhDzjl.getOutTradeNo());

        WlgbJdDjbbd wlgbJdDjbbd = new WlgbJdDjbbd();
        BeanUtils.copyProperties(wlgbJdDjLsjlb1, wlgbJdDjbbd);
        JSONObject jsonObjectlc = fqbdsj(wlgbJdDjbbd);
        //定金表唯一标识
        jsonObjectlc.put("djwybs", IdConfig.uuId());


        String formId = "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1";

        GatewayResult gatewayResult = null;
        for (int i = 0; i < 5; i++) {
            try {
                gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObjectlc.toString(), wlgbJdDjbbd.getFqrId(), formId);
            } catch (Exception e) {
                e.printStackTrace();
                gatewayResult = new GatewayResult();
            }
            Boolean success = gatewayResult.getSuccess();
            if (success != null && !success) {
                if (i == 4) {
                    try {
                        String context1 = "定金到账同步表单出错了0，收款编号：" + outTradeNo + "，错误原因：" + gatewayResult.toString();
                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
//                            DingDBConfig.sendGztzText(dingkey, "159909317438346", context1);
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                break;
            }
        }
        String ly = null;
        if (wlgbJdDjbbd.getDjlx() == 2) {
            if (wlgbJdDjbbd.getSspt() == 1) {
                ly = "美团";
            } else {
                ly = "短租";
            }
        }
        WlgbJdYhk wlgbJdYhk = wlgbJdYhkService.querySfScAndLikeBz(0, "定金");
        Boolean skd3 = KingDeeConfig.saveSkd3(wlgbHxyhDzjl.getTradeNo(), wlgbHxyhDzjl.getTotalAmount(), wlgbHxyhDzjl.getOutTradeNo(), jsonObjectlc.getString("hqqm"), ly, date, wlgbJdYhk != null ? wlgbJdYhk.getZh() : "");
        if (skd3) {
            WlgbHxyhDzjl wlgbHxyhDzjl1 = new WlgbHxyhDzjl();
            wlgbHxyhDzjl1.setId(wlgbHxyhDzjl.getId());
            wlgbHxyhDzjl1.setSflrjd(1);
            wlgbHxyhDzjlService.updateById(wlgbHxyhDzjl1);
            WlgbJdDjbbd wlgbJdDjbbd1 = wlgbJdDjbbdService.queryByLshAndSkBhAndSfScAndDjLx(wlgbJdDjLsjlb1.getLsh(), wlgbJdDjLsjlb1.getSkbh(), 0, 1);
            if (wlgbJdDjbbd1 != null) {
                WlgbJdDjbbd wlgbJdDjbbd2 = new WlgbJdDjbbd();
                wlgbJdDjbbd2.setId(wlgbJdDjbbd1.getId());
                wlgbJdDjbbd2.setSflrjd(1);
                wlgbJdDjbbdService.updateById(wlgbJdDjbbd2);
            }
        }

        //获取表单实例id
        if (gatewayResult.getSuccess()) {
            WlgbJdDjLsjlb wlgbJdDjLsjlb = new WlgbJdDjLsjlb();
            wlgbJdDjLsjlb.setFormInstId(gatewayResult.getResult());
            wlgbJdDjLsjlb.setId(wlgbJdDjLsjlb1.getId());
            wlgbJdDjLsjlb.setSfdz(1);
            wlgbJdDjLsjlb.setDzsj(date);
            wlgbJdDjLsjlb.setSfhk(1);
            if (list.size() > 0) {
                wlgbJdDjLsjlb.setSfxyld(1);
                wlgbJdDjLsjlb.setSfbjdjwk(1);
                wlgbJdDjLsjlb.setSfbjdj(1);
            }
            wlgbJdDjLsjlbService.updateById(wlgbJdDjLsjlb);


        }
    }

    //发起表单数据
    public JSONObject fqbdsj(WlgbJdDjbbd wlgbJdDjbbd) {
        JSONObject jsonObject = new JSONObject();
        //流水号
        jsonObject.put("lsh", wlgbJdDjbbd.getLsh());

        //金额
        jsonObject.put("je", wlgbJdDjbbd.getJe());
        jsonObject.put("skbh", wlgbJdDjbbd.getSkbh());
        jsonObject.put("crmbh", wlgbJdDjbbd.getCrmbh());
        jsonObject.put("khdh", wlgbJdDjbbd.getKhdh());
        jsonObject.put("fqr", wlgbJdDjbbd.getFqrId());
        jsonObject.put("fqrName", wlgbJdDjbbd.getFqrName());
        jsonObject.put("fqrId", wlgbJdDjbbd.getFqrId());
        if (Optional.ofNullable(wlgbJdDjbbd.getFqsj()).isPresent()) {
            jsonObject.put("fqsj", wlgbJdDjbbd.getFqsj().getTime());
        }
        jsonObject.put("djlx", wlgbJdDjbbd.getDjlx());
        jsonObject.put("sfdgzz", wlgbJdDjbbd.getSfdgzz());
        //二维码
        List<JSONObject> urlList = YdConfig.setTpList(wlgbJdDjbbd.getEwm(), "线下定金" + wlgbJdDjbbd.getSkbh());
        jsonObject.put("ewm", urlList);
        jsonObject.put("sfdz", wlgbJdDjbbd.getSfdz());
        jsonObject.put("dzsj", wlgbJdDjbbd.getDzsj());
        jsonObject.put("sspt", wlgbJdDjbbd.getSspt());
        jsonObject.put("dzsfhk", wlgbJdDjbbd.getSfhk());
        jsonObject.put("mtmd", wlgbJdDjbbd.getMtmd());
        jsonObject.put("hqqm", wlgbJdDjbbd.getHqqm());
        jsonObject.put("yqsfwc", wlgbJdDjbbd.getYqsfwc());
        jsonObject.put("yqjg", wlgbJdDjbbd.getYqjg());
        jsonObject.put("yqqm", wlgbJdDjbbd.getYqqm());
        if (Optional.ofNullable(wlgbJdDjbbd.getYqsj()).isPresent()) {
            jsonObject.put("yqsj", wlgbJdDjbbd.getYqsj().getTime());
        }
        jsonObject.put("dm", wlgbJdDjbbd.getDm());
        jsonObject.put("qmsj", wlgbJdDjbbd.getQmsj());
        jsonObject.put("yj", wlgbJdDjbbd.getYj());
        jsonObject.put("yqr", wlgbJdDjbbd.getYqr());
        jsonObject.put("sftk", wlgbJdDjbbd.getSftk());
        jsonObject.put("sfsddj", wlgbJdDjbbd.getSfsddj());
        jsonObject.put("sfscdj", wlgbJdDjbbd.getSfscdj());
        jsonObject.put("sfxd", wlgbJdDjbbd.getSfxd());
        jsonObject.put("ddbh", wlgbJdDjbbd.getDdbh());
        jsonObject.put("sfbjdj", wlgbJdDjbbd.getSfbjdj());
        if (Optional.ofNullable(wlgbJdDjbbd.getXdsj()).isPresent()) {
            jsonObject.put("xdsj", wlgbJdDjbbd.getXdsj().getTime());
        }
        String outTradeNo = wlgbJdDjbbd.getSkbh();
        String zffs = "微信";
        if (outTradeNo != null && outTradeNo.contains("ZFB")) {
            zffs = "支付宝";
        }
        jsonObject.put("zffs", zffs);
        jsonObject.put("sfsc", wlgbJdDjbbd.getSfsc());
        jsonObject.put("sfz", wlgbJdDjbbd.getSfz());
        jsonObject.put("sfxyld", wlgbJdDjbbd.getSfxyld());
        jsonObject.put("sfbjdjwk", wlgbJdDjbbd.getSfbjdjwk());
        jsonObject.put("sfbjdj", wlgbJdDjbbd.getSfbjdj());
        jsonObject.put("ytje", 0);

        return jsonObject;
    }

    /**
     * 解决：下了订单，但是定金还没有改状态的定金
     *
     * @return
     */
    @RequestMapping(value = "queryXdWxgZt")
    public Result queryXdWxgZt() {
        List<Map<String, Object>> list = wlgbJdDjLsjlbService.queryXdWxgZt();
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_J15SHV6W8MP3BI9SY7TL");
        ydAppkey.setToken("CFYJVHDVC9SM4CAD1K2F3BZP5NMA2Q1W6A2KKBC1");
        list.forEach(l -> {
            TbXyd tbXyd = wlgbJdDjLsjlbService.queryXydByDdBh(l.get("lsh").toString());
            if (tbXyd != null) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("sfxd", 1);
                jsonObject.put("xdsj", tbXyd.getXkhtjtime());
                jsonObject.put("ddbh", tbXyd.getXddbh());
                try {
                    String gxbd = YdConfig.gxbd(jsonObject.toJSONString(), ydAppkey.getAppkey(), ydAppkey.getToken(), l.get("form_inst_id").toString());
//                    System.out.println(gxbd);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });


        return Result.OK();
    }

    /**
     * 解决：线上定金，美团验券成功但是员工的定金表中没有该笔线上定金
     *
     * @return
     */
    @GetMapping(value = "queryXsDjWlr")
    public Result queryXsDjWlr() {
        List<WlgbMtLog> list = wlgbJdDjLsjlbService.queryXsDjWlr();
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_J15SHV6W8MP3BI9SY7TL");
        ydAppkey.setToken("CFYJVHDVC9SM4CAD1K2F3BZP5NMA2Q1W6A2KKBC1");
        list.forEach(l -> {
            String qm = l.getQh();
            String ddbh = wlgbJdDjLsjlbService.queryYqJl(qm);
            JSONObject jsonObject2 = wlgbJdDjLsjlbService.queryMtMd(l);
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("lsh", ddbh);
            jsonObject1.put("je", l.getSpsmj());
            jsonObject1.put("fqr", l.getYqrid());
            jsonObject1.put("fqrId", l.getYqrid());
            jsonObject1.put("fqrName", l.getYqr());
            jsonObject1.put("fqsj", new Date());
            jsonObject1.put("djlx", 2);
            jsonObject1.put("sspt", 1);
            jsonObject1.put("mtmd", jsonObject2.getString("open_shop_uuid"));
            jsonObject1.put("hqqm", qm);
            jsonObject1.put("yqsfwc", 1);
            jsonObject1.put("yqjg", 1);
            jsonObject1.put("yqqm", qm);
            jsonObject1.put("yqsj", l.getYqsj());
            jsonObject1.put("dm", jsonObject2.getString("zh"));
            jsonObject1.put("qmsj", 1);
            jsonObject1.put("yqr", l.getYqrid());
            jsonObject1.put("yqrId", l.getYqrid());
            jsonObject1.put("yqrName", l.getYqr());
            jsonObject1.put("sfsc", 0);
            jsonObject1.put("sftk", 0);
            jsonObject1.put("sfsddj", 0);
            jsonObject1.put("sfscdj", 0);
            jsonObject1.put("khdh", l.getKhdh());
            jsonObject1.put("crmbh", l.getCrmbh());
            jsonObject1.put("sfbjdj", 0);
            jsonObject1.put("sfxd", 0);
            jsonObject1.put("ytje", 0);
            jsonObject1.put("sfbjdjwk", 0);
            jsonObject1.put("sfxyld", 0);
            //定金表唯一标识
            jsonObject1.put("djwybs", UUID.randomUUID().toString().replace("-", ""));
            GatewayResult gatewayResult1 = null;
            try {
                String xzbd = YdConfig.xzbd(jsonObject1.toJSONString(), l.getYqrid(), ydAppkey.getAppkey(), ydAppkey.getToken(), "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1");
//                gatewayResult1 = YdConfig.xzBdSl(finalToken, ydAppkey, jsonObject1.toJSONString(), l.getYqrid(), "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1");
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println(gatewayResult1);
        });

        return Result.OK(list);
    }

    /**
     * 根据具体单号补充线下定金
     */
    @GetMapping(value = "queryXxDjBySkBh")
    public Result queryXxDjBySkBh(@RequestParam(value = "skbh", defaultValue = "") String skbh) {
        WlgbHxyhDzjl wlgbHxyhDzjl = wlgbJdDjLsjlbService.queryXxDjBySkBh(skbh);
        if (wlgbHxyhDzjl != null) {
            String outTradeNo = skbh;
            if (outTradeNo != null && outTradeNo.contains("ZFB")) {
                outTradeNo = outTradeNo.replace("ZFB", "");
            }
            if (outTradeNo != null && outTradeNo.contains("WX")) {
                outTradeNo = outTradeNo.replace("WX", "");
            }
            WlgbJdDjLsjlb wlgbJdDjLsjlb = wlgbJdDjLsjlbService.queryBySkBhAndSfSc(outTradeNo, 0);
            if (wlgbJdDjLsjlb == null) {
                WlgbJdDjLsjlb wlgbJdDjLsjlb1 = wlgbJdDjLsjlbService.queryBySkBhAndSfSc(outTradeNo, 1);
                if (wlgbJdDjLsjlb1 != null) {
                    WlgbJdDjLsjlb wlgbJdDjLsjlb2 = new WlgbJdDjLsjlb();
                    wlgbJdDjLsjlb2.setSfsc(0);
                    wlgbJdDjLsjlb2.setId(wlgbJdDjLsjlb1.getId());
                    wlgbJdDjLsjlbService.updateById(wlgbJdDjLsjlb2);
                }
            }
            String s = null;
            Map<String, String> map = new HashMap<>();
            map.put("id", wlgbHxyhDzjl.getId());
            String url = "http://47.113.125.34:8080/weilian-dingdingdzxcx/wlgb/hxyh/tbDzXx";
            try {
                s = HttpClientUtil.get(url, map);
            } catch (IOException e) {
                log.info("*******queryXxDjBySkBh接口eeeeeeee*********{}", e);
            }
            return Result.OK();
        } else {
            return Result.error("数据已存在或找不到！");
        }

    }

    /**
     * 根据验券券号单独补充定金表数据
     */
    @GetMapping(value = "queryXsDjByQh")
    public Result queryXsDjByQh(@RequestParam(name = "yqqm", defaultValue = "") String yqqm) {
        WlgbMtLog wlgbMtLog = wlgbJdDjLsjlbService.queryXsDjByQh(yqqm);
        if (wlgbMtLog != null) {
            YdAppkey ydAppkey = new YdAppkey();
            ydAppkey.setAppkey("APP_J15SHV6W8MP3BI9SY7TL");
            ydAppkey.setToken("CFYJVHDVC9SM4CAD1K2F3BZP5NMA2Q1W6A2KKBC1");
            String qm = wlgbMtLog.getQh();
            String ddbh = wlgbJdDjLsjlbService.queryYqJl(qm);
            JSONObject jsonObject2 = wlgbJdDjLsjlbService.queryMtMd(wlgbMtLog);
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("lsh", ddbh);
            jsonObject1.put("je", wlgbMtLog.getSpsmj());
            jsonObject1.put("fqr", wlgbMtLog.getYqrid());
            jsonObject1.put("fqrId", wlgbMtLog.getYqrid());
            jsonObject1.put("fqrName", wlgbMtLog.getYqr());
            jsonObject1.put("fqsj", new Date());
            jsonObject1.put("djlx", 2);
            jsonObject1.put("sspt", 1);
            jsonObject1.put("mtmd", jsonObject2.getString("open_shop_uuid"));
            jsonObject1.put("hqqm", qm);
            jsonObject1.put("yqsfwc", 1);
            jsonObject1.put("yqjg", 1);
            jsonObject1.put("yqqm", qm);
            jsonObject1.put("yqsj", wlgbMtLog.getYqsj());
            jsonObject1.put("dm", jsonObject2.getString("zh"));
            jsonObject1.put("qmsj", 1);
            jsonObject1.put("yqr", wlgbMtLog.getYqrid());
            jsonObject1.put("yqrId", wlgbMtLog.getYqrid());
            jsonObject1.put("yqrName", wlgbMtLog.getYqr());
            jsonObject1.put("sfsc", 0);
            jsonObject1.put("sftk", 0);
            jsonObject1.put("sfsddj", 0);
            jsonObject1.put("sfscdj", 0);
            jsonObject1.put("khdh", wlgbMtLog.getKhdh());
            jsonObject1.put("crmbh", wlgbMtLog.getCrmbh());
            jsonObject1.put("sfbjdj", 0);
            jsonObject1.put("sfxd", 0);
            jsonObject1.put("ytje", 0);
            jsonObject1.put("sfbjdjwk", 0);
            jsonObject1.put("sfxyld", 0);
            //定金表唯一标识
            jsonObject1.put("djwybs", UUID.randomUUID().toString().replace("-", ""));
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            String token = null;
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObject1.toString(), wlgbMtLog.getYqrid(), "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1");
            } catch (Exception e) {
                e.printStackTrace();
            }
            String xzbd = YdConfig.xzbd(jsonObject1.toJSONString(), wlgbMtLog.getYqrid(), ydAppkey.getAppkey(), ydAppkey.getToken(), "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1");
            System.out.println(gatewayResult);
            System.out.println(xzbd);
            return Result.OK();
        } else {
            return Result.error("数据已存在或找不到！");
        }
    }

    @GetMapping(value = "test3")
    public Result test3() {

        return Result.OK("测试部署接口3");
    }

    /**
     * 解决定金已下单未改状态，根据流水号修改
     *
     * @param lsh 流水号
     */
    @GetMapping(value = "queryDjYxdWxgByLsh")
    public Result queryDjYxdWxgByLsh(@RequestParam(name = "lsh", defaultValue = "") String lsh) {
        List<WlgbJdDjbbd> list = wlgbJdDjbbdService.queryDjYxdWxgByLsh(lsh);
        if (list == null || list.size() == 0) {
            return Result.error("流水号错误！");
        }
        for (WlgbJdDjbbd wlgbJdDjLsjlb : list) {
            TbXyd tbXyd = wlgbJdDjLsjlbService.queryXydByDdBh(wlgbJdDjLsjlb.getLsh());
            if (tbXyd == null) {
                continue;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("sfxd", 1);
            jsonObject.put("xdsj", tbXyd.getXkhtjtime());
            jsonObject.put("ddbh", tbXyd.getXddbh());
            YdAppkey ydAppkey = new YdAppkey();
            ydAppkey.setAppkey("APP_J15SHV6W8MP3BI9SY7TL");
            ydAppkey.setToken("CFYJVHDVC9SM4CAD1K2F3BZP5NMA2Q1W6A2KKBC1");
            try {
                String gxbd = YdConfig.gxbd(jsonObject.toJSONString(), ydAppkey.getAppkey(), ydAppkey.getToken(), wlgbJdDjLsjlb.getFormInstId());
                System.out.println(gxbd);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return Result.OK(list);
    }

    /**
     * 处理crm当天的客户id重复数据
     */
    @GetMapping(value = "queryCrmData")
    public Result queryCrmData() {
        //设置宜搭应用配置
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_J15SHV6W8MP3BI9SY7TL");
        ydAppkey.setToken("CFYJVHDVC9SM4CAD1K2F3BZP5NMA2Q1W6A2KKBC1");
        //获取当前时间
        Date date = new Date();
        date.setHours(0);
        date.setMinutes(0);
        date.setSeconds(0);

        //使用当前一天加上一天
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 1);
        Date date1 = calendar.getTime();
        JSONObject jsonObject3 = new JSONObject();
        //查询状态为0、1的
        jsonObject3.put("qxxzt", new int[]{0, 1});
        //设置查询创建时间范围为当天
        jsonObject3.put("qcjsj", new String[]{date.getTime() + "", date1.getTime() + ""});
        //获取数据
        String datas = YdConfig.getDatas(ydAppkey, jsonObject3.toJSONString(), "FORM-NT766881WWDV73OD5QI52DV0G5ON3HZ9OJ4WKI", "1");
        JSONObject jsonObject = JSON.parseObject(datas);
        int count = jsonObject.getJSONObject("result") != null ? jsonObject.getJSONObject("result").getInteger("totalCount") : 0;
        //分页判断
        double b = count / 100.00;
        int a = (int) b;
        int c = b > a ? a + 1 : a;
        List<String> list = new ArrayList<>();
        if (c > 0) {
            //将第一页获取到的客户id存起来
            JSONObject result = jsonObject.getJSONObject("result");
            JSONArray data = result.getJSONArray("data");
            for (int i = 0; i < data.size(); i++) {
                JSONObject formData = data.getJSONObject(i).getJSONObject("formData");
                String external_user_id = formData.getString("external_user_id");
                if (external_user_id != null && !"".equals(external_user_id)) {
                    list.add(external_user_id);
                }

            }
        }
        //分页查询，存储客户id
        for (int i = 2; i <= c; i++) {
            String datas1 = YdConfig.getDatas(ydAppkey, jsonObject3.toJSONString(), "FORM-NT766881WWDV73OD5QI52DV0G5ON3HZ9OJ4WKI", i + "");
            JSONObject jsonObject1 = JSON.parseObject(datas1);
            JSONObject result = jsonObject1.getJSONObject("result");
            JSONArray data = result.getJSONArray("data");
            for (int k = 0; k < data.size(); k++) {
                JSONObject formData = data.getJSONObject(k).getJSONObject("formData");
                String external_user_id = formData.getString("external_user_id");
                if (external_user_id != null && !"".equals(external_user_id)) {
                    list.add(external_user_id);
                }
            }
        }


        //客户id去重
        list = list.stream().distinct().collect(Collectors.toList());


        List<String> list1 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        //将所有客户id遍历查询，查询是否存在重复
        list.forEach(l -> {
            JSONObject jsonObject1 = new JSONObject();
            //客户id
            jsonObject1.put("external_user_id", l);
            //状态为0、1
            jsonObject1.put("qxxzt", new int[]{0, 1});
            String datas1 = YdConfig.getDatas(ydAppkey, jsonObject1.toJSONString(), "FORM-NT766881WWDV73OD5QI52DV0G5ON3HZ9OJ4WKI", "1");
            JSONObject jsonObject2 = JSON.parseObject(datas1).getJSONObject("result");
            Integer totalCount = jsonObject2.getInteger("totalCount");
            if (totalCount > 1) {
                JSONArray data1 = jsonObject2.getJSONArray("data");
                list1.add(l);
                //循环根据客户id查询到的结果，排除第一条不执行，其他的执行修改更改状态
                for (int i = 1; i < data1.size(); i++) {
                    JSONObject jsonObject4 = data1.getJSONObject(i);
                    JSONObject formData = jsonObject4.getJSONObject("formData");
                    String crmbh = formData.getString("crmbh");
                    list2.add(crmbh);
                    String formInstId = jsonObject4.getString("formInstId");
                    JSONObject jsonObject5 = new JSONObject();
                    jsonObject5.put("qxxzt", 2);
                    String gxbd = YdConfig.gxbd(jsonObject5.toJSONString(), ydAppkey.getAppkey(), ydAppkey.getToken(), formInstId);
                    System.out.println(gxbd);
                }
            }
//            System.out.println(jsonObject2);
        });

        //防止上面未修改成功，再修改一遍数据库
        list2.forEach(l -> {
            wlgbJdDjLsjlbService.updateCrmXxZt(l);
        });

        return Result.OK(list2);
    }

    /**
     * 查询所有新场地费下载的记录
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "zxCdfTableList")
    public Result zxCdfTableList(HttpServletRequest request) {
        String currentPage = request.getParameter("currentPage");
        String pageSize = request.getParameter("pageSize");
        String search = request.getParameter("searchKey");
        if (currentPage.isEmpty() || pageSize.isEmpty()) {
            return Result.OK("缺少参数");
        }
        PageHelp pageHelp = new PageHelp(Integer.valueOf((currentPage != null && !"".equals(currentPage) ? currentPage : "1")), Integer.valueOf((pageSize != null && !"".equals(pageSize) ? pageSize : "10")));
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);

        log.info("***************{}", pageHelpUtil);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        if (!isEmpty(search)) {
            map.put("search", search);
        }
        PageHelpUtil helpUtil = weiLianDdXcxService.querySjCdfNewJl(map);
        helpUtil = PageConfig.pageHelpUtil(helpUtil, pageHelp);
        return Result.OK(helpUtil);
    }

    /**
     * 场地费表下载
     */
    @RequestMapping(value = "zxCdfTableDownLoad")
    public Result zxCdfTableDownLoad(HttpServletRequest request) {
        String userid = request.getParameter("userid");
        String username = request.getParameter("username");
        String xzkssj = request.getParameter("xzkssj");
        String xzjssj = request.getParameter("xzjssj");
        if (userid.isEmpty() || xzkssj.isEmpty() || xzjssj.isEmpty() || username.isEmpty()) {
            return Result.OK("缺少参数");
        }
        //如果上线了新的对账 使用下面的 注释上面的 重启项目
        queryXCdf1("直营", userid, username, xzkssj, xzjssj);
        queryXCdf1("加盟", userid, username, xzkssj, xzjssj);
        return Result.OK("ok");
    }

    public void queryXCdf1(String bsxz, String userid, String username, String xzkssj, String xzjssj) {
        //执行场地费存储过程
        weiLianDdXcxService.zxXCdf(bsxz, xzkssj, xzjssj);
        List<Map<String, Object>> list = weiLianDdXcxService.queryDzyXCdf();
        //1、创建工作簿
        Workbook wb = new XSSFWorkbook();
        //1.1、设置表格的格式----居中
        CellStyle cs = wb.createCellStyle();
        //设置水平对齐的样式为居中对齐;
        cs.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        //设置垂直对齐的样式为居中对齐;
        cs.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        Sheet sheet1 = wb.createSheet("场地费表");
        Row row1 = sheet1.createRow(0);
        Cell cell1 = row1.createCell(0);
        try {
            cell1.setCellStyle(cs);
            cell1.setCellValue("对账日期");
            cell1 = row1.createCell(1);
            cell1.setCellStyle(cs);
            cell1.setCellValue("订单编号");
            cell1 = row1.createCell(2);
            cell1.setCellStyle(cs);
            cell1.setCellValue("编号");
            cell1 = row1.createCell(3);
            cell1.setCellStyle(cs);
            cell1.setCellValue("城市");
            cell1 = row1.createCell(4);
            cell1.setCellStyle(cs);
            cell1.setCellValue("门店");
            cell1 = row1.createCell(5);
            cell1.setCellStyle(cs);
            cell1.setCellValue("业务员");
            cell1 = row1.createCell(6);
            cell1.setCellStyle(cs);
            cell1.setCellValue("退场时间");
            cell1 = row1.createCell(7);
            cell1.setCellStyle(cs);
            cell1.setCellValue("场次");
            cell1 = row1.createCell(8);
            cell1.setCellStyle(cs);
            cell1.setCellValue("场数");
            cell1 = row1.createCell(9);
            cell1.setCellStyle(cs);
            cell1.setCellValue("优惠后场地费");
            cell1 = row1.createCell(10);
            cell1.setCellStyle(cs);
            cell1.setCellValue("场地费原价");
            cell1 = row1.createCell(11);
            cell1.setCellStyle(cs);
            cell1.setCellValue("尾款");
            cell1 = row1.createCell(12);
            cell1.setCellStyle(cs);
            cell1.setCellValue("电费收入");
            cell1 = row1.createCell(13);
            cell1.setCellStyle(cs);
            cell1.setCellValue("餐费收入");
            cell1 = row1.createCell(14);
            cell1.setCellStyle(cs);
            cell1.setCellValue("转回公司商品金额");
            cell1 = row1.createCell(15);
            cell1.setCellStyle(cs);
            cell1.setCellValue("策划收入");
            cell1 = row1.createCell(16);
            cell1.setCellStyle(cs);
            cell1.setCellValue("增值服务收入");
            cell1 = row1.createCell(17);
            cell1.setCellStyle(cs);
            cell1.setCellValue("其他收入");
            cell1 = row1.createCell(18);
            cell1.setCellStyle(cs);
            cell1.setCellValue("收入");
            cell1 = row1.createCell(19);
            cell1.setCellStyle(cs);
            cell1.setCellValue("优惠支出");
            cell1 = row1.createCell(20);
            cell1.setCellStyle(cs);
            cell1.setCellValue("盈余");
            cell1 = row1.createCell(21);
            cell1.setCellStyle(cs);
            cell1.setCellValue("增值服务支出");
            cell1 = row1.createCell(22);
            cell1.setCellStyle(cs);
            cell1.setCellValue("备注");
            cell1 = row1.createCell(23);
            cell1.setCellStyle(cs);
            cell1.setCellValue("线下定金");
            cell1 = row1.createCell(24);
            cell1.setCellStyle(cs);
            cell1.setCellValue("线上定金");
            cell1 = row1.createCell(25);
            cell1.setCellStyle(cs);
            cell1.setCellValue("店长奖金池");
            cell1 = row1.createCell(26);
            cell1.setCellStyle(cs);
            cell1.setCellValue("公司应收");
            cell1 = row1.createCell(27);
            cell1.setCellStyle(cs);
            cell1.setCellValue("店长");
            cell1 = row1.createCell(28);
            cell1.setCellStyle(cs);
            cell1.setCellValue("场次类别");
            cell1 = row1.createCell(29);
            cell1.setCellStyle(cs);
            cell1.setCellValue("平台券码");
            cell1 = row1.createCell(30);
            cell1.setCellStyle(cs);
            cell1.setCellValue("补交平台券码");
            cell1 = row1.createCell(31);
            cell1.setCellStyle(cs);
            cell1.setCellValue("别墅所属账套");
        } catch (Exception e) {
            e.printStackTrace();
        }

        SimpleDateFormat df2 = new SimpleDateFormat("yyyy年MM月dd日");
        //5、写入实体数据
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = new JSONObject(list.get(i));
            if (jsonObject.size() > 0) {
                //3.2、创建行----内容行
                row1 = sheet1.createRow(i + 1);
                Cell cell2 = row1.createCell(0);
                cell2.setCellValue(jsonObject.getDate("dzrq") != null ? df2.format(jsonObject.getDate("dzrq")) : "");
                cell2.setCellStyle(cs);
                cell2 = row1.createCell(1);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("ddbh") != null ? jsonObject.getString("ddbh") : "");
                cell2 = row1.createCell(2);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("bh") != null ? jsonObject.getString("bh") : "2");
                cell2 = row1.createCell(3);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("city") != null ? jsonObject.getString("city") : "");
                cell2 = row1.createCell(4);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("md") != null ? jsonObject.getString("md") : "");
                cell2 = row1.createCell(5);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("ywy") != null ? jsonObject.getString("ywy") : "");
                cell2 = row1.createCell(6);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("tcsj") != null ? jsonObject.getString("tcsj") : "");
                cell2 = row1.createCell(7);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("cc") != null ? jsonObject.getString("cc") : "");
                cell2 = row1.createCell(8);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getInteger("ccnum") != null ? jsonObject.getInteger("ccnum") : 0);
                cell2 = row1.createCell(9);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("yhhcdf") != null ? jsonObject.getDouble("yhhcdf") : 0);
                cell2 = row1.createCell(10);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("cdfyj") != null ? jsonObject.getDouble("cdfyj") : 0);
                cell2 = row1.createCell(11);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("wk") != null ? jsonObject.getDouble("wk") : 0);
                cell2 = row1.createCell(12);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("df") != null ? jsonObject.getDouble("df") : 0);
                cell2 = row1.createCell(13);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("cy") != null ? jsonObject.getDouble("cy") : 0);
                cell2 = row1.createCell(14);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("zhgssp") != null ? jsonObject.getDouble("zhgssp") : 0);
                cell2 = row1.createCell(15);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("ch") != null ? jsonObject.getDouble("ch") : 0);
                cell2 = row1.createCell(16);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("zzfw") != null ? jsonObject.getDouble("zzfw") : 0);
                cell2 = row1.createCell(17);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("qt") != null ? jsonObject.getDouble("qt") : 0);
                cell2 = row1.createCell(18);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("sr") != null ? jsonObject.getDouble("sr") : 0);
                cell2 = row1.createCell(19);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("yhzc") != null ? jsonObject.getDouble("yhzc") : 0);
                cell2 = row1.createCell(20);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("yy") != null ? jsonObject.getDouble("yy") : 0);
                cell2 = row1.createCell(21);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("zzfwzc") != null ? jsonObject.getDouble("zzfwzc") : 0);
                cell2 = row1.createCell(22);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("bz") != null ? jsonObject.getString("bz") : "");
                cell2 = row1.createCell(23);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("xxdj") != null ? jsonObject.getDouble("xxdj") : 0);
                cell2 = row1.createCell(24);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("xsdj") != null ? jsonObject.getDouble("xsdj") : 0);
                cell2 = row1.createCell(25);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("dzjjc") != null ? jsonObject.getDouble("dzjjc") : 0);
                cell2 = row1.createCell(26);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getDouble("gsys") != null ? jsonObject.getDouble("gsys") : 0);
                cell2 = row1.createCell(27);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("zbdz") != null ? jsonObject.getString("zbdz") : "");
                cell2 = row1.createCell(28);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getInteger("ccnum") != null ? jsonObject.getInteger("ccnum") > 1 ? "连场" : "是".equals(jsonObject.getString("sfzm")) ? "周末" : "非周末" : "非周末");
                cell2 = row1.createCell(29);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("fkm"));
                cell2 = row1.createCell(30);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("bjfkm"));
                cell2 = row1.createCell(31);
                cell2.setCellStyle(cs);
                cell2.setCellValue(jsonObject.getString("bszt"));
            }
        }
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        Calendar calendar = Calendar.getInstance();
        String format = df1.format(calendar.getTime());

        //6、将文件储存到指定位置
        String upload = "";
        try {
            String url = FileConfig.getFileAbsolutePath("static" + File.separator + "img", (format) + ".xlsx");
            FileOutputStream fout = new FileOutputStream(url);
            wb.write(fout);
            fout.close();
            File file = new File(url);
            FileInputStream fileInputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
            try {
                upload = ossFileService.upload(multipartFile);
                if (upload != null && !"".equals(upload)) {
                    upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
                }
                System.out.println(upload);
            } catch (Exception e) {
                e.printStackTrace();
            }
            fileInputStream.close();
            file.delete();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (upload != null && !"".equals(upload)) {
            try {
                String content = df2.format(new Date()) + ("直营".equals(bsxz) ? "直营" : "非直营") + "新场地费表\n" + upload;
                String webhoot = "https://oapi.dingtalk.com/robot/send?access_token=e29f36dcbd922722c9df0976e46de574e80ea96a1c788d96c9f1d7ef89865484";
                String secret = "SEC5511d87046d65b5495122c9309fe913eb8e13970bbfca00ae7618807c2bc54f5";
//                DingDingUtil.sendMsg(webhoot, secret, content, null, false);
                SjCdfNewJl sjCdfNewJl = new SjCdfNewJl();
                sjCdfNewJl.setUrl(upload);
                sjCdfNewJl.setCreatime(new Date());
                sjCdfNewJl.setCzrid(userid);
                sjCdfNewJl.setCzrname(username);
                sjCdfNewJl.setBsxz("直营".equals(bsxz) ? "直营" : "非直营");
                sjCdfNewJl.setXzjssj(xzjssj);
                sjCdfNewJl.setXzkssj(xzkssj);
                weiLianDdXcxService.insertSjCdfNewJl(sjCdfNewJl);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 删除发起流程
     */
    @RequestMapping(value = "zxLcSc")
    public Result zxLcSc(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String fpslid = jsonObject.getString("fpslid");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = new GatewayResult();
        try {
            gatewayResult = DingBdLcConfig.scLcSl(token, ydAppkey, "012412221639786136545", fpslid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!gatewayResult.getSuccess()) {
            try {
                String context1 = "删除发票流程失败，原因：" + gatewayResult.toString();
                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }


        return Result.OK();
    }

    /**
     * 根据城市查询餐饮信息
     */
    @RequestMapping(value = "queryCyXxByCity")
    public Result queryCyXxByCity(HttpServletRequest request) {
        String page = request.getParameter("page");
        String pageSize = request.getParameter("pageSize");
        String jqmc = request.getParameter("jqmc");
        String jqid = request.getParameter("jqid");

        //菜品
        YdAppkey ydAppkey1 = new YdAppkey();
        ydAppkey1.setAppkey("APP_JPQRCIS3ASCKF4BJ7UPZ");
        ydAppkey1.setToken("3J966U61TOUD8M1M8X4WX810EW5G26HBR90MLW2");
        JSONObject jsonObject1 = new JSONObject();
        //集群编号
        if (jqid != null && !"".equals(jqid)) {
            jsonObject1.put("textField_lmu279t4", jqid);
        }
        //集群名称
        if (jqmc != null && !"".equals(jqmc)) {
            jsonObject1.put("textField_lmu279t5", jqmc);
        }
        String zzBdSl = YdConfig.getDatas1(ydAppkey1, jsonObject1.toJSONString(), "FORM-IQ8666B1LKDELZNWBUSLP7SQVQYE3I2TGYTML9", page, pageSize);
        JSONObject jsonObject2 = JSONObject.parseObject(zzBdSl);
        JSONObject result2 = jsonObject2.getJSONObject("result");
        Integer totalCount = result2.getInteger("totalCount");
        Integer currentPage = result2.getInteger("currentPage");
        JSONArray data = result2.getJSONArray("data");
        List<JSONObject> list = new ArrayList<>();
        for (Object o : data) {
            JSONObject jsonObject = (JSONObject) o;
            JSONObject formData = jsonObject.getJSONObject("formData");
            formData.put("formInstId", jsonObject.getString("formInstId"));
            list.add(formData);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("totalCount", totalCount);
        jsonObject.put("currentPage", currentPage);
        jsonObject.put("list", list);

        return Result.OK(jsonObject);
    }

    @GetMapping(value = "test")
    public Result test() throws IOException {
        Boolean bmCl = KingDeeConfig.bmCl("610739120", "重庆");
        return Result.OK();
    }


}
