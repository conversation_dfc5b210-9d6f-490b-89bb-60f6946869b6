package com.wlgb.service3.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.config.PageHelpUtil;
import com.wlgb.entity.Csry;
import com.wlgb.entity.vo.DdhfUtil;
import com.wlgb.mapper1.WeiLianDaiBanMapper;
import com.wlgb.service3.WeiLianDaiBanService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月14日 3:06
 */
@Service
@DS("third")
public class WeiLianDaiBanServiceImpl implements WeiLianDaiBanService {
    @Resource
    private WeiLianDaiBanMapper weiLianDaiBanMapper;

    @Override
    public Csry queryCsRyByUserId(String userid) {
        return weiLianDaiBanMapper.queryCsRyByUserId(userid);
    }

    @Override
    public Csry queryCsRyJmByUserId(String userid) {
        return weiLianDaiBanMapper.queryCsRyJmByUserId(userid);
    }

    @Override
    public Integer queryNotFsHf(Map<String, Object> map) {
        return weiLianDaiBanMapper.queryNotFsHf(map);
    }

    @Override
    public void qkDbLb() {
        weiLianDaiBanMapper.qkDbLb();
    }

    @Override
    public PageHelpUtil queryNotKfList(Map<String, Object> map) {
        List<DdhfUtil> list = weiLianDaiBanMapper.queryNotKfList(map);
        Integer count = weiLianDaiBanMapper.queryNotKfListCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public PageHelpUtil queryNotKfListYwjl(Map<String, Object> map) {
        List<Map<String, Object>> list = weiLianDaiBanMapper.queryNotKfListYwjl(map);
        Integer count = weiLianDaiBanMapper.queryNotKfListCountYwjl(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public void updateHfZt(String xid) {
        weiLianDaiBanMapper.updateHfZt(xid);
    }

    @Override
    public void zjZxr(Map<String, Object> map) {
        weiLianDaiBanMapper.zjZxr(map);
    }
}
