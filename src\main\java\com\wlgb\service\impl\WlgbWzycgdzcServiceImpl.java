package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbWzycgdzc;
import com.wlgb.mapper.WlgbWzycgdzcMapper;
import com.wlgb.service.WlgbWzycgdzcService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/10 22:58
 */
@Service
public class WlgbWzycgdzcServiceImpl implements WlgbWzycgdzcService {
    @Resource
    private WlgbWzycgdzcMapper wlgbWzycgdzcMapper;

    @Override
    public void save(WlgbWzycgdzc wlgbWzycgdzc) {
        wlgbWzycgdzc.setCreateTime(new Date());
        wlgbWzycgdzc.setId(IdConfig.uuId());
        wlgbWzycgdzcMapper.insertSelective(wlgbWzycgdzc);
    }

    @Override
    public void updateById(WlgbWzycgdzc wlgbWzycgdzc) {
        wlgbWzycgdzc.setUpdateTime(new Date());
        wlgbWzycgdzcMapper.updateByPrimaryKeySelective(wlgbWzycgdzc);
    }

    @Override
    public WlgbWzycgdzc queryByBsMcAndSfScAndWpMc(String bsmc, Integer sfsc, String wpmc) {
        Example example = new Example(WlgbWzycgdzc.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bsmc", bsmc);
        criteria.andEqualTo("sfsc", sfsc);
        criteria.andEqualTo("wpmc", wpmc);
        return wlgbWzycgdzcMapper.selectOneByExample(example);
    }
}
