package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.*;
import com.wlgb.service.*;
import com.wlgb.service2.*;
import com.wlgb.service3.DzxydService;
import com.wlgb.service3.WeiLianDaiBanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: Class
 * @author: fwq
 * @date: 2024年05月24日 10:56
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/xydsavetask")
public class XydSaveTaSkController {
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private WlgbJdDjbbdService wlgbJdDjbbdService;
    @Autowired
    private OssFileService ossFileService;
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private WlgbYlxdService wlgbYlxdService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WlgbXydLogService wlgbXydLogService;
    @Autowired
    private WlgbXxbjdjjlService wlgbXxbjdjjlService;
    @Autowired
    private WlgbXsdjbjjlService wlgbXsdjbjjlService;
    @Autowired
    private WlgbBdjlService wlgbBdjlService;
    @Autowired
    private WlgbDksljlService wlgbDksljlService;
    @Autowired
    private DzxydService dzxydService;
    @Autowired
    private JqrConfig jqrConfig;
    @Autowired
    private WeiLianDaiBanService weiLianDaiBanService;
    @Autowired
    private WlgbOrderCyjlService wlgbOrderCyjlService;
    @Autowired
    private WlgbEatTcjlService wlgbEatTcjlService;
    @Autowired
    private WlgbEatCpjlService wlgbEatCpjlService;
    @Autowired
    private WlgbEatScjlService wlgbEatScjlService;
    @Autowired
    private TbYddXydService tbYddXydService;


    /**
     * 下单异步
     */
    @RequestMapping(value = "xydSaveTask")
    public void xydSaveTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        log.info("*****进入xydSaveTask异步请求*******{}", datas);
        String formInstId = request.getParameter("formInstId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        TbXyd tbXyd = JSONObject.toJavaObject(jsonObject, TbXyd.class);
        log.info("*****进入tbXydtbXydtbXydtbXyd*******{}", tbXyd);
        //预留单查询
        WlgbYlxd wlgbYlxd = wlgbYlxdService.queryByDdBhAndSfSc(tbXyd.getXddbh(), 0);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        if (wlgbYlxd != null) {
            List<WlgbJdDjbbd> list = wlgbJdDjbbdService.queryByLshAndSfScAndSfXd(wlgbYlxd.getXddbh(), 0);
            list.forEach(l -> {
                WlgbJdDjbbd wlgbJdDjbbd = new WlgbJdDjbbd();
                wlgbJdDjbbd.setId(l.getId());
                wlgbJdDjbbd.setSfxd(1);
                wlgbJdDjbbd.setSfxyld(0);
                wlgbJdDjbbdService.updateById(wlgbJdDjbbd);
            });
            DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXsendder());
            GatewayResult gatewayResult = scBdSl(dingdingEmployee != null ? dingdingEmployee.getUserid() : "012412221639786136545", wlgbYlxd.getSlid());
            System.out.println("删除预留单：" + gatewayResult.toString());
        }
        if (tbXyd.getXddbh() == null || "".equals(tbXyd.getXddbh())) {
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXsendder());
            String context = (employee != null ? employee.getName() : "") + "正在下单，没有订单编号！！！！";
            String fd = "";
            //房东截取
            if (tbXyd.getXfd() != null && !"".equals(tbXyd.getXfd()) && tbXyd.getXfd().length() > 0) {
                fd = (xzjq(tbXyd.getXfd()));
                if (!"".equals(fd)) {
                    DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(fd);
                    fd = employee1 != null ? employee1.getName() : "";
                }
            }
            context += "\n房东：" + fd + "，" + "租客：" + tbXyd.getXzk() + "\n";
            DingDingUtil.sendMsg("https://oapi.dingtalk.com/robot/send?access_token=d7cddb7adbb6b0dd83462d0e6848f022a0a0410284e30aee67d8ffe61d9c1152", "SEC435041e1793d01fe2e43c025304ba328e8c675a7e164c1a81fd7e54f94161564", context, null, true);
        }
        TbXyd xyd = tbXydService.queryById(tbXyd.getXid());
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        if (villa == null) {
            villa = weiLianService.queryVillaByVname(tbXyd.getXbsmc());
            if (villa != null) {
                tbXyd.setXbsmc(villa.getVid());
            }
        }
        DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
        log.info("*****进入xydSaveTask异步请求*开始请求employee1******{}", employee1);
        if (xyd == null) {
            if (tbXyd.getXhdchtype() == null || "".equals(tbXyd.getXhdchtype()) || "undefined".equals(tbXyd.getXhdchtype()) || "NaN".equals(tbXyd.getXhdchtype())) {
                tbXyd.setXhdchtype("0");
            }
            if (tbXyd.getXkhly() != null && !"".equals(tbXyd.getXkhly())) {
                tbXyd.setXkhly(tbXyd.getXkhly().replace(" ", ""));
            }
            //剧本杀清空
            if ("0".equals(tbXyd.getXsfxyjbs())) {
                tbXyd.setXjbszje(0.0);
                tbXyd.setXjbscjr("");
                tbXyd.setXjbscjrid("");
                tbXyd.setXjbszxr("");
                tbXyd.setXjbsfzcjr("");
                tbXyd.setXjbsfzcjrid("");
            }

            if (tbXyd.getXsfys() == null || "".equals(tbXyd.getXsfys())) {
                tbXyd.setXsfys("0");
            }
            if (!"1".equals(tbXyd.getXsfys())) {
                tbXyd.setXysbz(null);
            }

            //定金选择类型清空
            if ("1".equals(tbXyd.getXdjtype())) {
                tbXyd.setXxsdj(0.0);
                tbXyd.setXxxdj(0.0);
                tbXyd.setXyqr(null);
            }
            if ("2".equals(tbXyd.getXdjtype())) {
                tbXyd.setXxsdj(0.0);
                tbXyd.setXxxdj(0.0);
                tbXyd.setXzzsj(null);
                tbXyd.setXzzhlw(null);
            }
            if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(tbXyd.getXdjtype())) {
                tbXyd.setXysdj(tbXyd.getXxsdj() + tbXyd.getXxxdj());
            }

            //补交定金选择类型清空
            if ("1".equals(tbXyd.getXbjdjlx())) {
                tbXyd.setXxsbj(0.0);
                tbXyd.setXxxbj(0.0);
            }
            if ("2".equals(tbXyd.getXbjdjlx())) {
                tbXyd.setXxsbj(0.0);
                tbXyd.setXxxbj(0.0);
            }
            if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(tbXyd.getXbjdjlx())) {
                tbXyd.setXbjdj(tbXyd.getXxxbj() + tbXyd.getXxsbj());
            }

            if (tbXyd.getXztze() == null || tbXyd.getXztze() == 0) {
                tbXyd.setXztze(0.0);
                tbXyd.setXchjlid(null);
                tbXyd.setXchcjr(null);
                tbXyd.setXchfzcjr(null);
            }
            if (tbXyd.getXsfhps() == null || tbXyd.getXsfhps() == 0) {
                tbXyd.setXhpscjr(null);
                tbXyd.setXhpscjrgh(null);
                tbXyd.setXhpsfzcjr(null);
                tbXyd.setXhpsfzcjrid(null);
                tbXyd.setXhpsfy(0.0);
            }
            if (tbXyd.getXsfzrcs() == null || tbXyd.getXsfzrcs() == 0) {
                tbXyd.setXzrcsrs(0);
                tbXyd.setXzrcsfy(0.0);
                tbXyd.setXzrcscjr(null);
            }

            if (tbXyd.getXisxzcj() == null || tbXyd.getXisxzcj() == 1) {
                tbXyd.setXcjr(null);
                tbXyd.setXcjrgh(null);
            }
            if (tbXyd.getXsfhxgjcj() == null || tbXyd.getXsfhxgjcj() == 1) {
                tbXyd.setXhxcjr(null);
                tbXyd.setXhxcjrgh(null);
            }

            if ((tbXyd.getXdcze() != null ? tbXyd.getXdcze() : 0) == 0) {
                tbXyd.setXbdje(0.0);
                tbXyd.setXbdsl(0);
                tbXyd.setXdcze(0.0);
                tbXyd.setXdccjr(null);
                tbXyd.setXdccjrgh(null);
                tbXyd.setXdcfzcjr(null);
            }
            if ((tbXyd.getXskze() != null ? tbXyd.getXskze() : 0) == 0) {
                tbXyd.setXskje(0.0);
                tbXyd.setXsksl(0);
                tbXyd.setXskze(0.0);
                tbXyd.setXskcjr(null);
                tbXyd.setXskcjrgh(null);
                tbXyd.setXskfzcjr(null);
            }

            //订单类型
            if (tbXyd.getXzdcl() != null) {
                if (tbXyd.getXzdcl() != 4) {
                    tbXyd.setXxdxm("");
                    tbXyd.setXxdsjxm("");
                    tbXyd.setXxdsjxmgh("");
                    tbXyd.setXxddh("");
                }
                if (tbXyd.getXzdcl() == 0) {
                    tbXyd.setXlbjf("");
                }
            }

            //如果下单有修改人直接赋空
            if (tbXyd.getXxgzid() != null && !"".equals(tbXyd.getXxgzid()) && tbXyd.getXxgzid().length() > 0) {
                tbXyd.setXxgzid(null);
            }
            //房东截取
            if (tbXyd.getXfd() != null && !"".equals(tbXyd.getXfd()) && tbXyd.getXfd().length() > 0) {
                tbXyd.setXfd(xzjq(tbXyd.getXfd()));
            }
            //老板截取
            if (tbXyd.getXlbjf() != null && !"".equals(tbXyd.getXlbjf()) && tbXyd.getXlbjf().length() > 0) {
                tbXyd.setXlbjf(xzjq(tbXyd.getXlbjf()));
            }
            //保险成交人
            if (tbXyd.getXbxcjr() != null && !"".equals(tbXyd.getXbxcjr()) && tbXyd.getXbxcjr().length() > 0) {
                tbXyd.setXbxcjr(xzjq(tbXyd.getXbxcjr()));
            }
            //策划经理
            if (tbXyd.getXchjlid() != null && !"".equals(tbXyd.getXchjlid()) && tbXyd.getXchjlid().length() > 0) {
                tbXyd.setXchjlid(xzjq(tbXyd.getXchjlid()));
            }
            //策划成交人
            if (tbXyd.getXchcjr() != null && !"".equals(tbXyd.getXchcjr()) && tbXyd.getXchcjr().length() > 0) {
                tbXyd.setXchcjr(xzjq(tbXyd.getXchcjr()));
            }
            //策划辅助成交人
            if (tbXyd.getXchfzcjr() != null && !"".equals(tbXyd.getXchfzcjr()) && tbXyd.getXchfzcjr().length() > 0) {
                tbXyd.setXchfzcjr(xzjq(tbXyd.getXchfzcjr()));
            }
            //轰趴成交人
            if (tbXyd.getXhpscjr() != null && !"".equals(tbXyd.getXhpscjr()) && tbXyd.getXhpscjr().length() > 0) {
                tbXyd.setXhpscjr(xzjq(tbXyd.getXhpscjr()));
            }
            //CS成交人
            if (tbXyd.getXzrcscjr() != null && !"".equals(tbXyd.getXzrcscjr()) && tbXyd.getXzrcscjr().length() > 0) {
                tbXyd.setXzrcscjr(xzjq(tbXyd.getXzrcscjr()));
            }
            //订餐成交人
            if (tbXyd.getXdccjr() != null && !"".equals(tbXyd.getXdccjr()) && tbXyd.getXdccjr().length() > 0) {
                tbXyd.setXdccjr(xzjq(tbXyd.getXdccjr()));
            }
            //订餐辅助成交人
            if (tbXyd.getXdcfzcjr() != null && !"".equals(tbXyd.getXdcfzcjr()) && tbXyd.getXdcfzcjr().length() > 0) {
                tbXyd.setXdcfzcjr(xzjq(tbXyd.getXdcfzcjr()));
            }
            //烧烤成交人
            if (tbXyd.getXskcjr() != null && !"".equals(tbXyd.getXskcjr()) && tbXyd.getXskcjr().length() > 0) {
                tbXyd.setXskcjr(xzjq(tbXyd.getXskcjr()));
            }
            //烧烤辅助成交人
            if (tbXyd.getXskfzcjr() != null && !"".equals(tbXyd.getXskfzcjr()) && tbXyd.getXskfzcjr().length() > 0) {
                tbXyd.setXskfzcjr(xzjq(tbXyd.getXskfzcjr()));
            }
            //现场成交人
            if (tbXyd.getXcjr() != null && !"".equals(tbXyd.getXcjr()) && tbXyd.getXcjr().length() > 0) {
                tbXyd.setXcjr(xzjq(tbXyd.getXcjr()));
            }
            //后续成交人
            if (tbXyd.getXhxcjr() != null && !"".equals(tbXyd.getXhxcjr()) && tbXyd.getXhxcjr().length() > 0) {
                tbXyd.setXhxcjr(xzjq(tbXyd.getXhxcjr()));
            }
            //剧本杀成交人
            if (tbXyd.getXjbscjr() != null && !"".equals(tbXyd.getXjbscjr()) && tbXyd.getXjbscjr().length() > 0) {
                tbXyd.setXjbscjr(xzjq(tbXyd.getXjbscjr()));
            }
            //剧本杀辅助成交人
            if (tbXyd.getXjbsfzcjr() != null && !"".equals(tbXyd.getXjbsfzcjr()) && tbXyd.getXjbsfzcjr().length() > 0) {
                tbXyd.setXjbsfzcjr(xzjq(tbXyd.getXjbsfzcjr()));
            }
            //轰趴师辅助成交人
            if (tbXyd.getXhpsfzcjr() != null && !"".equals(tbXyd.getXhpsfzcjr()) && tbXyd.getXhpsfzcjr().length() > 0) {
                tbXyd.setXhpsfzcjr(xzjq(tbXyd.getXhpsfzcjr()));
            }
            //fdUserId 是房东ID ,不是房东姓名
            String fdUserId = tbXyd.getXfd();
            tbXyd.setStatus(0);
            DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXsendder());
            tbXyd = fz(tbXyd);
            //获取房东所属部门
            FwqBbb2 fwqBbb2 = weiLianService.queryBbb2ByXm(tbXyd.getXfd());
            log.info("*****进入xydSaveTask异步请求*开始请求fwqBbb2******{}", fwqBbb2);
            if (fwqBbb2 != null) {
                tbXyd.setXfdssbm(fwqBbb2.getBm() != null ? fwqBbb2.getBm() + "" : null);
            }
            // TODO xid由宜搭生成，如果由后端生成就无法避免宜搭的重复提交   ddbh也由宜搭生成
            if (tbXyd.getXid() == null || "".equals(tbXyd.getXid())) {
                tbXyd.setXid(IdConfig.uuId());
            }
            if (tbXyd.getXddbh() == null || "".equals(tbXyd.getXddbh())) {
                SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                Calendar calendar = Calendar.getInstance();
                String format = df1.format(calendar.getTime());
                tbXyd.setXddbh(format);
            }
            tbXyd.setXsendtime(new Date());
            tbXyd.setXsendtime1(new Date());
            tbXyd.setXkhtjtime(new Date());
            Map<String, Object> map = new HashMap<>();
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            String token = null;
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                log.info("*****进入xydSaveTask异步请求*获取token失败******{}", e);
            }
            tbXyd.setXsendder(ding != null ? ding.getUserid() : null);
            //设置成1，下单是不需要调用宜搭绑定的修改的服务回调接口，在修改接口里面添加了判断，如果为1 这不进行修改
            tbXyd.setSfdyjk(1);
            tbXyd.setXscz(null);
            //人数默认值
            if (tbXyd.getXrs() == null) {
                tbXyd.setXrs(0);
            }
            //订餐数量
            if (tbXyd.getXbdsl() == null) {
                tbXyd.setXbdsl(0);
            }
            //烧烤数量
            if (tbXyd.getXsksl() == null) {
                tbXyd.setXsksl(0);
            }
            //策划数量
            if (tbXyd.getXztsl() == null) {
                tbXyd.setXztsl(0);
            }
            //赠送会员卡
            if (tbXyd.getXzshyksl() == null || "".equals(tbXyd.getXzshyksl())) {
                tbXyd.setXzshyksl("0");
            }
            //线上定金
            if (tbXyd.getXxsdj() == null) {
                tbXyd.setXxsdj(0.0);
            }
            //线下定金
            if (tbXyd.getXxxdj() == null) {
                tbXyd.setXxxdj(0.0);
            }
            //订餐总额
            if (tbXyd.getXdcze() == null) {
                tbXyd.setXdcze(0.0);
            }
            //烧烤总额
            if (tbXyd.getXskze() == null) {
                tbXyd.setXskze(0.0);
            }
            //策划总额
            if (tbXyd.getXztze() == null) {
                tbXyd.setXztze(0.0);
            }
            //补交定金
            if (tbXyd.getXbjdj() == null) {
                tbXyd.setXbjdj(0.0);
            }
            //线上租金
            if (tbXyd.getXxszj() == null) {
                tbXyd.setXxszj(0.0);
            }
            //线下补交定金
            if (tbXyd.getXxxbj() == null) {
                tbXyd.setXxxbj(0.0);
            }
            //线上补交定金
            if (tbXyd.getXxsbj() == null) {
                tbXyd.setXxsbj(0.0);
            }
            //已收增值定金
            if (tbXyd.getXyszzdj() == null || "".equals(tbXyd.getXyszzdj())) {
                tbXyd.setXyszzdj("0");
            }
            //人数默认值
            if (tbXyd.getXzzfybj() == null) {
                tbXyd.setXzzfybj(0.0);
            }
            //订单类型判断（校代与普通）
            if (tbXyd.getXzdcl() != null && tbXyd.getXzdcl() == 4) {
                tbXyd.setXddtype("校代");
            } else {
                tbXyd.setXddtype("普通");
            }
            //摄影图片数量
            if (tbXyd.getXsynum() == null) {
                tbXyd.setXsynum(0);
            }
            //定制横幅内容
            if (tbXyd.getXhfnr() == null) {
                tbXyd.setXhfnr("无");
            }
            //定制大屏内容
            if (tbXyd.getXdpnr() == null) {
                tbXyd.setXdpnr("无");
            }
            //是否购买三件套：0否，1是
            if (tbXyd.getXsfgmsjt() == null) {
                tbXyd.setXsfgmsjt("0");
            }
            //主播姓名
            if (tbXyd.getXzbxm() == null) {
                tbXyd.setXzbxm("");
            }
            //主播id
            if (tbXyd.getXzbuserid() == null) {
                tbXyd.setXzbuserid("");
            }
            //抖音数量
            if (tbXyd.getXdynum() == null) {
                tbXyd.setXdynum("0");
            }
            //关注数量
            if (tbXyd.getXgznum() == null) {
                tbXyd.setXgznum("0");
            }
            //收藏数量
            if (tbXyd.getXscnum() == null) {
                tbXyd.setXscnum("0");
            }

            //已收增值定金判断
            tbXyd.setXyszzdj(tbXyd.getXyszzdj() != null && !"".equals(tbXyd.getXyszzdj()) ? tbXyd.getXyszzdj() : "0");
            String finalToken = token;
            //电子协议单生成
            try {
                log.info("*****进入xydSaveTask异步请求*准备生成图片了******{}", tbXyd);
                map = dzXyd(tbXyd, jsonObject);
                log.info("*****进入xydSaveTask异步请求*准备生成图片了222******{}", tbXyd);
            } catch (Exception e) {
                log.info("*****进入xydSaveTask异步请求*生成协议单失败******{}", e);
                try {
                    DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : tbXyd.getXfd()) + "下单，订单编号”" + tbXyd.getXddbh() + "“协议单生成报错了，错误原因：" + e.getMessage());
                } catch (ApiException apiException) {
                    apiException.printStackTrace();
                }
            }
            if (!map.isEmpty()) {
                tbXyd.setXimagepath((String) map.get("png"));
            }
            String filePath = FileConfig.getFileAbsolutePath2("static" + File.separator + "img" + File.separator + tbXyd.getXimagepath());
            File file = null;
            String upload = "";
            try {
                file = new File(filePath);
                if (!file.exists()) {
                    log.error("文件不存在: {}", filePath);
                    //判断图片存在不存在,如果不存在将重新生成电子协议单
                    dzXyd(tbXyd, jsonObject);
                }
                FileInputStream fileInputStream = new FileInputStream(file);
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
                upload = ossFileService.upload(multipartFile);
                fileInputStream.close();
                if (upload != null && !"".equals(upload)) {
                    upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
                }
            } catch (Exception e) {
                log.info("*****进入xydSaveTask异步请求*上传oss文件报错******{}", e);
                try {
                    DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : tbXyd.getXfd()) + "下单，订单编号”" + tbXyd.getXddbh() + "“电子协议单上传阿里云报错了");
                } catch (ApiException apiException) {
                    apiException.printStackTrace();
                }
            }

            tbXyd.setXpdfpath(upload);
            tbXyd.setXimagepath(upload);

            tbXydService.save(tbXyd);

            log.info("*****进入xydSaveTask异步请求*开始请求tbXyd******{}", tbXyd);
            JSONArray cyzbd = jsonObject.getJSONArray("cyzbd");
            wlgbOrderCyjlService.clearCyJl(tbXyd.getXddbh());
            wlgbEatTcjlService.clearData(tbXyd.getXddbh());
            wlgbEatCpjlService.clearData(tbXyd.getXddbh());
            wlgbEatScjlService.clearData(tbXyd.getXddbh());
            TbXyd finalTbXyd = tbXyd;
            if (tbXyd.getXdcze() > 0) {
                TbVilla finalVilla = villa;
                cyzbd.forEach(l -> {
                    JSONObject item = (JSONObject) l;
                    WlgbOrderCyjl wlgbOrderCyjl = new WlgbOrderCyjl();
                    wlgbOrderCyjl.setDdbh(finalTbXyd.getXddbh());
                    String cymc = item.getString("cymc");
                    wlgbOrderCyjl.setCymc(cymc);
                    wlgbOrderCyjl.setCyjg(item.getDouble("cysj"));
                    wlgbOrderCyjl.setCysl(item.getInteger("cygmsl"));
                    wlgbOrderCyjl.setCyje(item.getDouble("cyje") != null ? item.getDouble("cyje") : finalTbXyd.getXdcze());
                    wlgbOrderCyjl.setCycb(item.getDouble("cycb"));
                    wlgbOrderCyjl.setCyzcb((item.getDouble("cycb") != null ? item.getDouble("cycb") : 0) * (item.getInteger("cygmsl") != null ? item.getInteger("cygmsl") : 0));
                    wlgbOrderCyjl.setCylb(item.getString("cylb"));
                    wlgbOrderCyjl.setCyjq(item.getString("bm"));
                    wlgbOrderCyjl.setCysx(item.getString("selectField_lnldd87u"));
                    wlgbOrderCyjl.setCybs(item.getString("cybs"));
                    JSONArray cjr = item.getJSONArray("employeeField_lmuespiw");
                    if (cjr != null && cjr.size() > 0) {
                        DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(cjr.getString(0));
                        if (dingdingEmployee1 != null) {
                            wlgbOrderCyjl.setCycjr(dingdingEmployee1.getName());
                        }
                    } else {
                        wlgbOrderCyjl.setCycjr(finalTbXyd.getXdccjr());
                    }
                    wlgbOrderCyjlService.save(wlgbOrderCyjl);

                    String cySx = item.getString("selectField_lnldd87u");
                    if ("直营".equals(cySx)) {
                        YdAppkey ydAppkey1 = new YdAppkey();
                        ydAppkey1.setAppkey("APP_JPQRCIS3ASCKF4BJ7UPZ");
                        ydAppkey1.setToken("3J966U61TOUD8M1M8X4WX810EW5G26HBR90MLW2");
                        JSONObject jsonObject1 = new JSONObject();
                        jsonObject1.put("textField_lmtyh13t", cymc);
                        jsonObject1.put("textField_lmu279t4", finalVilla.getVcyqybh());
                        //套餐
                        String zzBdSl = YdConfig.getDatas(ydAppkey1, jsonObject1.toJSONString(), "FORM-IQ8666B1LKDELZNWBUSLP7SQVQYE3I2TGYTML9", "1");
                        JSONObject jsonObject2 = JSONObject.parseObject(zzBdSl);
                        JSONObject result2 = jsonObject2.getJSONObject("result");
                        if (result2.getJSONArray("data") != null && result2.getJSONArray("data").size() > 0) {
                            JSONObject data2 = result2.getJSONArray("data").getJSONObject(0);
                            JSONObject data3 = data2.getJSONObject("formData");
                            String tcbh = data3.getString("textField_lmtyh13s");
                            String tcmc = data3.getString("textField_lmtyh13t");
                            String jqmc = data3.getString("textField_lmu279t5");
                            String jqbh = data3.getString("textField_lmu279t4");
                            String tclx = data3.getString("radioField_lmtyh13x");

                            WlgbEatTcjl wlgbEatTcjl = new WlgbEatTcjl();
                            wlgbEatTcjl.setDdbh(finalTbXyd.getXddbh());
                            wlgbEatTcjl.setTcbh(tcbh);
                            wlgbEatTcjl.setTcmc(tcmc);
                            wlgbEatTcjl.setTclx(tclx);
                            wlgbEatTcjl.setJqmc(jqmc);
                            wlgbEatTcjl.setJqbh(jqbh);
                            wlgbEatTcjl.setSj(data3.getDouble("numberField_lmub9h4u"));
                            wlgbEatTcjl.setCb(data3.getDouble("numberField_lmub9h4v"));
                            wlgbEatTcjl.setMl(data3.getDouble("numberField_lncq88zx"));
                            wlgbEatTcjlService.save(wlgbEatTcjl);

                            JSONArray cpzbd = data3.getJSONArray("tableField_lmtyh13u");
                            for (Object cp : cpzbd) {
                                JSONObject cp1 = (JSONObject) cp;
                                String cpbh = cp1.getString("textField_lnecrr3f");
                                if (cpbh != null && !"".equals(cpbh)) {
                                    //菜品
                                    String cpmc = cp1.getString("textField_lnofmvc3");
                                    JSONObject jsonObject3 = new JSONObject();
                                    jsonObject3.put("textField_lmtx9dew", cpbh);
                                    jsonObject3.put("textField_lmtx9des", cpmc);
                                    String zzBdSl1 = YdConfig.getDatas(ydAppkey1, jsonObject3.toJSONString(), "FORM-AC666081DPDEOL5BAFFF1BOR6GNO2SF59XTML5", "1");
                                    JSONObject jsonObject4 = JSONObject.parseObject(zzBdSl1);
                                    JSONObject result3 = jsonObject4.getJSONObject("result");
                                    JSONArray data4 = result3.getJSONArray("data");
                                    for (Object cp2 : data4) {
                                        JSONObject cp21 = (JSONObject) cp2;
                                        JSONObject data5 = cp21.getJSONObject("formData");
                                        WlgbEatCpjl wlgbEatCpjl = new WlgbEatCpjl();
                                        wlgbEatCpjl.setDdbh(finalTbXyd.getXddbh());
                                        wlgbEatCpjl.setTcbh(tcbh);
                                        wlgbEatCpjl.setTcmc(tcmc);
                                        wlgbEatCpjl.setCpbh(data5.getString("textField_lmtx9dew"));
                                        wlgbEatCpjl.setCpmc(data5.getString("textField_lmtx9des"));
                                        wlgbEatCpjl.setCplx(data5.getString("radioField_lmtyjk43"));
                                        wlgbEatCpjl.setJqmc(data5.getString("textField_lmu279t5"));
                                        wlgbEatCpjl.setJqbh(data5.getString("textField_lmu279t4"));
                                        wlgbEatCpjl.setCbhj(data5.getDouble("numberField_lmtx9df6"));
                                        wlgbEatCpjl.setJysj(data5.getDouble("numberField_lmtx9df7"));
                                        wlgbEatCpjl.setMll(data5.getDouble("numberField_lmtx9df8"));
                                        wlgbEatCpjlService.save(wlgbEatCpjl);
                                        JSONArray data5JSONArray = data5.getJSONArray("tableField_lmtx9dey");
                                        for (Object yl : data5JSONArray) {
                                            JSONObject yl1 = (JSONObject) yl;
                                            String gys = yl1.getString("selectField_lnjq3cqd");
                                            String gysbh = yl1.getString("textField_lnohuoyy");
                                            String scmc = yl1.getString("selectField_lnjq3cqe");
                                            String scbh = yl1.getString("textField_lnjy24ct");
                                            Double ylNum = yl1.getDouble("numberField_lnl3qdkf");
                                            Double zxdj = yl1.getDouble("numberField_lnjy24cs");
                                            String dw = yl1.getString("textField_lnl3qdke");
                                            Double cb = yl1.getDouble("numberField_lnl3qdkd");
                                            Double price = yl1.getDouble("numberField_lnjqa159");

                                            WlgbEatScjl wlgbEatScjl = new WlgbEatScjl();
                                            wlgbEatScjl.setDdbh(finalTbXyd.getXddbh());
                                            wlgbEatScjl.setTcbh(tcbh);
                                            wlgbEatScjl.setTcmc(tcmc);
                                            wlgbEatScjl.setCpbh(cpbh);
                                            wlgbEatScjl.setCpmc(cpmc);
                                            wlgbEatScjl.setGys(gys);
                                            wlgbEatScjl.setGysbh(gysbh);
                                            wlgbEatScjl.setScmc(scmc);
                                            wlgbEatScjl.setScbh(scbh);
                                            wlgbEatScjl.setYlNum(ylNum);
                                            wlgbEatScjl.setMinPrice(zxdj);
                                            wlgbEatScjl.setDw(dw);
                                            wlgbEatScjl.setPrice(price);
                                            wlgbEatScjl.setCb(cb);
                                            wlgbEatScjlService.save(wlgbEatScjl);
                                        }
                                    }
                                }
                            }
                        }
                    }
                });
            }

            //执行占据场次存储过程
            weiLianService.zxZjCc(tbXyd.getXid());

            TbXyd tbXyd1 = tbXydService.queryById(tbXyd.getXid());
            try {
                String kdjZy = weiLianService.queryKdjZy();
                if (kdjZy != null && !"".equals(kdjZy)) {
                    Date smdate = new Date();
                    Date bdate = tbXyd1.getXjctime();
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(smdate);
                    long time1 = cal.getTimeInMillis();
                    cal.setTime(bdate);
                    long time2 = cal.getTimeInMillis();
                    long betweenDays = (time2 - time1) / (1000 * 3600 * 24);
                    if (betweenDays > 30) {
                        String text = "该订单下单超过30天";
                        text += "\n\n房东：" + tbXyd1.getXfd();
                        text += "\n客户：" + tbXyd1.getXzk();
                        text += "\n进场时间：" + DateFormatConfig.df1(tbXyd1.getXjctime());
                        text += "\n订单编号：" + tbXyd1.getXddbh();
                        text += "\n场地费：" + tbXyd1.getXhfyj();
                        text += "\n送达时间：" + DateFormatConfig.df1(new Date());
                        DingDBConfig.sendGztzText(dingkey, kdjZy, text);
                    }
                }
            } catch (Exception e) {
                log.info("*****进入xydSaveTask异步请求*客单价专员失败******{}", e);
            }

            WlgbXydLog wlgbXydLog = new WlgbXydLog();
            wlgbXydLog.setXlid(IdConfig.uuId());
            wlgbXydLog.setXltext("无需带看，进入协议单");
            wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
            wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
            wlgbXydLog.setXlxid(tbXyd.getXid());
            wlgbXydLog.setXltime(new Date());

            wlgbXydLogService.save(wlgbXydLog);
            log.info("*****进入xydSaveTask异步请求*开始请求wlgbXydLog******{}", wlgbXydLog);
            String dingpan = "";
            try {
                if (file != null) {
                    dingpan = DingPan.dingpan(file, dingkey);
                }
            } catch (Exception e) {
                log.info("*****进入xydSaveTask异步请求*文件上传钉盘失败******{}", e);
                try {
                    DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : tbXyd1.getXfd()) + "下单，订单编号”" + tbXyd.getXddbh() + "“协议单上传钉盘报错了,原因：" + e.getMessage());
                } catch (ApiException apiException) {
                    apiException.printStackTrace();
                }
            }
            if (file != null) {
                file.delete();
            }

            Map<String, Object> formDatamap = new HashMap<>();

            Dzxyd dzxyd = new Dzxyd();
            dzxyd.setTip(IdConfig.uuId());
            dzxyd.setXid(tbXyd1.getXid());
            dzxyd.setUrl(upload);
            dzxyd.setTime(new Date());
            dzxydService.save(dzxyd);

            //定金表登记
            if (tbXyd.getXbjdj() > 0) {
                JSONArray xxbjzbd = jsonObject.getJSONArray("xxbjzbd");
                if (xxbjzbd != null && xxbjzbd.size() > 0) {
                    xxbjzbd.forEach(l -> {
                        JSONObject jsonObject1 = (JSONObject) l;
                        Double xxbjje = jsonObject1.getDouble("xxbjje");
                        if (xxbjje != null) {
                            WlgbXxbjdjjl wlgbXxbjdjjl = new WlgbXxbjdjjl();
                            Date bjzzsj = jsonObject1.getDate("bjzzsj");
                            String bjzfbhlw = jsonObject1.getString("bjzfbhlw");
                            wlgbXxbjdjjl.setXid(tbXyd1.getXid());
                            wlgbXxbjdjjl.setCzsj(new Date());
                            wlgbXxbjdjjl.setCzrid(ding != null ? ding.getUserid() : null);
                            wlgbXxbjdjjl.setCzr(ding != null ? ding.getName() : null);
                            wlgbXxbjdjjl.setDjje(xxbjje);
                            wlgbXxbjdjjl.setZzsj(bjzzsj);
                            wlgbXxbjdjjl.setBjzfbhlw(bjzfbhlw);
                            jsonObject1.put("xddbh", tbXyd1.getXddbh());
                            GatewayResult gatewayResult = null;
                            try {
                                gatewayResult = DingBdLcConfig.xzBdSl(finalToken, ydAppkey, jsonObject1.toJSONString(), "012412221639786136545", "FORM-Q4A664A1HFMVOZRF5HDEA53JLVXU3PIKAHDWK2");
                            } catch (Exception e) {
                                log.info("*****进入xydSaveTask异步请求*定金表登记失败2******{}", e);
                                ;
                            }
                            if (gatewayResult != null) {
                                wlgbXxbjdjjl.setSlid(gatewayResult.getResult());
                            }
                            wlgbXxbjdjjlService.save(wlgbXxbjdjjl);
                        }
                    });
                }
                JSONArray xsbjzbd = jsonObject.getJSONArray("xsbjzbd");
                if (xsbjzbd != null && xsbjzbd.size() > 0) {
                    xsbjzbd.forEach(l -> {
                        JSONObject jsonObject1 = (JSONObject) l;
                        Double xsbjje = jsonObject1.getDouble("xsbjje");
                        if (xsbjje != null) {
                            WlgbXsdjbjjl wlgbXsdjbjjl = new WlgbXsdjbjjl();
                            Date yqrq = jsonObject1.getDate("yqrq");
                            String dm = jsonObject1.getString("dm");
                            String qm = jsonObject1.getString("qm");
                            String yqr = jsonObject1.getString("yqr");
                            wlgbXsdjbjjl.setXid(tbXyd1.getXid());
                            wlgbXsdjbjjl.setCzsj(new Date());
                            wlgbXsdjbjjl.setCzrid(ding != null ? ding.getUserid() : null);
                            wlgbXsdjbjjl.setCzr(ding != null ? ding.getName() : null);
                            wlgbXsdjbjjl.setBjje(xsbjje);
                            wlgbXsdjbjjl.setYqrq(yqrq);
                            wlgbXsdjbjjl.setDm(dm);
                            wlgbXsdjbjjl.setQm(qm);
                            wlgbXsdjbjjl.setYqr(yqr);

                            jsonObject1.put("xddbh", tbXyd1.getXddbh());

                            GatewayResult gatewayResult = null;
                            try {
                                gatewayResult = DingBdLcConfig.xzBdSl(finalToken, ydAppkey, jsonObject1.toJSONString(), "012412221639786136545", "FORM-DX966R6183MVK5RP5RV1M734R4782M975IDWKI");
                            } catch (Exception e) {
                                log.info("*****进入xydSaveTask异步请求*定金表登记失败******{}", e);
                                ;
                            }
                            if (gatewayResult != null) {
                                wlgbXsdjbjjl.setSlid(gatewayResult.getResult());
                            }
                            wlgbXsdjbjjlService.save(wlgbXsdjbjjl);
                        }
                    });
                }
            }

            Map<String, Object> map4 = new HashMap<>();
            map4.put("jcsj", tbXyd1.getXjctime());
            map4.put("tcsj", tbXyd1.getXtctime());
            map4.put("vid", tbXyd1.getXbsmc());
            //底价
            Map<String, Object> map1 = weiLianService.queryKdjXx(map4);
            String ddSx = "普通单";
            String corlrSee = "#FFA500";
            if (map1 != null && map1.size() > 0) {
                Double dj = Double.valueOf(map1.get("dycje") != null ? map1.get("dycje").toString() : "0");
                Double jzx = Double.valueOf(map1.get("dycjjje") != null ? map1.get("dycjjje").toString() : "0");
                Double bj = Double.valueOf(map1.get("dycbjje") != null ? map1.get("dycbjje").toString() : "0");
                Double jzx1 = Double.valueOf(map1.get("jzx") != null ? map1.get("jzx").toString() : "0");
                Double bj1 = Double.valueOf(map1.get("bj") != null ? map1.get("bj").toString() : "0");
                if (dj > 0) {
                    if (tbXyd1.getXhfyj() > bj1) {
                        ddSx = "超神单";
                        corlrSee = "#FF69B4";
                    }
                    if (tbXyd1.getXhfyj() > jzx1 && tbXyd1.getXhfyj() <= bj1) {
                        ddSx = "优质单";
                        corlrSee = "#00FF00";
                    }
                    if (tbXyd1.getXhfyj() >= dj && tbXyd1.getXhfyj() <= jzx1) {
                        ddSx = "普通单";
                        corlrSee = "#FFA500";
                    }
                    if (tbXyd1.getXhfyj() < dj) {
                        ddSx = "不及格单";
                        corlrSee = "#FF0000";
                    }
                }
            }

            int hours = tbXyd1.getXjctime().getHours();
            int hours1 = tbXyd1.getXtctime().getHours();
            String cc = "跨";
            if (hours == 10 && hours1 == 17) {
                cc = "白";
            } else if (hours == 18 && hours1 == 8) {
                cc = "晚";
            }
            String bsXz = "加盟";
            if ("直营".equals(villa.getVxz())) {
                bsXz = "加盟合伙";
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            TbKhlyxz tdXz = weiLianService.queryKhLyXzByLidOrLname(tbXyd1.getXtdxz());
            TbKhlyxz khLy = weiLianService.queryKhLyXzByLidOrLname(tbXyd1.getXkhly());

            // 校代播报
            if (tbXyd.getXzdcl() != null) {
                if (tbXyd.getXzdcl() == 4) {
                    double v = (tbXyd1.getXzrcsfy() != null ? tbXyd1.getXzrcsfy() : 0) + (tbXyd1.getXdcze() != null ? tbXyd1.getXdcze() : 0) + (tbXyd1.getXskze() != null ? tbXyd1.getXskze() : 0) + (tbXyd1.getXbxzje() != null ? tbXyd1.getXbxzje() : 0) + (tbXyd1.getXztze() != null ? tbXyd1.getXztze() : 0) + (tbXyd1.getXhpsfy() != null ? tbXyd1.getXhpsfy() : 0) + (tbXyd1.getXjbszje() != null ? tbXyd1.getXjbszje() : 0);
                    Integer xdSd = weiLianService.queryXdSd(tbXyd1.getXxddh(), tbXyd1.getXxdxm()) - 1;
                    try {
                        DingQunSend.sendQun(dingkey, dingpan, "chatffd62c3cf2f2af8f13c21ee1a5093505");
                        String context = "刚刚" + tbXyd1.getXfd() + "下了一个" + villa.getVname() + "的" + bsXz + "校代单";
                        context += "\n\n校代：" + (tbXyd1.getXxdxm() != null ? tbXyd1.getXxdxm() : "");
                        context += "\n\n校代电话：" + (tbXyd1.getXxddh() != null ? tbXyd1.getXxddh() : "");
                        context += "\n\n校代上级：" + (tbXyd1.getXxdsjxm() != null ? tbXyd1.getXxdsjxm() : "");
                        context += "\n\n是否校代首单：<font color=" + (xdSd == 0 ? "#00FF00" : "#FF0000") + " size=4 >" + (xdSd == 0 ? "是" : "否") + "</font>";
                        context += "\n\n综合金额：" + (v + (tbXyd1.getXhfyj() != null ? tbXyd1.getXhfyj() : 0));
//                        context += "\n\n订单属性：<font color=" + corlrSee + " size=4 >" + ddSx + "</font>";
                        context += "\n\n进退场时间：" + DateFormatConfig.df2(tbXyd1.getXjctime()) + "———" + DateFormatConfig.df2(tbXyd1.getXtctime()) + "，" + getWeek(tbXyd1.getXjctime()) + cc + "场单";
                        DingQunSend.sendMarkDown(dingkey, context, "chatffd62c3cf2f2af8f13c21ee1a5093505", "下了一个校代单", upload);
                        if (xdSd == 0) {
                            DkBbJqr dkBbJqr = new DkBbJqr();
                            dkBbJqr.setWebhook("https://oapi.dingtalk.com/robot/send?access_token=c83a81f62fdd32bc4d459e2f01db437e485b12097232c7d08aa9f0313d88bab3");
                            List<String> list = new ArrayList<>();
                            list.add("17873403346");
                            list.add("17727747112");
                            list.add("15396083973");
                            String text = "校代" + (tbXyd1.getXxdxm() != null ? tbXyd1.getXxdxm() : "") + "开了首单[鼓掌][鼓掌][鼓掌]";
                            DingDingUtil.sendMsg(dkBbJqr != null ? dkBbJqr.getWebhook() : null, dkBbJqr != null ? dkBbJqr.getSecret() : null, text, list, false);
                        }
                    } catch (ApiException e) {
                        log.info("*****进入xydSaveTask异步请求*播报校代失败******{}", e);
                        ;
                    }
                }
            }

            //订餐处理(处理出库记录)
            Integer xsfdc = jsonObject.getInteger("xsfdc");
            if (xsfdc == 1) {
                JSONArray jsonArray = jsonObject.getJSONArray("cyzbd");
                String finalToken1 = token;
                try {
                    jsonArray.forEach(item -> {
                        JSONObject l = (JSONObject) item;
                        l.put("cybh", tbXyd1.getXddbh());
                        try {
                            YdConfig.xzBdSl(finalToken1, ydAppkey, l.toJSONString(), fdUserId, "FORM-6Q866L81621TOWRCYSHTECVS90KY10URT5RSKW5");
                        } catch (Exception e) {
                            log.info("*****进入xydSaveTask异步请求*订餐出库失败******{}", e);
                            ;
                        }
                    });
                } catch (Exception e) {
                    log.info("*****进入xydSaveTask异步请求*订餐处理失败******{}", e);
                    ;
                }
            }
            log.info("*****进入xydSaveTask异步请求*开始播报******{}", xsfdc);
            //pngurl必须用阿里云oss地址，不要用服务器图片地址
            formDatamap.put("aimg", YdConfig.setTpList(upload, "协议单图片"));
            formDatamap.put("xid", tbXyd.getXid());
            formDatamap.put("xddbh", tbXyd.getXddbh());
            //修改值班店长
            formDatamap.put("zbdz", employee1 != null ? employee1.getUserid() : "");
            //城市
            formDatamap.put("cs", villa.getCity());
            xgBdSl(formDatamap, formInstId, ding != null ? ding.getUserid() : "012412221639786136545");
            String userList = "";
            if (villa.getPid() != null && !"".equals(villa.getPid()) && !"null".equalsIgnoreCase(villa.getPid())) {
                if (!villa.getPid().equals(fdUserId)) {
                    userList = villa.getPid();
                }
            }
            try {
                lcAndBd(tbXyd1, villa, fdUserId, formInstId, employee1);
            } catch (Exception e) {
                log.info("*****进入xydSaveTask异步请求*新增流程失败******{}", e);
                try {
                    DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : tbXyd1.getXfd()) + "下单，订单编号”" + tbXyd1.getXddbh() + "“流程与表单生成报错了");
                } catch (ApiException apiException) {
                    apiException.printStackTrace();
                }
            }

            //TODO 设置播报休眠延迟6秒
//            try {
//                Thread.sleep(6000);
//            } catch (InterruptedException e) {
//                log.info("*****进入xydSaveTask异步请求*设置休眠失败******{}", e);;
//            }

            //排单群播报
            try {
                FwqBbb fwqBbb = weiLianService.queryBbbByXfd(tbXyd1.getXfd());
                if (fwqBbb != null) {
                    String body;
                    String xnc = "【恭喜" + tbXyd1.getXfd();
                    int fddsum = weiLianService.queryXydMothCountByXfd(tbXyd1.getXfd());
                    xnc += "[耶][鼓掌]本月第" + fddsum + "个单！[鼓掌][耶]】";
                    xnc += "\n\n\uD83D\uDC4D 恭喜" + fwqBbb.getXnc() + "刚刚下了1个" + villa.getVname() + "的" + bsXz + "单";
                    int ddnum = weiLianService.queryXydDateCountByXfd(tbXyd1.getXfd());
                    body = xnc + "，今天第" + ddnum + "个单。\uD83D\uDE0D";

                    if (ddnum == 0) {
                        ddnum = weiLianService.queryXydYesCountByXfd(tbXyd1.getXfd());
                        body = xnc + "，昨天第" + ddnum + "个单。\uD83D\uDE0D";
                    }
//                    body += "\n\n订单属性：<font color=" + corlrSee + " size=4 >" + ddSx + "</font>";
                    body += "\n\n客户性质：" + tdXz.getLname();
                    body += "\n\n客户来源：" + khLy.getLname();
                    body += "\n\n进退场时间：" + DateFormatConfig.df2(tbXyd1.getXjctime()) + "———" + DateFormatConfig.df2(tbXyd1.getXtctime()) + "，" + getWeek(tbXyd1.getXjctime()) + cc + "场单";
                    Double xdYj = weiLianService.queryXdYjByXfd(tbXyd1.getXfd());
                    body += "\n\n" + tbXyd1.getXfd() + "本月下单业绩[比心]" + xdYj + "[比心]";
                    //线上
                    FwqQunid fwqQunid = weiLianService.queryQun(villa.getBbcity());
                    if (fwqQunid != null) {
                        if (fwqQunid.getQcid() != null && !"".equals(fwqQunid.getQcid())) {
                            DingQunSend.sendMarkDown(dingkey, body, fwqQunid.getQcid(), "下单啦", upload);
                            DingQunSend.sendQun(dingkey, dingpan, fwqQunid.getQcid());
                        }
                    }
                    //线下
//                    DingQunSend.sendMarkDown(dingkey, body, "chate5bd183f6a0ebdd18e057550c487e5cc", "下单啦", upload);
//                    DingQunSend.sendQun(dingkey, dingpan, body, "chate5bd183f6a0ebdd18e057550c487e5cc");
                }
            } catch (Exception e) {
                log.info("*****进入xydSaveTask异步请求*播报排单群失败******{}", e);
                ;
                try {
                    DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : tbXyd1.getXfd()) + "下单，订单编号”" + tbXyd1.getXddbh() + "“排单群播报报错了，错误原因：" + e.getMessage());
                } catch (ApiException apiException) {
                    apiException.printStackTrace();
                }
            }
            //冲刺群播报
            try {
                if (fwqBbb2 != null) {
                    //执行存储过程
                    weiLianService.zxczgc();
                    FwqBbb3 fwqBbb3 = weiLianService.queryBmYj(fwqBbb2);
                    if (fwqBbb3 != null) {
                        double v1 = (tbXyd1.getXzrcsfy() != null ? tbXyd1.getXzrcsfy() : 0) + (tbXyd1.getXdcze() != null ? tbXyd1.getXdcze() : 0) + (tbXyd1.getXskze() != null ? tbXyd1.getXskze() : 0) + (tbXyd1.getXbxzje() != null ? tbXyd1.getXbxzje() : 0) + (tbXyd1.getXztze() != null ? tbXyd1.getXztze() : 0) + (tbXyd1.getXhpsfy() != null ? tbXyd1.getXhpsfy() : 0) + (tbXyd1.getXhfyj() != null ? tbXyd1.getXhfyj() : 0);
                        String context1 = "钱来了" + (tbXyd1.getXzdcl() == 4 ? "(校代单：" + tbXyd1.getXxdxm() + ")" : "") + "[色][色][色][色]";
                        context1 += "\n\n" + tbXyd1.getXfd() + "刚刚下了1个" + villa.getVname() + "的" + bsXz + "单，金额" + v1
                                + "\n\n(场地费：" + tbXyd1.getXhfyj() + ")"
                                + "\n\n(中餐费：" + (tbXyd1.getXdcze() != null ? tbXyd1.getXdcze() : 0) + ")"
                                + "\n\n(烧烤费：" + (tbXyd1.getXskze() != null ? tbXyd1.getXskze() : 0) + ")"
                                + "\n\n(策划费：" + (tbXyd1.getXztze() != null ? tbXyd1.getXztze() : 0) + ")"
                                + "\n\n(保险费：" + (tbXyd1.getXbxzje() != null ? tbXyd1.getXbxzje() : 0) + ")"
                                + "\n\n(酒水零食：" + (tbXyd1.getXsplsze() != null ? tbXyd1.getXsplsze() : 0) + ")"
                        ;
//                        context1 += "\n\n订单属性：<font color=" + corlrSee + " size=4 >" + ddSx + "</font>";
                        context1 += "\n\n客户性质：" + tdXz.getLname();
                        context1 += "\n\n客户来源：" + khLy.getLname();
                        context1 += "\n\n进退场时间：" + sdf.format(tbXyd1.getXjctime()) + "———" + sdf.format(tbXyd1.getXtctime()) + "，" + getWeek(tbXyd1.getXjctime()) + cc + "场单";
                        context1 += "\n\n" + fwqBbb3.getCdfyj() + " 下单总计" + (fwqBbb3.getDdsl() != null ? fwqBbb3.getDdsl() : 0) + "个     总业绩：" + (fwqBbb3.getZyj() != null ? fwqBbb3.getZyj() : 0);
                        //线上
                        DingQunSend.sendMarkDown(dingkey, context1, fwqBbb2.getChatid(), "下单啦", upload);
                    }
                }
            } catch (Exception e) {
                try {
                    DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : tbXyd1.getXfd()) + "下单，订单编号”" + tbXyd1.getXddbh() + "“冲刺群播报报错了，错误原因：" + e.getMessage());
                } catch (ApiException apiException) {
                    apiException.printStackTrace();
                }
            }

            //分公司小红书和分公司抖音播报
            if ("80ca5a42c2774390bcc314e02c6a034d".equals(tbXyd1.getXkhly()) || "a33c818bda9d4793b07fbc92e7a55f2e".equals(tbXyd1.getXkhly()) || "30197965afd34fb687ab7fe0ed70fa7c".equals(tbXyd1.getXkhly())) {
                try {
                    double v1 = (tbXyd1.getXzrcsfy() != null ? tbXyd1.getXzrcsfy() : 0) + (tbXyd1.getXdcze() != null ? tbXyd1.getXdcze() : 0) + (tbXyd1.getXskze() != null ? tbXyd1.getXskze() : 0) + (tbXyd1.getXbxzje() != null ? tbXyd1.getXbxzje() : 0) + (tbXyd1.getXztze() != null ? tbXyd1.getXztze() : 0) + (tbXyd1.getXhpsfy() != null ? tbXyd1.getXhpsfy() : 0) + (tbXyd1.getXhfyj() != null ? tbXyd1.getXhfyj() : 0);
                    String context1 = "\n\n" + tbXyd1.getXfd() + "刚刚下了1个" + villa.getVname() + "的" + bsXz + "单，金额" + v1 + "(场地费：" + tbXyd1.getXhfyj() + ")";
//                    context1 += "\n\n订单属性：<font color=" + corlrSee + " size=4 >" + ddSx + "</font>";
                    context1 += "\n\n客户性质：" + tdXz.getLname();
                    context1 += "\n\n客户来源：" + khLy.getLname();
                    context1 += "\n\n进退场时间：" + sdf.format(tbXyd1.getXjctime()) + "———" + sdf.format(tbXyd1.getXtctime()) + "，" + getWeek(tbXyd1.getXjctime()) + cc + "场单";
                    DingQunSend.sendMarkDown(dingkey, context1, "chat1310f05951c43d3f8bc73c1bf42c74cd", "订单通知", upload);
                    DingQunSend.sendQun(dingkey, dingpan, "chat1310f05951c43d3f8bc73c1bf42c74cd");
                } catch (Exception e) {
                    log.info("*****进入xydSaveTask异步请求*播报小红书失败******{}", e);
                    ;
                }
            }
            //策划播报
            if (tbXyd1.getXztze() != null) {
                if (tbXyd1.getXztze() > 0) {
                    try {
                        ch(tbXyd1, "有新的策划订单");
                    } catch (Exception e) {
                        log.info("*****进入xydSaveTask异步请求*播报策划失败******{}", e);
                    }
                }
            }
            //轰趴师播报
            if (tbXyd1.getXhpsfy() != null) {
                if (tbXyd1.getXhpsfy() > 0) {
                    try {
                        hps(tbXyd1, tbXyd1.getXfd() + "有新的轰趴师订单，请注意跟进", dingkey, dingpan);
                    } catch (Exception e) {
                        log.info("*****进入xydSaveTask异步请求*播报轰趴师失败******{}", e);
                    }
                }
            }
            // 剧本杀播报
            if (tbXyd1.getXjbszje() != null) {
                if (tbXyd1.getXjbszje() > 0) {
                    try {
                        jbsBb(tbXyd1.getXfd() + "有新的剧本杀订单，请注意跟进", dingkey, dingpan);
                    } catch (Exception e) {
                        log.info("*****进入xydSaveTask异步请求*剧本山播报失败******{}", e);
                    }
                }
            }
            // 朋友圈播报
            if (tbXyd1.getXzfts() != null) {
                if (tbXyd1.getXzfts() > 0) {
                    try {
                        Integer zfTs = weiLianService.queryXfdXdZfTs(tbXyd1.getXfd());
                        Integer sjZfTs = weiLianService.queryQrdSjZfTs(tbXyd1.getXfd());
                        String context = tbXyd1.getXfd() + "下了一个" + (villa.getCity() + "的") + "单，要求转发" + (tbXyd1.getXzfts() != null ? tbXyd1.getXzfts() : 0) + "条朋友圈，本月累计要求转发" + (zfTs != null ? zfTs : 0) + "条，实际已经转发" + (sjZfTs != null ? sjZfTs : 0) + "条";
                        pyqZf(context);
                    } catch (Exception e) {
                        log.info("*****进入xydSaveTask异步请求*发送朋友圈播报失败******{}", e);
                    }
                }
            }
            if (userList != null && !"".equals(userList) && userList.length() > 0) {
                String contexts = "您来了新的订单，请查收！";
                contexts += "\n\n别墅：" + villa.getVname();
                contexts += "\n\n房东：" + tbXyd1.getXfd() + "";
                if (employee1 != null) {
                    contexts += "\n\n店长：" + employee1.getName();
                }
                contexts += "\n\n送达时间：";
                try {
                    DingDBConfig.sendGztz1(userList, dingkey, "电子协议单",
                            contexts, upload);
                } catch (ApiException e) {
                    log.info("*****进入xydSaveTask异步请求*发送工作通知失败******{}", e);
                }
            }
            //加盟商播报
            WlgbJmsbb wlgbJmsbb = weiLianDdXcxService.queryJmsBbByVid(villa.getVid());
            if (wlgbJmsbb != null) {
                String contexts = "您来了新的订单，请查收！";
                contexts += "\n\n别墅：" + villa.getVname();
                contexts += "\n\n房东：" + tbXyd1.getXfd() + "";
                if (employee1 != null) {
                    contexts += "\n\n店长：" + employee1.getName();
                }
                contexts += "\n\n送达时间：";
                try {
                    DingDBConfig.sendGztz1(wlgbJmsbb.getBbrid(), dingkey,
                            "电子协议单", contexts, upload);
                } catch (ApiException e) {
                    log.info("*****进入xydSaveTask异步请求*加盟商播报失败******{}", e);
                }
            }
            //是否是加盟别墅
            Integer sfZn = weiLianService.queryBsSfZn(tbXyd1.getXbsmc());
            //是否是外部投资人
            Integer fdZn = weiLianService.queryFdZn(tbXyd1.getXfd());
            if (sfZn > 0 || fdZn > 0) {
                //线上
                try {
                    DingQunSend.sendQun(dingkey, dingpan, "chat7e557a4048205f21df21cce8f8c4df59");
                } catch (ApiException e) {
                    log.info("*****进入xydSaveTask异步请求*发送群图片失败******{}", e);
                }
            }
            List<WlgbJdDjbbd> list = wlgbJdDjbbdService.queryByLshAndSfSc(tbXyd1.getXddbh());
            list.forEach(l -> {
                Map<String, Object> jsonObject1 = new HashMap<>();
                jsonObject1.put("sfxd", 1);
                jsonObject1.put("ddbh", tbXyd1.getXddbh());
                jsonObject1.put("xdsj", tbXyd1.getXkhtjtime());
                xgBdSl(jsonObject1, l.getFormInstId(), ding.getUserid());
            });
        }
        log.info("*****进入xydSaveTask异步请求*开始播报时间******{}", new Date());
        if (xyd != null) {
            //房东截取
            if (tbXyd.getXfd() != null && !"".equals(tbXyd.getXfd()) && tbXyd.getXfd().length() > 0) {
                tbXyd.setXfd(xzjq(tbXyd.getXfd()));
            }
            //fdUserId 是房东ID ,不是房东姓名
            String fdUserId = tbXyd.getXfd();
            //执行流程
            try {
                lcAndBd(xyd, villa, fdUserId, formInstId, employee1);
            } catch (Exception e) {
                log.info("*****进入xydSaveTask异步请求*执行流程失败******{}", e);
            }
        }
        log.info("*****进入xydSaveTask异步请求 结束*******{}", datas);
    }

    /**
     * 删除表单
     *
     * @param slid 实例id
     */
    public GatewayResult scBdSl(String userId, String slid) {
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            log.info("*****进入xydSaveTask异步请求*删除表单实例之获取token失败******{}", e);
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.scBdSl(token, ydAppkey, userId, slid);
        } catch (Exception e) {
            gatewayResult = new GatewayResult();
            log.info("*****进入xydSaveTask异步请求*删除表单实例失败******{}", e);
        }

        return gatewayResult;
    }

    /**
     * 选人截取
     *
     * @param userid 用户id
     * @return 用户id
     */
    private String xzjq(String userid) {
        String s = userid.substring(0, 1);
        if ("[".equals(s)) {
            userid = userid.substring(2, userid.length() - 2);
        } else {
            userid = userid;
        }
        return userid;
    }

    /**
     * 对协议单部分数据赋值
     *
     * @param tbXyd ---协议单
     */
    private TbXyd fz(TbXyd tbXyd) {
        //进场场次
        Calendar c = Calendar.getInstance();
        c.setTime(tbXyd.getXjctime());
        int hours = c.get(Calendar.HOUR_OF_DAY);
        if (9 <= hours && hours <= 17) {
            tbXyd.setXjcflag(0);
        } else {
            tbXyd.setXjcflag(1);
        }

        //退场场次
        Calendar c2 = Calendar.getInstance();
        c2.setTime(tbXyd.getXtctime());
        int hour = c2.get(Calendar.HOUR_OF_DAY);
        if (9 <= hour && hour <= 17) {
            tbXyd.setXtcflag(0);
        } else {
            tbXyd.setXtcflag(1);
        }

        VillaIDsUtil iDsUtil = weiLianService.queryIdsByVid(tbXyd.getXbsmc());
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        //别墅分区
        if (iDsUtil != null) {
            tbXyd.setCshenfen(iDsUtil.getSid());
            tbXyd.setCfenqu(iDsUtil.getFid());
            tbXyd.setCompany(iDsUtil.getCid());
        }

        //协议单的值班店长
        if (villa != null) {
            if (villa.getPid() != null && !"".equals(villa.getPid()) && !"null".equalsIgnoreCase(villa.getPid())) {
                DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
                if (dingdingEmployee != null) {
                    tbXyd.setXdzid(villa.getPid());
                }
            }
        }

        //房东
        if (tbXyd.getXfd() != null && !"".equals(tbXyd.getXfd())) {
            DingdingEmployee fd = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXfd());
            if (fd != null) {
                tbXyd.setXfd(fd.getName());
                tbXyd.setXfdgh(fd.getJobnumber());
            }
        }

        //老板
        if (tbXyd.getXlbjf() != null && !"".equals(tbXyd.getXlbjf())) {
            DingdingEmployee lb = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXlbjf());
            if (lb != null) {
                tbXyd.setXlbjf(lb.getName());
            }
        }
        //校代上级
        if (tbXyd.getXxdsjxm() != null && !"".equals(tbXyd.getXxdsjxm())) {
            DingdingEmployee xdsj = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXxdsjxm());
            if (xdsj != null) {
                tbXyd.setXxdsjxm(xdsj.getName());
                tbXyd.setXxdsjxmgh(xdsj.getJobnumber());
            }
        }

        //保险成交人
        if (tbXyd.getXbxcjr() != null && !"".equals(tbXyd.getXbxcjr())) {
            DingdingEmployee xbxcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXbxcjr());
            if (xbxcjr != null) {
                tbXyd.setXbxcjr(xbxcjr.getName());
                tbXyd.setXbxcjrgh(xbxcjr.getJobnumber());
            }
        }
        //轰趴师成交人
        if (tbXyd.getXhpscjr() != null && !"".equals(tbXyd.getXhpscjr())) {
            DingdingEmployee hpscjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXhpscjr());
            if (hpscjr != null) {
                tbXyd.setXhpscjr(hpscjr.getName());
                tbXyd.setXhpscjrgh(hpscjr.getJobnumber());
            }
        }
        //订餐成交人
        if (tbXyd.getXdccjr() != null && !"".equals(tbXyd.getXdccjr())) {
            DingdingEmployee dccjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXdccjr());
            if (dccjr != null) {
                tbXyd.setXdccjr(dccjr.getName());
                tbXyd.setXdccjrgh(dccjr.getJobnumber());
            }
        }
        //烧烤成交人
        if (tbXyd.getXskcjr() != null && !"".equals(tbXyd.getXskcjr())) {
            DingdingEmployee skcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXskcjr());
            if (skcjr != null) {
                tbXyd.setXskcjr(skcjr.getName());
                tbXyd.setXskcjrgh(skcjr.getJobnumber());
            }
        }
        //现场成交人
        if (tbXyd.getXcjr() != null && !"".equals(tbXyd.getXcjr())) {
            DingdingEmployee xccjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXcjr());
            if (xccjr != null) {
                tbXyd.setXcjr(xccjr.getName());
                tbXyd.setXcjrgh(xccjr.getJobnumber());
            }
        }
        //后续成交人
        if (tbXyd.getXhxcjr() != null && !"".equals(tbXyd.getXhxcjr())) {
            DingdingEmployee hxcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXhxcjr());
            if (hxcjr != null) {
                tbXyd.setXhxcjr(hxcjr.getName());
                tbXyd.setXhxcjrgh(hxcjr.getJobnumber());
            }
        }
        //策划经理
        if (tbXyd.getXchjlid() != null && !"".equals(tbXyd.getXchjlid())) {
            DingdingEmployee chjl = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXchjlid());
            if (chjl != null) {
                tbXyd.setXchjl(chjl.getName());
            }
        }
        //策划成交人
        if (tbXyd.getXchcjr() != null && !"".equals(tbXyd.getXchcjr())) {
            DingdingEmployee chcj = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXchcjr());
            if (chcj != null) {
                tbXyd.setXchcjr(chcj.getName());
                tbXyd.setXchcjrgh(chcj.getJobnumber());
            }
        }
        //策划辅助成交人
        if (tbXyd.getXchfzcjr() != null && !"".equals(tbXyd.getXchfzcjr()) && tbXyd.getXchfzcjr().length() > 0) {
            DingdingEmployee chhzcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXchfzcjr());
            if (chhzcjr != null) {
                tbXyd.setXchfzcjr(chhzcjr.getName());
            }
        }
        //订餐辅助成交人
        if (tbXyd.getXdcfzcjr() != null && !"".equals(tbXyd.getXdcfzcjr()) && tbXyd.getXdcfzcjr().length() > 0) {
            DingdingEmployee dchzcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXdcfzcjr());
            if (dchzcjr != null) {
                tbXyd.setXdcfzcjr(dchzcjr.getName());
            }
        }
        //烧烤辅助成交人
        if (tbXyd.getXskfzcjr() != null && !"".equals(tbXyd.getXskfzcjr()) && tbXyd.getXskfzcjr().length() > 0) {
            DingdingEmployee skhzcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXskfzcjr());
            if (skhzcjr != null) {
                tbXyd.setXskfzcjr(skhzcjr.getName());
            }
        }
        //真人cs成交人
        if (tbXyd.getXzrcscjr() != null && !"".equals(tbXyd.getXzrcscjr()) && tbXyd.getXzrcscjr().length() > 0) {
            DingdingEmployee zrcscjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXzrcscjr());
            if (zrcscjr != null) {
                tbXyd.setXzrcscjr(zrcscjr.getName());
            }
        }
        //团队性质
        if (tbXyd.getXtdxz() != null && !"".equals(tbXyd.getXtdxz()) && tbXyd.getXtdxz().length() > 0) {
            TbKhlyxz khlyxz = weiLianService.queryKhLyXzByLidOrLname(tbXyd.getXtdxz());
            if (khlyxz != null) {
                tbXyd.setXtdxz(khlyxz.getLid());
            }
        }
        //客户来源
        if (tbXyd.getXkhly() != null && !"".equals(tbXyd.getXkhly()) && tbXyd.getXkhly().length() > 0) {
            TbKhlyxz khlyxz = weiLianService.queryKhLyXzByLidOrLname(tbXyd.getXkhly());
            if (khlyxz != null) {
                tbXyd.setXkhly(khlyxz.getLid());
            }
        }

        //TODO 剧本杀（不上线需要注释）
        //剧本杀成交人
        if (tbXyd.getXjbscjr() != null && !"".equals(tbXyd.getXjbscjr()) && tbXyd.getXjbscjr().length() > 0) {
            DingdingEmployee jbscjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXjbscjr());
            if (jbscjr != null) {
                tbXyd.setXjbscjr(jbscjr.getName());
                tbXyd.setXjbscjrid(jbscjr.getUserid());
            }
        }
        //剧本杀辅助成交人
        if (tbXyd.getXjbsfzcjr() != null && !"".equals(tbXyd.getXjbsfzcjr()) && tbXyd.getXjbsfzcjr().length() > 0) {
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXjbsfzcjr());
            if (employee != null) {
                tbXyd.setXjbsfzcjr(employee.getName());
                tbXyd.setXjbsfzcjrid(employee.getUserid());
            }
        }
        //轰趴师辅助成交人
        if (tbXyd.getXhpsfzcjr() != null && !"".equals(tbXyd.getXhpsfzcjr()) && tbXyd.getXhpsfzcjr().length() > 0) {
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXhpsfzcjr());
            if (employee != null) {
                tbXyd.setXhpsfzcjr(employee.getName());
                tbXyd.setXhpsfzcjrid(employee.getUserid());
            }
        }

        return tbXyd;
    }

    /**
     * 生成电子协议单
     *
     * @param xyd 协议单数据
     */
    private Map<String, Object> dzXyd(TbXyd xyd, JSONObject jsonObject) throws Exception {
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(xyd.getXbsmc());
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy年MM月dd日");
        String zpTime = "进场：" + DateFormatConfig.df1(xyd.getXjctime()) + "<br />退场：" + DateFormatConfig.df1(xyd.getXtctime());
        //除场地费之外的其他费用
        double v = (xyd.getXzrcsfy() != null ? xyd.getXzrcsfy() : 0) + (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXskze() != null ? xyd.getXskze() : 0) + (xyd.getXbxzje() != null ? xyd.getXbxzje() : 0) + (xyd.getXztze() != null ? xyd.getXztze() : 0) + (xyd.getXhpsfy() != null ? xyd.getXhpsfy() : 0) + (xyd.getXjbszje() != null ? xyd.getXjbszje() : 0);
        Map<String, Object> map = new HashMap<>();
        //苏州协议单设置
        map.put("cityType", "苏州".equals(villa.getCity()) ? "1" : "0");
        //订单编号
        map.put("ddbh", xyd.getXddbh());
        //别墅名字
        map.put("bsName", villa.getVname());
        //老板姓名
        map.put("lbxm", xyd.getXlbjf());
        //房东姓名
        map.put("fdName", xyd.getXfd());
        //房东电话
        map.put("fdTelphone", xyd.getXfddh());
        //租客姓名
        map.put("zkName", xyd.getXzk());
        //租客电话
        map.put("zkTelphone", xyd.getXzkdh());
        //租客身份证号码
        map.put("zksfz", xyd.getXzksfz());
        TbKhlyxz khlyxz = weiLianService.queryKhLyXzByLidOrLname(xyd.getXtdxz());
        //公司名称（性质）
        map.put("dwmc", xyd.getXdwmc() + (khlyxz != null ? "(" + khlyxz.getLname() + ")" : ""));
        TbKhlyxz tbKhlyxz = weiLianService.queryKhLyXzByLidOrLname(xyd.getXkhly());
        //客户来源
        map.put("khly", (tbKhlyxz != null ? tbKhlyxz.getLname() : ""));
        //租赁时间（进场时间与退场时间）
        map.put("zpTime", zpTime);
        //进场时间
        map.put("jxtime", "进场:" + DateFormatConfig.df1(xyd.getXjctime()));
        //退场时间
        map.put("tctime", "退场:" + DateFormatConfig.df1(xyd.getXtctime()));
        //中餐单品总额
        Double xzcdpze = jsonObject.getObject("xzcdpze", Double.class);
        if (xzcdpze == null) {
            xzcdpze = 0.0;
        }
        map.put("xzcdpze", xzcdpze);
        //烧烤火锅总额
        Double xskhgze = jsonObject.getObject("xskhgze", Double.class);
        if (xskhgze == null) {
            xskhgze = 0.0;
        }
        map.put("xskhgze", xskhgze);
        //烤全羊总额
        Double xkqyze = jsonObject.getObject("xkqyze", Double.class);
        if (xkqyze == null) {
            xkqyze = 0.0;
        }
        map.put("xkqyze", xkqyze);
        //龙虾宴总额
        Double xlxyze = jsonObject.getObject("xlxyze", Double.class);
        if (xlxyze == null) {
            xlxyze = 0.0;
        }
        map.put("xlxyze", xlxyze);
        //自助餐/甜品总额
        Double xzzcze = jsonObject.getObject("xzzcze", Double.class);
        if (xzzcze == null) {
            xzzcze = 0.0;
        }
        map.put("xzzcze", xzzcze);
        //单点
        Double xddcyze = jsonObject.getObject("xddcyze", Double.class);
        if (xddcyze == null) {
            xddcyze = 0.0;
        }
        map.put("xddcyze", xddcyze);
        //商品零食总额
        Double xsplsze = (xyd.getXsplsze() != null ? xyd.getXsplsze() : 0);
        if (xsplsze == null) {
            xsplsze = 0.0;
        }
        map.put("xsplsze", xsplsze);
        //人数
        map.put("rs", xyd.getXrs());
        //别墅所在城市
        map.put("city", villa.getCity());
        //超出人数加收
        map.put("ccMoney", xyd.getXcudrfy());
        //支付宝订单号后六位
        String hlw = "";
        if (xyd.getXzzhlw() != null && !"".equals(xyd.getXzzhlw()) && xyd.getXzzhlw().length() > 0) {
            if (xyd.getXzzhlw().length() > 6) {
                hlw = xyd.getXzzhlw().substring(xyd.getXzzhlw().length() - 6);
            } else {
                hlw = xyd.getXzzhlw();
            }
        }
        //支付宝订单号后两位
        map.put("zfbddh", hlw);
        //支付宝转账定金时间
        map.put("zfbzzTime", xyd.getXzzsj() != null ? DateFormatConfig.df1(xyd.getXzzsj()) : "");
        //场地租赁费
        map.put("qkje", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0));
        //增值定金
        map.put("xyszzdj", (!xyd.getXyszzdj().isEmpty()) ? Double.parseDouble(xyd.getXyszzdj()) : 0);
        //增值费用补交
        map.put("xzzfybj", xyd.getXzzfybj() != null ? xyd.getXzzfybj() : 0);
        //剧本杀
        map.put("xjbs", xyd.getXjbszje() != null ? xyd.getXjbszje() : 0);
        //已收场地费定金=已收定金+场地费租金补交-增值定金
        double ysdj = (xyd.getXysdj() != null ? xyd.getXysdj() : 0) + (xyd.getXcdzjbj() != null ? xyd.getXcdzjbj() : 0) - (xyd.getXyszzdj() != null && !"".equals(xyd.getXyszzdj()) ? Double.parseDouble(xyd.getXyszzdj()) : 0);
        if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(xyd.getXdjtype())) {
            Double xwsywdddsyje = xyd.getXwsywdddsyje();
            if (xwsywdddsyje != null) {
                ysdj += xwsywdddsyje;
            }
        }
        map.put("xyscdfdj", ysdj);
        //代码
        String dm = "";
        if (xyd.getXdm() != null && !"".equals(xyd.getXdm()) && xyd.getXdm().length() > 0) {
            if (xyd.getXdm().length() > 6) {
                dm = xyd.getXdm().substring(0, 6);
            } else {
                dm = xyd.getXdm();
            }
        }
        map.put("dm", dm);
        //付款码
        String fkm = "";
        if (xyd.getXfkm() != null && !"".equals(xyd.getXfkm()) && xyd.getXfkm().length() > 0) {
            if (xyd.getXfkm().length() > 2) {
                fkm = xyd.getXfkm().substring(0, 2);
            } else {
                fkm = xyd.getXfkm();
            }
        }
        map.put("zfbfkm", fkm);
        //订餐+烧烤
        map.put("dctczjsktczj", (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXskze() != null ? xyd.getXskze() : 0));
        map.put("xdcze", (xyd.getXdcze() != null ? xyd.getXdcze() : 0));
        map.put("xskze", (xyd.getXskze() != null ? xyd.getXskze() : 0));
        //保险
        map.put("xbxzje", xyd.getXbxzje() != null ? xyd.getXbxzje() : 0);
        //策划
        map.put("zttczj", xyd.getXztze() != null ? xyd.getXztze() : 0);
        //轰趴师
        map.put("sfhps", xyd.getXhpsfy() != null ? xyd.getXhpsfy() : 0);
        //全款金额
        map.put("qkje3", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) + v);
        //优惠至全款
        map.put("qkje4", (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) + v);
        //完成条件后优惠至
        map.put("qkje2", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) - (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) > 0 ? (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) - (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) : 0);
        //转发
        map.put("zgdst", xyd.getXzfts() != null ? xyd.getXzfts() : 0);
        //好评
        map.put("hpdst", xyd.getXhpts() != null ? xyd.getXhpts() : 0);
        //集赞
        map.put("jzst", xyd.getXjzts() != null ? xyd.getXjzts() : 0);
        //消费券
        map.put("xspyhq", xyd.getXspyhq() != null ? xyd.getXspyhq() : 0);
        //值班店长
        map.put("zbdzxm", dingdingEmployee != null ? dingdingEmployee.getName() : "");
        //值班店长电话
        map.put("zbdhxm", dingdingEmployee != null ? dingdingEmployee.getMobile() : "");
        //是否现场成交
        map.put("sfxccj", xyd.getXisxzcj() != null && xyd.getXisxzcj() == 0 ? "是" : "否");
        //是否泳池单
        map.put("sfycd", "否");
        //校代姓名
        map.put("xxdxm", xyd.getXxdxm());
        //主播姓名
        map.put("xzbxm", xyd.getXzbxm());
        //赠送特权会员卡
        map.put("zstkhyk", xyd.getXzshyksl() != null ? xyd.getXzshyksl() : 0);
        //备注
        map.put("bzxx", xyd.getXsxbz());
        //甲方签名
        map.put("jfxm", xyd.getXisxzcj() != null && xyd.getXisxzcj() == 0 ? xyd.getXcjr() : xyd.getXfd());
        //乙方签名
        map.put("yfxm", xyd.getXzk());
        //日期
        map.put("rq", xyd.getXgsj() != null ? formatter1.format(xyd.getXgsj()) : formatter1.format(xyd.getXkhtjtime()));
        //别墅所在城市
        map.put("city", villa.getCity());
        //摄影图片数量
        map.put("xsynum", xyd.getXsynum());
        //定制横幅内容
        map.put("xhfnr", (xyd.getXhfnr() == null ? "无" : xyd.getXhfnr()));
        //抖音数量
        map.put("xdynum", (xyd.getXdynum() == null ? "无" : xyd.getXdynum()));
        //关注数量
        map.put("xgznum", (xyd.getXgznum() == null ? "无" : xyd.getXgznum()));
        //收藏数量
        map.put("xscnum", (xyd.getXscnum() == null ? "无" : xyd.getXscnum()));

        //定制大屏内容
        map.put("xdpnr", (xyd.getXdpnr() == null ? "无" : xyd.getXdpnr()));
        //是否购买三件套：0否，1是
        map.put("xsfgmsjt", (xyd.getXsfgmsjt() == null ? "无" : xyd.getXsfgmsjt()));
        //模板
        String tempName = FileConfig.getFileAbsolutePath2("static" + File.separator + "template2.html");
        String context = PDFUtil.freeMarkerRender(map, tempName);
        String id = "DZHC" + DateFormatConfig.df2(new Date()) + IdConfig.uuId();
        String pdf = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
        String png = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".png");
        File newPdf = new File(pdf);
        if (!newPdf.exists()) {
            log.error("文件不存在pdf: {}", pdf);
            log.error("文件不存在png: {}", png);
            log.error("文件不存在context: {}", context);
        }
        Map<String, Object> map1 = new HashMap<>();
        try {
            log.info("*****进入xydSaveTask异步请求*准备生成图片了333******{}", map);
            // 生成pdf
            PDFUtil.createPdf(context, newPdf.getPath(), true);
            // 生成图片
            PDFUtil.pdfToImg(newPdf.getPath(), 1, png);
            log.info("*****进入xydSaveTask异步请求*生成图片成功****newPdf.getPath**{}", newPdf.getPath());
            map1.put("png", id + ".png");
            // 删除pdf临时文件
            if (!newPdf.delete()) {
                log.warn("删除pdf临时文件报错: {}", newPdf.getPath());
            }
        } catch (Exception e) {
            log.error("生成PDF/png文件失败: ", e);
        }
        return map1;
    }

    /**
     * 获取星期几
     */
    public static String getWeek(Date date) {
        String[] weeks = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int week_index = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (week_index < 0) {
            week_index = 0;
        }

        return weeks[week_index];

    }

    /**
     * 修改表单实例
     *
     * @param id     要更新的表单数据ID
     * @param map    表单内容
     * @param userid 用户id
     */
    public GatewayResult xgBdSl(Map<String, Object> map, String id, String userid) {
        JSONObject json = new JSONObject(map);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            log.info("*****进入xydSaveTask异步请求*修改表单实例之获取token 失败******{}", e);
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, id, json.toJSONString());
        } catch (Exception e) {
            log.info("*****进入xydSaveTask异步请求*修改表单实例失败******{}", e);
        }
        return gatewayResult;
    }

    /**
     * 流程与表单新增记录
     *
     * @param tbXyd1     协议单数据
     * @param villa      别墅对象
     * @param fdUserId   房东id
     * @param formInstId 下单实例id
     */
    public void lcAndBd(TbXyd tbXyd1, TbVilla villa, String fdUserId, String formInstId, DingdingEmployee employee1) throws Exception {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        //隔离加盟别墅 如果不在加盟别墅表内就发起流程
        Integer count = weiLianDdXcxService.queryBsCount(tbXyd1.getXbsmc());
        String xydUrl = dzXydCl(tbXyd1);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        //时间差
        long l = tbXyd1.getXjctime().getTime() - new Date().getTime();
        //进场时间减去下单时间的分钟数
        long l1 = l / 1000 / 60;

        //如果时间差小于两个小时立即发起对账表单
        if (l1 <= (60 * 2) || tbXyd1.getXjctime().getTime() < new Date().getTime()) {
            try {
                //上传对账表单
                scDzBd(tbXyd1, employee1, villa, fdUserId, xydUrl, ydAppkey);
            } catch (Exception e) {
                log.info("*****进入xydSaveTask异步请求*上传对账表单失败******{}", e);
            }
        }

        String vxz = villa.getVxz();
        if (count == 0) {
            //直营的
            if ("直营".equals(vxz)) {
                Csry csry = weiLianDaiBanService.queryCsRyByUserId(villa.getPid());
                if (csry != null) {
                    //房东
                    DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(fdUserId);
                    //如果时间差的分钟数小于等于12个小时的分钟发送进场及消费，否则发送下单及跟单
                    if (l1 <= (60 * 3) || tbXyd1.getXjctime().getTime() < new Date().getTime()) {
                        //两个小时内才发流程
                        if (l1 <= (60 * 2) || tbXyd1.getXjctime().getTime() < new Date().getTime()) {
                            WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid(tbXyd1.getXid(), "进场及消费");
                            YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("进场及消费");
                            TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(tbXyd1.getXid());
                            WlgbDksljl wlgbDksljl = JcJXfLcConfig.jcJXfLc(tbXyd1, villa, xydUrl, dingkey, employee1, ydAppkey, ydBd, ding, dksljl, tbYddXyd != null ? "是" : "否");
                            if (wlgbDksljl != null) {
                                wlgbDksljlService.save(wlgbDksljl);
                            }
                        }
                    } else {
                        WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid(tbXyd1.getXid(), "下单及跟单");
                        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("下单及跟单");
                        WlgbDksljl wlgbDksljl = XdJGdLcConfig.xdJGdLc(tbXyd1, villa, ding, dksljl, dingkey, employee1, xydUrl, ydAppkey, ydBd);
                        if (wlgbDksljl != null) {
                            wlgbDksljlService.save(wlgbDksljl);
                        }
                    }
                }
            }
            //托管加盟的
            if ("托管加盟".equals(vxz)) {
                Csry csry = weiLianDaiBanService.queryCsRyJmByUserId(villa.getPid());
                //判断店长是不是在范围内
                if (csry != null) {
                    //房东
                    DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(fdUserId);
                    //如果时间差的分钟数小于等于12个小时的分钟发送进场及消费，否则发送下单及跟单
                    if (l1 > (60 * 3)) {
                        WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid(tbXyd1.getXid(), "下单及跟单");
                        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("下单及跟单");
                        WlgbDksljl wlgbDksljl = XdJGdLcConfig.xdJGdLc(tbXyd1, villa, ding, dksljl, dingkey, employee1, xydUrl, ydAppkey, ydBd);
                        if (wlgbDksljl != null) {
                            wlgbDksljlService.save(wlgbDksljl);
                        }
                    }
                }
            }
        }

        WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(tbXyd1.getXid(), "下单表单提交");
        if (wlgbBdjl == null) {
            WlgbBdjl wlgbBdjl1 = new WlgbBdjl();
            wlgbBdjl1.setId(IdConfig.uuId());
            wlgbBdjl1.setTime(new Date());
            wlgbBdjl1.setXid(tbXyd1.getXid());
            wlgbBdjl1.setSlid(formInstId);
            wlgbBdjl1.setBz("下单表单提交");
            wlgbBdjl1.setUserid(tbXyd1.getXsendder());
            wlgbBdjlService.save(wlgbBdjl1);
        }
    }

    /**
     * 机器人发送策划群
     *
     * @param tbXyd 协议单
     * @param title 标题
     */
    public void ch(TbXyd tbXyd, String title) {
        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        TbKhlyxz khlyxz = weiLianService.queryKhLyXzByLidOrLname(tbXyd.getXtdxz());
        DingdingEmployee dingdingEmployee = null;
        if (tbVilla.getPid() != null && !"null".equalsIgnoreCase(tbVilla.getPid()) && !"".equals(tbVilla.getPid())) {
            dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbVilla.getPid());
        }
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String context = "#### <font color=#ff0303 size=7>" + title + "</font>";
        context += "\n\n><font size=4>房东</font>:<" + tbXyd.getXfd() + ">";
        context += "\n\n><font size=4>别墅</font>:<<font color=#ff0303>" + tbVilla.getVname() + "</font>>";
        context += "\n\n><font size=4>场次</font>:<<font color=#ff0303>进场：" + df2.format(tbXyd.getXjctime()) + "</font>>";
        context += "\n\n><font size=4>场次</font>:<<font color=#ff0303>退场：" + df2.format(tbXyd.getXtctime()) + "</font>>";
        context += "\n\n><font size=4>性质</font>:<<font color=#ff0303>" + tbXyd.getXdwmc() + "(" + khlyxz.getLname() + ")</font>>";
        context += "\n\n><font size=4>策划金额</font>:<<font color=#ff0303>" + (tbXyd.getXztze() != null ? tbXyd.getXztze() : 0) + "</font>>";
        context += "\n\n><font size=4>策划经理</font>:<<font color=#ff0303>" + (tbXyd.getXchjl() != null ? tbXyd.getXchjl() : "") + "</font>>";
        context += "\n\n><font size=4>策划成交人</font>:<" + (tbXyd.getXchcjr() != null ? tbXyd.getXchcjr() : "") + ">";
        context += "\n\n><font size=4>策划辅助成交人</font>:<" + (tbXyd.getXchfzcjr() != null ? tbXyd.getXchfzcjr() : "") + ">";
        if (dingdingEmployee != null) {
            context += "\n\n><font size=4>值班店长</font>:<" + dingdingEmployee.getName() + ">";
        }
        context += "\n\n><font size=4>备注信息</font>:<" + tbXyd.getXsxbz() + ">";
        Calendar calendar1 = Calendar.getInstance();
        context += "\n\n> ###### " + df2.format(calendar1.getTime()) + "     [发送]";
        String wook = jqrConfig.getChwebhook();
        String se = null;
        //线下
        se = jqrConfig.getChkey();
        DingDingUtil.sendMark(wook, se, title, context, null, false);
    }

    /**
     * 机器人发送轰趴师群
     *
     * @param tbXyd   ---协议单
     * @param context 发送内容
     */
    public void hps(TbXyd tbXyd, String context, Dingkey dingkey, String dingpan) throws Exception {
        List<String> list = new ArrayList<>();
        String wook = jqrConfig.getHpswebhook();
        String se = null;
        //线上
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        if (villa != null) {
            list = weiLianService.queryHpsAt(villa.getCity());
        }
        DingQunSend.sendQun1(dingkey, dingpan, "chatde3a0d1446cf4b2f99be143625faded5");
        DingDingUtil.sendMsg(wook, se, context, list, false);
    }

    /**
     * 剧本杀机器人播报轰趴师群
     *
     * @param context 发送内容
     */
    public void jbsBb(String context, Dingkey dingkey, String dingpan) throws Exception {
        List<String> list = new ArrayList<>();
        String wook = jqrConfig.getHpswebhook();
        String se = null;
        //线下
//        se = jqrConfig.getHpskey();
//        DingQunSend.sendQun1(dingkey, dingpan, "chate5bd183f6a0ebdd18e057550c487e5cc");
        //线上
        wook = jqrConfig.getHpswebhook();
        DingQunSend.sendQun1(dingkey, dingpan, "chatde3a0d1446cf4b2f99be143625faded5");
        DingDingUtil.sendMsg(wook, se, context, list, false);
    }

    /**
     * 机器人发送朋友圈转发
     *
     * @param context 发送内容
     */
    public void pyqZf(String context) {
        //线上
        DingDingUtil.sendMsg("https://oapi.dingtalk.com/robot/send?access_token=28ecb8285b817e09af58bf743b01db683bbd67d422704e65a8f537ab80b9d12b", "SEC5463aa211d2f21d4b86a8350fcc6af74e8fe0204a327d77aed853b604261ef6c", context, null, false);
    }

    /**
     * 电子协议单处理
     *
     * @param tbXyd 协议单对象
     * @return 协议单地址
     */
    public String dzXydCl(TbXyd tbXyd) throws Exception {
        Dzxyd dzxyd = dzxydService.queryByXid(tbXyd.getXid());
        String upload;
        if (dzxyd != null) {
            //电子协议单
            upload = dzxyd.getUrl();
        } else {
            String bddz = "D:\\tomcat9\\webapps\\weilian3\\";
            File file1 = new File(bddz + tbXyd.getXimagepath());
            //判断图片存在不存在,如果不存在将重新生成电子协议单
            if (!file1.exists()) {
//                dzXyd(tbXyd);
            }
            File file = new File(bddz + tbXyd.getXimagepath());
            FileInputStream fileInputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
            fileInputStream.close();
            upload = ossFileService.upload(multipartFile);
            if (upload != null && !"".equals(upload)) {
                upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
            }
            Dzxyd dzxyd1 = new Dzxyd();
            dzxyd1.setUrl(upload);
            dzxyd1.setTip(IdConfig.uuId());
            dzxyd1.setXid(tbXyd.getXid());
            dzxyd1.setTime(new Date());
            dzxydService.save(dzxyd1);
        }
        return upload;
    }

    /**
     * 上传对账表单
     *
     * @param tbXyd 协议单对象
     */
    public void scDzBd(TbXyd tbXyd, DingdingEmployee employee2, TbVilla villa, String userid, String xydUrl, YdAppkey ydAppkey) throws Exception {
        //本场基准价
        Map<String, Object> map = new HashMap<>();
        map.put("jcsj", tbXyd.getXjctime());
        map.put("tcsj", tbXyd.getXtctime());
        map.put("vid", tbXyd.getXbsmc());
        Double jzj = weiLianService.queryKdjJzj(map);
        WlgbBdjl wlgbBdjl2 = wlgbBdjlService.queryByXidAndBz(tbXyd.getXid(), "对账表单提交");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(tbXyd.getXid());


        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("对账");
        WlgbBdjl wlgbBdjl = DzBdConfig.scDzBd(tbXyd, employee2, villa, ding, dingkey, wlgbBdjl2, jzj, xydUrl, ydAppkey, ydBd, tbYddXyd != null ? "是" : "否");
        if (wlgbBdjl != null) {
            if (wlgbBdjl2 != null) {
                wlgbBdjlService.updateById(wlgbBdjl);
            } else {
                wlgbBdjlService.save(wlgbBdjl);
            }
        }
    }


}
