package com.wlgb.service.impl;

import com.wlgb.entity.WlgbDdhf;
import com.wlgb.mapper.WlgbDdhfMapper;
import com.wlgb.service.WlgbDdhfService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年06月05日 14:04
 */
@Service
public class WlgbDdhfServiceImpl implements WlgbDdhfService {
    @Resource
    private WlgbDdhfMapper wlgbDdhfMapper;

    @Override
    public void save(WlgbDdhf wlgbDdhf) {
        wlgbDdhf.setCreateTime(new Date());
        wlgbDdhfMapper.insertSelective(wlgbDdhf);
    }

    @Override
    public void updateById(WlgbDdhf wlgbDdhf) {
        wlgbDdhf.setUpdateTime(new Date());
        wlgbDdhfMapper.updateByPrimaryKeySelective(wlgbDdhf);
    }

    @Override
    public WlgbDdhf queryByXidAndSfSc(String xid) {
        Example example = new Example(WlgbDdhf.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xid", xid);
        criteria.andEqualTo("sfsc", 0);
        return wlgbDdhfMapper.selectOneByExample(example);
    }
}
