package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.openapi.sdk.api.customerauth.session.CustomerKeyShopScopeQuery;
import com.dianping.openapi.sdk.api.customerauth.session.entity.CustomerKeyShopScopeRequest;
import com.dianping.openapi.sdk.api.customerauth.session.entity.CustomerKeyShopScopeResponse;
import com.dianping.openapi.sdk.api.customerauth.session.entity.CustomerKeyShopScopeResponseEntity;
import com.dianping.openapi.sdk.api.oauth.entity.CustomerRefreshTokenResponse;
import com.dianping.openapi.sdk.httpclient.DefaultOpenAPIClient;
import com.dingtalk.api.request.OapiWorkrecordAddRequest;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.WlgbJmsbb;
import com.wlgb.entity.vo.WlgbNotVilla;
import com.wlgb.entity.vo.WlgbQrdDzjl;
import com.wlgb.service.*;
import com.wlgb.service2.TbQrdService;
import com.wlgb.service2.TbXydService;
import com.wlgb.service2.WeiLianService;
import com.wlgb.service2.WlgbYlxdService;
import com.wlgb.service3.FormitemService;
import com.wlgb.service3.OapiworkrecordService;
import com.wlgb.service3.WeiLianDaiBanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月17日 14:21
 */

/**
 * @Component标签一定要加上去，否则调用接口会报404
 */
@Component
@RestController
@RequestMapping(value = "/wlgb/job2")
public class JobsController {

    @Autowired
    private WlgbMtTokenService wlgbMtTokenService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private WlgbDksljlService wlgbDksljlService;
    @Autowired
    private WlgbBdjlService wlgbBdjlService;
    @Autowired
    private WlgbMtMdService wlgbMtMdService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WlgbYlxdService wlgbYlxdService;
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private TbQrdService tbQrdService;
    @Autowired
    private OssFileService ossFileService;
    @Autowired
    private FormitemService formitemService;
    @Autowired
    private OapiworkrecordService oapiworkrecordService;
    @Autowired
    private WlgbDdhfService wlgbDdhfService;
    @Autowired
    private WeiLianDaiBanService weiLianDaiBanService;
    @Autowired
    private WlgbJdYsdCdfjlService wlgbJdYsdCdfjlService;

//
//    /**
//     * 删除预留单
//     */
//    @RequestMapping(value = "scYld")
//    public Result scYld() {
//        System.out.println("开始删除预留单");
//        List<WlgbYlxd> list = wlgbYlxdService.list();
//        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
//        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
//        String token = null;
//        try {
//            token = DingToken.token(dingkey);
//        } catch (ApiException e) {
//            e.printStackTrace();
//        }
//        String finalToken = token;
//        list.forEach(l -> {
//            if ((System.currentTimeMillis()) - l.getYtitime().getTime() > (1000 * 2 * 60 * 60)) {
//                GatewayResult gatewayResult = new GatewayResult();
//                try {
//                    gatewayResult = DingBdLcConfig.scBdSl(finalToken, ydAppkey, "012412221639786136545", l.getSlid());
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                System.out.println("删除预留单：" + gatewayResult.toString());
//            }
//        });
//        return Result.OK();
//    }

//    /**
//     * 每天晚上12点半美团token是否过期，过期将刷新token
//     */
//    @RequestMapping(value = "mtToken")
////    @Scheduled(cron = "0 30 0 * * ? ")
//    public void mtToken() {
//        List<WlgbMtToken> list = wlgbMtTokenService.queryAllList();
//        list.forEach(l -> {
//            Date date = new Date();
//            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
//            String format = df2.format(date);
//            String format1 = df2.format(l.getEndtime());
//            if (format.equals(format1)) {
//                CustomerRefreshTokenResponse response = MtConfig.sxToken(l.getAppkey(), l.getAppsecret(), l.getRefreshToken());
//                if (response.getCode() == 200) {
//                    WlgbMtToken wlgbMtToken = new WlgbMtToken();
//                    wlgbMtToken.setId(l.getId());
//                    wlgbMtToken.setBid(response.getBid());
//                    wlgbMtToken.setToken(response.getAccess_token());
//                    wlgbMtToken.setRefreshToken(response.getRefresh_token());
//                    Date date1 = new Date();
//                    Calendar nowTime = Calendar.getInstance();
//                    nowTime.setTime(date1);
//                    nowTime.add(Calendar.SECOND, Integer.parseInt(String.valueOf(response.getExpires_in())));
//                    wlgbMtToken.setEndtime(nowTime.getTime());
//                    wlgbMtToken.setRemainRefreshCount(response.getRemain_refresh_count());
//                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                    System.out.println(sdf.format(nowTime.getTime()));
//                    wlgbMtTokenService.updateById(wlgbMtToken);
//                }
//            }
//        });
//    }
//
//    /**
//     * 二次跟单发送流程 10,18
//     */
//    @RequestMapping(value = "ejGdLcFs")
////    @Scheduled(cron = "0 0 10,18 * * ? ")
//    public void ejGdLcFs() {
//        List<TbXyd> list = weiLianDdXcxService.queryXyEcGd();
//        list.forEach(l -> {
//            TbVilla villa = weiLianDdXcxService.queryTbVillaById(l.getXbsmc());
//            if (villa != null) {
//                if (villa.getPid() != null && !"".equals(villa.getPid()) && !"null".equalsIgnoreCase(villa.getPid())) {
//                    Integer count3 = weiLianDdXcxService.queryBsCount(l.getXbsmc());
//                    if (count3 == 0) {
//                        Csry csry = null;
//                        String vxz = villa.getVxz();
//                        if ("直营".equals(vxz)) {
//                            csry = weiLianDdXcxService.queryCsRyByUserId(villa.getPid());
//                        }
//                        if ("托管加盟".equals(vxz)) {
//                            csry = weiLianDdXcxService.queryCsRyJmByUserId(villa.getPid());
//                        }
//                        if (csry != null) {
//                            int count1 = weiLianDdXcxService.queryJdbOneCountByXid(l.getXid());
//                            if (count1 > 0) {
//                                int count2 = wlgbDksljlService.queryCountByBzAndXid(l.getXid(), "进场及消费");
//                                if (count2 == 0) {
//                                    int count = wlgbDksljlService.queryCountByBzAndXid(l.getXid(), "二次跟单");
//                                    if (count < 1) {
//                                        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
//                                        String xydUrl = null;
//                                        try {
//                                            xydUrl = dzXydCl(l);
//                                        } catch (Exception e) {
//                                            e.printStackTrace();
//                                        }
//                                        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
//                                        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
//                                        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("二次跟单");
//                                        WlgbDksljl wlgbDksljl1 = wlgbDksljlService.queryByBzAndXid(l.getXid(), "二次跟单");
//                                        //发起人改成房东
//                                        try {
//                                            WlgbDksljl wlgbDksljl = EcGdLcConfig.ecGdLc(l, villa, dingkey, xydUrl, employee, ydAppkey, ydBd, wlgbDksljl1);
//                                            if (wlgbDksljl != null) {
//                                                wlgbDksljlService.save(wlgbDksljl);
//                                            }
//                                        } catch (Exception e) {
//                                            e.printStackTrace();
//                                            try {
//                                                DingDBConfig.sendGztzText(dingkey, "159909317438346", "定时任务发起二次跟单，订单编号”" + l.getXddbh() + "“更新定时任务发起二次跟单报错了");
//                                            } catch (ApiException apiException) {
//                                                apiException.printStackTrace();
//                                            }
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        });
//    }
//
//    /**
//     * 电子协议单处理
//     *
//     * @param tbXyd 协议单对象
//     * @return 协议单地址
//     */
//    public String dzXydCl(TbXyd tbXyd) {
//        Dzxyd dzxyd = weiLianDdXcxService.queryDzXydByXid(tbXyd.getXid());
//        if (dzxyd == null) {
//            return "";
//        }
//        String upload = dzxyd.getUrl();
//
//        return upload;
//    }
//
//    /**
//     * 发送进场及消费流程
//     * 从0点开始，每隔两个小时执行一次，11点和23点额外执行一次遗漏补差
//     */
//    @RequestMapping(value = "drcLcFs")
//    public void drcLcFs() {
//        //当前时间
//        Calendar c1 = Calendar.getInstance();
//        c1.set(Calendar.MINUTE, 0);
//        c1.set(Calendar.SECOND, 0);
//        c1.set(Calendar.MILLISECOND, 0);
//        //两个小时后
//        Calendar c2 = Calendar.getInstance();
//        c2.setTimeInMillis(c1.getTimeInMillis());
//        c2.add(Calendar.HOUR, 2);
//        Map<String, Object> map = new HashMap<>();
//        map.put("jc1", c1.getTime());
//        map.put("jc2", c2.getTime());
//
//        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
//        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("进场及消费");
//
//        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
//        List<TbXyd> list = weiLianDdXcxService.queryNotFsJcLc(map);
//        list.forEach(l -> {
//            TbVilla villa = weiLianDdXcxService.queryTbVillaById(l.getXbsmc());
//            if (villa != null) {
//                if (villa.getPid() != null && !"".equals(villa.getPid()) && !"null".equalsIgnoreCase(villa.getPid())) {
//                    Integer count1 = weiLianDdXcxService.queryBsCount(l.getXbsmc());
//                    if (count1 == 0) {
//                        Csry csry = weiLianDdXcxService.queryCsRyByUserId(villa.getPid());
//                        if (csry != null) {
//                            int count = wlgbDksljlService.queryCountByBzAndXid("进场及消费", l.getXid());
//                            if (count == 0) {
//                                int count2 = wlgbDksljlService.queryCountByBzAndXid("下单及跟单", l.getXid());
//                                boolean b = false;
//                                //防止老订单 以防没有其他流程
//                                if (count2 == 0) {
//                                    b = true;
//                                }
//                                if (count2 > 0) {
//                                    int count3 = wlgbDksljlService.queryCountByBzAndXid("二次跟单", l.getXid());
//                                    if (count3 > 0) {
//                                        int count4 = weiLianDdXcxService.queryJdbTwoCountByXid(l.getXid());
//                                        //二次跟单流程跟完的
//                                        if (count4 > 0) {
//                                            b = true;
//                                        }
//                                    }
//                                    if (count3 == 0) {
//                                        int count4 = weiLianDdXcxService.queryNotEcGdCountByXid(l.getXid());
//                                        //当下单及跟单已经跟完但提交时间已经在超过二次跟单发起的范围内的订单
//                                        if (count4 > 0) {
//                                            b = true;
//                                        }
//                                    }
//                                }
//                                if (b) {
//                                    String xydUrl = null;
//                                    try {
//                                        xydUrl = dzXydCl(l);
//                                    } catch (Exception e) {
//                                        e.printStackTrace();
//                                    }
//                                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
//                                    WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid("进场及消费", l.getXid());
//                                    try {
//                                        WlgbDksljl wlgbDksljl = JcJXfLcConfig.jcJXfLc(l, villa, xydUrl, dingkey, employee, ydAppkey, ydBd, null, dksljl);
//                                        if (wlgbDksljl != null) {
//                                            wlgbDksljlService.save(wlgbDksljl);
//                                        }
//                                    } catch (Exception e) {
//                                        e.printStackTrace();
//                                        try {
//                                            DingDBConfig.sendGztzText(dingkey, "159909317438346", "定时任务发起进场及消费，订单编号”" + l.getXddbh() + "“更新定时任务发起进场及消费报错了");
//                                        } catch (ApiException apiException) {
//                                            apiException.printStackTrace();
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        });
//        YdBd ydBd1 = weiLianDdXcxService.queryYdBdByBz("对账");
//        //对账表单发起
//        List<TbXyd> list1 = weiLianDdXcxService.queryNotFsDzBd(map);
//        list1.forEach(l -> {
//            TbVilla villa = weiLianDdXcxService.queryTbVillaById(l.getXbsmc());
//            DingdingEmployee employee2 = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa != null ? villa.getPid() : null);
//            try {
//                //上传对账表单
//                scDzBd(l, employee2, villa != null ? villa : new TbVilla(), l.getXsendder(), dingkey, ydAppkey, ydBd1);
//            } catch (Exception e) {
//                e.printStackTrace();
//                try {
//                    DingDBConfig.sendGztzText(dingkey, "159909317438346", "系统定时任务发起对账，订单编号”" + l.getXddbh() + "“对账表单报错了，错误原因：" + e);
//                } catch (ApiException apiException) {
//                    apiException.printStackTrace();
//                }
//            }
//        });
//
//    }
//
//    /**
//     * 上传对账表单
//     *
//     * @param tbXyd 协议单对象
//     */
//    public void scDzBd(TbXyd tbXyd, DingdingEmployee employee2, TbVilla villa, String userid, Dingkey dingkey, YdAppkey ydAppkey, YdBd ydBd) {
//        //本场基准价
//        Map<String, Object> map = new HashMap<>();
//        map.put("jcsj", tbXyd.getXjctime());
//        map.put("tcsj", tbXyd.getXtctime());
//        map.put("vid", tbXyd.getXbsmc());
//        Double jzj = weiLianDdXcxService.queryKdjJzj(map);
//        WlgbBdjl wlgbBdjl2 = wlgbBdjlService.queryByXidAndBz(tbXyd.getXid(), "对账表单提交");
//        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
//
//        String xydUrl = dzXydCl(tbXyd);
//        WlgbBdjl wlgbBdjl = DzBdConfig.scDzBd(tbXyd, employee2, villa, ding, dingkey, wlgbBdjl2, jzj, xydUrl, ydAppkey, ydBd);
//        if (wlgbBdjl != null) {
//            if (wlgbBdjl2 != null) {
//                wlgbBdjlService.updateById(wlgbBdjl);
//            } else {
//                wlgbBdjlService.save(wlgbBdjl);
//            }
//        }
//    }
//
//    /**
//     * 每天晚上1点20美团门店更新
//     */
//    @RequestMapping(value = "sxMtMd")
////    @Scheduled(cron = "0 20 1 * * ? ")
//    public void sxMtMd() {
//        List<WlgbMtToken> list = wlgbMtTokenService.queryAllList();
//        List<WlgbMtMd> list2 = new ArrayList<>();
//        list.forEach(l -> {
//            //session适用店铺查询接口
//            CustomerKeyShopScopeResponse c = MtConfig.getSYDP(l.getAppkey(), l.getAppsecret(), l.getToken(),
//                    l.getBid());
//            List<CustomerKeyShopScopeResponseEntity> data = c.getData();
//            data.forEach(d -> {
//                int count = wlgbMtMdService.queryCountByOpenUuId(d.getOpen_shop_uuid());
//                if (count == 0) {
//                    WlgbMtMd wlgbMtMd = new WlgbMtMd();
//                    wlgbMtMd.setId(IdConfig.uuId());
//                    wlgbMtMd.setOpenShopUuid(d.getOpen_shop_uuid());
//                    wlgbMtMd.setBranchname(d.getBranchname());
//                    wlgbMtMd.setCityname(d.getCityname());
//                    wlgbMtMd.setShopname(d.getShopname());
//                    wlgbMtMd.setShopaddress(d.getShopaddress());
//                    String dm1 = l.getZh().substring(l.getZh().length() - 3);
//                    wlgbMtMd.setZh(dm1);
//                    list2.add(wlgbMtMd);
//                }
//
//            });
//        });
//        wlgbMtMdService.saveBatch(list2);
//        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
//        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
//        String token = null;
//        try {
//            token = DingToken.token(dingkey);
//        } catch (ApiException e) {
//            e.printStackTrace();
//        }
//        String finalToken = token;
//        list2.forEach(l -> {
//            Map<String, Object> map = new HashMap<>();
//            map.put("open_shop_uuid", l.getOpenShopUuid());
//            map.put("shopname", l.getShopname());
//            map.put("branchname", l.getBranchname());
//            map.put("shopaddress", l.getShopaddress());
//            map.put("cityname", l.getCityname());
//            map.put("zh", l.getZh());
//            JSONObject json = new JSONObject(map);
//            GatewayResult gatewayResult = null;
//            try {
//                gatewayResult = DingBdLcConfig.xzBdSl(finalToken, ydAppkey, json.toJSONString(), "012412221639786136545", "FORM-HC9660C1OVURS0LOWOFF6Z36D11Y1VTSRB3RK11");
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            System.out.println(gatewayResult);
//        });
//    }


    @RequestMapping(value = "test2")
    public Result test2() {
        Map<String, Object> map1 = new HashMap<>();
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar3 = Calendar.getInstance();
        Calendar calendar4 = Calendar.getInstance();
        calendar3.set(Calendar.DAY_OF_MONTH, calendar3.get(Calendar.DAY_OF_MONTH) - 1);
        map1.put("jc", df2.format(calendar3.getTime()) + " 18:00:00");
        map1.put("tc", df2.format(calendar4.getTime()) + " 08:00:00");
        List<TbVilla> list = weiLianService.queryTodayWcMd(map1);

        return Result.OK(list);
    }

}
