package com.wlgb.service;

import com.wlgb.entity.WlgbHxyhDzjl;
import com.wlgb.entity.WlgbHxyhYjWkDzjl;

import java.util.List;

public interface WlgbHxyhYjWkDzjlService {
    void save(WlgbHxyhYjWkDzjl wlgbHxyhDzjl);

    void updateById(WlgbHxyhYjWkDzjl wlgbHxyhDzjl);

    WlgbHxyhYjWkDzjl queryByYsBhAndLsBh(String ysBh, String lsBh);

    List<WlgbHxyhYjWkDzjl> queryBdj();

    WlgbHxyhYjWkDzjl queryWlgbHxyhYjWkDzjlByWlgbHxyhYjWkDzjl(WlgbHxyhYjWkDzjl wlgbHxyhYjWkDzjl);
}
