package com.wlgb.service.impl;

import com.wlgb.entity.WlgbProduct4;
import com.wlgb.mapper.WlgbProduct4Mapper;
import com.wlgb.service.WlgbProduct4Service;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/18 18:46
 */
@Service
public class WlgbProduct4ServiceImpl implements WlgbProduct4Service {
    @Resource
    private WlgbProduct4Mapper wlgbProduct4Mapper;

    @Override
    public void save(WlgbProduct4 wlgbProduct4) {
        wlgbProduct4.setCreateTime(new Date());
        wlgbProduct4Mapper.insertSelective(wlgbProduct4);
    }

    @Override
    public void updateById(WlgbProduct4 wlgbProduct4) {
        wlgbProduct4.setUpdateTime(new Date());
        wlgbProduct4Mapper.updateByPrimaryKeySelective(wlgbProduct4);
    }

    @Override
    public List<WlgbProduct4> queryByDdbhList(String ddbh) {
        Example example = new Example(WlgbProduct4.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddbh", ddbh);
        criteria.andEqualTo("sfsc", 0);
        return wlgbProduct4Mapper.selectByExample(example);
    }
}
