package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "tb_ydd_nbuser")
@Data
public class TbYddNBUser {
    @Id
    @KeySql(useGeneratedKeys = true)
    private String id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 员工id
     */
    private String userid;
    /**
     * 员工sjh
     */
    private String sjh;

}
