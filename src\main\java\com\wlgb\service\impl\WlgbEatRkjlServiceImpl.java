package com.wlgb.service.impl;

import com.wlgb.entity.WlgbEatRkjl;
import com.wlgb.mapper.WlgbEatRkjlMapper;
import com.wlgb.service.WlgbEatRkjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/12 16:34
 */
@Service
public class WlgbEatRkjlServiceImpl implements WlgbEatRkjlService {
    @Resource
    private WlgbEatRkjlMapper wlgbEatRkjlMapper;

    @Override
    public void save(WlgbEatRkjl wlgbEatRkjl) {
        wlgbEatRkjl.setCreateTime(new Date());
        wlgbEatRkjlMapper.insertSelective(wlgbEatRkjl);
    }

    @Override
    public void clearData(String lsh) {
        WlgbEatRkjl wlgbEatRkjl = new WlgbEatRkjl();
        wlgbEatRkjl.setLsh(lsh);
        wlgbEatRkjl.setSfsc(0);
        wlgbEatRkjlMapper.delete(wlgbEatRkjl);
    }

    @Override
    public void deleteRkJl(String lsh) {
        WlgbEatRkjl wlgbEatRkjl = new WlgbEatRkjl();
        wlgbEatRkjl.setLsh(lsh);
        wlgbEatRkjl.setSfsc(0);
        List<WlgbEatRkjl> list = wlgbEatRkjlMapper.select(wlgbEatRkjl);
        list.forEach(l->{
            WlgbEatRkjl wlgbEatRkjl1 = new WlgbEatRkjl();
            wlgbEatRkjl1.setSfsc(1);
            wlgbEatRkjl1.setId(l.getId());
            wlgbEatRkjl1.setUpdateTime(new Date());
            wlgbEatRkjlMapper.updateByPrimaryKeySelective(wlgbEatRkjl1);
        });
    }
}
