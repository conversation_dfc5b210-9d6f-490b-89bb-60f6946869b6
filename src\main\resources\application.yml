server:
  port: 8082
  servlet:
    context-path: /ysdjtb
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  tomcat:
    uri-encoding: UTF-8
spring:
  #  main:
  #    # 解决bean corsFilter重复定义
  #    allow-bean-definition-overriding: true
  #  datasource:
  #    url: jdbc:mysql://**************:3306/weilian-ddxcx?characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
  #    username: wlgbdata
  #    password: '#uy4QJwNX&'
  #    driver-class-name: com.mysql.cj.jdbc.Driver
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: ***************************************************************************************************************************************************************************************************************
          username: wlgbdata
          password: '#uy4QJwNX&'
          driver-class-name: com.mysql.cj.jdbc.Driver
        second:
          url: *********************************************************************************************************************************************************************************************************
          username: wlgbdata
          password: '#uy4QJwNX&'
          driver-class-name: com.mysql.cj.jdbc.Driver
        third:
          url: ***************************************************************************************************************************************************************************************************************
          username: wlgbdata
          password: '#uy4QJwNX&'
          driver-class-name: com.mysql.cj.jdbc.Driver
        fourth:
          url: ******************************************************************************************************************************************************************************************************
          username: wlgbdata
          password: '#uy4QJwNX&'
          driver-class-name: com.mysql.cj.jdbc.Driver
# 打印sql语句
logging:
  level:
    com.wlgb.mapper: debug
    com.wlgb.mapper1: debug
mybatis:
  mapper-locations: classpath*:com/wlgb/mapper1/xml/*.xml
#OSS
oss:
  aliyun:
    endpoint: oss-cn-shenzhen.aliyuncs.com
    accessKey: LTAI5tBZxBNShSb4DbExkLgW
    secretKey: ******************************
    bucketName: jz-appjiuyundaojia
    staticDomain: oss-cn-shenzhen.aliyuncs.com

hanshujisuan:
  # 主要程序 异步程序，只给主要程序使用，为了解决宜搭、企微等品牌需要6秒内返回值
  ddxturl: 'https://yinshengdj.qianquan888.com'
  #线下测试地址
  #  ddxturl: 'https://u6r3157071.zicp.fun'
  # 抖音接口链接
  douyinurl: 'https://open.douyin.com'



#策划,轰趴师机器人播报配置
jqrtz:
  #线上
  chwebhook: https://oapi.dingtalk.com/robot/send?access_token=fcfdf72a3c019129000f8ce534710de94bd55b910131224ada2788bc90a44c0d
  chkey: SEC435041e1793d01fe2e43c025304ba328e8c675a7e164c1a81fd7e54f94161564
  hpswebhook: https://oapi.dingtalk.com/robot/send?access_token=ba136363420f8556defbca5ec64e425d9bd47564b23818684719d4d01defcee6
  hpskey: SECa69b4a0978fac61b5209da52e323b520d4e7f7ebed095623f12f783c34109aaa
  xdwebhook: https://oapi.dingtalk.com/robot/send?access_token=acf812dcf04fa96e94ebc71aeb125d940824d2e98b32fdd4cfaff7bc514bef2a
  xdkey: SECbb7e8fd708998c9a70b817309bf29277355116836de8007693b1c5cdb636d088
  #线上
  dhrjxydwebhook: https://oapi.dingtalk.com/robot/send?access_token=d64531c958612b00f3ef8b53e9a2c7bf617543491af6d6debc984cc5f3fcdc0d
  dhrjxydkey: SEC6b032cbe012a8b93eceeb8c7fe31ecbc753cf2fbcf8e77c4a5207c96d5067b29



#美团技术平台验券配置地址
mtpeizhi:
  #开发者ID（之前是湖南价值跳动 北极星   直营的账号）
  developerId: 112687
  #AppKey
  AppKey: a771979820d7bd32
  #SignKey
  SignKey: m6swe3tjjqh352tn
  #开发者ID （之前是长沙尽欢文化 北极星  加盟的账号）
  developerIdJM: 113438
  #AppKey
  AppKeyJM: c23e31427e8db956
  #SignKey
  SignKeyJM: k8lpus26c2lj7phf
  #获取门店token
  mdtokenurl: https://api-open-cater.meituan.com/oauth/token
  #输码验券校验
  prepare: https://api-open-cater.meituan.com/ddzh/tuangou/receipt/prepare
  #验券
  consume: https://api-open-cater.meituan.com/ddzh/tuangou/receipt/consume
  #查询已验券信息
  getconsumed: https://api-open-cater.meituan.com/ddzh/tuangou/receipt/getconsumed
  #查询门店星级和单项分
  querystar: https://api-open-cater.meituan.com/ddzh/ugc/querystar
  #单一门店查询评论数据
  queryshopreview: https://api-open-cater.meituan.com/ddzh/ugc/queryshopreview
  #对应客户门店ID映射关系
  queryPoiMapping: https://api-open-cater.meituan.com/ddzhkh/auth/token/queryPoiMapping
  #适用门店查询
  pageQueryPoiList: https://api-open-cater.meituan.com/ddzhkh/auth/token/pageQueryPoiList
