package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqDyYq;
import com.wlgb.entity.FwqWxfddtz;
import com.wlgb.entity.TbXyd;
import com.wlgb.mapper.FwqDyYqMapper;
import com.wlgb.mapper.FwqWxfddtzMapper;
import com.wlgb.service2.FwqDyYqService;
import com.wlgb.service2.FwqWxfddtzService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
@DS(value = "second")
public class FwqWxfddtzServiceImpl implements FwqWxfddtzService {
    @Resource
    private FwqWxfddtzMapper fwqWxfddtzMapper;

    @Override
    public void save(FwqWxfddtz fwqWxfddtz) {
        fwqWxfddtzMapper.insertSelective(fwqWxfddtz);
    }

    @Override
    public FwqWxfddtz selectByXddbh(String xddbh) {
        Example example = new Example(FwqWxfddtz.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xddbh", xddbh);
        return fwqWxfddtzMapper.selectOneByExample(example);
    }

}
