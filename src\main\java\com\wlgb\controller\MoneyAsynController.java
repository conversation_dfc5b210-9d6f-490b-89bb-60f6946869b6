package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.WlgbJdDjLsjlb;
import com.wlgb.entity.WlgbJdDjbbd;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service.WlgbJdDjLsjlbService;
import com.wlgb.service.WlgbJdDjbbdService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月17日 20:37
 */
@RestController
@RequestMapping(value = "/wlgb/money/asyn")
public class MoneyAsynController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private WlgbJdDjbbdService wlgbJdDjbbdService;
    @Autowired
    private WlgbJdDjLsjlbService wlgbJdDjLsjlbService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;


    @RequestMapping("/djbBdSave2")
    public Result djbBdSave2(@RequestParam(value = "datas", required = false) String datas,
                             @RequestParam(value = "formInstId", required = false) String formInstId) {
        this.logger.info("\n进入异步函数  datas = [{}]", datas);
        JSONObject jsonObject = JSON.parseObject(datas);
        WlgbJdDjbbd wlgbJdDjbbd = jsonObject.toJavaObject(WlgbJdDjbbd.class);
        if (!Optional.ofNullable(wlgbJdDjbbd).isPresent()) {
            return Result.error("对象类型为空");
        }
        Double je = wlgbJdDjbbd.getJe();
        if (je < 0.0) {
            return Result.error("金额不能小于0");
        }
        if (wlgbJdDjbbd.getDjwybs() != null && !"".equals(wlgbJdDjbbd.getDjwybs())) {
            Integer count = wlgbJdDjbbdService.queryCountByWybs(wlgbJdDjbbd.getDjwybs());
            if (count > 0) {
                return Result.error("唯一标识的数据已存在，不允许重复");
            }
        }

        //删除定金临时表有关此客户电话的记录
        List<WlgbJdDjLsjlb> listjl = wlgbJdDjLsjlbService.queryListByKhDhAndSfSc(wlgbJdDjbbd.getKhdh(), 0);
        listjl.forEach(l -> l.setSfsc(1));
        wlgbJdDjLsjlbService.updateBatchById(listjl);

        if (Optional.ofNullable(wlgbJdDjbbd.getEwm()).isPresent()) {
            JSONArray jsonArray = jsonObject.getJSONArray("ewm");

            List<String> list = new ArrayList<>();
            jsonArray.forEach(l -> {
                JSONObject jsonObject1 = JSONObject.parseObject(l.toString());
                String url = jsonObject1.getString("url");
                list.add(url);
            });
            wlgbJdDjbbd.setEwm(list.get(0));
        }
        String yqrName = jsonObject.getString("yqrName");
        if (yqrName != null && !"".equals(yqrName)) {
            wlgbJdDjbbd.setYqr(yqrName);
        }

        wlgbJdDjbbd.setId(IdConfig.uuId());
        wlgbJdDjbbd.setFormInstId(formInstId);
        wlgbJdDjbbdService.save(wlgbJdDjbbd);

        return Result.OK();
    }


    @RequestMapping("/djbBdUpdate2")
    public Result djbBdUpdate2(@RequestParam(value = "datas", required = false) String datas,
                               @RequestParam(value = "formInstId", required = false) String formInstId,
                               @RequestParam(value = "userid", required = false) String userid) {
        JSONObject jsonObject = JSON.parseObject(datas);

        WlgbJdDjbbd wlgbDjbbd = jsonObject.toJavaObject(WlgbJdDjbbd.class);
        if (!Optional.ofNullable(wlgbDjbbd).isPresent()) {
            return Result.error("对象类型为空");
        }
        WlgbJdDjbbd wlgbJdDjbbd = wlgbJdDjbbdService.queryByLshAndFormId(wlgbDjbbd.getLsh(), formInstId);
        if (!Optional.ofNullable(wlgbJdDjbbd).isPresent()) {
            return Result.error("对象为空");
        }

        wlgbDjbbd.setId(wlgbJdDjbbd.getId());
        wlgbDjbbd.setEwm(null);

        if (wlgbDjbbd.getSfsc() == 1) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            String text = "您删除了一条的定金收款单！";

            if (userid.equals(wlgbJdDjbbd.getFqrId())) {
                text = "您发起的定金收款单已被删除";
            }
            text += "\n\n客户电话：" + wlgbJdDjbbd.getKhdh();
            text += "\n定金类型:" + (wlgbJdDjbbd.getDjlx() == 1 ? "线下" : "线上");
            text += "\n定金金额：" + wlgbJdDjbbd.getJe();
            if (!userid.equals(wlgbJdDjbbd.getFqrId())) {
                DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
                text += "\n删除人:" + dingdingEmployee.getName();
            }
            text += "\n\n删除时间：" + sdf.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, wlgbJdDjbbd.getFqrId(), text);
                if (!userid.equals(wlgbJdDjbbd.getFqrId())) {
                    DingDBConfig.sendGztzText(dingkey, userid, text);
                }
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
        String yqrName = jsonObject.getString("yqrName");
        if (yqrName != null && !"".equals(yqrName)) {
            wlgbDjbbd.setYqr(yqrName);
        }
        wlgbJdDjbbdService.updateById(wlgbDjbbd);
        return Result.OK();
    }

}
