package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/5 15:20
 */
@Data
@Table(name = "wlgb_order_cyjl")
public class WlgbOrderCyjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**创建时间*/
    private Date createTime;
    /**修改时间*/
    private Date updateTime;
    /**订单编号*/
    private String ddbh;
    /**餐饮名称*/
    private String cymc;
    /**餐饮价格*/
    private Double cyjg;
    /**餐饮数量*/
    private Integer cysl;
    /**餐饮总金额*/
    private Double cyje;
    /**餐饮成本*/
    private Double cycb;
    /**餐饮总成本*/
    private Double cyzcb;
    /**餐饮实际成本*/
    private Double cysjcb;
    /**餐饮实际总成本*/
    private Double cysjzcb;
    /**餐饮实际利润*/
    private Double cysjlr;
    /**餐饮成交人*/
    private String cycjr;
    /**餐饮类别*/
    private String cylb;
    /**餐饮集群*/
    private String cyjq;
    /**是否删除(0:否，1:是)*/
    private Integer csfsc;
    /**餐饮属性，直营/外包*/
    private String cysx;
    /**烤全羊执行人*/
    private String kqyzxr;
    /**烤全羊执行人id*/
    private String kqyzxrid;
    /**厨师姓名*/
    private String cycsxm;
    /**厨师id*/
    private String cycsid;
    /**餐饮唯一标识*/
    private String cybs;
}
