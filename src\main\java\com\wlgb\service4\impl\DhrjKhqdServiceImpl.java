package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.DhrjKhqd;
import com.wlgb.mapper.DhrjKhqdMapper;
import com.wlgb.service4.DhrjKhqdService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 16:14
 */
@Service
@DS("fourth")
public class DhrjKhqdServiceImpl implements DhrjKhqdService {
    @Resource
    private DhrjKhqdMapper dhrjKhqdMapper;

    @Override
    public void save(DhrjKhqd dhrjKhqd) {
        dhrjKhqdMapper.insertSelective(dhrjKhqd);
    }

    @Override
    public void updateById(DhrjKhqd dhrjKhqd) {
        dhrjKhqdMapper.updateByPrimaryKeySelective(dhrjKhqd);
    }

    @Override
    public DhrjKhqd queryDhrjKhqdByDhrjKhqd(DhrjKhqd dhrjKhqd) {
        return dhrjKhqdMapper.selectOne(dhrjKhqd);
    }
}
