package com.wlgb.controller;

import com.wlgb.config.Result;
import com.wlgb.entity.FwqHnfpXiaoqu;
import com.wlgb.service.OssFileService;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.FwqHnfpService;
import com.wlgb.service2.FwqHnfpXiaoquService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static com.wlgb.config.Tools.isEmpty;

/**
 * <AUTHOR>
 * @Date 2025/02/21 18:30
 * @Version 1.0
 */
@RestController
@Slf4j
@RequestMapping(value = "/hnfp/xiaoqu")
public class FwqHnfpXiaoquController {
    @Autowired
    private FwqHnfpXiaoquService fwqHnfpXiaoquService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private OssFileService ossFileService;


    /**
     * 查询小区的数据
     */
    @RequestMapping(value = "selectXiaoqu")
    public Result selectXiaoqu(@RequestParam(value = "sousuo", defaultValue = "") String sousuo,
                              @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                              @RequestParam(value = "pageSize", defaultValue = "100000000") int pageSize) {
        log.info("***************sousuosousuo{}", sousuo);
        List<FwqHnfpXiaoqu> list = fwqHnfpXiaoquService.selectAllBySousuo(sousuo, pageNum, pageSize);
        return Result.OK(list);
    }


}
