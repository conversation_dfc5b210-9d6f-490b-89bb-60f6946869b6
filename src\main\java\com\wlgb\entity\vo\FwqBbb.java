package com.wlgb.entity.vo;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 昵称表
 * @Author: jeecg-boot
 * @Date:   2020-11-01
 * @Version: V1.0
 */
@Data
@Table(name = "fwq_bbb")
public class FwqBbb {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
    /**钉钉id*/
    private java.lang.String xddid;

	/**房东姓名*/
    private java.lang.String xfd;
	/**该订单业绩*/
    private java.lang.Double xzyj;
	/**昵称*/
    private java.lang.String xnc;
}
