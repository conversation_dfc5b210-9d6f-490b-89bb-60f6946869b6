package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqHnfpXiaoqu;
import com.wlgb.mapper.FwqHnfpMapper;
import com.wlgb.mapper.FwqHnfpXiaoquMapper;
import com.wlgb.service2.FwqHnfpService;
import com.wlgb.service2.FwqHnfpXiaoquService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Service
@DS(value = "second")
public class FwqHnfpXiaoquServiceImpl implements FwqHnfpXiaoquService {
    @Resource
    private FwqHnfpXiaoquMapper fwqHnfpXiaoquMapper;

    @Override
    public void save(FwqHnfpXiaoqu fwqHnfp) {
        fwqHnfpXiaoquMapper.insertSelective(fwqHnfp);
    }

    @Override
    public void deleteByPrimaryKey(FwqHnfpXiaoqu fwqHnfp) {
        fwqHnfpXiaoquMapper.deleteByPrimaryKey(fwqHnfp);
    }

    @Override
    public FwqHnfpXiaoqu selectOneByName(String url) {
        Example example = new Example(FwqHnfpXiaoqu.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("zyurl", url);
        //是否有效
        criteria.andEqualTo("sfsc", "0");
        return fwqHnfpXiaoquMapper.selectOneByExample(example);
    }

    @Override
    public void update(FwqHnfpXiaoqu fwqHnfp) {
        fwqHnfpXiaoquMapper.updateByPrimaryKey(fwqHnfp);
    }

    @Override
    public List<FwqHnfpXiaoqu> selectAllBySousuo(String sousuo, int pageNum, int pageSize) {
        int offset = (pageNum - 1) * pageSize;
        return fwqHnfpXiaoquMapper.selectGroupByXiaoquName(sousuo, offset, pageSize);
    }
}
