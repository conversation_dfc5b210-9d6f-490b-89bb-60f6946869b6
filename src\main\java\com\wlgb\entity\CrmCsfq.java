package com.wlgb.entity;


import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: crm_cs
 * @Author: jeecg-boot
 * @Date:   2020-12-02
 * @Version: V1.0
 */
@Data
@Table(name = "crm_csfq")
public class CrmCsfq {
	/**城市分区id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private Integer csfqid;
	/**城市id*/
    private Integer csid;
	/**城市名称*/
    private String csname;
    /**分区名称*/
    private String fqname;
	/**正常：0，删除：1*/
    private String sfsc;
}
