package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdYxcgsqjl;
import com.wlgb.mapper.WlgbJdYxcgsqjlMapper;
import com.wlgb.service.WlgbJdYxcgsqjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/30 10:30
 */
@Service
public class WlgbJdYxcgsqjlServiceImpl implements WlgbJdYxcgsqjlService {
    @Resource
    private WlgbJdYxcgsqjlMapper wlgbJdYxcgsqjlMapper;

    @Override
    public void save(WlgbJdYxcgsqjl wlgbJdYxcgsqjl) {
        wlgbJdYxcgsqjl.setCreateTime(new Date());
        wlgbJdYxcgsqjl.setId(IdConfig.uuId());
        wlgbJdYxcgsqjlMapper.insertSelective(wlgbJdYxcgsqjl);
    }

    @Override
    public void updateById(WlgbJdYxcgsqjl wlgbJdYxcgsqjl) {
        wlgbJdYxcgsqjl.setUpdateTime(new Date());
        wlgbJdYxcgsqjlMapper.updateByPrimaryKeySelective(wlgbJdYxcgsqjl);
    }

    @Override
    public WlgbJdYxcgsqjl queryBySpBh(String spBh) {
        Example example = new Example(WlgbJdYxcgsqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        return wlgbJdYxcgsqjlMapper.selectOneByExample(example);
    }

    @Override
    public WlgbJdYxcgsqjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd) {
        Example example = new Example(WlgbJdYxcgsqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdYxcgsqjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdYxcgsqjl> queryBySfLrJd(Integer sfLrJd) {
        Example example = new Example(WlgbJdYxcgsqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdYxcgsqjlMapper.selectByExample(example);
    }
}
