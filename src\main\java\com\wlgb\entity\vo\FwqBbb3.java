package com.wlgb.entity.vo;

import lombok.Data;

/**
 * @Description: fwq_bbb3
 * @Author: jeecg-boot
 * @Date:   2020-12-21
 * @Version: V1.0
 */
@Data
public class FwqBbb3 {

	/**id*/
    private java.lang.Integer id;
	/**场地费业绩*/
    private java.lang.String cdfyj;
	/**部门编号*/
    private java.lang.Integer bm;
	/**订单数量*/
    private java.lang.Integer ddsl;
	/**增值业绩*/
    private java.lang.Double zzyj;
	/**总业绩*/
    private java.lang.Double zyj;
	/**中心部门（1.营销中心、2.一线铁军、3.其他职能部门）*/
    private java.lang.String bmzx;
	/**备用*/
    private java.lang.String by;
	/**备用2*/
    private java.lang.String by2;
}
