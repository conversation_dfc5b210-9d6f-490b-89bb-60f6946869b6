package com.wlgb.service.impl;

import com.wlgb.entity.WlgbBdjl;
import com.wlgb.mapper.WlgbBdjlMapper;
import com.wlgb.service.WlgbBdjlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月18日 10:15
 */
@Service
@Slf4j
public class WlgbBdjlServiceImpl implements WlgbBdjlService {
    @Resource
    private WlgbBdjlMapper wlgbBdjlMapper;

    @Override
    public void save(WlgbBdjl wlgbBdjl) {
        wlgbBdjl.setCreateTime(new Date());
        boolean cfTest = false;
        try {
            wlgbBdjlMapper.insertSelective(wlgbBdjl);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbBdjlMapper.insertSelective(wlgbBdjl);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，insert：参数" + wlgbBdjl.toString());
                    }
                }
            }
        }

    }

    @Override
    public void updateById(WlgbBdjl wlgbBdjl) {
        wlgbBdjl.setUpdateTime(new Date());
        boolean cfTest = false;
        try {
            wlgbBdjlMapper.updateByPrimaryKeySelective(wlgbBdjl);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbBdjlMapper.updateByPrimaryKeySelective(wlgbBdjl);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，update：参数" + wlgbBdjl.toString());
                    }
                }
            }
        }
    }

    @Override
    public WlgbBdjl queryByXidAndBz(String xid, String bz) {
        Example example = new Example(WlgbBdjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xid", xid);
        criteria.andEqualTo("bz", bz);
        return wlgbBdjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbBdjl> queryByXid(String xid) {
        Example example = new Example(WlgbBdjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xid", xid);
        return wlgbBdjlMapper.selectByExample(example);
    }

    @Override
    public void removeById(String id) {
        wlgbBdjlMapper.deleteByPrimaryKey(id);
    }
}
