package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 调拨记录，从A集群调拨到B集群
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/12 16:13
 */
@Data
@Table(name = "wlgb_eat_dbjl")
public class WlgbEatDbjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 流水号
     */
    private String lsh;
    /**
     * 提交人
     */
    private String tjr;
    /**
     * 提交人id
     */
    private String tjrid;
    /**
     * 出库集群名称
     */
    private String ckjqname;
    /**
     * 出库集群编号
     */
    private String ckjqbh;
    /**
     * 出库供应商名称
     */
    private String ckgysname;
    /**
     * 出库供应商编号
     */
    private String ckgysbh;
    /**
     * 出库食材数量
     */
    private Integer ckscnum;
    /**
     * 出库食材成本
     */
    private Double cksccb;
    /**
     * 出库食材名称
     */
    private String ckscmc;
    /**
     * 出库食材编号
     */
    private String ckscbh;
    /**
     * 调拨时间
     */
    private Date dbtime;
    /**
     * 入库集群名称
     */
    private String rkjqname;
    /**
     * 入库集群编号
     */
    private String rkjqbh;
    /**
     * 入库供应商名称
     */
    private String rkgysname;
    /**
     * 入库供应商编号
     */
    private String rkgysbh;
    /**
     * 是否删除(0:否，1:是)
     */
    private Integer sfsc;

}
