package com.wlgb.entity;

import java.io.Serializable;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 确认单_对账
 * @Author: jeecg-boot
 * @Date:   2020-09-30
 * @Version: V1.0
 */
@Data
@Table(name = "tb_qrd")
public class TbQrd {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String qid;
	/**费用明细*/
    private java.lang.String qfymx;
	/**入场电度数*/
    private java.lang.Double qrcdds;
	/**退场电度数*/
    private java.lang.Double qtcdds;
	/**用电度数*/
    private java.lang.Double qydds;
	/**电费*/
    private java.lang.Double qdf;
	/**商品消费*/
    private java.lang.Double qspxf;
	/**酒水消费*/
    private java.lang.Double qjsxf;
	/**订餐消费*/
    private java.lang.Double qdcxf;
	/**代沟食材*/
    private java.lang.Double qdgsc;
	/**烧烤套餐*/
    private java.lang.Double qsktc;
	/**策划服务*/
    private java.lang.Double qchfw;
	/**卫生费*/
    private java.lang.Double qwsf;
	/**延时费*/
    private java.lang.Double qysf;
	/**赔偿费*/
    private java.lang.Double qbcf;
	/**订餐成本*/
    private java.lang.Double qdccb;
	/**烧烤成本*/
    private java.lang.Double qskcb;
	/**应退押金*/
    private java.lang.Double qytyj;
	/**客户评价 1:非常满意、2：满意、3.不满意*/
    private java.lang.String qkhpj;
	/**补差金额*/
    private java.lang.Double qbcje;
	/**确认单文件*/
    private java.lang.String qqrdF;
	/**订单成本文件*/
    private java.lang.String qddcbF;
	/**烧烤成本文件*/
    private java.lang.String qskcbF;
	/**备注信息*/
    private java.lang.String qremark;
	/**共收入金额*/
    private java.lang.Double qgsr;
	/**共支出金额*/
    private java.lang.Double qgzc;
	/**发送人id*/
    private java.lang.String qsendder;
	/**公司应收*/
    private java.lang.Double qgsys;
	/**确认时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date qrsj;
	/**商品确认单 文件*/
    private java.lang.String qspqrdF;
	/**协议单id*/
    private java.lang.String qxydid;
	/**实到人数*/
    private java.lang.Integer qsdrs;
	/**实转条数*/
    private java.lang.Integer qszts;
	/**实评条数*/
    private java.lang.Integer qspts;
	/**应收场地费*/
    private java.lang.Double qsjcdf;
	/**实进场小时*/
    private java.lang.String qjctime;
	/**实进场分钟*/
    private java.lang.String qjcmin;
	/**实退场小时*/
    private java.lang.String qtctime;
	/**实退场分钟*/
    private java.lang.String qtcmin;
	/**超出小时*/
    private java.lang.String qccxs;
	/**厨房使用费*/
    private java.lang.Double qcfsyf;
	/**超出人数费用*/
    private java.lang.Double qccrsfy;
	/**场地增收*/
    private java.lang.Double qcdzs;
	/**缴纳金额*/
    private java.lang.Double qjnje;
	/**回访评价 0：不满意 1：满意*/
    private java.lang.Integer qhfpj;
	/**是否被审核 0:未审核，允许修改确认单、1：已审核，不允许修改*/
    private java.lang.Integer qissh;
	/**值班店长*/
    private java.lang.String qzbdz;
	/**是否删除 0否 1是*/
    private java.lang.String qsfsc;
	/**啤酒开支*/
    private java.lang.Double qpjkz;
	/**调账*/
    private java.lang.Double qtz;
	/**未收取原因*/
    private java.lang.String qwsqyy;
	/**删除者*/
    private java.lang.String qscz;
	/**删除时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date qscsj;
	/**删除原因*/
    private java.lang.String qscyy;
	/**审核时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date qshsj;
	/**审核人*/
    private java.lang.String qshr;
	/**平台好评码1*/
    private java.lang.String xpthpm1;
	/**平台好评码2*/
    private java.lang.String xpthpm2;
	/**客户对轰趴师评价*/
    private java.lang.String qkhpj2;
	/**客户对店长评价*/
    private java.lang.String qkhpj3;
	/**0:无撞单  1：双方平分*/
    private java.lang.Integer xysfzdcl;
	/**延时费归属人1*/
    private java.lang.String qysfgsr1;
	/**延时费归属人2*/
    private java.lang.String qysfgsr2;
	/**0.32/0.27*/
    private java.lang.Double qspzhgsbl;
	/**校代金额*/
    private java.lang.Double qxdje;
	/**自助餐收入*/
    private java.lang.Double qzzcsr;
	/**策划其他收入*/
    private java.lang.Double qchqtsr;
    /**押金*/
    private java.lang.Double qyj;
	/**转发证据截图*/
    private java.lang.String zfjzjt;
    /**卫生证据截图*/
    private java.lang.String wsjzjt;
    /**赔偿证据截图*/
    private java.lang.String pcjzjt;
    /**策划成本*/
    private java.lang.Double qchcb;
    /**转账来源*/
    private java.lang.String qzzly;
    /**转到账户*/
    private java.lang.String qzdzh;
    /**转账时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date qzzsj;
    /**转账金额*/
    private java.lang.Double zzje;
    /**转账图片1*/
    private java.lang.String zztp1;
    /**转账图片2*/
    private java.lang.String zztp2;

    /**场地费尾款金额*/
    private java.lang.Double cdfwkje;

    /**增值尾款金额*/
    private java.lang.Double zzwkje;

    /**是否完成对账(0:否，1:是)*/
    private java.lang.String sfwcdz;

    /**是否调用接口*/
    private java.lang.Integer sfdyjk;

    /**店长完成对账是否审核（0：否，1：是）*/
    private java.lang.String dzysfsh;
    /**商品确认单1 文件*/
    private String qspqrdF1;
    /**商品确认单2 文件*/
    private String qspqrdF2;
    /**订单成本文件2*/
    private String qddcbF2;
    /**订单成本文件2*/
    private String qddcbF3;
    /**烧烤成本文件2*/
    private String qskcbF2;
    /**烧烤成本文件3*/
    private String qskcbF3;
    /**剧本杀支出*/
    private java.lang.Double qjbszc;

    /**剧本杀执行人工资*/
    private java.lang.Double qjbszxrgz;

    /**确认单图片地址*/
    private java.lang.String qimagepath;

    /**客户退押金方式(0支付宝，1银行卡)*/
    private java.lang.String qkhskfs;
    /**客户支付宝账号*/
    private java.lang.String qkhzfb;
    /**客户银行卡*/
    private java.lang.String qkhyhk;
    /**退店长金额*/
    private java.lang.Double qtdzje;
    /**是否异常*/
    private java.lang.Integer qsfyc;
    /**特殊说明*/
    private java.lang.String qtssm;
    /**代购收入*/
    private java.lang.Double qdgsr;
    /**代购成本*/
    private java.lang.Double qdgcb;
    /**客户开支*/
    private java.lang.Double qkhkz;
    /**店长开支*/
    private java.lang.Double qdzkz;
    /**供应商1*/
    private java.lang.String qgys1;
    /**供应商2*/
    private java.lang.String qgys2;
    /**供应商1卡号*/
    private java.lang.String qgysyhk1;
    /**供应商2卡号*/
    private java.lang.String qgysyhk2;
    /**客户姓名*/
    private java.lang.String qkhxm;
    /**客户身份证号*/
    private java.lang.String qkhsfzh;
    /**客户银行卡号*/
    private java.lang.String qkhyhkh;
    /**省份+银行名称*/
    private java.lang.String qsfyhmc;
    /**客户实际已付定金*/
    private java.lang.Double qkhsjyfzdj;
    /**自助餐成本*/
    private java.lang.Double qzzccb;
    /**自助餐成本图片1*/
    private java.lang.String qzzccbtp1;
    /**自助餐成本图片2*/
    private java.lang.String qzzccbtp2;
    /**策划转回公司比例*/
    private java.lang.Double qchzhgsbl;
    /**策划转回公司金额*/
    private java.lang.Double qchzhgsje;
    /**策划外包金额*/
    private java.lang.Double qchwbje;
    /**策划外包成本*/
    private java.lang.Double qchwbcb;
    /**策划外包利润*/
    private java.lang.Double qchwblr;
    /**策划外包成本图片1*/
    private java.lang.String qchwbcbtp1;
    /**策划外包成本图片2*/
    private java.lang.String qchwbcbtp2;
    /**是否店长对账*/
    private java.lang.String sfdzdz;
    /**是否延时单  0：否，1：是*/
    private java.lang.String qsfys;
    /**延时备注*/
    private java.lang.String qysbz;
    /**扯皮开支*/
    private java.lang.Double qcpkz;
    /**扯皮开支支出类型*/
    private java.lang.String qcpkzzclx;
    /**策划执行人*/
    private java.lang.String qchzxr;
    /**保洁姓名*/
    private java.lang.String qbj;
    /**保洁临时人员姓名*/
    private java.lang.String bjlsryxm;
    /**保洁临时人员联系方式*/
    private java.lang.String bjlsrylsfs;
    /**店长提交业绩开支*/
    private java.lang.Double dztjyjkz;
    /**代购成本单*/
    private java.lang.String qdgcbd;
    /**代购付款截图*/
    private java.lang.String qdgfkjt;
    /**延时时长*/
    private java.lang.Integer qyssc;
    /**延时单价*/
    private java.lang.Double qysdj;
    /**优惠合计*/
    private java.lang.Double qyhhj;
    /**场地费优惠金额*/
    private java.lang.Double qcdfyhje;
    /**本场基准价*/
    private java.lang.Double qbcjzj;
    /**差值*/
    private java.lang.Double qcz;
    /**商品应转回公司金额2*/
    private java.lang.Double qspyzhgsje2;
    /**店长收入*/
    private java.lang.Double qdzsr;
    /**公司应收餐费*/
    private java.lang.Double qgsyscf;
    /**应转回公司布置策划金额*/
    private java.lang.Double yzhgsbzchje;
    /**分公司经费*/
    private java.lang.Double qfgsjf;
    /**自助餐利润*/
    private java.lang.Double qzzclr;
    /**优惠后场地费金额*/
    private java.lang.Double qyhhcdfje;
    /**是否代班保洁*/
    private java.lang.String qsfdbbj;
    /**电费优惠金额*/
    private java.lang.Double xdfje;
    /**账套编码*/
    private java.lang.String qztbm;
    /**店长对账及时率打分*/
    private java.lang.String qdzjsl;
    /**及时率打分备注*/
    private java.lang.String qdzjslbz;
    /**店长对账内容正确率打分*/
    private java.lang.String qdzzql;
    /**对账内容正确率打分备注*/
    private java.lang.String qdzzqlbz;
    /**供应商姓名*/
    private java.lang.String gysname;
    /**供应商收款账号*/
    private java.lang.String gysskzh;
    /**供应商收款银行*/
    private java.lang.String gysskyh;
    /**餐饮保洁*/
    private java.lang.String cybj;
    /**是否预定单*/
    private java.lang.String sfydd;

}
