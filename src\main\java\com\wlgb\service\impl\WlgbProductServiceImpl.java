package com.wlgb.service.impl;

import com.wlgb.entity.WlgbProduct;
import com.wlgb.mapper.WlgbProductMapper;
import com.wlgb.service.WlgbProductService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/18 19:29
 */
@Service
public class WlgbProductServiceImpl implements WlgbProductService {
    @Resource
    private WlgbProductMapper wlgbProductMapper;

    @Override
    public WlgbProduct queryBySpbhAndTxm(String spbh, String txm) {
        Example example = new Example(WlgbProduct.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spbh);
        criteria.andEqualTo("txm", txm);
        criteria.andEqualTo("sfsc", 0);
        return wlgbProductMapper.selectOneByExample(example);
    }
}
