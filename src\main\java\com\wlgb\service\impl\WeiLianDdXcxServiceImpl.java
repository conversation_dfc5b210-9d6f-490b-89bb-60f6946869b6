package com.wlgb.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.*;
import com.wlgb.mapper1.WeiLianDdXcxMapper;
import com.wlgb.service.WeiLianDdXcxService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月17日 14:44
 */
@Service
public class WeiLianDdXcxServiceImpl implements WeiLianDdXcxService {

    @Resource
    private WeiLianDdXcxMapper weiLianDdXcxMapper;

    @Override
    public List<TbXyd> queryXyEcGd() {
        return weiLianDdXcxMapper.queryXyEcGd();
    }

    @Override
    public Integer queryBsCount(String vid) {
        return weiLianDdXcxMapper.queryBsCount(vid);
    }

    @Override
    public Csry queryCsRyByUserId(String userId) {
        return weiLianDdXcxMapper.queryCsRyByUserId(userId);
    }

    @Override
    public Csry queryCsRyJmByUserId(String userId) {
        return weiLianDdXcxMapper.queryCsRyJmByUserId(userId);
    }

    @Override
    public Integer queryJdbOneCountByXid(String xid) {
        return weiLianDdXcxMapper.queryJdbOneCountByXid(xid);
    }

    @Override
    public Integer queryJdbTwoCountByXid(String xid) {
        return weiLianDdXcxMapper.queryJdbTwoCountByXid(xid);
    }

    @Override
    public TbVilla queryTbVillaById(String vid) {
        return weiLianDdXcxMapper.queryTbVillaById(vid);
    }

    @Override
    public TbVilla queryTbVillaByIdOrVname(String vidorvname) {
        return weiLianDdXcxMapper.queryTbVillaByIdOrVname(vidorvname);
    }

    @Override
    public Dingkey queryDingKeyById(String id) {
        return weiLianDdXcxMapper.queryDingKeyById(id);
    }

    @Override
    public DingdingEmployee queryDingdingEmployeeByUserId(String userId) {
        return weiLianDdXcxMapper.queryDingdingEmployeeByUserId(userId);
    }

    @Override
    public DingdingEmployee queryDingdingEmployeeByUserName(String name) {
        return weiLianDdXcxMapper.queryDingdingEmployeeByUserName(name);
    }

    @Override
    public DingdingEmployee queryDingdingEmployeeBySJH(String mobile) {
        return weiLianDdXcxMapper.queryDingdingEmployeeBySJH(mobile);
    }

    @Override
    public Dzxyd queryDzXydByXid(String xid) {
        return weiLianDdXcxMapper.queryDzXydByXid(xid);
    }

    @Override
    public YdAppkey queryYdAppKeyByBz(String bz) {
        return weiLianDdXcxMapper.queryYdAppKeyByBz(bz);
    }

    @Override
    public YdBd queryYdBdByBz(String bz) {
        return weiLianDdXcxMapper.queryYdBdByBz(bz);
    }

    @Override
    public List<TbXyd> queryNotFsJcLc(Map<String, Object> map) {
        return weiLianDdXcxMapper.queryNotFsJcLc(map);
    }

    @Override
    public List<TbXyd> queryNotFsDzBd(Map<String, Object> map) {
        return weiLianDdXcxMapper.queryNotFsDzBd(map);
    }

    @Override
    public Integer queryNotEcGdCountByXid(String xid) {
        return weiLianDdXcxMapper.queryNotEcGdCountByXid(xid);
    }

    @Override
    public Double queryKdjJzj(Map<String, Object> map) {
        return weiLianDdXcxMapper.queryKdjJzj(map);
    }

    @Override
    public List<TbVilla> queryEhDbBsList() {
        return weiLianDdXcxMapper.queryEhDbBsList();
    }

    @Override
    public List<WlgbHrQun> queryHrQunList() {
        return weiLianDdXcxMapper.queryHrQunList();
    }

    @Override
    public List<HrLsZy> queryHrLsZy(Map<String, Object> map) {
        return weiLianDdXcxMapper.queryHrLsZy(map);
    }

    @Override
    public List<HrLsZy> queryHrLsJl(Map<String, Object> map) {
        return weiLianDdXcxMapper.queryHrLsJl(map);
    }

    @Override
    public PageHelpUtil QueryMtXsList(Map<String, Object> map) {
        List<WlgbJdDjbbdVo> list = weiLianDdXcxMapper.QueryMtXsList(map);
        Integer count = weiLianDdXcxMapper.CoutMtXsList(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public List<WlgbJdDjbbd> queryDjbDataByFormIdList(Map<String, Object> map) {
        return weiLianDdXcxMapper.queryDjbDataByFormIdList(map);
    }

    @Override
    public PageHelpUtil queryDzWhkList(Map<String, Object> map) {
        List<WlgbJdDjLsjlb> list = weiLianDdXcxMapper.queryDzWhkList(map);
        Integer count = weiLianDdXcxMapper.countDzWhkList(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public PageHelpUtil queryXsDj(Map<String, Object> map) {
        List<WlgbJdDjbbd> list = weiLianDdXcxMapper.queryXsDj(map);
        Integer count = weiLianDdXcxMapper.queryXsDjCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public List<Fwqthbd1cjtcopy1> queryYj(Map<String, Object> map) {
        return weiLianDdXcxMapper.queryYj(map);
    }

    @Override
    public WlgbJmsbb queryJmsBbByVid(String vid) {
        return weiLianDdXcxMapper.queryJmsBbByVid(vid);
    }

    @Override
    public Integer queryNotQx(String userId) {
        return weiLianDdXcxMapper.queryNotQx(userId);
    }

    @Override
    public Integer querySfSqKdj(Map<String, Object> map) {
        return weiLianDdXcxMapper.querySfSqKdj(map);
    }

    @Override
    public void zxKdjHfGc(Map<String, Object> map) {
        weiLianDdXcxMapper.zxKdjHfGc(map);
    }

    @Override
    public Integer queryDdCz(String xid) {
        return weiLianDdXcxMapper.queryDdCz(xid);
    }

    @Override
    public Integer queryByXidDzJlCount(String xid) {
        return weiLianDdXcxMapper.queryByXidDzJlCount(xid);
    }

    @Override
    public void insertDzHfJl(WlgbQrdDzjl wlgbQrdDzjl) {
        weiLianDdXcxMapper.insertDzHfJl(wlgbQrdDzjl);
    }

    @Override
    public List<WlgbQrdDzjl> queryFsHf() {
        return weiLianDdXcxMapper.queryFsHf();
    }

    @Override
    public void updateXgZt(WlgbQrdDzjl wlgbQrdDzjl) {
        weiLianDdXcxMapper.updateXgZt(wlgbQrdDzjl);
    }

    @Override
    public List<WlgbNotVilla> queryGqGl() {
        return weiLianDdXcxMapper.queryGqGl();
    }

    @Override
    public void deleteSc(String vid) {
        weiLianDdXcxMapper.deleteSc(vid);
    }

    @Override
    public List<DkBbJqr> queryLcBbJqr() {
        return weiLianDdXcxMapper.queryLcBbJqr();
    }

    @Override
    public List<String> queryJqrBbAt(String city) {
        return weiLianDdXcxMapper.queryJqrBbAt(city);
    }

    @Override
    public void zxCdf() {
        weiLianDdXcxMapper.zxCdf();
    }
    @Override
    public void zxCdf2(String xddbh) {
        weiLianDdXcxMapper.zxCdf2(xddbh);
    }
    @Override
    public List<JSONObject> queryCdfList() {
        return weiLianDdXcxMapper.queryCdfList();
    }

    @Override
    public PageHelpUtil queryProductByTypeList(Map<String, Object> map) {
        List<Map<String, Object>> list = weiLianDdXcxMapper.queryProductByTypeList(map);
        Integer count = weiLianDdXcxMapper.queryProductByTypeCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public Integer querySpLbCount(String processCode) {
        return weiLianDdXcxMapper.querySpLbCount(processCode);
    }

    @Override
    public List<WlgbHxyhDzjl> queryBdj() {
        return weiLianDdXcxMapper.queryBdj();
    }

    @Override
    public JSONObject queryCnQyByBmId(String bmid) {
        return weiLianDdXcxMapper.queryCnQyByBmId(bmid);
    }

    @Override
    public void zxPyqBbSj() {
        weiLianDdXcxMapper.zxPyqBbSj();
    }

    @Override
    public List<Map<String, Object>> queryBbSjRy() {
        return weiLianDdXcxMapper.queryBbSjRy();
    }

    @Override
    public List<Map<String, Object>> queryBbSjBm() {
        return weiLianDdXcxMapper.queryBbSjBm();
    }

    @Override
    public String queryBsKySjByBsMc(String bsmc) {
        return weiLianDdXcxMapper.queryBsKySjByBsMc(bsmc);
    }

    @Override
    public List<WlgbJdDjbbd> queryDgZzWscJdList() {
        return weiLianDdXcxMapper.queryDgZzWscJdList();
    }

    @Override
    public List<JSONObject> queryYhxQm() {
        return weiLianDdXcxMapper.queryYhxQm();
    }

    @Override
    public void qkJdFyKmTable() {
        weiLianDdXcxMapper.qkJdFyKmTable();
    }

    @Override
    public List<Map<String, Object>> queryHrGzRzFsr() {
        return weiLianDdXcxMapper.queryHrGzRzFsr();
    }

    @Override
    public void zxKdjSj(String start, String end) {
        weiLianDdXcxMapper.zxKdjSj(start, end);
    }

    @Override
    public void zxKdjSjMs(String start, String end) {
        weiLianDdXcxMapper.zxKdjSjMs(start, end);
    }

    @Override
    public List<Map<String, Object>> queryKdjZsZdj() {
        return weiLianDdXcxMapper.queryKdjZsZdj();
    }

    @Override
    public List<Map<String, Object>> queryBsHzbZdj() {
        return weiLianDdXcxMapper.queryBsHzbZdj();
    }

    @Override
    public List<Map<String, Object>> queryRqZzbZdj() {
        return weiLianDdXcxMapper.queryRqZzbZdj();
    }

    @Override
    public WlgbJdDjbbd queryDjJlByLsh(String lsh) {
        return weiLianDdXcxMapper.queryDjJlByLsh(lsh);
    }

    @Override
    public List<Select> queryFyKmSelect() {
        return weiLianDdXcxMapper.queryFyKmSelect();
    }

    @Override
    public void insertCpJl(Map<String, Object> map) {
        weiLianDdXcxMapper.insertCpJl(map);
    }

    @Override
    public PageHelpUtil queryYwJlHfJl(Map<String, Object> map) {
        List<DdhfUtil> list = weiLianDdXcxMapper.queryYwJlHfJl(map);
        Integer count = weiLianDdXcxMapper.queryYwJlHfJlCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public Integer queryYxbTzLb(String lbid) {
        return weiLianDdXcxMapper.queryYxbTzLb(lbid);
    }

    @Override
    public List<String> queryYxBmHrTz() {
        return weiLianDdXcxMapper.queryYxBmHrTz();
    }

    @Override
    public String queryZpbRy() {
        return weiLianDdXcxMapper.queryZpbRy();
    }

    @Override
    public List<Map<String, Object>> queryDtSj() {
        return weiLianDdXcxMapper.queryDtSj();
    }

    @Override
    public List<Map<String, Object>> queryDySj() {
        return weiLianDdXcxMapper.queryDySj();
    }

    @Override
    public List<Map<String, Object>> queryDzSj() {
        return weiLianDdXcxMapper.queryDzSj();
    }

    @Override
    public List<Map<String, Object>> querySzSj() {
        return weiLianDdXcxMapper.querySzSj();
    }

    @Override
    public List<Map<String, Object>> queryDyySj() {
        return weiLianDdXcxMapper.queryDyySj();
    }

    @Override
    public List<Map<String, Object>> queryOldyySj() {
        return weiLianDdXcxMapper.queryOldyySj();
    }

    @Override
    public List<Map<String, Object>> queryDnnSj() {
        return weiLianDdXcxMapper.queryDnnSj();
    }

    @Override
    public List<Map<String, Object>> querySnnSj() {
        return weiLianDdXcxMapper.querySnnSj();
    }

    @Override
    public List<Map<String, Object>> queryYgwyx() {
        return weiLianDdXcxMapper.queryYgwyx();
    }

    @Override
    public List<Map<String, Object>> queryNgwyx() {
        return weiLianDdXcxMapper.queryNgwyx();
    }

    @Override
    public List<Map<String, Object>> queryBmmb() {
        return weiLianDdXcxMapper.queryBmmb();
    }

    @Override
    public List<Map<String, Object>> queryGrmb() {
        return weiLianDdXcxMapper.queryGrmb();
    }

    @Override
    public List<Map<String, Object>> queryYbmmb() {
        return weiLianDdXcxMapper.queryYbmmb();
    }

    @Override
    public List<Map<String, Object>> querySgrmb() {
        return weiLianDdXcxMapper.querySgrmb();
    }

    @Override
    public void insertSc(WlgbNotVilla wlgbNotVilla) {
        weiLianDdXcxMapper.insertSc(wlgbNotVilla);
    }

    @Override
    public Integer queryByVidCount(String vid) {
        return weiLianDdXcxMapper.queryByVidCount(vid);
    }

    @Override
    public void updateSc(WlgbNotVilla wlgbNotVilla) {
        weiLianDdXcxMapper.updateSc(wlgbNotVilla);
    }

    @Override
    public void zxXCdf(String bsxz, String xzkssj, String xzjssj) {
        weiLianDdXcxMapper.zxXCdf(bsxz, xzkssj, xzjssj);
    }

    @Override
    public List<Map<String, Object>> queryDzyXCdf() {
        return weiLianDdXcxMapper.queryDzyXCdf();
    }

    @Override
    public List<WlgbOrderCyjl> queryCyJlByz() {
        return weiLianDdXcxMapper.queryCyJlByz();
    }

    @Override
    public List<String> queryCyJlDzByz() {
        return weiLianDdXcxMapper.queryCyJlDzByz();
    }

    @Override
    public String queryProcessTypeByProcessCode(String processCode) {
        return weiLianDdXcxMapper.queryProcessTypeByProcessCode(processCode);
    }

    @Override
    public List<YjDzVo> queryYjWkZfJlByDdBh(String ddbh) {
        return weiLianDdXcxMapper.queryYjWkZfJlByDdBh(ddbh);
    }

    @Override
    public PageHelpUtil querySjCdfNewJl(Map<String, Object> map) {
        List<SjCdfNewJl> list = weiLianDdXcxMapper.querySjCdfNewJl(map);
        Integer count = weiLianDdXcxMapper.CoutSjCdfNewJlList(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public void insertSjCdfNewJl(SjCdfNewJl sjCdfNewJl) {
        weiLianDdXcxMapper.insertSjCdfNewJl(sjCdfNewJl);
    }

    @Override
    public PageHelpUtil querySpList(Map<String, Object> map) {
        List<JSONObject> list = weiLianDdXcxMapper.querySpList(map);
        Integer count = weiLianDdXcxMapper.querySpListCount(map);
        return new PageHelpUtil(count, list);
    }

}
