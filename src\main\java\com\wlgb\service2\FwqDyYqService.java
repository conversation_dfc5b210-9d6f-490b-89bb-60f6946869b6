package com.wlgb.service2;

import com.wlgb.entity.FwqDyYq;

import java.util.List;

public interface FwqDyYqService {
    void save(FwqDyYq fwqDyPoi);

    List<FwqDyYq> selectAll();

    List<FwqDyYq> selectByOrderid(String orderid);

    FwqDyYq selectByOrderidAndZt(String orderid, String verifytoken, String encryptedcode);

    void deleteByPrimaryKey(FwqDyYq fwqDyPoi);

    void update(FwqDyYq fwqDyYq);
}
