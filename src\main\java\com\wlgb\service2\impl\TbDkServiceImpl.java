package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbDk;
import com.wlgb.mapper.TbDkMapper;
import com.wlgb.service2.TbDkService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/18 15:57
 */
@Service
@DS(value = "second")
public class TbDkServiceImpl implements TbDkService {
    @Resource
    private TbDkMapper tbDkMapper;

    @Override
    public void save(TbDk tbDk) {
        tbDkMapper.insertSelective(tbDk);
    }

    @Override
    public void updateById(TbDk tbDk) {
        tbDkMapper.updateByPrimaryKeySelective(tbDk);
    }
}
