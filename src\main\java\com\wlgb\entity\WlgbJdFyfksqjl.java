package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 费用付款申请记录
 * @Author: jeecg-boot
 * @Date:   2022-04-15
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_fyfksqjl")
public class WlgbJdFyfksqjl {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**审批编号*/
    private java.lang.String spbh;
	/**审批标题*/
    private java.lang.String spbt;
	/**收缴机构（商家名字）*/
    private java.lang.String skjg;
	/**户名（第三方收款账户名称）*/
    private java.lang.String hm;
	/**收款账号*/
    private java.lang.String skzh;
	/**收款开户行*/
    private java.lang.String skkhh;
	/**费用使用方*/
    private java.lang.String fysyf;
	/**支付费用类别*/
    private java.lang.String zffylx;
	/**费用摘要明细*/
    private java.lang.String fyzymx;
	/**费用所属分摊*/
    private java.lang.String fyssft;
	/**是否所有门店都平摊*/
    private java.lang.String sfsymdft;
	/**付款类别*/
    private java.lang.String fklb;
	/**支付截止日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date zfjzrq;
	/**合计金额（元）*/
    private java.lang.Double hjje;
	/**备注*/
    private java.lang.String bz;
    /**申请时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date sqsj;
    /**审批结束时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date spjssj;
    /**申请人*/
    private java.lang.String sqr;
    /**申请人id*/
    private java.lang.String sqrid;
    /**申请人部门*/
    private java.lang.String sqrbm;
    /**申请人部门id*/
    private java.lang.String sqrbmid;
    /**出纳转账时间*/
    private java.lang.String cnzzsj;
    /**出纳转账金额*/
    private java.lang.Double cnzzje;
    /**是否上传金蝶*/
    private java.lang.Integer sflrjd;
    /**审批实例id*/
    private java.lang.String slid;
    /**是否新分摊*/
    private java.lang.Integer sfxft;
}
