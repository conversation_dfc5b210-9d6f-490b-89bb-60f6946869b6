package com.wlgb.service.impl;

import com.wlgb.entity.WlgbEatTcjl;
import com.wlgb.mapper.WlgbEatTcjlMapper;
import com.wlgb.service.WlgbEatTcjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/13 22:19
 */
@Service
public class WlgbEatTcjlServiceImpl implements WlgbEatTcjlService {
    @Resource
    private WlgbEatTcjlMapper wlgbEatTcjlMapper;

    @Override
    public void save(WlgbEatTcjl wlgbEatTcjl) {
        wlgbEatTcjl.setCreateTime(new Date());
        wlgbEatTcjlMapper.insertSelective(wlgbEatTcjl);
    }

    @Override
    public void clearData(String ddbh) {
        WlgbEatTcjl wlgbEatTcjl = new WlgbEatTcjl();
        wlgbEatTcjl.setDdbh(ddbh);
        wlgbEatTcjl.setSfsc(0);
        List<WlgbEatTcjl> list = wlgbEatTcjlMapper.select(wlgbEatTcjl);
        list.forEach(l->{
            WlgbEatTcjl wlgbEatTcjl1 = new WlgbEatTcjl();
            wlgbEatTcjl1.setId(l.getId());
            wlgbEatTcjl1.setSfsc(1);
            wlgbEatTcjl1.setUpdateTime(new Date());
            wlgbEatTcjlMapper.updateByPrimaryKeySelective(wlgbEatTcjl1);
        });
    }
}
