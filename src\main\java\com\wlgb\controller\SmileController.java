package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.tea.TeaException;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.taobao.api.ApiException;
import com.taobao.api.internal.util.StringUtils;
import com.wlgb.config.*;
import com.wlgb.config.oss.OssBootUtil;
import com.wlgb.entity.FwqSmile;
import com.wlgb.service.*;
import com.wlgb.service2.FwqSmileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.LocalTime;
import java.util.*;

import static com.wlgb.config.Tools.isEmpty;
import static com.wlgb.config.oss.OssBootUtil.initOSS;

/**
 * <AUTHOR>
 * @Date 2025/01/30 18:30
 * @Version 1.0
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/smile")
public class SmileController {
    @Autowired
    private FwqSmileService fwqSmileService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private OssFileService ossFileService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;

    /**
     * 查询今天是否已经上传过了
     *
     * @param userid
     * @return
     */
    @RequestMapping(value = "selectJTOne")
    public Result selectJTOne(@RequestParam("userid") String userid) {
        // 检查是否为空
        if (userid.isEmpty()) {
            return Result.error("userid为空");
        }

        // 获取当前时间
        LocalTime now = LocalTime.now();
        LocalTime twelve = LocalTime.of(12, 0);
        LocalTime thirteen = LocalTime.of(13, 0);
        LocalTime eighteen = LocalTime.of(18, 0);

        // 设置 sctime 为今天的时间范围
        Calendar startOfToday = Calendar.getInstance();
        startOfToday.set(Calendar.HOUR_OF_DAY, 0);
        startOfToday.set(Calendar.MINUTE, 0);
        startOfToday.set(Calendar.SECOND, 0);
        startOfToday.set(Calendar.MILLISECOND, 0);
        Calendar endOfToday = Calendar.getInstance();
        endOfToday.set(Calendar.HOUR_OF_DAY, 23);
        endOfToday.set(Calendar.MINUTE, 59);
        endOfToday.set(Calendar.SECOND, 59);
        endOfToday.set(Calendar.MILLISECOND, 999);

        FwqSmile f = new FwqSmile();
        Map<String, Object> map = new HashMap<>();
        map.put("userid", userid);
        map.put("sfyx", "0");
        map.put("endOfToday", endOfToday.getTime());
        map.put("startOfToday", startOfToday.getTime());
        // 判断当前时间是否在允许的查询时间段内
        if (now.isBefore(twelve)) {
            map.put("swxw", "1");
            //上午
            f = fwqSmileService.selectJTOne(map);
        } else if (now.isAfter(thirteen) && now.isBefore(eighteen)) {
            map.put("swxw", "2");
            //下午
            f = fwqSmileService.selectJTOne(map);
        }
        // 即使 service 返回 null，也返回默认空对象
        if (f == null) {
            f = new FwqSmile();
        }
        return Result.OK(f);
    }

    /**
     * 查询某个人所有记录
     *
     * @param userid
     * @return
     */
    @RequestMapping(value = "selectAllByUserid")
    public Result selectAllByUserid(@RequestParam("userid") String userid) {
        // 检查是否为空
        if (userid.isEmpty()) {
            return Result.ok("缺少参数");
        }
        List<FwqSmile> list = fwqSmileService.selectAllByUserid(userid);
        if (!list.isEmpty()) {
            return Result.ok(list);
        }
        return Result.ok("该用户没有上传过自拍数据");
    }

    /**
     * 上传店长打卡照片到oss
     */
    @RequestMapping(value = "uploadtptooss")
    public Result uploadtptooss(@RequestParam("file") MultipartFile file, @RequestParam("userid") String userid) throws IOException {
        // 文件校验
        if (file == null || file.isEmpty()) {
            return Result.error("文件为空");
        }

        // 获取用户信息
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        if (dingdingEmployee == null) {
            return Result.error("页面参数丢失，请从钉钉重新打开");
        }
        if (isEmpty(dingdingEmployee.getName())) {
            return Result.error("您不在钉钉组织架构内，不能使用本功能");
        }

        // 判断当前时间是否允许上传
        LocalTime now = LocalTime.now();
        Map<String, String> timeStatus;
        try {
            timeStatus = determineTimeStatus(now);
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        }

        String swxw = timeStatus.get("swxw");
        String pzzt = timeStatus.get("pzzt");

        // 上传图片至 OSS
        String uploadUrl = ossFileService.uploadSmile(file);
        String bucketName = OssBootUtil.getBucketName();
        String key = uploadUrl.replaceFirst("http://jiuyun2.qianquan888.com/", "");

        // 获取图像表情数据
        String imageFaces;
        try {
            imageFaces = getsmilefenshu(bucketName, key);
        } catch (Exception e) {
            log.error("获取笑容值失败", e);
            return Result.error("服务器异常，无法识别表情");
        }

        JSONObject parsedObject = JSON.parseObject(imageFaces);
        JSONArray figuresjson = parsedObject.getJSONArray("Figures");

        if (figuresjson == null || figuresjson.isEmpty()) {
            return Result.error("未检测到表情信息，请重新拍照");
        }

        // 处理第一个表情结果
        JSONObject datajson = figuresjson.getJSONObject(0);
        String emotion = datajson.getString("Emotion");
        float emotionConfidence = datajson.getFloat("EmotionConfidence");

        FwqSmile fwqSmile = new FwqSmile();
        fwqSmile.setUrl(uploadUrl);
        fwqSmile.setSctime(new Date());
        fwqSmile.setUserid(userid);
        fwqSmile.setUsername(dingdingEmployee.getName());
        fwqSmile.setEmotion(emotion);
        fwqSmile.setEmotionconfidence(String.valueOf(emotionConfidence));
        fwqSmile.setSwxw(swxw);
        fwqSmile.setPzzt(pzzt);

        // 判断是否满足上传条件
        if ("happiness".equals(emotion)) {
            if (emotionConfidence > 0.79) {
                fwqSmile.setSfyx("0");
                fwqSmileService.save(fwqSmile);
                return Result.OK(fwqSmile);
            } else {
                fwqSmile.setSfyx("1");
                fwqSmileService.save(fwqSmile);
                return Result.error("分数小于80分，请重新拍照");
            }
        } else {
            fwqSmile.setSfyx("1");
            fwqSmileService.save(fwqSmile);
            return Result.error("没有笑容值，请重新拍照");
        }
    }

    /**
     * 判断当前时间是否允许上传，并返回时间段和状态
     *
     * @param now 当前时间
     * @return 包含 swxw 和 pzzt 的 map
     */
    private Map<String, String> determineTimeStatus(LocalTime now) {
        if (now == null) {
            throw new IllegalArgumentException("时间参数不能为空");
        }

        // 定义常用时间点
        LocalTime seven = LocalTime.of(7, 0);
        LocalTime eightThirty = LocalTime.of(8, 30);
        LocalTime eightFortyFive = LocalTime.of(8, 45);
        LocalTime twelve = LocalTime.of(12, 0);
        LocalTime thirteen = LocalTime.of(13, 0);
        LocalTime fourteen = LocalTime.of(14, 0);
        LocalTime fourteenFifteen = LocalTime.of(14, 15);
        LocalTime eighteen = LocalTime.of(18, 0);

        String swxw;
        String pzzt;
        if (isBetween(now, seven, eightThirty)) {
            pzzt = "正常";
            swxw = "1";
        } else if (isBetween(now, eightThirty, eightFortyFive)) {
            pzzt = "状态1";
            swxw = "1";
        } else if (isBetween(now, eightFortyFive, twelve)) {
            pzzt = "状态2";
            swxw = "1";
        } else if (isBetween(now, thirteen, fourteen)) {
            pzzt = "正常";
            swxw = "2";
        } else if (isBetween(now, fourteen, fourteenFifteen)) {
            pzzt = "状态1";
            swxw = "2";
        } else if (isBetween(now, fourteenFifteen, eighteen)) {
            pzzt = "状态2";
            swxw = "2";
        } else {
            throw new IllegalArgumentException("当前时间不可以上传拍照");
        }

        Map<String, String> result = new HashMap<>();
        result.put("swxw", swxw);
        result.put("pzzt", pzzt);
        return result;
    }

    /**
     * 辅助方法：判断时间是否在 [start, end) 区间内
     *
     * @param time
     * @param start
     * @param end
     * @return
     */
    private boolean isBetween(LocalTime time, LocalTime start, LocalTime end) {
        return !time.isBefore(start) && time.isBefore(end);
    }

    /**
     * 计算图片的笑容值
     *
     * @param bucketName
     * @param key
     * @return
     * @throws IOException
     */
    public String getsmilefenshu(String bucketName, String key) throws IOException {
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, key);
        getObjectRequest.setProcess("image/faces");

        OSS ossClient = null;
        OSSObject ossObject = null;
        try {
            ossClient = OssBootUtil.initOSS(OssBootUtil.getEndPoint(), OssBootUtil.getAccessKeyId(), OssBootUtil.getAccessKeySecret());
            ossObject = ossClient.getObject(getObjectRequest);

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = ossObject.getObjectContent().read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            String imageFaces = baos.toString("UTF-8");
            System.out.println("Image Faces:");
            System.out.println(imageFaces);
            return imageFaces;
        } finally {
            if (ossObject != null) {
                ossObject.close();
            }
        }
    }

    ////////////////////////////// 下面是关于钉钉审批的接口/////////////////////////////////////////

    public String token() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        Client client = new Client(config);
        com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest getAccessTokenRequest = new com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest().setAppKey("dingjrnvifk8bmoyzaai").setAppSecret("3LGENGTZ3_6dX52-37BihNYt1WxYcq2OJyuSBJ3naAfhw0-zBc2vpIKA_DfjU2aI");
        GetAccessTokenResponse gtoken = new GetAccessTokenResponse();
        try {
            gtoken = client.getAccessToken(getAccessTokenRequest);
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }
        return gtoken.getBody().getAccessToken();
    }

    /**
     * 批量获取考勤组详情
     *
     * @return
     * @throws ApiException
     */
    @RequestMapping(value = "plhqkqz")
    public Result plhqkqz() throws Exception {
        String token = token();
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/getsimplegroups");
        OapiAttendanceGetsimplegroupsRequest req = new OapiAttendanceGetsimplegroupsRequest();
        req.setOffset(0L);
        req.setSize(10L);
        OapiAttendanceGetsimplegroupsResponse rsp = client.execute(req, token);
        String code = rsp.getErrorCode();
        if ("0".equals(code)) {
            List<OapiAttendanceGetsimplegroupsResponse.AtGroupForTopVo> llg = rsp.getResult().getGroups();
            for (OapiAttendanceGetsimplegroupsResponse.AtGroupForTopVo aa : llg) {
                log.info("**********批量获取考勤组详情******{}", aa);
                // 这个是一线考勤的考勤组ID
                if (aa.getGroupId() == 41900014) {
                    List<String> lll = getKqzxq(aa.getGroupId(), token);

                    String lllresult = String.join(",", lll);

                    // getQjZt(lllresult,)
                }
            }
        }
        return Result.OK(rsp.getBody());
    }

    /**
     * 获取参与考勤人员
     *
     * @return
     * @throws ApiException
     */
    public List<String> getKqzxq(Long groupId, String token) throws ApiException {
        List<String> lll = new ArrayList<>();
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/group/memberusers/list");
        OapiAttendanceGroupMemberusersListRequest req = new OapiAttendanceGroupMemberusersListRequest();
        req.setCursor(0L);
        req.setOpUserId("15349026426046931");
        req.setGroupId(groupId);
        OapiAttendanceGroupMemberusersListResponse rsp = client.execute(req, token);
        System.out.println(rsp.getBody());
        boolean ss = rsp.getSuccess();
        if (ss) {
            lll = rsp.getResult().getResult();
            // 是否还有更多数据 true:有 false:没有
            boolean bb = rsp.getResult().getHasMore();
            if (bb) {
                // 分页获取下一次请求的起始位置。
                Long cursor = rsp.getResult().getCursor();
                req.setCursor(cursor);
                req.setOpUserId("15349026426046931");
                req.setGroupId(groupId);
                rsp = client.execute(req, token);
                ss = rsp.getSuccess();
                if (ss) {
                    lll.addAll(rsp.getResult().getResult());
                }
            }
            return lll;
        }
        return lll;
    }

    /**
     * 获取考勤报表列定义
     *
     * @throws Exception
     */
    @RequestMapping(value = "attendanceColumns")
    public void attendanceColumns() throws Exception {
        String token = token();
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/getattcolumns");
        OapiAttendanceGetattcolumnsRequest req = new OapiAttendanceGetattcolumnsRequest();
        OapiAttendanceGetattcolumnsResponse rsp = client.execute(req, token);
        System.out.println(rsp.getBody());
        log.info("**********所有的列-----------{}", rsp.getBody());
        attendanceColumnsValue(token);
    }

    public void attendanceColumnsValue(String token) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/getcolumnval");
        OapiAttendanceGetcolumnvalRequest req = new OapiAttendanceGetcolumnvalRequest();
        req.setUserid("17393443080413415");
        req.setColumnIdList("8424201,7827197,40305004,6752059,6752057");
        req.setFromDate(StringUtils.parseDateTime("2025-02-01 00:00:00"));
        req.setToDate(StringUtils.parseDateTime("2025-03-01 00:00:00"));
        OapiAttendanceGetcolumnvalResponse rsp = client.execute(req, token);
        System.out.println(rsp.getBody());
    }

}
