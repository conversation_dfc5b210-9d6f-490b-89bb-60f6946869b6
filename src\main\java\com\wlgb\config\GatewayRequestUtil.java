package com.wlgb.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.GetClient;
import com.alibaba.xxpt.gateway.shared.client.http.PostClient;
import org.springframework.util.CollectionUtils;

import java.util.Map;

/**
 * 宜搭网关服务请求工具
 *
 * @Author: weimeng(shanyu)
 * @Date: 2019/2/28 下午2:50
 */

public class GatewayRequestUtil {
    /**
     * 请求网关
     *
     * @param param 请求参数
     * @param url   网关地址
     * @return 请求结果
     */
    public static GatewayResult baseRequest(Map<String, String> param, String url) {
        try {
            ExecutableClient e = ExecutableClient.getInstance();
            PostClient postClient = e.newPostClient(url);
            if (!CollectionUtils.isEmpty(param)) {
                for (Map.Entry<String, String> entry : param.entrySet()) {
                    postClient.addParameter(entry.getKey(), entry.getValue());
                }
            }

            String result = postClient.post();
            return JSON.parseObject(result, GatewayResult.class);
        } catch (Throwable e) {
            e.printStackTrace();
            GatewayResult result = new GatewayResult();
            result.setSuccess(false);
            return result;
        }
    }

    /**
     * 请求网关
     *
     * @param param 请求参数
     * @param url   网关地址
     * @return 请求结果
     */
    public static GatewayResult baseRequestGet(Map<String, String> param, String url) {
        try {
            ExecutableClient e = ExecutableClient.getInstance();
            GetClient getClient = e.newGetClient(url);
//            PostClient postClient = e.newPostClient(url);
            if (!CollectionUtils.isEmpty(param)) {
                for (Map.Entry<String, String> entry : param.entrySet()) {
                    getClient.addParameter(entry.getKey(), entry.getValue());
                }
            }

            String result = getClient.get();
            return JSON.parseObject(result, GatewayResult.class);
        } catch (Throwable e) {
            e.printStackTrace();
            GatewayResult result = new GatewayResult();
            result.setSuccess(false);
            return result;
        }
    }
}