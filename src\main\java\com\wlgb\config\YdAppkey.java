package com.wlgb.config;


import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class YdAppkey {

	/**主键*/
    private String id;
	/**创建人*/
    private String createBy;
	/**创建日期*/
    private java.util.Date createTime;
	/**更新人*/
    private String updateBy;
	/**更新日期*/
    private java.util.Date updateTime;
	/**所属部门*/
    private String sysOrgCode;
	/**应用编码*/
    private String appkey;
	/**应用密钥*/
    private String token;
	/**备注*/
    private String bz;
}
