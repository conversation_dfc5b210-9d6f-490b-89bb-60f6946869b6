package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.config.DingdingEmployee;
import com.wlgb.config.Dingkey;
import com.wlgb.config.PageHelpUtil;
import com.wlgb.entity.*;
import com.wlgb.mapper1.DhRjMapper;
import com.wlgb.service4.DhRjService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 12:06
 */
@Service
@DS("fourth")
public class DhRjServiceImpl implements DhRjService {
    @Resource
    private DhRjMapper dhRjMapper;

    @Override
    public Integer queryCountDhRjXydSfCz(DhrjXyd dhrjXyd) {
        return dhRjMapper.queryCountDhRjXydSfCz(dhrjXyd);
    }

    @Override
    public DingdingEmployee queryDhRjDingDingEmployeeByUserId(String userId) {
        return dhRjMapper.queryDhRjDingDingEmployeeByUserId(userId);
    }

    @Override
    public DingdingEmployee queryDhRjDingDingEmployeeByName(String name) {
        return dhRjMapper.queryDhRjDingDingEmployeeByName(name);
    }

    @Override
    public Integer queryDhRjXydMothCountByXfd(String xfd) {
        return dhRjMapper.queryDhRjXydMothCountByXfd(xfd);
    }

    @Override
    public Integer queryDhRjXydDateCountByXfd(String xfd) {
        return dhRjMapper.queryDhRjXydDateCountByXfd(xfd);
    }

    @Override
    public Integer queryDhRjXydYesCountByXfd(String xfd) {
        return dhRjMapper.queryDhRjXydYesCountByXfd(xfd);
    }

    @Override
    public Double queryDhRjXdYjByXfd(String xfd) {
        return dhRjMapper.queryDhRjXdYjByXfd(xfd);
    }

    @Override
    public List<CrmCs> queryCrmCsList() {
        return dhRjMapper.queryCrmCsList();
    }

    @Override
    public List<CrmKhzt> queryCrmKhZtList(Integer lxid) {
        return dhRjMapper.queryCrmKhZtList(lxid);
    }

    @Override
    public Integer queryCrmDhSfcz(Map<String, Object> map) {
        return dhRjMapper.queryCrmDhSfcz(map);
    }

    @Override
    public Integer queryCrmWxhCount(String qkhwxh) {
        return dhRjMapper.queryCrmWxhCount(qkhwxh);
    }

    @Override
    public Integer queryCrmHmCount(String dhhm) {
        return dhRjMapper.queryCrmHmCount(dhhm);
    }

    @Override
    public List<CrmCsfq> queryCrmCsSfqByCsName(String csName) {
        return dhRjMapper.queryCrmCsSfqByCsName(csName);
    }

    @Override
    public String queryCrmBbh() {
        return dhRjMapper.queryCrmBbh();
    }

    @Override
    public List<CrmQbkh> queryCrmDataImport(Map<String, Object> map) {
        return dhRjMapper.queryCrmDataImport(map);
    }

    @Override
    public PageHelpUtil queryCrmData(Map<String, Object> map) {
        List<CrmQbkh> list = dhRjMapper.queryCrmDataImport(map);
        Integer count = dhRjMapper.queryCrmDataImportCount(map);
        return new PageHelpUtil(count, list);
    }

    @Override
    public Integer queryCmrCsFqIdByFqName(String fqName) {
        return dhRjMapper.queryCmrCsFqIdByFqName(fqName);
    }

    @Override
    public List<CrmBmxq> queryCrmBmCxByBmDa(String bmDa) {
        return dhRjMapper.queryCrmBmCxByBmDa(bmDa);
    }

    @Override
    public List<CrmQbkh> queryCrmCcSjByCcData(String cc) {
        return dhRjMapper.queryCrmCcSjByCcData(cc);
    }

    @Override
    public List<CrmCsfq> queryCrmCsFqList() {
        return dhRjMapper.queryCrmCsFqList();
    }

    @Override
    public Dingkey queryDingKeyById(String id) {
        return dhRjMapper.queryDingKeyById(id);
    }

    @Override
    public DingDingToken queryDingDingTokenByName(String name) {
        return dhRjMapper.queryDingDingTokenByName(name);
    }

    @Override
    public List<CrmUser> queryCrmUserList() {
        return dhRjMapper.queryCrmUserList();
    }

    @Override
    public void qkCrmBmXqTable() {
        dhRjMapper.qkCrmBmXqTable();
    }

    @Override
    public void qkCrmBmTable() {
        dhRjMapper.qkCrmBmTable();
    }

    @Override
    public void qkCrmUserBackupsTable() {
        dhRjMapper.qkCrmUserBackupsTable();
    }

    @Override
    public void bfCrmUserInBackups() {
        dhRjMapper.bfCrmUserInBackups();
    }

    @Override
    public void qkCrmUserCopyTable() {
        dhRjMapper.qkCrmUserCopyTable();
    }

    @Override
    public void saveCrmUserCopy(CrmUser crmUser) {
        dhRjMapper.saveCrmUserCopy(crmUser);
    }

    @Override
    public void delCrmUserCopy() {
        dhRjMapper.delCrmUserCopy();
    }

    @Override
    public void qkCrmUserTable() {
        dhRjMapper.qkCrmUserTable();
    }

    @Override
    public void saveCrmUser() {
        dhRjMapper.saveCrmUser();
    }
}
