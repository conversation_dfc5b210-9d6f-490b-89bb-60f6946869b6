package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmBmxq;
import com.wlgb.mapper.CrmBmxqMapper;
import com.wlgb.service2.CrmBmxqService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/16 17:34
 */
@Service
@DS(value = "second")
public class CrmBmxqServiceImpl implements CrmBmxqService {
    @Resource
    private CrmBmxqMapper crmBmxqMapper;

    @Override
    public List<CrmBmxq> select(CrmBmxq crmBmxq) {
        return crmBmxqMapper.select(crmBmxq);
    }

    @Override
    public void save(CrmBmxq crmBmxq) {
        crmBmxqMapper.insertSelective(crmBmxq);
    }

    @Override
    public void update(CrmBmxq crmBmxq) {
        crmBmxqMapper.updateByPrimaryKey(crmBmxq);
    }
}
