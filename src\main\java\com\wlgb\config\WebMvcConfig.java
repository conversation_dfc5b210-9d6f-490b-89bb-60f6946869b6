//package com.wlgb.config;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.cors.CorsConfiguration;
//import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
//import org.springframework.web.filter.CorsFilter;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import java.util.ArrayList;
//import java.util.List;
//
////springboot 2.4以上的方式
//@Configuration
//public class WebMvcConfig implements WebMvcConfigurer {
//
//    private CorsConfiguration buildConfig() {
//        CorsConfiguration corsConfiguration = new CorsConfiguration();
//        //corsConfiguration.addAllowedOrigin("*");
//        // 跨域配置报错，将.allowedOrigins替换成.allowedOriginPatterns即可。
//        corsConfiguration.addAllowedOriginPattern("*");
//        corsConfiguration.addAllowedHeader("*");
//        corsConfiguration.addAllowedMethod("*");
//        corsConfiguration.setAllowCredentials(true);
//        return corsConfiguration;
//    }
//
//    @Bean
//    public CorsFilter corsFilter() {
//        System.out.println("开始跨域了");
//        CorsConfiguration config = new CorsConfiguration();
//        // 设置访问源地址
//        config.addAllowedOriginPattern("*");
//        // 允许访问的客户端域名
//        // (springboot2.4以上的加入这一段可解决 allowedOrigins cannot contain the special value "*"问题)
//        List<String> allowedOriginPatterns = new ArrayList<>();
//        allowedOriginPatterns.add("*");
//        config.setAllowedOriginPatterns(allowedOriginPatterns);
//        // 设置访问源请求头
//        config.addAllowedHeader("*");
//        // 设置访问源请求方法
//        config.addAllowedMethod("*");
//        //允许跨越发送cookie
////        config.setAllowCredentials(true);
//        // 有效期 1800秒
//        config.setMaxAge(1800L);
//        // 添加映射路径，拦截一切请求
//        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
//        source.registerCorsConfiguration("/**", config);
//        // 返回新的CorsFilter
//        return new CorsFilter(source);
//    }
//}
