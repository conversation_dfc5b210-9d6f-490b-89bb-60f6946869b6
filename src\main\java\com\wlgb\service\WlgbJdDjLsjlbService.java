package com.wlgb.service;

import com.alibaba.fastjson.JSONObject;
import com.wlgb.entity.TbXyd;
import com.wlgb.entity.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/12/11 19:56
 * @Version 1.0
 */
public interface WlgbJdDjLsjlbService {

    void save(WlgbJdDjLsjlb wlgbJdDjLsjlb);

    WlgbJdDjLsjlb queryBySkBhAndSfSc(String skbh, Integer sfsc);

    List<WlgbHxyhDzjl> queryDzWtb();

    void updateById(WlgbJdDjLsjlb wlgbJdDjLsjlb);

    List<Map<String, Object>> queryXdWxgZt();

    TbXyd queryXydByDdBh(String ddbh);

    Integer queryQrdSfCz(String qxydid);

    Double queryQrdGsSr(String qxydid);

    List<WlgbMtLog> queryXsDjWlr();

    String queryYqJl(String qm);

    JSONObject queryMtMd(WlgbMtLog wlgbMtLog);

    WlgbHxyhDzjl queryXxDjBySkBh(String skbh);

    WlgbMtLog queryXsDjByQh(String yqqm);

    List<WlgbJdDjbbd> queryDjYxdWxgByLsh(String lsh);

    void updateCrmXxZt(String crmbh);

    void updateBatchById(List<WlgbJdDjLsjlb> list);

    List<WlgbJdDjLsjlb> queryListByKhDhAndSfSc(String khdh, Integer sfsc);

    List<WlgbJdDjLsjlb> queryListByKhDhAndSfScAndSfDz(String khdh, Integer sfsc, Integer sfDz,Integer yxq);

    List<WlgbJdDjLsjlb> queryListByCrmBhAndSfScAndSfDz(String crmBh, Integer sfSc, Integer sfDz,String uuid);

    List<WlgbJdDjLsjlb> queryListByWlgbJdDjLsjlb(WlgbJdDjLsjlb wlgbJdDjLsjlb);

    WlgbJdDjLsjlb queryByLshAndSfScAndDjLxAndSfDz(String lsh, Integer sfsc, Integer djLx, Integer sfDz);

}
