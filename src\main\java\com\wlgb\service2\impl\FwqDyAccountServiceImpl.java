package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqDyAccount;
import com.wlgb.mapper.FwqDyAccountMapper;
import com.wlgb.service2.FwqDyAccountService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@DS(value = "second")
public class FwqDyAccountServiceImpl implements FwqDyAccountService {
    @Resource
    private FwqDyAccountMapper fwqDyAccountMapper;

    @Override
    public void save(FwqDyAccount fwqDyAccount) {
        fwqDyAccountMapper.insertSelective(fwqDyAccount);
    }

    @Override
    public List<FwqDyAccount> selectByAccountid(String accountid) {
        FwqDyAccount fwqDyAccount = new FwqDyAccount();
        fwqDyAccount.setAccountid(accountid);
        return fwqDyAccountMapper.select(fwqDyAccount);
    }

    @Override
    public List<FwqDyAccount> selectAll() {
        List<FwqDyAccount> ll = fwqDyAccountMapper.selectAll();
        return ll;
    }

    @Override
    public void deleteByPrimaryKey(FwqDyAccount fwqDyAccount) {
        fwqDyAccountMapper.deleteByPrimaryKey(fwqDyAccount);
    }
}
