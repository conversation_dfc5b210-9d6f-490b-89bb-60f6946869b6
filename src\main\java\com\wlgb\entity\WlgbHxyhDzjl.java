package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Date 2022/12/11 21:11
 * @Version 1.0
 */
@Data
@Table(name = "wlgb_hxyh_dzjl")
public class WlgbHxyhDzjl {
    /**主键*/
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
    /**创建人*/
    private java.lang.String createBy;
    /**创建日期*/
    private java.util.Date createTime;
    /**更新人*/
    private java.lang.String updateBy;
    /**更新日期*/
    private java.util.Date updateTime;
    /**所属部门*/
    private java.lang.String sysOrgCode;
    /**未知*/
    private java.lang.String totalDiscount;
    /**入账的时间，格式 "yyyyMMdd"*/
    private java.lang.String accountDate;
    /**该笔订单的手续费总 和(参考)，单位为 RMB-Yuan。取值范围 为 [0.01 ， *********.00]，精确 到小数点后两位。*/
    private java.lang.Double fee;
    /**签名字符串，Base64 编码*/
    private java.lang.String sign;
    /**未知*/
    private java.lang.String isDiscount;
    /**通知类型*/
    private java.lang.String notifyType;
    /**该笔订单的合作方手 续费(参考)，单位为 RMB-Yuan。取值范围 为 [0.01 ， *********.00]，精确 到小数点后两位。*/
    private java.lang.Double partnerFee;
    /**交易目前所处的状 态。成功状态的值： TRADE_SUCCESS|TRAD E_CLOSED 等具体详 情看下文中的交易状 态详解*/
    private java.lang.String tradeStatus;
    /**未知*/
    private java.lang.String totalDiscountFee;
    /**签名类型*/
    private java.lang.String signType;
    /**发送请求的时间，格 式 "yyyy-MM-dd HH:mm:ss"*/
    private java.lang.String notifyTime;
    /**该笔订单的收款方手 续费(参考)，单位为 RMB-Yuan。取值范围 为 [0.01 ， *********.00]，精确 到小数点后两位。*/
    private java.lang.Double payeeFee;
    /**公用回传参数*/
    private java.lang.String extraCommonParam;
    /**未知*/
    private java.lang.String cardType;
    /**支付宝用户 Uid*/
    private java.lang.String buyerUserId;
    /**银盛支付合作商户网 站唯一订单号。*/
    private java.lang.String outTradeNo;
    /**该笔订单的资金总 额 ， 单 位 为 RMB-Yuan。取值范围 为 [0.01 ， *********.00]，精确 到小数点后两位。*/
    private java.lang.Double totalAmount;
    /**该交易在银盛支付系 统中的交易流水号。*/
    private java.lang.String tradeNo;
    /**结算金额*/
    private java.lang.Double settlementAmount;
    /**支付网关编号*/
    private java.lang.String paygateNo;
    /**支付宝账户*/
    private java.lang.String buyerLogonId;
    /**该笔订单的付款方手 续费(参考)，单位为 RMB-Yuan。取值范围 为 [0.01 ， *********.00]，精确 到小数点后两位。*/
    private java.lang.String payerFee;
    /**渠道返回流水号*/
    private java.lang.String channelRecvSn;
    /**发往渠道流水号*/
    private java.lang.String channelSendSn;
    /**下单订单编号*/
    private java.lang.String ddbh;
    /**是否录入金蝶*/
    private java.lang.Integer sflrjd;
}
