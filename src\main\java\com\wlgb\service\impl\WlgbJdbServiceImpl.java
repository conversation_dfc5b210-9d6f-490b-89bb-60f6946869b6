package com.wlgb.service.impl;

import com.wlgb.entity.WlgbJdb;
import com.wlgb.mapper.WlgbJdbMapper;
import com.wlgb.service.WlgbJdbService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/16 14:12
 */
@Service
public class WlgbJdbServiceImpl implements WlgbJdbService {
    @Resource
    private WlgbJdbMapper wlgbJdbMapper;

    @Override
    public void save(WlgbJdb wlgbJdb) {
        wlgbJdbMapper.insertSelective(wlgbJdb);
    }

    @Override
    public void updateById(WlgbJdb wlgbJdb) {
        wlgbJdbMapper.updateByPrimaryKeySelective(wlgbJdb);
    }

    @Override
    public WlgbJdb queryByXid(String xid) {
        Example example = new Example(WlgbJdb.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xid", xid);
        return wlgbJdbMapper.selectOneByExample(example);
    }
}
