package com.wlgb.config;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月13日 23:37
 */
public class DateFormatConfig {
    /**将时间转换年月日时分秒字符串yyyy-MM-dd HH:mm:ss*/
    public static String df1(Object date){
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df2.format(date);
    }
    /**将时间转换年月日时分秒字符串yyyy-MM-dd HH:mm*/
    public static String df3(Object date){
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return df2.format(date);
    }
    /**将时间转换年月日字符串yyyy-MM-dd*/
    public static String df2(Object date){
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
        return df2.format(date);
    }
    /**将时间转换年月日字符串yyyyMMddHHmmssSSS*/
    public static String dfSjc(){
        SimpleDateFormat df2 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return df2.format(new Date());
    }
}
