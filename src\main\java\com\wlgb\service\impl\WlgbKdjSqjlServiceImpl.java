package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbKdjSqjl;
import com.wlgb.mapper.WlgbKdjSqjlMapper;
import com.wlgb.service.WlgbKdjSqjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27 17:34
 */
@Service
public class WlgbKdjSqjlServiceImpl implements WlgbKdjSqjlService {
    @Resource
    private WlgbKdjSqjlMapper wlgbKdjSqjlMapper;

    @Override
    public void save(WlgbKdjSqjl wlgbKdjSqjl) {
        wlgbKdjSqjl.setId(IdConfig.uuId());
        wlgbKdjSqjl.setCreateTime(new Date());
        wlgbKdjSqjlMapper.insertSelective(wlgbKdjSqjl);
    }
}
