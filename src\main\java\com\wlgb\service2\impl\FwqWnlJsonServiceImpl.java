package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqWnl;
import com.wlgb.entity.FwqWnlJson;
import com.wlgb.mapper.FwqWnlJsonMapper;
import com.wlgb.mapper.FwqWnlMapper;
import com.wlgb.mapper1.WeiLianMapper;
import com.wlgb.service2.FwqWnlJsonService;
import com.wlgb.service2.FwqWnlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@DS(value = "second")
public class FwqWnlJsonServiceImpl implements FwqWnlJsonService {
    @Resource
    private FwqWnlJsonMapper fwqWnlJsonMapper;

    @Override
    public void save(FwqWnlJson fwqWnlJson) {
        fwqWnlJsonMapper.insertSelective(fwqWnlJson);
    }

    @Override
    public FwqWnlJson selectOne(FwqWnlJson fwqWnlJson) {
        return fwqWnlJsonMapper.selectOne(fwqWnlJson);
    }

    @Override
    public List<FwqWnlJson> selectAll() {
        return fwqWnlJsonMapper.selectAll();
    }

}
