package com.wlgb.jobs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.openapi.sdk.api.customerauth.session.entity.CustomerKeyShopScopeResponse;
import com.dianping.openapi.sdk.api.customerauth.session.entity.CustomerKeyShopScopeResponseEntity;
import com.dianping.openapi.sdk.api.oauth.entity.CustomerRefreshTokenResponse;
import com.dianping.openapi.sdk.api.oauth.entity.DynamicTokenResponse;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.OapiDepartmentListParentDeptsResponse;
import com.dingtalk.api.response.OapiDepartmentListResponse;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiUserListbypageResponse;
import com.kingdee.bos.webapi.sdk.K3CloudApi;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.FwqBbb;
import com.wlgb.entity.vo.WlgbJmsbb;
import com.wlgb.entity.vo.WlgbNotVilla;
import com.wlgb.entity.vo.WlgbQrdDzjl;
import com.wlgb.service.*;
import com.wlgb.service2.*;
import com.wlgb.service3.FormitemService;
import com.wlgb.service3.OapiworkrecordService;
import com.wlgb.service3.WeiLianDaiBanService;
import com.wlgb.service3.WlgbDbwcwService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.util.*;

import static java.lang.Thread.sleep;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/30 15:05
 */
@Component
@RestController
@RequestMapping("/wlgb/job")
@Slf4j
public class Jobs {

    @Autowired
    private WlgbYlxdService wlgbYlxdService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WeiLianDaiBanService weiLianDaiBanService;
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private WlgbMtTokenService wlgbMtTokenService;
    @Autowired
    private TbYddXydService tbYddXydService;
    @Autowired
    private WlgbDksljlService wlgbDksljlService;
    @Autowired
    private WlgbBdjlService wlgbBdjlService;
    @Autowired
    private WlgbMtMdService wlgbMtMdService;
    @Autowired
    private WlgbDdhfService wlgbDdhfService;
    @Autowired
    private OapiworkrecordService oapiworkrecordService;
    @Autowired
    private FormitemService formitemService;
    @Autowired
    private OssFileService ossFileService;
    @Autowired
    private TbQrdService tbQrdService;
    @Autowired
    private FwqBbbService fwqBbbService;
    @Autowired
    private CrmRyxxService crmRyxxService;
    @Autowired
    private WlgbDbwcwService wlgbDbwcwService;
    @Autowired
    private CrmBmxqService crmBmxqService;
    @Autowired
    private CrmBmService crmBmService;
    @Autowired
    private WlgbJdYsdCdfjlService wlgbJdYsdCdfjlService;
    @Autowired
    private WlgbJdDjbbdService wlgbJdDjbbdService;
    @Autowired
    private WlgbJdYhkService wlgbJdYhkService;
    @Autowired
    private WlgbJdFykmService wlgbJdFykmService;
    @Autowired
    private WlgbJdYxcgsqjlService wlgbJdYxcgsqjlService;
    @Autowired
    private WlgbJdSpCwlrjdsjService wlgbJdSpCwlrjdsjService;
    @Autowired
    private WlgbJdBsmcXgjlService wlgbJdBsmcXgjlService;


    /**
     * {"key":"scYld","bz":"定时删除预留单，一般是两小时删除一次，这里是20分钟执行一次"}
     */
    @RequestMapping(value = "scYld")
    public Result scYld() {
        System.out.println("开始删除预留单");
        List<WlgbYlxd> list = wlgbYlxdService.list();
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        String finalToken = token;
        list.forEach(l -> {
            if ((System.currentTimeMillis()) - l.getYtitime().getTime() > (1000 * 2 * 60 * 60)) {
                GatewayResult gatewayResult = new GatewayResult();
                try {
                    gatewayResult = DingBdLcConfig.scBdSl(finalToken, ydAppkey, "012412221639786136545", l.getSlid());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                System.out.println("删除预留单：" + gatewayResult.toString());
            }
        });
        return Result.OK();
    }


    /**
     * 每天晚上12点半美团token是否过期，过期将刷新token（调用的是北极星session刷新接口）
     */
    @RequestMapping(value = "mtToken")
    public void mtToken() {
        List<WlgbMtToken> list = wlgbMtTokenService.queryAllList();
        list.forEach(l -> {
            Date date = new Date();
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
            String format = df2.format(date);
            String format1 = df2.format(l.getEndtime());
            if (format.equals(format1)) {
                CustomerRefreshTokenResponse response = MtConfig.sxToken(l.getAppkey(), l.getAppsecret(), l.getRefreshToken());
                if (response.getCode() == 200) {
                    WlgbMtToken wlgbMtToken = new WlgbMtToken();
                    wlgbMtToken.setId(l.getId());
                    wlgbMtToken.setBid(response.getBid());
                    wlgbMtToken.setToken(response.getAccess_token());
                    wlgbMtToken.setRefreshToken(response.getRefresh_token());
                    Date date1 = new Date();
                    Calendar nowTime = Calendar.getInstance();
                    nowTime.setTime(date1);
                    nowTime.add(Calendar.SECOND, Integer.parseInt(String.valueOf(response.getExpires_in())));
                    wlgbMtToken.setEndtime(nowTime.getTime());
                    wlgbMtToken.setRemainRefreshCount(response.getRemain_refresh_count());
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    System.out.println(sdf.format(nowTime.getTime()));
                    wlgbMtTokenService.updateById(wlgbMtToken);
                }
            }
        });
    }

    @RequestMapping(value = "getTokenList")
    public Result getTokenList() {
        List<WlgbMtToken> list = wlgbMtTokenService.queryAllListById();
        return Result.OK(list);
    }

    /**
     * session换取接口
     * 1.先登录https://developer.meituan.com/platform/bjx#/application/console/overview?appkey=c23e31427e8db956，
     * 2.然后点击商家授权 ：授权地址（需要拼接）：https://e.dianping.com/dz-open/merchant/auth?app_key=c23e31427e8db956&redirect_url=https://wlgb.qianquan888.com/weilian-dingdingdzxcx/wlgb/yd/fwqTest&state=teststate
     * 3.授权后，会跳转到一个新的地址，新的地址中有auth_code参数，利用这个参数去换取session（HqToken接口）
     * 4.申请测试下面会有一个refresh_session，将这个refresh_session复制到数据库对应的字段里面
     * 5.调用本接口就可以了（最好是将endtime字段的时间改成今天）
     *
     * @param req
     */
    @RequestMapping(value = "HqMtToken")
    public Result HqToken(HttpServletRequest req) {
        String auth_code = req.getParameter("auth_code");
        String appkey = req.getParameter("appkey");
        String redirect_url = req.getParameter("redirect_url");
        WlgbMtToken wtoken = new WlgbMtToken();
        wtoken.setAppkey(appkey);
        WlgbMtToken wlgbMtToken1 = wlgbMtTokenService.queryById(wtoken);
        DynamicTokenResponse response = MtConfig.HqToken(wlgbMtToken1.getAppkey(), wlgbMtToken1.getAppsecret(), auth_code, redirect_url);
        log.info("******response====********{}", response);
        if (response.getExpires_in() <= 0) {
            return Result.OK("更新失败,auth_code过期了", "更新失败,auth_code过期了");
        } else {
            //更新美团的token
            wlgbMtToken1.setToken(response.getAccess_token());
            wlgbMtToken1.setBid(response.getBid());
            wlgbMtToken1.setRefreshToken(response.getRefresh_token());
            Calendar nowTime = Calendar.getInstance();
            Date date1 = new Date();
            nowTime.setTime(date1);
            nowTime.add(Calendar.SECOND, Integer.parseInt(String.valueOf(response.getExpires_in())));
            wlgbMtToken1.setEndtime(nowTime.getTime());
            wlgbMtToken1.setRemainRefreshCount(response.getRemain_refresh_count());
            wlgbMtTokenService.updateById(wlgbMtToken1);
            return Result.OK(wlgbMtToken1);
        }
    }

    /**
     * 二次跟单发送流程 10,18
     */
//    @Scheduled(cron = "0 0 10,18 * * ? ")
    @RequestMapping(value = "ejGdLcFs")
    public void ejGdLcFs() {
        List<TbXyd> list = weiLianDdXcxService.queryXyEcGd();
        list.forEach(l -> {
            TbVilla villa = weiLianDdXcxService.queryTbVillaById(l.getXbsmc());
            if (villa != null) {
                if (villa.getPid() != null && !"".equals(villa.getPid()) && !"null".equalsIgnoreCase(villa.getPid())) {
                    Integer count3 = weiLianDdXcxService.queryBsCount(l.getXbsmc());
                    if (count3 == 0) {
                        Csry csry = null;
                        String vxz = villa.getVxz();
                        if ("直营".equals(vxz)) {
                            csry = weiLianDdXcxService.queryCsRyByUserId(villa.getPid());
                        }
                        if ("托管加盟".equals(vxz)) {
                            csry = weiLianDdXcxService.queryCsRyJmByUserId(villa.getPid());
                        }
                        if (csry != null) {
                            int count1 = weiLianDdXcxService.queryJdbOneCountByXid(l.getXid());
                            if (count1 > 0) {
                                int count2 = wlgbDksljlService.queryCountByBzAndXid("进场及消费", l.getXid());
                                if (count2 == 0) {
                                    int count = wlgbDksljlService.queryCountByBzAndXid("二次跟单", l.getXid());
                                    if (count < 1) {
                                        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
                                        String xydUrl = null;
                                        try {
                                            xydUrl = dzXydCl(l);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
                                        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
                                        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("二次跟单");
                                        WlgbDksljl wlgbDksljl1 = wlgbDksljlService.queryByBzAndXid(l.getXid(), "二次跟单");
                                        //发起人改成房东
                                        try {
                                            WlgbDksljl wlgbDksljl = EcGdLcConfig.ecGdLc(l, villa, dingkey, xydUrl, employee, ydAppkey, ydBd, wlgbDksljl1);
                                            if (wlgbDksljl != null) {
                                                wlgbDksljlService.save(wlgbDksljl);
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            try {
                                                DingDBConfig.sendGztzText(dingkey, "15349026426046931", "定时任务发起二次跟单，订单编号”" + l.getXddbh() + "“更新定时任务发起二次跟单报错了");
                                            } catch (ApiException apiException) {
                                                apiException.printStackTrace();
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * 待入场发送流程
     */
//    @Scheduled(cron = "0 0 0/2 * * ? ")
    @RequestMapping(value = "drcLcFs")
    public void drcLcFs() {
        //当前时间
        Calendar c1 = Calendar.getInstance();
        c1.set(Calendar.MINUTE, 0);
        c1.set(Calendar.SECOND, 0);
        c1.set(Calendar.MILLISECOND, 0);
        //两个小时后
        Calendar c2 = Calendar.getInstance();
        c2.setTimeInMillis(c1.getTimeInMillis());
        c2.add(Calendar.HOUR, 2);
        Map<String, Object> map = new HashMap<>();
        map.put("jc1", c1.getTime());
        map.put("jc2", c2.getTime());
        //查询需要发起进场及消费流程
        List<TbXyd> list = weiLianDdXcxService.queryNotFsJcLc(map);
        //查询需要发起对账表单
        List<TbXyd> list1 = weiLianDdXcxService.queryNotFsDzBd(map);
        fsJcLcAndDzBd(list, list1);
    }

    /**
     * 发送对账流程和表单处理
     */
    public void fsJcLcAndDzBd(List<TbXyd> list, List<TbXyd> list1) {
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("进场及消费");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        //获取表单id
        YdBd ydBd1 = weiLianDdXcxService.queryYdBdByBz("对账");
        list.forEach(l -> {
            TbVilla villa = weiLianDdXcxService.queryTbVillaById(l.getXbsmc());
            if (villa != null) {
                if (villa.getPid() != null && !"".equals(villa.getPid()) && !"null".equalsIgnoreCase(villa.getPid())) {
                    Integer count1 = weiLianDdXcxService.queryBsCount(l.getXbsmc());
                    if (count1 == 0) {
                        Csry csry = weiLianDdXcxService.queryCsRyByUserId(villa.getPid());
                        if (csry != null) {
                            int count = wlgbDksljlService.queryCountByBzAndXid("进场及消费", l.getXid());
                            if (count == 0) {
                                int count2 = wlgbDksljlService.queryCountByBzAndXid("下单及跟单", l.getXid());
                                boolean b = false;
                                //防止老订单 以防没有其他流程
                                if (count2 == 0) {
                                    b = true;
                                }
                                if (count2 > 0) {
                                    int count3 = wlgbDksljlService.queryCountByBzAndXid("二次跟单", l.getXid());
                                    if (count3 > 0) {
                                        int count4 = weiLianDdXcxService.queryJdbTwoCountByXid(l.getXid());
                                        //二次跟单流程跟完的
                                        if (count4 > 0) {
                                            b = true;
                                        }
                                    }
                                    if (count3 == 0) {
                                        int count4 = weiLianDdXcxService.queryNotEcGdCountByXid(l.getXid());
                                        //当下单及跟单已经跟完但提交时间已经在超过二次跟单发起的范围内的订单
                                        if (count4 > 0) {
                                            b = true;
                                        }
                                    }
                                }
                                if (b) {
                                    String xydUrl = null;
                                    try {
                                        xydUrl = dzXydCl(l);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
                                    WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid("进场及消费", l.getXid());
                                    try {
                                        TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(l.getXid());
                                        WlgbDksljl wlgbDksljl = JcJXfLcConfig.jcJXfLc(l, villa, xydUrl, dingkey, employee, ydAppkey, ydBd, null, dksljl, tbYddXyd != null ? "是" : "否");
                                        if (wlgbDksljl != null) {
                                            wlgbDksljlService.save(wlgbDksljl);
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        try {
                                            DingDBConfig.sendGztzText(dingkey, "15349026426046931", "定时任务发起进场及消费，订单编号”" + l.getXddbh() + "“更新定时任务发起进场及消费报错了");
                                        } catch (ApiException apiException) {
                                            apiException.printStackTrace();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });
        list1.forEach(l -> {
            TbVilla villa = weiLianDdXcxService.queryTbVillaById(l.getXbsmc());
            DingdingEmployee employee2 = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa != null ? villa.getPid() : null);
            try {
                //上传对账表单
                scDzBd(l, employee2, villa != null ? villa : new TbVilla(), l.getXsendder(), dingkey, ydAppkey, ydBd1);
            } catch (Exception e) {
                try {
                    DingDBConfig.sendGztzText(dingkey, "15349026426046931", "系统定时任务发起对账1，订单编号”" + l.getXddbh() + "“对账表单报错了，错误原因：" + e);
                } catch (ApiException apiException) {
                    log.info("******数据库连接出错********{}", apiException);
                }
            }
        });
    }

    /**
     * 待入场发送流程(以防流失补发)
     */
//    @Scheduled(cron = "0 0 11,23 * * ? ")
    @RequestMapping(value = "drcLcFsYfLsBf")
    public void drcLcFsYfLsBf() {
        //当前时间
        Calendar c1 = Calendar.getInstance();
        c1.set(Calendar.MINUTE, 0);
        c1.set(Calendar.SECOND, 0);
        c1.set(Calendar.MILLISECOND, 0);
        int i = c1.get(Calendar.HOUR_OF_DAY);
        Map<String, Object> map = new HashMap<>();
        if (i == 23) {
            Calendar c2 = Calendar.getInstance();
            c2.setTimeInMillis(c1.getTimeInMillis());
            c2.set(Calendar.HOUR_OF_DAY, 12);
            Calendar c3 = Calendar.getInstance();
            c3.setTimeInMillis(c1.getTimeInMillis());
            c3.add(Calendar.HOUR_OF_DAY, 1);
            c3.set(Calendar.HOUR_OF_DAY, 0);
            map.put("jc1", c2.getTime());
            map.put("jc2", c3.getTime());
        } else {
            Calendar c2 = Calendar.getInstance();
            c2.setTimeInMillis(c1.getTimeInMillis());
            c2.set(Calendar.HOUR_OF_DAY, 0);
            Calendar c3 = Calendar.getInstance();
            c3.setTimeInMillis(c1.getTimeInMillis());
            c3.set(Calendar.HOUR_OF_DAY, 12);
            map.put("jc1", c2.getTime());
            map.put("jc2", c3.getTime());
        }
        //查询需要发起流程
        List<TbXyd> list = weiLianDdXcxService.queryNotFsJcLc(map);

        //对账表单发起
        List<TbXyd> list1 = weiLianDdXcxService.queryNotFsDzBd(map);
        fsJcLcAndDzBd(list, list1);

    }

//    /**
//     * 无场工作管理发送流程
//     */
//    @Scheduled(cron = "0 0 17 * * ? ")
//    public void wcGzLc() {
//        List<String> list = new ArrayList<>();
//        if ("prod".equals(hjConfig.getActive())) {
//            //测试店长上线打开
//            list = csdzService.queryCsRyId();
//        }
////        if ("dev".equals(hjConfig.getActive())) {
////            List<String> list2 = new ArrayList<>();
////            list2.add("16012841527668377");
////            list = list2;
////        }
//        list.forEach(l -> {
//            Map<String, Object> map = new HashMap<>();
//            map.put("userid", l);
//            Integer yd = villaService.queryBcSfYd(map);
//            if (yd < 1) {
//                List<TbVilla> list1 = villaService.queryByUserIdBs(map);
//                if (list1.size() > 0) {
//                    String a = "";
//                    for (int i = 0; i < list1.size(); i++) {
//                        a += list1.get(i).getVname();
//                        if (i < list1.size() - 1) {
//                            a += ",";
//                        }
//                    }
//                    SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd ");
//                    //实例标题
//                    Map<String, Object> formDatamap = new HashMap<String, Object>();
//                    //需要和数据库保持一致
//                    formDatamap.put("aid", l);
//                    formDatamap.put("bsmc", a);
//                    formDatamap.put("kcwh", 1);
//                    //城市
//                    formDatamap.put("city", list1 != null ? list1.size() > 0 ? list1.get(0).getCity() : null : null);
//                    //当前流程的名字
//                    formDatamap.put("aname", "无场工作管理");
//                    DingdingEmployee employee = dingdingEmployeeService.getById(l);
//                    formDatamap.put("zbdz", employee != null ? employee.getName() : "未设置值班店长");
//                    formDatamap.put("zbdzid", employee != null ? employee.getUserid() : "未设置值班店长");
//                    formDatamap.put("cc", df2.format(new Date()) + " 10:00-17:00 白场");
//                    formDatamap.put("ecgdzxr", l);
//                    String lcid = lcid("012412221639786136545", formDatamap, "无场工作管理");
//                    WlgbDksljl wlgbDksljl = new WlgbDksljl();
//                    wlgbDksljl.setId(IdConfig.uuId());
//                    wlgbDksljl.setTjsj(new Date());
//                    JSONObject jsonObject1 = JSONObject.parseObject(lcid);
//                    GatewayResult gatewayResult = JSONObject.toJavaObject(jsonObject1, GatewayResult.class);
//                    if (gatewayResult != null) {
//                        wlgbDksljl.setSlid(gatewayResult.getResult());
//                    }
//                    wlgbDksljl.setBz("无场工作管理");
//                    wlgbDksljl.setJsr(l);
//                    wlgbDksljl.setTjr("定时任务发起");
//                    wlgbDksljlService.save(wlgbDksljl);
//                }
//            }
//        });
//
//    }

    /**
     * 每个月2号8点钟发送店长上传电表
     */
//    @Scheduled(cron = "0 0 8 2 * ? ")
    @RequestMapping(value = "fsDzScDb")
    public void fsDzScDb() {
        List<TbVilla> list1 = weiLianDdXcxService.queryEhDbBsList();
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("店长上传电表");
        String finalToken = token;
        list1.forEach(l -> {
            Map<String, Object> map = new HashMap<>();
            map.put("bsmc", l.getVname());
            map.put("bsid", l.getVid());
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy年MM月");
            map.put("yf", new Date());
            map.put("aname", df2.format(new Date()) + l.getVname() + "2号电表上传");
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(l.getPid());
            map.put("zbdz", employee != null ? employee.getUserid() : "未设置值班店长");
            map.put("zbdz1", employee != null ? employee.getName() : "未设置值班店长");
            map.put("ecgdzxr", l.getPid());
            //城市
            map.put("city", l.getCity());
            map.put("DzSsBm", l.getVbsssbm() != null ? l.getVbsssbm().replace("(", "").replace(")", "") : "");
            GatewayResult gatewayResult = PtLcCL.lcid("012412221639786136545", map, ydAppkey, ydBd, finalToken);
            WlgbDksljl wlgbDksljl = new WlgbDksljl();
            wlgbDksljl.setId(IdConfig.uuId());
            wlgbDksljl.setTjsj(new Date());
            if (gatewayResult != null) {
                wlgbDksljl.setSlid(gatewayResult.getResult());
            }
            wlgbDksljl.setBz("店长上传电表图");
            wlgbDksljl.setJsr(l.getPid());
            wlgbDksljl.setTjr("定时任务发起");
            wlgbDksljlService.save(wlgbDksljl);
        });
    }

//    /**
//     * 每天早上8点35分给王子伟和范伟旗发送工作通知设置不正确的值班店长
//     */
//    @Scheduled(cron = "0 35 8 * * ? ")
//    public void wszZbDz() {
//        if ("prod".equals(hjConfig.getActive())) {
//            Dingkey dingkey = dingkeyService.getById("weilianxcx");
//            List<TbVilla> list = villaService.queryVillaNotDz1();
//            String context = "共有" + (list != null ? list.size() : 0) + "个别墅未设置正确的值班店长";
//            context += "\n\n请及时跟进";
//            context += "\n\n送达时间：";
//            try {
//                DingDBConfig.sendGztz1("15349026426046931", dingkey, "请设置值班店长", context, jtUrlConfig.getUrl() + "villa.html");
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }

    /**
     * 早上8点通知无场店长进行晚场电表登记
     */
//    @Scheduled(cron = "0 0 8 * * ? ")
    @PostMapping(value = "btWcDb2")
    public void btWcDb() {
        Map<String, Object> map1 = new HashMap<>();
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar3 = Calendar.getInstance();
        Calendar calendar4 = Calendar.getInstance();
        calendar3.set(Calendar.DAY_OF_MONTH, calendar3.get(Calendar.DAY_OF_MONTH) - 1);
        map1.put("jc", df2.format(calendar3.getTime()) + " 18:00:00");
        map1.put("tc", df2.format(calendar4.getTime()) + " 08:00:00");
        List<TbVilla> list = weiLianService.queryTodayWcMd(map1);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("无场电表登记");
        String finalToken = token;
        list.forEach(l -> {
            Map<String, Object> map = new HashMap<>();
            //性质（运营查数据）
            map.put("vxz", l.getVxz());
            //需要和数据库保持一致
            map.put("bsmc", l.getVname());
            map.put("bsid", l.getVid());
            map.put("DzSsBm", l.getVbsssbm() != null ? l.getVbsssbm().replace("(", "").replace(")", "") : "");
            map.put("cc", "晚");
            //当前流程的名字
            map.put("aname", "无场电表登记");
            Calendar calendar1 = Calendar.getInstance();
            Calendar calendar2 = Calendar.getInstance();
            calendar1.set(Calendar.DAY_OF_MONTH, calendar1.get(Calendar.DAY_OF_MONTH) - 1);
            map.put("jcsj", df2.format(calendar1.getTime()) + " 18:00:00");
            map.put("tcsj", df2.format(calendar2.getTime()) + " 08:00:00");
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(l.getPid());
            map.put("zbdz", employee != null ? employee.getName() : "未设置值班店长");
            map.put("sfkc", "是");
            map.put("tjrid", l.getPid());
            map.put("ecgdzxr", l.getPid());
            String city = l.getCszq() != null ? !"".equals(l.getCszq()) ? l.getCszq() : l.getCity() : l.getCity();
            map.put("city", city);
            GatewayResult gatewayResult = PtLcCL.lcid("012412221639786136545", map, ydAppkey, ydBd, finalToken);
            WlgbDksljl wlgbDksljl = new WlgbDksljl();
            wlgbDksljl.setId(IdConfig.uuId());
            wlgbDksljl.setTjsj(new Date());
            if (gatewayResult != null) {
                wlgbDksljl.setSlid(gatewayResult.getResult());
            }
            wlgbDksljl.setBz("晚场无场电表登记");
            wlgbDksljl.setJsr(l.getPid());
            wlgbDksljl.setTjr("定时任务发起");
            wlgbDksljlService.save(wlgbDksljl);
        });
    }

    /**
     * 17点通知无场的店长晚场进行电表登记
     */
//    @Scheduled(cron = "0 0 17 * * ? ")
    @RequestMapping(value = "wsWcDb")
    public void wsWcDb() {
        Map<String, Object> map1 = new HashMap<>();
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        map1.put("jc", df2.format(calendar.getTime()) + " 10:00:00");
        map1.put("tc", df2.format(calendar.getTime()) + " 17:00:00");
        List<TbVilla> list = weiLianService.queryTodayWcMd(map1);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("无场电表登记");
        String finalToken = token;
        list.forEach(l -> {
            Map<String, Object> map = new HashMap<>();
            //性质（运营查数据）

            map.put("vxz", l.getVxz());
            //需要和数据库保持一致
            map.put("bsmc", l.getVname());
            map.put("bsid", l.getVid());
            map.put("DzSsBm", l.getVbsssbm() != null ? l.getVbsssbm().replace("(", "").replace(")", "") : "");
            map.put("cc", "晚");
            //当前流程的名字
            map.put("aname", "无场电表登记");
            Calendar calendar1 = Calendar.getInstance();
            map.put("jcsj", df2.format(calendar1.getTime()) + " 10:00:00");
            map.put("tcsj", df2.format(calendar1.getTime()) + " 17:00:00");
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(l.getPid());
            map.put("zbdz", employee != null ? employee.getName() : "未设置值班店长");
            map.put("sfkc", "是");
            map.put("tjrid", l.getPid());
            map.put("ecgdzxr", l.getPid());
            //城市
            String city = l.getCszq() != null ? !"".equals(l.getCszq()) ? l.getCszq() : l.getCity() : l.getCity();
            map.put("city", city);
            GatewayResult gatewayResult = PtLcCL.lcid("012412221639786136545", map, ydAppkey, ydBd, finalToken);
            WlgbDksljl wlgbDksljl = new WlgbDksljl();
            wlgbDksljl.setId(IdConfig.uuId());
            wlgbDksljl.setTjsj(new Date());
            if (gatewayResult != null) {
                wlgbDksljl.setSlid(gatewayResult.getResult());
            }
            wlgbDksljl.setBz("白场无场电表登记");
            wlgbDksljl.setJsr(l.getPid());
            wlgbDksljl.setTjr("定时任务发起");
            wlgbDksljlService.save(wlgbDksljl);
        });
    }

    /**
     * 发送新增表单
     *
     * @param userid 收取人id
     * @param map    表单内容
     * @param lx     表单类型
     */
    public GatewayResult xzbd(String userid, Map<String, Object> map, String lx) {
        JSONObject json = new JSONObject(map);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz(lx);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, json.toJSONString(), userid, ydBd.getFormid());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return gatewayResult;
    }

    /**
     * 每天晚上1点20美团门店更新，TODO 在这里更新所有门店uuid和名称，然后再调用refreshToken接口来完善每一个门店的Token
     */
    @RequestMapping(value = "sxMtMd")
    public String sxMtMd() throws Exception {
        //每次更新之前都批量删除所有数据，包括数据库和宜搭
        String userid = "012412221639786136545";
        String formUuId = "FORM-HC9660C1OVURS0LOWOFF6Z36D11Y1VTSRB3RK11";

        //查询现在的美团账号（token）
        List<WlgbMtToken> list = wlgbMtTokenService.queryAllListById();
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = DingToken.token(dingkey);
        List<WlgbMtMd> ll = wlgbMtMdService.queryAllWlgbMtMd();
        List<String> lll = new ArrayList<>();
        for (WlgbMtMd md : ll) {
            lll.add(md.getSlid());
        }
        //删除宜搭的所有数据
        GatewayResult gg = DingBdLcConfig.plscBdSl(token, ydAppkey, userid, formUuId, lll);
        if (gg.getSuccess()) {
            System.out.println("宜搭数据全部删除");
        } else {
            System.out.println("宜搭删除数据失败");
        }
        List<String> llll = new ArrayList<>();

        String finalToken = token;
        list.forEach(l -> {
            //session适用店铺查询接口
            CustomerKeyShopScopeResponse c = MtConfig.getSYDP(l.getAppkey(), l.getAppsecret(), l.getToken(), l.getBid());
            List<CustomerKeyShopScopeResponseEntity> data = c.getData();
            log.info("*********查询美团门店list********{}", data);
            if (data != null) {
                data.forEach(d -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("open_shop_uuid", d.getOpen_shop_uuid());
                    map.put("cityname", d.getCityname());
                    map.put("shopaddress", d.getShopaddress());
                    map.put("branchname", d.getBranchname());
                    map.put("shopname", d.getShopname());
                    map.put("textField_m2k5xifd", l.getZh());

                    WlgbMtMd wlgbMtMd = new WlgbMtMd();
                    wlgbMtMd.setId(IdConfig.uuId());
                    wlgbMtMd.setOpenShopUuid(d.getOpen_shop_uuid());
                    wlgbMtMd.setBranchname(d.getBranchname());
                    wlgbMtMd.setCityname(d.getCityname());
                    wlgbMtMd.setShopname(d.getShopname());
                    wlgbMtMd.setShopaddress(d.getShopaddress());
                    wlgbMtMd.setZh(l.getZh());
                    wlgbMtMd.setSfsc(0);

                    WlgbMtMd ww = wlgbMtMdService.queryByShopUuId(d.getOpen_shop_uuid());
                    if (ww == null) {
                        wlgbMtMdService.save(wlgbMtMd);
                    }
                    //新增宜搭数据
                    JSONObject json = new JSONObject(map);
                    llll.add(json.toJSONString());
                });
            }
        });

        // 分批调用 plxzBdSl 方法
        int batchSize = 100;
        int totalBatches = (llll.size() + batchSize - 1) / batchSize;
        for (int i = 0; i < totalBatches; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min(fromIndex + batchSize, llll.size());
            List<String> batch = llll.subList(fromIndex, toIndex);
            GatewayResult gatewayResult = DingBdLcConfig.plxzBdSl(finalToken, ydAppkey, batch, userid, formUuId);
            if (gatewayResult.getSuccess()) {
                System.out.println("第 " + (i + 1) + " 批次新增成功");
            } else {
                System.out.println("第 " + (i + 1) + " 批次新增失败: 错误码:" + gatewayResult.getErrorCode() + ",错误内容:" + gatewayResult.getErrorMsg());
            }
        }
        return "ok";
    }

    /**
     * 电子协议单处理
     *
     * @param tbXyd 协议单对象
     * @return 协议单地址
     */
    public String dzXydCl(TbXyd tbXyd) {
        Dzxyd dzxyd = weiLianDdXcxService.queryDzXydByXid(tbXyd.getXid());
        if (dzxyd == null) {
            return "";
        }
        String upload = dzxyd.getUrl();

        return upload;
    }

    /**
     * 发送回访记录
     */
//    @Scheduled(cron = "0 34 7 * * ? ")
    @RequestMapping(value = "       ")
    public void hfFs() {
        List<WlgbQrdDzjl> list = weiLianDdXcxService.queryFsHf();
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        list.forEach(l -> {
            TbXyd tbXyd = tbXydService.queryByIdAndSfSc(l.getXid());
            if (tbXyd != null) {
                try {
                    Map<String, Object> map = new HashMap<>();
                    map.put("xid", tbXyd.getXid());
                    Integer hf = weiLianDaiBanService.queryNotFsHf(map);
                    if (hf == 0) {
                        DingdingEmployee employee = weiLianService.queryDingDingByName(tbXyd.getXfd());
                        if (employee != null) {
                            ewmYb(tbXyd);
                        }
                        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
                        //加盟商播报
                        WlgbJmsbb wlgbJmsbb = weiLianDdXcxService.queryJmsBbByVid(villa.getVid());
                        if (wlgbJmsbb != null) {
                            String jsd = null;
                            try {
                                jsd = dzJsd(tbXyd.getXid());
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            String contexts = "电子结算单来啦！";
                            contexts += "\n\n别墅：" + villa.getVname();
                            contexts += "\n\n房东：" + tbXyd.getXfd();
                            contexts += "\n\n客户：" + tbXyd.getXzk();
                            contexts += "\n\n进场时间：" + DateFormatConfig.df1(tbXyd.getXjctime());
                            contexts += "\n\n退场时间：" + DateFormatConfig.df1(tbXyd.getXtctime());
                            contexts += "\n\n送达时间：";
                            try {
                                DingDBConfig.sendGztz1(wlgbJmsbb.getBbrid(), dingkey, "电子结算单来啦！", contexts, jsd);
                            } catch (ApiException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    l.setSfcl("1");
                    l.setCltime(new Date());
                    weiLianDdXcxService.updateXgZt(l);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    /**
     * 二维码异步生成
     *
     * @param xyd 协议单对象
     */
    public void ewmYb(TbXyd xyd) {
        String jsd = null;
        try {
            jsd = dzJsd(xyd.getXid());
        } catch (Exception e) {
            e.printStackTrace();
        }

        TbXyd tbXyd = new TbXyd();
        tbXyd.setXid(xyd.getXid());
        tbXyd.setKhhfzt(1);
        tbXydService.updateById(tbXyd);
        WlgbDdhf wlgbDdhf1 = wlgbDdhfService.queryByXidAndSfSc(xyd.getXid());
        if (wlgbDdhf1 == null) {
            WlgbDdhf wlgbDdhf = new WlgbDdhf();
            wlgbDdhf.setId(IdConfig.uuId());
            wlgbDdhf.setEwm(jsd);
            wlgbDdhf.setHqewmtime(new Date());
            wlgbDdhf.setXid(xyd.getXid());
            wlgbDdhf.setSfsc(0);
            wlgbDdhfService.save(wlgbDdhf);
        } else {
            WlgbDdhf wlgbDdhf = new WlgbDdhf();
            wlgbDdhf.setId(wlgbDdhf1.getId());
            wlgbDdhf.setEwm(jsd);
            wlgbDdhf.setHqewmtime(new Date());
            wlgbDdhf.setXid(xyd.getXid());
            wlgbDdhfService.updateById(wlgbDdhf);
        }

        DingdingEmployee employee = weiLianService.queryDingDingByName(xyd.getXfd());
        if (employee != null) {
            String id = IdConfig.uuId();
            int a = 1;
            List<OapiWorkrecordAddRequest.FormItemVo> list2 = new ArrayList<>();
            List<Formitem> list = new ArrayList<>();
            OapiWorkrecordAddRequest.FormItemVo obj1 = new OapiWorkrecordAddRequest.FormItemVo();
            obj1.setContent(xyd.getXddbh());
            obj1.setTitle("订单编号");
            list2.add(obj1);
            Formitem formitem1 = new Formitem();
            formitem1.setTid(id);
            formitem1.setTitle("订单编号");
            formitem1.setCid(IdConfig.uuId());
            formitem1.setCount(a++);
            formitem1.setContent(xyd.getXddbh());
            list.add(formitem1);
            TbVilla villa = weiLianDdXcxService.queryTbVillaById(xyd.getXbsmc());
            if (villa != null) {
                OapiWorkrecordAddRequest.FormItemVo obj2 = new OapiWorkrecordAddRequest.FormItemVo();
                obj2.setContent(villa.getVname());
                obj2.setTitle("别墅");
                list2.add(obj2);
                Formitem formitem2 = new Formitem();
                formitem2.setTid(id);
                formitem2.setTitle("别墅");
                formitem2.setCid(IdConfig.uuId());
                formitem2.setCount(a++);
                formitem2.setContent(villa.getVname());
                list.add(formitem2);
                if (villa.getPid() != null && !"".equals(villa.getPid()) && !"null".equalsIgnoreCase(villa.getPid())) {
                    DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
                    if (employee1 != null) {
                        OapiWorkrecordAddRequest.FormItemVo obj3 = new OapiWorkrecordAddRequest.FormItemVo();
                        obj3.setContent(employee1.getName());
                        obj3.setTitle("值班店长");
                        list2.add(obj3);
                        Formitem formitem3 = new Formitem();
                        formitem3.setTid(id);
                        formitem3.setTitle("值班店长");
                        formitem3.setCid(IdConfig.uuId());
                        formitem3.setCount(a++);
                        formitem3.setContent(employee1.getName());
                        list.add(formitem3);
                    }
                }
            }
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            OapiWorkrecordAddRequest.FormItemVo obj3 = new OapiWorkrecordAddRequest.FormItemVo();
            obj3.setContent(df2.format(xyd.getXjctime()));
            obj3.setTitle("进场时间");
            list2.add(obj3);
            Formitem formitem3 = new Formitem();
            formitem3.setTid(id);
            formitem3.setTitle("进场时间");
            formitem3.setCid(IdConfig.uuId());
            formitem3.setCount(a++);
            formitem3.setContent(df2.format(xyd.getXjctime()));
            list.add(formitem3);

            OapiWorkrecordAddRequest.FormItemVo obj4 = new OapiWorkrecordAddRequest.FormItemVo();
            obj4.setContent(df2.format(xyd.getXtctime()));
            obj4.setTitle("退场时间");
            list2.add(obj4);
            Formitem formitem4 = new Formitem();
            formitem4.setTid(id);
            formitem4.setTitle("退场时间");
            formitem4.setCid(IdConfig.uuId());
            formitem4.setCount(a++);
            formitem4.setContent(df2.format(xyd.getXtctime()));
            list.add(formitem4);
            OapiWorkrecordAddRequest.FormItemVo obj5 = new OapiWorkrecordAddRequest.FormItemVo();
            obj5.setContent(xyd.getXzk());
            obj5.setTitle("客户姓名");
            list2.add(obj5);
            Formitem formitem5 = new Formitem();
            formitem5.setTid(id);
            formitem5.setTitle("客户姓名");
            formitem5.setCid(IdConfig.uuId());
            formitem5.setCount(a++);
            formitem5.setContent(xyd.getXzk());
            list.add(formitem5);

            OapiWorkrecordAddRequest.FormItemVo obj6 = new OapiWorkrecordAddRequest.FormItemVo();
            obj6.setContent(xyd.getXzkdh());
            obj6.setTitle("客户电话");
            list2.add(obj6);
            Formitem formitem6 = new Formitem();
            formitem6.setTid(id);
            formitem6.setTitle("客户电话");
            formitem6.setCid(IdConfig.uuId());
            formitem6.setCount(a);
            formitem6.setContent(xyd.getXzkdh());
            list.add(formitem6);
            Oapiworkrecord api = new Oapiworkrecord();
            api.setDingUserId(employee.getUserid());
            api.setTime(new Date());
            api.setTitle("订单完成，请进行订单回访！");
            api.setDingUserName(employee.getName());
//            api.setUrl(jtUrlConfig.getUrl() + "hftp.html?" + id);
            api.setSourceName("威廉店长小程序");
//            try {
//                api = DingDBConfig.sendDBNotUrl1(jtUrlConfig.getUrl() + "hftp.html?" + id, employee, dingkey, "订单完成，请进行订单回访！", list2);
//            } catch (ApiException e) {
//                e.printStackTrace();
//            }
            api.setId(id);
            oapiworkrecordService.save(api);
            Formitem formitem = new Formitem();
            formitem.setCid(IdConfig.uuId());

            formitem.setTid(id);
            WlgbDdhf wlgbDdhf = wlgbDdhfService.queryByXidAndSfSc(xyd.getXid());
            if (wlgbDdhf != null) {
                formitem.setContent(wlgbDdhf.getEwm());
            }
            formitem.setCount(0);
            formitem.setTitle("url");
            list.add(formitem);

            formitemService.saveBatch(list);
        }
    }

    /**
     * 电子结算单
     *
     * @param xid 协议单id
     * @
     */
    public String dzJsd(String xid) throws Exception {
        TbXyd tbXyd = tbXydService.queryById(xid);
        if (tbXyd != null) {
            TbQrd tbQrd = tbQrdService.queryQxydIdAndQsfSc(tbXyd.getXid());
            if (tbQrd != null) {
                TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
                Map<String, Object> map = new HashMap<>();
                //别墅名字
                map.put("bsName", villa != null ? villa.getVname() : "");
                SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                //退场时间
                map.put("tcsj", df2.format(tbXyd.getXtctime()));
                //进场时间
                map.put("jcsj", df2.format(tbXyd.getXjctime()));
                //租金（场地费优惠后金额）
                map.put("zj", tbQrd.getQsjcdf() != null ? tbQrd.getQsjcdf() : 0);
                //押金
                map.put("yj", tbQrd.getQyj() != null ? tbQrd.getQyj() : 0);
                //电费
                map.put("df", (tbQrd.getQdf() != null ? tbQrd.getQdf() : 0) - (tbQrd.getXdfje() != null ? tbQrd.getXdfje() : 0));
                //使用电
                map.put("sydds", tbQrd.getQydds() != null ? tbQrd.getQydds() : 0);
                //电单价
                map.put("ddj", "c0099614d4104d60b89ad437f5c9c433".equals(tbXyd.getXbsmc()) ? 22.5 : 1.5);
                //商品消费
                map.put("spxf", tbQrd.getQspxf() != null ? tbQrd.getQspxf() : 0);
                //点餐消费
                map.put("dcxf", (tbQrd.getQdcxf() != null ? tbQrd.getQdcxf() : 0) + (tbQrd.getQsktc() != null ? tbQrd.getQsktc() : 0));
                //人头费
                map.put("rtf", tbQrd.getQccrsfy() != null ? tbQrd.getQccrsfy() : 0);
                //厨房使用费
                map.put("cfsyf", tbQrd.getQcfsyf() != null ? tbQrd.getQcfsyf() : 0);
                //卫生费
                map.put("wsf", tbQrd.getQwsf() != null ? tbQrd.getQwsf() : 0);
                //赔偿费
                map.put("pcf", tbQrd.getQbcf() != null ? tbQrd.getQbcf() : 0);
                //保险费
                map.put("bxf", tbXyd.getXbxzje() != null ? tbXyd.getXbxzje() : 0);
                //轰趴师费用
                map.put("hpsfy", tbXyd.getXhpsfy() != null ? tbXyd.getXhpsfy() : 0);
                //策划
                map.put("ch", tbQrd.getQchfw() != null ? tbQrd.getQchfw() : 0);
                //剧本杀
                map.put("jbs", tbXyd.getXjbszje() != null ? tbXyd.getXjbszje() : 0);
                //其他收费
                map.put("qtsf", tbQrd.getQcdzs() != null ? tbQrd.getQcdzs() : 0);
                //延时费
                map.put("ysf", tbQrd.getQysf() != null ? tbQrd.getQysf() : 0);
                //总费用
                Double sum = (tbQrd.getQsjcdf() != null ? tbQrd.getQsjcdf() : 0) + (tbQrd.getQdf() != null ? tbQrd.getQdf() : 0) + (tbQrd.getQspxf() != null ? tbQrd.getQspxf() : 0) + (tbQrd.getQdcxf() != null ? tbQrd.getQdcxf() : 0) + (tbQrd.getQsktc() != null ? tbQrd.getQsktc() : 0) + (tbQrd.getQccrsfy() != null ? tbQrd.getQccrsfy() : 0) + (tbQrd.getQcfsyf() != null ? tbQrd.getQcfsyf() : 0) + (tbQrd.getQwsf() != null ? tbQrd.getQwsf() : 0) + (tbQrd.getQbcf() != null ? tbQrd.getQbcf() : 0) + (tbXyd.getXbxzje() != null ? tbXyd.getXbxzje() : 0) + (tbXyd.getXhpsfy() != null ? tbXyd.getXhpsfy() : 0) + (tbQrd.getQchfw() != null ? tbQrd.getQchfw() : 0) + (tbXyd.getXjbszje() != null ? tbXyd.getXjbszje() : 0) + (tbQrd.getQdgsr() != null ? tbQrd.getQdgsr() : 0) + (tbQrd.getQcdzs() != null ? tbQrd.getQcdzs() : 0) + (tbQrd.getQysf() != null ? tbQrd.getQysf() : 0) - (tbQrd.getXdfje() != null ? tbQrd.getXdfje() : 0);
                //费用总计
                map.put("fyzj", sum);
                String url = hfEwm1(tbXyd.getXddbh());
                String img = "<img src='" + url + "' style='height: 180px;width: 180px;'/>";
                map.put("url", img);
                //模板
                String tempName = FileConfig.getFileAbsolutePath2("static" + File.separator + "qrd.html");
                String context = PDFUtil.freeMarkerRender(map, tempName);
                String id = "DZJS" + DateFormatConfig.df2(new Date()) + IdConfig.uuId();
                String pdf = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
                String png = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".png");
                File newPdf = new File(pdf);
                //生成pdf
                PDFUtil.createPdf1(context, newPdf.getPath(), 380, 850);
                //生成图片
                PDFUtil.pdfToImg(newPdf.getPath(), 1, png);
                File file = new File(png);
                FileInputStream fileInputStream = new FileInputStream(file);
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
                fileInputStream.close();

                String upload = ossFileService.upload(multipartFile);
                if (upload != null && !"".equals(upload)) {
                    upload = upload.replace("https://appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
                }

                TbQrd tbQrd1 = new TbQrd();
                tbQrd1.setQid(tbQrd.getQid());
                tbQrd1.setQimagepath(upload);
                tbQrdService.updateById(tbQrd1);
                newPdf.delete();
                file.delete();

                return upload;
            }
        }
        return null;
    }

    /**
     * 5点处理无场隔离表单过期数据
     */
//    @Scheduled(cron = "0 0 5 * * ? ")
    @RequestMapping(value = "queryGqGl")
    public void queryGqGl() {
        List<WlgbNotVilla> list = weiLianDdXcxService.queryGqGl();
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        String finalToken = token;
        list.forEach(l -> {
            WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(l.getVid(), "无场隔离");
            if (wlgbBdjl != null) {
                GatewayResult gatewayResult = new GatewayResult();
                try {
                    gatewayResult = DingBdLcConfig.scBdSl(finalToken, ydAppkey, "012412221639786136545", wlgbBdjl.getSlid());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                wlgbBdjlService.removeById(wlgbBdjl.getId());
            }
            weiLianDdXcxService.deleteSc(l.getVid());
        });
    }

    /**
     * 获取宜搭未完成下单及跟单的流程详情
     *
     * @return
     */
    public Map<String, Object> queryXdJGd(DkBbJqr j) {
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("下单及跟单");
        Map<String, Object> map1 = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        List<String> list1 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        List<JSONObject> list3 = new ArrayList<>();
        List<String> citys = (j.getCity() != null ? !"".equals(j.getCity()) ? Arrays.asList(j.getCity().split(",")) : new ArrayList<>() : new ArrayList<>());
        citys.forEach(l2 -> {
            if (ydBd != null) {
                Map<String, Object> map2 = new HashMap<>();
                map2.put("city", l2);
                String scSl = hqScSl(ydBd.getFormid(), map2, "1");
                JSONObject object = JSONObject.parseObject(scSl);
                int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;
                double b = count / 100.00;
                int a = (int) b;
                int c = b > a ? a + 1 : a;
                if (c > 0) {
                    JSONArray jsonArray = object.getJSONObject("result").getJSONArray("data");
                    jsonArray.forEach(l -> list3.add((JSONObject) l));
                }
                for (int i = 2; i <= c; i++) {
                    String lcid1 = hqScSl(ydBd.getFormid(), map2, i + "");
                    JSONObject jsonObject2 = JSONObject.parseObject(lcid1);
                    JSONArray jsonArray = jsonObject2.getJSONObject("result").getJSONArray("data");
                    jsonArray.forEach(l -> list3.add((JSONObject) l));
                }
                list3.forEach(l -> {
                    JSONObject data1 = l.getJSONObject("data");
                    String zxr = data1.getJSONArray("xdjgdzxr").get(0).toString();
                    String zxrid = data1.getJSONArray("xdjgdzxr_id").get(0).toString();
                    String bsmc = data1.getString("bsmc");
                    String xid = data1.getString("xid");
                    String city = data1.getString("city");
                    if ((city != null ? !"".equals(city) ? city : "长沙" : "长沙").equals(l2)) {
                        Date jcsj = data1.getDate("jcsj");
                        Date tcsj = data1.getDate("tcsj");
                        Map<String, Object> map = new HashMap<>();
                        map.put("zxr", zxr);
                        map.put("bsmc", bsmc);
                        map.put("jcsj", jcsj);
                        map.put("tcsj", tcsj);
                        //昨天12点
                        Calendar c1 = Calendar.getInstance();
                        c1.add(Calendar.DAY_OF_MONTH, -1);
                        c1.set(Calendar.HOUR_OF_DAY, 12);
                        c1.set(Calendar.MINUTE, 0);
                        c1.set(Calendar.SECOND, 0);
                        c1.set(Calendar.MILLISECOND, 0);
                        //今天12点
                        Calendar c2 = Calendar.getInstance();
                        c2.set(Calendar.HOUR_OF_DAY, 12);
                        c2.set(Calendar.MINUTE, 0);
                        c2.set(Calendar.SECOND, 0);
                        c2.set(Calendar.MILLISECOND, 0);
                        Date gmtCreate = l.getDate("gmtCreate");
                        map.put("cjsj", gmtCreate);
                        map.put("city", city);

                        if (c1.getTimeInMillis() < gmtCreate.getTime() && gmtCreate.getTime() <= c2.getTimeInMillis()) {
                            TbXyd tbXyd = tbXydService.queryById(xid);
                            TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd != null ? tbXyd.getXbsmc() : null);
                            if (tbVilla != null) {
                                if ("直营".equals(tbVilla.getVxz())) {
                                    if (!list1.contains(zxrid)) {
                                        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(zxrid);
                                        if (employee != null) {
                                            list2.add(employee.getMobile());
                                        }
                                        list1.add(zxrid);
                                    }
                                    list.add(map);
                                }
                            }
                        }
                    }
                });
            }
        });
//        List<Map<String, Object>> mapList = list.stream().distinct().collect(Collectors.toList());
//        map1.put("data", mapList);
        map1.put("data", list);
        map1.put("mobile", list2);

        return map1;
    }

    /**
     * 获取宜搭未完成二次跟单的流程详情
     *
     * @return
     */
    public Map<String, Object> queryEcGd(DkBbJqr j) {
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("二次跟单");
        Map<String, Object> map1 = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        List<String> list1 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        List<JSONObject> list3 = new ArrayList<>();
        List<String> citys = (j.getCity() != null ? !"".equals(j.getCity()) ? Arrays.asList(j.getCity().split(",")) : new ArrayList<>() : new ArrayList<>());
        citys.forEach(l2 -> {
            if (ydBd != null) {
                Map<String, Object> map2 = new HashMap<>();
                map2.put("city", l2);
                String scSl = hqScSl(ydBd.getFormid(), map2, "1");
                JSONObject object = JSONObject.parseObject(scSl);
                int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;
                double b = count / 100.00;
                int a = (int) b;
                int c = b > a ? a + 1 : a;
                if (c > 0) {
                    JSONArray jsonArray = object.getJSONObject("result").getJSONArray("data");
                    jsonArray.forEach(l -> list3.add((JSONObject) l));
                }
                for (int i = 2; i <= c; i++) {
                    String lcid1 = hqScSl(ydBd.getFormid(), map2, i + "");
                    JSONObject jsonObject2 = JSONObject.parseObject(lcid1);
                    JSONArray jsonArray = jsonObject2.getJSONObject("result").getJSONArray("data");
                    jsonArray.forEach(l -> list3.add((JSONObject) l));
                }
                list3.forEach(l -> {
                    JSONObject data1 = l.getJSONObject("data");
                    String zxr = data1.getJSONArray("ecgdzxr").get(0).toString();
                    String zxrid = data1.getJSONArray("ecgdzxr_id").get(0).toString();
                    String bsmc = data1.getString("bsmc");
                    Date jcsj = data1.getDate("jcsj");
                    Date tcsj = data1.getDate("tcsj");
                    String xid = data1.getString("xid");
                    String city = data1.getString("city");
                    if ((city != null ? !"".equals(city) ? city : "长沙" : "长沙").equals(l2)) {
                        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
                        //明天
                        Calendar c1 = Calendar.getInstance();
                        c1.add(Calendar.DAY_OF_MONTH, 1);
                        c1.set(Calendar.HOUR_OF_DAY, 12);
                        c1.set(Calendar.MINUTE, 0);
                        c1.set(Calendar.SECOND, 0);
                        c1.set(Calendar.MILLISECOND, 0);
                        if (df2.format(c1.getTime()).equals(df2.format(jcsj))) {
                            TbXyd tbXyd = tbXydService.queryById(xid);
                            TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd != null ? tbXyd.getXbsmc() : null);
                            if (tbVilla != null) {
                                if ("直营".equals(tbVilla.getVxz())) {
                                    Map<String, Object> map = new HashMap<>();
                                    map.put("zxr", zxr);
                                    map.put("bsmc", bsmc);
                                    map.put("jcsj", jcsj);
                                    map.put("tcsj", tcsj);
                                    Date gmtCreate = l.getDate("gmtCreate");
                                    map.put("cjsj", gmtCreate);
                                    map.put("city", city);
                                    if (!list1.contains(zxrid)) {
                                        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(zxrid);
                                        if (employee != null) {
                                            list2.add(employee.getMobile());
                                        }
                                        list1.add(zxrid);
                                    }
                                    list.add(map);
                                }
                            }
                        }
                    }
                });
            }
        });
//        List<Map<String, Object>> mapList = list.stream().distinct().collect(Collectors.toList());
//        map1.put("data", mapList);
        map1.put("data", list);
        map1.put("mobile", list2);
        return map1;
    }

    /**
     * 获取宜搭未完成进场及消费的流程详情（当前时间已经大于退场时间的）
     *
     * @param j        集群（可能包含多个城市）
     * @param ydAppkey
     * @return
     */
    public Map<String, Object> queryJcJXf(DkBbJqr j, YdAppkey ydAppkey) {
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("进场及消费新");
        Map<String, Object> map1 = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        List<String> list1 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        List<JSONObject> list3 = new ArrayList<>();
        List<String> citys = (j.getCity() != null ? !"".equals(j.getCity()) ? Arrays.asList(j.getCity().split(",")) : new ArrayList<>() : new ArrayList<>());
        //遍历集群下面的城市
        citys.forEach(l2 -> {
            if (ydBd != null) {
                Map<String, Object> map2 = new HashMap<>();
                map2.put("city", l2);
                //获取实例详情
                String scSl = hqScSl(ydBd.getFormid(), map2, "1", ydAppkey);
                JSONObject object = JSONObject.parseObject(scSl);
                int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;
                double b = count / 100.00;
                int a = (int) b;
                int c = b > a ? a + 1 : a;
                if (c > 0) {
                    JSONArray jsonArray = object.getJSONObject("result").getJSONArray("data");
                    jsonArray.forEach(l -> list3.add((JSONObject) l));
                }
                for (int i = 2; i <= c; i++) {
                    String lcid1 = hqScSl(ydBd.getFormid(), map2, i + "", ydAppkey);
                    JSONObject jsonObject2 = JSONObject.parseObject(lcid1);
                    JSONArray jsonArray = jsonObject2.getJSONObject("result").getJSONArray("data");
                    jsonArray.forEach(l -> list3.add((JSONObject) l));
                }
                list3.forEach(l -> {
                    JSONObject data1 = l.getJSONObject("data");
                    String zxr = data1.getJSONArray("jcjxfzxr").get(0).toString();
                    String zxrid = data1.getJSONArray("jcjxfzxr_id").get(0).toString();
                    String bsmc = data1.getString("bsmc");
                    Date jcsj = data1.getDate("jcsj");
                    Date tcsj = data1.getDate("tcsj");
                    String xid = data1.getString("xid");
                    String city = data1.getString("city");
                    if ((city != null ? !"".equals(city) ? city : "长沙" : "长沙").equals(l2)) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("zxr", zxr);
                        map.put("bsmc", bsmc);
                        map.put("jcsj", jcsj);
                        map.put("tcsj", tcsj);
                        Date gmtCreate = l.getDate("gmtCreate");
                        map.put("cjsj", gmtCreate);
                        map.put("city", city);
                        //昨天12点
                        Calendar c1 = Calendar.getInstance();
                        c1.add(Calendar.DAY_OF_MONTH, -1);
                        c1.set(Calendar.HOUR_OF_DAY, 8);
                        c1.set(Calendar.MINUTE, 0);
                        c1.set(Calendar.SECOND, 0);
                        c1.set(Calendar.MILLISECOND, 0);
                        //今天12点
                        Calendar c2 = Calendar.getInstance();
                        c2.set(Calendar.HOUR_OF_DAY, 8);
                        c2.set(Calendar.MINUTE, 0);
                        c2.set(Calendar.SECOND, 0);
                        c2.set(Calendar.MILLISECOND, 0);
                        if (c1.getTimeInMillis() < tcsj.getTime() && tcsj.getTime() <= c2.getTimeInMillis()) {
                            TbXyd tbXyd = tbXydService.queryById(xid);
                            TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd != null ? tbXyd.getXbsmc() : null);
                            if (tbVilla != null) {
                                if ("直营".equals(tbVilla.getVxz())) {
                                    if (!list1.contains(zxrid)) {
                                        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(zxrid);
                                        if (employee != null) {
                                            list2.add(employee.getMobile());
                                        }
                                        list1.add(zxrid);
                                    }
                                    list.add(map);
                                }
                            }
                        }
                    }
                });
            }
        });
        map1.put("data", list);
        map1.put("mobile", list2);
        return map1;
    }

    /**
     * 获取宜搭未完成无场电表的流程详情（前一天发起的）
     *
     * @return
     */
    public Map<String, Object> queryWcDb(DkBbJqr j, YdBd ydBd, YdAppkey ydAppkey) {
        Map<String, Object> map1 = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        List<String> list1 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        List<JSONObject> list3 = new ArrayList<>();
        List<String> citys = (j.getCity() != null ? !"".equals(j.getCity()) ? Arrays.asList(j.getCity().split(",")) : new ArrayList<>() : new ArrayList<>());
        citys.forEach(l2 -> {
            if (ydBd != null) {
                Map<String, Object> map2 = new HashMap<>();
                map2.put("city", l2);
                String scSl = hqScSl(ydBd.getFormid(), map2, "1", ydAppkey);
                JSONObject object = JSONObject.parseObject(scSl);
                int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;
                double b = count / 100.00;
                int a = (int) b;
                int c = b > a ? a + 1 : a;
                if (c > 0) {
                    JSONArray jsonArray = object.getJSONObject("result").getJSONArray("data");
                    jsonArray.forEach(l -> {
                        list3.add((JSONObject) l);
                    });
                }
                for (int i = 2; i <= c; i++) {
                    String lcid1 = hqScSl(ydBd.getFormid(), map2, i + "", ydAppkey);
                    JSONObject jsonObject2 = JSONObject.parseObject(lcid1);
                    JSONArray jsonArray = jsonObject2.getJSONObject("result").getJSONArray("data");
                    jsonArray.forEach(l -> {
                        list3.add((JSONObject) l);
                    });
                }
                list3.forEach(l -> {
                    JSONObject data1 = l.getJSONObject("data");
                    String zxr = data1.getJSONArray("ecgdzxr").get(0).toString();
                    String zxrid = data1.getJSONArray("ecgdzxr_id").get(0).toString();
                    String bsmc = data1.getString("bsmc");
                    Date jcsj = data1.getDate("jcsj");
                    Date tcsj = data1.getDate("tcsj");
                    Map<String, Object> map = new HashMap<>();
                    map.put("zxr", zxr);
                    map.put("bsmc", bsmc);
                    map.put("jcsj", jcsj);
                    map.put("tcsj", tcsj);
                    String bsid = data1.getString("bsid");
                    String city = data1.getString("city");
                    if ((city != null ? !"".equals(city) ? city : "长沙" : "长沙").equals(l2)) {
                        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
                        Date gmtCreate = l.getDate("gmtCreate");
                        map.put("cjsj", gmtCreate);
                        map.put("city", city);
                        Calendar instance = Calendar.getInstance();
                        instance.add(Calendar.DAY_OF_MONTH, -1);
                        if (df2.format(instance.getTime()).equals(df2.format(jcsj))) {
                            TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(bsid);
                            if (tbVilla != null) {
                                if ("直营".equals(tbVilla.getVxz())) {
                                    if (!list1.contains(zxrid)) {
                                        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(zxrid);
                                        if (employee != null) {
                                            list2.add(employee.getMobile());
                                        }
                                        list1.add(zxrid);
                                    }
                                    list.add(map);
                                }
                            }
                        }
                    }
                });
            }
        });
//        List<Map<String, Object>> mapList = list.stream().distinct().collect(Collectors.toList());
//        map1.put("data", mapList);
        map1.put("data", list);
        map1.put("mobile", list2);
        return map1;
    }

    /**
     * 流程数据生成图片
     *
     * @param list  数据
     * @param title 标题
     * @return 图片地址
     */
    public String xdJGdTpSc(List<Map<String, Object>> list, String title) throws Exception {
        Map<String, Object> map = new HashMap<>();
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = "<table><tr>\n" +
                "                    <th width='36%'>别墅</th>\n" +
                "                    <th width='15%'>进场时间</th>\n" +
                "                    <th width='15%'>退场时间</th>\n" +
                "                    <th width='15%'>执行人</th>\n" +
                "                    <th width='9%'>城市</th>\n" +
                "                    <th width='15%'>创建时间</th>\n" +
                "                </tr>";
        for (int i = 0; i < list.size(); i++) {
            str += "<tr><td>" + list.get(i).get("bsmc") + "</td>";
            str += "<td>" + df2.format(list.get(i).get("jcsj")) + "</td>";
            str += "<td>" + df2.format(list.get(i).get("tcsj")) + "</td>";
            str += "<td>" + list.get(i).get("zxr") + "</td>";
            str += "<td>" + list.get(i).get("city") + "</td>";
            str += "<td>" + df2.format(list.get(i).get("cjsj")) + "</td></tr>";
        }
        str += "</table>";
        map.put("title", "<span style='color:red;font-weight: 900;'>【" + title + "未执行完成进度】</span><br />数据更新时间：" + df2.format(new Date()));
        map.put("str", str);
        String tempName = FileConfig.getFileAbsolutePath2("static" + File.separator + "Pdf.html");
        ;
        String context = PDFUtil.freeMarkerRender(map, tempName);
        String bddz = PathUtil.getClassResources();
        String id = UUID.randomUUID().toString().replace("-", "");
        String pdf = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
        String png = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".png");
        File newPdf = new File(bddz + pdf);
        File newPng = new File(bddz + png);
        //生成pdf
        PDFUtil.createPdf1(context, newPdf.getPath(), 760, list != null ? list.size() > 3 ? (list.size() + 1) * 50 : 240 : 240);
        //生成图片
        PDFUtil.pdfToImg(newPdf.getPath(), 1, newPng.getPath());
        FileInputStream fileInputStream = new FileInputStream(newPng);
        MultipartFile multipartFile = new MockMultipartFile(newPng.getName(), fileInputStream);
        if (fileInputStream != null) {
            fileInputStream.close();
        }
        String upload = ossFileService.upload(multipartFile);
        if (upload != null && !"".equals(upload)) {
            upload = upload.replace("https://appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
        }
        newPdf.delete();
        newPng.delete();
        return upload;
    }

    /**
     * 进场及消费提醒播报
     */
//    @Scheduled(cron = "0 0 10 * * ? ")
    @RequestMapping(value = "wcGzAndWcDb")
    public void wcGzAndWcDb() {
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        List<DkBbJqr> list = weiLianDdXcxService.queryLcBbJqr();
        list.forEach(l1 -> {
            List<String> mobile = new ArrayList<>();
            //进场及消费
            List<String> list2 = jcJXf(l1, ydAppkey);
            //数据专员
            mobile.add("19974916615");
            list2.forEach(l -> {
                if (!mobile.contains(l)) {
                    mobile.add(l);
                }
            });
            List<String> bbAt = weiLianDdXcxService.queryJqrBbAt(l1.getCity());
            bbAt.forEach(l -> {
                if (!mobile.contains(l)) {
                    mobile.add(l);
                }
            });
            if (list2 != null && list2.size() > 0) {
                DingDingUtil.sendMsg(l1.getWebhook(), l1.getSecret(), "您有未处理的流程，请尽快处理！", mobile, false);
            }
        });
    }

    /**
     * 无场电表提醒播报
     */
    @RequestMapping(value = "wcGzBb")
    public void wcGzBb() {
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("无场电表登记");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        List<DkBbJqr> list4 = weiLianDdXcxService.queryLcBbJqr();
        list4.forEach(l1 -> {
            //无场电表
            List<String> list1 = wcDb(l1, ydBd, ydAppkey);
            List<String> mobile = new ArrayList<>();
            //数据专员
            mobile.add("19974916615");
            list1.forEach(l -> {
                if (!mobile.contains(l)) {
                    mobile.add(l);
                }
            });
            List<String> bbAt = weiLianDdXcxService.queryJqrBbAt(l1.getCity());
            bbAt.forEach(l -> {
                if (!mobile.contains(l)) {
                    mobile.add(l);
                }
            });
            if (((list1 != null ? list1.size() : 0)) > 0) {
                //发送到群里
                DingDingUtil.sendMsg(l1.getWebhook(), l1.getSecret(), "您有未处理的流程，请尽快处理！", mobile, false);
            }
        });
    }

    /**
     * 下单及跟单提醒播报
     */
//    @Scheduled(cron = "0 0 12 * * ? ")
    @RequestMapping(value = "gdAndJc")
    public void gdAndJc() {
        List<DkBbJqr> list4 = weiLianDdXcxService.queryLcBbJqr();
        list4.forEach(l1 -> {
            List<String> mobile = new ArrayList<>();
            //下单及跟单
            List<String> list = xdJGd(l1);
            //数据专员
            mobile.add("19974916615");
            list.forEach(l -> {
                if (!mobile.contains(l)) {
                    mobile.add(l);
                }
            });
            List<String> bbAt = weiLianDdXcxService.queryJqrBbAt(l1.getCity());
            bbAt.forEach(l -> {
                if (!mobile.contains(l)) {
                    mobile.add(l);
                }
            });

            if (list.size() > 0) {
                DingDingUtil.sendMsg(l1.getWebhook(), l1.getSecret(), "您有未处理的流程，请尽快处理！", mobile, false);
            }
        });
    }

    /**
     * 二次跟单提醒
     */
//    @Scheduled(cron = "0 0 14 * * ? ")
    @RequestMapping(value = "syAndLC1")
    public void syAndLC1() {
        List<DkBbJqr> list4 = weiLianDdXcxService.queryLcBbJqr();
        list4.forEach(l1 -> {
            List<String> mobile = new ArrayList<>();
            //二次跟单
            List<String> list1 = ecGd(l1);
            //数据专员
            mobile.add("19974916615");

            list1.forEach(l -> {
                if (!mobile.contains(l)) {
                    mobile.add(l);
                }
            });
            List<String> bbAt = weiLianDdXcxService.queryJqrBbAt(l1.getCity());
            bbAt.forEach(l -> {
                if (!mobile.contains(l)) {
                    mobile.add(l);
                }
            });

            if ((list1 != null ? list1.size() : 0) > 0) {
                DingDingUtil.sendMsg(l1.getWebhook(), l1.getSecret(), "您有未处理的流程，请尽快处理！", mobile, false);
            }
        });
    }

    /**
     * 根据条件获取实例数据详情
     */
    public String hqScSl(String id, Map<String, Object> map, String currentPage) {
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        JSONObject json = new JSONObject(map);
        String sclc = YdConfig.hqLcSlData("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), id, json.toJSONString(), currentPage);
        return sclc;
    }

    /**
     * 获取实例性情
     *
     * @param id
     * @param map
     * @param currentPage
     * @param ydAppkey
     * @return
     */
    public String hqScSl(String id, Map<String, Object> map, String currentPage, YdAppkey ydAppkey) {
        JSONObject json = new JSONObject(map);
        String sclc = YdConfig.hqLcSlData("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), id, json.toJSONString(), currentPage);
        return sclc;
    }

    /**
     * 下单及跟单
     */
    public List<String> xdJGd(DkBbJqr j) {
        //下单及跟单
        Map<String, Object> map1 = queryXdJGd(j);
        List<Map<String, Object>> list4 = (List<Map<String, Object>>) map1.get("data");
        List<String> mobile = (List<String>) map1.get("mobile");
        if (list4 != null && list4.size() > 0) {
            String a = "#### <font color=#ff0303 size=7>下单及跟单未处理完成，共" + list4.size() + "条</font>";
            a += "\n\n><font color=#1a75ff>点击下面的图片可以查看全部数据</font>";
            String sc = null;
            try {
                sc = xdJGdTpSc(list4, "下单及跟单");
            } catch (Exception e) {
                e.printStackTrace();
            }
            a += "\n\n> ![图片](" + sc + ")";
            DingDingUtil.sendMark(j.getWebhook(), j.getSecret(), "下单及跟单进度播报", a, null, false);
        }
        return mobile;
    }

    /**
     * 二次跟单
     */
    public List<String> ecGd(DkBbJqr j) {
        //二次跟单
        Map<String, Object> map1 = queryEcGd(j);
        List<Map<String, Object>> list3 = (List<Map<String, Object>>) map1.get("data");
        List<String> mobile = (List<String>) map1.get("mobile");
        if (list3 != null && list3.size() > 0) {
            String b = "#### <font color=#ff0303 size=7>二次跟单未处理完成，共" + list3.size() + "条</font>";
            b += "\n\n><font color=#1a75ff>点击下面的图片可以查看全部数据</font>";
            String sc = null;
            try {
                sc = xdJGdTpSc(list3, "二次跟单");
            } catch (Exception e) {
                e.printStackTrace();
            }
            b += "\n\n> ![图片](" + sc + ")";
            DingDingUtil.sendMark(j.getWebhook(), j.getSecret(), "二次跟单进度播报", b, null, false);
        }
        return mobile;
    }

    /**
     * 进场及消费
     *
     * @param j        集群实体类，一共有七八个集群
     * @param ydAppkey
     * @return
     */
    public List<String> jcJXf(DkBbJqr j, YdAppkey ydAppkey) {
        //查询出这个集群的所有未进场消费的订单
        Map<String, Object> map1 = queryJcJXf(j, ydAppkey);
        List<Map<String, Object>> list = (List<Map<String, Object>>) map1.get("data");
        List<String> mobile = (List<String>) map1.get("mobile");
        if (list != null && list.size() > 0) {
            String c = "#### <font color=#ff0303 size=7>进场及消费未处理完成，共" + list.size() + "条</font>";
            c += "\n\n><font color=#1a75ff>点击下面的图片可以查看全部数据</font>";
            String sc = null;
            try {
                sc = xdJGdTpSc(list, "进场及消费");
            } catch (Exception e) {
                e.printStackTrace();
            }
            c += "\n\n> ![图片](" + sc + ")";
            //群播报
            DingDingUtil.sendMark(j.getWebhook(), j.getSecret(), "进场及消费进度播报", c, null, false);
        }
        return mobile;
    }

    /**
     * 无场电表
     */
    public List<String> wcDb(DkBbJqr j, YdBd ydBd, YdAppkey ydAppkey) {
        //无场电表
        Map<String, Object> map1 = queryWcDb(j, ydBd, ydAppkey);
        List<Map<String, Object>> list1 = (List<Map<String, Object>>) map1.get("data");
        List<String> mobile = (List<String>) map1.get("mobile");
        if (list1 != null && list1.size() > 0) {
            String d = "#### 无场电表登记未处理完成，共<font color=#ff0303>" + list1.size() + "</font>条";
            d += "\n\n><font color=#1a75ff>点击下面的图片可以查看全部数据</font>";
            String sc = null;
            try {
                sc = xdJGdTpSc(list1, "无场电表登记");
            } catch (Exception e) {
                e.printStackTrace();
            }
            d += "\n\n> ![图片](" + sc + ")";
            //发送群消息
            DingDingUtil.sendMark(j.getWebhook(), j.getSecret(), "无场电表登记进度播报", d, null, false);
        }
        return mobile;
    }

    /**
     * 回访二维码
     */
    public String hfEwm1(String ddbh) throws Exception {
        String url = "https://jztdpp.aliwork.com/o/zheshiyigehuifang?ddbh=" + ddbh;
        File file = new File(FileConfig.getFileAbsolutePath("static" + File.separator + "img", "ewm.jpg"));
        ZXingCode.drawLogoQRCode(new File(FileConfig.getFileAbsolutePath2("static" + File.separator + "img" + File.separator + "weilian_logo.jpg")),
                file,
                url, "");

        FileInputStream fileInputStream = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
        if (fileInputStream != null) {
            fileInputStream.close();
        }
        String upload = ossFileService.upload(multipartFile);
        if (upload != null && !"".equals(upload)) {
            upload = upload.replace("https://appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
        }
        file.delete();
        return upload;
    }

    /**
     * 获取宜搭已经进场还没有操作任何流程订单
     *
     * @return
     */
    public Map<String, Object> queryWZxJcLc(DkBbJqr j, Integer sum) {
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("进场及消费");
        Map<String, Object> map1 = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        List<String> list1 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        List<JSONObject> list3 = new ArrayList<>();
        List<String> citys = (j.getCity() != null ? !"".equals(j.getCity()) ? Arrays.asList(j.getCity().split(",")) : new ArrayList<>() : new ArrayList<>());
        citys.forEach(l2 -> {
            if (ydBd != null) {
                Map<String, Object> map2 = new HashMap<>();
                map2.put("city", l2);
                String scSl = hqScSl(ydBd.getFormid(), map2, "1");
                JSONObject object = JSONObject.parseObject(scSl);
                int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;
                double b = count / 100.00;
                int a = (int) b;
                int c = b > a ? a + 1 : a;
                if (c > 0) {
                    JSONArray jsonArray = object.getJSONObject("result").getJSONArray("data");
                    jsonArray.forEach(l -> list3.add((JSONObject) l));
                }
                for (int i = 2; i <= c; i++) {
                    String lcid1 = hqScSl(ydBd.getFormid(), map2, i + "");
                    JSONObject jsonObject2 = JSONObject.parseObject(lcid1);
                    JSONArray jsonArray = jsonObject2.getJSONObject("result").getJSONArray("data");
                    jsonArray.forEach(l -> list3.add((JSONObject) l));
                }
                map1.put("datas", object.getJSONObject("result"));
                list3.forEach(l -> {
                    JSONObject data1 = l.getJSONObject("data");
                    Date jcsj = data1.getDate("jcsj");
                    Date tcsj = data1.getDate("tcsj");
                    //今天9点
                    Calendar c1 = Calendar.getInstance();
                    if (sum == 1) {
                        c1.set(Calendar.HOUR_OF_DAY, 9);
                    } else {
                        c1.set(Calendar.HOUR_OF_DAY, 17);
                    }

                    c1.set(Calendar.MINUTE, 0);
                    c1.set(Calendar.SECOND, 0);
                    c1.set(Calendar.MILLISECOND, 0);
                    //今天10点
                    Calendar c2 = Calendar.getInstance();
                    if (sum == 1) {
                        c2.set(Calendar.HOUR_OF_DAY, 10);
                    } else {
                        c2.set(Calendar.HOUR_OF_DAY, 18);
                    }
                    c2.set(Calendar.MINUTE, 0);
                    c2.set(Calendar.SECOND, 0);
                    c2.set(Calendar.MILLISECOND, 0);
                    String city = data1.getString("city");
                    String xid = data1.getString("xid");
                    String zxr = data1.getJSONArray("jcjxfzxr").get(0).toString();
                    String zxrid = data1.getJSONArray("jcjxfzxr_id").get(0).toString();
                    String bsmc = data1.getString("bsmc");
                    Object qrcdds = data1.get("qrcdds");
                    if (qrcdds == null || "".equals(qrcdds)) {
                        if ((city != null ? !"".equals(city) ? city : "长沙" : "长沙").equals(l2)) {
                            if (c1.getTimeInMillis() <= jcsj.getTime() && jcsj.getTime() <= c2.getTimeInMillis()) {
                                TbXyd tbXyd = tbXydService.queryById(xid);
                                TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd != null ? tbXyd.getXbsmc() : null);
                                if (tbVilla != null) {
                                    if ("直营".equals(tbVilla.getVxz())) {
                                        if (!list1.contains(zxrid)) {
                                            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(zxrid);
                                            if (employee != null) {
                                                list2.add(employee.getMobile());
                                            }
                                            list1.add(zxrid);
                                        }
                                        Map<String, Object> map = new HashMap<>();
                                        map.put("zxr", zxr);
                                        map.put("bsmc", bsmc);
                                        map.put("jcsj", jcsj);
                                        map.put("tcsj", tcsj);
                                        Date gmtCreate = l.getDate("gmtCreate");
                                        map.put("cjsj", gmtCreate);
                                        map.put("city", city);
                                        list.add(map);
                                    }
                                }
                            }
                        }

                    }
                });
            }
        });
        map1.put("data", list);
        map1.put("mobile", list2);
        return map1;
    }

    /**
     * 进场及消费
     */
    public List<String> wZxJcLc(DkBbJqr j, Integer sum) {
        //进场及消费
        Map<String, Object> map1 = queryWZxJcLc(j, sum);
        List<Map<String, Object>> list = (List<Map<String, Object>>) map1.get("data");
        List<String> mobile = (List<String>) map1.get("mobile");
        if (list != null && list.size() > 0) {
            String c = "#### <font color=#ff0303 size=7>客户已进场，流程未跟进名单</font>";
            if (list != null && list.size() > 0) {
                c += "\n\n><font color=#1a75ff>点击下面的图片可以查看全部数据</font>";
                String sc = null;
                try {
                    sc = xdJGdTpSc(list, "收尾款流程");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                c += "\n\n> ![图片](" + sc + ")";
            } else {
                c += "\n\n>店长们，你们辛苦啦，再接再厉[送花花][送花花]";
            }
            DingDingUtil.sendMark(j.getWebhook(), j.getSecret(), "客户已进场消费进度播报", c, null, false);
        }
        return mobile;
    }

    /**
     * 未处理进场流程收定金提醒
     */
    @RequestMapping(value = "queryJcWcl")
    public void queryJcWcl() {
        //当前时间
        Calendar c1 = Calendar.getInstance();
        c1.set(Calendar.MINUTE, 0);
        c1.set(Calendar.SECOND, 0);
        c1.set(Calendar.MILLISECOND, 0);
        int i = c1.get(Calendar.HOUR_OF_DAY);
        List<DkBbJqr> list = weiLianDdXcxService.queryLcBbJqr();
        list.forEach(l1 -> {
            List<String> mobile = new ArrayList<>();
            //文纯
//            mobile.add("18674805754");
            //数据专员
            mobile.add("19974916615");
            List<String> list1;
            if (i == 11) {
                list1 = wZxJcLc(l1, 1);
            } else {
                list1 = wZxJcLc(l1, 2);
            }
            List<String> bbAt = weiLianDdXcxService.queryJqrBbAt(l1.getCity());
            bbAt.forEach(l -> {
                if (!mobile.contains(l)) {
                    mobile.add(l);
                }
            });
            if (list1.size() > 0) {
                DingDingUtil.sendMsg(l1.getWebhook(), l1.getSecret(), "客户已经进场1小时，店长还未收尾款和押金", mobile, false);
            }
        });

    }

    /**
     * 上传对账表单
     *
     * @param tbXyd 协议单对象
     */
    public void scDzBd(TbXyd tbXyd, DingdingEmployee employee2, TbVilla villa, String userid, Dingkey dingkey, YdAppkey ydAppkey, YdBd ydBd) {
        //本场基准价
        Map<String, Object> map = new HashMap<>();
        map.put("jcsj", tbXyd.getXjctime());
        map.put("tcsj", tbXyd.getXtctime());
        map.put("vid", tbXyd.getXbsmc());
        Double jzj = weiLianDdXcxService.queryKdjJzj(map);
        WlgbBdjl wlgbBdjl2 = wlgbBdjlService.queryByXidAndBz(tbXyd.getXid(), "对账表单提交");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(tbXyd.getXid());
        String xydUrl = dzXydCl(tbXyd);
        WlgbBdjl wlgbBdjl = DzBdConfig.scDzBd(tbXyd, employee2, villa, ding, dingkey, wlgbBdjl2, jzj, xydUrl, ydAppkey, ydBd, tbYddXyd != null ? "是" : "否");
        if (wlgbBdjl != null) {
            if (wlgbBdjl2 != null) {
                wlgbBdjlService.updateById(wlgbBdjl);
            } else {
                wlgbBdjlService.save(wlgbBdjl);
            }
        }
    }

    /**
     * 宜搭图片上传截取地址
     *
     * @param json 上传的json图片地址
     * @return 截取后的图片地址
     */
    public String ydTpScJq(String json) {
        JSONArray objects = JSONObject.parseArray(json);
        if (objects.size() > 0) {
            String previewUrl = objects.getJSONObject(0).getString("previewUrl");
            if (previewUrl == null) {
                return null;
            } else {
                if (previewUrl.length() > 8) {
                    String substring = previewUrl.substring(0, 8);
                    if ("https://".equals(substring)) {
                        return previewUrl;
                    } else {
                        return "https://jztdpp.aliwork.com" + previewUrl;
                    }
                } else {
                    return "https://jztdpp.aliwork.com" + previewUrl;
                }

            }
        }
        return null;
    }

    /**
     * 新增fwq_bbb新员工
     */
//    @Scheduled(cron = "0 43 2 * * ?")
    @RequestMapping(value = "WlgbDdgxryxx")
    public void WlgbDdgxryxx() {
        List<DingdingEmployee> dingdingEmployeeList = weiLianService.queryDingdingEmployeeList();
        if (dingdingEmployeeList != null || dingdingEmployeeList.size() > 0) {
            int size = dingdingEmployeeList.size();
            for (int i = 0; i < size; i++) {
                String userid = dingdingEmployeeList.get(i).getUserid();
                String name = dingdingEmployeeList.get(i).getName();
                String[] arr = {
                        "王子伟", "范伟旗", "许振飞"
                };
                List<String> list = Arrays.asList(arr);
                FwqBbb fwqBbb1 = weiLianService.queryBbbByXfd(name);
                if (!list.contains(name)) {
                    if (fwqBbb1 == null) {
                        FwqBbb fwqBbb = new FwqBbb();
                        String xfd = "你的小";
                        String name1 = name.substring(name.length() - 1);
                        xfd = xfd + name1 + name1 + name;
                        fwqBbb.setXnc(xfd);
                        fwqBbb.setXddid(userid);
                        fwqBbb.setXfd(name);
                        fwqBbbService.save(fwqBbb);
                    }
                }
            }
        }
    }

    /**
     * 招聘群播报
     */
//    @Scheduled(cron = "0 5 9 * * ?")
    @RequestMapping(value = "hrDataBb")
    public void hrDataBb() throws Exception {
        List<HrMsz> list = weiLianService.queryHrYcSj();
        if (list.size() > 0) {
            Map<String, Object> map = new HashMap<>();
            String str = "<table><tr>\n" +
                    "                    <th width='13%'>责任人</th>\n" +
                    "                    <th width='14%'>面试时间</th>\n" +
                    "                    <th width='20%'>岗位</th>\n" +
                    "                    <th width='13%'>姓名</th>\n" +
                    "                    <th width='40%'>错误提示</th>\n" +
                    "                </tr>";
            for (int i = 0; i < list.size(); i++) {
                HrMsz hrMsz = list.get(i);
                str += "<tr><td>" + (hrMsz.getYyrname() != null && !"".equals(hrMsz.getYyrname()) ? hrMsz.getYyrname() : "") + "</td>";
                str += "<td>" + (hrMsz.getMsdata() != null && !"".equals(hrMsz.getMsdata()) ? hrMsz.getMsdata() : "") + "</td>";
                str += "<td>" + (hrMsz.getGwcity() != null && !"".equals(hrMsz.getGwcity()) ? hrMsz.getGwcity() : "") + "</td>";
                str += "<td>" + (hrMsz.getName() != null && !"".equals(hrMsz.getName()) ? hrMsz.getName() : "") + "</td>";
                str += "<td>" + (hrMsz.getBeizhu() != null && !"".equals(hrMsz.getBeizhu()) ? hrMsz.getBeizhu() : "") + "</td></tr>";
            }
            str += "</table>";
            map.put("title", "【请注意招聘系统的规范使用，以下错误请马上处理】");
            map.put("str", str);
            String tempName = FileConfig.getFileAbsolutePath2("static" + File.separator + "Pdf.html");
            ;
            String context = PDFUtil.freeMarkerRender(map, tempName);
            String bddz = PathUtil.getClassResources();
            String id = UUID.randomUUID().toString().replace("-", "");
            String pdf = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
            String png = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".png");
            File newPdf = new File(bddz + pdf);
            File newPng = new File(bddz + png);
            //生成pdf
            PDFUtil.createPdf1(context, newPdf.getPath(), 800, list != null ? list.size() > 3 ? (list.size() + 1) * 40 : 180 : 180);
            //生成图片
            PDFUtil.pdfToImg(newPdf.getPath(), 1, newPng.getPath());
            FileInputStream fileInputStream = new FileInputStream(newPng);
            MultipartFile multipartFile = new MockMultipartFile(newPng.getName(), fileInputStream);
            fileInputStream.close();
            String upload = ossFileService.upload(multipartFile);
            if (upload != null && !"".equals(upload)) {
                upload = upload.replace("https://appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
            }
            newPdf.delete();
            newPng.delete();
            String b = "#### 请注意招聘系统的规范使用，以下错误请马上处理，共<font color=#ff0303>" + list.size() + "</font>项";
            b += "\n\n><font color=#1a75ff>点击下面的图片可以查看全部数据</font>";
            b += "\n\n> ![图片](" + upload + ")";

            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar1 = Calendar.getInstance();
            b += "\n\n> ###### " + df2.format(calendar1.getTime()) + "     [发送]";
            DingDingUtil.sendMark("https://oapi.dingtalk.com/robot/send?access_token=7631e6716f1c18a77ca07f57a641219c0effe5448279d836bf5883a901321c2c", "SEC659cb715683b010c280c322494f476114d0671be1c8d370f5778f77b0f095a71", "今日数据播报", b, null, true);
//                DingDingUtil.sendMark("https://oapi.dingtalk.com/robot/send?access_token=3c763d5efa8d7be33af091ee5cdb9753ebfed67ef6502a77ab07bb801d67f6d5", "SEC0b2dca1c21646ea0664cdc643e39969f6062ba28790033daab3c779f25351360", "今日数据播报", b, null, true);
        }
    }

    /**
     * 获取人员信息
     */
//    @Scheduled(cron = "0 20 3 * * ?")
    @RequestMapping(value = "hqXx")
    public void hqXx() {
        List<DingdingEmployee> list = weiLianService.queryDingdingEmployeeList();
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        list.forEach(l -> {
            int count = crmRyxxService.queryCountByUserId(l.getUserid());
            if (count == 0) {
                JSONObject jsonObject = DingHmc.hqHmcByUserId(l.getUserid(), dingkey);
                if (jsonObject.getBoolean("success")) {
                    JSONArray result = jsonObject.getJSONArray("result");
                    if (result != null && result.size() > 0) {
                        JSONObject jsonObject1 = result.getJSONObject(0);
                        String userid = jsonObject1.getString("userid");
                        JSONArray field_list = jsonObject1.getJSONArray("field_list");
                        CrmRyxx crmRyxx = new CrmRyxx();
                        crmRyxx.setUserid(userid);
                        for (int i = 0; i < field_list.size(); i++) {
                            JSONObject jsonObject2 = field_list.getJSONObject(i);
                            if ("姓名".equals(jsonObject2.getString("field_name"))) {
                                crmRyxx.setName(jsonObject2.getString("value"));
                            }
                            if ("联系人电话".equals(jsonObject2.getString("field_name"))) {
                                crmRyxx.setMobile(jsonObject2.getString("value"));
                            }
                            if ("工号".equals(jsonObject2.getString("field_name"))) {
                                crmRyxx.setJobnumber(jsonObject2.getString("value"));
                            }
                            if ("入职时间".equals(jsonObject2.getString("field_name"))) {
                                crmRyxx.setRzsj(jsonObject2.getString("value"));
                            }
                            if ("办公地点".equals(jsonObject2.getString("field_name"))) {
                                crmRyxx.setBgdd(jsonObject2.getString("value"));
                            }
                            if ("职位".equals(jsonObject2.getString("field_name"))) {
                                crmRyxx.setGw(jsonObject2.getString("value"));
                            }
                            if ("部门".equals(jsonObject2.getString("field_name"))) {
                                crmRyxx.setBm(jsonObject2.getString("value"));
                            }
                            if ("部门id".equals(jsonObject2.getString("field_name"))) {
                                crmRyxx.setBmid(jsonObject2.getString("value"));
                            }
                        }
                        crmRyxxService.save(crmRyxx);
                    }
                }
            }
        });
    }

    /**
     * 获取待办列表
     */
//    @Scheduled(cron = "0 26 4 * * ?")
    @RequestMapping(value = "dingDb")
    public void dingDb() {
        //清空待办列表
        weiLianDaiBanService.qkDbLb();
        //获取数据
        List<DingdingEmployee> list1 = weiLianService.queryDingdingEmployeeList();
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        list1.forEach(l2 -> {
            boolean b = true;
            int count = 0;
            List<JSONObject> list2 = new ArrayList<>();
            while (b) {
                String body = DingDBzxGet.hqWwcDb(dingkey, l2.getUserid(), Long.parseLong(String.valueOf(count * 50)));
                JSONObject jsonObject = JSONObject.parseObject(body);
                String errmsg = jsonObject.getString("errmsg");
                if ("ok".equals(errmsg)) {
                    JSONObject result = jsonObject.getJSONObject("result");
                    if (result.getBoolean("has_more")) {
                        JSONArray list = result.getJSONArray("list");
                        list.forEach(l -> {
                            JSONObject l1 = (JSONObject) l;
                            JSONObject jsonObject1 = new JSONObject();
                            jsonObject1.put("slid", l1.getString("instance_id"));
                            jsonObject1.put("title", l1.getString("title"));
                            jsonObject1.put("url", l1.getString("url"));
                            list2.add(jsonObject1);
                        });
                        if (list.size() != 50) {
                            b = false;
                        }
                        count += 1;
                    } else {
                        b = false;
                    }
                } else {
                    b = false;
                }
            }
            sjCl(list2, dingkey, l2);
        });
    }

    /**
     * 待办列表数据处理
     */
    public void sjCl(List<JSONObject> list2, Dingkey dingkey, DingdingEmployee ding) {
        list2.forEach(l -> {
            try {
                String body = DingDBzxGet.hqWwcDbXq(dingkey, l.getString("slid"));
                JSONObject jsonObject = JSONObject.parseObject(body);
                String errmsg = jsonObject.getString("errmsg");
                if ("ok".equalsIgnoreCase(errmsg)) {
                    JSONObject process_instance = jsonObject.getJSONObject("process_instance");
                    WlgbDbwcw wlgbDbwcw = new WlgbDbwcw();
                    wlgbDbwcw.setSpbh(process_instance.getString("business_id"));
                    wlgbDbwcw.setSpfqsj(process_instance.getDate("create_time"));
                    wlgbDbwcw.setTitle(process_instance.getString("title"));
                    wlgbDbwcw.setSpfqrbm(process_instance.getString("originator_dept_name") != null ? process_instance.getString("originator_dept_name") : "来源宜搭");
                    DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(process_instance.getString("originator_userid"));
                    wlgbDbwcw.setSpfqr(dingdingEmployee != null ? dingdingEmployee.getName() : null);
                    wlgbDbwcw.setSpfqrid(dingdingEmployee != null ? dingdingEmployee.getUserid() : null);
                    JSONArray tasks = process_instance.getJSONArray("tasks");
                    tasks.forEach(l2 -> {
                        JSONObject l1 = (JSONObject) l2;
                        if ("RUNNING".equals(l1.getString("task_status")) && ding.getUserid().equals(l1.getString("userid"))) {
                            wlgbDbwcw.setSpjdsj(l1.getDate("create_time"));
                            DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(l1.getString("userid"));
                            wlgbDbwcw.setSpr(dingdingEmployee1 != null ? dingdingEmployee1.getName() : null);
                            wlgbDbwcw.setSprid(dingdingEmployee1 != null ? dingdingEmployee1.getUserid() : null);
                            wlgbDbwcw.setRwzt(l1.getString("task_status"));
                            wlgbDbwcw.setUrl(l.getString("url"));
                            return;
                        }
                    });
                    wlgbDbwcwService.save(wlgbDbwcw);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    /**
     * 总公司token
     *
     * @return
     * @throws ApiException
     */
    public String token() throws ApiException {
        DefaultDingTalkClient client1 = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest request1 = new OapiGettokenRequest();
        request1.setAppkey("ding291dcdb4d2ea030935c2f4657eb6378f");
        request1.setAppsecret("rwLkbETYd_YmO80p6thV5uRCXTspi9oaR9CXXKUj8rDmh0SVIdSpLPCnFyEiZN6A");
        request1.setHttpMethod("GET");
        OapiGettokenResponse response1 = client1.execute(request1);
        String token = response1.getAccessToken();

        return token;
    }

    /**
     * 获取部门详情
     */
    public CrmBmxq getBmxq(String userid, String name) throws ApiException {
        String token = token();
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/department/list_parent_depts");
        OapiDepartmentListParentDeptsRequest request = new OapiDepartmentListParentDeptsRequest();
        request.setUserId(userid);
        request.setHttpMethod("GET");
        OapiDepartmentListParentDeptsResponse response = client.execute(request, token);
        CrmBmxq bmxq = new CrmBmxq();
        try {
            if (response.getDepartment() != null) {
                String bumen = response.getDepartment().replace("[", "");//去除[
                bumen = bumen.replace("]", "");//去除 ]
                bumen = bumen.replace(" ", "");//去除空格
                String[] bmid = bumen.trim().split(",");//将id分别存入String数组
                List<String> bmidl = Arrays.asList(bmid);
                HashSet<String> h = new HashSet<>(bmidl);
                List<String> bmidlist = new ArrayList<>();
                bmidlist.addAll(h);
                bmxq.setUserid(userid);
                bmxq.setName(name);
                if (bmidlist.size() > 0) {
                    bmxq.setBmid(bmidlist.get(0));
                }
                if (bmidlist.size() > 1) {
                    bmxq.setBmid2(bmidlist.get(1));
                }
                if (bmidlist.size() > 2) {
                    bmxq.setBmid3(bmidlist.get(2));
                }
                if (bmidlist.size() > 3) {
                    bmxq.setBmid4(bmidlist.get(3));
                }
                if (bmidlist.size() > 4) {
                    bmxq.setBmid5(bmidlist.get(4));
                }
                if (bmidlist.size() > 5) {
                    bmxq.setBmid6(bmidlist.get(5));
                }
                if (bmidlist.size() > 6) {
                    bmxq.setBmid7(bmidlist.get(6));
                }
                if (bmidlist.size() > 7) {
                    bmxq.setBmid8(bmidlist.get(7));
                }
                if (bmidlist.size() > 8) {
                    bmxq.setBmid9(bmidlist.get(8));
                }
                if (bmidlist.size() > 9) {
                    bmxq.setBmid10(bmidlist.get(9));
                }
                if (bmidlist.size() > 10) {
                    bmxq.setBmid11(bmidlist.get(10));
                }
                if (bmidlist.size() > 11) {
                    bmxq.setBmid12(bmidlist.get(11));
                }
                if (bmidlist.size() > 12) {
                    bmxq.setBmid13(bmidlist.get(12));
                }
                if (bmidlist.size() > 13) {
                    bmxq.setBmid14(bmidlist.get(13));
                }
                if (bmidlist.size() > 14) {
                    bmxq.setBmid15(bmidlist.get(14));
                }
                sleep(500);
                return bmxq;
            }
        } catch (Exception e) {
            e.printStackTrace();
            Dingkey dingkey1 = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            DingQunSend.send("部门详情查询失败：" + e, dingkey1, "chate5bd183f6a0ebdd18e057550c487e5cc");
        }
        return bmxq;

    }

    /**
     * 更新crm部门详情表
     */
//    @Scheduled(cron = "0 40 3 * * ?")
    @RequestMapping(value = "getBmxq")
    public void getBmxq() throws ApiException {
        Dingkey dingkey1 = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        System.out.println("crm_bmxq 开始更新");
        try {
            List<CrmUser> useridlist = weiLianService.queryCrmUserList();
            List<CrmBmxq> Lbmxq = new ArrayList<>();
            for (int i = 0; i < useridlist.size(); i++) {
                System.out.println("第" + i + "个" + useridlist.get(i));
                CrmBmxq bmxq = getBmxq(useridlist.get(i).getUserid(), useridlist.get(i).getName());
                Lbmxq.add(i, bmxq);
            }
            weiLianService.qkCrmBmXqTable();
            for (CrmBmxq crmBmxq : Lbmxq) {
                crmBmxqService.save(crmBmxq);
            }
        } catch (Exception e) {
            DingQunSend.send("部门详情添加失败：" + e, dingkey1, "chate5bd183f6a0ebdd18e057550c487e5cc");
        }
        System.out.println("crm_bmxq 更新完毕");
    }

    //部门新增
    public List csjkxz() throws ApiException {
        String token = token();
        List<Long> list1 = new ArrayList<>();
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/department/list");
            OapiDepartmentListRequest request = new OapiDepartmentListRequest();
            request.setId("1");
            request.setHttpMethod("GET");
            OapiDepartmentListResponse response = client.execute(request, token);
            //将获取的数据转化成json数据，并单独将部门id取出
            JSONObject obj = JSONObject.parseObject(response.getBody());
            JSONArray data = obj.getJSONArray("department");

            //截断部门表
            weiLianService.qkCrmBmTable();
            for (Object s : data) {
                CrmBm bmentity = new CrmBm();
                JSONObject mapbm = (JSONObject) s;
                long idd = mapbm.getLong("id");
                list1.add(idd);
                bmentity.setBmid(mapbm.getString("id"));
                bmentity.setBmname(mapbm.getString("name"));
                bmentity.setParentid(mapbm.getString("parentid"));
                crmBmService.save(bmentity);
                //移除不显示的部门人员
                if (idd == 33502412) {
                    list1.remove(idd);
                }
            }
        } catch (Exception e) {
            Dingkey dingkey1 = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            DingQunSend.send("部门新增失败" + e, dingkey1, "chate5bd183f6a0ebdd18e057550c487e5cc");
        }
        return list1;
    }

    /**
     * (钉钉端)根据部门id遍历得到全体员工信息
     *
     * @return
     * @throws ApiException
     */
    public List<CrmUser> getUser() throws ApiException {
        String token = token();
        List<CrmUser> userlist = new ArrayList<>();
        try {
            int num = 0;
            //GetUserlist();
            List bmlist = csjkxz();
            for (Object o : bmlist) {
                String ids = o.toString();
                Long id = Long.parseLong(ids);
                DingTalkClient client1 = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/listbypage");
                OapiUserListbypageRequest request1 = new OapiUserListbypageRequest();
                request1.setDepartmentId(id);
                request1.setOffset(0L);
                request1.setSize(100L);
                request1.setOrder("entry_desc");
                request1.setHttpMethod("GET");
                OapiUserListbypageResponse execute = client1.execute(request1, token);
                JSONObject objuser = JSONObject.parseObject(execute.getBody());
                JSONArray datauser = objuser.getJSONArray("userlist");
                for (Object u : datauser) {
                    CrmUser userEntity = new CrmUser();
                    JSONObject mapuser = (JSONObject) u;
                    userEntity.setUserid(mapuser.getString("userid") != null ? mapuser.getString("userid") : "");
                    userEntity.setName(mapuser.getString("name"));
                    userEntity.setAvatar(mapuser.getString("avatar"));
                    userEntity.setMobile(mapuser.getString("mobile"));
                    System.out.println(userEntity.getName());
                    userlist.add(num, userEntity);
                    num = num + 1;
                }
            }
        } catch (Exception e) {
            Dingkey dingkey1 = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            DingQunSend.send("人员详情查询失败：" + e, dingkey1, "chate5bd183f6a0ebdd18e057550c487e5cc");
            e.printStackTrace();
        }
        return userlist;
    }

    /**
     * 更新crm用户表
     */
//    @Scheduled(cron = "0 15 3 * * ?")
    @RequestMapping(value = "getUList")
    public void getUList() {
        Dingkey dingkey1 = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        try {
            List<CrmUser> list = getUser();
            CrmUser userEntity = new CrmUser();
            //截断crm_user_backups 人员备份表
            weiLianService.qkCrmUserBackupsTable();
            //将crm_user人员表 赋值给 crm_user_backups 人员备份表
            weiLianService.bfCrmUserInBackups();
            weiLianService.qkCrmUserCopyTable();
            if (list.size() > 0) {
                for (CrmUser crmUser : list) {
                    userEntity.setUserid(crmUser.getUserid());
                    userEntity.setName(crmUser.getName());
                    userEntity.setMobile(crmUser.getMobile());
                    userEntity.setAvatar(crmUser.getAvatar());
                    weiLianService.saveCrmUserCopy(userEntity);
                }
            }
            //去除重复人员
            weiLianService.delCrmUserCopy();
            //截断crm_user表
            weiLianService.qkCrmUserTable();
            weiLianService.saveCrmUser();

        } catch (Exception e) {
            e.printStackTrace();
            try {
                DingQunSend.send("人员详情添加失败：" + e, dingkey1, "chate5bd183f6a0ebdd18e057550c487e5cc");
            } catch (ApiException apiException) {
                apiException.printStackTrace();
            }
        }
    }

    /**
     * 查询固定资产，同步到转移物品信息
     */
//    @Scheduled(cron = "0 6 13,19 * * ?")
    @RequestMapping(value = "tbZywpxx")
    public void tbZywpxx() {
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        List<JSONObject> list3 = new ArrayList<>();
        String formUuid = "FORM-6J8668A14XLUZZ4VX7VA8GL41IWQ1RAUEYYUK73";
        String s = YdConfig.getFromDatas("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, "1", "", "", null);
        JSONObject object = JSONObject.parseObject(s);

        int count = object.getJSONObject("result") != null ? object.getJSONObject("result").getInteger("totalCount") : 0;

        double b = count / 100.00;
        int a = (int) b;
        int c = b > a ? a + 1 : a;
        if (c > 0) {
            if (object.getJSONObject("result") != null) {
                if (object.getJSONObject("result").getJSONArray("data") != null || object.getJSONObject("result").getJSONArray("data").size() > 0) {
                    JSONArray jsonArray = object.getJSONObject("result").getJSONArray("data");
                    if (jsonArray.size() > 0) {
                        jsonArray.forEach(l -> list3.add((JSONObject) l));
                    }
                }
            }

        }
        for (int i = 2; i <= c; i++) {
            String lcid1 = YdConfig.getFromDatas("15349026426046931", ydAppkey.getAppkey(), ydAppkey.getToken(), formUuid, i + "", "", "", null);
            JSONObject jsonObject2 = JSONObject.parseObject(lcid1);
            if (jsonObject2 != null) {
                if (jsonObject2.getJSONObject("result") != null) {
                    if (jsonObject2.getJSONObject("result").getJSONArray("data") != null || jsonObject2.getJSONObject("result").getJSONArray("data").size() > 0) {
                        JSONArray jsonArray = jsonObject2.getJSONObject("result").getJSONArray("data");
                        if (jsonArray.size() > 0) {
                            jsonArray.forEach(l -> list3.add((JSONObject) l));
                        }
                    }
                }
            }
        }
        list3.forEach(l -> {
            JSONObject data1 = l.getJSONObject("formData");
            //使用区域
            String syqy = data1.getString("zcqy");
            //流水号
            String lsh = data1.getString("lsh");
            //别墅
            String bsmc = data1.getString("bsmc");
            //物品名称
            String wpmc = data1.getString("wpmc");
            //品牌
            String brand = data1.getString("brand");
            //采购数量
            Double cgnum = data1.getDouble("cgnum");
            //负责人
            String fzr = data1.getString("fzr");
            //单位
            String dw = data1.getString("dw");
            //资产区域
            String zcqy = data1.getString("zcqy");
            //采购时间cgtime
            String cgtime = data1.getString("cgtime");
            //单价
            double dj;
            if ("".equals(data1.getString("dj")) || data1.getString("dj") == null) {
                dj = 0.0;
            } else {
                dj = Double.parseDouble(data1.getString("dj"));
            }

            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("zybs", bsmc);
            jsonObject1.put("zypm", wpmc);
            jsonObject1.put("syqy", syqy);
            jsonObject1.put("lsh", lsh);
            String slid1 = YdConfig.hqId("012412221639786136545", ydAppkey.getAppkey(), ydAppkey.getToken(), "FORM-78766VC1IMZV9OLYZYXILAXFEZJT1PUW9VVWK0", jsonObject1.toJSONString(), "1", "1");
            JSONObject object2 = JSONObject.parseObject(slid1);
            JSONObject jsonObject3 = object2.getJSONObject("result");
            if (jsonObject3 != null) {
                if (jsonObject3.getJSONArray("data") == null || jsonObject3.getJSONArray("data").size() == 0) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("zybs", bsmc != null && !"".equals(bsmc) ? bsmc : null);
                    map.put("zypm", wpmc != null && !"".equals(wpmc) ? wpmc : null);
                    map.put("lsh", lsh);
                    map.put("syqy", zcqy);
                    map.put("ppxh", brand);
                    map.put("sysl", cgnum);
                    map.put("dw", dw);
                    map.put("zjr", fzr);
                    map.put("dj", dj);
                    if (!"区域平摊".equals(bsmc) && !"区域办公室".equals(bsmc)) {
                        String kysj = weiLianDdXcxService.queryBsKySjByBsMc(bsmc);
                        if (kysj != null) {
                            map.put("cgsj", kysj);
                        }
                    }

                    xzbd("012412221639786136545", map, "物资移交表单同步");
                }
            }
        });
    }

    //    @Scheduled(cron = "0 56 11,22 * * ?")
    @RequestMapping(value = "dzBdTx")
    public void dzBdTx() {
        //发送对账表单数量
        Integer sl = weiLianService.queryFsDzbdSl();
        //实际订单数量
        Integer sl1 = weiLianService.querySyDdSl();
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String content = "截至当前近30天数据";
        content += "\n\n订单数量：" + sl1;
        content += "\n\n对账表单数据：" + sl;
        try {
            DingQunSend.send(content, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
        } catch (ApiException e) {
            e.printStackTrace();
        }
        List<WlgbOrderCyjl> list = weiLianDdXcxService.queryCyJlByz();
        if (list.size() > 0) {
            String text = "订餐问题存在" + list.size() + "笔订单";
            text += "\n\n订单编号为：";
            for (WlgbOrderCyjl w : list) {
                text += "\n\n" + w.getDdbh();
            }
            try {
                DingQunSend.send(text, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
        List<String> list1 = weiLianDdXcxService.queryCyJlDzByz();
        if (list1.size() > 0) {
            String text = "对账时间近30天餐饮对账完成问题存在" + list1.size() + "笔订单";
            text += "\n\n订单编号为：";
            for (String s : list1) {
                text += "\n\n" + s;
            }
            try {
                DingQunSend.send(text, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 上传场地费应收单（包括对完账的数据也会上传到金蝶）
     */
//    @Scheduled(cron = "0 25 15 * * ?")
    @RequestMapping(value = "scYsdCdf")
    public Result scYsdCdf() {
        weiLianDdXcxService.zxCdf();
        List<JSONObject> list = weiLianDdXcxService.queryCdfList();
        scysd(list);
        return Result.OK("OK");
    }

    @RequestMapping(value = "scYsdCdf2")
    public Result scYsdCdf2(HttpServletRequest req) {
        String xddbh = req.getParameter("xddbh");
        System.out.println("+++++++++参数订单编号+++++++++++" + xddbh);
        weiLianDdXcxService.zxCdf2(xddbh);
        List<JSONObject> list = weiLianDdXcxService.queryCdfList();
        scysd(list);
        return Result.OK("OK");
    }

    private void scysd(List<JSONObject> list) {
        list.forEach(jsonObject -> {
            String ddbh = jsonObject.getString("ddbh");
            String ztbm = jsonObject.getString("ztbm");
            String md = jsonObject.getString("md").replace("★", "");
            JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(md);
            log.info("********上传金蝶之前查看别墅在金蝶中是否存在******{}", jsonObject1);
            WlgbJdYsdCdfjl wlgbJdYsdCdfjl = JSONObject.toJavaObject(jsonObject, WlgbJdYsdCdfjl.class);
            if (wlgbJdYsdCdfjl != null) {
                wlgbJdYsdCdfjl.setId(null);
                WlgbJdYsdCdfjl wlgbJdYsdCdfjl1 = wlgbJdYsdCdfjlService.queryByDdBhAndSfSc(wlgbJdYsdCdfjl.getDdbh());
                Boolean success = jsonObject1.getBoolean("success");
                Boolean t;
                String bm = jsonObject1.getString("bm");
                if (wlgbJdYsdCdfjl1 != null) {
                    if (wlgbJdYsdCdfjl1.getSflrjd() == 0) {
                        if (success) {
                            t = KingDeeConfig.cdf(jsonObject, bm, ztbm);
                            //上传金蝶后新增场地费记录
                            if (t) {
                                WlgbJdYsdCdfjl wlgbJdYsdCdfjl2 = new WlgbJdYsdCdfjl();
                                wlgbJdYsdCdfjl2.setSflrjd(1);
                                wlgbJdYsdCdfjl2.setId(wlgbJdYsdCdfjl1.getId());
                                wlgbJdYsdCdfjlService.updateById(wlgbJdYsdCdfjl2);
                            }
                        }
                    } else {
                        sendGztz(5, ddbh, "已经录入金蝶了", "");
                    }
                } else {
                    if (success) {
                        t = KingDeeConfig.cdf(jsonObject, bm, ztbm);
                        //上传金蝶后新增场地费记录
                        if (t) {
                            wlgbJdYsdCdfjl.setSflrjd(1);
                        }
                    } else {
                        sendGztz(5, ddbh, "门店缺失，异常", "");
                    }
                    wlgbJdYsdCdfjlService.save(wlgbJdYsdCdfjl);
                }
            }
        });
    }

    /**
     * 发送工作通知
     *
     * @param type 审批类型
     * @param spbh 审批编号
     * @param yy   错误原因
     */
    public void sendGztz(Integer type, String spbh, String yy, String sp) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String context1 = "单据上传金蝶失败了";
        context1 += "\n\n原因：" + yy;
        context1 += "\n审批编号：" + spbh;
        if (type == 0) {
            context1 += "\n审批单据：" + sp + "审批";
        } else if (type == 4) {
            context1 += "\n表单上传：对公转账";
        } else if (type == 5) {
            context1 += "\n表单上传：场地费转应收单";
        } else if (type == 6) {
            context1 += "\n表单上传：新美大核销券码";
        } else if (type == 7) {
            context1 += "\n表单上传：对账完成多退少补";
        }
        context1 += "\n送达时间：" + df2.format(new Date());
        try {
            DingDBConfig.sendGztzText(dingkey, "15349026426046931", context1);
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    /**
     * 对公转账上传金蝶
     */
//    @Scheduled(cron = "0 52 0 * * ?")
    @RequestMapping(value = "queryDgZzSc")
    public void queryDgZzSc() {
        WlgbJdYhk wlgbJdYhk = wlgbJdYhkService.querySfScAndLikeBz(0, "对公转账");
        List<WlgbJdDjbbd> list = weiLianDdXcxService.queryDgZzWscJdList();
        for (WlgbJdDjbbd l : list) {
            Boolean skd3 = KingDeeConfig.saveSkd3(l.getSkbh(), l.getJe(), "", "", "对公转账", l.getDzsj(), wlgbJdYhk != null ? wlgbJdYhk.getZh() : "");
            if (skd3) {
                WlgbJdDjbbd wlgbJdDjbbd = new WlgbJdDjbbd();
                wlgbJdDjbbd.setId(l.getId());
                wlgbJdDjbbd.setSflrjd(1);
                wlgbJdDjbbdService.updateById(wlgbJdDjbbd);
            } else {
                sendGztz(4, l.getLsh(), "对公转账上传收款单失败", "");
            }
        }
    }

    /**
     * 上传美团收款单
     */
//    @Scheduled(cron = "0 6 2 * * ?")
    @RequestMapping(value = "scMtSkd")
    public void scMtSkd() {
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        WlgbJdYhk wlgbJdYhk = wlgbJdYhkService.querySfScAndLikeBz(0, "定金");
        List<JSONObject> list = weiLianDdXcxService.queryYhxQm();
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        for (JSONObject json : list) {
            String ddbh = json.getString("ddbh");
            TbXyd tbXyd = tbXydService.queryByDdBh(ddbh);
            String mdlx = "102";
            String bm = "";
            if (tbXyd != null) {
                TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
                if (villa != null) {
                    if ("加盟".equals(villa.getVmdsstz())) {
                        mdlx = "101";
                    } else if ("威廉集团".equals(villa.getVmdsstz())) {
                        mdlx = "100";
                    }
                    JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(villa.getVname());
                    Boolean success = jsonObject1.getBoolean("success");
                    if (success) {
                        bm = jsonObject1.getString("bm");
                    }
                }
            }
            Integer sspt = json.getInteger("sspt");

            Boolean skd1 = KingDeeConfig.saveSkd5(json.getDouble("jsj"), ddbh, json.getString("qh"), sspt == 1 ? "美团" : "抖音", json.getDate("yqtime"), bm, mdlx, wlgbJdYhk != null ? wlgbJdYhk.getZh() : "");
            if (skd1) {
                String id = json.getString("id");
                WlgbJdDjbbd wlgbJdDjbbd = new WlgbJdDjbbd();
                wlgbJdDjbbd.setId(id);
                wlgbJdDjbbd.setSflrjd(1);
                wlgbJdDjbbdService.updateById(wlgbJdDjbbd);


                JSONObject jsonObject = new JSONObject();
                jsonObject.put("sfhk", "1");
                GatewayResult gatewayResult;
                try {
                    gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, json.getString("fqr_id"), json.getString("form_inst_id"), jsonObject.toJSONString());
                } catch (Exception e) {
                    gatewayResult = new GatewayResult();
                    e.printStackTrace();
                }

                System.out.println(gatewayResult.toString());
            } else {
                sendGztz(6, json.getString("qh"), "同步（" + (sspt == 1 ? "美团" : "抖音") + "）金蝶失败", "");
            }
        }
    }

    /**
     * 执行费用科目存储
     */
//    @Scheduled(cron = "0 16 1 * * ?")
    @RequestMapping(value = "zxFyKm")
    public void zxFyKm() {
        K3CloudApi k3CloudApi = new K3CloudApi();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FormId", "BD_Account");
        jsonObject.put("FieldKeys", "FNumber,FName,FFullName");
        jsonObject.put("Limit", "10000");

        List<JSONObject> jsonObjectList = new ArrayList<>();
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("Left", "(");
        jsonObject1.put("FieldName", "FUseOrgId");
        jsonObject1.put("Compare", "=");
        jsonObject1.put("Value", "102");
        jsonObject1.put("Right", ")");
        jsonObject1.put("Logic", "AND");
        jsonObjectList.add(jsonObject1);

        jsonObject.put("FilterString", jsonObjectList);

        List<List<Object>> lists = null;
        try {
            lists = k3CloudApi.executeBillQuery(jsonObject.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<WlgbJdFykm> list = new ArrayList<>();
        lists.forEach(l -> {
            WlgbJdFykm wlgbJdFykm = new WlgbJdFykm();
            wlgbJdFykm.setBm(l.get(0) != null ? l.get(0).toString() : "");
            wlgbJdFykm.setMc(l.get(1) != null ? l.get(1).toString() : "");
            wlgbJdFykm.setQm(l.get(2) != null ? l.get(2).toString() : "");
            list.add(wlgbJdFykm);
        });
        List<WlgbJdFykm> newList = new ArrayList<>(list.size());
        list.forEach(i -> {
            if (!newList.contains(i)) {
                // 如果新集合中不存在则插入
                newList.add(i);
            }
        });
        if (newList.size() > 0) {
            weiLianDdXcxService.qkJdFyKmTable();
            wlgbJdFykmService.saveBatch(newList);
        }
    }

    /**
     * 发送人事招聘日志
     */
    //     @Scheduled(cron = "0 58 17 * * ?")
    @RequestMapping(value = "hrFsRz")
    public void hrFsRz() {
        List<Map<String, Object>> list = weiLianDdXcxService.queryHrGzRzFsr();
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_J7KY7FDIH52I23EK327R");
        ydAppkey.setToken("E1666M91QHIQ9M3Y37EYM88SNWFU20MQCX7PKW5");
        String finalToken = token;
        list.forEach(l -> {
            JSONObject jsonObject = new JSONObject();
            Integer jrDm = weiLianService.queryHrJrDmByYyName(l.get("name") != null ? l.get("name").toString() : "");
            Integer tg = weiLianService.queryHrJrTgByYyName(l.get("name") != null ? l.get("name").toString() : "");
            jsonObject.put("clr", l.get("userid") != null ? l.get("userid").toString() : "");
            jsonObject.put("rq", new Date());
            jsonObject.put("zrmbsfwc", "否");
            jsonObject.put("jrzhlsfwc", "否");
            jsonObject.put("jtdmrs", jrDm);
            jsonObject.put("jrmstgrs", tg);
            jsonObject.put("jrjl", "未处理");
            jsonObject.put("msyqsxs", 0);
            jsonObject.put("msyqsdh", 0);
            jsonObject.put("mrmbdm", 0);
            jsonObject.put("mrmbtg", 0);
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = DingBdLcConfig.fqXzLcSl(finalToken, ydAppkey, "012412221639786136545", "FORM-O6966WB1XD0U52Q32U7JXA1X3S3A3KBQZW3UKS", "TPROC--O6966WB1XD0U52Q32U7JXA1X3S3A3KBQZW3UKT", jsonObject.toJSONString());
            } catch (Exception e) {
                e.printStackTrace();
            }
//            System.out.println(gatewayResult);
        });
    }

    /**
     * 一线报修数据处理
     */
    //    @Scheduled(cron = "0 25 3 * * ?")
    @RequestMapping(value = "yxBx")
    public void yxBx() {
        List<WlgbJdYxcgsqjl> list = wlgbJdYxcgsqjlService.queryBySfLrJd(0);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        list.forEach(l -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("textField_kpt1mxko", l.getSpbh());
            String zzBdSl = YdConfig.zzBdSl(ydAppkey.getAppkey(), ydAppkey.getToken(), "FORM-QA966CD1SIMQNH59249FPCYIO48628YH1MGPKHE", jsonObject.toJSONString());
            JSONObject jsonObject1 = JSONObject.parseObject(zzBdSl);
            if (jsonObject1.getBoolean("success")) {
                JSONObject jsonObject2 = jsonObject1.getJSONObject("result");
                if (jsonObject2.getJSONArray("data") != null && jsonObject2.getJSONArray("data").size() > 0) {
                    JSONObject data = jsonObject2.getJSONArray("data").getJSONObject(0);
                    l.setSpbt(data.getString("title"));
                    l.setSqrid(data.getString("creator"));
                    l.setSqr(data.getJSONObject("originator").getJSONObject("name").getString("zh_CN"));
                    System.out.println(data);
                    wlgbJdYxcgsqjlService.updateById(l);
                }
            }
        });
    }

    /**
     * 一线采购数据处理
     */
//    @Scheduled(cron = "0 30 3 * * ?")
    @RequestMapping(value = "yxCg")
    public void yxCg() {
        List<WlgbJdYxcgsqjl> list = wlgbJdYxcgsqjlService.queryBySfLrJd(0);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        list.forEach(l -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("textField_kpt1l40v", l.getSpbh());
            String zzBdSl = YdConfig.zzBdSl(ydAppkey.getAppkey(), ydAppkey.getToken(), "FORM-FO966391U1IQUPM1Y62542OW7J5S3V66UH6PK5", jsonObject.toJSONString());
            JSONObject jsonObject1 = JSONObject.parseObject(zzBdSl);
            if (jsonObject1.getBoolean("success")) {
                JSONObject jsonObject2 = jsonObject1.getJSONObject("result");
                if (jsonObject2.getJSONArray("data") != null && jsonObject2.getJSONArray("data").size() > 0) {
                    JSONObject data = jsonObject2.getJSONArray("data").getJSONObject(0);
                    l.setSpbt(data.getString("title"));
                    l.setSqrid(data.getString("creator"));
                    l.setSqr(data.getJSONObject("originator").getJSONObject("name").getString("zh_CN"));
                    System.out.println(data);
                    wlgbJdYxcgsqjlService.updateById(l);
                }
            }

        });
    }

    /**
     * 数据补录金蝶
     */
//    @Scheduled(cron = "0 50 3 * * ?")
    @RequestMapping(value = "spBlJd")
    public Result spBlJd() {
        List<WlgbJdYxcgsqjl> list = wlgbJdYxcgsqjlService.queryBySfLrJd(0);
        log.info("*********************{}", JSON.toJSONString(list));
        list.forEach(l -> {
            yxCgBxCl(l.getSpbh(), null, 1);
        });
        return Result.OK();
    }

    /**
     * 一线采购报修审批处理
     */
    public boolean yxCgBxCl(String spbh, WlgbJdYxcgsqjl wlgbJdYxcgsqjl, Integer type) {
        if (type == 1) {
            wlgbJdYxcgsqjl = wlgbJdYxcgsqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdYxcgsqjl != null) {
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String zzsj = df2.format(wlgbJdYxcgsqjl.getCnzzsj());
            String sqrbmid = wlgbJdYxcgsqjl.getSqrbmid();
            Boolean bmCl = KingDeeConfig.bmCl(sqrbmid, wlgbJdYxcgsqjl.getSqrbm());
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(wlgbJdYxcgsqjl.getSqrid(), wlgbJdYxcgsqjl.getSqr());
                if (ryCl) {
                    String bsmc = wlgbJdYxcgsqjl.getBsmc();
                    if (bsmc != null && !"".equals(bsmc)) {
                        //查询别墅表存不存在
                        TbVilla tbVilla = weiLianService.queryVillaByVname(bsmc);
                        //如果不存在
                        if (tbVilla == null) {
                            //去别墅修改记录表查询
                            WlgbJdBsmcXgjl wlgbJdBsmcXgjl = wlgbJdBsmcXgjlService.queryByYnameAndSfSc(bsmc, 0);
                            //别墅修改记录表存在
                            if (wlgbJdBsmcXgjl != null) {
                                //把别墅名字给上传使用
                                bsmc = wlgbJdBsmcXgjl.getVname();
                            }
                        }
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        Boolean success = jsonObject1.getBoolean("success");
                        if ("区域平摊".equals(bsmc) || "区域办公室".equals(bsmc)) {
                            success = true;
                        }
                        if (success) {
                            String bm = jsonObject1.getString("bm");
                            wlgbJdYxcgsqjl.setBsbh(bm);
                            List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(10, wlgbJdYxcgsqjl.getSpbh(), 0);
                            List<String> list2 = new ArrayList<>();
                            list.forEach(l -> {
                                if (!list2.contains(l.getZtbm())) {
                                    list2.add(l.getZtbm());
                                }
                            });
                            for (String l : list2) {
                                List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(10, wlgbJdYxcgsqjl.getSpbh(), 0, l);
                                String json = "{\n" +
                                        "    \"NeedUpDateFields\": [],\n" +
                                        "    \"NeedReturnFields\": [],\n" +
                                        "    \"IsDeleteEntry\": \"true\",\n" +
                                        "    \"SubSystemId\": \"\",\n" +
                                        "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                        "    \"IsEntryBatchFill\": \"true\",\n" +
                                        "    \"ValidateFlag\": \"true\",\n" +
                                        "    \"NumberSearch\": \"true\",\n" +
                                        "    \"IsAutoAdjustField\": \"false\",\n" +
                                        "    \"InterationFlags\": \"\",\n" +
                                        "    \"IgnoreInterationFlag\": \"\",\n" +
                                        "    \"Model\": {\n" +
                                        "        \"FID\": 0,\n" +
                                        "        \"FBillTypeID\": {\n" +
                                        "            \"FNUMBER\": \"" + ("代付".equals(wlgbJdYxcgsqjl.getFkfs()) ? "QTYFD01_SYS" : "QTYFD02_SYS") + "\"\n" +
                                        "        },\n" +
                                        "        \"FDATE\": \"" + zzsj + "\",\n" +
                                        "        \"FENDDATE_H\": \"" + zzsj + "\",\n" +
                                        "        \"FISINIT\": false,\n" +
                                        "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                        "        \"FCONTACTUNIT\": {\n" +
                                        "            \"FNumber\": \"" + wlgbJdYxcgsqjl.getSqrid() + "\"\n" +
                                        "        },\n" +
                                        "        \"FCURRENCYID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FDEPARTMENTID\": {\n" +
                                        "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                        "        },\n" +
                                        "        \"FSETTLEORGID\": {\n" +
                                        "            \"FNumber\": \"" + l + "\"\n" +
                                        "        },\n" +
                                        "        \"FPURCHASEORGID\": {\n" +
                                        "            \"FNumber\": \"" + l + "\"\n" +
                                        "        },\n" +
                                        "        \"FPAYORGID\": {\n" +
                                        "            \"FNumber\": \"" + l + "\"\n" +
                                        "        },\n" +
                                        "        \"FMAINBOOKSTDCURRID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FEXCHANGETYPE\": {\n" +
                                        "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                        "        },\n" +
                                        "        \"FExchangeRate\": 1.0,\n" +
                                        "        \"FACCNTTIMEJUDGETIME\": \"" + zzsj + "\",\n" +
                                        "        \"FCancelStatus\": \"A\",\n" +
                                        "        \"FBUSINESSTYPE\": \"T\",\n" +
                                        "        \"FRemarks\": \"" + wlgbJdYxcgsqjl.getBz() + "\",\n" +
                                        "        \"FSPSLBT\": \"" + wlgbJdYxcgsqjl.getSpbt() + "\",\n" +
                                        "        \"FSPBH\": \"" + wlgbJdYxcgsqjl.getSpbh() + "\",\n" +
                                        "        \"FSPSQR\": \"" + wlgbJdYxcgsqjl.getSqr() + "\",\n" +
                                        "        \"FSPSQRBM\": \"" + wlgbJdYxcgsqjl.getSqrbm() + "\",\n";
                                List<JSONObject> list1 = new ArrayList<>();
                                WlgbJdYxcgsqjl finalWlgbJdYxcgsqjl = wlgbJdYxcgsqjl;
                                list3.forEach(l1 -> {
                                    JSONObject jsonObject2 = scQtYfdMx(finalWlgbJdYxcgsqjl.getBsbh(), l1.getFykmbm(), l1.getFyje(), sqrbmid);
                                    list1.add(jsonObject2);
                                });
                                json += "        \"FEntity\": " + list1.toString() +
                                        "    }\n" +
                                        "}";
                                Map<String, String> map = KingDeeConfig.spTjJdDj2(json, "其他应付单");
                                save = "true".equals(map.get("jg"));
                                if (save) {
                                    String json1 = "{\n" +
                                            "    \"NeedUpDateFields\": [],\n" +
                                            "    \"NeedReturnFields\": [],\n" +
                                            "    \"IsDeleteEntry\": \"true\",\n" +
                                            "    \"SubSystemId\": \"\",\n" +
                                            "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                            "    \"IsEntryBatchFill\": \"true\",\n" +
                                            "    \"ValidateFlag\": \"true\",\n" +
                                            "    \"NumberSearch\": \"true\",\n" +
                                            "    \"IsAutoAdjustField\": \"false\",\n" +
                                            "    \"InterationFlags\": \"\",\n" +
                                            "    \"IgnoreInterationFlag\": \"\",\n" +
                                            "    \"Model\": {\n" +
                                            "        \"FID\": 0,\n" +
                                            "        \"FBillTypeID\": {\n" +
                                            "            \"FNUMBER\": \"" + ("代付".equals(wlgbJdYxcgsqjl.getFkfs()) ? "FKDLX02_SYS" : "FKDLX04_SYS") + "\"\n" +
                                            "        },\n" +
                                            "        \"FDATE\": \"" + zzsj + "\",\n" +
                                            "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FCONTACTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdYxcgsqjl.getSqrid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FRECTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdYxcgsqjl.getSqrid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FDepartment\": {\n" +
                                            "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                            "        },\n" +
                                            "        \"FISINIT\": false,\n" +
                                            "        \"FCURRENCYID\": {\n" +
                                            "            \"FNumber\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FEXCHANGERATE\": 1.0,\n" +
                                            "        \"FSETTLERATE\": 1.0,\n" +
                                            "        \"FSETTLEORGID\": {\n" +
                                            "            \"FNumber\": \"" + l + "\"\n" +
                                            "        },\n" +
                                            "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                            "        \"FBUSINESSTYPE\": \"3\",\n" +
                                            "        \"FCancelStatus\": \"A\",\n" +
                                            "        \"FPAYORGID\": {\n" +
                                            "            \"FNumber\": \"" + l + "\"\n" +
                                            "        },\n" +
                                            "        \"FISSAMEORG\": true,\n" +
                                            "        \"FIsCredit\": false,\n" +
                                            "        \"FSETTLECUR\": {\n" +
                                            "            \"FNUMBER\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FIsWriteOff\": false,\n" +
                                            "        \"FREALPAY\": false,\n" +
                                            "        \"FISCARRYRATE\": false,\n" +
                                            "        \"FSETTLEMAINBOOKID\": {\n" +
                                            "            \"FNUMBER\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FMoreReceive\": false,\n" +
                                            "        \"FVirIsSameAcctOrg\": false,\n" +
                                            "        \"FREMARK\": \"" + wlgbJdYxcgsqjl.getBz() + "\",\n" +
                                            "        \"FSPBT\": \"" + wlgbJdYxcgsqjl.getSpbt() + "\",\n" +
                                            "        \"FSPBH\": \"" + wlgbJdYxcgsqjl.getSpbh() + "\",\n" +
                                            "        \"FSPSQR\": \"" + wlgbJdYxcgsqjl.getSqr() + "\",\n" +
                                            "        \"FSPSQRBM\": \"" + wlgbJdYxcgsqjl.getSqrbm() + "\",\n";
                                    WlgbJdYhk wlgbJdYhk = yhkCl(l);
                                    List<JSONObject> list4 = new ArrayList<>();
                                    list1.forEach(l2 -> {
                                        String fcostid = l2.getJSONObject("FCOSTID").getString("FNumber");
                                        String fssmd = l2.getJSONObject("FSSMD").getString("FNumber");
                                        Double fnotaxamountfor = l2.getDouble("FNOTAXAMOUNTFOR");
                                        JSONObject jsonObject2 = fkdMx1(fssmd, fnotaxamountfor, zzsj, fcostid, sqrbmid, ("代付".equals(finalWlgbJdYxcgsqjl.getFkfs()) ? "SFKYT20_SYS" : "SFKYT10_SYS"), wlgbJdYhk.getZh());

                                        list4.add(jsonObject2);
                                    });
                                    json1 += "        \"FPAYBILLENTRY\": " + list4.toString() +
                                            "    }\n" +
                                            "}";
                                    //录入金蝶
                                    Map<String, String> map2 = KingDeeConfig.spTjJdDj2(json1, "付款单");
                                    save = "true".equals(map2.get("jg"));
                                    if (save) {
                                        list3.forEach(l1 -> {
                                            WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj1 = new WlgbJdSpCwlrjdsj();
                                            wlgbJdSpCwlrjdsj1.setId(l1.getId());
                                            wlgbJdSpCwlrjdsj1.setSfscjd(1);
                                            wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj1);
                                        });
                                    }
                                }
                            }
                            if (save) {
                                if (type == 1) {
                                    WlgbJdYxcgsqjl wlgbJdYxcgsqjl1 = new WlgbJdYxcgsqjl();
                                    wlgbJdYxcgsqjl1.setId(wlgbJdYxcgsqjl.getId());
                                    wlgbJdYxcgsqjl1.setSflrjd(1);
                                    wlgbJdYxcgsqjlService.updateById(wlgbJdYxcgsqjl1);
                                }
                            }
                        } else {
                            sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "别墅不存在", wlgbJdYxcgsqjl.getSpbt());
                        }
                    } else {
                        sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "别墅缺失，补充错误", wlgbJdYxcgsqjl.getSpbt());
                    }
                } else {
                    sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "员工缺失，补充错误", wlgbJdYxcgsqjl.getSpbt());
                }
            } else {
                sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "部门缺失，补充错误", wlgbJdYxcgsqjl.getSpbt());
            }
        }

        return save;
    }

    /**
     * 上传其他应付单明细
     *
     * @param bm   门店
     * @param kmbm 科目编码
     * @param sum  金额
     * @param bmid 部门id
     * @return 明细数据
     */
    public JSONObject scQtYfdMx(String bm, String kmbm, Double sum, String bmid) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FCOSTID", KingDeeConfig.zhdx(kmbm));
        jsonObject.put("FCOSTDEPARTMENTID", KingDeeConfig.zhdx(bmid));
        jsonObject.put("FSSMD", KingDeeConfig.zhdx(bm));
        jsonObject.put("FNOTAXAMOUNTFOR", sum);
        jsonObject.put("FTOTALAMOUNTFOR", sum);
        jsonObject.put("FNOTSETTLEAMOUNTFOR_D", sum);
        jsonObject.put("FNOTAXAMOUNT_D", sum);
        jsonObject.put("FCREATEINVOICE", false);

        return jsonObject;
    }

    /**
     * 银行卡处理
     *
     * @param ztBm 账套编码
     * @return 银行卡对象
     */
    private WlgbJdYhk yhkCl(String ztBm) {
        String bz = "";
        if ("100".equals(ztBm)) {
            bz = "总部收入支出";
        } else if ("101".equals(ztBm)) {
            bz = "加盟收入支出";
        } else if ("102".equals(ztBm)) {
            bz = "直营费用";
        } else if ("103".equals(ztBm)) {
            bz = "乐诚收入支出";
        } else if ("104".equals(ztBm)) {
            bz = "奋马收入支出";
        } else if ("105".equals(ztBm)) {
            bz = "九运";
        } else if ("106".equals(ztBm)) {
            bz = "直营费用";
        }
        WlgbJdYhk wlgbJdYhk;
        if (!"".equals(bz)) {
            wlgbJdYhk = wlgbJdYhkService.querySfScAndLikeBz(0, bz);
        } else {
            wlgbJdYhk = new WlgbJdYhk();
        }
        return wlgbJdYhk;
    }

    /**
     * 付款单明细
     *
     * @param bm     金蝶门店
     * @param sum    金额
     * @param cnzzsj 出纳转账时间
     * @param fykmbm 费用科目编码
     * @param bmid   部门id
     */
    public JSONObject fkdMx1(String bm, Double sum, String cnzzsj, String fykmbm, String bmid, String fkyt, String yhk) {
        JSONObject jsonObject = new JSONObject();
        //现金
//        jsonObject.put("FSETTLETYPEID", KingDeeConfig.zhdx("JSFS01_SYS"));
        //电汇
        jsonObject.put("FSETTLETYPEID", KingDeeConfig.zhdx("JSFS04_SYS"));
        //银行卡账号
        jsonObject.put("FACCOUNTID", KingDeeConfig.zhdx(yhk));
        jsonObject.put("FPURPOSEID", KingDeeConfig.zhdx(fkyt));
        jsonObject.put("FPAYTOTALAMOUNTFOR", sum);
        jsonObject.put("FPAYAMOUNTFOR_E", sum);
        jsonObject.put("FSETTLEPAYAMOUNTFOR", sum);
        jsonObject.put("FRecType", "1");
        jsonObject.put("FCOSTID", KingDeeConfig.zhdx(fykmbm));
        jsonObject.put("FMD", KingDeeConfig.zhdx(bm));
        //部门
        jsonObject.put("FEXPENSEDEPTID_E", KingDeeConfig.zhdx(bmid));
        jsonObject.put("FPAYAMOUNT_E", sum);
        jsonObject.put("FPOSTDATE", cnzzsj);
        jsonObject.put("FRuZhangType", "1");
        jsonObject.put("FPayType", "A");
        jsonObject.put("FNOTVERIFICATEAMOUNT", sum);
        jsonObject.put("FBankInvoice", false);
        jsonObject.put("FByAgentBank", false);
        jsonObject.put("FOverseaPay", false);

        return jsonObject;
    }

    @RequestMapping(value = "queryXhsAndDyXdYjDay")
    public void queryXhsAndDyXdYjDay() {
        try {
            Map<String, Object> map1 = new HashMap<>();
            map1.put("type", 2);
            map1.put("grOrBm", 2);
            List<String> list1 = new ArrayList<>();
            list1.add("80ca5a42c2774390bcc314e02c6a034d");
            list1.add("a33c818bda9d4793b07fbc92e7a55f2e");
            map1.put("ly", list1);
            List<JSONObject> list = weiLianService.queryXhsAndDyXdYj(map1);
            String content = "今日小红书/抖音下单业绩播报\n\n";
            if (list.size() > 0) {
                content += "部门\t\t\t业绩\n";
                for (JSONObject l : list) {
                    content += l.getString("bmmc") + "\t\t" + l.getDouble("zje") + "\n";
                }
            } else {
                content += "下单量：0\t\t总业绩：0.0";
            }
            Map<String, Object> map = new HashMap<>();
            map.put("city", "小红书/抖音");
            List<DkBbJqr> jqrList = weiLianService.queryDkBb(map);
            DkBbJqr dkBbJqr = jqrList != null ? jqrList.size() > 0 ? jqrList.get(0) : null : null;
            DingDingUtil.sendMsg(dkBbJqr != null ? dkBbJqr.getWebhook() : null, dkBbJqr != null ? dkBbJqr.getSecret() : null, content, null, false);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            Map<String, Object> map1 = new HashMap<>();
            map1.put("type", 2);
            map1.put("grOrBm", 2);
            List<String> list1 = new ArrayList<>();
            list1.add("30197965afd34fb687ab7fe0ed70fa7c");
            map1.put("ly", list1);
            List<JSONObject> list = weiLianService.queryXhsAndDyXdYj(map1);
            String content = "今日短租下单业绩播报\n\n";
            if (list.size() > 0) {
                content += "部门\t\t\t业绩\n";
                for (JSONObject l : list) {
                    content += l.getString("bmmc") + "\t\t" + l.getDouble("zje") + "\n";
                }
            } else {
                content += "下单量：0\t\t总业绩：0.0";
            }
            Map<String, Object> map = new HashMap<>();
            map.put("city", "小红书/抖音");
            List<DkBbJqr> jqrList = weiLianService.queryDkBb(map);
            DkBbJqr dkBbJqr = jqrList != null ? jqrList.size() > 0 ? jqrList.get(0) : null : null;
            DingDingUtil.sendMsg(dkBbJqr != null ? dkBbJqr.getWebhook() : null, dkBbJqr != null ? dkBbJqr.getSecret() : null, content, null, false);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 30天后发送二次美团推送
     */
    @RequestMapping(value = "sendMtHpSl")
    public Result sendMtHpSl() {
        List<Hpsl> list = weiLianService.queryHpSlCgMonth();
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_IHDDOQT44HHKKST9CMK5");
        ydAppkey.setToken("RA766M91LDWRE24I5EVJM81EL0MJ3X9R5L8RKQ7");
        list.forEach(l -> {
            String contexts = "![](https://jiuyun2.qianquan888.com/upload/test/1_1699415809541.png)";
            contexts += "\n\n审批超时提醒";
            contexts += "\n\n" + l.getDz() + "发起的好评审批，已过30天，请填写留存数";
            contexts += "\n\n送达时间：";
            try {
                DingDBConfig.sendGztz1(l.getDjrid(), dingkey, "审批超时提醒",
                        contexts, "https://jztdpp.aliwork.com/APP_IHDDOQT44HHKKST9CMK5/processDetail?procInsId=" + l.getSlid());
            } catch (ApiException e) {
                e.printStackTrace();
            }
            String spJl21 = YdConfig.getSpJdJl(l.getSlid(), ydAppkey.getAppkey(), ydAppkey.getToken());
            JSONObject jsonObject3 = JSONObject.parseObject(spJl21);
            JSONArray content = jsonObject3.getJSONArray("content");
            if (content != null && content.size() > 0) {
                List<JSONObject> list1 = new ArrayList<>();
                JSONObject o = new JSONObject();
                content.forEach(l1 -> {
                    JSONObject j = (JSONObject) l1;
                    if (j != null) {
                        if ("TODO".equals(j.getString("type"))) {
                            list1.add(j);
                        }
                    }
                });
                if (list1.size() > 0) {
                    o = list1.get(0);
                }
                String taskId = o.getString("taskId");
                if (taskId != null && !"".equals(taskId)) {
                    String zjLc = YdConfig.zjLc(ydAppkey.getAppkey(), ydAppkey.getToken(), l.getSlid(), l.getDjrid(), "填写留存数量", taskId);
                    JSONObject jsonObject1 = JSONObject.parseObject(zjLc);
                    if (jsonObject1 != null) {
                        if (!jsonObject1.getBoolean("success")) {
                            System.out.println("转交失败");
                        }
                    }
                }
            }
        });
        List<FwqThbD9c> list2 = weiLianService.queryDyDataMonth();
        list2.forEach(l -> {
            String contexts = "![](https://jiuyun2.qianquan888.com/upload/test/1_1699415809541.png)";
            contexts += "\n\n审批超时提醒";
            contexts += "\n\n" + l.getXm() + "发起的抖音/小红书/地推/抖音好评审批，已过30天，请填写留存数";
            contexts += "\n\n送达时间：";
            try {
                DingDBConfig.sendGztz1(l.getSprid(), dingkey, "审批超时提醒",
                        contexts, "https://jztdpp.aliwork.com/APP_IHDDOQT44HHKKST9CMK5/processDetail?procInsId=" + l.getSlid());
            } catch (ApiException e) {
                e.printStackTrace();
            }
            String spJl21 = YdConfig.getSpJdJl(l.getSlid(), ydAppkey.getAppkey(), ydAppkey.getToken());
            JSONObject jsonObject3 = JSONObject.parseObject(spJl21);
            JSONArray content = jsonObject3.getJSONArray("content");
            if (content != null && content.size() > 0) {
                List<JSONObject> list1 = new ArrayList<>();
                JSONObject o = new JSONObject();
                content.forEach(l1 -> {
                    JSONObject j = (JSONObject) l1;
                    if (j != null) {
                        if ("TODO".equals(j.getString("type"))) {
                            list1.add(j);
                        }
                    }
                });
                if (list1.size() > 0) {
                    o = list1.get(0);
                }
                String taskId = o.getString("taskId");
                if (taskId != null && !"".equals(taskId)) {
                    String zjLc = YdConfig.zjLc(ydAppkey.getAppkey(), ydAppkey.getToken(), l.getSlid(), l.getSprid(), "填写留存数量", taskId);
                    JSONObject jsonObject1 = JSONObject.parseObject(zjLc);
                    if (jsonObject1 != null) {
                        if (!jsonObject1.getBoolean("success")) {
                            System.out.println("转交失败");
                        }
                    }
                }
            }
        });

        return Result.OK();
    }

}
