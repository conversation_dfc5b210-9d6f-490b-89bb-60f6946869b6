package com.wlgb.config;

import jdk.nashorn.internal.runtime.ErrorManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月23日 20:00
 */
@Slf4j
public class FileConfig {
    private final static ErrorManager logger = new ErrorManager();


    /**
     * linux下   文件实际上是存在的
     *
     * @param path 传相对路径 比如 cert/alipay/xxx.crt
     * @return 返回的路径就是放在linux服务器上的文件路径
     */
    public static String getFileAbsolutePath2(String path) {
        try {
            // 创建临时文件，获取jar里面的配置文件
            File file = new File(path);
            if (file.exists()) {
                return file.getAbsolutePath();
            }
            InputStream inputStream = null;
            try {
                ClassPathResource resource = new ClassPathResource(path);
                inputStream = resource.getInputStream();
                FileUtils.copyInputStreamToFile(inputStream, file);
                return file.getAbsolutePath();
            } finally {
                IOUtils.closeQuietly(inputStream);
            }
        } catch (Exception e) {
            logger.warning("getFileAbsolutePath报错了");
        }
        return null;
    }

    /**
     * linux下，文件实际上是不存在的，创建临时文件
     *
     * @param path 传相对路径 比如 cert/alipay/xxx.crt
     * @return 返回的路径就是放在linux服务器上的文件路径
     */
    public static String getFileAbsolutePath(String path, String tempFileName) {
        log.info("*****进入getFileAbsolutePath方法*****");
        // 临时文件所在的目录
        File tempDir = new File(path);
        if (!tempDir.exists()) {
            // 创建目录，如果不存在
            tempDir.mkdirs();
        }
        File tempFile = new File(tempDir, tempFileName);
        try {
            tempFile.createNewFile(); // 创建文件
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.info("*****进入getFileAbsolutePath方法222222*****");
        // 获取当前工作目录的绝对路径
        String currentDir = System.getProperty("user.dir");
        log.info("*****进入getFileAbsolutePath方法****获取当前工作目录的绝对路径*****{}",currentDir);
        // 计算临时文件的相对路径
        return tempFile.getAbsolutePath().replace(currentDir + "/", "");
    }
}
