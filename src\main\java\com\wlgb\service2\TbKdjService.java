package com.wlgb.service2;

import com.wlgb.entity.TbKdj;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27 15:41
 */
public interface TbKdjService {
    void save(TbKdj tbKdj);

    void updateById(TbKdj tbKdj);

    TbKdj queryByBsAndCcAndRq(String bs, String cc, String rq);

    TbKdj queryByBsAndCcAndRqAndJcAndTc(String bs, String cc, String rq, Date jc, Date tc);

    TbKdj queryByBsAndRqAndJcAndTc(String bs, String rq, Date jc, Date tc);
}
