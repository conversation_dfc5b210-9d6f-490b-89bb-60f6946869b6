package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmRyxx;
import com.wlgb.mapper.CrmRyxxMapper;
import com.wlgb.service2.CrmRyxxService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/16 16:39
 */
@Service
@DS(value = "second")
public class CrmRyxxServiceImpl implements CrmRyxxService {
    @Resource
    private CrmRyxxMapper crmRyxxMapper;

    @Override
    public void save(CrmRyxx crmRyxx) {
        crmRyxxMapper.insertSelective(crmRyxx);
    }

    @Override
    public Integer queryCountByUserId(String userId) {
        Example example = new Example(CrmRyxx.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userid", userId);
        return crmRyxxMapper.selectCountByExample(example);
    }
}
