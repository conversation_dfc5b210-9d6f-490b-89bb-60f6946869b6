package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.*;
import com.wlgb.service.*;
import com.wlgb.service2.TbXydService;
import com.wlgb.service2.TbYddXydService;
import com.wlgb.service2.WeiLianService;
import com.wlgb.service2.WlgbYlxdService;
import com.wlgb.service3.DzxydService;
import com.wlgb.service3.WeiLianDaiBanService;
import freemarker.template.TemplateException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: Class
 * @author: fwq
 * @date: 2024年05月24日 11:16
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/xydedittask")
public class XydEditTaSkController {
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private OssFileService ossFileService;
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WlgbXydLogService wlgbXydLogService;
    @Autowired
    private WlgbXxbjdjjlService wlgbXxbjdjjlService;
    @Autowired
    private WlgbXsdjbjjlService wlgbXsdjbjjlService;
    @Autowired
    private WlgbBdjlService wlgbBdjlService;
    @Autowired
    private WlgbDksljlService wlgbDksljlService;
    @Autowired
    private DzxydService dzxydService;
    @Autowired
    private JqrConfig jqrConfig;
    @Autowired
    private WeiLianDaiBanService weiLianDaiBanService;
    @Autowired
    private WlgbOrderCyjlService wlgbOrderCyjlService;
    @Autowired
    private WlgbEatTcjlService wlgbEatTcjlService;
    @Autowired
    private WlgbEatCpjlService wlgbEatCpjlService;
    @Autowired
    private WlgbEatScjlService wlgbEatScjlService;
    @Autowired
    private TbYddXydService tbYddXydService;

    /**
     * 修改订单异步
     */
    @RequestMapping(value = "xydeditTask")
    public void editXydTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        log.info("*****进入editXydTask异步请求*******{}", datas);
        //实例ID
        String formInstId = request.getParameter("formInstId");
        String userId = request.getParameter("userId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        TbXyd xyd = JSONObject.toJavaObject(jsonObject, TbXyd.class);
        if (xyd == null) {
            return;
        }
        if (xyd.getXhdchtype() == null || "".equals(xyd.getXhdchtype()) || "undefined".equals(xyd.getXhdchtype()) || "NaN".equals(xyd.getXhdchtype())) {
            xyd.setXhdchtype("0");
        }
        if (xyd.getXkhly() != null && !"".equals(xyd.getXkhly())) {
            xyd.setXkhly(xyd.getXkhly().replace(" ", ""));
        }
        if (xyd.getXsfys() == null || "".equals(xyd.getXsfys())) {
            xyd.setXsfys("0");
        }
        if (!"1".equals(xyd.getXsfys())) {
            xyd.setXysbz("");
        }
        //剧本杀清空
        if ("0".equals(xyd.getXsfxyjbs())) {
            xyd.setXjbszje(0.0);
            xyd.setXjbscjr("");
            xyd.setXjbscjrid("");
            xyd.setXjbszxr("");
            xyd.setXjbsfzcjr("");
            xyd.setXjbsfzcjrid("");
        }
        //定金选择类型清空
        if ("1".equals(xyd.getXdjtype())) {
            xyd.setXxsdj(0.0);
            xyd.setXxxdj(0.0);
            xyd.setXyqr(null);
        }
        if ("2".equals(xyd.getXdjtype())) {
            xyd.setXxsdj(0.0);
            xyd.setXxxdj(0.0);
            xyd.setXzzsj(null);
            xyd.setXzzhlw(null);
        }
        if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(xyd.getXdjtype())) {
            xyd.setXysdj(xyd.getXxsdj() + xyd.getXxxdj());
        }
        if ("0".equals(xyd.getXsfbx())) {
            xyd.setXbxrs(0);
            xyd.setXbxdj(0);
            xyd.setXbxzje(0.0);
            xyd.setXbxcjr("");
            xyd.setXbxcjrgh("");
        }
        if (xyd.getXztze() == null || xyd.getXztze() == 0) {
            xyd.setXchjlid("");
            xyd.setXchcjr("");
            xyd.setXchfzcjr("");
            xyd.setXchjl("");
            xyd.setXchcjrgh("");
        }
        if (xyd.getXsfhps() == null || xyd.getXsfhps() == 0) {
            xyd.setXhpscjr("");
            xyd.setXhpsfy(0.0);
            xyd.setXhpscjrgh("");
            xyd.setXhpsfzcjr("");
            xyd.setXhpsfzcjrid("");
            xyd.setXhpsxm("");
            xyd.setXhpsfymx("");
            xyd.setXhpszcgz(0.0);
            xyd.setXhpszcqt(0.0);
        }
        if (xyd.getXsfzrcs() == null || xyd.getXsfzrcs() == 0) {
            xyd.setXzrcsrs(0);
            xyd.setXzrcsfy(0.0);
            xyd.setXzrcscjr("");
        }
        //订单类型
        if (xyd.getXzdcl() != null) {
            if (xyd.getXzdcl() != 4) {
                xyd.setXxdxm("");
                xyd.setXxdsjxm("");
                xyd.setXxdsjxmgh("");
                xyd.setXxddh("");
            }
            if (xyd.getXzdcl() == 0 || xyd.getXzdcl() == 4) {
                xyd.setXlbjf("");
            }
        }
        if ((xyd.getXdcze() != null ? xyd.getXdcze() : 0) == 0) {
            xyd.setXbdje(0.0);
            xyd.setXbdsl(0);
            xyd.setXdcze(0.0);
            xyd.setXdccjr("");
            xyd.setXdccjrgh("");
            xyd.setXdcfzcjr("");
        }
        if ((xyd.getXskze() != null ? xyd.getXskze() : 0) == 0) {
            xyd.setXskje(0.0);
            xyd.setXsksl(0);
            xyd.setXskze(0.0);
            xyd.setXskcjr("");
            xyd.setXskcjrgh("");
            xyd.setXskfzcjr("");
        }
        if (xyd.getXisxzcj() == null || xyd.getXisxzcj() == 1) {
            xyd.setXcjr("");
            xyd.setXcjrgh("");
        }
        if (xyd.getXsfhxgjcj() == null || xyd.getXsfhxgjcj() == 1) {
            xyd.setXhxcjr("");
            xyd.setXhxcjrgh("");
        }
        if (xyd.getXsendder() != null && !"".equals(xyd.getXsendder()) && xyd.getXsendder().length() > 0) {
            xyd.setXsendder(null);
        }
        if (xyd.getXfd() != null && !"".equals(xyd.getXfd()) && xyd.getXfd().length() > 0) {
            xyd.setXfd(xzjq(xyd.getXfd()));
        }
        if (xyd.getXlbjf() != null && !"".equals(xyd.getXlbjf()) && xyd.getXlbjf().length() > 0) {
            xyd.setXlbjf(xzjq(xyd.getXlbjf()));
        }
        if (xyd.getXbxcjr() != null && !"".equals(xyd.getXbxcjr()) && xyd.getXbxcjr().length() > 0) {
            xyd.setXbxcjr(xzjq(xyd.getXbxcjr()));
        }
        if (xyd.getXchjlid() != null && !"".equals(xyd.getXchjlid()) && xyd.getXchjlid().length() > 0) {
            xyd.setXchjlid(xzjq(xyd.getXchjlid()));
        }
        if (xyd.getXchcjr() != null && !"".equals(xyd.getXchcjr()) && xyd.getXchcjr().length() > 0) {
            xyd.setXchcjr(xzjq(xyd.getXchcjr()));
        }
        if (xyd.getXchfzcjr() != null && !"".equals(xyd.getXchfzcjr()) && xyd.getXchfzcjr().length() > 0) {
            xyd.setXchfzcjr(xzjq(xyd.getXchfzcjr()));
        }
        if (xyd.getXhpscjr() != null && !"".equals(xyd.getXhpscjr()) && xyd.getXhpscjr().length() > 0) {
            xyd.setXhpscjr(xzjq(xyd.getXhpscjr()));
        }
        if (xyd.getXzrcscjr() != null && !"".equals(xyd.getXzrcscjr()) && xyd.getXzrcscjr().length() > 0) {
            xyd.setXzrcscjr(xzjq(xyd.getXzrcscjr()));
        }
        if (xyd.getXdccjr() != null && !"".equals(xyd.getXdccjr()) && xyd.getXdccjr().length() > 0) {
            xyd.setXdccjr(xzjq(xyd.getXdccjr()));
        }
        if (xyd.getXdcfzcjr() != null && !"".equals(xyd.getXdcfzcjr()) && xyd.getXdcfzcjr().length() > 0) {
            xyd.setXdcfzcjr(xzjq(xyd.getXdcfzcjr()));
        }
        if (xyd.getXskcjr() != null && !"".equals(xyd.getXskcjr()) && xyd.getXskcjr().length() > 0) {
            xyd.setXskcjr(xzjq(xyd.getXskcjr()));
        }
        if (xyd.getXskfzcjr() != null && !"".equals(xyd.getXskfzcjr()) && xyd.getXskfzcjr().length() > 0) {
            xyd.setXskfzcjr(xzjq(xyd.getXskfzcjr()));
        }
        if (xyd.getXcjr() != null && !"".equals(xyd.getXcjr()) && xyd.getXcjr().length() > 0) {
            xyd.setXcjr(xzjq(xyd.getXcjr()));
        }
        if (xyd.getXhxcjr() != null && !"".equals(xyd.getXhxcjr()) && xyd.getXhxcjr().length() > 0) {
            xyd.setXhxcjr(xzjq(xyd.getXhxcjr()));
        }
        //剧本杀成交人
        if (xyd.getXjbscjr() != null && !"".equals(xyd.getXjbscjr()) && xyd.getXjbscjr().length() > 0) {
            xyd.setXjbscjr(xzjq(xyd.getXjbscjr()));
        }
        //剧本杀辅助成交人
        if (xyd.getXjbsfzcjr() != null && !"".equals(xyd.getXjbsfzcjr()) && xyd.getXjbsfzcjr().length() > 0) {
            xyd.setXjbsfzcjr(xzjq(xyd.getXjbsfzcjr()));
        }
        //轰趴师辅助成交人
        if (xyd.getXhpsfzcjr() != null && !"".equals(xyd.getXhpsfzcjr()) && xyd.getXhpsfzcjr().length() > 0) {
            xyd.setXhpsfzcjr(xzjq(xyd.getXhpsfzcjr()));
        }
        if (xyd.getXbjdj() == null || xyd.getXbjdj() == 0) {
            xyd.setXxxbj(0.0);
            xyd.setXxsbj(0.0);
            xyd.setXbjdj(0.0);
            xyd.setXbjdjlx("");
            xyd.setXcdzjbj(0.0);
            xyd.setXzzfybj(0.0);
        }
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);
        log.info("*****进入editXydTask异步请求**ding*****{}", ding);
        System.out.println("修改人：" + userId);
        String fdUserId = xyd.getXfd();
        xyd = fz(xyd);
        xyd.setXgsj(new Date());
        xyd.setXxgzid(userId);
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(xyd.getXbsmc());
        if (villa == null) {
            villa = weiLianService.queryVillaByVname(xyd.getXbsmc());
            if (villa != null) {
                xyd.setXbsmc(villa.getVid());
            }
        }
        Map<String, Object> map = new HashMap<>();
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        try {
            map = dzXyd(xyd, jsonObject);
        } catch (Exception e) {
            log.info("*****进入editXydTask异步请求*生成电子协议单失败******{}", e);
            try {
                DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : "") + "修改协议单，订单编号”" + xyd.getXddbh() + "“协议单生成报错了");
            } catch (ApiException apiException) {
                apiException.printStackTrace();
            }
        }
        if (map.size() > 0) {
            xyd.setXimagepath((String) map.get("png"));
        }
        String upload = "";
        String filePath = FileConfig.getFileAbsolutePath2("static" + File.separator + "img" + File.separator + xyd.getXimagepath());
        File file = null;
        try {
            file = new File(filePath);
            if (!file.exists()) {
                log.error("文件不存在: {}", filePath);
                //判断图片存在不存在,如果不存在将重新生成电子协议单
                dzXyd(xyd, jsonObject);
            }
            FileInputStream fileInputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
            fileInputStream.close();
            upload = ossFileService.upload(multipartFile);
            if (upload != null && !"".equals(upload)) {
                upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
            }
        } catch (Exception e) {
            log.info("*****进入editXydTask异步请求*获取文件/上传oss文件 失败******{}", e);
            try {
                DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : xyd.getXfd()) + "修改协议单，订单编号”" + xyd.getXddbh() + "“协议单上传阿里云报错了");
            } catch (ApiException apiException) {
                apiException.printStackTrace();
            }
        }
        String dingpan = null;
        try {
            if (file != null) {
                dingpan = DingPan.dingpan(file, dingkey);
            }
        } catch (Exception e) {
            log.info("*****进入editXydTask异步请求*上传钉盘2失败******{}", e);
        }
        if (file != null) {
            file.delete();
        }
        xyd.setXimagepath(upload);
        xyd.setXpdfpath(upload);

        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXltext("修改协议单");
        xyd.setXgsj(new Date());
        xyd.setXxgorxt("1");
        TbXyd tbXyd1 = tbXydService.queryById(xyd.getXid());
        //不改房东不修改房东所属部门
        if (!tbXyd1.getXfd().equals(xyd.getXfd())) {
            //获取房东所属部门
            FwqBbb2 fwqBbb2 = weiLianService.queryBbb2ByXm(xyd.getXfd());
            if (fwqBbb2 != null) {
                xyd.setXfdssbm(fwqBbb2.getBm() != null ? fwqBbb2.getBm().toString() : null);
            }
        }
        //修改本地数据库的sfdyjk值,修改为1 意思是下一次不需要执行
        xyd.setSfdyjk(1);
        xyd.setXscz(null);
        //已收增值定金判断
        xyd.setXyszzdj(xyd.getXyszzdj() != null && !"".equals(xyd.getXyszzdj()) ? xyd.getXyszzdj() : "0");
        //订单类型判断（校代与普通）
        if (xyd.getXzdcl() != null && xyd.getXzdcl() == 4) {
            xyd.setXddtype("校代");
        } else {
            xyd.setXddtype("普通");
        }
        tbXydService.updateById(xyd);
        TbYddXyd tbYddXyd1 = new TbYddXyd();
        try {
            TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(xyd.getXid());
            if (tbYddXyd != null) {
                tbYddXyd1.setXid(tbYddXyd.getXid());
                tbYddXyd1.setXbsmc(xyd.getXbsmc());
                tbYddXyd1.setXbsname(villa.getVname());
                tbYddXyd1.setXjctime(xyd.getXjctime());
                tbYddXyd1.setXtctime(xyd.getXtctime());
                tbYddXyd1.setXzk(xyd.getXzk());
                tbYddXyd1.setXzkdh(xyd.getXzkdh());
                tbYddXyd1.setXzksfz(xyd.getXzksfz());
                tbYddXyd1.setXfd(xyd.getXfd());
                tbYddXyd1.setXfdid(fdUserId);
                tbYddXyd1.setXfddh(xyd.getXfddh());
                tbYddXyd1.setXkhly(xyd.getXkhly());
                tbYddXyd1.setXtdxz(xyd.getXtdxz());
                tbYddXyd1.setXdcze(xyd.getXdcze());
                tbYddXyd1.setXhpsfy(xyd.getXhpsfy());
                tbYddXyd1.setXbxzje(xyd.getXbxzje());
                tbYddXyd1.setXztze(xyd.getXztze());
                tbYddXyd1.setXqkzj(xyd.getXqkzj());
                tbYddXyd1.setXhfyj(xyd.getXhfyj());
                tbYddXydService.updateById(tbYddXyd1);
            }
        } catch (Exception e) {
            log.info("*****进入editXydTask异步请求*获取协议单信息333失败******{}", e);
        }
        JSONArray cyzbd = jsonObject.getJSONArray("cyzbd");
        WlgbOrderCyjl wlgbOrderCyjl2 = new WlgbOrderCyjl();
        wlgbOrderCyjl2.setDdbh(xyd.getXddbh());
        wlgbOrderCyjl2.setCsfsc(0);
        List<WlgbOrderCyjl> listCyJl = wlgbOrderCyjlService.queryListByOrderCyJl(wlgbOrderCyjl2);
        log.info("*****进入editXydTask异步请求**listCyJl*****{}", listCyJl);
        wlgbOrderCyjlService.clearCyJl(xyd.getXddbh());
        wlgbEatTcjlService.clearData(xyd.getXddbh());
        wlgbEatCpjlService.clearData(xyd.getXddbh());
        wlgbEatScjlService.clearData(xyd.getXddbh());
        TbXyd finalTbXyd = xyd;
        log.info("*****进入editXydTask异步请求 进行到一半*******{}", datas);
        if (xyd.getXdcze() > 0) {
            cyzbd.forEach(l -> {
                JSONObject item = (JSONObject) l;
                WlgbOrderCyjl wlgbOrderCyjl = new WlgbOrderCyjl();
                wlgbOrderCyjl.setDdbh(finalTbXyd.getXddbh());
                wlgbOrderCyjl.setCymc(item.getString("cymc"));
                wlgbOrderCyjl.setCyjg(item.getDouble("cysj"));
                wlgbOrderCyjl.setCysl(item.getInteger("cygmsl"));
                wlgbOrderCyjl.setCyje(item.getDouble("cyje") != null ? item.getDouble("cyje") : finalTbXyd.getXdcze());
                log.info("*********餐饮成本*********{}", item.getDouble("cycb"));
                wlgbOrderCyjl.setCycb(item.getDouble("cycb"));
                wlgbOrderCyjl.setCyzcb((item.getDouble("cycb") != null ? item.getDouble("cycb") : 0) * (item.getInteger("cygmsl") != null ? item.getInteger("cygmsl") : 0));
                wlgbOrderCyjl.setCylb(item.getString("cylb"));
                wlgbOrderCyjl.setCyjq(item.getString("bm"));
                wlgbOrderCyjl.setCysx(item.getString("selectField_lnldd87u"));
                wlgbOrderCyjl.setCybs(item.getString("cybs"));
                JSONArray cjr = item.getJSONArray("employeeField_lmuespiw");
                if (cjr != null && cjr.size() > 0) {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(cjr.getString(0));
                    if (dingdingEmployee1 != null) {
                        wlgbOrderCyjl.setCycjr(dingdingEmployee1.getName());
                    }
                } else {
                    wlgbOrderCyjl.setCycjr(finalTbXyd.getXdccjr());
                }
                for (WlgbOrderCyjl cy : listCyJl) {
                    if (cy.getCybs() != null && !"".equals(cy.getCybs())) {
                        if (cy.getCybs().equals(item.getString("cybs"))) {
                            wlgbOrderCyjl.setCysjcb(cy.getCysjcb());
                            wlgbOrderCyjl.setCysjlr(cy.getCysjlr());
                            wlgbOrderCyjl.setCysjzcb(cy.getCysjzcb());
                            try {
                                if (cy.getKqyzxr() != null && !"".equals(cy.getKqyzxr())) {
                                    wlgbOrderCyjl.setKqyzxr(cy.getKqyzxr());
                                }
                                if (cy.getKqyzxrid() != null && !"".equals(cy.getKqyzxrid())) {
                                    wlgbOrderCyjl.setKqyzxrid(cy.getKqyzxrid());
                                }
                                if (cy.getCycsxm() != null && !"".equals(cy.getCycsxm())) {
                                    wlgbOrderCyjl.setCycsxm(cy.getCycsxm());
                                }
                                if (cy.getCycsid() != null && !"".equals(cy.getCycsid())) {
                                    wlgbOrderCyjl.setCycsid(cy.getCycsid());
                                }
                            } catch (Exception e) {
                                log.info("*****进入editXydTask异步请求*不知道是什么3失败******{}", e);
                            }
                            break;
                        }
                    } else {
                        if (cy.getCymc().equals(item.getString("cymc")) && cy.getCysl().equals(item.getInteger("cygmsl")) && cy.getCyje().equals(item.getDouble("cyje"))) {
                            wlgbOrderCyjl.setCysjcb(cy.getCysjcb());
                            wlgbOrderCyjl.setCysjlr(cy.getCysjlr());
                            wlgbOrderCyjl.setCysjzcb(cy.getCysjzcb());
                            try {
                                if (cy.getKqyzxr() != null && !"".equals(cy.getKqyzxr())) {
                                    wlgbOrderCyjl.setKqyzxr(cy.getKqyzxr());
                                }
                                if (cy.getKqyzxrid() != null && !"".equals(cy.getKqyzxrid())) {
                                    wlgbOrderCyjl.setKqyzxrid(cy.getKqyzxrid());
                                }
                                if (cy.getCycsxm() != null && !"".equals(cy.getCycsxm())) {
                                    wlgbOrderCyjl.setCycsxm(cy.getCycsxm());
                                }
                                if (cy.getCycsid() != null && !"".equals(cy.getCycsid())) {
                                    wlgbOrderCyjl.setCycsid(cy.getCycsid());
                                }
                            } catch (Exception e) {
                                log.info("*****进入editXydTask异步请求*不知道是什么失败******{}", e);
                            }
                            break;
                        }
                    }
                }
                wlgbOrderCyjlService.save(wlgbOrderCyjl);

                String cySx = item.getString("selectField_lnldd87u");
            });
        }
        //执行占据场次存储过程
        weiLianService.zxZjCc(xyd.getXid());
        TbXyd tbXyd = tbXydService.queryById(xyd.getXid());
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            log.info("*****进入editXydTask异步请求*获取token34444失败******{}", e);
        }
        String finalToken = token;
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        if (tbXyd.getXbjdj() == 0) {
            List<WlgbXxbjdjjl> list = wlgbXxbjdjjlService.queryByXidAndSfSc(tbXyd.getXid());
            list.forEach(l -> {
                GatewayResult gatewayResult = scBdSl(l.getSlid());
                WlgbXxbjdjjl wlgbXxbjdjjl = new WlgbXxbjdjjl();
                wlgbXxbjdjjl.setId(l.getId());
                wlgbXxbjdjjl.setSfsc(1);
                wlgbXxbjdjjlService.updateById(wlgbXxbjdjjl);
            });
            List<WlgbXsdjbjjl> list1 = wlgbXsdjbjjlService.queryByXidAndSfSc(tbXyd.getXid());
            list1.forEach(l -> {
                GatewayResult gatewayResult = scBdSl(l.getSlid());
                WlgbXsdjbjjl wlgbXsdjbjjl = new WlgbXsdjbjjl();
                wlgbXsdjbjjl.setId(l.getId());
                wlgbXsdjbjjl.setSfsc(1);
                wlgbXsdjbjjlService.updateById(wlgbXsdjbjjl);
            });
        }
        //定金表登记
        if (tbXyd.getXbjdj() > 0) {
            JSONArray xxbjzbd = jsonObject.getJSONArray("xxbjzbd");
            List<WlgbXxbjdjjl> list = wlgbXxbjdjjlService.queryByXidAndSfSc(tbXyd.getXid());
            list.forEach(l -> {
                scBdSl(l.getSlid());
                WlgbXxbjdjjl wlgbXxbjdjjl = new WlgbXxbjdjjl();
                wlgbXxbjdjjl.setId(l.getId());
                wlgbXxbjdjjl.setSfsc(1);
                wlgbXxbjdjjlService.updateById(wlgbXxbjdjjl);
            });
            List<WlgbXsdjbjjl> list1 = wlgbXsdjbjjlService.queryByXidAndSfSc(tbXyd.getXid());
            list1.forEach(l -> {
                scBdSl(l.getSlid());
                WlgbXsdjbjjl wlgbXsdjbjjl = new WlgbXsdjbjjl();
                wlgbXsdjbjjl.setId(l.getId());
                wlgbXsdjbjjl.setSfsc(1);
                wlgbXsdjbjjlService.updateById(wlgbXsdjbjjl);
            });
            if (xxbjzbd != null && xxbjzbd.size() > 0) {
                xxbjzbd.forEach(l -> {
                    JSONObject jsonObject1 = (JSONObject) l;
                    Double xxbjje = jsonObject1.getDouble("xxbjje");
                    if (xxbjje != null) {
                        WlgbXxbjdjjl wlgbXxbjdjjl = new WlgbXxbjdjjl();
                        Date bjzzsj = jsonObject1.getDate("bjzzsj");
                        String bjzfbhlw = jsonObject1.getString("bjzfbhlw");
                        wlgbXxbjdjjl.setXid(tbXyd.getXid());
                        wlgbXxbjdjjl.setCzsj(new Date());
                        wlgbXxbjdjjl.setCzrid(ding != null ? ding.getUserid() : null);
                        wlgbXxbjdjjl.setCzr(ding != null ? ding.getName() : null);
                        wlgbXxbjdjjl.setDjje(xxbjje);
                        wlgbXxbjdjjl.setZzsj(bjzzsj);
                        wlgbXxbjdjjl.setBjzfbhlw(bjzfbhlw);
                        jsonObject1.put("xddbh", tbXyd.getXddbh());
                        GatewayResult gatewayResult = null;
                        try {
                            gatewayResult = DingBdLcConfig.xzBdSl(finalToken, ydAppkey, jsonObject1.toJSONString(), "012412221639786136545", "FORM-Q4A664A1HFMVOZRF5HDEA53JLVXU3PIKAHDWK2");
                        } catch (Exception e) {
                            log.info("*****进入editXydTask异步请求*新增表单实例5失败******{}", e);
                        }
                        if (gatewayResult != null) {
                            wlgbXxbjdjjl.setSlid(gatewayResult.getResult());
                        }
                        wlgbXxbjdjjlService.save(wlgbXxbjdjjl);
                    }
                });
            }
            JSONArray xsbjzbd = jsonObject.getJSONArray("xsbjzbd");
            if (xsbjzbd != null && xsbjzbd.size() > 0) {
                xsbjzbd.forEach(l -> {
                    JSONObject jsonObject1 = (JSONObject) l;
                    Double xsbjje = jsonObject1.getDouble("xsbjje");
                    if (xsbjje != null) {
                        WlgbXsdjbjjl wlgbXsdjbjjl = new WlgbXsdjbjjl();
                        Date yqrq = jsonObject1.getDate("yqrq");
                        String dm = jsonObject1.getString("dm");
                        String qm = jsonObject1.getString("qm");
                        String yqr = jsonObject1.getString("yqr");
                        wlgbXsdjbjjl.setXid(tbXyd.getXid());
                        wlgbXsdjbjjl.setCzsj(new Date());
                        wlgbXsdjbjjl.setCzrid(ding != null ? ding.getUserid() : null);
                        wlgbXsdjbjjl.setCzr(ding != null ? ding.getName() : null);
                        wlgbXsdjbjjl.setBjje(xsbjje);
                        wlgbXsdjbjjl.setYqrq(yqrq);
                        wlgbXsdjbjjl.setDm(dm);
                        wlgbXsdjbjjl.setQm(qm);
                        wlgbXsdjbjjl.setYqr(yqr);

                        jsonObject1.put("xddbh", tbXyd.getXddbh());
                        GatewayResult gatewayResult = null;
                        try {
                            gatewayResult = DingBdLcConfig.xzBdSl(finalToken, ydAppkey, jsonObject1.toJSONString(), "012412221639786136545", "FORM-DX966R6183MVK5RP5RV1M734R4782M975IDWKI");
                        } catch (Exception e) {
                            log.info("*****进入editXydTask异步请求*新增表单实例2失败******{}", e);
                        }
                        if (gatewayResult != null) {
                            wlgbXsdjbjjl.setSlid(gatewayResult.getResult());
                        }
                        wlgbXsdjbjjlService.save(wlgbXsdjbjjl);
                    }
                });
            }
        }
        TbQrd tbQrd = weiLianService.queryQrdByXidAndSfSc(tbXyd.getXid());
        if (tbQrd != null) {
            if ("1".equals(tbQrd.getSfwcdz())) {
                Integer qx = weiLianDdXcxService.queryNotQx(userId);
                if (qx == 0) {
                    SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String context = (ding != null ? ding.getName() : "") + "修改了已退场的订单";
                    context += "\n订单编号:" + tbXyd1.getXddbh();
                    context += "\n房东:" + tbXyd1.getXfd();
                    context += "\n客户:" + tbXyd1.getXzk();
                    context += "\n进场时间:" + df2.format(tbXyd.getXjctime());
                    context += "\n退场时间:" + df2.format(tbXyd.getXtctime());
                    TbVilla villa1 = weiLianDdXcxService.queryTbVillaById(tbXyd1.getXbsmc());
                    context += "\n别墅:" + (villa1 != null ? villa1.getVname() : "");
                    List<String> list = new ArrayList<>();
                    //线上
                    String wook = "https://oapi.dingtalk.com/robot/send?access_token=e29f36dcbd922722c9df0976e46de574e80ea96a1c788d96c9f1d7ef89865484";
                    String se = "SEC5511d87046d65b5495122c9309fe913eb8e13970bbfca00ae7618807c2bc54f5";
                    DingDingUtil.sendMsg(wook, se, context, list, false);
                }
            }
        }
        //电子协议单
        Dzxyd dzxyd = dzxydService.queryByXid(xyd.getXid());
        if (dzxyd == null) {
            //该订单没有就上传记录
            Dzxyd dzxyd1 = new Dzxyd();
            dzxyd1.setXid(xyd.getXid());
            dzxyd1.setTime(new Date());
            dzxyd1.setTip(IdConfig.uuId());
            dzxyd1.setUrl(upload);
            dzxydService.save(dzxyd1);
        } else {
            //存在直接修改
            dzxyd.setUrl(upload);
            dzxydService.updateById(dzxyd);
        }
        log.info("*****进入editXydTask异步请求**dzxyd*****{}", dzxyd);
        Map<String, Object> formDatamap = new HashMap<>();
        //pngurl必须用阿里云oss地址，不要用服务器图片地址
        formDatamap.put("aimg", YdConfig.setTpList(upload, "协议单图片"));
        DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
        //修改值班店长
        formDatamap.put("zbdz", employee1 != null ? employee1.getUserid() : "");
        //城市
        formDatamap.put("cs", villa != null ? villa.getCity() : null);
        //每次修改都需要改成相反值
        formDatamap.put("sfdyjk", ("0".equals(jsonObject.get("sfdyjk"))) ? "1" : "0");
        //修改表单内容
        xgBdSl(formDatamap, formInstId, userId);
        try {
            if (tbXyd.getXztze() != null && tbXyd.getXztze() > 0) {
                String title = "该订单";
                int counts = 0;
                if (!tbXyd.getXztze().equals(tbXyd1.getXztze())) {
                    title += "策划金额、";
                    counts++;
                }
                if (!tbXyd.getXfd().equals(tbXyd1.getXfd())) {
                    title += "房东、";
                    counts++;
                }
                if (!tbXyd.getXbsmc().equals(tbXyd1.getXbsmc())) {
                    title += "别墅、";
                    counts++;
                }
                if (!tbXyd.getXjctime().equals(tbXyd1.getXjctime()) || !tbXyd.getXtctime().equals(tbXyd1.getXtctime())) {
                    title += "场次、";
                    counts++;
                }
                title = title.substring(0, title.length() - 1);
                title += "被修改";
                if (counts > 0) {
                    ch(tbXyd, title);
                }
            }
        } catch (Exception e) {
            log.info("*****进入editXydTask异步请求*策划主题2失败******{}", e);
        }
        //删除原有餐饮记录（宜搭表单：餐饮记录）
        JSONObject jsonObjectQuery = new JSONObject();
        jsonObjectQuery.put("cybh", tbXyd.getXddbh());
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = YdConfig.queryBdSlData(1, ydAppkey, token, jsonObjectQuery.toJSONString(), "FORM-6Q866L81621TOWRCYSHTECVS90KY10URT5RSKW5");
        } catch (Exception e) {
            log.info("*****进入editXydTask异步请求*删除原有订餐记录失败******{}", e);
        }
        if (gatewayResult.getSuccess()) {
            JSONObject jsonObject1 = JSONObject.parseObject(gatewayResult.getResult());
            JSONArray jsonArray = jsonObject1.getJSONArray("data");
            if (jsonArray != null && jsonArray.size() > 0) {
                jsonArray.forEach(l -> {
                    JSONObject item = (JSONObject) l;
                    String formInstanceId = item.getString("formInstanceId");
                    try {
                        //删除表单实例
                        DingBdLcConfig.scBdSl(finalToken, ydAppkey, "012412221639786136545", formInstanceId);
                    } catch (Exception e) {
                        log.info("*****进入editXydTask异步请求***删除订单出库之删除流程失败******{}", e);
                    }
                });
            }
        }
        //新增餐饮记录（宜搭表单：餐饮记录）
        Integer xsfdc = jsonObject.getInteger("xsfdc");
        if (xsfdc == 1) {
            JSONArray jsonArray = jsonObject.getJSONArray("cyzbd");
            String finalToken1 = token;
            try {
                jsonArray.forEach(item -> {
                    JSONObject l = (JSONObject) item;
                    l.put("cybh", tbXyd1.getXddbh());
                    try {
                        YdConfig.xzBdSl(finalToken1, ydAppkey, l.toJSONString(), fdUserId, "FORM-6Q866L81621TOWRCYSHTECVS90KY10URT5RSKW5");
                    } catch (Exception e) {
                        log.info("*****进入editXydTask异步请求*订餐出库失败******{}", e);
                    }
                });
            } catch (Exception e) {
                log.info("*****进入editXydTask异步请求*cyzbd失败******{}", e);
            }
        }
        //轰趴师播报
        try {
            if (tbXyd.getXhpsfy() != null) {
                if (!tbXyd1.getXhpsfy().equals(tbXyd.getXhpsfy())) {
                    if (tbXyd1.getXhpsfy() == 0 && tbXyd.getXhpsfy() > tbXyd1.getXhpsfy()) {
                        String text = "此订单被修改,新增了轰趴师(金额" + tbXyd.getXhpsfy() + ")，请联系房东[比心]" + tbXyd.getXfd() + "[比心]确认!";
                        text += "\n亲爱的轰趴师们，辛苦啦！！";
                        hps(tbXyd, text, dingkey, dingpan);
                    }
                    if (tbXyd1.getXhpsfy() != 0 && tbXyd.getXhpsfy() != 0) {
                        String text = "此订单被修改,修改了轰趴师(金额由" + tbXyd1.getXhpsfy() + "修改至" + tbXyd.getXhpsfy() + ")，请联系房东[比心]" + tbXyd.getXfd() + "[比心]确认!";
                        text += "\n亲爱的轰趴师们，辛苦啦！！";
                        hps(tbXyd, text, dingkey, dingpan);
                    }
                    if (tbXyd.getXhpsfy() == 0) {
                        String text = "此订单被修改,删除了轰趴师(金额" + tbXyd.getXhpsfy() + ")，请联系房东[比心]" + tbXyd.getXfd() + "[比心]确认!";
                        text += "\n亲爱的轰趴师们，辛苦啦！！";
                        hps(tbXyd, text, dingkey, dingpan);
                    }
                }
            }
        } catch (Exception e) {
            log.info("*****进入editXydTask异步请求*轰趴师播报失败******{}", e);
        }
        //剧本杀播报
        boolean bl = true;
        try {
            if (bl) {
                if (tbXyd.getXhpsfy() != null) {
                    if (tbXyd.getXhpsfy() > 0) {
                        if (!tbXyd.getXbsmc().equals(tbXyd1.getXbsmc())) {
                            if (!tbXyd.getXjctime().equals(tbXyd1.getXjctime()) || !tbXyd.getXtctime().equals(tbXyd1.getXtctime())) {
                                String text = "此轰趴师订单被修改,修改了别墅和场次(金额" + tbXyd.getXhpsfy() + ")，请联系房东[比心]" + tbXyd.getXfd() + "[比心]确认!";
                                text += "\n亲爱的轰趴师们，辛苦啦！！";
                                List<String> list = new ArrayList<>();
                                String wook = jqrConfig.getHpswebhook();
                                String se = null;
                                //线上
                                list.add("17673091798");
                                DingQunSend.sendQun1(dingkey, dingpan, "chatde3a0d1446cf4b2f99be143625faded5");
                                DingDingUtil.sendMsg(wook, se, text, list, false);
                                bl = false;
                            } else {
                                String text = "此轰趴师订单被修改,修改了场次(金额" + tbXyd.getXhpsfy() + ")，请联系房东[比心]" + tbXyd.getXfd() + "[比心]确认!";
                                text += "\n亲爱的轰趴师们，辛苦啦！！";
                                List<String> list = new ArrayList<>();
                                String wook = jqrConfig.getHpswebhook();
                                String se = null;
                                //线上
                                list.add("17673091798");
                                DingQunSend.sendQun1(dingkey, dingpan, "chatde3a0d1446cf4b2f99be143625faded5");
                                DingDingUtil.sendMsg(wook, se, text, list, false);
                                bl = false;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("*****进入editXydTask异步请求*修改表单实例失败******{}", e);
        }
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXltime(new Date());
        wlgbXydLog.setXlxid(xyd.getXid());
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLogService.save(wlgbXydLog);
        DingdingEmployee employee2 = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
        Map<String, Object> map2 = new HashMap<>();
        //协议单id
        map2.put("xid", tbXyd.getXid());
        //别墅id
        map2.put("bs", tbXyd.getXbsmc());
        //协议单图片
        map2.put("aimg", YdConfig.setTpList(upload, "协议单图片"));
        //客户电话
        map2.put("xzkdh", tbXyd.getXzkdh());
        //定金类型：收的钱
        map2.put("xysdj", tbXyd.getXysdj() != null ? tbXyd.getXysdj() : 0);
        map2.put("xxsdj", tbXyd.getXxsdj() != null ? tbXyd.getXxsdj() : 0);
        map2.put("xxxdj", tbXyd.getXxxdj() != null ? tbXyd.getXxxdj() : 0);
        //定金类型
        map2.put("xdjtype", tbXyd.getXdjtype() != null ? tbXyd.getXdjtype() : "1");
        //场地费原价
        map2.put("xqkzj", tbXyd.getXqkzj() != null ? tbXyd.getXqkzj() : 0);
        //人数
        map2.put("xrs", tbXyd.getXrs() != null ? tbXyd.getXrs() : 0);
        //超出人数/元
        map2.put("xcudrfy", tbXyd.getXcudrfy() != null ? tbXyd.getXcudrfy() : 0);
        //转发优惠至金额
        map2.put("xhfyj", tbXyd.getXhfyj() != null ? tbXyd.getXhfyj() : 0);
        //保险总额
        map2.put("xbxzje", tbXyd.getXbxzje() != null ? tbXyd.getXbxzje() : 0);
        //主题总额
        map2.put("xztze", tbXyd.getXztze() != null ? tbXyd.getXztze() : 0);
        //轰趴师费用
        map2.put("xhpsfy", tbXyd.getXhpsfy() != null ? tbXyd.getXhpsfy() : 0);
        //轰趴师姓名
        map2.put("xhpsxm", tbXyd.getXhpsxm() != null && !"".equals(tbXyd.getXhpsxm()) ? tbXyd.getXhpsxm() : null);
        //Cs真人费用
        map2.put("xzrcsfy", tbXyd.getXzrcsfy() != null ? tbXyd.getXzrcsfy() : 0);
        //订餐总额
        map2.put("xdcze", tbXyd.getXdcze() != null ? tbXyd.getXdcze() : 0);
        //烧烤总额
        map2.put("xskze", tbXyd.getXskze() != null ? tbXyd.getXskze() : 0);
        //好评数
        map2.put("xhpts", tbXyd.getXhpts() != null ? tbXyd.getXhpts() : 0);
        //共转数
        map2.put("xzfts", tbXyd.getXzfts() != null ? tbXyd.getXzfts() : 0);
        //集赞数
        map2.put("xjzts", tbXyd.getXjzts() != null ? tbXyd.getXjzts() : 0);
        //赠送的会员卡数
        map2.put("xzshyksl", tbXyd.getXzshyksl() != null && !"".equals(tbXyd.getXzshyksl()) ? tbXyd.getXzshyksl() : 0);
        //别墅名字
        map2.put("bsmc", villa.getVname());
        //客户姓名
        map2.put("xzk", tbXyd.getXzk());
        //补交定金
        map2.put("xbjdj", tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0);
        //值班店长ID
        map2.put("zbdz", employee2 != null ? employee2.getUserid() : null);
        //进场时间
        map2.put("qjctime", tbXyd.getXjctime());
        //退场时间
        map2.put("qtctime", tbXyd.getXtctime());
        //进场时间
        map2.put("jcsj", tbXyd.getXjctime());
        //退场时间
        map2.put("tcsj", tbXyd.getXtctime());
        //保险单价
        map2.put("xbxdj", tbXyd.getXbxdj() != null ? tbXyd.getXbxdj() : 0);
        //保险人数
        map2.put("xbxrs", tbXyd.getXbxrs() != null ? tbXyd.getXbxrs() : 0);
        //性质（运营查数据）
        map2.put("vxz", villa.getVxz());
        //城市
        String city = villa.getCszq() != null ? !"".equals(villa.getCszq()) ? villa.getCszq() : villa.getCity() : villa.getCity();
        map2.put("city", city);
        //商品优惠券
        map2.put("xspyhq", tbXyd.getXspyhq() != null ? tbXyd.getXspyhq() : 0);

        //客户姓名
        map2.put("qkhxm", tbXyd.getXzk());
        //客户身份证
        map2.put("qkhsfzh", tbXyd.getXzksfz());
        map2.put("xjbszje", tbXyd.getXjbszje() != null ? tbXyd.getXjbszje() : 0);
        //可能包含了下单及跟单、策划对接单、二次跟单、进场及消费等流程的实例ID
        List<WlgbDksljl> wlgbDksljls = wlgbDksljlService.queryByXid(xyd.getXid());
        //如果修改了别墅将之前的流程撤回
        if (!tbXyd1.getXbsmc().equals(tbXyd.getXbsmc())) {
            wlgbDksljls.forEach(l -> {
                //删除流程
                queryYdLcScRc(tbXyd, "修改别墅撤回之前的流程" + l.getBz(), l.getSlid(), userId, dingkey);
                wlgbDksljlService.removeById(l.getId());
            });
        }
        //时间差
        long l = tbXyd.getXjctime().getTime() - new Date().getTime();
        //进场时间减去下单时间的分钟数
        long l1 = l / 1000 / 60;
        System.out.println("差距分钟数：" + l1);

        Boolean b = false;
        if (!tbXyd1.getXbsmc().equals(tbXyd.getXbsmc())) {
            b = true;
        }

        if (!tbXyd1.getXjctime().equals(tbXyd.getXjctime())) {
            b = true;
        }

        //修改判断发送流程
        if (b) {
            lsdPd(tbXyd, l1, fdUserId, villa, employee2);
        }

        List<WlgbDksljl> wlgbDksljls1 = wlgbDksljlService.queryByXid(xyd.getXid());
        log.info("*****进入editXydTask异步请求**wlgbDksljls1*****{}", wlgbDksljls1);
        if (tbQrd == null) {
            for (WlgbDksljl sl : wlgbDksljls1) {
                if (sl != null) {
                    //修改流程实例容错
                    queryYdLcXgRc(tbXyd, sl.getBz(), map2, sl.getSlid(), ding != null ? ding.getUserid() : "012412221639786136545", dingkey);
                }
            }
        }
        //对账表单不再立即发起
        if (l1 <= (60 * 2)) {
            try {
                String xydUrl = dzXydCl(tbXyd1);
                //上传对账表单
                scDzBd(tbXyd, employee2, villa, userId, xydUrl, ydAppkey);
            } catch (Exception e) {
                log.info("*****进入editXydTask异步请求*上传对账表单2失败******{}", e);
                try {
                    DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : tbXyd.getXfd()) + "修改协议单，订单编号”" + tbXyd1.getXddbh() + "“对账表单修改报错了");
                } catch (ApiException apiException) {
                    apiException.printStackTrace();
                }
            }
        }
        //如果存在大于两个小时的对账表单还是同步修改
        if (l1 > (60 * 2)) {
            try {
                WlgbBdjl wlgbBdjl1 = wlgbBdjlService.queryByXidAndBz(tbXyd.getXid(), "对账表单提交");
                if (wlgbBdjl1 != null) {
                    try {
                        String xydUrl = dzXydCl(tbXyd1);
                        //上传对账表单
                        scDzBd(tbXyd, employee2, villa, userId, xydUrl, ydAppkey);
                    } catch (Exception e) {
                        log.info("*****进入editXydTask异步请求*上传对账表单失败******{}", e);
                        try {
                            DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : tbXyd.getXfd()) + "修改协议单，订单编号”" + tbXyd1.getXddbh() + "“对账表单修改报错了");
                        } catch (ApiException apiException) {
                            apiException.printStackTrace();
                        }
                    }
                }
            } catch (Exception e) {
                log.info("*****进入editXydTask异步请求*对账表单提交保存 失败******{}", e);
            }
        }

        DingdingEmployee employee3 = weiLianService.queryDingDingByName(tbXyd.getXfd());
        String userList = employee3 != null ? employee3.getUserid() : "";
        if (villa.getPid() != null && !"".equals(villa.getPid()) && !"null".equalsIgnoreCase(villa.getPid())) {
            if (employee3 != null) {
                if (!villa.getPid().equals(employee3.getUserid())) {
                    userList += "," + villa.getPid();
                }
            } else {
                userList += villa.getPid();
            }
        }
        String contexts = (ding != null ? ding.getName() : "") + "修改了订单！";
        System.out.println("发送人列表：" + userList);
        contexts += "\n\n别墅：" + villa.getVname();
        contexts += "\n\n房东：" + xyd.getXfd() + "";
        if (employee2 != null) {
            contexts += "\n\n店长：" + employee2.getName();
        }
        contexts += "\n\n送达时间：";
        try {
            DingDBConfig.sendGztz1(userList, dingkey, "电子协议单", contexts, upload);
        } catch (ApiException e) {
            log.info("*****进入editXydTask异步请求*发送工作通知5失败******{}", e);
        }

        //加盟商播报
        WlgbJmsbb wlgbJmsbb = weiLianDdXcxService.queryJmsBbByVid(villa.getVid());
        if (wlgbJmsbb != null) {
            try {
                DingDBConfig.sendGztz1(wlgbJmsbb.getBbrid(), dingkey, "电子协议单", contexts, upload);
            } catch (ApiException e) {
                log.info("*****进入editXydTask异步请求*发送工作通知3失败******{}", e);
            }
        }
        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd1.getXbsmc());
        //修改播报
        boolean xgBb = true;
        //通知原值班店长
        if (!tbXyd.getXbsmc().equals(tbXyd1.getXbsmc())) {
            xgBb = false;
            Date date2 = new Date();

            //如果有客单价申请，撤回客单价
            Map<String, Object> map5 = new HashMap<>();
            map5.put("vid", tbXyd1.getXbsmc());
            map5.put("jctime", tbXyd1.getXjctime());
            map5.put("tctime", tbXyd1.getXtctime());
            Integer sqKdj = weiLianDdXcxService.querySfSqKdj(map5);
            if (sqKdj > 0) {
                weiLianDdXcxService.zxKdjHfGc(map5);
            }
            if (date2.getTime() < tbXyd1.getXtctime().getTime()) {
                SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                SimpleDateFormat df3 = new SimpleDateFormat("yyyy年MM月dd日");
                //场次判断
                Date date = tbXyd1.getXjctime();
                int cc = 0;
                int hours = date.getHours();
                if (hours == 10) {
                    Date date1 = tbXyd1.getXtctime();
                    int hours1 = date1.getHours();
                    if (hours1 == 17) {
                        //白场
                        cc = 1;
                    } else if (hours1 == 8) {
                        //全天1
                        cc = 3;
                    } else {
                        //自定义
                        cc = 5;
                    }
                } else if (hours == 18) {
                    Date date1 = tbXyd1.getXtctime();
                    int hours1 = date1.getHours();
                    if (hours1 == 8) {
                        //晚场
                        cc = 2;
                    } else if (hours1 == 17) {
                        //全天2
                        cc = 4;
                    } else {
                        //自定义
                        cc = 5;
                    }
                } else {
                    //自定义
                    cc = 5;
                }
                FwqQunid fwqQunid = weiLianService.queryQun(tbVilla.getBbcity());
                if (fwqQunid != null) {
                    String webhook = fwqQunid.getQjqrweb();
                    String secret = fwqQunid.getQjqrser();
                    String content = tbVilla.getVname();
                    if (cc == 5) {
                        content += "\n进场时间：" + df2.format(tbXyd1.getXjctime());
                        content += "\n退场时间：" + df2.format(tbXyd1.getXtctime());
                    } else {
                        content += "，" + df3.format(tbXyd1.getXjctime()) + (cc == 1 ? "白场" : cc == 2 ? "晚场" : cc == 3 ? "全天1" : cc == 4 ? "全天2" : "");
                    }
                    content += "\n房东：" + tbXyd1.getXfd();
                    content += "\n已更改别墅，该场地空出，有单可下";
                    try {
                        DingDingUtil.sendMsg(webhook, secret, content, null, true);
                    } catch (Exception e) {
                        log.info("*****进入editXydTask异步请求*发送消息7失败******{}", e);
                    }
                }
            }
            if (tbVilla != null) {
                //判断有没有设置值班店长
                if (tbVilla.getPid() != null && !"".equals(tbVilla.getPid()) && !"null".equalsIgnoreCase(tbVilla.getPid())) {
                    //判断两个值班店长是否一致
                    if (!tbVilla.getPid().equals(villa.getPid())) {
                        //判断原店长与房东是否为一人
                        if (!(employee3 != null ? employee3.getUserid() : "").equals(tbVilla.getPid())) {
                            String context1 = (ding != null ? ding.getName() : "") + "修改了订单并修改了别墅！";
                            context1 += "\n\n别墅：" + villa.getVname();
                            context1 += "\n\n房东：" + xyd.getXfd() + "";
                            if (employee2 != null) {
                                context1 += "\n\n店长：" + employee2.getName();
                            }
                            context1 += "\n\n送达时间：";
                            try {
                                DingDBConfig.sendGztz1(tbVilla.getPid(), dingkey, "电子协议单", context1, upload);
                            } catch (ApiException e) {
                                log.info("*****进入editXydTask异步请求*发送工作通知1失败******{}", e);
                            }
                        }
                    }
                }
            }
        }
        if (xgBb) {
            if (tbXyd.getXjctime().getTime() != tbXyd1.getXjctime().getTime() || tbXyd.getXtctime().getTime() != tbXyd1.getXtctime().getTime()) {
                boolean b1 = belongCalendar(tbXyd1.getXjctime(), tbXyd.getXjctime(), tbXyd.getXtctime());
                boolean b2 = belongCalendar(tbXyd1.getXtctime(), tbXyd.getXjctime(), tbXyd.getXtctime());
                SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                SimpleDateFormat df3 = new SimpleDateFormat("yyyy年MM月dd日");
                Date date2 = new Date();
                FwqQunid fwqQunid = weiLianService.queryQun(villa.getBbcity());

                //两个时间不沾边
                if (!b1 && !b2) {
                    int cc = cc(tbXyd1.getXjctime(), tbXyd1.getXtctime());

                    //如果有客单价申请，撤回客单价
                    Map<String, Object> map5 = new HashMap<>();
                    map5.put("vid", tbXyd1.getXbsmc());
                    map5.put("jctime", tbXyd1.getXjctime());
                    map5.put("tctime", tbXyd1.getXtctime());

                    Integer sqKdj = weiLianDdXcxService.querySfSqKdj(map5);
                    if (sqKdj > 0) {
                        weiLianDdXcxService.zxKdjHfGc(map5);
                    }
                    if (date2.getTime() < tbXyd1.getXtctime().getTime()) {
                        if (fwqQunid != null) {
                            String webhook = fwqQunid.getQjqrweb();
                            String secret = fwqQunid.getQjqrser();
                            String content = villa.getVname();
                            if (cc == 5) {
                                content += "\n进场时间：" + df2.format(tbXyd1.getXjctime());
                                content += "\n退场时间：" + df2.format(tbXyd1.getXtctime());
                            } else {
                                content += "，" + df3.format(tbXyd1.getXjctime()) + (cc == 1 ? "白场" : cc == 2 ? "晚场" : cc == 3 ? "全天1" : cc == 4 ? "全天2" : "");
                            }
                            content += "\n房东：" + tbXyd1.getXfd();
                            content += "\n已更改场次，该场地空出，有单可下";
                            try {
                                DingDingUtil.sendMsg(webhook, secret, content, null, true);
                            } catch (Exception e) {
                                log.info("*****进入editXydTask异步请求*发送群消息6失败******{}", e);
                            }
                        }
                    }
                } else if (!b1 && b2) {
                    //进场时间不在范围内
                    int hours = tbXyd.getXjctime().getHours();
                    if (hours <= 10) {
                        tbXyd.getXjctime().setHours(8);
                    } else if (10 < hours && hours <= 18) {
                        tbXyd.getXjctime().setHours(17);
                    }
                    int cc = cc(tbXyd1.getXjctime(), tbXyd.getXjctime());

                    //如果有客单价申请，撤回客单价
                    Map<String, Object> map5 = new HashMap<>();
                    map5.put("vid", tbXyd1.getXbsmc());
                    map5.put("jctime", tbXyd1.getXjctime());
                    map5.put("tctime", tbXyd.getXjctime());

                    Integer sqKdj = weiLianDdXcxService.querySfSqKdj(map5);
                    if (sqKdj > 0) {
                        weiLianDdXcxService.zxKdjHfGc(map5);
                    }
                    if (date2.getTime() < tbXyd1.getXtctime().getTime()) {
                        if (fwqQunid != null) {
                            String webhook = fwqQunid.getQjqrweb();
                            String secret = fwqQunid.getQjqrser();
                            String content = villa.getVname();
                            if (cc == 5) {
                                content += "\n进场时间：" + df2.format(tbXyd.getXjctime());
                                content += "\n退场时间：" + df2.format(tbXyd1.getXjctime());
                            } else {
                                content += "，" + df3.format(tbXyd1.getXjctime()) + (cc == 1 ? "白场" : cc == 2 ? "晚场" : cc == 3 ? "全天1" : cc == 4 ? "全天2" : "");
                            }
                            content += "\n房东：" + tbXyd1.getXfd();
                            content += "\n已更改场次，该场地空出，有单可下";
                            try {
                                DingDingUtil.sendMsg(webhook, secret, content, null, true);
                            } catch (Exception e) {
                                log.info("*****进入editXydTask异步请求*发送群消息5失败******{}", e);
                            }
                        }
                    }
                } else if (b1 && !b2) {
                    //退场时间不在范围内
                    int hours = tbXyd.getXtctime().getHours();
                    if (hours < 10) {
                        tbXyd.getXtctime().setHours(10);
                    } else if (10 < hours && hours < 18) {
                        tbXyd.getXtctime().setHours(18);
                    }
                    int cc = cc(tbXyd.getXtctime(), tbXyd1.getXtctime());

                    //如果有客单价申请，撤回客单价
                    Map<String, Object> map5 = new HashMap<>();
                    map5.put("vid", tbXyd1.getXbsmc());
                    map5.put("jctime", tbXyd.getXtctime());
                    map5.put("tctime", tbXyd1.getXtctime());

                    Integer sqKdj = weiLianDdXcxService.querySfSqKdj(map5);
                    if (sqKdj > 0) {
                        weiLianDdXcxService.zxKdjHfGc(map5);
                    }
                    if (date2.getTime() < tbXyd1.getXtctime().getTime()) {
                        if (fwqQunid != null) {
                            String webhook = fwqQunid.getQjqrweb();
                            String secret = fwqQunid.getQjqrser();
                            String content = villa.getVname();
                            if (cc == 5) {
                                content += "\n进场时间：" + df2.format(tbXyd1.getXjctime());
                                content += "\n退场时间：" + df2.format(tbXyd1.getXtctime());
                            } else {
                                content += "，" + df3.format(tbXyd.getXtctime()) + (cc == 1 ? "白场" : cc == 2 ? "晚场" : cc == 3 ? "全天1" : cc == 4 ? "全天2" : "");
                            }
                            content += "\n房东：" + tbXyd.getXfd();
                            content += "\n已更改场次，该场地空出，有单可下";
                            try {
                                DingDingUtil.sendMsg(webhook, secret, content, null, true);
                            } catch (Exception e) {
                                log.info("*****进入editXydTask异步请求*发送群消息3失败******{}", e);
                            }
                        }
                    }
                }
            }
        }
        log.info("*****进入editXydTask异步请求 结束*******{}", datas);
    }

    /**
     * 删除表单
     *
     * @param slid 实例id
     */
    public GatewayResult scBdSl(String slid) {
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            log.info("*****进入editXydTask异步请求*删除表单实例之获取token失败******{}", e);
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.scBdSl(token, ydAppkey, "012412221639786136545", slid);
        } catch (Exception e) {
            gatewayResult = new GatewayResult();
            log.info("*****进入editXydTask异步请求*删除表单失败******{}", e);
        }

        return gatewayResult;
    }

    /**
     * 选人截取
     *
     * @param userid 用户id
     * @return 用户id
     */
    private String xzjq(String userid) {
        String s = userid.substring(0, 1);
        if ("[".equals(s)) {
            userid = userid.substring(2, userid.length() - 2);
        } else {
            userid = userid;
        }
        return userid;
    }

    /**
     * 对协议单部分数据赋值
     *
     * @param tbXyd ---协议单
     */
    private TbXyd fz(TbXyd tbXyd) {
        //进场场次
        Calendar c = Calendar.getInstance();
        c.setTime(tbXyd.getXjctime());
        int hours = c.get(Calendar.HOUR_OF_DAY);
        if (9 <= hours && hours <= 17) {
            tbXyd.setXjcflag(0);
        } else {
            tbXyd.setXjcflag(1);
        }

        //退场场次
        Calendar c2 = Calendar.getInstance();
        c2.setTime(tbXyd.getXtctime());
        int hour = c2.get(Calendar.HOUR_OF_DAY);
        if (9 <= hour && hour <= 17) {
            tbXyd.setXtcflag(0);
        } else {
            tbXyd.setXtcflag(1);
        }

        VillaIDsUtil iDsUtil = weiLianService.queryIdsByVid(tbXyd.getXbsmc());
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        //别墅分区
        if (iDsUtil != null) {
            tbXyd.setCshenfen(iDsUtil.getSid());
            tbXyd.setCfenqu(iDsUtil.getFid());
            tbXyd.setCompany(iDsUtil.getCid());
        }

        //协议单的值班店长
        if (villa != null) {
            if (villa.getPid() != null && !"".equals(villa.getPid()) && !"null".equalsIgnoreCase(villa.getPid())) {
                DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
                if (dingdingEmployee != null) {
                    tbXyd.setXdzid(villa.getPid());
                }
            }
        }

        //房东
        if (tbXyd.getXfd() != null && !"".equals(tbXyd.getXfd())) {
            DingdingEmployee fd = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXfd());
            if (fd != null) {
                tbXyd.setXfd(fd.getName());
                tbXyd.setXfdgh(fd.getJobnumber());
            }
        }

        //老板
        if (tbXyd.getXlbjf() != null && !"".equals(tbXyd.getXlbjf())) {
            DingdingEmployee lb = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXlbjf());
            if (lb != null) {
                tbXyd.setXlbjf(lb.getName());
            }
        }
        //校代上级
        if (tbXyd.getXxdsjxm() != null && !"".equals(tbXyd.getXxdsjxm())) {
            DingdingEmployee xdsj = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXxdsjxm());
            if (xdsj != null) {
                tbXyd.setXxdsjxm(xdsj.getName());
                tbXyd.setXxdsjxmgh(xdsj.getJobnumber());
            }
        }

        //保险成交人
        if (tbXyd.getXbxcjr() != null && !"".equals(tbXyd.getXbxcjr())) {
            DingdingEmployee xbxcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXbxcjr());
            if (xbxcjr != null) {
                tbXyd.setXbxcjr(xbxcjr.getName());
                tbXyd.setXbxcjrgh(xbxcjr.getJobnumber());
            }
        }
        //轰趴师成交人
        if (tbXyd.getXhpscjr() != null && !"".equals(tbXyd.getXhpscjr())) {
            DingdingEmployee hpscjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXhpscjr());
            if (hpscjr != null) {
                tbXyd.setXhpscjr(hpscjr.getName());
                tbXyd.setXhpscjrgh(hpscjr.getJobnumber());
            }
        }
        //订餐成交人
        if (tbXyd.getXdccjr() != null && !"".equals(tbXyd.getXdccjr())) {
            DingdingEmployee dccjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXdccjr());
            if (dccjr != null) {
                tbXyd.setXdccjr(dccjr.getName());
                tbXyd.setXdccjrgh(dccjr.getJobnumber());
            }
        }
        //烧烤成交人
        if (tbXyd.getXskcjr() != null && !"".equals(tbXyd.getXskcjr())) {
            DingdingEmployee skcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXskcjr());
            if (skcjr != null) {
                tbXyd.setXskcjr(skcjr.getName());
                tbXyd.setXskcjrgh(skcjr.getJobnumber());
            }
        }
        //现场成交人
        if (tbXyd.getXcjr() != null && !"".equals(tbXyd.getXcjr())) {
            DingdingEmployee xccjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXcjr());
            if (xccjr != null) {
                tbXyd.setXcjr(xccjr.getName());
                tbXyd.setXcjrgh(xccjr.getJobnumber());
            }
        }
        //后续成交人
        if (tbXyd.getXhxcjr() != null && !"".equals(tbXyd.getXhxcjr())) {
            DingdingEmployee hxcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXhxcjr());
            if (hxcjr != null) {
                tbXyd.setXhxcjr(hxcjr.getName());
                tbXyd.setXhxcjrgh(hxcjr.getJobnumber());
            }
        }
        //策划经理
        if (tbXyd.getXchjlid() != null && !"".equals(tbXyd.getXchjlid())) {
            DingdingEmployee chjl = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXchjlid());
            if (chjl != null) {
                tbXyd.setXchjl(chjl.getName());
            }
        }
        //策划成交人
        if (tbXyd.getXchcjr() != null && !"".equals(tbXyd.getXchcjr())) {
            DingdingEmployee chcj = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXchcjr());
            if (chcj != null) {
                tbXyd.setXchcjr(chcj.getName());
                tbXyd.setXchcjrgh(chcj.getJobnumber());
            }
        }
        //策划辅助成交人
        if (tbXyd.getXchfzcjr() != null && !"".equals(tbXyd.getXchfzcjr()) && tbXyd.getXchfzcjr().length() > 0) {
            DingdingEmployee chhzcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXchfzcjr());
            if (chhzcjr != null) {
                tbXyd.setXchfzcjr(chhzcjr.getName());
            }
        }
        //订餐辅助成交人
        if (tbXyd.getXdcfzcjr() != null && !"".equals(tbXyd.getXdcfzcjr()) && tbXyd.getXdcfzcjr().length() > 0) {
            DingdingEmployee dchzcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXdcfzcjr());
            if (dchzcjr != null) {
                tbXyd.setXdcfzcjr(dchzcjr.getName());
            }
        }
        //烧烤辅助成交人
        if (tbXyd.getXskfzcjr() != null && !"".equals(tbXyd.getXskfzcjr()) && tbXyd.getXskfzcjr().length() > 0) {
            DingdingEmployee skhzcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXskfzcjr());
            if (skhzcjr != null) {
                tbXyd.setXskfzcjr(skhzcjr.getName());
            }
        }
        //真人cs成交人
        if (tbXyd.getXzrcscjr() != null && !"".equals(tbXyd.getXzrcscjr()) && tbXyd.getXzrcscjr().length() > 0) {
            DingdingEmployee zrcscjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXzrcscjr());
            if (zrcscjr != null) {
                tbXyd.setXzrcscjr(zrcscjr.getName());
            }
        }
        //团队性质
        if (tbXyd.getXtdxz() != null && !"".equals(tbXyd.getXtdxz()) && tbXyd.getXtdxz().length() > 0) {
            TbKhlyxz khlyxz = weiLianService.queryKhLyXzByLidOrLname(tbXyd.getXtdxz());
            if (khlyxz != null) {
                tbXyd.setXtdxz(khlyxz.getLid());
            }
        }
        //客户来源
        if (tbXyd.getXkhly() != null && !"".equals(tbXyd.getXkhly()) && tbXyd.getXkhly().length() > 0) {
            TbKhlyxz khlyxz = weiLianService.queryKhLyXzByLidOrLname(tbXyd.getXkhly());
            if (khlyxz != null) {
                tbXyd.setXkhly(khlyxz.getLid());
            }
        }

        //TODO 剧本杀（不上线需要注释）
        //剧本杀成交人
        if (tbXyd.getXjbscjr() != null && !"".equals(tbXyd.getXjbscjr()) && tbXyd.getXjbscjr().length() > 0) {
            DingdingEmployee jbscjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXjbscjr());
            if (jbscjr != null) {
                tbXyd.setXjbscjr(jbscjr.getName());
                tbXyd.setXjbscjrid(jbscjr.getUserid());
            }
        }
        //剧本杀辅助成交人
        if (tbXyd.getXjbsfzcjr() != null && !"".equals(tbXyd.getXjbsfzcjr()) && tbXyd.getXjbsfzcjr().length() > 0) {
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXjbsfzcjr());
            if (employee != null) {
                tbXyd.setXjbsfzcjr(employee.getName());
                tbXyd.setXjbsfzcjrid(employee.getUserid());
            }
        }
        //轰趴师辅助成交人
        if (tbXyd.getXhpsfzcjr() != null && !"".equals(tbXyd.getXhpsfzcjr()) && tbXyd.getXhpsfzcjr().length() > 0) {
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXhpsfzcjr());
            if (employee != null) {
                tbXyd.setXhpsfzcjr(employee.getName());
                tbXyd.setXhpsfzcjrid(employee.getUserid());
            }
        }

        return tbXyd;
    }

    /**
     * 生成电子协议单
     *
     * @param xyd 协议单数据
     */
    @SneakyThrows
    private Map<String, Object> dzXyd(TbXyd xyd, JSONObject jsonObject) throws TemplateException, IOException {
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(xyd.getXbsmc());
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy年MM月dd日");
        String zpTime = "进场：" + DateFormatConfig.df1(xyd.getXjctime()) + "<br />退场：" + DateFormatConfig.df1(xyd.getXtctime());
        //除场地费之外的其他费用
        double v = (xyd.getXzrcsfy() != null ? xyd.getXzrcsfy() : 0) + (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXskze() != null ? xyd.getXskze() : 0) + (xyd.getXbxzje() != null ? xyd.getXbxzje() : 0) + (xyd.getXztze() != null ? xyd.getXztze() : 0) + (xyd.getXhpsfy() != null ? xyd.getXhpsfy() : 0) + (xyd.getXjbszje() != null ? xyd.getXjbszje() : 0);
        Map<String, Object> map = new HashMap<>();
        //苏州协议单设置
        map.put("cityType", "苏州".equals(villa.getCity()) ? "1" : "0");
        //订单编号
        map.put("ddbh", xyd.getXddbh());
        //别墅名字
        map.put("bsName", villa.getVname());
        //老板姓名
        map.put("lbxm", xyd.getXlbjf());
        //房东姓名
        map.put("fdName", xyd.getXfd());
        //房东电话
        map.put("fdTelphone", xyd.getXfddh());
        //租客姓名
        map.put("zkName", xyd.getXzk());
        //租客电话
        map.put("zkTelphone", xyd.getXzkdh());
        //租客身份证号码
        map.put("zksfz", xyd.getXzksfz());
        TbKhlyxz khlyxz = weiLianService.queryKhLyXzByLidOrLname(xyd.getXtdxz());
        //公司名称（性质）
        map.put("dwmc", xyd.getXdwmc() + (khlyxz != null ? "(" + khlyxz.getLname() + ")" : ""));
        TbKhlyxz tbKhlyxz = weiLianService.queryKhLyXzByLidOrLname(xyd.getXkhly());
        //客户来源
        map.put("khly", (tbKhlyxz != null ? tbKhlyxz.getLname() : ""));
        //租赁时间（进场时间与退场时间）
        map.put("zpTime", zpTime);
        //进场时间
        map.put("jxtime", "进场:" + DateFormatConfig.df1(xyd.getXjctime()));
        //退场时间
        map.put("tctime", "退场:" + DateFormatConfig.df1(xyd.getXtctime()));
        //中餐单品总额
        Double xzcdpze = jsonObject.getObject("xzcdpze", Double.class);
        if (xzcdpze == null) {
            xzcdpze = 0.0;
        }
        map.put("xzcdpze", xzcdpze);
        //烧烤火锅总额
        Double xskhgze = jsonObject.getObject("xskhgze", Double.class);
        if (xskhgze == null) {
            xskhgze = 0.0;
        }
        map.put("xskhgze", xskhgze);
        //烤全羊总额
        Double xkqyze = jsonObject.getObject("xkqyze", Double.class);
        if (xkqyze == null) {
            xkqyze = 0.0;
        }
        map.put("xkqyze", xkqyze);
        //龙虾宴总额
        Double xlxyze = jsonObject.getObject("xlxyze", Double.class);
        if (xlxyze == null) {
            xlxyze = 0.0;
        }
        map.put("xlxyze", xlxyze);
        //自助餐/甜品总额
        Double xzzcze = jsonObject.getObject("xzzcze", Double.class);
        if (xzzcze == null) {
            xzzcze = 0.0;
        }
        map.put("xzzcze", xzzcze);
        //单点
        Double xddcyze = jsonObject.getObject("xddcyze", Double.class);
        if (xddcyze == null) {
            xddcyze = 0.0;
        }
        map.put("xddcyze", xddcyze);
        //商品零食总额
        Double xsplsze = (xyd.getXsplsze() != null ? xyd.getXsplsze() : 0);
        if (xsplsze == null) {
            xsplsze = 0.0;
        }
        map.put("xsplsze", xsplsze);
        //人数
        map.put("rs", xyd.getXrs());
        //超出人数加收
        map.put("ccMoney", xyd.getXcudrfy());
        //支付宝订单号后六位
        String hlw = "";
        if (xyd.getXzzhlw() != null && !"".equals(xyd.getXzzhlw()) && xyd.getXzzhlw().length() > 0) {
            if (xyd.getXzzhlw().length() > 6) {
                hlw = xyd.getXzzhlw().substring(xyd.getXzzhlw().length() - 6);
            } else {
                hlw = xyd.getXzzhlw();
            }
        }
        //支付宝订单号后两位
        map.put("zfbddh", hlw);
        //支付宝转账定金时间
        map.put("zfbzzTime", xyd.getXzzsj() != null ? DateFormatConfig.df1(xyd.getXzzsj()) : "");
        //场地租赁费
        map.put("qkje", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0));
        //增值定金
        map.put("xyszzdj", (!xyd.getXyszzdj().isEmpty()) ? Double.parseDouble(xyd.getXyszzdj()) : 0);
        //增值费用补交
        map.put("xzzfybj", xyd.getXzzfybj() != null ? xyd.getXzzfybj() : 0);
        //真人CS(关闭)
//        map.put("csfyxm", xyd.getXzrcsfy() != null ? xyd.getXzrcsfy() : 0);
        //剧本杀
        map.put("xjbs", xyd.getXjbszje() != null ? xyd.getXjbszje() : 0);
        //已收场地费定金=已收定金+场地费租金补交-增值定金
        double ysdj = (xyd.getXysdj() != null ? xyd.getXysdj() : 0) + (xyd.getXcdzjbj() != null ? xyd.getXcdzjbj() : 0) - (xyd.getXyszzdj() != null && !"".equals(xyd.getXyszzdj()) ? Double.parseDouble(xyd.getXyszzdj()) : 0);
        if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(xyd.getXdjtype())) {
            Double xwsywdddsyje = xyd.getXwsywdddsyje();
            if (xwsywdddsyje != null) {
                ysdj += xwsywdddsyje;
            }
        }
        map.put("xyscdfdj", ysdj);
        //代码
        String dm = "";
        if (xyd.getXdm() != null && !"".equals(xyd.getXdm()) && xyd.getXdm().length() > 0) {
            if (xyd.getXdm().length() > 6) {
                dm = xyd.getXdm().substring(0, 6);
            } else {
                dm = xyd.getXdm();
            }
        }
        map.put("dm", dm);
        //付款码
        String fkm = "";
        if (xyd.getXfkm() != null && !"".equals(xyd.getXfkm()) && xyd.getXfkm().length() > 0) {
            if (xyd.getXfkm().length() > 2) {
                fkm = xyd.getXfkm().substring(0, 2);
            } else {
                fkm = xyd.getXfkm();
            }
        }
        map.put("zfbfkm", fkm);
        //订餐+烧烤
        map.put("dctczjsktczj", (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXskze() != null ? xyd.getXskze() : 0));
        map.put("xdcze", (xyd.getXdcze() != null ? xyd.getXdcze() : 0));
        map.put("xskze", (xyd.getXskze() != null ? xyd.getXskze() : 0));
        //保险
        map.put("xbxzje", xyd.getXbxzje() != null ? xyd.getXbxzje() : 0);
        //策划
        map.put("zttczj", xyd.getXztze() != null ? xyd.getXztze() : 0);
        //轰趴师
        map.put("sfhps", xyd.getXhpsfy() != null ? xyd.getXhpsfy() : 0);
        //全款金额
        map.put("qkje3", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) + v);
        //优惠至全款
        map.put("qkje4", (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) + v);
        //优惠金额
        map.put("qkje2", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) - (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) > 0 ? (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) - (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) : 0);
        //转发
        map.put("zgdst", xyd.getXzfts() != null ? xyd.getXzfts() : 0);
        //好评
        map.put("hpdst", xyd.getXhpts() != null ? xyd.getXhpts() : 0);
        //集赞
        map.put("jzst", xyd.getXjzts() != null ? xyd.getXjzts() : 0);
        //消费券
        map.put("xspyhq", xyd.getXspyhq() != null ? xyd.getXspyhq() : 0);
        //值班店长
        map.put("zbdzxm", dingdingEmployee != null ? dingdingEmployee.getName() : "");
        //值班店长电话
        map.put("zbdhxm", dingdingEmployee != null ? dingdingEmployee.getMobile() : "");
        //是否现场成交
        map.put("sfxccj", xyd.getXisxzcj() != null && xyd.getXisxzcj() == 0 ? "是" : "否");
        //是否泳池单
        map.put("sfycd", "否");
        //校代姓名
        map.put("xxdxm", xyd.getXxdxm());
        //赠送特权会员卡
        map.put("zstkhyk", xyd.getXzshyksl() != null ? xyd.getXzshyksl() : 0);
        //备注
        map.put("bzxx", xyd.getXsxbz());
        //甲方签名
        map.put("jfxm", xyd.getXisxzcj() != null && xyd.getXisxzcj() == 0 ? xyd.getXcjr() : xyd.getXfd());
        //乙方签名
        map.put("yfxm", xyd.getXzk());
        //日期
        map.put("rq", xyd.getXgsj() != null ? formatter1.format(xyd.getXgsj()) : formatter1.format(xyd.getXkhtjtime()));
        //别墅所在城市
        map.put("city", villa.getCity());
        //摄影图片数量
        map.put("xsynum", xyd.getXsynum());
        //定制横幅内容
        map.put("xhfnr", (xyd.getXhfnr() == null ? "无" : xyd.getXhfnr()));
        //定制大屏内容
        map.put("xdpnr", (xyd.getXdpnr() == null ? "无" : xyd.getXdpnr()));
        //是否购买三件套：0否，1是
        map.put("xsfgmsjt", (xyd.getXsfgmsjt() == null ? "无" : xyd.getXsfgmsjt()));
        //抖音数量
        map.put("xdynum", (xyd.getXdynum() == null ? "无" : xyd.getXdynum()));
        //关注数量
        map.put("xgznum", (xyd.getXgznum() == null ? "无" : xyd.getXgznum()));
        //收藏数量
        map.put("xscnum", (xyd.getXscnum() == null ? "无" : xyd.getXscnum()));
        //主播姓名
        map.put("xzbxm", xyd.getXzbxm());
        //模板
        String tempName = "";
        boolean isnew = false;
        tempName = FileConfig.getFileAbsolutePath2("static" + File.separator + "template2.html");
        String context = PDFUtil.freeMarkerRender(map, tempName);
        String id = "DZHC" + DateFormatConfig.df2(new Date()) + IdConfig.uuId();
        String pdf = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
        String png = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".png");
        File newPdf = new File(pdf);
        if (!newPdf.exists()) {
            log.error("文11件不存在pdf: {}", pdf);
            log.error("文22件不存在png: {}", png);
            log.error("文33件不存在context: {}", context);
        }
        Map<String, Object> map1 = new HashMap<>();
        try {
            // 生成pdf
            PDFUtil.createPdf(context, newPdf.getPath(), isnew);
            // 生成图片
            PDFUtil.pdfToImg(newPdf.getPath(), 1, png);
            log.info("*****进入xydSaveTask异步请求*生成图片成功******{}", newPdf.getPath());
            map1.put("png", id + ".png");
            // 删除pdf临时文件
            if (!newPdf.delete()) {
                log.warn("删除pdf临时文件报错: {}", newPdf.getPath());
            }
        } catch (Exception e) {
            log.error("生成PDF/png文件失败: ", e);
        }
        return map1;
    }

    /**
     * 修改表单实例
     *
     * @param id     要更新的表单数据ID
     * @param map    表单内容
     * @param userid 用户id
     */
    public GatewayResult xgBdSl(Map<String, Object> map, String id, String userid) {
        JSONObject json = new JSONObject(map);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            log.info("*****进入xydSaveTask异步请求*修改表单实例之获取token 失败******{}", e);
        }
        GatewayResult gatewayResult = null;
        try {
            Map<String, Object> map2 = new HashMap<>();
            map2.put("ddtoken", token);
            map2.put("ydAppkey", ydAppkey);
            map2.put("json", json.toJSONString());
            map2.put("userId", userid);
            map2.put("formInstanceId", id);
            map2.put("type", "bd_update");
//            gatewayResult =  DingBdLcConfig.ydbdAndlc(map2);

            gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, id, json.toJSONString());
        } catch (Exception e) {
            log.info("*****进入xydSaveTask异步请求*修改表单实例失败******{}", e);
        }
        return gatewayResult;
    }

    /**
     * 机器人发送策划群
     *
     * @param tbXyd 协议单
     * @param title 标题
     */
    public void ch(TbXyd tbXyd, String title) {
        List<String> list = new ArrayList<>();
        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        TbKhlyxz khlyxz = weiLianService.queryKhLyXzByLidOrLname(tbXyd.getXtdxz());
        DingdingEmployee dingdingEmployee = null;
        if (tbVilla.getPid() != null && !"null".equalsIgnoreCase(tbVilla.getPid()) && !"".equals(tbVilla.getPid())) {
            dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbVilla.getPid());
        }
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String context = "#### <font color=#ff0303 size=7>" + title + "</font>";
        context += "\n\n><font size=4>房东</font>:<" + tbXyd.getXfd() + ">";
        context += "\n\n><font size=4>别墅</font>:<<font color=#ff0303>" + tbVilla.getVname() + "</font>>";
        context += "\n\n><font size=4>场次</font>:<<font color=#ff0303>进场：" + df2.format(tbXyd.getXjctime()) + "</font>>";
        context += "\n\n><font size=4>场次</font>:<<font color=#ff0303>退场：" + df2.format(tbXyd.getXtctime()) + "</font>>";
        context += "\n\n><font size=4>性质</font>:<<font color=#ff0303>" + tbXyd.getXdwmc() + "(" + khlyxz.getLname() + ")</font>>";
        context += "\n\n><font size=4>策划金额</font>:<<font color=#ff0303>" + (tbXyd.getXztze() != null ? tbXyd.getXztze() : 0) + "</font>>";
        context += "\n\n><font size=4>策划经理</font>:<<font color=#ff0303>" + (tbXyd.getXchjl() != null ? tbXyd.getXchjl() : "") + "</font>>";
        context += "\n\n><font size=4>策划成交人</font>:<" + (tbXyd.getXchcjr() != null ? tbXyd.getXchcjr() : "") + ">";
        context += "\n\n><font size=4>策划辅助成交人</font>:<" + (tbXyd.getXchfzcjr() != null ? tbXyd.getXchfzcjr() : "") + ">";
        if (dingdingEmployee != null) {
            context += "\n\n><font size=4>值班店长</font>:<" + dingdingEmployee.getName() + ">";
        }
        context += "\n\n><font size=4>备注信息</font>:<" + tbXyd.getXsxbz() + ">";
        Calendar calendar1 = Calendar.getInstance();
        context += "\n\n> ###### " + df2.format(calendar1.getTime()) + "     [发送]";
        String wook = jqrConfig.getChwebhook();
        String se = null;
        //线下
        se = jqrConfig.getChkey();
        //@17673091798
        list.add("17673091798");
        DingDingUtil.sendMark(wook, se, title, context, list, false);
    }

    /**
     * 机器人发送轰趴师群
     *
     * @param tbXyd   ---协议单
     * @param context 发送内容
     */
    public void hps(TbXyd tbXyd, String context, Dingkey dingkey, String dingpan) throws Exception {
        List<String> list = new ArrayList<>();
        String wook = jqrConfig.getHpswebhook();
        String se = null;
        //线上
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        if (villa != null) {
            list = weiLianService.queryHpsAt(villa.getCity());
        }
        //@17673091798
        list.add("17673091798");
        DingQunSend.sendQun1(dingkey, dingpan, "chatde3a0d1446cf4b2f99be143625faded5");
        DingDingUtil.sendMsg(wook, se, context, list, false);
    }

    /**
     * 电子协议单处理
     *
     * @param tbXyd 协议单对象
     * @return 协议单地址
     */
    public String dzXydCl(TbXyd tbXyd) throws Exception {
        Dzxyd dzxyd = dzxydService.queryByXid(tbXyd.getXid());
        String upload;
        if (dzxyd != null) {
            //电子协议单
            upload = dzxyd.getUrl();
        } else {
            String bddz = "D:\\tomcat9\\webapps\\weilian3\\";
            File file1 = new File(bddz + tbXyd.getXimagepath());
            //判断图片存在不存在,如果不存在将重新生成电子协议单
            if (!file1.exists()) {
//                dzXyd(tbXyd);
            }
            File file = new File(bddz + tbXyd.getXimagepath());
            FileInputStream fileInputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
            fileInputStream.close();
            upload = ossFileService.upload(multipartFile);
            if (upload != null && !"".equals(upload)) {
                upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
            }
            Dzxyd dzxyd1 = new Dzxyd();
            dzxyd1.setUrl(upload);
            dzxyd1.setTip(IdConfig.uuId());
            dzxyd1.setXid(tbXyd.getXid());
            dzxyd1.setTime(new Date());
            dzxydService.save(dzxyd1);
        }
        return upload;
    }

    /**
     * 上传对账表单
     *
     * @param tbXyd 协议单对象
     */
    public void scDzBd(TbXyd tbXyd, DingdingEmployee employee2, TbVilla villa, String userid, String xydUrl, YdAppkey ydAppkey) throws Exception {
        //本场基准价
        Map<String, Object> map = new HashMap<>();
        map.put("jcsj", tbXyd.getXjctime());
        map.put("tcsj", tbXyd.getXtctime());
        map.put("vid", tbXyd.getXbsmc());
        Double jzj = weiLianService.queryKdjJzj(map);
        WlgbBdjl wlgbBdjl2 = wlgbBdjlService.queryByXidAndBz(tbXyd.getXid(), "对账表单提交");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(tbXyd.getXid());


        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("对账");
        WlgbBdjl wlgbBdjl = DzBdConfig.scDzBd(tbXyd, employee2, villa, ding, dingkey, wlgbBdjl2, jzj, xydUrl, ydAppkey, ydBd, tbYddXyd != null ? "是" : "否");
        if (wlgbBdjl != null) {
            if (wlgbBdjl2 != null) {
                wlgbBdjlService.updateById(wlgbBdjl);
            } else {
                wlgbBdjlService.save(wlgbBdjl);
            }
        }
    }

    /**
     * 删除宜搭流程容错机制
     *
     * @param tbXyd      协议单对象
     * @param name       操作名称
     * @param formInstId 实例id
     * @param userid     操作人id
     * @param dingkey    钉钉key
     */
    public void queryYdLcScRc(TbXyd tbXyd, String name, String formInstId, String userid, Dingkey dingkey) {
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            log.info("*****进入editXydTask异步请求*删除流程之获取token失败******{}", e);
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        for (int i = 0; i < 5; i++) {
            GatewayResult gatewayResult;
            try {
                //不需要传id，默认使用系统机器人
                gatewayResult = scLcSl(formInstId, token, ydAppkey);
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                log.info("*****进入editXydTask异步请求*删除实例失败******{}", e);
            }
            if (!gatewayResult.getSuccess()) {
                if (i == 4) {
                    try {
                        DingQunSend.send((ding != null ? ding.getName() : tbXyd.getXfd()) + "正在" + name + "，删除流程出错了，订单编号：" + tbXyd.getXddbh() + "，错误原因：" + gatewayResult.toString(), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e) {
                        log.info("*****进入editXydTask异步请求*删除流程之发送消息失败******{}", e);
                    }
                }
            } else {
                break;
            }
        }
    }

    /**
     * 临时单判断
     *
     * @param tbXyd     查询协议单
     * @param l1        时间差（分钟）
     * @param fdUserId  房东id
     * @param villa     别墅对象
     * @param employee2 店长对象
     */
    public void lsdPd(TbXyd tbXyd, long l1, String fdUserId, TbVilla villa, DingdingEmployee employee2) {
        //如果时间差的分钟数小于等于24个小时的分钟发送进场及消费，否则发送下单及跟单
        Integer count1 = weiLianDdXcxService.queryBsCount(tbXyd.getXbsmc());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String xydUrl = null;
        try {
            xydUrl = dzXydCl(tbXyd);
        } catch (Exception e) {
            log.info("*****进入editXydTask异步请求*临时单判断失败******{}", e);
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(fdUserId);
        String vxz = villa.getVxz();
        if (count1 == 0) {
            if (l1 <= (60 * 3) || tbXyd.getXjctime().getTime() < new Date().getTime()) {
                WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid("下单及跟单", tbXyd.getXid());
                WlgbDksljl dksljl1 = wlgbDksljlService.queryByBzAndXid("二级跟单", tbXyd.getXid());
                //下单及跟单
                if (dksljl != null) {
                    //删除容错机制
                    queryYdLcScRc(tbXyd, "切换场次下单及跟单", dksljl.getSlid(), fdUserId, dingkey);
                    wlgbDksljlService.removeById(dksljl.getId());
                }
                //二级跟单
                if (dksljl1 != null) {
                    //删除容错机制
                    queryYdLcScRc(tbXyd, "切换场次二次跟单", dksljl1.getSlid(), fdUserId, dingkey);
                    wlgbDksljlService.removeById(dksljl1.getId());
                }
                if ("直营".equals(vxz)) {
                    Csry csry = weiLianDaiBanService.queryCsRyByUserId(villa.getPid());
                    if (csry != null) {
                        int count = wlgbDksljlService.queryCountByBzAndXid("进场及消费", tbXyd.getXid());
                        if (count == 0) {
                            //两个小时内才发流程
                            if (l1 <= (60 * 2) || tbXyd.getXjctime().getTime() < new Date().getTime()) {
                                WlgbDksljl dksljl4 = wlgbDksljlService.queryByBzAndXid("进场及消费", tbXyd.getXid());
                                YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("进场及消费");
                                TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(tbXyd.getXid());
                                WlgbDksljl wlgbDksljl = JcJXfLcConfig.jcJXfLc(tbXyd, villa, xydUrl, dingkey, employee2, ydAppkey, ydBd, employee1, dksljl4, tbYddXyd != null ? "是" : "否");
                                if (dksljl4 == null) {
                                    wlgbDksljlService.save(wlgbDksljl);
                                }
                            }
                        }
                    }
                }
            } else {
                WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid("进场及消费", tbXyd.getXid());
                //进场及消费
                if (dksljl != null) {
                    //删除容错机制
                    queryYdLcScRc(tbXyd, "切换场次进场及消费", dksljl.getSlid(), fdUserId, dingkey);
                    wlgbDksljlService.removeById(dksljl.getId());
                }
                Csry csry = null;
                if ("直营".equals(vxz)) {
                    csry = weiLianDaiBanService.queryCsRyByUserId(villa.getPid());
                }
                if ("托管加盟".equals(vxz)) {
                    csry = weiLianDaiBanService.queryCsRyJmByUserId(villa.getPid());
                }
                //判断店长是不是在范围内
                if (csry != null) {
                    WlgbDksljl dksljl1 = wlgbDksljlService.queryByBzAndXid("下单及跟单", tbXyd.getXid());
                    //下单及跟单
                    if (dksljl1 == null) {
                        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("下单及跟单");
                        //第一个参数改成房东ID
                        WlgbDksljl wlgbDksljl = XdJGdLcConfig.xdJGdLc(tbXyd, villa, employee1, dksljl1, dingkey, employee2, xydUrl, ydAppkey, ydBd);
                        if (wlgbDksljl != null) {
                            wlgbDksljlService.save(wlgbDksljl);
                        }
                    }
                }
            }
        }
    }


    /**
     * 修改宜搭流程容错机制
     *
     * @param tbXyd       协议单对象
     * @param name        操作名称
     * @param formDatamap 数据
     * @param formInstId  实例id
     * @param userid      操作人id
     * @param dingkey     钉钉key
     */
    public void queryYdLcXgRc(TbXyd tbXyd, String name, Map<String, Object> formDatamap, String formInstId, String userid, Dingkey dingkey) {
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            log.info("*****进入editXydTask异步请求*修改表单实例之获取token失败******{}", e);
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        for (int i = 0; i < 5; i++) {
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = xgLcSl(formDatamap, formInstId, "012412221639786136545", token, ydAppkey);
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                log.info("*****进入editXydTask异步请求*修改表单实例失败******{}", e);
            }
            if (!gatewayResult.getSuccess()) {
                if (i == 4) {
                    try {
                        DingQunSend.send((ding != null ? ding.getName() : tbXyd.getXfd()) + "正在" + name + "，修改流程出错了，订单编号：" + tbXyd.getXddbh() + "，错误原因：" + gatewayResult.getResult(), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e) {
                        log.info("*****进入editXydTask异步请求*修改流程2222322失败******{}", e);
                    }
                }
            } else {
                break;
            }
        }
    }

    /**
     * 判断时间是否在时间段内
     *
     * @param nowTime
     * @param beginTime
     * @param endTime
     * @return
     */
    public boolean belongCalendar(Date nowTime, Date beginTime, Date endTime) {
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);
        Calendar begin = Calendar.getInstance();
        begin.setTime(beginTime);
        Calendar end = Calendar.getInstance();
        end.setTime(endTime);
        if (date.after(begin) && date.before(end)) {
            return true;
        } else if (nowTime.compareTo(beginTime) == 0 || nowTime.compareTo(endTime) == 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 场次判断
     */
    private int cc(Date starTime, Date endTime) {
        //场次判断
        Date date = starTime;
        int cc = 0;
        int hours = date.getHours();
        if (hours == 10) {
            Date date1 = endTime;
            int hours1 = date1.getHours();
            if (hours1 == 17) {
                //白场
                cc = 1;
            } else if (hours1 == 8) {
                //全天1
                cc = 3;
            } else {
                //自定义
                cc = 5;
            }
        } else if (hours == 18) {
            Date date1 = endTime;
            int hours1 = date1.getHours();
            if (hours1 == 8) {
                //晚场
                cc = 2;
            } else if (hours1 == 17) {
                //全天2
                cc = 4;
            } else {
                //自定义
                cc = 5;
            }
        } else {
            //自定义
            cc = 5;
        }

        return cc;
    }

    /**
     * 删除流程实例
     *
     * @param slid     实例id
     * @param token    美团token
     * @param ydAppkey 宜搭配置
     * @return 返回结果
     */
    public GatewayResult scLcSl(String slid, String token, YdAppkey ydAppkey) {
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.scLcSl(token, ydAppkey, "012412221639786136545", slid);
        } catch (Exception e) {
            log.info("*****进入editXydTask异步请求*删除流程333333失败******{}", e);
        }
        return gatewayResult;
    }

    /**
     * 修改流程实例
     *
     * @param id     要更新的流程数据ID
     * @param map    表单内容
     * @param userid 用户id
     */
    public GatewayResult xgLcSl(Map<String, Object> map, String id, String userid, String token, YdAppkey ydAppkey) {
        return LcConfig.gxLcSl(userid, map, ydAppkey, id, token);
    }
}
