package com.wlgb.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/10 17:23
 */
@Data
public class SelectVo {
    private String total_discount;
    private String account_date;
    private Double fee;
    private String sign;
    private String is_discount;
    private String notify_type;
    private Double partner_fee;
    private String trade_status;
    private String total_discount_fee;
    private String sign_type;
    private String notify_time;
    private Double payee_fee;
    private String extra_common_param;
    private String card_type;
    private String buyer_user_id;
    private String out_trade_no;
    private Double total_amount;
    private String trade_no;
    private Double settlement_amount;
    private String paygate_no;
    private String buyer_logon_id;
    private String payer_fee;
    private String channel_recv_sn;
    private String channel_send_sn;
}
