package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbNotecgd;
import com.wlgb.mapper.WlgbNotecgdMapper;
import com.wlgb.service.WlgbNotecgdService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/16 8:47
 */
@Service
public class WlgbNotecgdServiceImpl implements WlgbNotecgdService {
    @Resource
    private WlgbNotecgdMapper wlgbNotecgdMapper;

    @Override
    public void save(WlgbNotecgd wlgbNotecgd) {
        wlgbNotecgd.setCreateTime(new Date());
        wlgbNotecgd.setId(IdConfig.uuId());
        wlgbNotecgdMapper.insert(wlgbNotecgd);
    }
}
