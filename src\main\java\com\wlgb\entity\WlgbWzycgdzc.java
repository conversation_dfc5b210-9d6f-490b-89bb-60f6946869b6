package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 物资一线固定资产
 * @Author: jeecg-boot
 * @Date:   2021-09-06
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_wzycgdzc")
public class WlgbWzycgdzc {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**城市*/
    private java.lang.String city;
	/**别墅名称*/
    private java.lang.String bsmc;
	/**别墅id*/
    private java.lang.String bsid;
	/**物品名称*/
    private java.lang.String wpmc;
	/**品牌*/
    private java.lang.String brand;
	/**采购时间*/
//	@Excel(name = "采购时间", width = 15, format = "yyyy-MM-dd")
//	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
//    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.lang.String cgtime;
	/**数量*/
    private java.lang.Double cgnum;
	/**单价*/
    private java.lang.Double dj;
    /**单位*/
    private java.lang.String dw;

	/**质保*/
    private java.lang.String zb;
	/**使用寿命*/
    private java.lang.String sysm;
	/**负责人*/
    private java.lang.String fzr;
	/**负责人id*/
    private java.lang.String fzrid;
	/**城市总经理*/
    private java.lang.String cszjl;
	/**城市总经理id*/
    private java.lang.String cszjlid;
	/**资产区域*/
    private java.lang.String zcqy;
	/**标配*/
    private java.lang.Integer bp;
	/**状态*/
    private java.lang.String zt;
	/**备注*/
    private java.lang.String bz;
	/**采购渠道*/
    private java.lang.String cgqd;
	/**备用数量*/
    private java.lang.Integer bysl;

    /**提交人姓名*/
    private java.lang.String tjrname;

    /**提交人id*/
    private java.lang.String tjrid;

    /**维修数量*/
    private java.lang.Integer wxnum;

    /**是否备用(0:否,1:是)*/
    private java.lang.Integer sfby;

    /**是否删除(0:否,1:是)*/
    private java.lang.Integer sfsc;

    /**部门*/
    private java.lang.String bm;

    /**区域*/
    private java.lang.String wzgsqy;

    private java.lang.String lsh;

    private java.lang.String fl;

    private java.lang.Integer wzsl;


//    private java.lang.Double zje;


}
