package com.wlgb.service.impl;

import com.wlgb.entity.WlgbCustomerLog;
import com.wlgb.mapper.WlgbCustomerLogMapper;
import com.wlgb.service.WlgbCustomerLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/18 16:32
 */
@Service
public class WlgbCustomerLogServiceImpl implements WlgbCustomerLogService {
    @Resource
    private WlgbCustomerLogMapper wlgbCustomerLogMapper;

    @Override
    public void save(WlgbCustomerLog wlgbCustomerLog) {
        wlgbCustomerLogMapper.insertSelective(wlgbCustomerLog);
    }
}
