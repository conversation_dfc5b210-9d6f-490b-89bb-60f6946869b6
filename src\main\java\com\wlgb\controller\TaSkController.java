package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.openapi.sdk.api.tuangou.TuangouReceiptGetConsumed;
import com.dianping.openapi.sdk.api.tuangou.TuangouReceiptPrepare;
import com.dianping.openapi.sdk.api.tuangou.entity.*;
import com.dianping.openapi.sdk.httpclient.DefaultOpenAPIClient;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.*;
import com.wlgb.service.*;
import com.wlgb.service2.*;
import com.wlgb.service3.DzxydService;
import com.wlgb.service3.WeiLianDaiBanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.wlgb.config.Tools.isEmpty;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月22日 19:56
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/task")
public class TaSkController {
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private WlgbJdDjbbdService wlgbJdDjbbdService;
    @Autowired
    private WlgbMtLogService wlgbMtLogService;
    @Autowired
    private WlgbMtTokenService wlgbMtTokenService;
    @Autowired
    private WlgbMtMdService wlgbMtMdService;
    @Autowired
    private WlgbMtJlService wlgbMtJlService;
    @Autowired
    private WlgbJdDjLsjlbService wlgbJdDjLsjlbService;
    @Autowired
    private WlgbDjsljlccService wlgbDjsljlccService;
    @Autowired
    private OssFileService ossFileService;
    @Autowired
    private WlgbHxyhDzjlService wlgbHxyhDzjlService;
    @Autowired
    private WlgbHxyhFqskService wlgbHxyhFqskService;
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private WlgbYlxdService wlgbYlxdService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WlgbXydLogService wlgbXydLogService;
    @Autowired
    private WlgbBdjlService wlgbBdjlService;
    @Autowired
    private WlgbDksljlService wlgbDksljlService;
    @Autowired
    private DzxydService dzxydService;
    @Autowired
    private JqrConfig jqrConfig;
    @Autowired
    private TbQrdService tbQrdService;
    @Autowired
    private TbKdjZkService tbKdjZkService;
    @Autowired
    private WlgbJdbOneService oneService;
    @Autowired
    private WlgbJdbTwoService twoService;
    @Autowired
    private WlgbNotecgdService wlgbNotecgdService;
    @Autowired
    private WlgbJdbService wlgbJdbService;
    @Autowired
    private TbDkService tbDkService;
    @Autowired
    private WlgbCustomerLogService wlgbCustomerLogService;
    @Autowired
    private CrmQbkhService crmQbkhService;
    @Autowired
    private CrmXgjlbService crmXgjlbService;
    @Autowired
    private TbKdjService tbKdjService;
    @Autowired
    private TbKdjFdCzjlService tbKdjFdCzjlService;
    @Autowired
    private TbKdjJzxCzjlService tbKdjJzxCzjlService;
    @Autowired
    private TbKdjZdjCzjlService tbKdjZdjCzjlService;
    @Autowired
    private WlgbKdjSqjlService wlgbKdjSqjlService;
    @Autowired
    private HrMszService hrMszService;
    @Autowired
    private HrGwCityService hrGwCityService;
    @Autowired
    private HrMsgjlService hrMsgjlService;
    @Autowired
    private WlgbOrderCyjlService wlgbOrderCyjlService;
    @Autowired
    private WlgbEatTcjlService wlgbEatTcjlService;
    @Autowired
    private WlgbEatCpjlService wlgbEatCpjlService;
    @Autowired
    private WlgbEatScjlService wlgbEatScjlService;
    @Autowired
    private TbYddXydService tbYddXydService;
    @Autowired
    private FwqDyYqService fwqDyYqService;
    @Autowired
    private WlgbYddYjWkJlService wlgbYddYjWkJlService;
    @Autowired
    private FwqDyAccountService fwqDyAccountService;
    @Autowired
    private FwqDyTokenService fwqDyTokenService;
    @Autowired
    private WlgbYddMdxfjlService wlgbYddMdxfjlService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;
    @Value("${hanshujisuan.douyinurl}")
    private String douyinurl;


    /**
     * 定金表单提交新增至数据库
     */
    @RequestMapping(value = "/djbBdSave")
    public void djbBdSave(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        JSONObject jsonObject = JSON.parseObject(datas);
        WlgbJdDjbbd wlgbJdDjbbd = jsonObject.toJavaObject(WlgbJdDjbbd.class);
        Double je = wlgbJdDjbbd.getJe();
        if (wlgbJdDjbbd.getDjwybs() != null && !"".equals(wlgbJdDjbbd.getDjwybs())) {
            Integer count = wlgbJdDjbbdService.queryCountByWybs(wlgbJdDjbbd.getDjwybs());
            if (count > 0) {
                return;
            }
        }
        //删除定金临时表有关此客户电话的记录
        List<WlgbJdDjLsjlb> listjl = wlgbJdDjLsjlbService.queryListByKhDhAndSfSc(wlgbJdDjbbd.getKhdh(), 0);
        listjl.forEach(l -> l.setSfsc(1));
        wlgbJdDjLsjlbService.updateBatchById(listjl);
        if (Optional.ofNullable(wlgbJdDjbbd.getEwm()).isPresent()) {
            JSONArray jsonArray = jsonObject.getJSONArray("ewm");
            List<String> list = new ArrayList<>();
            jsonArray.forEach(l -> {
                JSONObject jsonObject1 = JSONObject.parseObject(l.toString());
                String url = jsonObject1.getString("url");
                list.add(url);
            });
            wlgbJdDjbbd.setEwm(list.get(0));
        }
        String yqrName = jsonObject.getString("yqrName");
        if (yqrName != null && !"".equals(yqrName)) {
            wlgbJdDjbbd.setYqr(yqrName);
        }
        wlgbJdDjbbd.setId(IdConfig.uuId());
        wlgbJdDjbbd.setFormInstId(formInstId);
        wlgbJdDjbbdService.save(wlgbJdDjbbd);
    }

    /**
     * 定金表单修改异步
     */
    @RequestMapping(value = "/djbBdUpdateTaSk")
    public void djbBdUpdate(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (!Optional.ofNullable(datas).isPresent()) {
            return;
        }
        String formInstId = request.getParameter("formInstId");
        if (!Optional.ofNullable(formInstId).isPresent()) {
            return;
        }
        String userid = request.getParameter("userid");
        if (userid == null || "".equals(userid)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(datas);

        WlgbJdDjbbd wlgbDjbbd = jsonObject.toJavaObject(WlgbJdDjbbd.class);
        if (!Optional.ofNullable(wlgbDjbbd).isPresent()) {
            return;
        }
        WlgbJdDjbbd wlgbJdDjbbd = wlgbJdDjbbdService.queryByLshAndFormId(wlgbDjbbd.getLsh(), formInstId);
        if (!Optional.ofNullable(wlgbJdDjbbd).isPresent()) {
            return;
        }

        wlgbDjbbd.setId(wlgbJdDjbbd.getId());
        wlgbDjbbd.setEwm(null);

        if (wlgbDjbbd.getSfsc() == 1) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            String text = "您删除了一条的定金收款单！";

            if (userid.equals(wlgbJdDjbbd.getFqrId())) {
                text = "您发起的定金收款单已被删除";
            }
            text += "\n\n客户电话：" + wlgbJdDjbbd.getKhdh();
            text += "\n定金类型:" + (wlgbJdDjbbd.getDjlx() == 1 ? "线下" : "线上");
            text += "\n定金金额：" + wlgbJdDjbbd.getJe();
            if (!userid.equals(wlgbJdDjbbd.getFqrId())) {
                DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
                text += "\n删除人:" + dingdingEmployee.getName();
            }
            text += "\n\n删除时间：" + sdf.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, wlgbJdDjbbd.getFqrId(), text);
                if (!userid.equals(wlgbJdDjbbd.getFqrId())) {
                    DingDBConfig.sendGztzText(dingkey, userid, text);
                }
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
        String yqrName = jsonObject.getString("yqrName");
        if (yqrName != null && !"".equals(yqrName)) {
            wlgbDjbbd.setYqr(yqrName);
        }
        wlgbJdDjbbdService.updateById(wlgbDjbbd);
    }

    /**
     * 验券异步(定金和补交定金，不是尾款定金)
     */
    @RequestMapping(value = "mtYqTaSk")
    public Result mtYqTaSk(HttpServletRequest request) throws ApiException {
        String datas = request.getParameter("datas");
        String userId = request.getParameter("userId");
        Date date = new Date();
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String md = jsonObject.getString("mdid");
        String qm = jsonObject.getString("smqm");
        String crmbh = jsonObject.getString("crmbh");
        String khdh = jsonObject.getString("khdh");
        String lsh = jsonObject.getString("lsh");
        Integer sfbjdjwk = jsonObject.getInteger("sfbjdjwk");
        Integer sfbjdj = jsonObject.getInteger("sfbjdj");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);
        //数据库中所有的token
        List<WlgbMtToken> tokenlist = wlgbMtTokenService.queryAllList();
        double b = qm.length() / 10.00;
        int a = (int) b;
        int c = b > a ? a + 1 : a;
        int star = 0;
        int end = 10;
        List<String> list = new ArrayList<>();
        for (int i = 0; i < c; i++) {
            list.add(qm.substring(star, end));
            star += 10;
            end += 10;
        }
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        lsh = lsh != null && !"".equals(lsh) ? lsh : df1.format(new Date());

        WlgbMtMd wlgbMtMd = wlgbMtMdService.queryByShopUuId(md);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = DingToken.token(dingkey);

        //判断有没有券验券成功
        boolean b2 = false;
        for (String l : list) {
            WlgbMtJl wlgbMtJl = new WlgbMtJl();
            wlgbMtJl.setId(IdConfig.uuId());
            wlgbMtJl.setYqr(ding != null ? ding.getUserid() : null);
            wlgbMtJl.setYqrxm(ding != null ? ding.getName() : null);
            wlgbMtJl.setMtqh(l);
            wlgbMtJl.setYqsj(new Date());
            boolean bb = true;
            double sum = 0.0;
            String cgqm = "";
            String dm = "";
            for (WlgbMtToken f : tokenlist) {
                //跳过已经失效的token
                if ("1".equals(f.getSfsc())) {
                    break;
                }
                if (!isEmpty(f.getAppkey())) {
                    //session适用店铺查询接口
                    TuangouReceiptPrepareResponse tp = jyQm(f.getAppkey(), f.getAppsecret(), f.getToken(), l, md, null);
                    try {
                        if (tp.getCode() == 200) {
                            TuangouReceiptPrepareResponseEntity data = tp.getData();
                            if (data != null) {
                                if (data.getCount() != null) {
                                    //进行验券
                                    String id = IdConfig.uuId();
                                    TuangouReceiptConsumeResponse tt = MtConfig.getYQ(f.getAppkey(), f.getAppsecret(), f.getToken(), id, l, data.getCount() != null ? data.getCount() : 0, md, f.getZh(), f.getRemake());
                                    try {
                                        if (tt.getCode() == 200) {
                                            dm = f.getZh();
                                            if (dm != null && !"".equals(dm)) {
                                                if (dm.length() > 0) {
                                                    dm = dm.substring(dm.length() - 3);
                                                }
                                            }
                                            if (tt.getData().size() > 0) {
                                                List<TuangouReceiptPreparePaymentDetail> paymentDetail = tt.getData().get(0).getPayment_detail();
                                                double sum1s = 0;
                                                for (TuangouReceiptPreparePaymentDetail ttt : paymentDetail) {
                                                    if (ttt.getAmount_type() != 8 && ttt.getAmount_type() != 16 && ttt.getAmount_type() != 17 && ttt.getAmount_type() != 18 && ttt.getAmount_type() != 22) {
                                                        sum1s += ttt.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                                                    }
                                                }
                                                sum = sum1s;
                                            }
                                            double sum2 = 0.0;
                                            for (int i = 0; i < tt.getData().size(); i++) {
                                                b2 = true;
                                                String receiptCode = tt.getData().get(i).getReceipt_code();
                                                WlgbMtLog wlgbMtLog = MtConfig.saveMtLog(lsh, null, ding, receiptCode, tt.getData().get(i), wlgbMtMd, crmbh, khdh);
                                                wlgbMtLog.setId(IdConfig.uuId());
                                                wlgbMtLogService.save(wlgbMtLog);
                                                WlgbMtJl wlgbMtJl1 = new WlgbMtJl();
                                                wlgbMtJl1.setId(IdConfig.uuId());
                                                wlgbMtJl1.setYqr(ding != null ? ding.getUserid() : null);
                                                wlgbMtJl1.setYqrxm(ding != null ? ding.getName() : null);
                                                wlgbMtJl1.setMtqh(tt.getData().get(i).getReceipt_code());
                                                wlgbMtJl1.setYqsj(new Date());
                                                wlgbMtJl1.setYqjg("验券成功");
                                                wlgbMtJl1.setDkje(tt.getData().get(i).getDeal_price());
                                                wlgbMtJl1.setMtyqjg(tt.getMsg());
                                                wlgbMtJl1.setDdbh(lsh);
                                                wlgbMtJlService.save(wlgbMtJl1);
                                                JSONObject jsonObject1 = new JSONObject();
                                                jsonObject1.put("lsh", lsh);
                                                double sum1 = sum / (data.getCount() != null ? data.getCount() : 0);
                                                BigDecimal bigDecimal = new BigDecimal(sum1);
                                                jsonObject1.put("je", bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                                                sum2 += bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                                                if (i == tt.getData().size() - 1) {
                                                    if (sum2 < sum) {
                                                        double ce = sum - sum2;
                                                        double bce = sum1 + ce;
                                                        BigDecimal bigDecimal2 = new BigDecimal(bce);
                                                        jsonObject1.put("je", bigDecimal2.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                                                    }
                                                }
                                                jsonObject1.put("fqr", userId);
                                                jsonObject1.put("fqrId", userId);
                                                jsonObject1.put("fqrName", ding != null ? ding.getName() : null);
                                                jsonObject1.put("fqsj", date);
                                                jsonObject1.put("djlx", 2);
                                                jsonObject1.put("sspt", 1);
                                                jsonObject1.put("mtmd", md);
                                                jsonObject1.put("hqqm", qm);
                                                jsonObject1.put("yqsfwc", 1);
                                                jsonObject1.put("yqjg", 1);
                                                jsonObject1.put("yqqm", receiptCode);
                                                jsonObject1.put("yqsj", new Date());
                                                jsonObject1.put("dm", dm);
                                                jsonObject1.put("qmsj", 1);
                                                jsonObject1.put("yqr", userId);
                                                jsonObject1.put("yqrId", userId);
                                                jsonObject1.put("yqrName", ding != null ? ding.getName() : null);
                                                jsonObject1.put("sfsc", 0);
                                                jsonObject1.put("sftk", 0);
                                                jsonObject1.put("sfsddj", 0);
                                                jsonObject1.put("sfscdj", 0);
                                                jsonObject1.put("khdh", khdh);
                                                jsonObject1.put("crmbh", crmbh);
                                                jsonObject1.put("sfbjdj", sfbjdj != null ? sfbjdj : 0);
                                                jsonObject1.put("sfxd", 0);
                                                jsonObject1.put("ytje", 0);
                                                if (sfbjdjwk != null) {
                                                    jsonObject1.put("sfbjdjwk", 1);
                                                    jsonObject1.put("sfxyld", 1);
                                                } else {
                                                    jsonObject1.put("sfbjdjwk", 0);
                                                    jsonObject1.put("sfxyld", 0);
                                                }
                                                //定金表唯一标识
                                                jsonObject1.put("djwybs", IdConfig.uuId());
                                                GatewayResult gatewayResult = null;
                                                for (int j = 0; j < 5; j++) {
                                                    gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObject1.toJSONString(), userId, "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1");
                                                    if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                                                        if (j == 4) {
                                                            try {
                                                                String context1 = "验券成功同步表单出错了，券码：" + receiptCode + "，错误原因：" + gatewayResult.toString();
                                                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                                            } catch (ApiException e) {
                                                                e.printStackTrace();
                                                            }
                                                        }
                                                    } else {
                                                        break;
                                                    }
                                                }
                                                bb = false;
                                                if (!"".equals(cgqm)) {
                                                    cgqm += ",";
                                                }
                                                cgqm += receiptCode;
                                            }
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            if (bb) {
                wlgbMtJl.setDdbh(lsh);
                wlgbMtJl.setYqjg("不存在券码或已经验过了");
                wlgbMtJlService.save(wlgbMtJl);
                String text = "您提交的新美大验券，验券失败了！";
                text += "\n\n券码：" + l;
                text += "\n\n送达时间：" + df2.format(new Date());
                try {
                    DingDBConfig.sendGztzText(dingkey, userId, text);
                } catch (ApiException e) {
                    e.printStackTrace();
                }
            } else {
                BigDecimal bd = new BigDecimal(sum);
                sum = bd.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                String text = "您提交的新美大验券，验券完成了！";
                text += "\n\n券码：" + cgqm;
                text += "\n金额：" + sum;
                text += "\n代码：" + dm;
                text += "\n\n送达时间：" + df2.format(date);

                try {
                    DingDBConfig.sendGztzText(dingkey, userId, text);
                } catch (ApiException e) {
                    e.printStackTrace();
                }
            }
        }
        if (b2) {
            if (sfbjdjwk != null) {
                List<WlgbJdDjbbd> list1 = wlgbJdDjbbdService.queryByLshAndSfSc(lsh);
                String finalToken = token;
                list1.forEach(l -> {
                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("sfbjdjwk", 1);
                    jsonObject1.put("sfxyld", 1);
                    for (int j = 0; j < 5; j++) {
                        GatewayResult gatewayResult = null;
                        try {
                            gatewayResult = DingBdLcConfig.xgBdSl(finalToken, ydAppkey, l.getFqrId(), l.getFormInstId(), jsonObject1.toJSONString());
                        } catch (Exception e) {
                            gatewayResult = new GatewayResult();
                            e.printStackTrace();
                        }
                        if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                            if (j == 4) {
                                try {
                                    String context1 = "验券成功，是补交同步出错了，券码：" + l.getLsh() + "，错误原因：" + gatewayResult.toString();
                                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                } catch (ApiException e) {
                                    e.printStackTrace();
                                }
                            }
                        } else {
                            break;
                        }
                    }
                });
            }
        }
        return Result.OK();
    }

    /**
     * 验券异步---尾款补交定金（直接同步协议单）
     */
    @RequestMapping(value = "mtYqBjDjTaSk")
    public Result mtYqBjDjTaSk(HttpServletRequest request) throws Exception {
        String datas = request.getParameter("datas");
        log.info("**********补交定金验券参数********{}", datas);
        String userId = request.getParameter("userId");
        Date date = new Date();
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String md = jsonObject.getString("mdid");
        String qm = jsonObject.getString("smqm");
        String crmbh = jsonObject.getString("crmbh");
        String khdh = jsonObject.getString("xkhdh");
        String lsh = jsonObject.getString("xddbh");
        //这个slid是协议单的id
        String slid = jsonObject.getString("slid");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);
        if (ding == null) {
            return Result.OK("当前用户不存在");
        }
        double b = qm.length() / 10.00;
        int a = (int) b;
        int c = b > a ? a + 1 : a;
        int star = 0;
        int end = 10;
        List<String> list = new ArrayList<>();
        for (int i = 0; i < c; i++) {
            list.add(qm.substring(star, end));
            star += 10;
            end += 10;
        }
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        lsh = lsh != null && !"".equals(lsh) ? lsh : df1.format(new Date());
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        //获取钉钉key
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        //获取美团门店的详情
        WlgbMtMd wlgbMtMd = wlgbMtMdService.queryByShopUuId(md);

        //数据库中所有的有效的token
        Map<String, Object> map = new HashMap<>();
        map.put("zh", wlgbMtMd.getZh());
        List<WlgbMtToken> tokenlist = wlgbMtTokenService.queryAllListById(map);
        if (tokenlist.isEmpty()) {
            DingDBConfig.sendGztzText(dingkey, "15349026426046931", "获取美团token异常");
        }

        String token = DingToken.token(dingkey);
        //账号代码
        String dm = "";
        //券码的价格（补交定金的价格）
        double sum = 0.0;
        //券码总数
        int quansum = 0;
        //券码的数量
        int quannum = 0;
        //如果上传宜搭失败就不修改协议单
        boolean b2 = false;
        //定金的总金额
        double je = 0.0;
        //保存所有的券码信息
        List<JSONObject> list1 = new ArrayList<>();
        //保存所有的券码信息，用于批量上传到宜搭
        List<String> list2 = new ArrayList<>();
        List<TuangouReceiptConsumeResponse> ttlist = new ArrayList<>();
        for (String l : list) {
            if (!isEmpty(tokenlist.get(0).getAppkey())) {
                //输码验券校验接口：根据团购券码，查询券码对应的dealid下，该用户可使用的券数据量
                TuangouReceiptPrepareResponse tp = jyQm(tokenlist.get(0).getAppkey(), tokenlist.get(0).getAppsecret(), tokenlist.get(0).getToken(), l, md, null);
                if (tp != null && tp.getCode() == 200) {
                    TuangouReceiptPrepareResponseEntity data = tp.getData();
                    if (data != null) {
                        if (data.getCount() > 0) {
                            String id = IdConfig.uuId();
                            //只有校验完之后，就可以批量验券了
                            TuangouReceiptConsumeResponse tt = MtConfig.getYQ(tokenlist.get(0).getAppkey(), tokenlist.get(0).getAppsecret(), tokenlist.get(0).getToken(), id, l, data.getCount(), md, tokenlist.get(0).getZh(), tokenlist.get(0).getRemake());
                            if (tt != null && tt.getCode() == 200) {
                                if (!tt.getData().isEmpty()) {
                                    ttlist.add(tt);
                                    dm = tokenlist.get(0).getZh();
                                    dm = dm.substring(dm.length() - 3);
                                    quannum += data.getCount();
                                }
                            }
                        }
                    }
                }
            }
        }

        if (!ttlist.isEmpty()) {
            for (TuangouReceiptConsumeResponse tt : ttlist) {
                List<TuangouReceiptPreparePaymentDetail> paymentDetail = tt.getData().get(0).getPayment_detail();
                double sum1s = 0;
                //获取 去除商家优惠之后的定金的价格
                for (TuangouReceiptPreparePaymentDetail trppd : paymentDetail) {
                    if (trppd.getAmount_type() != 8 && trppd.getAmount_type() != 16 && trppd.getAmount_type() != 17 && trppd.getAmount_type() != 18 && trppd.getAmount_type() != 22 && trppd.getAmount_type() != 24 && trppd.getAmount_type() != 28) {
                        sum1s += trppd.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    }
                }
                sum = sum1s;
                double sum2 = 0.0;
                for (int i = 0; i < tt.getData().size(); i++) {
                    String receiptCode = tt.getData().get(i).getReceipt_code();
                    quansum++;
                    //获取美团记录表
                    WlgbMtLog wlgbMtLog = MtConfig.saveMtLog(lsh, null, ding, receiptCode, tt.getData().get(i), wlgbMtMd, crmbh, khdh);
                    //先判断这条记录是否已经在数据库内。
                    List<WlgbMtLog> mtLoglist = wlgbMtLogService.queryByWlgbMtLog(wlgbMtLog);
                    if (mtLoglist.isEmpty()) {
                        wlgbMtLog.setId(IdConfig.uuId());
                        wlgbMtLogService.save(wlgbMtLog);
                    } else {
                        log.info("***************该券码在数据库中已存在" + receiptCode);
                    }
                    WlgbMtJl wlgbMtJl1 = new WlgbMtJl();
                    wlgbMtJl1.setYqr(ding.getUserid());
                    wlgbMtJl1.setYqrxm(ding.getName());
                    wlgbMtJl1.setMtqh(tt.getData().get(i).getReceipt_code());
                    wlgbMtJl1.setYqsj(new Date());
                    wlgbMtJl1.setYqjg("验券成功");
                    wlgbMtJl1.setDkje(tt.getData().get(i).getDeal_price());
                    wlgbMtJl1.setMtyqjg(tt.getMsg());
                    wlgbMtJl1.setDdbh(lsh);
                    //先判断这条记录是否已经在数据库内。
                    List<WlgbMtJl> mtjllist = wlgbMtJlService.queryListByWlgbMtJl(wlgbMtJl1);
                    if (mtjllist.isEmpty()) {
                        wlgbMtJl1.setId(IdConfig.uuId());
                        wlgbMtJlService.save(wlgbMtJl1);
                    } else {
                        log.info("***************该券码在数据库中已存在mtjllist" + receiptCode);
                    }
                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("lsh", lsh);
                    //平均每个券码的价格
                    double sum1 = sum / (quannum);
                    BigDecimal bigDecimal = new BigDecimal(sum1);
                    jsonObject1.put("je", bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                    //所有券码的总价
                    sum2 += bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    if (i == tt.getData().size() - 1) {
                        //当总价小于单张券码时
                        if (sum2 < sum) {
                            double ce = sum - sum2;
                            double bce = sum1 + ce;
                            BigDecimal bigDecimal2 = new BigDecimal(bce);
                            jsonObject1.put("je", bigDecimal2.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                        }
                    }
                    jsonObject1.put("fqr", userId);
                    jsonObject1.put("fqrId", userId);
                    jsonObject1.put("fqrName", ding.getName());
                    jsonObject1.put("fqsj", date);
                    jsonObject1.put("djlx", 2);
                    jsonObject1.put("sspt", 1);
                    jsonObject1.put("mtmd", md);
                    jsonObject1.put("hqqm", qm);
                    jsonObject1.put("yqsfwc", 1);
                    jsonObject1.put("yqjg", 1);
                    jsonObject1.put("yqqm", receiptCode);
                    jsonObject1.put("yqsj", new Date());
                    jsonObject1.put("dm", dm);
                    jsonObject1.put("qmsj", 1);
                    jsonObject1.put("yqr", userId);
                    jsonObject1.put("yqrId", userId);
                    jsonObject1.put("yqrName", ding.getName());
                    jsonObject1.put("sfsc", 0);
                    jsonObject1.put("sftk", 0);
                    jsonObject1.put("sfsddj", 0);
                    jsonObject1.put("sfscdj", 0);
                    jsonObject1.put("khdh", khdh);
                    jsonObject1.put("crmbh", crmbh);
                    jsonObject1.put("sfbjdj", 1);
                    jsonObject1.put("sfxd", 1);
                    jsonObject1.put("ddbh", lsh);
                    jsonObject1.put("xdsj", new Date());
                    jsonObject1.put("ytje", 0);
                    jsonObject1.put("sfbjdjwk", 0);
                    jsonObject1.put("sfxyld", 0);
                    JSONObject jsonObject2 = new JSONObject();
                    jsonObject2.put("xsbjje", jsonObject1.getDouble("je"));
                    jsonObject2.put("yqrq", new Date());
                    jsonObject2.put("dm", dm);
                    jsonObject2.put("qm", receiptCode);
                    jsonObject2.put("yqr", ding.getName());
                    list1.add(jsonObject2);
                    //定金表唯一标识改成订单编号和券码、验券人id结合
                    jsonObject1.put("djwybs", lsh + "_" + receiptCode + "_" + userId);
                    list2.add(jsonObject1.toJSONString());
                }
            }
        } else {
            log.info("*********没用的券码********" + userId + "_" + qm);
        }

        if (!list2.isEmpty()) {
            //已经把所有数据都保存到数据库中了，接下来是调用宜搭接口先批量上传到宜搭定金表表单，最后再调用接口去修改协议单
            GatewayResult gatewayResult = DingBdLcConfig.plxzBdSl(token, ydAppkey, list2, userId, "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1");
            boolean gs = gatewayResult.getSuccess();
            int qmsum = Integer.parseInt(gatewayResult.getResult());
            boolean qmsl = (qmsum == quansum);
            if (!gs) {
                String context1 = "订单编号:" + lsh + ",验券人:" + ding.getName() + "的券码已经保存到数据库中，但是上传宜搭失败：错误原因：" + gatewayResult.toString();
                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
            } else {
                //上传宜搭成功，但是有失败的几条
                if (!qmsl) {
                    String context1 = "订单编号:" + lsh + ",验券人:" + ding.getName() + "的券码已经保存到数据库中，但是上传宜搭有" + (quansum - qmsum) + "张失败：错误原因：" + gatewayResult.toString();
                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                } else {
                    BigDecimal bd = new BigDecimal(sum);
                    sum = bd.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    je += sum;
                    String text = "您提交的补交定金美团验券，验券完成了！";
                    text += "\n\n券码：" + qm;
                    text += "\n金额：" + sum;
                    text += "\n代码：" + dm;
                    text += "\n系统稍后将自动修改协议单，请注意工作通知";
                    text += "\n\n送达时间：" + df2.format(date);
                    try {
                        DingDBConfig.sendGztzText(dingkey, userId, text);
                    } catch (ApiException e) {
                        System.out.println("发给店长的工作通知出错了");
                    }
                    //为true就会去修改协议单
                    b2 = true;
                }
            }
        }

        //批量修改协议单
        if (b2) {
            TbXyd tbXyd = tbXydService.queryByDdBh(lsh);
            if (tbXyd != null) {
                JSONObject jsonObject1 = new JSONObject();
                double xbjdj = tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0;
                //数据库中如果补交金额是0，那就直接将宜搭改成线上补交
                if (xbjdj == 0) {
                    jsonObject1.put("sfbj", "是");
                    jsonObject1.put("xcdzjbj", je);
                    jsonObject1.put("xbjdj", je);
                    jsonObject1.put("xzzfybj", 0.0);
                    jsonObject1.put("xbjdjlx", "2");
                    jsonObject1.put("xxsbj", je);
                    jsonObject1.put("xsbjzbd", list1);
                    //修改下单表单的实例
                    GatewayResult gatewayResult = xgBdSl(jsonObject1, slid, userId);
                    Boolean b1 = gatewayResult.getSuccess();
                    if (!b1) {
                        //再次重试，如果三次都失败就播报
                        try {
                            gatewayResult = xgBdSl(jsonObject1, slid, userId);
                            b1 = gatewayResult.getSuccess();
                            if (!b1) {
                                gatewayResult = xgBdSl(jsonObject1, slid, userId);
                                b1 = gatewayResult.getSuccess();
                                if (!b1) {
                                    String text = "店长提交的补交定金美团验券，验券完成但修改协议单失败了！";
                                    text += "\n\n券码：" + qm;
                                    text += "\n系统稍后将自动修改协议单，请注意工作通知";
                                    text += "\n\n送达时间：" + df2.format(date);
                                    text += "\n\n" + gatewayResult.getErrorMsg();
                                    text += "\n\n订单编号：" + lsh;
                                    DingDBConfig.sendGztzText(dingkey, "15349026426046931", text);
                                }
                            }
                        } catch (ApiException e) {
                            log.info("****发送工作通知失败****");
                        }
                    }
                    System.out.println("修改订单：" + gatewayResult);
                } else {
                    JSONObject data = null;
                    for (int i = 0; i < 5; i++) {
                        //获取宜搭协议单表单数据
                        String s = YdConfig.hqBdSj(ydAppkey.getAppkey(), ydAppkey.getToken(), slid);
                        if (!"error".equals(s)) {
                            JSONObject jsonObject5 = JSONObject.parseObject(s);
                            if (jsonObject5.getBoolean("success")) {
                                JSONObject result = jsonObject5.getJSONObject("result");
                                if (result != null && result.size() > 0) {
                                    data = result.getJSONObject("formData");
                                    break;
                                }
                            }
                        }
                        if (i == 4) {
                            try {
                                String context1 = "补交定金验券查询订单失败，原因：" + s;
                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                            } catch (ApiException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    if (data != null) {
                        if ("2".equals(tbXyd.getXbjdjlx())) {
                            //线上补交定金子表单
                            JSONArray xsbjzbd = data.getJSONArray("xsbjzbd");
                            jsonObject1.put("sfbj", "是");
                            jsonObject1.put("xbjdjlx", "2");

                            double je1 = je;
                            if (xsbjzbd != null && xsbjzbd.size() > 0) {
                                for (Object o : xsbjzbd) {
                                    JSONObject j = (JSONObject) o;
                                    Double xsbjje = j.getDouble("xsbjje") != null ? j.getDouble("xsbjje") : 0;
                                    je += xsbjje;
                                    if (xsbjje > 0) {
                                        list1.add(j);
                                    }
                                }
                            }
                            jsonObject1.put("xcdzjbj", je1 + (tbXyd.getXcdzjbj() != null ? tbXyd.getXcdzjbj() : 0.0));
                            jsonObject1.put("xbjdj", je1 + (tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0));
                            jsonObject1.put("xxsbj", je);
                            jsonObject1.put("xsbjzbd", list1);
                        } else if ("1".equals(tbXyd.getXbjdjlx())) {

                            jsonObject1.put("sfbj", "是");
                            jsonObject1.put("xbjdjlx", "e9d99d68afc54a25bdf08c8cb9dd767e");

                            jsonObject1.put("xcdzjbj", je + (tbXyd.getXcdzjbj() != null ? tbXyd.getXcdzjbj() : 0.0));
                            jsonObject1.put("xbjdj", je + (tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0));
                            jsonObject1.put("xxsbj", je);
                            jsonObject1.put("xsbjzbd", list1);
                        } else if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(tbXyd.getXbjdjlx())) {
                            jsonObject1.put("sfbj", "是");
                            jsonObject1.put("xbjdjlx", "e9d99d68afc54a25bdf08c8cb9dd767e");

                            double je1 = je;
                            JSONArray xsbjzbd = data.getJSONArray("xsbjzbd");
                            if (xsbjzbd != null && xsbjzbd.size() > 0) {
                                for (Object o : xsbjzbd) {
                                    JSONObject j = (JSONObject) o;
                                    double xsbjje = j.getDouble("xsbjje") != null ? j.getDouble("xsbjje") : 0;
                                    je += xsbjje;
                                    if (xsbjje > 0) {
                                        list1.add(j);
                                    }
                                }
                            }
                            jsonObject1.put("xcdzjbj", je1 + (tbXyd.getXcdzjbj() != null ? tbXyd.getXcdzjbj() : 0.0));
                            jsonObject1.put("xbjdj", je1 + (tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0));
                            jsonObject1.put("xxsbj", je);
                            jsonObject1.put("xsbjzbd", list1);
                        }
                        for (int i = 0; i < 5; i++) {
                            System.out.println("这是第" + i + "次修改了");
                            GatewayResult gatewayResult = null;
                            try {
                                //修改表单实例的参数
                                gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userId, slid, jsonObject1.toJSONString());
                            } catch (Exception e) {
                                gatewayResult = new GatewayResult();
                                e.printStackTrace();
                            }
                            if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                                String context1 = "补交定金同步协议单宜搭错误,第" + i;
                                context1 += "\n\n错误信息:" + gatewayResult.toString();
                                context1 += "\n送达时间：" + df2.format(new Date());
                                try {
                                    DingDBConfig.sendGztzText(dingkey, "15349026426046931", context1);
                                } catch (ApiException e) {
                                    e.printStackTrace();
                                }
                            } else {
                                break;
                            }
                        }
                    }
                }
            }
        }
        return Result.OK();
    }

    /**
     * //TODO（未做完） 防止网络异常、函数超时等情况无法同步券码(验券成功但是上传宜搭失败)到宜搭（实例新增成功后会通过定时任务同步到金蝶）和数据库
     *
     * @param request
     * @throws Exception
     */
    @RequestMapping(value = "mtYqBjDj2")
    public void mtYqBjDj2(HttpServletRequest request) throws Exception {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String md = jsonObject.getString("mdid");
        String qm = jsonObject.getString("smqm").replaceAll(",", "").trim();
        //数据库中所有的有效的token
        List<WlgbMtToken> tokenlist = wlgbMtTokenService.queryAllListById();
        double b = qm.length() / 10.00;
        int a = (int) b;
        int c = b > a ? a + 1 : a;
        int star = 0;
        int end = 10;
        //券码数组
        List<String> list = new ArrayList<>();
        for (int i = 0; i < c; i++) {
            list.add(qm.substring(star, end));
            star += 10;
            end += 10;
        }
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        //遍历循环所有券码
        for (String l : list) {
            //循环所有美团token
            for (WlgbMtToken f : tokenlist) {
                TuangouReceiptGetConsumedReponse t = ygdqm(f.getAppkey(), f.getAppsecret(), f.getToken(), l, md);
                TuangouReceiptGetConsumedReponseEntity tdata = t.getData();
                log.info("***********已经验过券码的详情**************{}", t.getMsg());
                log.info("***********已经验过券码的详情**************{}", tdata);
            }
        }
    }

    @RequestMapping(value = "mtBc")
    public Result mtBc(HttpServletRequest request) {
        String lsh = request.getParameter("lsh");
        String slid = "FINST-SNB667B1Y84KWQC4B8EGOCPL3SIN3JHWS9TULXG7";
        TbXyd tbXyd = tbXydService.queryByDdBh(lsh);
        List<JSONObject> list1 = new ArrayList<>();
        if (tbXyd != null) {
            AtomicReference<String> userId = new AtomicReference<>("");
            YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            String token = null;
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            WlgbMtLog wlgbMtLog = new WlgbMtLog();
            wlgbMtLog.setDdbh(lsh);
            List<WlgbMtLog> list = wlgbMtLogService.queryListByWlgbMtLog(wlgbMtLog);
            String finalToken = token;
            list.forEach(l -> {
                DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(l.getYqrid());
                userId.set(ding.getUserid());
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("lsh", lsh);
                jsonObject1.put("je", l.getSpsmj());
                jsonObject1.put("fqr", ding.getUserid());
                jsonObject1.put("fqrId", ding.getUserid());
                jsonObject1.put("fqrName", ding != null ? ding.getName() : null);
                jsonObject1.put("fqsj", l.getCreateTime());
                jsonObject1.put("djlx", 2);
                jsonObject1.put("sspt", 1);
                jsonObject1.put("mtmd", "3b4c0acbb13a22ce3a5f853b279764f9");
                jsonObject1.put("hqqm", l.getQh());
                jsonObject1.put("yqsfwc", 1);
                jsonObject1.put("yqjg", 1);
                jsonObject1.put("yqqm", l.getQh());
                jsonObject1.put("yqsj", new Date());
                jsonObject1.put("dm", "688");
                jsonObject1.put("qmsj", 1);
                jsonObject1.put("yqr", ding.getUserid());
                jsonObject1.put("yqrId", ding.getUserid());
                jsonObject1.put("yqrName", ding != null ? ding.getName() : null);
                jsonObject1.put("sfsc", 0);
                jsonObject1.put("sftk", 0);
                jsonObject1.put("sfsddj", 0);
                jsonObject1.put("sfscdj", 0);
                jsonObject1.put("khdh", l.getKhdh());
                jsonObject1.put("crmbh", l.getCrmbh());
                jsonObject1.put("sfbjdj", 1);
                jsonObject1.put("sfxd", 1);
                jsonObject1.put("ddbh", lsh);
                jsonObject1.put("xdsj", new Date());
                jsonObject1.put("ytje", 0);
                jsonObject1.put("sfbjdjwk", 0);
                jsonObject1.put("sfxyld", 0);

                JSONObject jsonObject2 = new JSONObject();
                jsonObject2.put("xsbjje", jsonObject1.getDouble("je"));
                jsonObject2.put("yqrq", new Date());
                jsonObject2.put("dm", "688");
                jsonObject2.put("qm", l.getQh());
                jsonObject2.put("yqr", ding != null ? ding.getName() : null);
                list1.add(jsonObject2);

                //定金表唯一标识
                jsonObject1.put("djwybs", IdConfig.uuId());
                GatewayResult gatewayResult = null;
                for (int j = 0; j < 5; j++) {
                    try {
                        gatewayResult = DingBdLcConfig.xzBdSl(finalToken, ydAppkey, jsonObject1.toJSONString(), ding.getUserid(), "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                        if (j == 4) {
                            try {
                                String context1 = "验券成功同步表单出错了，券码：" + wlgbMtLog.getQh() + "，错误原因：" + gatewayResult.toString();
                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                            } catch (ApiException e) {
                                e.printStackTrace();
                            }
                        }
                    } else {
                        break;
                    }
                }
            });
            Map<String, Object> jsonObject1 = new HashMap<>();
            double xbjdj = tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0;
            double je = 1400.0;
            if (xbjdj == 0) {
                jsonObject1.put("sfbj", "是");
                jsonObject1.put("xcdzjbj", je);
                jsonObject1.put("xbjdj", je);
                jsonObject1.put("xzzfybj", 0.0);
                jsonObject1.put("xbjdjlx", "2");
                jsonObject1.put("xxsbj", je);
                jsonObject1.put("xsbjzbd", list1);
                GatewayResult gatewayResult = xgBdSl(jsonObject1, slid, userId.get());
                System.out.println("修改订单：" + gatewayResult);
            } else {
                JSONObject data = null;
                for (int i = 0; i < 5; i++) {
                    String s = YdConfig.hqBdSj(ydAppkey.getAppkey(), ydAppkey.getToken(), slid);
                    if (!"error".equals(s)) {
                        JSONObject jsonObject5 = JSONObject.parseObject(s);
                        if (jsonObject5.getBoolean("success")) {
                            JSONObject result = jsonObject5.getJSONObject("result");
                            if (result != null && result.size() > 0) {
                                data = result.getJSONObject("formData");
                                break;
                            }
                        }
                    }
                    if (i == 4) {
                        try {
                            String context1 = "补交定金验券查询订单失败，原因：" + s;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e) {
                            e.printStackTrace();
                        }
                    }
                }
                if (data != null) {
                    if ("2".equals(tbXyd.getXbjdjlx())) {
                        JSONArray xsbjzbd = data.getJSONArray("xsbjzbd");
                        jsonObject1.put("sfbj", "是");
                        jsonObject1.put("xbjdjlx", "2");

                        double je1 = je;
                        if (xsbjzbd != null && xsbjzbd.size() > 0) {
                            for (Object o : xsbjzbd) {
                                JSONObject j = (JSONObject) o;
                                Double xsbjje = j.getDouble("xsbjje") != null ? j.getDouble("xsbjje") : 0;
                                je += xsbjje;
                                if (xsbjje > 0) {
                                    list1.add(j);
                                }
                            }
                        }
                        jsonObject1.put("xcdzjbj", je1 + (tbXyd.getXcdzjbj() != null ? tbXyd.getXcdzjbj() : 0.0));
                        jsonObject1.put("xbjdj", je1 + (tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0));
                        jsonObject1.put("xxsbj", je);
                        jsonObject1.put("xsbjzbd", list1);
                        GatewayResult gatewayResult = xgBdSl(jsonObject1, slid, userId.get());
                        System.out.println(gatewayResult);
                    } else if ("1".equals(tbXyd.getXbjdjlx())) {

                        jsonObject1.put("sfbj", "是");
                        jsonObject1.put("xbjdjlx", "e9d99d68afc54a25bdf08c8cb9dd767e");

                        jsonObject1.put("xcdzjbj", je + (tbXyd.getXcdzjbj() != null ? tbXyd.getXcdzjbj() : 0.0));
                        jsonObject1.put("xbjdj", je + (tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0));
                        jsonObject1.put("xxsbj", je);
                        jsonObject1.put("xsbjzbd", list1);

                        GatewayResult gatewayResult = xgBdSl(jsonObject1, slid, userId.get());
                        System.out.println(gatewayResult);
                    } else if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(tbXyd.getXbjdjlx())) {
                        jsonObject1.put("sfbj", "是");
                        jsonObject1.put("xbjdjlx", "e9d99d68afc54a25bdf08c8cb9dd767e");

                        double je1 = je;
                        JSONArray xsbjzbd = data.getJSONArray("xsbjzbd");
                        if (xsbjzbd != null && xsbjzbd.size() > 0) {
                            for (Object o : xsbjzbd) {
                                JSONObject j = (JSONObject) o;
                                double xsbjje = j.getDouble("xsbjje") != null ? j.getDouble("xsbjje") : 0;
                                je += xsbjje;
                                if (xsbjje > 0) {
                                    list1.add(j);
                                }
                            }
                        }
                        jsonObject1.put("xcdzjbj", je1 + (tbXyd.getXcdzjbj() != null ? tbXyd.getXcdzjbj() : 0.0));
                        jsonObject1.put("xbjdj", je1 + (tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0));
                        jsonObject1.put("xxsbj", je);
                        jsonObject1.put("xsbjzbd", list1);

                        GatewayResult gatewayResult = xgBdSl(jsonObject1, slid, userId.get());
                        System.out.println(gatewayResult);
                    }
                }
            }
        }
        return Result.OK();
    }

    /**
     * 输码验券校验接口:https://openapi.dianping.com/router/tuangou/receipt/prepare
     * 根据团购券码，查询券码对应的dealid下，该用户可使用的券数据量
     *
     * @param app_key
     * @param app_secret
     * @param session
     * @param receipt_code
     * @param open_shop_uuid
     */
    public static TuangouReceiptPrepareResponse jyQm(String app_key, String app_secret, String session, String receipt_code, String open_shop_uuid, String app_shop_id) {
        DefaultOpenAPIClient openAPIClient = new DefaultOpenAPIClient();
        TuangouReceiptPrepareRequest request = new TuangouReceiptPrepareRequest(app_key, app_secret, session, receipt_code, app_shop_id, open_shop_uuid);
        TuangouReceiptPrepare tuangouReceiptPrepare = new TuangouReceiptPrepare(request);
        TuangouReceiptPrepareResponse response = new TuangouReceiptPrepareResponse();
        try {
            response = openAPIClient.invoke(tuangouReceiptPrepare);
            log.info("******校验返回****{}", app_key);
        } catch (Exception e) {
            return null;
        }
        return response;
    }

    /**
     * 查询已经验过的券码详情
     *
     * @param app_key
     * @param app_secret
     * @param session
     * @param receipt_code
     */
    public static TuangouReceiptGetConsumedReponse ygdqm(String app_key, String app_secret, String session, String receipt_code, String open_shop_uuid) {
        DefaultOpenAPIClient openAPIClient = new DefaultOpenAPIClient();
        TuangouReceiptGetConsumedRequest request = new TuangouReceiptGetConsumedRequest(app_key, app_secret, session, receipt_code, null, open_shop_uuid);
        TuangouReceiptGetConsumed tuangouReceiptGetConsumed = new TuangouReceiptGetConsumed(request);
        TuangouReceiptGetConsumedReponse response = openAPIClient.invoke(tuangouReceiptGetConsumed);
        try {
            response = openAPIClient.invoke(tuangouReceiptGetConsumed);
        } catch (Exception e) {
            return null;
        }
        return response;
    }

    /**
     * 页面点击生成线下定金二维码异步
     */
    @RequestMapping(value = "/ywySendSkDjTaSk")
    public void ywySendSkDjTaSk(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (!Optional.ofNullable(datas).isPresent()) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        WlgbJdDjLsjlb wlgbJdDjLsjlb = jsonObject.toJavaObject(WlgbJdDjLsjlb.class);
        if (!Optional.ofNullable(wlgbJdDjLsjlb).isPresent()) {
            return;
        }
        //查询该客户是否有未使用的定金记录
        List<WlgbJdDjLsjlb> wlgbJdDjLsjlbjl = wlgbJdDjLsjlbService.queryListByCrmBhAndSfScAndSfDz(wlgbJdDjLsjlb.getCrmbh() != null ? wlgbJdDjLsjlb.getCrmbh() : "", 0, 0, wlgbJdDjLsjlb.getUuid());
        //多次发起，则删除之前的，只保留最新的登记记录
        if (!wlgbJdDjLsjlbjl.isEmpty()) {
            wlgbJdDjLsjlbjl.forEach(l -> {
                WlgbJdDjLsjlb wlgbJdDjLsjlb1 = new WlgbJdDjLsjlb();
                wlgbJdDjLsjlb1.setSfsc(1);
                wlgbJdDjLsjlb1.setId(l.getId());
                wlgbJdDjLsjlbService.updateById(wlgbJdDjLsjlb1);
            });
        }
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        SimpleDateFormat df3 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.HOUR, 1);
        String format = df1.format(new Date());
        //调用接口生成二维码
        String url = ddxturl + "/ysdjtb/wlgb/money/pdFs?skbh=" + format;
        //收款二维码
        String ewm = null;
        try {
            //生成收款二维码
            ewm = scEwm(url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(wlgbJdDjLsjlb.getFqrId());
        wlgbJdDjLsjlb.setFqrName(dingdingEmployee != null ? dingdingEmployee.getName() : "");
        wlgbJdDjLsjlb.setId(IdConfig.uuId());
        wlgbJdDjLsjlb.setSkbh(format);
        wlgbJdDjLsjlb.setEwm(ewm);
        wlgbJdDjLsjlbService.save(wlgbJdDjLsjlb);
        //定金实例记录存储
        WlgbDjsljlcc wlgbDjsljlcc = new WlgbDjsljlcc();
        wlgbDjsljlcc.setSkbh(format);
        wlgbDjsljlcc.setCzr(dingdingEmployee != null ? dingdingEmployee.getName() : null);
        wlgbDjsljlcc.setCzrid(dingdingEmployee != null ? dingdingEmployee.getUserid() : null);
        wlgbDjsljlcc.setSkje(wlgbJdDjLsjlb.getJe());
        wlgbDjsljlcc.setEwm(ewm);
        wlgbDjsljlcc.setType("1");
        wlgbDjsljlcc.setId(IdConfig.uuId());
        wlgbDjsljlcc.setSkmc("线下定金");
        wlgbDjsljlccService.save(wlgbDjsljlcc);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String text = "您发起的线下定金付款已生效，请提醒客户前往微信公众号进行查询支付！";
        text += "\n\n客户电话：" + wlgbJdDjLsjlb.getKhdh();
        text += "\n金额：" + wlgbJdDjLsjlb.getJe();
        text += "\n送达时间：" + df3.format(new Date());
        try {
            DingDBConfig.sendGztzText(dingkey, dingdingEmployee != null ? dingdingEmployee.getUserid() : wlgbJdDjLsjlb.getFqrId(), text);
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    /**
     * 线下定金到账
     *
     * @param request
     */
    @RequestMapping(value = "djDzTaSk")
    public void djDzTaSk(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        SelectVo selectVo = JSONObject.toJavaObject(jsonObject, SelectVo.class);
        System.out.println(selectVo);
        WlgbHxyhDzjl wlgbHxyhDzjl = new WlgbHxyhDzjl();
        wlgbHxyhDzjl.setAccountDate(selectVo.getAccount_date());
        wlgbHxyhDzjl.setBuyerLogonId(selectVo.getBuyer_logon_id());
        wlgbHxyhDzjl.setBuyerUserId(selectVo.getBuyer_user_id());
        wlgbHxyhDzjl.setCardType(selectVo.getCard_type());
        wlgbHxyhDzjl.setChannelRecvSn(selectVo.getChannel_recv_sn());
        wlgbHxyhDzjl.setChannelSendSn(selectVo.getChannel_send_sn());
        wlgbHxyhDzjl.setExtraCommonParam(selectVo.getExtra_common_param());
        wlgbHxyhDzjl.setFee(selectVo.getFee());
        wlgbHxyhDzjl.setIsDiscount(selectVo.getIs_discount());
        wlgbHxyhDzjl.setNotifyTime(selectVo.getNotify_time());
        wlgbHxyhDzjl.setNotifyType(selectVo.getNotify_type());
        wlgbHxyhDzjl.setOutTradeNo(selectVo.getOut_trade_no());
        wlgbHxyhDzjl.setPartnerFee(selectVo.getPartner_fee());
        wlgbHxyhDzjl.setPayeeFee(selectVo.getPayee_fee());
        wlgbHxyhDzjl.setPayerFee(selectVo.getPayer_fee());
        wlgbHxyhDzjl.setPaygateNo(selectVo.getPaygate_no());
        wlgbHxyhDzjl.setSettlementAmount(selectVo.getSettlement_amount());
        wlgbHxyhDzjl.setSign(selectVo.getSign());
        wlgbHxyhDzjl.setSignType(selectVo.getSign_type());
        wlgbHxyhDzjl.setTotalAmount(selectVo.getTotal_amount());
        wlgbHxyhDzjl.setTotalDiscount(selectVo.getTotal_discount());
        wlgbHxyhDzjl.setTotalDiscountFee(selectVo.getTotal_discount_fee());
        wlgbHxyhDzjl.setTradeNo(selectVo.getTrade_no());
        wlgbHxyhDzjl.setTradeStatus(selectVo.getTrade_status());

        String outTradeNo = selectVo.getOut_trade_no();
        if (outTradeNo != null && outTradeNo.contains("ZFB")) {
            outTradeNo = outTradeNo.replace("ZFB", "");
        }
        if (outTradeNo != null && outTradeNo.contains("WX")) {
            outTradeNo = outTradeNo.replace("WX", "");
        }

        WlgbHxyhDzjl hxyhDzjl = wlgbHxyhDzjlService.queryByYsBhAndLsBh(selectVo.getTrade_no(), selectVo.getOut_trade_no());
        if (hxyhDzjl != null) {
            if (!(hxyhDzjl.getTradeStatus() != null ? hxyhDzjl.getTradeStatus() : "").equals(selectVo.getTrade_status())) {
                wlgbHxyhDzjl.setId(hxyhDzjl.getId());
                wlgbHxyhDzjlService.updateById(wlgbHxyhDzjl);
            }
            return;
        }
        wlgbHxyhDzjl.setId(IdConfig.uuId());
        wlgbHxyhDzjlService.save(wlgbHxyhDzjl);

        List<WlgbHxyhFqsk> list = wlgbHxyhFqskService.queryByDdBhAndYsBh(outTradeNo, wlgbHxyhDzjl.getTradeNo());
        WlgbHxyhFqsk hxyhFqsk = null;
        if (list != null && list.size() > 0) {
            hxyhFqsk = list.get(0);
        }
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (hxyhFqsk != null) {
            String text = "您发起的二维码收款（" + hxyhFqsk.getFymc() + "）已到账！";
            text += "\n\n订单编号：" + outTradeNo;
            text += "\n\n到账金额：" + (wlgbHxyhDzjl.getTotalAmount() != null ? wlgbHxyhDzjl.getTotalAmount() : 0) + "元";
            text += "\n\n付款方式：" + hxyhFqsk.getFkfs();
            text += "\n\n发起收款时间：" + df1.format(hxyhFqsk.getTjsj());
            text += "\n\n送达时间：" + df1.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, hxyhFqsk.getTjrid(), text);
            } catch (ApiException apiException) {
                apiException.printStackTrace();
            }
            list.forEach(l -> {
                WlgbHxyhFqsk wlgbHxyhFqsk = new WlgbHxyhFqsk();
                wlgbHxyhFqsk.setId(l.getId());
                wlgbHxyhFqsk.setZfzt("是");
                wlgbHxyhFqskService.updateById(wlgbHxyhFqsk);
            });
        }
        WlgbJdDjLsjlb wlgbJdDjLsjlb1 = wlgbJdDjLsjlbService.queryBySkBhAndSfSc(outTradeNo, 0);
        Date date = new Date();
        try {
            date = df1.parse(wlgbHxyhDzjl.getNotifyTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        wlgbJdDjLsjlb1.setSfxyld(0);
        wlgbJdDjLsjlb1.setSfbjdjwk(0);
        wlgbJdDjLsjlb1.setSfdz(1);
        wlgbJdDjLsjlb1.setDzsj(date);
        wlgbJdDjLsjlb1.setSfhk(1);
        List<WlgbJdDjbbd> list1 = wlgbJdDjbbdService.queryByLshAndSfScAndDjLx(wlgbJdDjLsjlb1.getLsh(), 1);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        wlgbJdDjLsjlb1.setSfbjdj(0);
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        if (list1.size() > 0) {
            wlgbJdDjLsjlb1.setSfxyld(1);
            wlgbJdDjLsjlb1.setSfbjdjwk(1);
            String finalToken = token;
            String finalOutTradeNo = outTradeNo;
            list1.forEach(l -> {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("sfxyld", 1);
                jsonObject1.put("sfbjdjwk", 1);
                for (int i = 0; i < 5; i++) {
                    GatewayResult gatewayResult;
                    try {
                        gatewayResult = DingBdLcConfig.xgBdSl(finalToken, ydAppkey, l.getFqrId(), l.getFormInstId(), jsonObject1.toJSONString());
                    } catch (Exception e) {
                        e.printStackTrace();
                        gatewayResult = new GatewayResult();
                    }
                    Boolean success = gatewayResult.getSuccess();
                    if (success != null && !success) {
                        if (i == 4) {
                            try {
                                String context1 = "定金是补交到账出错，收款编号：" + finalOutTradeNo + "，错误原因：" + gatewayResult.toString();
                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                            } catch (ApiException e) {
                                e.printStackTrace();
                            }
                        }
                    } else {
                        break;
                    }
                }
            });
            wlgbJdDjLsjlb1.setSfbjdj(1);
        }
        wlgbJdDjLsjlb1.setSkbh(wlgbHxyhDzjl.getOutTradeNo());
        WlgbJdDjbbd wlgbJdDjbbd = new WlgbJdDjbbd();
        BeanUtils.copyProperties(wlgbJdDjLsjlb1, wlgbJdDjbbd);
        JSONObject jsonObjectlc = fqDjBdData(wlgbJdDjbbd);
        //定金表唯一标识
        jsonObjectlc.put("djwybs", IdConfig.uuId());
        String formId = "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1";
        GatewayResult gatewayResult = null;
        for (int i = 0; i < 5; i++) {
            try {
                gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObjectlc.toString(), wlgbJdDjbbd.getFqrId(), formId);
            } catch (Exception e) {
                e.printStackTrace();
                gatewayResult = new GatewayResult();
            }
            Boolean success = gatewayResult.getSuccess();
            if (success != null && !success) {
                if (i == 4) {
                    try {
                        String context1 = "定金到账同步表单出错了1，收款编号：" + outTradeNo + "，错误原因：" + gatewayResult.toString();
                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                break;
            }
        }
        String ly = null;
        if (wlgbJdDjbbd.getDjlx() == 2) {
            if (wlgbJdDjbbd.getSspt() == 1) {
                ly = "美团";
            } else {
                ly = "短租";
            }
        }
        Boolean skd3 = KingDeeConfig.saveSkd3(wlgbHxyhDzjl.getTradeNo(), wlgbHxyhDzjl.getTotalAmount(), wlgbHxyhDzjl.getOutTradeNo(), jsonObjectlc.getString("hqqm"), ly, date, "6230200551557759");
        if (skd3) {
            WlgbHxyhDzjl wlgbHxyhDzjl2 = wlgbHxyhDzjlService.queryByYsBhAndLsBh(wlgbHxyhDzjl.getTradeNo(), wlgbHxyhDzjl.getOutTradeNo());
            WlgbHxyhDzjl wlgbHxyhDzjl1 = new WlgbHxyhDzjl();
            wlgbHxyhDzjl1.setId(wlgbHxyhDzjl2.getId());
            wlgbHxyhDzjl1.setSflrjd(1);
            wlgbHxyhDzjlService.updateById(wlgbHxyhDzjl1);
            WlgbJdDjbbd wlgbJdDjbbd1 = wlgbJdDjbbdService.queryByLshAndSkBhAndSfScAndDjLx(wlgbJdDjLsjlb1.getLsh(), wlgbJdDjLsjlb1.getSkbh(), 0, 1);
            if (wlgbJdDjbbd1 != null) {
                WlgbJdDjbbd wlgbJdDjbbd2 = new WlgbJdDjbbd();
                wlgbJdDjbbd2.setId(wlgbJdDjbbd1.getId());
                wlgbJdDjbbd2.setSflrjd(1);
                wlgbJdDjbbdService.updateById(wlgbJdDjbbd2);
            }
        }
        //获取表单实例id
        if (gatewayResult.getSuccess()) {
            WlgbJdDjLsjlb wlgbJdDjLsjlb = new WlgbJdDjLsjlb();
            wlgbJdDjLsjlb.setFormInstId(gatewayResult.getResult());
            wlgbJdDjLsjlb.setId(wlgbJdDjLsjlb1.getId());
            wlgbJdDjLsjlb.setSfdz(1);
            wlgbJdDjLsjlb.setDzsj(date);
            wlgbJdDjLsjlb.setSfhk(1);
            if (list1.size() > 0) {
                wlgbJdDjLsjlb.setSfxyld(1);
                wlgbJdDjLsjlb.setSfbjdjwk(1);
                wlgbJdDjLsjlb.setSfbjdj(1);
            }
            wlgbJdDjLsjlbService.updateById(wlgbJdDjLsjlb);
        }
    }

    @RequestMapping(value = "scDj")
    public Result scDj() {
        WlgbHxyhDzjl wlgbHxyhDzjl = wlgbHxyhDzjlService.queryByYsBhAndLsBh("231970327910240510600700866536", "20240510150455038WX");
        String outTradeNo = wlgbHxyhDzjl.getOutTradeNo();
        if (outTradeNo != null && outTradeNo.contains("ZFB")) {
            outTradeNo = outTradeNo.replace("ZFB", "");
        }
        if (outTradeNo != null && outTradeNo.contains("WX")) {
            outTradeNo = outTradeNo.replace("WX", "");
        }
        WlgbJdDjLsjlb wlgbJdDjLsjlb1 = wlgbJdDjLsjlbService.queryBySkBhAndSfSc(outTradeNo, 0);
        Date date = new Date();
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            date = df1.parse(wlgbHxyhDzjl.getNotifyTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        wlgbJdDjLsjlb1.setSfxyld(0);
        wlgbJdDjLsjlb1.setSfbjdjwk(0);
        wlgbJdDjLsjlb1.setSfdz(1);
        wlgbJdDjLsjlb1.setDzsj(date);
        wlgbJdDjLsjlb1.setSfhk(1);

        List<WlgbJdDjbbd> list1 = wlgbJdDjbbdService.queryByLshAndSfScAndDjLx(wlgbJdDjLsjlb1.getLsh(), 1);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        wlgbJdDjLsjlb1.setSfbjdj(0);

        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }

        if (list1.size() > 0) {
            wlgbJdDjLsjlb1.setSfxyld(1);
            wlgbJdDjLsjlb1.setSfbjdjwk(1);
            String finalToken = token;
            String finalOutTradeNo = outTradeNo;
            list1.forEach(l -> {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("sfxyld", 1);
                jsonObject1.put("sfbjdjwk", 1);
                for (int i = 0; i < 5; i++) {
                    GatewayResult gatewayResult;
                    try {
                        gatewayResult = DingBdLcConfig.xgBdSl(finalToken, ydAppkey, l.getFqrId(), l.getFormInstId(), jsonObject1.toJSONString());
                    } catch (Exception e) {
                        e.printStackTrace();
                        gatewayResult = new GatewayResult();
                    }
                    Boolean success = gatewayResult.getSuccess();
                    if (success != null && !success) {
                        if (i == 4) {
                            try {
                                String context1 = "定金是补交到账出错，收款编号：" + finalOutTradeNo + "，错误原因：" + gatewayResult.toString();
                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
//                                    DingDBConfig.sendGztzText(dingkey, "159909317438346", context1);
                            } catch (ApiException e) {
                                e.printStackTrace();
                            }
                        }
                    } else {
                        break;
                    }
                }
            });
            wlgbJdDjLsjlb1.setSfbjdj(1);
        }
        wlgbJdDjLsjlb1.setSkbh(wlgbHxyhDzjl.getOutTradeNo());

        WlgbJdDjbbd wlgbJdDjbbd = new WlgbJdDjbbd();
        BeanUtils.copyProperties(wlgbJdDjLsjlb1, wlgbJdDjbbd);
        JSONObject jsonObjectlc = fqDjBdData(wlgbJdDjbbd);
        //定金表唯一标识
        jsonObjectlc.put("djwybs", IdConfig.uuId());


        String formId = "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1";

        GatewayResult gatewayResult = null;
        for (int i = 0; i < 5; i++) {
            try {
                gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObjectlc.toString(), wlgbJdDjbbd.getFqrId(), formId);
            } catch (Exception e) {
                e.printStackTrace();
                gatewayResult = new GatewayResult();
            }
            Boolean success = gatewayResult.getSuccess();
            if (success != null && !success) {
                if (i == 4) {
                    try {
                        String context1 = "定金到账同步表单出错了2，收款编号：" + outTradeNo + "，错误原因：" + gatewayResult.toString();
                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                break;
            }
        }
        String ly = null;
        if (wlgbJdDjbbd.getDjlx() == 2) {
            if (wlgbJdDjbbd.getSspt() == 1) {
                ly = "美团";
            } else {
                ly = "短租";
            }
        }
        Boolean skd3 = KingDeeConfig.saveSkd3(wlgbHxyhDzjl.getTradeNo(), wlgbHxyhDzjl.getTotalAmount(), wlgbHxyhDzjl.getOutTradeNo(), jsonObjectlc.getString("hqqm"), ly, date, "6230200551557759");
        if (skd3) {
            WlgbHxyhDzjl wlgbHxyhDzjl2 = wlgbHxyhDzjlService.queryByYsBhAndLsBh(wlgbHxyhDzjl.getTradeNo(), wlgbHxyhDzjl.getOutTradeNo());
            WlgbHxyhDzjl wlgbHxyhDzjl1 = new WlgbHxyhDzjl();
            wlgbHxyhDzjl1.setId(wlgbHxyhDzjl2.getId());
            wlgbHxyhDzjl1.setSflrjd(1);
            wlgbHxyhDzjlService.updateById(wlgbHxyhDzjl1);
            WlgbJdDjbbd wlgbJdDjbbd1 = wlgbJdDjbbdService.queryByLshAndSkBhAndSfScAndDjLx(wlgbJdDjLsjlb1.getLsh(), wlgbJdDjLsjlb1.getSkbh(), 0, 1);
            if (wlgbJdDjbbd1 != null) {
                WlgbJdDjbbd wlgbJdDjbbd2 = new WlgbJdDjbbd();
                wlgbJdDjbbd2.setId(wlgbJdDjbbd1.getId());
                wlgbJdDjbbd2.setSflrjd(1);
                wlgbJdDjbbdService.updateById(wlgbJdDjbbd2);
            }
        }

        //获取表单实例id
        if (gatewayResult.getSuccess()) {
            WlgbJdDjLsjlb wlgbJdDjLsjlb = new WlgbJdDjLsjlb();
            wlgbJdDjLsjlb.setFormInstId(gatewayResult.getResult());
            wlgbJdDjLsjlb.setId(wlgbJdDjLsjlb1.getId());
            wlgbJdDjLsjlb.setSfdz(1);
            wlgbJdDjLsjlb.setDzsj(date);
            wlgbJdDjLsjlb.setSfhk(1);
            if (list1.size() > 0) {
                wlgbJdDjLsjlb.setSfxyld(1);
                wlgbJdDjLsjlb.setSfbjdjwk(1);
                wlgbJdDjLsjlb.setSfbjdj(1);
            }
            wlgbJdDjLsjlbService.updateById(wlgbJdDjLsjlb);
        }
        return Result.OK();
    }

    @RequestMapping(value = "djDzYddTaSk")
    public void djDzYddTaSk(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        SelectVo selectVo = JSONObject.toJavaObject(jsonObject, SelectVo.class);
        System.out.println(selectVo);
        WlgbHxyhDzjl wlgbHxyhDzjl = new WlgbHxyhDzjl();
        wlgbHxyhDzjl.setAccountDate(selectVo.getAccount_date());
        wlgbHxyhDzjl.setBuyerLogonId(selectVo.getBuyer_logon_id());
        wlgbHxyhDzjl.setBuyerUserId(selectVo.getBuyer_user_id());
        wlgbHxyhDzjl.setCardType(selectVo.getCard_type());
        wlgbHxyhDzjl.setChannelRecvSn(selectVo.getChannel_recv_sn());
        wlgbHxyhDzjl.setChannelSendSn(selectVo.getChannel_send_sn());
        wlgbHxyhDzjl.setExtraCommonParam(selectVo.getExtra_common_param());
        wlgbHxyhDzjl.setFee(selectVo.getFee());
        wlgbHxyhDzjl.setIsDiscount(selectVo.getIs_discount());
        wlgbHxyhDzjl.setNotifyTime(selectVo.getNotify_time());
        wlgbHxyhDzjl.setNotifyType(selectVo.getNotify_type());
        wlgbHxyhDzjl.setOutTradeNo(selectVo.getOut_trade_no());
        wlgbHxyhDzjl.setPartnerFee(selectVo.getPartner_fee());
        wlgbHxyhDzjl.setPayeeFee(selectVo.getPayee_fee());
        wlgbHxyhDzjl.setPayerFee(selectVo.getPayer_fee());
        wlgbHxyhDzjl.setPaygateNo(selectVo.getPaygate_no());
        wlgbHxyhDzjl.setSettlementAmount(selectVo.getSettlement_amount());
        wlgbHxyhDzjl.setSign(selectVo.getSign());
        wlgbHxyhDzjl.setSignType(selectVo.getSign_type());
        wlgbHxyhDzjl.setTotalAmount(selectVo.getTotal_amount());
        wlgbHxyhDzjl.setTotalDiscount(selectVo.getTotal_discount());
        wlgbHxyhDzjl.setTotalDiscountFee(selectVo.getTotal_discount_fee());
        wlgbHxyhDzjl.setTradeNo(selectVo.getTrade_no());
        wlgbHxyhDzjl.setTradeStatus(selectVo.getTrade_status());

        String outTradeNo = selectVo.getOut_trade_no();
        if (outTradeNo != null && outTradeNo.contains("ZFB")) {
            outTradeNo = outTradeNo.replace("ZFB", "");
        }
        if (outTradeNo != null && outTradeNo.contains("WX")) {
            outTradeNo = outTradeNo.replace("WX", "");
        }

        WlgbHxyhDzjl hxyhDzjl = wlgbHxyhDzjlService.queryByYsBhAndLsBh(selectVo.getTrade_no(), selectVo.getOut_trade_no());
        if (hxyhDzjl != null) {
            if (!(hxyhDzjl.getTradeStatus() != null ? hxyhDzjl.getTradeStatus() : "").equals(selectVo.getTrade_status())) {
                wlgbHxyhDzjl.setId(hxyhDzjl.getId());
                wlgbHxyhDzjlService.updateById(wlgbHxyhDzjl);
            }
            return;
        }
        wlgbHxyhDzjl.setId(IdConfig.uuId());
        wlgbHxyhDzjlService.save(wlgbHxyhDzjl);

        List<WlgbHxyhFqsk> list = wlgbHxyhFqskService.queryByDdBhAndYsBh(outTradeNo, wlgbHxyhDzjl.getTradeNo());
        WlgbHxyhFqsk hxyhFqsk = null;
        if (list != null && list.size() > 0) {
            hxyhFqsk = list.get(0);
        }
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (hxyhFqsk != null) {
            list.forEach(l -> {
                WlgbHxyhFqsk wlgbHxyhFqsk = new WlgbHxyhFqsk();
                wlgbHxyhFqsk.setId(l.getId());
                wlgbHxyhFqsk.setZfzt("是");
                wlgbHxyhFqskService.updateById(wlgbHxyhFqsk);
            });

        }

        WlgbJdDjbbd wlgbJdDjbbd3 = new WlgbJdDjbbd();
        Date date = new Date();
        try {
            date = df1.parse(wlgbHxyhDzjl.getNotifyTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        wlgbJdDjbbd3.setJe(wlgbHxyhDzjl.getTotalAmount());
        wlgbJdDjbbd3.setSfxyld(0);
        wlgbJdDjbbd3.setSfbjdjwk(0);
        wlgbJdDjbbd3.setSfdz(1);
        wlgbJdDjbbd3.setDzsj(date);
        wlgbJdDjbbd3.setLsh(outTradeNo);
        wlgbJdDjbbd3.setSfscdj(0);

        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");

        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }

        wlgbJdDjbbd3.setSkbh(wlgbHxyhDzjl.getOutTradeNo());

        TbYddXyd tbYddXyd1 = new TbYddXyd();
        tbYddXyd1.setXsfsc(0);
        tbYddXyd1.setXskbh(outTradeNo);
        TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd1);
        if (tbYddXyd != null) {
            String text = "您发起的预定单二维码收款定金已到账，系统即将自动完成下单操作，请稍后注意留意一下订单信息！";
            text += "\n\n订单编号：" + tbYddXyd.getXddbh();
            text += "\n门店：" + tbYddXyd.getXbsname();
            text += "\n客户姓名：" + tbYddXyd.getXzk();
            text += "\n客户电话：" + tbYddXyd.getXzkdh();
            text += "\n进场时间：" + DateFormatConfig.df3(tbYddXyd.getXjctime());
            text += "\n退场时间：" + DateFormatConfig.df3(tbYddXyd.getXtctime());
            text += "\n到账金额：" + (wlgbHxyhDzjl.getTotalAmount() != null ? wlgbHxyhDzjl.getTotalAmount() : 0) + "元";
            text += "\n发起收款时间：" + DateFormatConfig.df1(tbYddXyd.getXdjurltime());
            text += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, tbYddXyd.getXfdid(), text);
            } catch (ApiException apiException) {
                apiException.printStackTrace();
            }

            wlgbJdDjbbd3.setCrmbh(tbYddXyd.getQqid());
            wlgbJdDjbbd3.setFqrId(tbYddXyd.getXfdid());
            wlgbJdDjbbd3.setFqrName(tbYddXyd.getXfd());
            wlgbJdDjbbd3.setKhdh(tbYddXyd.getXzkdh());
            wlgbJdDjbbd3.setFqsj(tbYddXyd.getXdjurltime());
            wlgbJdDjbbd3.setSfz(tbYddXyd.getXzksfz());
            wlgbJdDjbbd3.setSfxd(1);
            wlgbJdDjbbd3.setXdsj(tbYddXyd.getCreateTime());
            wlgbJdDjbbd3.setDdbh(tbYddXyd.getXddbh());
            wlgbJdDjbbd3.setEwm(tbYddXyd.getXdjurl());

            TbYddXyd tbYddXyd2 = new TbYddXyd();
            tbYddXyd2.setXstatu("已交定金，等待店长主动联系您");
            tbYddXyd2.setXdjpaytime(date);
            tbYddXyd2.setXid(tbYddXyd.getXid());
            tbYddXydService.updateById(tbYddXyd2);

            //设置操作日志
            WlgbXydLog wlgbXydLog = new WlgbXydLog();
            wlgbXydLog.setXlid(tbYddXyd.getXid());
            wlgbXydLog.setXltime(new Date());
            wlgbXydLog.setXluserid("系统");
            wlgbXydLog.setXlname("系统自动操作");
            wlgbXydLog.setXlid(IdConfig.uuId());
            wlgbXydLog.setXltext("已交定金，等待店长主动联系您");
            wlgbXydLogService.save(wlgbXydLog);

            String post = null;
            try {
                Map<String, String> map = new HashMap<>();
                map.put("ddbh", tbYddXyd.getXddbh());
                map.put("zt", tbYddXyd2.getXstatu());
                //总价
                map.put("zj", tbYddXyd.getXhfyj() + tbYddXyd.getXztze() + tbYddXyd.getXdcze() + tbYddXyd.getXhpsfy() + tbYddXyd.getXbxzje() + "");
                post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/b2e50678-62d2-4d65-a042-27427265a26f", map);
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    String context1 = "定金到账将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，错误原因：" + e;
                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                } catch (ApiException e1) {
                    e1.printStackTrace();
                }
            }
            try {
                if (post != null && !"".equals(post)) {
                    JSONObject jsonObject1 = JSONObject.parseObject(post);
                    String jg = jsonObject1.getString("jg");
                    if (!"ok".equalsIgnoreCase(jg)) {
                        try {
                            String context1 = "定金到账将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                    }
                }
            } catch (Exception e) {
                try {
                    String context1 = "定金到账将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                } catch (ApiException e1) {
                    e1.printStackTrace();
                }
                e.printStackTrace();
            }

            JSONObject map = new JSONObject();
            map.put("xzzsj", tbYddXyd2.getXdjpaytime());
            //支付时间
            map.put("dateField_ls5tv21l", tbYddXyd2.getXdjpaytime());
            //支付后六位
            map.put("xzzhlw", selectVo.getOut_trade_no());

            YdAppkey ydAppkey1 = new YdAppkey();
            ydAppkey1.setAppkey("APP_RDI3PDV2X43HWZM6ACGS");
            ydAppkey1.setToken("9X866BD1IR0ICG8C8T6NI4AKCWXX36BCVLVRL04");

            GatewayResult gatewayResult = new GatewayResult();
            try {
                gatewayResult = YdConfig.querySpJl(token, ydAppkey1, tbYddXyd.getXslid());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (gatewayResult.getSuccess()) {
                JSONArray objects = JSONObject.parseArray(gatewayResult.getResult());
                for (Object obj : objects) {
                    JSONObject jsonObject1 = (JSONObject) obj;
                    if ("TODO".equals(jsonObject1.getString("type")) && "客户支付定金".equals(jsonObject1.getString("showName"))) {
                        System.out.println("查到任务");
                        long taskId = jsonObject1.getLong("taskId");
                        //准备执行任务
                        GatewayResult gatewayResult1 = null;
                        try {
                            gatewayResult1 = YdConfig.zxYdSpJdRw(token, tbYddXyd.getXslid(), ydAppkey1, "客户已支付定金", map.toJSONString(), taskId, "012412221639786136545");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        System.out.println(gatewayResult1);
                    }
                }
            }
        }

        JSONObject jsonObjectlc = fqDjBdDataYdd(wlgbJdDjbbd3);
        //定金表唯一标识
        jsonObjectlc.put("djwybs", IdConfig.uuId());


        String formId = "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1";

        GatewayResult gatewayResult = null;
        for (int i = 0; i < 5; i++) {
            try {
                gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObjectlc.toString(), wlgbJdDjbbd3.getFqrId(), formId);
            } catch (Exception e) {
                e.printStackTrace();
                gatewayResult = new GatewayResult();
            }
            Boolean success = gatewayResult.getSuccess();
            if (success != null && !success) {
                if (i == 4) {
                    try {
                        String context1 = "定金到账同步表单出错了3，收款编号：" + outTradeNo + "，错误原因：" + gatewayResult.toString();
                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                break;
            }
        }

        Boolean skd3 = KingDeeConfig.saveSkd3(wlgbHxyhDzjl.getTradeNo(), wlgbHxyhDzjl.getTotalAmount(), wlgbHxyhDzjl.getOutTradeNo(), jsonObjectlc.getString("hqqm"), null, date, "6230200551557759");
        if (skd3) {
            WlgbHxyhDzjl wlgbHxyhDzjl2 = wlgbHxyhDzjlService.queryByYsBhAndLsBh(wlgbHxyhDzjl.getTradeNo(), wlgbHxyhDzjl.getOutTradeNo());
            WlgbHxyhDzjl wlgbHxyhDzjl1 = new WlgbHxyhDzjl();
            wlgbHxyhDzjl1.setId(wlgbHxyhDzjl2.getId());
            wlgbHxyhDzjl1.setSflrjd(1);
            wlgbHxyhDzjlService.updateById(wlgbHxyhDzjl1);
            WlgbJdDjbbd wlgbJdDjbbd1 = wlgbJdDjbbdService.queryByLshAndSkBhAndSfScAndDjLx(wlgbJdDjbbd3.getLsh(), wlgbJdDjbbd3.getSkbh(), 0, 1);
            if (wlgbJdDjbbd1 != null) {
                WlgbJdDjbbd wlgbJdDjbbd2 = new WlgbJdDjbbd();
                wlgbJdDjbbd2.setId(wlgbJdDjbbd1.getId());
                wlgbJdDjbbd2.setSflrjd(1);
                wlgbJdDjbbdService.updateById(wlgbJdDjbbd2);
            }
        }
    }

    /**
     * 下单与改单客单价判断
     */
    @RequestMapping(value = "tjCcPdTask")
    public void tjCcPdTask(HttpServletRequest request) {
        String bsname = request.getParameter("bsname");
        String jcsj = request.getParameter("jcsj");
        String tcsj = request.getParameter("tcsj");
        String userid = request.getParameter("userid");
        String cdfje = request.getParameter("cdfje");
        String kdjje = request.getParameter("kdjje");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String text = "下单/改单失败了";
        text += "\n\n原因：场地费金额低于客单价";
        if (bsname != null && !"".equals(bsname)) {
            text += "\n别墅：" + bsname;
        }
        text += "\n进场时间：" + jcsj;
        text += "\n退场时间：" + tcsj;
        text += "\n您的场地费金额：" + cdfje;
        text += "\n客单价场地费金额：" + kdjje;
        text += "\n送达时间：" + DateFormatConfig.df1(new Date());
        text += "\n\n请联系客单价专员进行处理！";
        try {
            DingDBConfig.sendGztzText(dingkey, userid, text);
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    /**
     * 发起定金表单数据
     */
    public JSONObject fqDjBdData(WlgbJdDjbbd wlgbJdDjbbd) {
        JSONObject jsonObject = new JSONObject();
        //流水号
        jsonObject.put("lsh", wlgbJdDjbbd.getLsh());
        //金额
        jsonObject.put("je", wlgbJdDjbbd.getJe());
        jsonObject.put("skbh", wlgbJdDjbbd.getSkbh());
        jsonObject.put("crmbh", wlgbJdDjbbd.getCrmbh());
        jsonObject.put("khdh", wlgbJdDjbbd.getKhdh());
        jsonObject.put("fqr", wlgbJdDjbbd.getFqrId());
        jsonObject.put("fqrName", wlgbJdDjbbd.getFqrName());
        jsonObject.put("fqrId", wlgbJdDjbbd.getFqrId());
        if (Optional.ofNullable(wlgbJdDjbbd.getFqsj()).isPresent()) {
            jsonObject.put("fqsj", wlgbJdDjbbd.getFqsj().getTime());
        }
        jsonObject.put("djlx", wlgbJdDjbbd.getDjlx());
        jsonObject.put("sfdgzz", wlgbJdDjbbd.getSfdgzz());
        //二维码
        List<JSONObject> urlList = YdConfig.setTpList(wlgbJdDjbbd.getEwm(), "线下定金" + wlgbJdDjbbd.getSkbh());
        jsonObject.put("ewm", urlList);
        jsonObject.put("sfdz", wlgbJdDjbbd.getSfdz());
        jsonObject.put("dzsj", wlgbJdDjbbd.getDzsj());
        jsonObject.put("sspt", wlgbJdDjbbd.getSspt());
        jsonObject.put("dzsfhk", wlgbJdDjbbd.getSfhk());
        jsonObject.put("mtmd", wlgbJdDjbbd.getMtmd());
        jsonObject.put("hqqm", wlgbJdDjbbd.getHqqm());
        jsonObject.put("yqsfwc", wlgbJdDjbbd.getYqsfwc());
        jsonObject.put("yqjg", wlgbJdDjbbd.getYqjg());
        jsonObject.put("yqqm", wlgbJdDjbbd.getYqqm());
        if (Optional.ofNullable(wlgbJdDjbbd.getYqsj()).isPresent()) {
            jsonObject.put("yqsj", wlgbJdDjbbd.getYqsj().getTime());
        }
        jsonObject.put("dm", wlgbJdDjbbd.getDm());
        jsonObject.put("qmsj", wlgbJdDjbbd.getQmsj());
        jsonObject.put("yj", wlgbJdDjbbd.getYj());
        jsonObject.put("yqr", wlgbJdDjbbd.getYqr());
        jsonObject.put("sftk", wlgbJdDjbbd.getSftk());
        jsonObject.put("sfsddj", wlgbJdDjbbd.getSfsddj());
        jsonObject.put("sfscdj", wlgbJdDjbbd.getSfscdj());
        jsonObject.put("sfxd", wlgbJdDjbbd.getSfxd());
        jsonObject.put("ddbh", wlgbJdDjbbd.getDdbh());
        jsonObject.put("sfbjdj", wlgbJdDjbbd.getSfbjdj());
        if (Optional.ofNullable(wlgbJdDjbbd.getXdsj()).isPresent()) {
            jsonObject.put("xdsj", wlgbJdDjbbd.getXdsj().getTime());
        }
        String outTradeNo = wlgbJdDjbbd.getSkbh();
        String zffs = "微信";
        if (outTradeNo != null && outTradeNo.contains("ZFB")) {
            zffs = "支付宝";
        }
        jsonObject.put("zffs", zffs);
        jsonObject.put("sfsc", wlgbJdDjbbd.getSfsc());
        jsonObject.put("sfz", wlgbJdDjbbd.getSfz());
        jsonObject.put("sfxyld", wlgbJdDjbbd.getSfxyld());
        jsonObject.put("sfbjdjwk", wlgbJdDjbbd.getSfbjdjwk());
        jsonObject.put("sfbjdj", wlgbJdDjbbd.getSfbjdj());
        jsonObject.put("ytje", 0);

        return jsonObject;
    }

    /**
     * 发起定金表单数据---预定单
     */
    public JSONObject fqDjBdDataYdd(WlgbJdDjbbd wlgbJdDjbbd) {
        JSONObject jsonObject = new JSONObject();
        //流水号
        jsonObject.put("lsh", wlgbJdDjbbd.getLsh());
        //金额
        jsonObject.put("je", wlgbJdDjbbd.getJe());
        jsonObject.put("skbh", wlgbJdDjbbd.getSkbh());
        jsonObject.put("crmbh", wlgbJdDjbbd.getCrmbh());
        jsonObject.put("khdh", wlgbJdDjbbd.getKhdh());
        jsonObject.put("fqr", wlgbJdDjbbd.getFqrId());
        jsonObject.put("fqrName", wlgbJdDjbbd.getFqrName());
        jsonObject.put("fqrId", wlgbJdDjbbd.getFqrId());
        if (Optional.ofNullable(wlgbJdDjbbd.getFqsj()).isPresent()) {
            jsonObject.put("fqsj", wlgbJdDjbbd.getFqsj().getTime());
        }
        jsonObject.put("djlx", 1);
        jsonObject.put("sfdgzz", 0);
        //二维码
        List<JSONObject> urlList = YdConfig.setTpList(wlgbJdDjbbd.getEwm(), "预定单定金" + wlgbJdDjbbd.getSkbh());
        jsonObject.put("ewm", urlList);
        jsonObject.put("sfdz", wlgbJdDjbbd.getSfdz());
        jsonObject.put("dzsj", wlgbJdDjbbd.getDzsj());
        jsonObject.put("sftk", 0);
        jsonObject.put("sfsddj", 0);
        jsonObject.put("sfscdj", 0);
        jsonObject.put("sfxd", wlgbJdDjbbd.getSfxd());
        jsonObject.put("ddbh", wlgbJdDjbbd.getDdbh());
        jsonObject.put("sfbjdj", 0);
        if (Optional.ofNullable(wlgbJdDjbbd.getXdsj()).isPresent()) {
            jsonObject.put("xdsj", wlgbJdDjbbd.getXdsj().getTime());
        }
        String outTradeNo = wlgbJdDjbbd.getSkbh();
        String zffs = "微信";
        if (outTradeNo != null && outTradeNo.contains("ZFB")) {
            zffs = "支付宝";
        }
        jsonObject.put("zffs", zffs);
        jsonObject.put("sfsc", 0);
        jsonObject.put("sfz", wlgbJdDjbbd.getSfz());
        jsonObject.put("sfxyld", 0);
        jsonObject.put("sfbjdjwk", 0);
        jsonObject.put("sfbjdj", 0);
        jsonObject.put("ytje", 0);

        return jsonObject;
    }

    /**
     * 生成定金二维码
     *
     * @param url 二维码访问地址
     * @return 阿里云地址
     */
    public String scEwm(String url) throws Exception {
//        String filePath = PathUtil.getClassResources() + "static" + File.separator + "fkewm.jpg";
        String filePath = FileConfig.getFileAbsolutePath2("static" + File.separator + "img" + File.separator + "fkewm" + ".jpg");
        File file = new File(filePath);
        ZXingCode.drawLogoQRCode(null, file, url, "");

        FileInputStream fileInputStream = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
        if (fileInputStream != null) {
            fileInputStream.close();
        }
        String upload = ossFileService.upload(multipartFile);
        if (upload != null && !"".equals(upload)) {
            upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
        }
//        file.delete();
        return upload;
    }

    /**
     * 生成定金二维码
     *
     * @param url 二维码访问地址
     * @return 阿里云地址
     */
    public String scEwm1(String url, String name) throws Exception {
//        String filePath = PathUtil.getClassResources() + "static" + File.separator + "fkewm.jpg";
        String filePath = FileConfig.getFileAbsolutePath("static" + File.separator + "img", name + ".jpg");
        File file = new File(filePath);
        ZXingCode.drawLogoQRCode(null, file, url, "");

        FileInputStream fileInputStream = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
        if (fileInputStream != null) {
            fileInputStream.close();
        }
        String upload = ossFileService.upload(multipartFile);
        if (upload != null && !"".equals(upload)) {
            upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
        }
        file.delete();
//        file.delete();
        return upload;
    }

    /**
     * 选人截取
     *
     * @param userid 用户id
     * @return 用户id
     */
    private String xzjq(String userid) {
        String s = userid.substring(0, 1);
        if ("[".equals(s)) {
            userid = userid.substring(2, userid.length() - 2);
        } else {
            userid = userid;
        }
        return userid;
    }

    /**
     * 生成电子协议单
     *
     * @param xyd 协议单数据
     */
    private Map<String, Object> dzXyd(TbXyd xyd) throws Exception {
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(xyd.getXbsmc());
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy年MM月dd日");
        String zpTime = "进场：" + DateFormatConfig.df1(xyd.getXjctime()) + "<br />退场：" + DateFormatConfig.df1(xyd.getXtctime());
        //除场地费之外的其他费用
        double v = (xyd.getXzrcsfy() != null ? xyd.getXzrcsfy() : 0) + (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXskze() != null ? xyd.getXskze() : 0) + (xyd.getXbxzje() != null ? xyd.getXbxzje() : 0) + (xyd.getXztze() != null ? xyd.getXztze() : 0) + (xyd.getXhpsfy() != null ? xyd.getXhpsfy() : 0) + (xyd.getXjbszje() != null ? xyd.getXjbszje() : 0);
        Map<String, Object> map = new HashMap<>();
        //苏州协议单设置
        map.put("cityType", "苏州".equals(villa.getCity()) ? "1" : "0");
        //订单编号
        map.put("ddbh", xyd.getXddbh());
        //别墅名字
        map.put("bsName", villa.getVname());
        //老板姓名
        map.put("lbxm", xyd.getXlbjf());
        //房东姓名
        map.put("fdName", xyd.getXfd());
        //房东电话
        map.put("fdTelphone", xyd.getXfddh());
        //租客姓名
        map.put("zkName", xyd.getXzk());
        //租客电话
        map.put("zkTelphone", xyd.getXzkdh());
        //租客身份证号码
        map.put("zksfz", xyd.getXzksfz());
        TbKhlyxz khlyxz = weiLianService.queryKhLyXzByLidOrLname(xyd.getXtdxz());
        //公司名称（性质）
        map.put("dwmc", xyd.getXdwmc() + (khlyxz != null ? "(" + khlyxz.getLname() + ")" : ""));
        TbKhlyxz tbKhlyxz = weiLianService.queryKhLyXzByLidOrLname(xyd.getXkhly());
        //客户来源
        map.put("khly", (tbKhlyxz != null ? tbKhlyxz.getLname() : ""));
        //租赁时间（进场时间与退场时间）
        map.put("zpTime", zpTime);
        //进场时间
        map.put("jxtime", "进场:" + DateFormatConfig.df1(xyd.getXjctime()));
        //退场时间
        map.put("tctime", "退场:" + DateFormatConfig.df1(xyd.getXtctime()));
        //人数
        map.put("rs", xyd.getXrs());
        //超出人数加收
        map.put("ccMoney", xyd.getXcudrfy());
        //支付宝订单号后六位
        String hlw = "";
        if (xyd.getXzzhlw() != null && !"".equals(xyd.getXzzhlw()) && xyd.getXzzhlw().length() > 0) {
            if (xyd.getXzzhlw().length() > 6) {
                hlw = xyd.getXzzhlw().substring(xyd.getXzzhlw().length() - 6);
            } else {
                hlw = xyd.getXzzhlw();
            }
        }
        //支付宝订单号后两位
        map.put("zfbddh", hlw);
        //支付宝转账定金时间
        map.put("zfbzzTime", xyd.getXzzsj() != null ? DateFormatConfig.df1(xyd.getXzzsj()) : "");
        //场地租赁费
        map.put("qkje", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0));
        //增值定金
        map.put("xyszzdj", (!xyd.getXyszzdj().isEmpty()) ? Double.parseDouble(xyd.getXyszzdj()) : 0);
        //增值费用补交
        map.put("xzzfybj", xyd.getXzzfybj() != null ? xyd.getXzzfybj() : 0);
        //真人CS(关闭)
//        map.put("csfyxm", xyd.getXzrcsfy() != null ? xyd.getXzrcsfy() : 0);
        //剧本杀
        map.put("xjbs", xyd.getXjbszje() != null ? xyd.getXjbszje() : 0);
        //已收场地费定金=已收定金+场地费租金补交-增值定金
        double ysdj = (xyd.getXysdj() != null ? xyd.getXysdj() : 0) + (xyd.getXcdzjbj() != null ? xyd.getXcdzjbj() : 0) - (xyd.getXyszzdj() != null && !"".equals(xyd.getXyszzdj()) ? Double.parseDouble(xyd.getXyszzdj()) : 0);
        if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(xyd.getXdjtype())) {
            Double xwsywdddsyje = xyd.getXwsywdddsyje();
            if (xwsywdddsyje != null) {
                ysdj += xwsywdddsyje;
            }
        }
        map.put("xyscdfdj", ysdj);
        //代码
        String dm = "";
        if (xyd.getXdm() != null && !"".equals(xyd.getXdm()) && xyd.getXdm().length() > 0) {
            if (xyd.getXdm().length() > 6) {
                dm = xyd.getXdm().substring(0, 6);
            } else {
                dm = xyd.getXdm();
            }
        }
        map.put("dm", dm);
        //付款码
        String fkm = "";
        if (xyd.getXfkm() != null && !"".equals(xyd.getXfkm()) && xyd.getXfkm().length() > 0) {
            if (xyd.getXfkm().length() > 2) {
                fkm = xyd.getXfkm().substring(0, 2);
            } else {
                fkm = xyd.getXfkm();
            }
        }
        map.put("zfbfkm", fkm);
        //订餐+烧烤
        map.put("dctczjsktczj", (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXskze() != null ? xyd.getXskze() : 0));
        map.put("xdcze", (xyd.getXdcze() != null ? xyd.getXdcze() : 0));
        map.put("xskze", (xyd.getXskze() != null ? xyd.getXskze() : 0));
        //保险
        map.put("xbxzje", xyd.getXbxzje() != null ? xyd.getXbxzje() : 0);
        //策划
        map.put("zttczj", xyd.getXztze() != null ? xyd.getXztze() : 0);
        //轰趴师
        map.put("sfhps", xyd.getXhpsfy() != null ? xyd.getXhpsfy() : 0);
        //全款金额
        map.put("qkje3", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) + v);
        //完成条件后优惠至
        map.put("qkje2", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) - (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) > 0 ? (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) - (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) : 0);
        //转发
        map.put("zgdst", xyd.getXzfts() != null ? xyd.getXzfts() : 0);
        //好评
        map.put("hpdst", xyd.getXhpts() != null ? xyd.getXhpts() : 0);
        //集赞
        map.put("jzst", xyd.getXjzts() != null ? xyd.getXjzts() : 0);
        //消费券
        map.put("xspyhq", xyd.getXspyhq() != null ? xyd.getXspyhq() : 0);
        //值班店长
        map.put("zbdzxm", dingdingEmployee != null ? dingdingEmployee.getName() : "");
        //值班店长电话
        map.put("zbdhxm", dingdingEmployee != null ? dingdingEmployee.getMobile() : "");
        //是否现场成交
        map.put("sfxccj", xyd.getXisxzcj() != null && xyd.getXisxzcj() == 0 ? "是" : "否");
        //是否泳池单
        map.put("sfycd", "否");
        //校代姓名
        map.put("xxdxm", xyd.getXxdxm());
        //赠送特权会员卡
        map.put("zstkhyk", xyd.getXzshyksl() != null ? xyd.getXzshyksl() : 0);
        //备注
        map.put("bzxx", xyd.getXsxbz());
        //甲方签名
        map.put("jfxm", xyd.getXisxzcj() != null && xyd.getXisxzcj() == 0 ? xyd.getXcjr() : xyd.getXfd());
        //乙方签名
        map.put("yfxm", xyd.getXzk());
        //日期
        map.put("rq", xyd.getXgsj() != null ? formatter1.format(xyd.getXgsj()) : formatter1.format(xyd.getXkhtjtime()));
        //别墅所在城市
        map.put("city", villa.getCity());
        //模板
        String tempName = FileConfig.getFileAbsolutePath2("static" + File.separator + "template.html");
        String context = PDFUtil.freeMarkerRender(map, tempName);
        String id = "DZHC" + DateFormatConfig.df2(new Date()) + IdConfig.uuId();
        String pdf = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
        String png = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".png");
        File newPdf = new File(pdf);

        Map<String, Object> map1 = new HashMap<>();
        try {
            // 生成pdf
            PDFUtil.createPdf(context, newPdf.getPath(),true);
            // 生成图片
            PDFUtil.pdfToImg(newPdf.getPath(), 1, png);
            log.info("*****进入xydSaveTask异步请求*生成图片成功******{}", newPdf.getPath());
            map1.put("png", id + ".png");
            // 删除pdf临时文件
            if (!newPdf.delete()) {
                log.warn("删除pdf临时文件报错: {}", newPdf.getPath());
            }
        } catch (Exception e) {
            log.error("生成PDF/png文件失败: ", e);
        }
        return map1;
    }

    /**
     * 修改表单实例
     *
     * @param id     要更新的表单数据ID
     * @param map    表单内容
     * @param userid 用户id
     */
    public GatewayResult xgBdSl(Map<String, Object> map, String id, String userid) {
        JSONObject json = new JSONObject(map);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = null;
        try {
            //修改表单实例
            gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, id, json.toJSONString());
        } catch (Exception e) {
            System.out.println("修改表单实例报错");
        }
        return gatewayResult;
    }

    /**
     * 电子协议单处理
     *
     * @param tbXyd 协议单对象
     * @return 协议单地址
     */
    public String dzXydCl(TbXyd tbXyd) throws Exception {
        Dzxyd dzxyd = dzxydService.queryByXid(tbXyd.getXid());
        String upload;
        if (dzxyd != null) {
            //电子协议单
            upload = dzxyd.getUrl();
        } else {
            String bddz = "D:\\tomcat9\\webapps\\weilian3\\";
            File file1 = new File(bddz + tbXyd.getXimagepath());
            //判断图片存在不存在,如果不存在将重新生成电子协议单
            if (!file1.exists()) {
                dzXyd(tbXyd);
            }
            File file = new File(bddz + tbXyd.getXimagepath());
            FileInputStream fileInputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
            fileInputStream.close();
            upload = ossFileService.upload(multipartFile);
            if (upload != null && !"".equals(upload)) {
                upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
            }
            Dzxyd dzxyd1 = new Dzxyd();
            dzxyd1.setUrl(upload);
            dzxyd1.setTip(IdConfig.uuId());
            dzxyd1.setXid(tbXyd.getXid());
            dzxyd1.setTime(new Date());
            dzxydService.save(dzxyd1);
        }
        return upload;
    }

    /**
     * 预留单
     */
    @RequestMapping("yldSaveTask")
    public void yldSaveTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        if (datas == null || "".equals(datas)) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        WlgbYlxd wlgbYlxd = JSONObject.toJavaObject(jsonObject, WlgbYlxd.class);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        if (wlgbYlxd.getYdjtp() != null && !"".equals(wlgbYlxd.getYdjtp())) {
            wlgbYlxd.setYdjtp(ydTpScJq(wlgbYlxd.getYdjtp()));
        }
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(wlgbYlxd.getXbsmc());
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        if (wlgbYlxd.getYdjtp() != null && !"".equals(wlgbYlxd.getYdjtp())) {
            GatewayResult fileUrl = new GatewayResult();
            try {
                fileUrl = YdConfig.getFileUrl(token, ydAppkey, wlgbYlxd.getYdjtp());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                String url1 = fileUrl.getResult();
                String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "xjtp.png");
                downloadPicture(url1, path);
                String tp1 = alyTp(path);
                wlgbYlxd.setYdjtp(tp1);
            }
        }
        wlgbYlxd.setId(IdConfig.uuId());
        wlgbYlxd.setXhfyj(wlgbYlxd.getXqkzj());
        wlgbYlxd.setXbdsl(0);
        wlgbYlxd.setXsksl(0);
        wlgbYlxd.setXztze(0);
        wlgbYlxd.setXxgorxt("0");
        wlgbYlxd.setXddtype("5");
        wlgbYlxd.setXsfhps(0);
        wlgbYlxd.setSftscc(0);
        wlgbYlxd.setXyxxt(1);
        wlgbYlxd.setXbjdj(0.0);
        wlgbYlxd.setSfsc(0);
        wlgbYlxd.setSlid(formInstId);
        wlgbYlxdService.save(wlgbYlxd);

        String context1 = "有新的预留单";
        context1 += "\n" + "订单编号：" + wlgbYlxd.getXddbh();
        context1 += "\n" + "别墅：" + wlgbYlxd.getXbsname();
        if (wlgbYlxd.getXcc() == 5) {
            context1 += "\n进场时间：" + DateFormatConfig.df1(wlgbYlxd.getXjctime());
            context1 += "\n退场时间：" + DateFormatConfig.df1(wlgbYlxd.getXtctime());
        } else {
            context1 += "\n进场时间：" + DateFormatConfig.df2(wlgbYlxd.getXjctime());
            context1 += "\n退场时间：" + DateFormatConfig.df2(wlgbYlxd.getXtctime());
        }
        if (wlgbYlxd.getXcc() == 1) {

            context1 += "\n场次：白场";
        }
        if (wlgbYlxd.getXcc() == 2) {
            context1 += "\n场次：晚场";
        }
        if (wlgbYlxd.getXcc() == 3) {
            context1 += "\n场次：全天1";
        }
        if (wlgbYlxd.getXcc() == 4) {
            context1 += "\n场次：全天2";
        }

        if (wlgbYlxd.getSfylj() != null) {
            context1 += "\n预留金/定金：" + wlgbYlxd.getXysdj();
        }
        context1 += "\n" + "全款：" + wlgbYlxd.getXqkzj();
        context1 += "\n" + "预留人：" + wlgbYlxd.getXfd();

        FwqQunid fwqQunid = weiLianService.queryQun(tbVilla.getBbcity());
        if (fwqQunid != null) {
            if (fwqQunid.getQcid() != null && !"".equals(fwqQunid.getQcid())) {
                try {
                    DingQunSend.send(context1, dingkey, fwqQunid.getQcid());
                } catch (ApiException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 预留单删除异步
     */
    @RequestMapping("ylScTask")
    public void ylScTask(HttpServletRequest request) {
        String xid = request.getParameter("xid");
        if (xid == null || "".equals(xid)) {
            return;
        }
        String scrid = request.getParameter("scrid");
        if (scrid == null || "".equals(scrid)) {
            return;
        }
        WlgbYlxd wlgbYlxd = wlgbYlxdService.queryByXid(xid);
        if (wlgbYlxd == null) {
            return;
        }
        WlgbYlxd wlgbYlxd1 = new WlgbYlxd();
        wlgbYlxd1.setSfsc(1);
        wlgbYlxd1.setScrid(scrid);
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(scrid);
        wlgbYlxd1.setScrname(dingdingEmployee.getName());
        wlgbYlxd1.setScitime(new Date());
        wlgbYlxd1.setId(wlgbYlxd.getId());
        wlgbYlxdService.updateById(wlgbYlxd1);
        List<WlgbJdDjbbd> list = wlgbJdDjbbdService.queryByLshAndSfScAndSfXd(wlgbYlxd.getXddbh(), 0);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        String finalToken = token;
        list.forEach(l -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("sfxyld", "0");
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = DingBdLcConfig.xgBdSl(finalToken, ydAppkey, scrid, l.getFormInstId(), jsonObject.toJSONString());
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println("删除预留恢复定金：" + gatewayResult);
        });
    }

    /**
     * 对账员提交异步
     */
    @RequestMapping("dzyTjQrdTask")
    public void dzyTjQrdTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        //实例ID
        String userid = request.getParameter("userid");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        log.info("**********dzyTjQrdTask接口的参数*********{}", jsonObject);
        TbQrd tbQrd = JSONObject.toJavaObject(jsonObject, TbQrd.class);
        TbQrd tbQrd1 = tbQrdService.queryQxydIdAndQsfSc(tbQrd.getQxydid());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        //确认完成转发证据图片截取地址
        if (tbQrd.getZfjzjt() != null && !"".equals(tbQrd.getZfjzjt()) && tbQrd.getZfjzjt().length() > 0) {
            tbQrd.setZfjzjt(ydTpScJq(tbQrd.getZfjzjt()));
        }
        //确认卫生费图片截取地址
        if (tbQrd.getWsjzjt() != null && !"".equals(tbQrd.getWsjzjt()) && tbQrd.getWsjzjt().length() > 0) {
            tbQrd.setWsjzjt(ydTpScJq(tbQrd.getWsjzjt()));
            GatewayResult fileUrl = new GatewayResult();
            try {
                fileUrl = YdConfig.getFileUrl(token, ydAppkey, tbQrd.getWsjzjt());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                String url1 = fileUrl.getResult();
                String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "qrwsf.png");
                downloadPicture(url1, path);
                String tp1 = alyTp(path);
                tbQrd.setWsjzjt(tp1);
            }
        }
        //确认赔偿费图片截取地址
        if (tbQrd.getPcjzjt() != null && !"".equals(tbQrd.getPcjzjt()) && tbQrd.getPcjzjt().length() > 0) {
            tbQrd.setPcjzjt(ydTpScJq(tbQrd.getPcjzjt()));
            GatewayResult fileUrl = new GatewayResult();
            try {
                fileUrl = YdConfig.getFileUrl(token, ydAppkey, tbQrd.getPcjzjt());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                String url1 = fileUrl.getResult();
                String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "qrpcf.png");
                downloadPicture(url1, path);
                String tp1 = alyTp(path);
                tbQrd.setPcjzjt(tp1);
            }
        }
        //对账订餐成本图片截取地址
        if (tbQrd.getQddcbF() != null && !"".equals(tbQrd.getQddcbF()) && tbQrd.getQddcbF().length() > 0) {
            tbQrd.setQddcbF(ydTpScJq(tbQrd.getQddcbF()));
        }
        //对账烧烤成本图片截取地址
        if (tbQrd.getQskcbF() != null && !"".equals(tbQrd.getQskcbF()) && tbQrd.getQskcbF().length() > 0) {
            tbQrd.setQskcbF(ydTpScJq(tbQrd.getQskcbF()));
        }
        //转账金额截图1截取地址
        if (tbQrd.getZztp1() != null && !"".equals(tbQrd.getZztp1()) && tbQrd.getZztp1().length() > 0) {
            tbQrd.setZztp1(ydTpScJq(tbQrd.getZztp1()));
            GatewayResult fileUrl = new GatewayResult();
            try {
                fileUrl = YdConfig.getFileUrl(token, ydAppkey, tbQrd.getZztp1());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                String url1 = fileUrl.getResult();
                String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "zztp1.png");
                downloadPicture(url1, path);
                String tp1 = alyTp(path);
                tbQrd.setZztp1(tp1);
            }
        }
        //转账金额截图2截取地址
        if (tbQrd.getZztp2() != null && !"".equals(tbQrd.getZztp2()) && tbQrd.getZztp2().length() > 0) {
            tbQrd.setZztp2(ydTpScJq(tbQrd.getZztp2()));
            GatewayResult fileUrl = new GatewayResult();
            try {
                fileUrl = YdConfig.getFileUrl(token, ydAppkey, tbQrd.getZztp2());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                String url1 = fileUrl.getResult();
                String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "zztp2.png");
                downloadPicture(url1, path);
                String tp1 = alyTp(path);
                tbQrd.setZztp2(tp1);
            }
        }
        //消费商品图片截取地址
        if (tbQrd.getQspqrdF() != null && !"".equals(tbQrd.getQspqrdF()) && tbQrd.getQspqrdF().length() > 0) {
            tbQrd.setQspqrdF(ydTpScJq(tbQrd.getQspqrdF()));
        }
        //消费商品图片1截取地址
        if (tbQrd.getQspqrdF1() != null && !"".equals(tbQrd.getQspqrdF1()) && tbQrd.getQspqrdF1().length() > 0) {
            tbQrd.setQspqrdF1(ydTpScJq(tbQrd.getQspqrdF1()));
        }
        //消费商品图片2截取地址
        if (tbQrd.getQspqrdF2() != null && !"".equals(tbQrd.getQspqrdF2()) && tbQrd.getQspqrdF2().length() > 0) {
            tbQrd.setQspqrdF2(ydTpScJq(tbQrd.getQspqrdF2()));
        }
        //对账订餐成本图片截取地址
        if (tbQrd.getQddcbF2() != null && !"".equals(tbQrd.getQddcbF2()) && tbQrd.getQddcbF2().length() > 0) {
            tbQrd.setQddcbF2(ydTpScJq(tbQrd.getQddcbF2()));
        }
        //对账订餐成本图片截取地址
        if (tbQrd.getQddcbF3() != null && !"".equals(tbQrd.getQddcbF3()) && tbQrd.getQddcbF3().length() > 0) {
            tbQrd.setQddcbF3(ydTpScJq(tbQrd.getQddcbF3()));
        }
        //对账烧烤成本图片截取地址
        if (tbQrd.getQskcbF2() != null && !"".equals(tbQrd.getQskcbF2()) && tbQrd.getQskcbF2().length() > 0) {
            tbQrd.setQskcbF2(ydTpScJq(tbQrd.getQskcbF2()));
        }
        //对账烧烤成本图片截取地址
        if (tbQrd.getQskcbF3() != null && !"".equals(tbQrd.getQskcbF3()) && tbQrd.getQskcbF3().length() > 0) {
            tbQrd.setQskcbF3(ydTpScJq(tbQrd.getQskcbF3()));
        }
        //自助餐成本图片1
        if (tbQrd.getQzzccbtp1() != null && !"".equals(tbQrd.getQzzccbtp1()) && tbQrd.getQzzccbtp1().length() > 0) {
            tbQrd.setQzzccbtp1(ydTpScJq(tbQrd.getQzzccbtp1()));
        }
        //自助餐成本图片2
        if (tbQrd.getQzzccbtp2() != null && !"".equals(tbQrd.getQzzccbtp2()) && tbQrd.getQzzccbtp2().length() > 0) {
            tbQrd.setQzzccbtp2(ydTpScJq(tbQrd.getQzzccbtp2()));
        }
        //策划外包成本图片1
        if (tbQrd.getQchwbcbtp1() != null && !"".equals(tbQrd.getQchwbcbtp1()) && tbQrd.getQchwbcbtp1().length() > 0) {
            tbQrd.setQchwbcbtp1(ydTpScJq(tbQrd.getQchwbcbtp1()));
        }
        //策划外包成本图片2
        if (tbQrd.getQchwbcbtp2() != null && !"".equals(tbQrd.getQchwbcbtp2()) && tbQrd.getQchwbcbtp2().length() > 0) {
            tbQrd.setQchwbcbtp2(ydTpScJq(tbQrd.getQchwbcbtp2()));
        }
        //代购成本单
        if (tbQrd.getQdgcbd() != null && !"".equals(tbQrd.getQdgcbd()) && tbQrd.getQdgcbd().length() > 0) {
            tbQrd.setQdgcbd(ydTpScJq(tbQrd.getQdgcbd()));
        }
        //代购付款截图
        if (tbQrd.getQdgfkjt() != null && !"".equals(tbQrd.getQdgfkjt()) && tbQrd.getQdgfkjt().length() > 0) {
            tbQrd.setQdgfkjt(ydTpScJq(tbQrd.getQdgfkjt()));
            GatewayResult fileUrl = new GatewayResult();
            try {
                fileUrl = YdConfig.getFileUrl(token, ydAppkey, tbQrd.getQdgfkjt());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                String url1 = fileUrl.getResult();
                String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "dgfk.png");
                downloadPicture(url1, path);
                String tp1 = alyTp(path);
                tbQrd.setQdgfkjt(tp1);
            }
        }
        //实际进场时间处理
        if (tbQrd.getQjctime() != null && !"".equals(tbQrd.getQjctime()) && tbQrd.getQjctime().length() > 0) {
            Calendar c = Calendar.getInstance();
            try {
                c.setTimeInMillis(Long.parseLong(tbQrd.getQjctime()));
                int day = c.get(Calendar.HOUR_OF_DAY);
                int min = c.get(Calendar.MINUTE);
                tbQrd.setQjctime(day + "");
                tbQrd.setQjcmin(min + "");
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        //实际退场时间处理
        if (tbQrd.getQtctime() != null && !"".equals(tbQrd.getQtctime()) && tbQrd.getQtctime().length() > 0) {
            Calendar c = Calendar.getInstance();
            try {
                c.setTimeInMillis(Long.parseLong(tbQrd.getQtctime()));
                int day = c.get(Calendar.HOUR_OF_DAY);
                int min = c.get(Calendar.MINUTE);
                tbQrd.setQtctime(day + "");
                tbQrd.setQtcmin(min + "");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        //延时费归属人
        if (tbQrd.getQysfgsr1() != null && !"".equals(tbQrd.getQysfgsr1()) && tbQrd.getQysfgsr1().length() > 0) {
            tbQrd.setQysfgsr1(xzjq(tbQrd.getQysfgsr1()));
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbQrd.getQysfgsr1());
            if (employee != null) {
                tbQrd.setQysfgsr1(employee.getName());
            }
        }
        //延时费归属人
        if (tbQrd.getQysfgsr2() != null && !"".equals(tbQrd.getQysfgsr2()) && tbQrd.getQysfgsr2().length() > 0) {
            tbQrd.setQysfgsr2(xzjq(tbQrd.getQysfgsr2()));
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbQrd.getQysfgsr2());
            if (employee != null) {
                tbQrd.setQysfgsr2(employee.getName());
            }
        }
        TbXyd xyd = tbXydService.queryById(tbQrd.getQxydid());
        //值班店长
        if (tbQrd.getQzbdz() != null && !"".equals(tbQrd.getQzbdz()) && tbQrd.getQzbdz().length() > 0) {
            tbQrd.setQzbdz(xzjq(tbQrd.getQzbdz()));
            if (tbQrd.getQzbdz() != null) {
                DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbQrd.getQzbdz());
                if (employee != null) {
                    tbQrd.setQzbdz(employee.getName());
                }
            } else {
                if (xyd != null) {
                    TbVilla villa = weiLianDdXcxService.queryTbVillaById(xyd.getXbsmc());
                    if (villa != null) {
                        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
                        if (employee != null) {
                            tbQrd.setQzbdz(employee.getName());
                        }
                    }
                }
            }
        } else {
            if (xyd != null) {
                TbVilla villa = weiLianDdXcxService.queryTbVillaById(xyd.getXbsmc());
                if (villa != null) {
                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
                    if (employee != null) {
                        tbQrd.setQzbdz(employee.getName());
                    }
                }
            }
        }
        //用电度数
        Double j = 0.0;
        Double t = 0.0;
        if (tbQrd.getQrcdds() != null) {
            j = tbQrd.getQrcdds();
        }
        if (tbQrd.getQtcdds() != null) {
            t = tbQrd.getQtcdds();
        }
        Double sum = 0.0;
        if (j > t) {
            sum = j - t;
        } else {
            sum = t - j;
        }
        BigDecimal bd = new BigDecimal(sum);
        //用电度数留取小数点后两位
        sum = bd.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        //用电度数
        tbQrd.setQydds(sum);
        if ((tbQrd.getDzysfsh() != null && !"".equals(tbQrd.getDzysfsh())) && "1".equals(tbQrd.getDzysfsh())) {
            tbQrd.setSfwcdz("1");
        }
        if (tbQrd1 != null) {
            if (!"1".equals(tbQrd1.getSfwcdz())) {
                if ("1".equals(tbQrd.getSfwcdz())) {
                    tbQrd.setSfdyjk(1);
                }
            }
        } else {
            if ("1".equals(tbQrd.getSfwcdz())) {
                tbQrd.setSfdyjk(1);
            }
        }
        String finalToken = token;
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        if (tbQrd1 != null) {
//            //确认时间（最开始没有就给，有就忽略）
//            if (tbQrd1.getQrsj() == null) {
//                tbQrd.setQrsj(new Date());
//            }
            //提交用户
            if (tbQrd1.getQsendder() == null || "".equals(tbQrd1.getQsendder())) {
                tbQrd.setQsendder(userid);
            }
            if ((tbQrd.getDzysfsh() != null && !"".equals(tbQrd.getDzysfsh())) && "1".equals(tbQrd.getDzysfsh())) {
                //审核时间
                if (tbQrd1.getQshsj() == null) {
                    tbQrd.setQshsj(new Date());
                }
                //确认时间
                if (tbQrd1.getQrsj() == null) {
                    tbQrd.setQrsj(new Date());
                    List<WlgbJdDjbbd> list = wlgbJdDjbbdService.queryByDdBhAndSfSc(xyd.getXddbh());
                    list.forEach(l -> {
                        JSONObject jsonObject1 = new JSONObject();
                        jsonObject1.put("sfxd", 3);
                        for (int i = 0; i < 5; i++) {
                            GatewayResult gatewayResult = null;
                            try {
                                gatewayResult = DingBdLcConfig.xgBdSl(finalToken, ydAppkey, userid, l.getFormInstId(), jsonObject1.toJSONString());
                            } catch (Exception e) {
                                gatewayResult = new GatewayResult();
                                e.printStackTrace();
                            }
                            if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                                if (i == 4) {
                                    try {
                                        DingQunSend.send((ding != null ? ding.getName() : "对账") + "正在修改定金消费完状态，更新表单出错了，订单编号：" + xyd.getXddbh() + "，定金流水号：" + l.getLsh() + "，错误原因：" + gatewayResult.toString(), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                    } catch (ApiException e) {
                                        e.printStackTrace();
                                    }
                                }
                            } else {
                                break;
                            }
                        }
                    });

                    TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(xyd.getXid());
                    if (tbYddXyd != null) {
                        TbYddXyd tbYddXyd1 = new TbYddXyd();
                        tbYddXyd1.setXid(tbYddXyd.getXid());
                        tbYddXyd1.setXstatu("已结算");
                        tbYddXydService.updateById(tbYddXyd1);

                        String post = null;
                        try {
                            Map<String, String> map = new HashMap<>();
                            map.put("ddbh", tbYddXyd.getXddbh());
                            map.put("zt", tbYddXyd1.getXstatu());
                            //总价
                            map.put("zj", tbYddXyd.getXhfyj() + tbYddXyd.getXztze() + tbYddXyd.getXdcze() + tbYddXyd.getXhpsfy() + tbYddXyd.getXbxzje() + "");
                            post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/b2e50678-62d2-4d65-a042-27427265a26f", map);
                        } catch (Exception e) {
                            e.printStackTrace();
                            try {
                                String context1 = "对账员审核将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，错误原因：" + e;
                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                            } catch (ApiException e1) {
                                e1.printStackTrace();
                            }
                        }
                        try {
                            if (post != null && !"".equals(post)) {
                                JSONObject jsonObject1 = JSONObject.parseObject(post);
                                String jg = jsonObject1.getString("jg");
                                if (!"ok".equalsIgnoreCase(jg)) {
                                    try {
                                        String context1 = "对账员审核将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post;
                                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                    } catch (ApiException e1) {
                                        e1.printStackTrace();
                                    }
                                }
                            }
                        } catch (Exception e) {
                            try {
                                String context1 = "对账员审核将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                            } catch (ApiException e1) {
                                e1.printStackTrace();
                            }
                            e.printStackTrace();
                        }
                    }
                }
                //审核人
                if (tbQrd1.getQshr() == null || "".equals(tbQrd1.getQshr())) {
                    tbQrd.setQshr(userid);
                }
                //审核状态
                if (tbQrd1.getQissh() == null || tbQrd1.getQissh() == 0) {
                    tbQrd.setQissh(1);
                }
                //协议单的是否对账
                if ((tbQrd1.getDzysfsh() != null && !"".equals(tbQrd1.getDzysfsh())) && "0".equals(tbQrd1.getDzysfsh())) {
                    TbXyd tbXyd = new TbXyd();
                    tbXyd.setXid(tbQrd.getQxydid());
                    tbXyd.setXstatu(1);
                    tbXydService.updateById(tbXyd);
                }
            }
            //发送回访待办（且在测试名单内的）
            if ((tbQrd.getSfwcdz() != null && !"".equals(tbQrd.getSfwcdz())) && "1".equals(tbQrd.getSfwcdz())) {
                if (!"1".equals(tbQrd1.getSfwcdz())) {
                    TbVilla villa = weiLianDdXcxService.queryTbVillaById(xyd.getXbsmc());
                    if (villa != null) {
                        Integer count = weiLianDdXcxService.queryByXidDzJlCount(xyd.getXid());
                        if (count == 0) {
                            //回访登记
                            WlgbQrdDzjl wlgbQrdDzjl = new WlgbQrdDzjl();
                            wlgbQrdDzjl.setTime(new Date());
                            wlgbQrdDzjl.setXid(xyd.getXid());
                            wlgbQrdDzjl.setSfcl("0");
                            weiLianDdXcxService.insertDzHfJl(wlgbQrdDzjl);
                        }
                    }
                }
            }
            tbQrd.setQid(tbQrd1.getQid());
            tbQrdService.updateById(tbQrd);
        } else {
            tbQrd.setQsfsc("0");
            if ((tbQrd.getDzysfsh() != null && !"".equals(tbQrd.getDzysfsh())) && "1".equals(tbQrd.getDzysfsh())) {
                tbQrd.setQshsj(new Date());
                tbQrd.setQrsj(new Date());
                List<WlgbJdDjbbd> list = wlgbJdDjbbdService.queryByDdBhAndSfSc(xyd.getXddbh());
                list.forEach(l -> {
                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("sfxd", 3);
                    for (int i = 0; i < 5; i++) {
                        GatewayResult gatewayResult = null;
                        try {
                            gatewayResult = DingBdLcConfig.xgBdSl(finalToken, ydAppkey, userid, l.getFormInstId(), jsonObject1.toJSONString());
                        } catch (Exception e) {
                            gatewayResult = new GatewayResult();
                            e.printStackTrace();
                        }
                        if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                            if (i == 4) {
                                try {
                                    DingQunSend.send((ding != null ? ding.getName() : "对账") + "正在修改定金消费完状态，更新表单出错了，订单编号：" + xyd.getXddbh() + "，定金流水号：" + l.getLsh() + "，错误原因：" + gatewayResult.toString(), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                } catch (ApiException e) {
                                    e.printStackTrace();
                                }
                            }
                        } else {
                            break;
                        }
                    }
                });
                tbQrd.setQshr(userid);
                tbQrd.setQissh(1);
                TbXyd tbXyd = new TbXyd();
                tbXyd.setXid(tbQrd.getQxydid());
                tbXyd.setXstatu(1);
                tbXydService.updateById(tbXyd);
            }
            if ((tbQrd.getSfwcdz() != null && !"".equals(tbQrd.getSfwcdz())) && "1".equals(tbQrd.getSfwcdz())) {
                TbVilla villa = weiLianDdXcxService.queryTbVillaById(xyd.getXbsmc());
                if (villa != null) {
                    Integer count = weiLianDdXcxService.queryByXidDzJlCount(xyd.getXid());
                    if (count == 0) {
                        //回访登记
                        WlgbQrdDzjl wlgbQrdDzjl = new WlgbQrdDzjl();
                        wlgbQrdDzjl.setTime(new Date());
                        wlgbQrdDzjl.setXid(xyd.getXid());
                        wlgbQrdDzjl.setSfcl("0");
                        weiLianDdXcxService.insertDzHfJl(wlgbQrdDzjl);
                    }
                }
            }
            tbQrd.setQsendder(userid);
            tbQrd.setQid(IdConfig.uuId());
            tbQrdService.save(tbQrd);
        }
        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXltext("对账员提交确认单");
        wlgbXydLog.setXlxid(tbQrd.getQxydid());
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXltime(new Date());
        wlgbXydLogService.save(wlgbXydLog);
        //判断是否完成对账，如果已经完成对账就不需要调用宜搭修改对账表单
        if (tbQrd1 != null) {
            if ((tbQrd1.getSfwcdz() != null && !"".equals(tbQrd1.getSfwcdz())) && !"1".equals(tbQrd1.getSfwcdz())) {
                if ((tbQrd.getDzysfsh() != null && !"".equals(tbQrd.getDzysfsh())) && "1".equals(tbQrd.getDzysfsh())) {
                    WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(tbQrd.getQxydid(), "对账表单提交");
                    if (wlgbBdjl != null) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("sfwcdz", 1);
                        xgBdSl(map, wlgbBdjl.getSlid(), userid);
                    }
                }
            }
        } else {
            if ((tbQrd.getDzysfsh() != null && !"".equals(tbQrd.getDzysfsh())) && "1".equals(tbQrd.getDzysfsh())) {
                WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(tbQrd.getQxydid(), "对账表单提交");
                if (wlgbBdjl != null) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("sfwcdz", 1);
                    //容错机制修改表单内容
                    JSONObject json = new JSONObject(map);
                    GatewayResult gatewayResult = null;
                    try {
                        gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, wlgbBdjl.getSlid(), json.toJSONString());
                    } catch (Exception e) {
                        gatewayResult = new GatewayResult();
                        e.printStackTrace();
                    }
                }
            }
        }
        //轰趴师姓名
        String xhpsxm = jsonObject.getString("xhpsxm");
        //轰趴师费用
        Double xhpsfy = jsonObject.getDouble("xhpsfy");
        //包/订餐金额
        Double xbdje = jsonObject.getDouble("xbdje");
        //包/订餐数量
        Double xbdsl = jsonObject.getDouble("xbdsl");
        //烧烤金额
        Double xskje = jsonObject.getDouble("xskje");
        //烧烤数量
        Double xsksl = jsonObject.getDouble("xsksl");
        //保险总金额
        Double xbxzje = jsonObject.getDouble("xbxzje");
        //保险人数
        Double xbxrs = jsonObject.getDouble("xbxrs");
        //保险单价
        Double xbxdj = jsonObject.getDouble("xbxdj");
        //轰趴师支出
        Double xhpszcgz = jsonObject.getDouble("xhpszcgz");
        //轰趴师其他支出
        Double xhpszcqt = jsonObject.getDouble("xhpszcqt");
        //保险支出
        Double xbxzc = jsonObject.getDouble("xbxzc");


        TbXyd tbXyd = new TbXyd();
        tbXyd.setXid(xyd.getXid());
        Boolean c = false;
        //轰趴师工资
        if ((xhpszcgz != null ? xhpszcgz : 0) != (xyd.getXhpszcgz() != null ? xyd.getXhpszcgz() : 0)) {
            tbXyd.setXhpszcgz(xhpszcgz != null ? xhpszcgz : 0);
            c = true;
        }
        //轰趴师其他费用
        if ((xhpszcqt != null ? xhpszcqt : 0) != (xyd.getXhpszcqt() != null ? xyd.getXhpszcqt() : 0)) {
            tbXyd.setXhpszcqt(xhpszcqt != null ? xhpszcqt : 0);
            c = true;
        }
        //保险支出
        if ((xbxzc != null ? xbxzc : 0) != (xyd.getXbxzc() != null ? xyd.getXbxzc() : 0)) {
            tbXyd.setXbxzc(xbxzc != null ? xbxzc : 0);
            c = true;
        }
        //才有b去判断是否需要调用协议单内容修改接口（以免接口循环被调用）
        Boolean b = false;
        Map<String, Object> map = new HashMap<>();

        //是否包餐
        String xsfbc = jsonObject.getString("xsfbc");
        Double xcf = jsonObject.getDouble("xcf");
        String xsfbc1 = xsfbc != null && !"".equals(xsfbc) ? xsfbc : "0";
        double xcf1 = xcf != null ? xcf : 0;
        map.put("xsfbc", xsfbc1);
        if (!(xyd.getXsfbc() != null && !"".equals(xyd.getXsfbc()) ? xyd.getXsfbc() : "0").equals(xsfbc1)) {
            if ("2".equals(xsfbc1)) {
                map.put("xcf", xcf1);
                tbXyd.setXcf(xcf1);
            } else {
                map.put("xcf", 0);
                tbXyd.setXcf(0.0);
            }
            tbXyd.setXsfbc(xsfbc1);
            c = true;
            b = true;
        } else {
            if ("2".equals(xsfbc1)) {
                if (xcf1 != (xyd.getXcf() != null ? xyd.getXcf() : 0)) {
                    map.put("xcf", xcf1);
                    tbXyd.setXcf(xcf1);
                    c = true;
                    b = true;
                }
            }
        }
        //是否包策划
        String xsfbch = jsonObject.getString("xsfbch");
        Double xchje = jsonObject.getDouble("xchje");
        String xsfbch1 = xsfbch != null && !"".equals(xsfbch) ? xsfbch : "0";
        double xchje1 = xchje != null ? xchje : 0;
        map.put("xsfbch", xsfbch1);
        if (!(xyd.getXsfbch() != null && !"".equals(xyd.getXsfbch()) ? xyd.getXsfbch() : "0").equals(xsfbch1)) {
            if ("2".equals(xsfbc1)) {
                map.put("xchje", xchje1);
                tbXyd.setXchje(xchje1);
            } else {
                map.put("xchje", 0);
                tbXyd.setXchje(0.0);
            }
            tbXyd.setXsfbch(xsfbch1);
            c = true;
            b = true;
        } else {
            if ("2".equals(xsfbch1)) {
                if (xchje1 != (xyd.getXchje() != null ? xyd.getXchje() : 0)) {
                    map.put("xchje", xchje1);
                    tbXyd.setXchje(xchje1);
                    c = true;
                    b = true;
                }
            }
        }
        //是否包电费
        String xsfbdf = jsonObject.getString("xsfbdf");
        Double xdfje = jsonObject.getDouble("xdfje");
        String xsfbdf1 = xsfbdf != null && !"".equals(xsfbdf) ? xsfbdf : "0";
        double xdfje1 = xdfje != null ? xdfje : 0;
        map.put("xsfbdf", xsfbdf1);
        if (!(xyd.getXsfbdf() != null && !"".equals(xyd.getXsfbdf()) ? xyd.getXsfbdf() : "0").equals(xsfbdf1)) {
            if ("2".equals(xsfbdf1)) {
                map.put("xdfje", xdfje1);
                tbXyd.setXdfje(xdfje1);
            } else {
                map.put("xdfje", 0);
                tbXyd.setXdfje(0.0);
            }
            tbXyd.setXsfbdf(xsfbdf1);
            c = true;
            b = true;
        } else {
            if ("2".equals(xsfbdf1)) {
                if (xdfje1 != (xyd.getXdfje() != null ? xyd.getXdfje() : 0)) {
                    map.put("xdfje", xdfje1);
                    tbXyd.setXdfje(xdfje1);
                    c = true;
                    b = true;
                }
            }
        }
        //是否包车
        String xsfbcc = jsonObject.getString("xsfbcc");
        Double xbcje = jsonObject.getDouble("xbcje");
        String xsfbcc1 = xsfbcc != null && !"".equals(xsfbcc) ? xsfbcc : "0";
        double xbcje1 = xbcje != null ? xbcje : 0;
        map.put("xsfbcc", xsfbcc1);
        if (!(xyd.getXsfbcc() != null && !"".equals(xyd.getXsfbcc()) ? xyd.getXsfbcc() : "0").equals(xsfbcc1)) {
            if ("2".equals(xsfbcc1)) {
                map.put("xbcje", xbcje1);
                tbXyd.setXbcje(xbcje1);
            } else {
                tbXyd.setXbcje(0.0);
                map.put("xbcje", 0);
            }
            tbXyd.setXsfbcc(xsfbcc1);
            c = true;
            b = true;
        } else {
            if ("2".equals(xsfbcc1)) {
                if (xbcje1 != (xyd.getXbcje() != null ? xyd.getXbcje() : 0)) {
                    map.put("xbcje", xbcje1);
                    tbXyd.setXbcje(xbcje1);
                    c = true;
                    b = true;
                }
            }
        }
        //是否包卫生费
        String xsfbwsf = jsonObject.getString("xsfbwsf");
        Double xwsfje = jsonObject.getDouble("xwsfje");
        String xsfbwsf1 = xsfbwsf != null && !"".equals(xsfbwsf) ? xsfbwsf : "0";
        double xwsfje1 = xwsfje != null ? xwsfje : 0;
        map.put("xsfbwsf", xsfbwsf1);
        if (!(xyd.getXsfbwsf() != null && !"".equals(xyd.getXsfbwsf()) ? xyd.getXsfbwsf() : "0").equals(xsfbwsf1)) {
            if ("2".equals(xsfbwsf1)) {
                map.put("xwsfje", xwsfje1);
                tbXyd.setXwsfje(xwsfje1);
            } else {
                tbXyd.setXwsfje(0.0);
                map.put("xwsfje", 0);
            }
            tbXyd.setXsfbwsf(xsfbwsf1);
            c = true;
            b = true;
        } else {
            if ("2".equals(xsfbwsf1)) {
                if (xwsfje1 != (xyd.getXwsfje() != null ? xyd.getXwsfje() : 0)) {
                    map.put("xwsfje", xwsfje1);
                    tbXyd.setXwsfje(xwsfje1);
                    c = true;
                    b = true;
                }
            }
        }
        //是否包厨房使用费
        String xsfbcfsyf = jsonObject.getString("xsfbcfsyf");
        Double xcfsyfje = jsonObject.getDouble("xcfsyfje");
        String xsfbcfsyf1 = xsfbcfsyf != null && !"".equals(xsfbcfsyf) ? xsfbcfsyf : "0";
        double xcfsyfje1 = xcfsyfje != null ? xcfsyfje : 0;
        map.put("xsfbcfsyf", xsfbcfsyf1);
        if (!(xyd.getXsfbcfsyf() != null && !"".equals(xyd.getXsfbcfsyf()) ? xyd.getXsfbcfsyf() : "0").equals(xsfbcfsyf1)) {
            if ("2".equals(xsfbcfsyf1)) {
                map.put("xcfsyfje", xcfsyfje1);
                tbXyd.setXcfsyfje(xcfsyfje1);
            } else {
                tbXyd.setXcfsyfje(0.0);
                map.put("xcfsyfje", 0);
            }
            tbXyd.setXsfbcfsyf(xsfbcfsyf1);
            c = true;
            b = true;
        } else {
            if ("2".equals(xsfbcfsyf1)) {
                if (xcfsyfje1 != (xyd.getXcfsyfje() != null ? xyd.getXcfsyfje() : 0)) {
                    map.put("xcfsyfje", xcfsyfje1);
                    tbXyd.setXcfsyfje(xcfsyfje1);
                    c = true;
                    b = true;
                }
            }
        }

        //剧本杀总金额
        Double xjbszje = jsonObject.getDouble("xjbszje");
        //剧本杀执行人
        String xjbszxr = jsonObject.getString("xjbszxr");

        DingdingEmployee ding1 = weiLianService.queryDingDingByName(xyd.getXfd());
        //剧本杀执行人
        xjbszxr = xjbszxr != null ? !"".equals(xjbszxr) ? xjbszxr : "" : "";
        //剧本杀执行人
        map.put("xjbszxr", xjbszxr);
        if (!(xjbszxr).equals((xyd.getXjbszxr() != null ? !"".equals(xyd.getXjbszxr()) ? xyd.getXjbszxr() : "" : ""))) {
            b = true;
        }
        //剧本杀
        map.put("xjbszje", xjbszje != null ? xjbszje : 0);
        if ((xjbszje != null ? xjbszje : 0) != (xyd.getXjbszje() != null ? xyd.getXjbszje() : 0)) {
            if ((xjbszje != null ? xjbszje : 0) > 0) {
                map.put("xsfxyjbs", 1);
                if (xyd.getXjbscjr() == null || "".equals(xyd.getXjbscjr())) {
                    map.put("xjbscjr", ding1 != null ? ding1.getUserid() : "");
                }
            } else {
                map.put("xjbscjr", "");
                map.put("xjbszxr", "");
            }
            b = true;
        }
        xhpsxm = xhpsxm != null ? !"".equals(xhpsxm) ? xhpsxm : "" : "";
        //轰趴师姓名
        map.put("xhpsxm", xhpsxm);
        if (!(xhpsxm).equals((xyd.getXhpsxm() != null ? !"".equals(xyd.getXhpsxm()) ? xyd.getXhpsxm() : "" : ""))) {
            b = true;
        }
        //轰趴师费用
        map.put("xhpsfy", xhpsfy != null ? xhpsfy : 0);
        if ((xyd.getXhpsfy() != null ? xyd.getXhpsfy() : 0) != (xhpsfy != null ? xhpsfy : 0)) {
            //发确认单需要
            tbXyd.setXhpsfy(xhpsfy != null ? xhpsfy : 0);
            c = true;

            //判断是否需要轰趴师
            if ((xhpsfy != null ? xhpsfy : 0) > 0) {
                map.put("xsfhps", 1);
                if (xyd.getXhpscjr() == null || "".equals(xyd.getXhpscjr())) {
                    //成交人赋值默认给房东
                    map.put("xhpscjr", ding1 != null ? ding1.getUserid() : "");
                }
            } else {
                //不需要则把值清空
                map.put("xsfhps", 0);
                map.put("xhpsfy", 0);
                map.put("xhpscjr", "");
                map.put("xhpsfymx", "");
            }
            b = true;
        }
        if (c) {
            tbXydService.updateById(tbXyd);
        }
        //保险人数
        map.put("xbxrs", xbxrs != null ? xbxrs : 0);
        if ((xyd.getXbxrs() != null ? xyd.getXbxrs() : 0) != (xbxrs != null ? xbxrs : 0)) {
            b = true;
        }
        //保险单价
        map.put("xbxdj", xbxdj != null ? xbxdj : 0);
        if ((xyd.getXbxdj() != null ? xyd.getXbxdj() : 0) != (xbxdj != null ? xbxdj : 0)) {
            b = true;
        }
        //保险总金额
        map.put("xbxzje", xbxzje != null ? xbxzje : 0);
        if ((xyd.getXbxzje() != null ? xyd.getXbxzje() : 0) != (xbxzje != null ? xbxzje : 0)) {
            if ((xbxzje != null ? xbxzje : 0) > 0) {
                //判断是否需要保险
                map.put("xsfbx", 1);
                if (xyd.getXbxcjr() == null || "".equals(xyd.getXbxcjr())) {
                    //成交人默认给房东
                    map.put("xbxcjr", ding != null ? ding1.getUserid() : "");
                }
            } else {
                //不需要把原有的值清空
                map.put("xsfbx", 0);
                map.put("xbxrs", 0);
                map.put("xbxdj", 0);
                map.put("xbxcjr", "");
            }
            b = true;
        }
        //包/订餐金额
        map.put("xbdje", xbdje != null ? xbdje : 0);
        if ((xyd.getXbdje() != null ? xyd.getXbdje() : 0) != (xbdje != null ? xbdje : 0)) {
            b = true;
        }
        //包/订餐数量
        map.put("xbdsl", xbdsl != null ? xbdsl : 0);
        if ((xyd.getXbdsl() != null ? xyd.getXbdsl() : 0) != (xbdsl != null ? xbdsl : 0)) {
            b = true;
        }
        //烧烤金额
        map.put("xskje", xskje != null ? xskje : 0);
        if ((xyd.getXskje() != null ? xyd.getXskje() : 0) != (xskje != null ? xskje : 0)) {
            b = true;
        }
        //烧烤数量
        map.put("xsksl", xsksl != null ? xsksl : 0);
        if ((xyd.getXsksl() != null ? xyd.getXsksl() : 0) != (xsksl != null ? xsksl : 0)) {
            b = true;
        }

        //订餐总额
        map.put("xdcze", tbQrd.getQdcxf() != null ? tbQrd.getQdcxf() : 0);
        if ((xyd.getXdcze() != null ? xyd.getXdcze() : 0) != (tbQrd.getQdcxf() != null ? tbQrd.getQdcxf() : 0)) {
            if ((tbQrd.getQdcxf() != null ? tbQrd.getQdcxf() : 0) > 0) {
                //是否需要订餐
                map.put("xsfdc", 1);
                if (xyd.getXdccjr() == null || "".equals(xyd.getXdccjr())) {
                    //成交人默认房东
                    map.put("xdccjr", ding1 != null ? ding1.getUserid() : "");
                }
            } else {
                //不需要把值清空
                map.put("xsfdc", 0);
                map.put("xdcze", 0);
                map.put("xbdje", 0);
                map.put("xbdsl", 0);
                map.put("xdccjr", "");
                map.put("xdcfzcjr", "");
            }
            b = true;
        }
        //烧烤总额
        map.put("xskze", tbQrd.getQsktc() != null ? tbQrd.getQsktc() : 0);
        if ((xyd.getXskze() != null ? xyd.getXskze() : 0) != (tbQrd.getQsktc() != null ? tbQrd.getQsktc() : 0)) {
            if ((tbQrd.getQsktc() != null ? tbQrd.getQsktc() : 0) > 0) {
                //是否需要烧烤
                map.put("xsfsk", 1);
                if (xyd.getXskcjr() == null || "".equals(xyd.getXskcjr())) {
                    //成交人默认房东
                    map.put("xdccjr", ding1 != null ? ding1.getUserid() : "");
                }
            } else {
                //不需要则把值清空
                map.put("xsfsk", 0);
                map.put("xskje", 0);
                map.put("xsksl", 0);
                map.put("xskze", 0);
                map.put("xskcjr", "");
                map.put("xskfzcjr", "");
            }
            b = true;
        }
        //策划
        map.put("xztze", tbQrd.getQchfw() != null ? tbQrd.getQchfw() : 0);
        if ((xyd.getXztze() != null ? xyd.getXztze() : 0) != (tbQrd.getQchfw() != null ? tbQrd.getQchfw() : 0)) {
            if ((tbQrd.getQchfw() != null ? tbQrd.getQchfw() : 0) > 0) {
                //策划经理与策划经理，如果开始有不换，没有改完房东
                if (xyd.getXchjlid() == null || "".equals(xyd.getXchjlid())) {
                    map.put("xchjlid", ding1 != null ? ding1.getUserid() : "");
                }
                if (xyd.getXchcjr() == null || "".equals(xyd.getXchcjr())) {
                    map.put("xchcjr", ding1 != null ? ding1.getUserid() : "");
                }
            } else {
                //清空
                map.put("xchjlid", "");
                map.put("xchcjr", "");
                map.put("xztze", 0);
            }
            b = true;
        }
        //已收定金
        Double xysdj = jsonObject.getDouble("xysdj");
        map.put("xysdj", xysdj != null ? xysdj : 0);
        if ((xyd.getXysdj() != null ? xyd.getXysdj() : 0) != (xysdj != null ? xysdj : 0)) {
            b = true;
        }
        //已收增值定金
        Double xyszzdj = jsonObject.getDouble("xyszzdj");
        map.put("xyszzdj", xyszzdj != null ? xyszzdj : 0);
        if ((xyd.getXyszzdj() != null ? !"".equals(xyd.getXyszzdj()) ? Double.parseDouble(xyd.getXyszzdj()) : 0 : 0) != (xyszzdj != null ? xyszzdj : 0)) {
            b = true;
        }
        //线上定金
        Double xxsdj = jsonObject.getDouble("xxsdj");
        map.put("xxsdj", xxsdj != null ? xxsdj : 0);
        if ((xyd.getXxsdj() != null ? xyd.getXxsdj() : 0) != (xxsdj != null ? xxsdj : 0)) {
            b = true;
        }
        //线下定金
        Double xxxdj = jsonObject.getDouble("xxxdj");
        map.put("xxxdj", xxxdj != null ? xxxdj : 0);
        if ((xyd.getXxxdj() != null ? xyd.getXxxdj() : 0) != (xxxdj != null ? xxxdj : 0)) {
            b = true;
        }
        //补交定金
        Double xbjdj = jsonObject.getDouble("xbjdj");
        map.put("xbjdj", xbjdj != null ? xbjdj : 0);
        if ((xyd.getXbjdj() != null ? xyd.getXbjdj() : 0) != (xbjdj != null ? xbjdj : 0)) {
            b = true;
        }
        //场地费补交
        Double xcdzjbj = jsonObject.getDouble("xcdzjbj");
        map.put("xcdzjbj", xcdzjbj != null ? xcdzjbj : 0);
        if ((xyd.getXcdzjbj() != null ? xyd.getXcdzjbj() : 0) != (xcdzjbj != null ? xcdzjbj : 0)) {
            b = true;
        }
        //增值补交
        Double xzzfybj = jsonObject.getDouble("xzzfybj");
        map.put("xzzfybj", xzzfybj != null ? xzzfybj : 0);
        if ((xyd.getXzzfybj() != null ? xyd.getXzzfybj() : 0) != (xzzfybj != null ? xzzfybj : 0)) {
            b = true;
        }
        //线上补交
        Double xxsbj = jsonObject.getDouble("xxsbj");
        map.put("xxsbj", xxsbj != null ? xxsbj : 0);
        if ((xyd.getXxsbj() != null ? xyd.getXxsbj() : 0) != (xxsbj != null ? xxsbj : 0)) {
            b = true;
        }
        //线下补交
        Double xxxbj = jsonObject.getDouble("xxxbj");
        map.put("xxxbj", xxxbj != null ? xxxbj : 0);
        if ((xyd.getXxxbj() != null ? xyd.getXxxbj() : 0) != (xxxbj != null ? xxxbj : 0)) {
            b = true;
        }
        //恢复原价
        Double xhfyj = jsonObject.getDouble("xhfyj");
        map.put("xhfyj", xhfyj != null ? xhfyj : 0);
        if ((xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) != (xhfyj != null ? xhfyj : 0)) {
            b = true;
        }
        log.info("******订餐消费是否大于0****{}", tbQrd.getQdcxf());
        if (tbQrd.getQdcxf() > 0) {
            WlgbOrderCyjl wlgbOrderCyjl = new WlgbOrderCyjl();
            wlgbOrderCyjl.setDdbh(xyd.getXddbh());
            wlgbOrderCyjl.setCsfsc(0);
            List<WlgbOrderCyjl> list = wlgbOrderCyjlService.queryListByOrderCyJl(wlgbOrderCyjl);
            JSONArray cyList = jsonObject.getJSONArray("cymx");
            boolean d = false;
            for (WlgbOrderCyjl wlgbOrderCyjl1 : list) {
                for (Object l : cyList) {
                    JSONObject jsonObject1 = (JSONObject) l;
                    if (wlgbOrderCyjl1.getCybs() != null && !"".equals(wlgbOrderCyjl1.getCybs())) {
                        if (wlgbOrderCyjl1.getCybs().equals(jsonObject1.getString("cybs"))) {
                            String cysx = jsonObject1.getString("cysx");
                            try {
                                if (cysx.contains("{")) {
                                    JSONObject jsonObject2 = JSONObject.parseObject(cysx);
                                    if (jsonObject2 != null && jsonObject2.size() > 0) {
                                        cysx = jsonObject2.getString("value");
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            if (!wlgbOrderCyjl1.getCysx().equals(cysx)) {
                                d = true;
                                b = true;
                            }
                        }
                    } else {
                        if (wlgbOrderCyjl1.getCymc().equals(jsonObject1.getString("cymc")) && wlgbOrderCyjl1.getCyje().equals(jsonObject1.getDouble("cyje")) && wlgbOrderCyjl1.getCysl().equals(jsonObject1.getInteger("cygmsl")) && wlgbOrderCyjl1.getCyjg().equals(jsonObject1.getDouble("cysj"))) {
                            String cysx = jsonObject1.getString("cysx");
                            try {
                                if (cysx.contains("{")) {
                                    JSONObject jsonObject2 = JSONObject.parseObject(cysx);
                                    if (jsonObject2 != null && jsonObject2.size() > 0) {
                                        cysx = jsonObject2.getString("value");
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            if (!wlgbOrderCyjl1.getCysx().equals(cysx)) {
                                d = true;
                                b = true;
                            }
                        }
                    }
                }
            }
            if (d) {
                List<JSONObject> list1 = new ArrayList<>();
                for (WlgbOrderCyjl wlgbOrderCyjl1 : list) {
                    for (Object l : cyList) {
                        JSONObject jsonObject1 = (JSONObject) l;
                        if (wlgbOrderCyjl1.getCybs() != null && !"".equals(wlgbOrderCyjl1.getCybs())) {
                            if (wlgbOrderCyjl1.getCybs().equals(jsonObject1.getString("cybs"))) {
                                JSONObject jsonObject2 = new JSONObject();
                                jsonObject2.put("cymc", wlgbOrderCyjl1.getCymc());
                                String cysx = jsonObject1.getString("cysx");
                                try {
                                    if (cysx.contains("{")) {
                                        JSONObject jsonObject3 = JSONObject.parseObject(cysx);
                                        if (jsonObject2 != null && jsonObject2.size() > 0) {
                                            cysx = jsonObject3.getString("value");
                                        }
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                jsonObject2.put("selectField_lnldd87u", cysx);
                                DingdingEmployee dingdingEmployee = weiLianService.queryDingDingByName(wlgbOrderCyjl1.getCycjr());
                                if (dingdingEmployee != null) {
                                    jsonObject2.put("employeeField_lmuespiw", dingdingEmployee.getUserid());
                                }
                                jsonObject2.put("cysj", wlgbOrderCyjl1.getCyjg());
                                jsonObject2.put("cygmsl", wlgbOrderCyjl1.getCysl());
                                jsonObject2.put("cyje", wlgbOrderCyjl1.getCyje());
                                jsonObject2.put("cycb", wlgbOrderCyjl1.getCycb());
                                jsonObject2.put("cylb", wlgbOrderCyjl1.getCylb());
                                jsonObject2.put("bm", wlgbOrderCyjl1.getCyjq());
                                jsonObject2.put("cybs", wlgbOrderCyjl1.getCybs());
                                list1.add(jsonObject2);
                            }
                        } else {
                            if (wlgbOrderCyjl1.getCymc().equals(jsonObject1.getString("cymc")) && wlgbOrderCyjl1.getCyje().equals(jsonObject1.getDouble("cyje")) && wlgbOrderCyjl1.getCysl().equals(jsonObject1.getInteger("cygmsl")) && wlgbOrderCyjl1.getCyjg().equals(jsonObject1.getDouble("cysj"))) {
                                JSONObject jsonObject2 = new JSONObject();
                                jsonObject2.put("cymc", wlgbOrderCyjl1.getCymc());
                                String cysx = jsonObject1.getString("cysx");
                                try {
                                    if (cysx.contains("{")) {
                                        JSONObject jsonObject3 = JSONObject.parseObject(cysx);
                                        if (jsonObject2 != null && jsonObject2.size() > 0) {
                                            cysx = jsonObject3.getString("value");
                                        }
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                jsonObject2.put("selectField_lnldd87u", cysx);
                                DingdingEmployee dingdingEmployee = weiLianService.queryDingDingByName(wlgbOrderCyjl1.getCycjr());
                                if (dingdingEmployee != null) {
                                    jsonObject2.put("employeeField_lmuespiw", dingdingEmployee.getUserid());
                                }
                                jsonObject2.put("cysj", wlgbOrderCyjl1.getCyjg());
                                jsonObject2.put("cygmsl", wlgbOrderCyjl1.getCysl());
                                jsonObject2.put("cyje", wlgbOrderCyjl1.getCyje());
                                jsonObject2.put("cycb", wlgbOrderCyjl1.getCycb());
                                jsonObject2.put("cylb", wlgbOrderCyjl1.getCylb());
                                jsonObject2.put("bm", wlgbOrderCyjl1.getCyjq());
                                jsonObject2.put("cybs", wlgbOrderCyjl1.getCybs());
                                list1.add(jsonObject2);
                            }
                        }
                    }
                }
                map.put("cyzbd", list1);
            }
        }

        Map<String, Object> map1 = new HashMap<>();
        map1.put("jcsj", xyd.getXjctime());
        map1.put("tcsj", xyd.getXtctime());
        map1.put("vid", xyd.getXbsmc());
        Integer pdCc = weiLianService.queryPdCc(map1);
        Double zk = 0.0;
        TbKdjZk kdjZk = tbKdjZkService.queryByCcAndSfSc(pdCc, "0");
        zk = kdjZk != null ? kdjZk.getZk() / 10 : 1;
        Double kdjZe = weiLianService.queryKdjZe(map1);
        double v = kdjZe * zk;
        if ((xhfyj != null ? xhfyj : 0) < v) {
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String text = "对账修改协议单失败了";
            text += "\n\n原因：场地费金额低于客单价";
            text += "\n订单编号：" + xyd.getXddbh();
            text += "\n您的场地费金额：" + xhfyj;
            text += "\n客单价场地费金额：" + v;
            text += "\n送达时间：" + df2.format(new Date());
            text += "\n\n请联系客单价专员进行处理！";
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            //根据是否有改动去判断调用协议单修改接口
            if (b) {
                WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(xyd.getXid(), "下单表单提交");
                //容错机制修改表单内容
                JSONObject json = new JSONObject(map);
                GatewayResult gatewayResult = null;
                try {
                    gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, wlgbBdjl.getSlid(), json.toJSONString());
                } catch (Exception e) {
                    gatewayResult = new GatewayResult();
                    e.printStackTrace();
                }
            }
        }
        WlgbOrderCyjl wlgbOrderCyjl = new WlgbOrderCyjl();
        wlgbOrderCyjl.setDdbh(xyd.getXddbh());
        wlgbOrderCyjl.setCsfsc(0);
        List<WlgbOrderCyjl> list = wlgbOrderCyjlService.queryListByOrderCyJl(wlgbOrderCyjl);
        JSONArray jsonArray = jsonObject.getJSONArray("cymx");
        if (!jsonArray.isEmpty()) {
            for (Object j1 : jsonArray) {
                log.info("********餐饮明细数组循环******{}", j1);
                JSONObject l = (JSONObject) j1;
                String cymc = l.getString("cymc");
                Integer cygmsl = l.getInteger("cygmsl");
                Double sjcycb = l.getDouble("sjcycb");
                Double cycjrlr = l.getDouble("cycjrlr");
                Double cyje = l.getDouble("cyje");
                String cycsxm = l.getString("cycsxm");
                String cycsid = l.getString("cycsid");
                String qkqyzxr = null;
                String qkqyzxrid = null;
                try {
                    qkqyzxr = l.getString("qkqyzxr");
                    String qkqyzxrid1 = l.getString("qkqyzxrid");
                    if (qkqyzxr != null && !"".equals(qkqyzxr)) {
                        qkqyzxr = xzjq(qkqyzxr);
                        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(qkqyzxr);
                        if (dingdingEmployee != null) {
                            qkqyzxr = dingdingEmployee.getName();
                            qkqyzxrid = dingdingEmployee.getUserid();
                        }
                    } else if (qkqyzxrid1 != null && !"".equals(qkqyzxrid1)) {
                        qkqyzxr = l.getString("qkqyzxrxm");
                        qkqyzxrid = qkqyzxrid1;
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (!list.isEmpty()) {
                    for (WlgbOrderCyjl cy : list) {
                        log.info("********餐饮明细表循环******{}", cy);
                        if (cy.getCybs() != null && !"".equals(cy.getCybs())) {
                            if (cy.getCybs().equals(l.getString("cybs"))) {
                                WlgbOrderCyjl wlgbOrderCyjl1 = new WlgbOrderCyjl();
                                wlgbOrderCyjl1.setId(cy.getId());
                                wlgbOrderCyjl1.setCysjcb(sjcycb);
                                wlgbOrderCyjl1.setCysjlr(cycjrlr);
                                wlgbOrderCyjl1.setCycsxm(cycsxm);
                                wlgbOrderCyjl1.setCycsid(cycsid);
                                wlgbOrderCyjl1.setCysjzcb((sjcycb != null ? sjcycb : 0) * (cy.getCysl() != null ? cy.getCysl() : 0));
                                try {
                                    if (qkqyzxr != null) {
                                        wlgbOrderCyjl1.setKqyzxr(qkqyzxr);
                                    }
                                    if (qkqyzxrid != null) {
                                        wlgbOrderCyjl1.setKqyzxrid(qkqyzxrid);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                log.info("********修改餐饮明细表******{}", wlgbOrderCyjl1);
                                wlgbOrderCyjlService.updateById(wlgbOrderCyjl1);
                            }
                        } else {
                            if (cy.getCymc().equals(cymc) && cy.getCysl().equals(cygmsl) && cy.getCyje().equals(cyje)) {
                                WlgbOrderCyjl wlgbOrderCyjl1 = new WlgbOrderCyjl();
                                wlgbOrderCyjl1.setId(cy.getId());
                                wlgbOrderCyjl1.setCysjcb(sjcycb);
                                wlgbOrderCyjl1.setCysjlr(cycjrlr);
                                wlgbOrderCyjl1.setCycsxm(cycsxm);
                                wlgbOrderCyjl1.setCycsid(cycsid);
                                wlgbOrderCyjl1.setCysjzcb((sjcycb != null ? sjcycb : 0) * (cy.getCysl() != null ? cy.getCysl() : 0));
                                try {
                                    if (qkqyzxr != null) {
                                        wlgbOrderCyjl1.setKqyzxr(qkqyzxr);
                                    }
                                    if (qkqyzxrid != null) {
                                        wlgbOrderCyjl1.setKqyzxrid(qkqyzxrid);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                wlgbOrderCyjlService.updateById(wlgbOrderCyjl1);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 跟单异步
     */
    @RequestMapping("followUpTask")
    public void followUpTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        int status = Integer.parseInt(request.getParameter("status"));
        JSONObject jsonObject = JSONObject.parseObject(datas);
        WlgbJdbBo wlgbJdb = JSONObject.toJavaObject(jsonObject, WlgbJdbBo.class);
        TbXyd tbXyd1 = tbXydService.queryById(wlgbJdb.getXid());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        if (tbXyd1 != null) {
            wlgbJdb.setXid(tbXyd1.getXid());
            DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(wlgbJdb.getUserId());
            wlgbJdb.setId(IdConfig.uuId());
            wlgbJdb.setUserId(ding != null ? ding.getUserid() : null);
            wlgbJdb.setTime(new Date());
            TbXyd tbXyd = new TbXyd();
            WlgbXydLog wlgbXydLog = new WlgbXydLog();
            TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd1.getXbsmc());
            String vxz = villa.getVxz();
            if (status == 0) {
                WlgbJdbOne one = new WlgbJdbOne();
                BeanUtils.copyProperties(wlgbJdb, one);
                WlgbJdbOne one1 = oneService.queryByXid(tbXyd1.getXid());
                //微信通过截图截取地址
                if (one.getWxtgjt() != null && !"".equals(one.getWxtgjt()) && one.getWxtgjt().length() > 0) {
                    one.setWxtgjt(ydTpScJq(one.getWxtgjt()));
                    try {
                        GatewayResult fileUrl = new GatewayResult();
                        try {
                            fileUrl = YdConfig.getFileUrl(token, ydAppkey, one.getWxtgjt());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                            String url1 = fileUrl.getResult();
                            String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "wxtg.png");
                            downloadPicture(url1, path);
                            String tp1 = alyTp(path);
                            one.setWxtgjt(tp1);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                //微信未通过截图截取地址
                if (one.getWxwtgjt() != null && !"".equals(one.getWxwtgjt()) && one.getWxwtgjt().length() > 0) {
                    one.setWxwtgjt(ydTpScJq(one.getWxwtgjt()));
                    try {
                        GatewayResult fileUrl = new GatewayResult();
                        try {
                            fileUrl = YdConfig.getFileUrl(token, ydAppkey, one.getWxwtgjt());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                            String url1 = fileUrl.getResult();
                            String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "wxwtg.png");
                            downloadPicture(url1, path);
                            String tp1 = alyTp(path);
                            one.setWxwtgjt(tp1);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (one1 == null) {
                    oneService.save(one);
                } else {
                    one.setId(one1.getId());
                    oneService.updateById(one);
                }
                wlgbXydLog.setXltext("店长" + (ding != null ? "“" + ding.getName() + "”" : "") + "接单及跟单");
                tbXyd.setStatus(1);
                //时间差
                long l = tbXyd1.getXjctime().getTime() - new Date().getTime();
                //进场时间减去下单时间的分钟数
                long l1 = l / 1000 / 60;
                //在两个小时内发起进场及消费流程
                if (l1 <= (2 * 60)) {
                    if ("直营".equals(vxz)) {
                        try {
                            jcJxf(tbXyd1, ding);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                //已经超过24小时并大于2小时加入记录
                if (l1 > (2 * 60) && l1 < (24 * 60)) {
                    if ("直营".equals(vxz)) {
                        WlgbNotecgd wlgbNotecgd = new WlgbNotecgd();
                        wlgbNotecgd.setTime(new Date());
                        wlgbNotecgd.setName(ding != null ? ding.getName() : null);
                        wlgbNotecgd.setUserid(ding != null ? ding.getUserid() : null);
                        wlgbNotecgd.setXid(tbXyd1.getXid());
                        wlgbNotecgd.setSfsc(0);
                        wlgbNotecgdService.save(wlgbNotecgd);
                    }
                }
                //30小时-24小时以内发送二次跟单
                if (l1 > (24 * 60) && l1 < (30 * 60)) {
                    String xydUrl = null;
                    try {
                        xydUrl = dzXydCl(tbXyd1);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
                    YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("二次跟单");
                    WlgbDksljl wlgbDksljl1 = wlgbDksljlService.queryByBzAndXid("二次跟单", tbXyd1.getXid());


                    //发起人改成房东
                    try {
                        WlgbDksljl wlgbDksljl = EcGdLcConfig.ecGdLc(tbXyd1, villa, dingkey, xydUrl, employee, ydAppkey, ydBd, wlgbDksljl1);
                        if (wlgbDksljl != null) {
                            wlgbDksljlService.save(wlgbDksljl);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        try {
                            DingDBConfig.sendGztzText(dingkey, "15349026426046931", "跟单完成发起二次跟单，订单编号”" + tbXyd1.getXddbh() + "“，错误原因：" + e.getMessage());
                        } catch (ApiException apiException) {
                            apiException.printStackTrace();
                        }
                    }
                }

            } else if (status == 1) {
                WlgbJdbTwo two = new WlgbJdbTwo();
                BeanUtils.copyProperties(wlgbJdb, two);
                WlgbJdbTwo two2 = twoService.queryByXid(tbXyd1.getXid());
                //客户确认入场图片截取地址
                if (two.getQrtp() != null && !"".equals(two.getQrtp()) && two.getQrtp().length() > 0) {
                    two.setQrtp(ydTpScJq(two.getQrtp()));
                    try {
                        GatewayResult fileUrl = new GatewayResult();
                        try {
                            fileUrl = YdConfig.getFileUrl(token, ydAppkey, two.getQrtp());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                            String url1 = fileUrl.getResult();
                            String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "qrtp.png");
                            downloadPicture(url1, path);
                            String tp1 = alyTp(path);
                            two.setQrtp(tp1);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                if (two2 == null) {
                    twoService.save(two);
                } else {
                    two.setId(two2.getId());
                    twoService.updateById(two);
                }
                wlgbXydLog.setXltext("店长" + (ding != null ? "“" + ding.getName() + "”" : "") + "二级跟单");
                tbXyd.setStatus(2);
                //时间差
                long l = tbXyd1.getXjctime().getTime() - new Date().getTime();
                //进场时间减去下单时间的分钟数
                long l1 = l / 1000 / 60;
                //在两个小时内发起进场及消费流程
                if (l1 <= (2 * 60)) {
                    if ("直营".equals(vxz)) {
                        try {
                            jcJxf(tbXyd1, ding);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
            tbXyd.setXid(wlgbJdb.getXid());
            tbXydService.updateById(tbXyd);
            wlgbXydLog.setXlid(IdConfig.uuId());
            wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
            wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
            wlgbXydLog.setXlxid(wlgbJdb.getXid());
            wlgbXydLog.setXltime(new Date());
            wlgbXydLogService.save(wlgbXydLog);

            TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(tbXyd.getXid());
            if (tbYddXyd != null) {
                TbYddXyd tbYddXyd1 = new TbYddXyd();
                tbYddXyd1.setXid(tbYddXyd.getXid());
                tbYddXyd1.setXstatu("已联系，等待进场");
                tbYddXydService.updateById(tbYddXyd1);

                String post = null;
                try {
                    Map<String, String> map = new HashMap<>();
                    map.put("ddbh", tbYddXyd.getXddbh());
                    map.put("zt", tbYddXyd1.getXstatu());
                    //总价
                    map.put("zj", tbYddXyd.getXhfyj() + tbYddXyd.getXztze() + tbYddXyd.getXdcze() + tbYddXyd.getXhpsfy() + tbYddXyd.getXbxzje() + "");
                    post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/b2e50678-62d2-4d65-a042-27427265a26f", map);
                } catch (Exception e) {
                    e.printStackTrace();
                    try {
                        String context1 = "完成跟单将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，错误原因：" + e;
                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e1) {
                        e1.printStackTrace();
                    }
                }
                try {
                    if (post != null && !"".equals(post)) {
                        JSONObject jsonObject1 = JSONObject.parseObject(post);
                        String jg = jsonObject1.getString("jg");
                        if (!"ok".equalsIgnoreCase(jg)) {
                            try {
                                String context1 = "完成跟单将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post;
                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                            } catch (ApiException e1) {
                                e1.printStackTrace();
                            }
                        }
                    }
                } catch (Exception e) {
                    try {
                        String context1 = "完成跟单将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e1) {
                        e1.printStackTrace();
                    }
                    e.printStackTrace();
                }
            }
        }

    }

    /**
     * 操作订单异步
     */
    @RequestMapping(value = "orderTaSk")
    public void orderTaSk(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("datasName");
        String formInstId = request.getParameter("formInstId");
        int status = Integer.parseInt(request.getParameter("status"));
        JSONObject jsonObject = JSONObject.parseObject(datas);
        WlgbJdb wlgbJdb = new WlgbJdb();
        String xid = jsonObject.getString("xid");
        if (xid == null || "".equals(xid)) {
            return;
        }
        wlgbJdb.setXid(xid);
        wlgbJdb.setUserId(userid);
        wlgbJdb.setJcdds(jsonObject.getDouble("qrcdds"));
        wlgbJdb.setTcdds(jsonObject.getDouble("qtcdds"));
        wlgbJdb.setCdfwkje(jsonObject.getDouble("cdfwkje"));
        wlgbJdb.setZzwkje(jsonObject.getDouble("zzwkje"));
        wlgbJdb.setQyj(jsonObject.getDouble("qyj"));

//        WlgbJdbBo wlgbJdb = JSONObject.toJavaObject(jsonObject, WlgbJdbBo.class);
        if (wlgbJdb.getUserId() != null && !"".equals(wlgbJdb.getUserId())) {
            userid = wlgbJdb.getUserId();
            userid = userid != null ? !"".equals(userid) ? userid : "012412221639786136545" : "012412221639786136545";
        }
        if (status == 3) {
            status = 11;
        }
        wlgbJdb.setHjtp(jsonObject.getString("hjtp"));
        //进场商品补充货架图截取地址
        if (wlgbJdb.getHjtp() != null && !"".equals(wlgbJdb.getHjtp()) && wlgbJdb.getHjtp().length() > 0) {
            wlgbJdb.setHjtp(ydTpScJq(wlgbJdb.getHjtp()));
        }
        wlgbJdb.setZzhtzp(jsonObject.getString("zzhtzp"));
        //纸质合同照片截取地址
        if (wlgbJdb.getZzhtzp() != null && !"".equals(wlgbJdb.getZzhtzp()) && wlgbJdb.getZzhtzp().length() > 0) {
            wlgbJdb.setZzhtzp(ydTpScJq(wlgbJdb.getZzhtzp()));
        }
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        //进场电表照片截取地址
        wlgbJdb.setRcSjxjdbt(jsonObject.getString("rcSjxjdbt"));
        if (wlgbJdb.getRcSjxjdbt() != null && !"".equals(wlgbJdb.getRcSjxjdbt()) && wlgbJdb.getRcSjxjdbt().length() > 0) {
            wlgbJdb.setRcSjxjdbt(ydTpScJq(wlgbJdb.getRcSjxjdbt()));
            try {
                GatewayResult fileUrl = new GatewayResult();
                try {
                    fileUrl = YdConfig.getFileUrl(token, ydAppkey, wlgbJdb.getRcSjxjdbt());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                    String url1 = fileUrl.getResult();
                    String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "rcdb1.png");
                    downloadPicture(url1, path);
                    String tp1 = alyTp(path);
                    wlgbJdb.setRcSjxjdbt(tp1);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        wlgbJdb.setRcSjxjdbt2(jsonObject.getString("rcSjxjdbt2"));
        //进场电表照片截取地址2
        if (wlgbJdb.getRcSjxjdbt2() != null && !"".equals(wlgbJdb.getRcSjxjdbt2()) && wlgbJdb.getRcSjxjdbt2().length() > 0) {
            wlgbJdb.setRcSjxjdbt2(ydTpScJq(wlgbJdb.getRcSjxjdbt2()));
            try {
                GatewayResult fileUrl = new GatewayResult();
                try {
                    fileUrl = YdConfig.getFileUrl(token, ydAppkey, wlgbJdb.getRcSjxjdbt2());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                    String url1 = fileUrl.getResult();
                    String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "rcdb2.png");
                    downloadPicture(url1, path);
                    String tp1 = alyTp(path);
                    wlgbJdb.setRcSjxjdbt2(tp1);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        wlgbJdb.setCdfwktp1(jsonObject.getString("cdfwktp1"));
        //场地费尾款图片截取地址
        if (wlgbJdb.getCdfwktp1() != null && !"".equals(wlgbJdb.getCdfwktp1()) && wlgbJdb.getCdfwktp1().length() > 0) {
            wlgbJdb.setCdfwktp1(ydTpScJq(wlgbJdb.getCdfwktp1()));
            try {
                GatewayResult fileUrl = new GatewayResult();
                try {
                    fileUrl = YdConfig.getFileUrl(token, ydAppkey, wlgbJdb.getCdfwktp1());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                    String url1 = fileUrl.getResult();
                    String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "cdfwk.png");
                    downloadPicture(url1, path);
                    String tp1 = alyTp(path);
                    wlgbJdb.setCdfwktp1(tp1);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        wlgbJdb.setCdfwktp2(jsonObject.getString("cdfwktp2"));
        //增值尾款图片截取地址
        if (wlgbJdb.getCdfwktp2() != null && !"".equals(wlgbJdb.getCdfwktp2()) && wlgbJdb.getCdfwktp2().length() > 0) {
            wlgbJdb.setCdfwktp2(ydTpScJq(wlgbJdb.getCdfwktp2()));
            try {
                GatewayResult fileUrl = new GatewayResult();
                try {
                    fileUrl = YdConfig.getFileUrl(token, ydAppkey, wlgbJdb.getCdfwktp2());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                    String url1 = fileUrl.getResult();
                    String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "zzwk.png");
                    downloadPicture(url1, path);
                    String tp1 = alyTp(path);
                    wlgbJdb.setCdfwktp2(tp1);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        wlgbJdb.setCfzp2(jsonObject.getString("cfzp2"));
        //防扰民打卡厨房图片截取地址
        if (wlgbJdb.getCfzp2() != null && !"".equals(wlgbJdb.getCfzp2()) && wlgbJdb.getCfzp2().length() > 0) {
            wlgbJdb.setCfzp2(ydTpScJq(wlgbJdb.getCfzp2()));
        }
        wlgbJdb.setHwjdtct(jsonObject.getString("hwjdtct"));
        //防扰民打卡户外街道停车图片截取地址
        if (wlgbJdb.getHwjdtct() != null && !"".equals(wlgbJdb.getHwjdtct()) && wlgbJdb.getHwjdtct().length() > 0) {
            wlgbJdb.setHwjdtct(ydTpScJq(wlgbJdb.getHwjdtct()));
        }
        wlgbJdb.setDttp2(jsonObject.getString("dttp2"));
        //防扰民打卡大厅图片截取地址
        if (wlgbJdb.getDttp2() != null && !"".equals(wlgbJdb.getDttp2()) && wlgbJdb.getDttp2().length() > 0) {
            wlgbJdb.setDttp2(ydTpScJq(wlgbJdb.getDttp2()));
        }
        wlgbJdb.setMctp(jsonObject.getString("mctp"));
        //防扰民打卡门窗图片截取地址
        if (wlgbJdb.getMctp() != null && !"".equals(wlgbJdb.getMctp()) && wlgbJdb.getMctp().length() > 0) {
            wlgbJdb.setMctp(ydTpScJq(wlgbJdb.getMctp()));
        }
        wlgbJdb.setSbyctp(jsonObject.getString("sbyctp"));
        //退场设备卫生异常图片截取地址
        if (wlgbJdb.getSbyctp() != null && !"".equals(wlgbJdb.getSbyctp()) && wlgbJdb.getSbyctp().length() > 0) {
            wlgbJdb.setSbyctp(ydTpScJq(wlgbJdb.getSbyctp()));
        }
        wlgbJdb.setTcSjdbt(jsonObject.getString("tcSjdbt"));
        //退场电表图片截取地址
        if (wlgbJdb.getTcSjdbt() != null && !"".equals(wlgbJdb.getTcSjdbt()) && wlgbJdb.getTcSjdbt().length() > 0) {
            wlgbJdb.setTcSjdbt(ydTpScJq(wlgbJdb.getTcSjdbt()));
            try {
                GatewayResult fileUrl = new GatewayResult();
                try {
                    fileUrl = YdConfig.getFileUrl(token, ydAppkey, wlgbJdb.getTcSjdbt());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                    String url1 = fileUrl.getResult();
                    String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "tcdb.png");
                    downloadPicture(url1, path);
                    String tp1 = alyTp(path);
                    wlgbJdb.setTcSjdbt(tp1);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        wlgbJdb.setTcSjdbt2(jsonObject.getString("tcSjdbt2"));
        //退场电表图片截取地址2
        if (wlgbJdb.getTcSjdbt2() != null && !"".equals(wlgbJdb.getTcSjdbt2()) && wlgbJdb.getTcSjdbt2().length() > 0) {
            wlgbJdb.setTcSjdbt2(ydTpScJq(wlgbJdb.getTcSjdbt2()));
            try {
                GatewayResult fileUrl = new GatewayResult();
                try {
                    fileUrl = YdConfig.getFileUrl(token, ydAppkey, wlgbJdb.getTcSjdbt2());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                    String url1 = fileUrl.getResult();
                    String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "tcdb2.png");
                    downloadPicture(url1, path);
                    String tp1 = alyTp(path);
                    wlgbJdb.setTcSjdbt2(tp1);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        if (status == 2) {
            wlgbJdb.setUserId(ding != null ? ding.getUserid() : null);
            wlgbJdb.setTime(new Date());
            WlgbJdb jdb = wlgbJdbService.queryByXid(wlgbJdb.getXid());
            if (jdb == null) {
                wlgbJdb.setId(IdConfig.uuId());
                wlgbJdbService.save(wlgbJdb);
            } else {
                wlgbJdb.setId(jdb.getId());
                wlgbJdbService.updateById(wlgbJdb);
            }
        } else {
            WlgbJdb wlgbJdb1 = new WlgbJdb();
            BeanUtils.copyProperties(wlgbJdb, wlgbJdb1);
            if (status == 11) {
                wlgbJdb.setTime3(new Date());
            } else {
                wlgbJdb.setTime4(new Date());
            }
            WlgbJdb jdb = wlgbJdbService.queryByXid(wlgbJdb.getXid());
            if (jdb == null) {
                wlgbJdb.setId(IdConfig.uuId());
                wlgbJdbService.save(wlgbJdb);
            } else {
                wlgbJdb.setId(jdb.getId());
                wlgbJdbService.updateById(wlgbJdb);
            }
        }
        TbXyd tbXyd = new TbXyd();
        if (status == 2) {
            tbXyd.setStatus(11);
        } else if (status == 11) {
            tbXyd.setStatus(4);
        } else {
            tbXyd.setStatus(status + 1);
        }
        tbXyd.setXid(wlgbJdb.getXid());
        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(tbXyd.getXid());
        switch (status) {
            case 2:
                wlgbXydLog.setXltext("入场并收尾款");
                if (tbYddXyd != null) {
                    TbYddXyd tbYddXyd1 = new TbYddXyd();
                    tbYddXyd1.setXid(tbYddXyd.getXid());
                    tbYddXyd1.setXstatu("已进场，等待交押金和尾款");
                    tbYddXydService.updateById(tbYddXyd1);

                    String post = null;
                    try {
                        Map<String, String> map = new HashMap<>();
                        map.put("ddbh", tbYddXyd.getXddbh());
                        map.put("zt", tbYddXyd1.getXstatu());
                        //总价
                        map.put("zj", tbYddXyd.getXhfyj() + tbYddXyd.getXztze() + tbYddXyd.getXdcze() + tbYddXyd.getXhpsfy() + tbYddXyd.getXbxzje() + "");
                        post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/b2e50678-62d2-4d65-a042-27427265a26f", map);
                    } catch (Exception e) {
                        e.printStackTrace();
                        try {
                            String context1 = "进场收尾款将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，错误原因：" + e;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                    }
                    try {
                        if (post != null && !"".equals(post)) {
                            JSONObject jsonObject1 = JSONObject.parseObject(post);
                            String jg = jsonObject1.getString("jg");
                            if (!"ok".equalsIgnoreCase(jg)) {
                                try {
                                    String context1 = "进场收尾款将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post;
                                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                } catch (ApiException e1) {
                                    e1.printStackTrace();
                                }
                            }
                        }
                    } catch (Exception e) {
                        try {
                            String context1 = "进场收尾款将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                        e.printStackTrace();
                    }
                }
                break;
            case 11:
                wlgbXydLog.setXltext("巡场服务");
                if (tbYddXyd != null) {
                    TbYddXyd tbYddXyd1 = new TbYddXyd();
                    tbYddXyd1.setXid(tbYddXyd.getXid());
                    tbYddXyd1.setXstatu("已交尾款，消费中");
                    tbYddXydService.updateById(tbYddXyd1);

                    String post = null;
                    try {
                        Map<String, String> map = new HashMap<>();
                        map.put("ddbh", tbYddXyd.getXddbh());
                        map.put("zt", tbYddXyd1.getXstatu());
                        //总价
                        map.put("zj", tbYddXyd.getXhfyj() + tbYddXyd.getXztze() + tbYddXyd.getXdcze() + tbYddXyd.getXhpsfy() + tbYddXyd.getXbxzje() + "");
                        post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/b2e50678-62d2-4d65-a042-27427265a26f", map);
                    } catch (Exception e) {
                        e.printStackTrace();
                        try {
                            String context1 = "巡查将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，错误原因：" + e;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                    }
                    try {
                        if (post != null && !"".equals(post)) {
                            JSONObject jsonObject1 = JSONObject.parseObject(post);
                            String jg = jsonObject1.getString("jg");
                            if (!"ok".equalsIgnoreCase(jg)) {
                                try {
                                    String context1 = "巡场将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post;
                                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                } catch (ApiException e1) {
                                    e1.printStackTrace();
                                }
                            }
                        }
                    } catch (Exception e) {
                        try {
                            String context1 = "巡场将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                        e.printStackTrace();
                    }
                }
                break;
            case 4:
                wlgbXydLog.setXltext("退场");
                if (tbYddXyd != null) {
                    TbYddXyd tbYddXyd1 = new TbYddXyd();
                    tbYddXyd1.setXid(tbYddXyd.getXid());
                    tbYddXyd1.setXstatu("已消费完，退场结算中");
                    tbYddXydService.updateById(tbYddXyd1);

                    String post = null;
                    try {
                        Map<String, String> map = new HashMap<>();
                        map.put("ddbh", tbYddXyd.getXddbh());
                        map.put("zt", tbYddXyd1.getXstatu());
                        //总价
                        map.put("zj", tbYddXyd.getXhfyj() + tbYddXyd.getXztze() + tbYddXyd.getXdcze() + tbYddXyd.getXhpsfy() + tbYddXyd.getXbxzje() + "");
                        post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/b2e50678-62d2-4d65-a042-27427265a26f", map);
                    } catch (Exception e) {
                        e.printStackTrace();
                        try {
                            String context1 = "退场将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，错误原因：" + e;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                    }
                    try {
                        if (post != null && !"".equals(post)) {
                            JSONObject jsonObject1 = JSONObject.parseObject(post);
                            String jg = jsonObject1.getString("jg");
                            if (!"ok".equalsIgnoreCase(jg)) {
                                try {
                                    String context1 = "退场将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post;
                                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                } catch (ApiException e1) {
                                    e1.printStackTrace();
                                }
                            }
                        }
                    } catch (Exception e) {
                        try {
                            String context1 = "退场将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                        e.printStackTrace();
                    }
                }
                break;
            default:
                break;
        }
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXlxid(wlgbJdb.getXid());
        wlgbXydLog.setXltime(new Date());
        tbXydService.updateById(tbXyd);

        wlgbXydLogService.save(wlgbXydLog);

        if (status == 2) {
            String sfydd = jsonObject.getString("sfydd");
            if ("是".equals(sfydd)) {
                TbXyd tbXyd1 = tbXydService.queryById(xid);
                if (tbXyd1 != null) {
                    DingdingEmployee ding1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(jsonObject.getString("qsendder"));
                    JSONObject jsonObject1 = new JSONObject();
                    WlgbYddYjWkJl wlgbYddYjWkJl = new WlgbYddYjWkJl();
                    wlgbYddYjWkJl.setSlid(formInstId);
                    wlgbYddYjWkJl.setXid(tbXyd1.getXid());
                    wlgbYddYjWkJl.setXddbh(tbXyd1.getXddbh());
                    if (ding1 != null) {
                        wlgbYddYjWkJl.setFqrid(ding1.getUserid());
                        wlgbYddYjWkJl.setFqr(ding1.getName());
                    }
                    Double qyj = jsonObject.getDouble("qyj") != null ? jsonObject.getDouble("qyj") : 0.0;
                    if (qyj > 0) {
                        wlgbYddYjWkJl.setSfyj(1);
                        String url = ddxturl + "/ysdjtb/wlgb/pay/yjPdFs?ddbh=" + tbXyd1.getXddbh();
                        //收款二维码
                        String ewm = null;
                        try {
                            ewm = scEwm1(url, "yjewm");
                            ;
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (ewm != null) {
                            wlgbYddYjWkJl.setYjewm(ewm);
                            jsonObject1.put("imageField_lt15rkkk", YdConfig.setTpList(ewm, "押金二维码"));
                        }
                        wlgbYddYjWkJl.setYjje(qyj);
                        wlgbYddYjWkJl.setYjskbh(DateFormatConfig.dfSjc());
                        wlgbYddYjWkJl.setYjsctime(new Date());
                        Calendar c = Calendar.getInstance();
                        c.setTime(wlgbYddYjWkJl.getYjsctime());
                        c.add(Calendar.HOUR_OF_DAY, 2);
                        wlgbYddYjWkJl.setYjsxtime(c.getTime());

                        jsonObject1.put("dateField_lt15rkkl", wlgbYddYjWkJl.getYjsctime());
                    }
                    Double wk = jsonObject.getDouble("cdfwkje") != null ? jsonObject.getDouble("cdfwkje") : 0.0;
                    if (wk > 0) {
                        wlgbYddYjWkJl.setSfwk(1);
                        String url = ddxturl + "/ysdjtb/wlgb/pay/wkPdFs?ddbh=" + tbXyd1.getXddbh();
                        //收款二维码
                        String ewm = null;
                        try {
                            ewm = scEwm1(url, "wkewm");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (ewm != null) {
                            wlgbYddYjWkJl.setWkewm(ewm);
                            jsonObject1.put("imageField_lt15rkkp", YdConfig.setTpList(ewm, "尾款二维码"));
                        }
                        wlgbYddYjWkJl.setWkje(wk);
                        wlgbYddYjWkJl.setWkskbh(DateFormatConfig.dfSjc());
                        wlgbYddYjWkJl.setWksctime(new Date());
                        Calendar c = Calendar.getInstance();
                        c.setTime(wlgbYddYjWkJl.getWksctime());
                        c.add(Calendar.HOUR_OF_DAY, 2);
                        wlgbYddYjWkJl.setWksxtime(c.getTime());

                        jsonObject1.put("dateField_lt15rkko", wlgbYddYjWkJl.getWksctime());
                    }
                    if (qyj > 0 || wk > 0) {
                        if (wlgbYddYjWkJl.getFqrid() != null && !"".equals(wlgbYddYjWkJl.getFqrid())) {
                            TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd1.getXbsmc());
                            String text = "您已成功发起押金与尾款的二维码收款，请提醒客户在小程序端完成支付！";
                            text += "\n\n订单编号：" + tbXyd1.getXddbh();
                            text += "\n门店：" + tbVilla.getVname();
                            text += "\n客户姓名：" + tbXyd1.getXzk();
                            text += "\n客户电话：" + tbXyd1.getXzkdh();
                            if (qyj > 0) {
                                text += "\n押金金额：" + qyj + "元";
                            }
                            if (wk > 0) {
                                text += "\n尾款金额：" + wk + "元";
                            }
                            text += "\n进场时间：" + DateFormatConfig.df3(tbXyd1.getXjctime());
                            text += "\n退场时间：" + DateFormatConfig.df3(tbXyd1.getXtctime());
                            text += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
                            try {
                                DingDBConfig.sendGztzText(dingkey, wlgbYddYjWkJl.getFqrid(), text);
                            } catch (ApiException apiException) {
                                apiException.printStackTrace();
                            }
                        }
                        wlgbYddYjWkJlService.save(wlgbYddYjWkJl);

                        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd1.getXbsmc());
                        //通过微信的服务通知给客户发送消息,通知客户去付尾款+押金
                        try {
                            Map<String, String> map2 = new HashMap<>();
                            //店长设置完尾款和押金后
                            map2.put("thing2", "请您支付尾款+押金");
                            //绑定真实的门店名称
                            map2.put("thing3", tbVilla.getVname());
                            //新增的字段，微信用户id
                            map2.put("accountId", tbYddXyd.getAccountid());
                            map2.put("templateId", "ckPS_k6kng_JRNVFlv4znhK5IA3JG-BhCu3sjrxMxZM");
                            HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/3d016937-2c0c-4899-8b2a-07b0db3260b1", map2);
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }

                        GatewayResult gatewayResult = null;
                        try {
                            gatewayResult = DingBdLcConfig.gxLcSl(token, ydAppkey, "012412221639786136545", jsonObject1.toJSONString(), formInstId);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        System.out.println(gatewayResult);
                    }
                }
            }
        }
    }

    /**
     * 生成门第消费二维码
     */
    @RequestMapping(value = "scMdXfEwmTask")
    public void scMdXfEwmTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        if (datas == null || "".equals(datas)) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String userid = jsonObject.getString("userid");
        TbQrd qrd = JSONObject.toJavaObject(jsonObject, TbQrd.class);
        if (qrd.getQxydid() == null || "".equals(qrd.getQxydid())) {
            qrd.setQxydid(jsonObject.getString("xid"));
        }
        if (qrd.getQxydid() == null || "".equals(qrd.getQxydid())) {
            return;
        }
        TbXyd xyd1 = tbXydService.queryById(qrd.getQxydid());
        if (xyd1 == null) {
            return;
        }
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXlxid(xyd1.getXid());
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXltext("生成门店消费二维码");
        wlgbXydLogService.save(wlgbXydLog);


        Double zzje = jsonObject.getDouble("zzje") != null ? jsonObject.getDouble("zzje") : 0.0;
        if (zzje > 0) {
            String url = ddxturl + "/ysdjtb/wlgb/pay/mdXfPdFs?ddbh=" + xyd1.getXddbh();
            //收款二维码
            String ewm = null;
            try {
                ewm = scEwm1(url, "mdXfEwm");
            } catch (Exception e) {
                e.printStackTrace();
            }
            WlgbYddMdxfjl wlgbYddMdxfjl = new WlgbYddMdxfjl();
            wlgbYddMdxfjl.setXid(xyd1.getXid());
            wlgbYddMdxfjl.setXddbh(xyd1.getXddbh());
            wlgbYddMdxfjl.setJe(zzje);
            wlgbYddMdxfjl.setSlid(formInstId);
            wlgbYddMdxfjl.setEwm(ewm);
            wlgbYddMdxfjl.setSctime(new Date());
            Calendar c = Calendar.getInstance();
            c.setTime(wlgbYddMdxfjl.getSctime());
            c.add(Calendar.HOUR_OF_DAY, 2);
            wlgbYddMdxfjl.setSxtime(c.getTime());
            wlgbYddMdxfjl.setSkbh(DateFormatConfig.dfSjc());
            wlgbYddMdxfjlService.save(wlgbYddMdxfjl);
        }


    }


    /**
     * 进场及消费转账异步
     */
    @RequestMapping(value = "jcJXfZzTask")
    public void jcJXfZzTask(HttpServletRequest request) throws Exception {
        String datas = request.getParameter("datas");
        String userId = request.getParameter("userid");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        TbQrd qrd = JSONObject.toJavaObject(jsonObject, TbQrd.class);
        TbXyd xyd1 = tbXydService.queryById(qrd.getQxydid());
        //确认完成转发证据图片截取地址
        if (qrd.getZfjzjt() != null && !"".equals(qrd.getZfjzjt()) && qrd.getZfjzjt().length() > 0) {
            qrd.setZfjzjt(ydTpScJq(qrd.getZfjzjt()));
        }
        //确认卫生费图片截取地址
        if (qrd.getWsjzjt() != null && !"".equals(qrd.getWsjzjt()) && qrd.getWsjzjt().length() > 0) {
            qrd.setWsjzjt(ydTpScJq(qrd.getWsjzjt()));
        }
        //确认赔偿费图片截取地址
        if (qrd.getPcjzjt() != null && !"".equals(qrd.getPcjzjt()) && qrd.getPcjzjt().length() > 0) {
            qrd.setPcjzjt(ydTpScJq(qrd.getPcjzjt()));
        }
        //对账订餐成本图片截取地址
        if (qrd.getQddcbF() != null && !"".equals(qrd.getQddcbF()) && qrd.getQddcbF().length() > 0) {
            qrd.setQddcbF(ydTpScJq(qrd.getQddcbF()));
        }
        //对账烧烤成本图片截取地址
        if (qrd.getQskcbF() != null && !"".equals(qrd.getQskcbF()) && qrd.getQskcbF().length() > 0) {
            qrd.setQskcbF(ydTpScJq(qrd.getQskcbF()));
        }
        //转账金额截图1截取地址
        if (qrd.getZztp1() != null && !"".equals(qrd.getZztp1()) && qrd.getZztp1().length() > 0) {
            qrd.setZztp1(ydTpScJq(qrd.getZztp1()));
        }
        //转账金额截图2截取地址
        if (qrd.getZztp2() != null && !"".equals(qrd.getZztp2()) && qrd.getZztp2().length() > 0) {
            qrd.setZztp2(ydTpScJq(qrd.getZztp2()));
        }
        //点餐消费商品图片截取地址
        if (qrd.getQspqrdF() != null && !"".equals(qrd.getQspqrdF()) && qrd.getQspqrdF().length() > 0) {
            qrd.setQspqrdF(ydTpScJq(qrd.getQspqrdF()));
        }
        //商品消费文件1截取地址
        if (qrd.getQspqrdF1() != null && !"".equals(qrd.getQspqrdF1()) && qrd.getQspqrdF1().length() > 0) {
            qrd.setQspqrdF1(ydTpScJq(qrd.getQspqrdF1()));
        }
        //商品消费文件2截取地址
        if (qrd.getQspqrdF2() != null && !"".equals(qrd.getQspqrdF2()) && qrd.getQspqrdF2().length() > 0) {
            qrd.setQspqrdF2(ydTpScJq(qrd.getQspqrdF2()));
        }
        //订餐成本截取地址2
        if (qrd.getQddcbF2() != null && !"".equals(qrd.getQddcbF2()) && qrd.getQddcbF2().length() > 0) {
            qrd.setQddcbF2(ydTpScJq(qrd.getQddcbF2()));
        }
        //订餐成本截取地址3
        if (qrd.getQddcbF3() != null && !"".equals(qrd.getQddcbF3()) && qrd.getQddcbF3().length() > 0) {
            qrd.setQddcbF3(ydTpScJq(qrd.getQddcbF3()));
        }
        //烧烤成本截取地址2
        if (qrd.getQskcbF2() != null && !"".equals(qrd.getQskcbF2()) && qrd.getQskcbF2().length() > 0) {
            qrd.setQskcbF2(ydTpScJq(qrd.getQskcbF2()));
        }
        //烧烤成本截取地址3
        if (qrd.getQskcbF3() != null && !"".equals(qrd.getQskcbF3()) && qrd.getQskcbF3().length() > 0) {
            qrd.setQskcbF3(ydTpScJq(qrd.getQskcbF3()));
        }
        //自助餐成本图片1
        if (qrd.getQzzccbtp1() != null && !"".equals(qrd.getQzzccbtp1()) && qrd.getQzzccbtp1().length() > 0) {
            qrd.setQzzccbtp1(ydTpScJq(qrd.getQzzccbtp1()));
        }
        //自助餐成本图片2
        if (qrd.getQzzccbtp2() != null && !"".equals(qrd.getQzzccbtp2()) && qrd.getQzzccbtp2().length() > 0) {
            qrd.setQzzccbtp2(ydTpScJq(qrd.getQzzccbtp2()));
        }
        //策划外包成本图片1
        if (qrd.getQchwbcbtp1() != null && !"".equals(qrd.getQchwbcbtp1()) && qrd.getQchwbcbtp1().length() > 0) {
            qrd.setQchwbcbtp1(ydTpScJq(qrd.getQchwbcbtp1()));
        }
        //策划外包成本图片2
        if (qrd.getQchwbcbtp2() != null && !"".equals(qrd.getQchwbcbtp2()) && qrd.getQchwbcbtp2().length() > 0) {
            qrd.setQchwbcbtp2(ydTpScJq(qrd.getQchwbcbtp2()));
        }
        //代购成本单
        if (qrd.getQdgcbd() != null && !"".equals(qrd.getQdgcbd()) && qrd.getQdgcbd().length() > 0) {
            qrd.setQdgcbd(ydTpScJq(qrd.getQdgcbd()));
        }
        //代购付款截图
        if (qrd.getQdgfkjt() != null && !"".equals(qrd.getQdgfkjt()) && qrd.getQdgfkjt().length() > 0) {
            qrd.setQdgfkjt(ydTpScJq(qrd.getQdgfkjt()));
        }
        //实际进场时间处理
        if (qrd.getQjctime() != null && !"".equals(qrd.getQjctime()) && qrd.getQjctime().length() > 0) {
            Calendar c = Calendar.getInstance();
            try {
                c.setTimeInMillis(Long.parseLong(qrd.getQjctime()));
                int day = c.get(Calendar.HOUR_OF_DAY);
                int min = c.get(Calendar.MINUTE);
                qrd.setQjctime(day + "");
                qrd.setQjcmin(min + "");
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

        //实际退场时间处理
        if (qrd.getQtctime() != null && !"".equals(qrd.getQtctime()) && qrd.getQtctime().length() > 0) {
            Calendar c = Calendar.getInstance();
            try {
                c.setTimeInMillis(Long.parseLong(qrd.getQtctime()));
                int day = c.get(Calendar.HOUR_OF_DAY);
                int min = c.get(Calendar.MINUTE);
                qrd.setQtctime(day + "");
                qrd.setQtcmin(min + "");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //延时费归属人1
        if (qrd.getQysfgsr1() != null && !"".equals(qrd.getQysfgsr1()) && qrd.getQysfgsr1().length() > 0) {
            qrd.setQysfgsr1(xzjq(qrd.getQysfgsr1()));
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(qrd.getQysfgsr1());
            if (employee != null) {
                qrd.setQysfgsr1(employee.getName());
            }
        }
        //延时费归属人2
        if (qrd.getQysfgsr2() != null && !"".equals(qrd.getQysfgsr2()) && qrd.getQysfgsr2().length() > 0) {
            qrd.setQysfgsr2(xzjq(qrd.getQysfgsr2()));
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(qrd.getQysfgsr2());
            if (employee != null) {
                qrd.setQysfgsr2(employee.getName());
            }
        }
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);
        TbXyd tbXyd = tbXydService.queryById(qrd.getQxydid());
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd != null ? tbXyd.getXbsmc() : null);
        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa != null ? villa.getPid() : null);
        String jcjxfzxr = jsonObject.getString("jcjxfzxr");
        if (jcjxfzxr != null && !"".equals(jcjxfzxr)) {
            jcjxfzxr = xzjq(jcjxfzxr);
        }
        DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(jcjxfzxr);
        qrd.setQzbdz(employee1 != null ? employee1.getName() : ding != null ? ding.getName() : employee != null ? employee.getName() : null);
        Double j = 0.0;
        Double t = 0.0;
        if (qrd.getQrcdds() != null) {
            j = qrd.getQrcdds();
        }
        if (qrd.getQtcdds() != null) {
            t = qrd.getQtcdds();
        }
        Double sum = 0.0;
        if (j > t) {
            sum = j - t;
        } else {
            sum = t - j;
        }
        BigDecimal bd = new BigDecimal(sum);
        //用电度数留取小数点后两位
        sum = bd.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

        TbQrd tbQrd = tbQrdService.queryQxydIdAndQsfSc(xyd1.getXid());
        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        if (tbQrd != null) {
            if (!"1".equals(tbQrd.getSfwcdz())) {
                qrd.setQydds(sum);
                qrd.setQid(tbQrd.getQid());
//                if (tbQrd.getQrsj() == null) {
//                    qrd.setQrsj(new Date());
//                }
                if (tbQrd.getQsendder() == null || "".equals(tbQrd.getQsendder())) {
                    qrd.setQsendder(userId);
                }
                tbQrdService.updateById(qrd);
                wlgbXydLog.setXltext("转账-同步数据-修改");
                //提交对账到宜搭确认单
                tbDz(xyd1, qrd, userId, jsonObject);
            } else {
                wlgbXydLog.setXltext("转账-不需要同步数据");
            }
        } else {
            qrd.setQid(IdConfig.uuId());
            qrd.setQsfsc("0");
            qrd.setQsendder(userId);
//            qrd.setQrsj(new Date());
            tbQrdService.save(qrd);
            wlgbXydLog.setXltext("转账-同步数据-新增");
            //提交对账到宜搭确认单
            tbDz(xyd1, qrd, userId, jsonObject);
        }

        TbXyd xyd = new TbXyd();
        xyd.setXid(qrd.getQxydid());
        xyd.setStatus(9);
        xyd.setXstatu(1);
        tbXydService.updateById(xyd);

        wlgbXydLog.setXltime(new Date());
        wlgbXydLog.setXlxid(xyd.getXid());

        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLogService.save(wlgbXydLog);
    }

    /**
     * 带看新增异步
     */
    @RequestMapping(value = "dkXzTask")
    public void dkXzTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        JSONObject object = JSONObject.parseObject(datas);
        TbDk tbDk = JSONObject.toJavaObject(object, TbDk.class);
        //带看人截取
        if (tbDk.getDkuser() != null && !"".equals(tbDk.getDkuser())) {
            tbDk.setDkuser(xzjq(tbDk.getDkuser()));
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbDk.getDkuser());
            tbDk.setDkusername(employee != null ? employee.getName() : null);
        }
        if (tbDk.getFbuser() != null && !"".equals(tbDk.getFbuser())) {
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbDk.getFbuser());
            tbDk.setFbusername(employee != null ? employee.getName() : null);
        }
        tbDk.setFbtime(new Date());
        boolean b = false;
        if (tbDk.getCity() == null || "".equals(tbDk.getCity())) {
            b = true;
        }
        if (tbDk.getDkbsmc() == null || "".equals(tbDk.getDkbsmc())) {
            b = true;
            TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbDk.getDkbsid());
            if (villa != null) {
                tbDk.setDkbsmc(villa.getVname());
                tbDk.setCity(villa.getCity());
                if ("长沙".equals(villa.getCity())) {
                    tbDk.setCity(villa.getCszq() != null ? villa.getCszq() : "长沙河西");
                }
                if ("成都".equals(villa.getCity())) {
                    tbDk.setCity(villa.getCszq() != null ? villa.getCszq() : "成都温江");
                }
            }
        }
        tbDkService.save(tbDk);
        if (b) {
            Map<String, Object> map = new HashMap<>();
            map.put("city", tbDk.getCity());
            map.put("dkbsmc", tbDk.getDkbsmc());
            GatewayResult xgbdsl = xgBdSl(map, formInstId, "012412221639786136545");
        }
        WlgbCustomerLog wlgbCustomerLog = new WlgbCustomerLog();
        DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbDk.getFbuser());
        if (tbDk.getDkzt() != null && !"".equals(tbDk.getDkzt()) && "0".equals(tbDk.getDkzt())) {
            System.out.println("客户已到现场");
            wlgbCustomerLog.setCltext("发布带看，无需抢单");
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbDk.getDkuser());
            if (employee != null) {
                String str = "带看需要处理，客户到现场";
            }
            Map<String, Object> map = new HashMap<>();
            map.put("ecgdzxr", employee != null ? employee.getUserid() : null);
            map.put("dkddbh", tbDk.getDkddbh() != null && !"".equals(tbDk.getDkddbh()) ? tbDk.getDkddbh() : null);
            map.put("dkzt", tbDk.getDkzt() != null && !"".equals(tbDk.getDkzt()) ? tbDk.getDkzt() : null);
            map.put("dkuser", tbDk.getDkuser() != null && !"".equals(tbDk.getDkuser()) ? tbDk.getDkuser() : null);
            map.put("city", tbDk.getCity() != null && !"".equals(tbDk.getCity()) ? tbDk.getCity() : null);
            map.put("dkbsmc", tbDk.getDkbsmc() != null && !"".equals(tbDk.getDkbsmc()) ? tbDk.getDkbsmc() : null);
            map.put("dktime", tbDk.getDktime());
            map.put("khname", tbDk.getKhname() != null && !"".equals(tbDk.getKhname()) ? tbDk.getKhname() : null);
            map.put("dknum", tbDk.getDknum() != null && !"".equals(tbDk.getDknum()) ? tbDk.getDknum() : null);
            map.put("zdj", tbDk.getZdj() != null && !"".equals(tbDk.getZdj()) ? tbDk.getZdj() : null);
            map.put("zgj", tbDk.getZgj() != null && !"".equals(tbDk.getZgj()) ? tbDk.getZgj() : null);
            map.put("bz", tbDk.getBz() != null && !"".equals(tbDk.getBz()) ? tbDk.getBz() : null);
            map.put("khtel", tbDk.getKhtel() != null && !"".equals(tbDk.getKhtel()) ? tbDk.getKhtel() : null);
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            TbXyd tbXyd = new TbXyd();
            tbXyd.setXddbh(tbDk.getDkddbh());
            tbXyd.setXfd(tbDk.getFbusername());
            String token = null;
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("带看应用");
            YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("带看流程-新");
            GatewayResult gatewayResult = PtLcCL.lcid(employee1 != null ? employee1.getUserid() : "012412221639786136545", map, ydAppkey, ydBd, token);
            WlgbDksljl wlgbDksljl = new WlgbDksljl();
            wlgbDksljl.setId(IdConfig.uuId());
            wlgbDksljl.setTjsj(new Date());
            if (gatewayResult != null) {
                wlgbDksljl.setSlid(gatewayResult.getResult());
            }
            wlgbDksljl.setBz("带看流程");
            wlgbDksljl.setJsr(employee != null ? employee.getUserid() : null);
            wlgbDksljl.setDdid(tbDk.getId().toString());
            if (employee1 != null) {
                wlgbDksljl.setTjrid(employee1.getUserid());
                wlgbDksljl.setTjr(employee1.getName());
            }
            wlgbDksljlService.save(wlgbDksljl);
        } else {
            wlgbCustomerLog.setCltext("发布带看，需抢单");
            String title = "有新的带看待抢";
            TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbDk.getDkbsid());
            Map<String, Object> map = new HashMap<>();
            map.put("jid", villa != null ? villa.getJid() : null);
            QyUtil qyUtil = weiLianService.queryQyUtil(map);
            String context = "<font color=#ff0303 size=5>" + (qyUtil != null ? qyUtil.getJname() : null) + "-" + (villa != null ? villa.getVname() : null) + "</font>";
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            context += "\n\n> ##### 踩点时间：" + df.format(tbDk.getDktime());
            context += "\n\n> ###### ———————————————————";
            context += " \n\n> ### [有新发布的带看待抢(点击查看详情)](https://jztdpp.aliwork.com/APP_ELLJ83Q1ILGCWPIK6RJ0/workbench/FORM-5L666481U0ZF4LNTAOUU873H207P3BH0E5ZOL72?cs=" + (tbDk != null ? tbDk.getCity() : "") + ")";
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar calendar1 = Calendar.getInstance();
            context += "\n\n> ###### ———————————————————" + " \n\n> ###### " + df2.format(calendar1.getTime()) + "     [发送]";
            Map<String, Object> map1 = new HashMap<>();
            String s = tbDk.getCity() != null && !"".equals(tbDk.getCity()) ? tbDk.getCity() : null;
            map1.put("city", s);
            List<DkBbJqr> list = weiLianService.queryDkBb(map1);
            String text = context;
            list.forEach(l -> DingDingUtil.sendMark(l != null ? l.getWebhook() : null, l != null ? l.getSecret() : null, title, text, null, true));
        }
        wlgbCustomerLog.setCltime(new Date());
        wlgbCustomerLog.setClid(IdConfig.uuId());
        wlgbCustomerLog.setClcid(tbDk.getDkddbh());
        wlgbCustomerLog.setCluserid(employee1 != null ? employee1.getUserid() : null);
        wlgbCustomerLog.setClname(employee1 != null ? employee1.getName() : null);
        wlgbCustomerLogService.save(wlgbCustomerLog);
        WlgbBdjl wlgbBdjl1 = wlgbBdjlService.queryByXidAndBz(tbDk.getDkddbh(), "带看表单提交");
        if (wlgbBdjl1 == null) {
            WlgbBdjl wlgbBdjl = new WlgbBdjl();
            wlgbBdjl.setSlid(formInstId);
            wlgbBdjl.setId(IdConfig.uuId());
            wlgbBdjl.setXid(tbDk.getDkddbh());
            wlgbBdjl.setTime(new Date());
            wlgbBdjl.setBz("带看表单提交");
            wlgbBdjl.setUserid(tbDk.getFbuser());
            wlgbBdjlService.save(wlgbBdjl);
        }
    }

    /**
     * 带看流程提交异步
     */
    @RequestMapping(value = "tjDkLcTask")
    public void tjDkLcTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject object = JSONObject.parseObject(datas);
        TbDk tbDk = JSON.toJavaObject(object, TbDk.class);
        TbDk tbDk1 = weiLianService.queryDkXq(tbDk.getDkddbh());
        Map<String, Object> map = new HashMap<>();
        TbDk tbDk2 = new TbDk();
        tbDk2.setId(tbDk1.getId());
        tbDk2.setDksfcg(tbDk.getDksfcg());
        map.put("dksfcg", tbDk2.getDksfcg());
        if ("1".equals(tbDk.getDksfcg())) {
            tbDk2.setDksbyy(tbDk.getDksbyy());
            map.put("dksbyy", tbDk2.getDksbyy());
            switch (tbDk.getDksbyy()) {
                case "0":
                    tbDk2.setSbyyjg(tbDk.getSbyyjg());
                    map.put("sbyyjg", tbDk2.getSbyyjg());
                    break;
                case "1":
                    tbDk2.setSbyydz(tbDk.getSbyydz());
                    map.put("sbyydz", tbDk2.getSbyydz());
                    break;
                case "2":
                    tbDk2.setSbyycd(tbDk.getSbyycd());
                    map.put("sbyycd", tbDk2.getSbyycd());
                    break;
                case "3":
                    tbDk2.setSbyycc(tbDk.getSbyycc());
                    map.put("sbyycc", tbDk2.getSbyycc());
                    break;
                case "4":
                    tbDk2.setSbyysj(tbDk.getSbyysj());
                    map.put("sbyysj", tbDk2.getSbyysj());
                    break;
                case "5":
                    tbDk2.setSbyyth(tbDk.getSbyyth());
                    map.put("sbyyth", tbDk2.getSbyyth());
                    break;
                default:
                    tbDk2.setSbyyqt(tbDk.getSbyyqt());
                    map.put("sbyyqt", tbDk2.getSbyyqt());
                    break;
            }
        } else {
            tbDk2.setKhjbxx(tbDk.getKhjbxx());
            map.put("khjbxx", tbDk2.getKhjbxx());
            if (tbDk.getXydtp() != null && !"".equals(tbDk.getXydtp())) {
                tbDk2.setXydtp(ydTpScJq(tbDk.getXydtp()));
                map.put("xydtp", YdConfig.setTpList(tbDk2.getXydtp(), "协议单图片"));
            }
        }
        tbDkService.updateById(tbDk2);
        WlgbCustomerLog wlgbCustomerLog = new WlgbCustomerLog();
        wlgbCustomerLog.setCltime(new Date());
        wlgbCustomerLog.setClid(IdConfig.uuId());
        wlgbCustomerLog.setClcid(tbDk.getDkddbh());
        wlgbCustomerLog.setCltext("提交带看结果");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbDk1.getDkuser() != null ? tbDk1.getDkuser() : tbDk1.getQduser());
        wlgbCustomerLog.setCluserid(ding != null ? ding.getUserid() : null);
        wlgbCustomerLog.setClname(ding != null ? ding.getName() : null);
        wlgbCustomerLogService.save(wlgbCustomerLog);
        WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(tbDk1.getDkddbh(), "带看表单提交");
        if (wlgbBdjl != null) {
            xgBdSl(map, wlgbBdjl.getSlid(), ("0".equals(tbDk1.getDkzt()) ? tbDk1.getDkuser() : tbDk1.getQduser()));
        }
    }

    @RequestMapping(value = "scDkTask")
    public void scDkTask(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        String userid = request.getParameter("userid");
        TbDk tbDk = weiLianService.queryDkXq(ddbh);

        TbDk tbDk1 = new TbDk();
        tbDk1.setId(tbDk.getId());
        tbDk1.setSfsc("1");
        tbDk1.setSctime(new Date());
        tbDk1.setScuser(userid);
        tbDkService.updateById(tbDk1);

        WlgbCustomerLog wlgbCustomerLog = new WlgbCustomerLog();
        wlgbCustomerLog.setCltime(new Date());
        wlgbCustomerLog.setClid(IdConfig.uuId());
        wlgbCustomerLog.setClcid(tbDk.getDkddbh());
        wlgbCustomerLog.setCltext("删除带看订单");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        wlgbCustomerLog.setCluserid(ding != null ? ding.getUserid() : null);
        wlgbCustomerLog.setClname(ding != null ? ding.getName() : null);
        wlgbCustomerLogService.save(wlgbCustomerLog);

        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("带看应用");

        WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(tbDk.getDkddbh(), "带看表单提交");
        if (wlgbBdjl != null) {
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = DingBdLcConfig.scBdSl(token, ydAppkey, "012412221639786136545", wlgbBdjl.getSlid());
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                e.printStackTrace();
            }
            System.out.println(gatewayResult);
            wlgbBdjlService.removeById(wlgbBdjl.getId());
        }
        WlgbDksljl wlgbDksljl = wlgbDksljlService.queryByBzAndXid("带看流程", tbDk.getId().toString());

        if (wlgbDksljl != null) {
            TbXyd tbXyd = new TbXyd();
            tbXyd.setXddbh(tbDk.getDkddbh());
            tbXyd.setXfd(tbDk.getFbusername());
            scLcSl(wlgbDksljl.getSlid(), token, ydAppkey);
            wlgbDksljlService.removeById(wlgbDksljl.getId());
        }
    }

    /**
     * 修改带看异步
     */
    @RequestMapping(value = "xgDkTask")
    public void xgDkTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("userid");
        JSONObject object = JSONObject.parseObject(datas);
        TbDk tbDk = JSON.toJavaObject(object, TbDk.class);

        TbDk tbDk1 = weiLianService.queryDkXq(tbDk.getDkddbh());
        if (tbDk.getQduser() != null && !"".equals(tbDk.getQduser())) {
            tbDk.setQduser(xzjq(tbDk.getQduser()));
        }
        if (tbDk.getDkuser() != null && !"".equals(tbDk.getDkuser())) {
            tbDk.setDkuser(xzjq(tbDk.getDkuser()));
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbDk.getDkuser());
            tbDk.setDkusername(employee != null ? employee.getName() : null);
        }

        //带看协议单图片
        if (tbDk.getXydtp() != null && !"".equals(tbDk.getXydtp()) && tbDk.getXydtp().length() > 0) {
            tbDk.setXydtp(ydTpScJq(tbDk.getXydtp()));
        }

        tbDk.setFbuser(null);
        tbDk.setId(tbDk1.getId());
        tbDk.setXguser(userid);
        tbDk.setXgtime(new Date());
        tbDkService.updateById(tbDk);
        Map<String, Object> map = new HashMap<>();
        map.put("dkddbh", tbDk.getDkddbh() != null && !"".equals(tbDk.getDkddbh()) ? tbDk.getDkddbh() : null);
        map.put("dkzt", tbDk.getDkzt() != null && !"".equals(tbDk.getDkzt()) ? tbDk.getDkzt() : null);
        map.put("qduser", tbDk.getQduser() != null && !"".equals(tbDk.getQduser()) ? tbDk.getQduser() : null);
        map.put("dkuser", tbDk.getDkuser() != null && !"".equals(tbDk.getDkuser()) ? tbDk.getDkuser() : null);
        map.put("city", tbDk.getCity() != null && !"".equals(tbDk.getCity()) ? tbDk.getCity() : null);
        map.put("dkbsmc", tbDk.getDkbsmc() != null && !"".equals(tbDk.getDkbsmc()) ? tbDk.getDkbsmc() : null);
        map.put("dktime", tbDk.getDktime());
        map.put("khname", tbDk.getKhname() != null && !"".equals(tbDk.getKhname()) ? tbDk.getKhname() : null);
        map.put("khtel", tbDk.getKhtel() != null && !"".equals(tbDk.getKhtel()) ? tbDk.getKhtel() : null);
        map.put("dknum", tbDk.getDknum() != null && !"".equals(tbDk.getDknum()) ? tbDk.getDknum() : null);
        map.put("zdj", tbDk.getZdj() != null && !"".equals(tbDk.getZdj()) ? tbDk.getZdj() : null);
        map.put("zgj", tbDk.getZgj() != null && !"".equals(tbDk.getZgj()) ? tbDk.getZgj() : null);
        map.put("bz", tbDk.getBz() != null && !"".equals(tbDk.getBz()) ? tbDk.getBz() : null);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("带看应用");
        WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid("带看流程", tbDk.getId().toString());
        if (dksljl != null) {
            TbXyd tbXyd = new TbXyd();
            tbXyd.setXfd(tbDk.getFbusername());
            tbXyd.setXddbh(tbDk.getDkddbh());

            GatewayResult gatewayResult = null;
            try {
                gatewayResult = xgLcSl(map, dksljl.getSlid(), "012412221639786136545", token, ydAppkey);
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                e.printStackTrace();
            }
        } else {
            if (tbDk.getSfjd() != null && !"".equals(tbDk.getSfjd()) && "0".equals(tbDk.getSfjd())) {
                TbXyd tbXyd = new TbXyd();
                tbXyd.setXfd(tbDk.getFbusername());
                tbXyd.setXddbh(tbDk.getDkddbh());
                YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("带看流程-新");
                DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbDk1.getFbuser());
                GatewayResult gatewayResult = PtLcCL.lcid(dingdingEmployee != null ? dingdingEmployee.getUserid() : "012412221639786136545", map, ydAppkey, ydBd, token);
//                GatewayResult gatewayResult = queryYdLcXzRc(tbXyd, "带看流程", map, "带看流程", tbDk.getFbuser() != null ? tbDk.getFbuser() : "012412221639786136545", dingkey);
                WlgbDksljl wlgbDksljl = new WlgbDksljl();
                wlgbDksljl.setId(IdConfig.uuId());
                wlgbDksljl.setTjsj(new Date());
                if (gatewayResult != null) {
                    wlgbDksljl.setSlid(gatewayResult.getResult());
                }
                wlgbDksljl.setBz("带看流程");
                wlgbDksljl.setDdid(tbDk.getId().toString());
                wlgbDksljl.setJsr(userid);
                DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbDk.getFbuser());
                if (employee1 != null) {
                    wlgbDksljl.setTjrid(employee1.getUserid());
                    wlgbDksljl.setTjr(employee1.getName());
                }
                wlgbDksljlService.save(wlgbDksljl);
            }
        }
        WlgbCustomerLog wlgbCustomerLog = new WlgbCustomerLog();
        wlgbCustomerLog.setCltime(new Date());
        wlgbCustomerLog.setClid(IdConfig.uuId());
        wlgbCustomerLog.setClcid(tbDk.getDkddbh());
        wlgbCustomerLog.setCltext("修改带看订单");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        wlgbCustomerLog.setCluserid(ding != null ? ding.getUserid() : null);
        wlgbCustomerLog.setClname(ding != null ? ding.getName() : null);
        wlgbCustomerLogService.save(wlgbCustomerLog);
    }

    /**
     * 同步至宜搭对账表单
     *
     * @param xyd   协议单数据
     * @param tbQrd 确认单数据
     */
    public void tbDz(TbXyd xyd, TbQrd tbQrd, String userid, JSONObject jsonObject) throws Exception {
        String xydUrl = dzXydCl(xyd);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        //新增数据表到宜搭---对账
        WlgbBdjl wlgbBdjl2 = wlgbBdjlService.queryByXidAndBz(xyd.getXid(), "对账表单提交");
        DingdingEmployee employee2 = weiLianService.queryDingDingByName(tbQrd.getQzbdz());
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(xyd.getXbsmc());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("对账");
        log.info("*************************同步到宜搭**************************{}", tbQrd);
        WlgbBdjl wlgbBdjl = DzBdConfig.tbDz(xyd, tbQrd, jsonObject, villa, wlgbBdjl2, xydUrl, employee2, dingkey, ydAppkey, ydBd, ding);
        if (wlgbBdjl != null) {
            if (wlgbBdjl2 != null) {
                wlgbBdjlService.updateById(wlgbBdjl);
            } else {
                wlgbBdjlService.save(wlgbBdjl);
            }
        }
//        if (tbQrd.getQdcxf() > 0) {
//            WlgbOrderCyjl wlgbOrderCyjl = new WlgbOrderCyjl();
//            wlgbOrderCyjl.setDdbh(xyd.getXddbh());
//            wlgbOrderCyjl.setCsfsc(0);
//            List<WlgbOrderCyjl> list = wlgbOrderCyjlService.queryListByOrderCyJl(wlgbOrderCyjl);
//            JSONArray cyList = jsonObject.getJSONArray("tableField_lp6be3gd");
//            boolean b = false;
//            for (WlgbOrderCyjl wlgbOrderCyjl1 : list) {
//                for (Object l : cyList) {
//                    JSONObject jsonObject1 = (JSONObject) l;
//                    if (wlgbOrderCyjl1.getCymc().equals(jsonObject1.getString("cymc")) && wlgbOrderCyjl1.getCyje().equals(jsonObject1.getDouble("cyje")) && wlgbOrderCyjl1.getCysl().equals(jsonObject1.getInteger("cygmsl")) && wlgbOrderCyjl1.getCyjg().equals(jsonObject1.getDouble("cysj"))) {
//                        if (!wlgbOrderCyjl1.getCysx().equals(jsonObject1.getString("cysx"))) {
//                            b = true;
//                        }
//                    }
//                }
//            }
//            if (b) {
//                List<JSONObject> list1 = new ArrayList<>();
//                for (WlgbOrderCyjl wlgbOrderCyjl1 : list) {
//                    for (Object l : cyList) {
//                        JSONObject jsonObject1 = (JSONObject) l;
//                        if (wlgbOrderCyjl1.getCymc().equals(jsonObject1.getString("cymc")) && wlgbOrderCyjl1.getCyje().equals(jsonObject1.getDouble("cyje")) && wlgbOrderCyjl1.getCysl().equals(jsonObject1.getInteger("cygmsl")) && wlgbOrderCyjl1.getCyjg().equals(jsonObject1.getDouble("cysj"))) {
//                            JSONObject jsonObject2 = new JSONObject();
//                            jsonObject2.put("cymc", wlgbOrderCyjl1.getCymc());
//                            jsonObject2.put("selectField_lnldd87u", jsonObject1.getString("cysx"));
//                            DingdingEmployee dingdingEmployee = weiLianService.queryDingDingByName(wlgbOrderCyjl1.getCycjr());
//                            if (dingdingEmployee != null) {
//                                jsonObject2.put("employeeField_lmuespiw", dingdingEmployee.getUserid());
//                            }
//                            jsonObject2.put("cysj", wlgbOrderCyjl1.getCyjg());
//                            jsonObject2.put("cygmsl", wlgbOrderCyjl1.getCysl());
//                            jsonObject2.put("cyje", wlgbOrderCyjl1.getCyje());
//                            jsonObject2.put("cycb", wlgbOrderCyjl1.getCycb());
//                            jsonObject2.put("cylb", wlgbOrderCyjl1.getCylb());
//                            jsonObject2.put("bm", wlgbOrderCyjl1.getCyjq());
//                            list1.add(jsonObject2);
//                        }
//                    }
//                }
//                String token = null;
//                try {
//                    token = DingToken.token(dingkey);
//                } catch (ApiException e) {
//                    e.printStackTrace();
//                }
//                WlgbBdjl wlgbBdjl1 = wlgbBdjlService.queryByXidAndBz(xyd.getXid(), "下单表单提交");
//                //容错机制修改表单内容
//                JSONObject json = new JSONObject();
//                json.put("cyzbd", list1);
//                GatewayResult gatewayResult = null;
//                try {
//                    gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, wlgbBdjl1.getSlid(), json.toJSONString());
//                } catch (Exception e) {
//                    gatewayResult = new GatewayResult();
//                    e.printStackTrace();
//                }
//            }
//        }
    }

    /**
     * 删除流程实例
     *
     * @param slid     实例id
     * @param token    美团token
     * @param ydAppkey 宜搭配置
     * @return 返回结果
     */
    public GatewayResult scLcSl(String slid, String token, YdAppkey ydAppkey) {
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.scLcSl(token, ydAppkey, "012412221639786136545", slid);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return gatewayResult;
    }

    /**
     * 进场及消费流程处理
     *
     * @param l 协议单对象
     */
    public void jcJxf(TbXyd l, DingdingEmployee ding) {
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(l.getXbsmc());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String xydUrl = null;
        try {
            xydUrl = dzXydCl(l);
        } catch (Exception e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("进场及消费");
        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
        WlgbDksljl dksljl = wlgbDksljlService.queryByBzAndXid("进场及消费", l.getXid());
        TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(l.getXid());
        WlgbDksljl wlgbDksljl = JcJXfLcConfig.jcJXfLc(l, villa, xydUrl, dingkey, employee, ydAppkey, ydBd, ding, dksljl, tbYddXyd != null ? "是" : "否");
        if (wlgbDksljl != null) {
            wlgbDksljlService.save(wlgbDksljl);
        }
    }

    /**
     * 修改流程实例
     *
     * @param id     要更新的流程数据ID
     * @param map    表单内容
     * @param userid 用户id
     */
    public GatewayResult xgLcSl(Map<String, Object> map, String id, String userid, String token, YdAppkey ydAppkey) {

        return LcConfig.gxLcSl(userid, map, ydAppkey, id, token);
    }

    /**
     * 宜搭图片上传截取地址
     *
     * @param json 上传的json图片地址
     * @return 截取后的图片地址
     */
    public String ydTpScJq(String json) {
        JSONArray objects = JSONObject.parseArray(json);
        if (objects.size() > 0) {
            String previewUrl = objects.getJSONObject(0).getString("previewUrl");
            if (previewUrl == null) {
                return null;
            } else {
                if (previewUrl.length() > 8) {
                    String substring = previewUrl.substring(0, 8);
                    if ("https://".equals(substring)) {
                        return previewUrl;
                    } else {
                        return "https://jztdpp.aliwork.com" + previewUrl;
                    }
                } else {
                    return "https://jztdpp.aliwork.com" + previewUrl;
                }
            }
        }
        return null;
    }

    //链接url下载图片
    private static void downloadPicture(String urlList, String path) {
        URL url = null;
        int z = 0;
        try {
            url = new URL(urlList);
            DataInputStream dataInputStream = new DataInputStream(url.openStream());

            FileOutputStream fileOutputStream = new FileOutputStream(new File(path));
            ByteArrayOutputStream output = new ByteArrayOutputStream();

            byte[] buffer = new byte[1024];
            int length;

            while ((length = dataInputStream.read(buffer)) > 0) {
                output.write(buffer, 0, length);
            }
            fileOutputStream.write(output.toByteArray());
//            if (fileOut)
            dataInputStream.close();
            fileOutputStream.close();
        } catch (MalformedURLException e) {
            z = 1;
            e.printStackTrace();
        } catch (IOException e) {
            z = 1;
            e.printStackTrace();
        }
    }

    /**
     * 阿里云图片上传
     */
    public String alyTp(String path) {
        String upload = null;
        try {
            File file = new File(path);
            FileInputStream fileInputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
            fileInputStream.close();
            upload = ossFileService.upload(multipartFile);
            if (upload != null && !"".equals(upload)) {
                upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
            }
            file.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return upload;
    }

    /**
     * crm新增
     */
    @RequestMapping(value = "saveCrmQbKhTask")
    public void saveCrmQbKhTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String slid = request.getParameter("slid");
        String bdid = request.getParameter("bdid");
        if (datas == null) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String xz = jsonObject.getString("xz");
        CrmQbkh qbkh = JSONObject.toJavaObject(jsonObject, CrmQbkh.class);
//        if ("1".equals(xz)) {
//            //修改表单ID给表单添加qid
//            YdAppkey ydAppkey = ydAppkeyService.getOne(
//                    new QueryWrapper<YdAppkey>()
//                            .lambda()
//                            .eq(YdAppkey::getBz, "办公平台")
//            );
//            YDLC ydlc = new YDLC();
//            CrmQbkh one = crmQbkhService.getOne(
//                    new QueryWrapper<CrmQbkh>()
//                            .lambda()
//                            .eq(CrmQbkh::getCrmbh, qbkh.getCrmbh())
//            );
//            //查询slid
//            JSONObject jsonObject2 = new JSONObject();
//            jsonObject2.put("crmbh", qbkh.getCrmbh());
//            String slid2 = ydlc.hqId("012412221639786136545", ydAppkey.getAppkey(), ydAppkey.getToken(), "FORM-NT766881WWDV73OD5QI52DV0G5ON3HZ9OJ4WKI", jsonObject2.toJSONString(), "1", "1");
//            JSONObject object1 = JSONObject.parseObject(slid2);
//            JSONObject jsonObject3 = object1.getJSONObject("result");
//            if (jsonObject3 != null) {
//                JSONArray jsonArray = jsonObject3.getJSONArray("data");
//                if (jsonArray.size() > 0) {
//                    String formInstId1 = (String) jsonArray.get(0);
//                    qbkh.setSlid(formInstId1);
//                    crmQbkhService.updateById(qbkh);
//                }
//            }
//            return Result.OK("不新增");
//        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //创建时间转换
        if (qbkh.getQcjsj() != null && !"".equals(qbkh.getQcjsj())) {
            Long cjsj = Long.parseLong(qbkh.getQcjsj());  //获取当前时间戳
            String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
            qbkh.setQcjsj(cjsjsd);

        }
        //修改时间转换
        if (qbkh.getQxgsj() != null && !"".equals(qbkh.getQxgsj())) {
            Long xgsj = Long.parseLong(qbkh.getQxgsj());  //获取当前时间戳
            String xgsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(xgsj))));      // 时间戳转换成时间
            qbkh.setQxgsj(xgsjsd);
        }
        //分配时间转换
        if (qbkh.getQfpsj() != null && !"".equals(qbkh.getQfpsj())) {
            Long fpsj = Long.parseLong(qbkh.getQfpsj());  //获取当前时间戳
            String fpsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(fpsj))));      // 时间戳转换成时间
            qbkh.setQfpsj(fpsjsd);
        }
        //捞取时间转换
        if (qbkh.getQlqsj() != null && !"".equals(qbkh.getQlqsj())) {
            Long lqsj = Long.parseLong(qbkh.getQlqsj());  //获取当前时间戳
            String lqsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(lqsj))));      // 时间戳转换成时间
            qbkh.setQlqsj(lqsjsd);
        }
        //移交时间转换
        if (qbkh.getQyjsj() != null && !"".equals(qbkh.getQyjsj())) {
            Long yjsj = Long.parseLong(qbkh.getQyjsj());  //获取当前时间戳
            String yjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(yjsj))));      // 时间戳转换成时间
            qbkh.setQyjsj(yjsjsd);
        }
        //删除时间转换
        if (qbkh.getQscsj() != null && !"".equals(qbkh.getQscsj())) {
            Long scsj = Long.parseLong(qbkh.getQscsj());  //获取当前时间戳
            String scsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(scsj))));      // 时间戳转换成时间
            qbkh.setQscsj(scsjsd);
        }
        //客户预订时间转换
        if (qbkh.getQkhydsj() != null && !"".equals(qbkh.getQkhydsj())) {
            Long khydsj = Long.parseLong(qbkh.getQkhydsj());  //获取当前时间戳
            String khydsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(khydsj))));      // 时间戳转换成时间
            qbkh.setQkhydsj(khydsjsd);
        }
        //根据钉钉id查询姓名
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(qbkh.getQfzrid());
        qbkh.setQfzr(ding != null ? ding.getName() : null);
        qbkh.setSlid(slid);
        crmQbkhService.save(qbkh);
        //发送添加成功通知
        xzCrmGzTz(qbkh.getQcjr(), qbkh.getQkhwxh(), qbkh.getQfzr(), qbkh.getQkhdh(), qbkh.getQcjrid(), qbkh.getQfzrid(), qbkh);

    }

    /**
     * crm信息修改，添加修改记录表
     */
    @RequestMapping(value = "updateCrmQbKhTask")
    public void updateCrmQbKhTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        //查询没有修改前的数据
        CrmQbkh crmQbkh = JSONObject.toJavaObject(jsonObject, CrmQbkh.class);
        CrmQbkh crmQbkh1 = crmQbkhService.queryByCrmBh(crmQbkh.getCrmbh());
        if (crmQbkh1 == null) {
            return;
        }

        CrmXgjlb qbkh = JSONObject.toJavaObject(jsonObject, CrmXgjlb.class);
        String xxtz = jsonObject.getString("xxtz");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //创建时间转换
        if (qbkh.getQcjsj() != null && !"".equals(qbkh.getQcjsj())) {
            Long cjsj = Long.parseLong(qbkh.getQcjsj());  //获取当前时间戳
            String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
            qbkh.setQcjsj(cjsjsd);

        }
        //修改时间转换
        if (qbkh.getQxgsj() != null && !"".equals(qbkh.getQxgsj())) {
            Long xgsj = Long.parseLong(qbkh.getQxgsj());  //获取当前时间戳
            String xgsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(xgsj))));      // 时间戳转换成时间
            qbkh.setQxgsj(xgsjsd);
        }
        //分配时间转换
        if (qbkh.getQfpsj() != null && !"".equals(qbkh.getQfpsj())) {
            Long fpsj = Long.parseLong(qbkh.getQfpsj());  //获取当前时间戳
            String fpsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(fpsj))));      // 时间戳转换成时间
            qbkh.setQfpsj(fpsjsd);
        }
        //捞取时间转换
        if (qbkh.getQlqsj() != null && !"".equals(qbkh.getQlqsj())) {
            Long lqsj = Long.parseLong(qbkh.getQlqsj());  //获取当前时间戳
            String lqsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(lqsj))));      // 时间戳转换成时间
            qbkh.setQlqsj(lqsjsd);
        }
        //移交时间转换
        if (qbkh.getQyjsj() != null && !"".equals(qbkh.getQyjsj())) {
            Long yjsj = Long.parseLong(qbkh.getQyjsj());  //获取当前时间戳
            String yjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(yjsj))));      // 时间戳转换成时间
            qbkh.setQyjsj(yjsjsd);
        }
        //删除时间转换
        if (qbkh.getQscsj() != null && !"".equals(qbkh.getQscsj())) {
            Long scsj = Long.parseLong(qbkh.getQscsj());  //获取当前时间戳
            String scsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(scsj))));      // 时间戳转换成时间
            qbkh.setQscsj(scsjsd);
        }
        //客户预订时间转换
        if (qbkh.getQkhydsj() != null && !"".equals(qbkh.getQkhydsj())) {
            Long khydsj = Long.parseLong(qbkh.getQkhydsj());  //获取当前时间戳
            String khydsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(khydsj))));      // 时间戳转换成时间
            qbkh.setQkhydsj(khydsjsd);
        }
        //根据钉钉id查询姓名
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(qbkh.getQfzrid());
        if (ding != null) {
            qbkh.setQfzr(ding.getName());
            //设置负责人
            crmQbkh.setQfzr(ding.getName());
        }
        //修改全部客户表
        crmQbkh.setQxgsj(qbkh.getQxgsj());
        crmQbkh.setQkhydsj(qbkh.getQkhydsj());
        crmQbkh.setQscsj(qbkh.getQscsj());
        crmQbkh.setQyjsj(qbkh.getQyjsj());
        crmQbkh.setQlqsj(qbkh.getQlqsj());
        crmQbkh.setQfpsj(qbkh.getQfpsj());
        crmQbkh.setQcjsj(qbkh.getQcjsj());
        crmQbkh.setQid(crmQbkh1.getQid());
        crmXgjlbService.save(qbkh);
        crmQbkhService.updateById(crmQbkh);
        //查询修改后的数据
        CrmQbkh crmQbkh2 = crmQbkhService.queryByCrmBh(crmQbkh.getCrmbh());
        if ("删除".equals(xxtz)) {
            scCrmGzTz(crmQbkh2.getQscr(), crmQbkh2.getQkhwxh(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), crmQbkh2.getQscrid(), crmQbkh2.getQfzrid());
        } else if ("公海池".equals(xxtz)) {
            ghcCrmGzTz(crmQbkh2.getQxgr(), crmQbkh2.getQkhwxh(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), crmQbkh2.getQxgrid());
        } else if ("分配".equals(xxtz)) {
            //根据钉钉id查询姓名
            DingdingEmployee fpr = weiLianDdXcxService.queryDingdingEmployeeByUserId(crmQbkh2.getQfpuserid());
            fpCrmGzTz(fpr.getName(), crmQbkh1.getQfzr(), crmQbkh1.getQfzrid(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), crmQbkh2.getQfpuserid(), crmQbkh2.getQfzrid(), crmQbkh2.getQkhwxh());
        } else if ("捞取".equals(xxtz)) {
            lqCrmGzTz(crmQbkh2.getQxgr(), crmQbkh2.getQkhwxh(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), crmQbkh2.getQxgrid(), crmQbkh2.getQfzrid());
        } else if ("移交".equals(xxtz)) {
            //根据钉钉id查询姓名
            DingdingEmployee fpr = weiLianDdXcxService.queryDingdingEmployeeByUserId(crmQbkh2.getQyjuserid());
            yjCrmGzTz(fpr.getName(), crmQbkh1.getQfzr(), crmQbkh1.getQfzrid(), crmQbkh2.getQkhwxh(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), crmQbkh2.getQyjuserid(), crmQbkh2.getQfzrid(), crmQbkh2);
        } else if ("修改".equals(xxtz)) {
            xgCrmGzTz(crmQbkh2.getQxgr(), crmQbkh2.getQkhwxh(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), crmQbkh2.getQxgrid(), crmQbkh2.getQfzrid(), crmQbkh2);
        }
    }

    /**
     * 流程申请修改客单价金额异步
     */
    @RequestMapping(value = "kdjSqXgJeTask")
    public void kdjSqXgJe(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("userid");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null || jsonObject.size() == 0) {
            return;
        }
        TbKdjFd tbKdjFd = JSONObject.toJavaObject(jsonObject, TbKdjFd.class);
        Date rq = jsonObject.getDate("rq");
        String cc = jsonObject.getString("cc");
        String bj = jsonObject.getString("bj");
        String jj = jsonObject.getString("jj");
        String je = tbKdjFd.getJe();


        if (cc.contains("全天")) {
            Double BJJ = Double.valueOf(Math.round((Double.valueOf(bj) / 2)));
            bj = String.valueOf(BJJ);
            Double JJ = Double.valueOf(Math.round((Double.valueOf(jj) / 2)));
            jj = String.valueOf(JJ);
            Double JE = Double.valueOf(Math.round((Double.valueOf(je) / 2)));

            if ("全天1".equals(cc)) {
                for (int i = 0; i < 2; i++) {
                    if (i == 0) {
                        cc = "白";
                    } else {
                        cc = "晚";
                    }
                    tbKdjFd.setJe(JE.toString());
                    if ("白".equals(cc)) {
                        Calendar c1 = Calendar.getInstance();
                        c1.setTime(rq);
                        c1.set(Calendar.MINUTE, 0);
                        c1.set(Calendar.SECOND, 0);
                        c1.set(Calendar.MILLISECOND, 0);
                        c1.add(Calendar.HOUR, 10);
                        tbKdjFd.setXjctime(c1.getTime());
                        Calendar c2 = Calendar.getInstance();
                        c2.setTime(rq);
                        c2.set(Calendar.MINUTE, 0);
                        c2.set(Calendar.SECOND, 0);
                        c2.set(Calendar.MILLISECOND, 0);
                        c2.add(Calendar.HOUR, 17);
                        tbKdjFd.setXtctime(c2.getTime());
                    } else {
                        Calendar c1 = Calendar.getInstance();
                        c1.setTime(rq);
                        c1.set(Calendar.MINUTE, 0);
                        c1.set(Calendar.SECOND, 0);
                        c1.set(Calendar.MILLISECOND, 0);
                        c1.add(Calendar.HOUR, 18);
                        tbKdjFd.setXjctime(c1.getTime());
                        Calendar c2 = Calendar.getInstance();
                        c2.setTime(rq);
                        c2.set(Calendar.DAY_OF_MONTH, c2.get(Calendar.DAY_OF_MONTH) + 1);
                        c2.set(Calendar.MINUTE, 0);
                        c2.set(Calendar.SECOND, 0);
                        c2.set(Calendar.MILLISECOND, 0);
                        c2.add(Calendar.HOUR, 8);
                        tbKdjFd.setXtctime(c2.getTime());
                    }
                    tbKdjFd.setCc(cc);
                    TbKdj tbKdj = new TbKdj();
                    tbKdj.setVid(tbKdjFd.getVid());
                    tbKdj.setVname(tbKdjFd.getVname());
                    tbKdj.setRq(tbKdjFd.getRq());
                    tbKdj.setCc(tbKdjFd.getCc());
                    tbKdj.setXjctime(tbKdjFd.getXjctime());
                    tbKdj.setXtctime(tbKdjFd.getXtctime());
                    tbKdj.setJe(tbKdjFd.getJe());
                    tbKdj.setJjje(jj);
                    tbKdj.setBjje(bj);
                    TbKdj kdj = tbKdjService.queryByBsAndCcAndRq(tbKdj.getVid(), tbKdj.getCc(), tbKdj.getRq());

                    TbKdjFdCzjl tbKdjFdCzjl = new TbKdjFdCzjl();
                    BeanUtils.copyProperties(tbKdjFd, tbKdjFdCzjl);
                    if (kdj == null) {
                        tbKdjFdCzjl.setYje("0");
                        tbKdjService.save(tbKdj);
                    } else {
                        if (kdj.getJe() != null && !"".equals(kdj.getJe())) {
                            tbKdj.setYje(kdj.getJe());
                            tbKdjFdCzjl.setYje(kdj.getJe());
                        } else {
                            tbKdjFdCzjl.setYje("0");
                        }

                        tbKdj.setId(kdj.getId());
                        try {
                            double je1 = Double.parseDouble(tbKdj.getJe());
                            double yje1 = Double.parseDouble(tbKdj.getYje());
                            tbKdj.setStatu(je1 > yje1 ? "1" : je1 < yje1 ? "2" : "0");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        tbKdjService.updateById(tbKdj);

                    }
                    tbKdjFdCzjl.setTime(new Date());
                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
                    tbKdjFdCzjl.setUserid(employee != null ? employee.getUserid() : null);
                    tbKdjFdCzjl.setName(employee != null ? employee.getName() : null);
                    //底价新增
                    tbKdjFdCzjlService.save(tbKdjFdCzjl);
                    //基价
                    tbKdjFd.setJe(jj);
                    TbKdjJzxCzjl tbKdjJzxCzjl = new TbKdjJzxCzjl();
                    BeanUtils.copyProperties(tbKdjFd, tbKdjJzxCzjl);


                    if (kdj == null) {
                        tbKdjJzxCzjl.setYje("0");
                    } else {
                        tbKdjFd.setYje(kdj.getJjje());
                        tbKdj.setId(kdj.getId());
                        if (kdj.getJjje() != null && !"".equals(kdj.getJjje())) {
                            tbKdj.setJjyje(kdj.getJjje());
                            tbKdjJzxCzjl.setYje(kdj.getJjje());
                        } else {
                            tbKdjJzxCzjl.setYje("0");
                        }
                        tbKdjService.updateById(tbKdj);
                    }

                    tbKdjJzxCzjl.setTime(new Date());
                    tbKdjJzxCzjl.setUserid(employee != null ? employee.getUserid() : null);
                    tbKdjJzxCzjl.setName(employee != null ? employee.getName() : null);
                    //基准线新增
                    tbKdjJzxCzjlService.save(tbKdjJzxCzjl);
                    //报价新增
                    tbKdjFd.setJe(bj);
                    TbKdjZdjCzjl tbKdjZdjCzjl = new TbKdjZdjCzjl();
                    BeanUtils.copyProperties(tbKdjFd, tbKdjZdjCzjl);

                    if (kdj == null) {
                        tbKdjZdjCzjl.setYje("0");
                    } else {
                        tbKdjFd.setYje(kdj.getBjje());

                        tbKdj.setId(kdj.getId());
                        if (kdj.getBjje() != null && !"".equals(kdj.getBjje())) {
                            tbKdj.setBjyje(kdj.getBjje());
                            tbKdjZdjCzjl.setYje(kdj.getBjje());
                        } else {
                            tbKdjZdjCzjl.setYje("0");
                        }
                        tbKdjService.updateById(tbKdj);
                    }
                    tbKdjZdjCzjl.setTime(new Date());
                    tbKdjZdjCzjl.setUserid(employee != null ? employee.getUserid() : null);
                    tbKdjZdjCzjl.setName(employee != null ? employee.getName() : null);
                    tbKdjZdjCzjlService.save(tbKdjZdjCzjl);
                }
            } else if ("全天2".equals(cc)) {
                for (int i = 0; i < 2; i++) {
                    if (i == 0) {
                        cc = "晚";
                    } else {
                        cc = "白";
                    }
                    tbKdjFd.setJe(JE.toString());

                    if ("白".equals(cc)) {
                        Calendar c1 = Calendar.getInstance();
                        c1.setTime(rq);
                        c1.set(Calendar.MINUTE, 0);
                        c1.set(Calendar.SECOND, 0);
                        c1.set(Calendar.MILLISECOND, 0);
                        c1.set(Calendar.DAY_OF_MONTH, c1.get(Calendar.DAY_OF_MONTH) + 1);
                        c1.add(Calendar.HOUR, 10);

                        tbKdjFd.setXjctime(c1.getTime());
                        Calendar c2 = Calendar.getInstance();
                        c2.setTime(rq);
                        c2.set(Calendar.MINUTE, 0);
                        c2.set(Calendar.SECOND, 0);
                        c2.set(Calendar.MILLISECOND, 0);
                        c2.set(Calendar.DAY_OF_MONTH, c2.get(Calendar.DAY_OF_MONTH) + 1);
                        c2.add(Calendar.HOUR, 17);
                        tbKdjFd.setXtctime(c2.getTime());

                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        String d = sdf.format(c2.getTime());
                        tbKdjFd.setRq(d);

                    } else {
                        Calendar c1 = Calendar.getInstance();
                        c1.setTime(rq);
                        c1.set(Calendar.MINUTE, 0);
                        c1.set(Calendar.SECOND, 0);
                        c1.set(Calendar.MILLISECOND, 0);
                        c1.add(Calendar.HOUR, 18);
                        tbKdjFd.setXjctime(c1.getTime());

                        Calendar c2 = Calendar.getInstance();
                        c2.setTime(rq);
                        c2.set(Calendar.DAY_OF_MONTH, c2.get(Calendar.DAY_OF_MONTH) + 1);
                        c2.set(Calendar.MINUTE, 0);
                        c2.set(Calendar.SECOND, 0);
                        c2.set(Calendar.MILLISECOND, 0);
                        c2.add(Calendar.HOUR, 8);
                        tbKdjFd.setXtctime(c2.getTime());

                    }
                    tbKdjFd.setCc(cc);

                    TbKdj tbKdj = new TbKdj();
                    tbKdj.setVid(tbKdjFd.getVid());
                    tbKdj.setVname(tbKdjFd.getVname());
                    tbKdj.setRq(tbKdjFd.getRq());
                    tbKdj.setCc(tbKdjFd.getCc());
                    tbKdj.setXjctime(tbKdjFd.getXjctime());
                    tbKdj.setXtctime(tbKdjFd.getXtctime());
                    tbKdj.setJe(tbKdjFd.getJe());
                    tbKdj.setJjje(jj);
                    tbKdj.setBjje(bj);
                    TbKdj kdj = tbKdjService.queryByBsAndCcAndRq(tbKdj.getVid(), tbKdj.getCc(), tbKdj.getRq());

                    TbKdjFdCzjl tbKdjFdCzjl = new TbKdjFdCzjl();
                    BeanUtils.copyProperties(tbKdjFd, tbKdjFdCzjl);
                    if (kdj == null) {
                        tbKdjFdCzjl.setYje("0");
                        tbKdjService.save(tbKdj);
                    } else {
                        if (kdj.getJe() != null && !"".equals(kdj.getJe())) {
                            tbKdj.setYje(kdj.getJe());
                            tbKdjFdCzjl.setYje(kdj.getJe());
                        } else {
                            tbKdjFdCzjl.setYje("0");
                        }
                        tbKdj.setId(kdj.getId());
                        try {
                            double je1 = Double.parseDouble(tbKdj.getJe());
                            double yje1 = Double.parseDouble(tbKdj.getYje());
                            tbKdj.setStatu(je1 > yje1 ? "1" : je1 < yje1 ? "2" : "0");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        tbKdjService.updateById(tbKdj);

                    }
                    tbKdjFdCzjl.setTime(new Date());
                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
                    tbKdjFdCzjl.setUserid(employee != null ? employee.getUserid() : null);
                    tbKdjFdCzjl.setName(employee != null ? employee.getName() : null);
                    //底价新增
                    tbKdjFdCzjlService.save(tbKdjFdCzjl);
                    //基价
                    tbKdjFd.setJe(jj);
                    TbKdjJzxCzjl tbKdjJzxCzjl = new TbKdjJzxCzjl();
                    BeanUtils.copyProperties(tbKdjFd, tbKdjJzxCzjl);


                    if (kdj == null) {
                        tbKdjJzxCzjl.setYje("0");
                    } else {
                        tbKdjFd.setYje(kdj.getJjje());
                        tbKdj.setId(kdj.getId());
                        if (kdj.getJjje() != null && !"".equals(kdj.getJjje())) {
                            tbKdj.setJjyje(kdj.getJjje());
                            tbKdjJzxCzjl.setYje(kdj.getJjje());
                        } else {
                            tbKdjJzxCzjl.setYje("0");
                        }
                        tbKdjService.updateById(tbKdj);
                    }

                    tbKdjJzxCzjl.setTime(new Date());
                    tbKdjJzxCzjl.setUserid(employee != null ? employee.getUserid() : null);
                    tbKdjJzxCzjl.setName(employee != null ? employee.getName() : null);
                    //基准线新增
                    tbKdjJzxCzjlService.save(tbKdjJzxCzjl);
                    //报价新增
                    tbKdjFd.setJe(bj);
                    TbKdjZdjCzjl tbKdjZdjCzjl = new TbKdjZdjCzjl();
                    BeanUtils.copyProperties(tbKdjFd, tbKdjZdjCzjl);
                    if (kdj == null) {
                        tbKdjZdjCzjl.setYje("0");
                    } else {
                        tbKdjFd.setYje(kdj.getBjje());

                        tbKdj.setId(kdj.getId());
                        if (kdj.getBjje() != null && !"".equals(kdj.getBjje())) {
                            tbKdj.setBjyje(kdj.getBjje());
                            tbKdjZdjCzjl.setYje(kdj.getBjje());
                        } else {
                            tbKdjZdjCzjl.setYje("0");
                        }
                        tbKdjService.updateById(tbKdj);
                    }

                    tbKdjZdjCzjl.setTime(new Date());
                    tbKdjZdjCzjl.setUserid(employee != null ? employee.getUserid() : null);
                    tbKdjZdjCzjl.setName(employee != null ? employee.getName() : null);
                    tbKdjZdjCzjlService.save(tbKdjZdjCzjl);

                }
            }
        } else {
            if ("白".equals(cc)) {
                Calendar c1 = Calendar.getInstance();
                c1.setTime(rq);
                c1.set(Calendar.MINUTE, 0);
                c1.set(Calendar.SECOND, 0);
                c1.set(Calendar.MILLISECOND, 0);
                c1.add(Calendar.HOUR, 10);
                tbKdjFd.setXjctime(c1.getTime());
                Calendar c2 = Calendar.getInstance();
                c2.setTime(rq);
                c2.set(Calendar.MINUTE, 0);
                c2.set(Calendar.SECOND, 0);
                c2.set(Calendar.MILLISECOND, 0);
                c2.add(Calendar.HOUR, 17);
                tbKdjFd.setXtctime(c2.getTime());
            } else if ("晚".equals(cc)) {
                Calendar c1 = Calendar.getInstance();
                c1.setTime(rq);
                c1.set(Calendar.MINUTE, 0);
                c1.set(Calendar.SECOND, 0);
                c1.set(Calendar.MILLISECOND, 0);
                c1.add(Calendar.HOUR, 18);
                tbKdjFd.setXjctime(c1.getTime());
                Calendar c2 = Calendar.getInstance();
                c2.setTime(rq);
                c2.set(Calendar.DAY_OF_MONTH, c2.get(Calendar.DAY_OF_MONTH) + 1);
                c2.set(Calendar.MINUTE, 0);
                c2.set(Calendar.SECOND, 0);
                c2.set(Calendar.MILLISECOND, 0);
                c2.add(Calendar.HOUR, 8);
                tbKdjFd.setXtctime(c2.getTime());
            }

            TbKdj tbKdj = new TbKdj();
            tbKdj.setVid(tbKdjFd.getVid());
            tbKdj.setVname(tbKdjFd.getVname());
            tbKdj.setRq(tbKdjFd.getRq());
            tbKdj.setCc(tbKdjFd.getCc());
            tbKdj.setXjctime(tbKdjFd.getXjctime());
            tbKdj.setXtctime(tbKdjFd.getXtctime());
            tbKdj.setJe(tbKdjFd.getJe());
            tbKdj.setJjje(jj);
            tbKdj.setBjje(bj);
            TbKdj kdj = tbKdjService.queryByBsAndCcAndRq(tbKdj.getVid(), tbKdj.getCc(), tbKdj.getRq());


            TbKdjFdCzjl tbKdjFdCzjl = new TbKdjFdCzjl();
            BeanUtils.copyProperties(tbKdjFd, tbKdjFdCzjl);
            if (kdj == null) {
                tbKdjFdCzjl.setYje("0");
                tbKdjService.save(tbKdj);
            } else {
                if (kdj.getJe() != null && !"".equals(kdj.getJe())) {
                    tbKdj.setYje(kdj.getJe());
                    tbKdjFdCzjl.setYje(kdj.getJe());
                } else {
                    tbKdjFdCzjl.setYje("0");
                }

                tbKdj.setId(kdj.getId());
                try {
                    double je1 = Double.parseDouble(tbKdj.getJe());
                    double yje1 = Double.parseDouble(tbKdj.getYje());
                    tbKdj.setStatu(je1 > yje1 ? "1" : je1 < yje1 ? "2" : "0");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                tbKdjService.updateById(tbKdj);

            }
            tbKdjFdCzjl.setTime(new Date());
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
            tbKdjFdCzjl.setUserid(employee != null ? employee.getUserid() : null);
            tbKdjFdCzjl.setName(employee != null ? employee.getName() : null);
            //底价新增
            tbKdjFdCzjlService.save(tbKdjFdCzjl);


            //基价
            tbKdjFd.setJe(jj);
            TbKdjJzxCzjl tbKdjJzxCzjl = new TbKdjJzxCzjl();
            BeanUtils.copyProperties(tbKdjFd, tbKdjJzxCzjl);
            if (kdj == null) {
                tbKdjJzxCzjl.setYje("0");
            } else {
                tbKdjFd.setYje(kdj.getJjje());
                tbKdj.setId(kdj.getId());
                if (kdj.getJjje() != null && !"".equals(kdj.getJjje())) {
                    tbKdj.setJjyje(kdj.getJjje());
                    tbKdjJzxCzjl.setYje(kdj.getJjje());
                } else {
                    tbKdjJzxCzjl.setYje("0");
                }
                tbKdjService.updateById(tbKdj);
            }

            tbKdjJzxCzjl.setTime(new Date());
            tbKdjJzxCzjl.setUserid(employee != null ? employee.getUserid() : null);
            tbKdjJzxCzjl.setName(employee != null ? employee.getName() : null);
            //基准线新增
            tbKdjJzxCzjlService.save(tbKdjJzxCzjl);


            //报价新增
            tbKdjFd.setJe(bj);
            TbKdjZdjCzjl tbKdjZdjCzjl = new TbKdjZdjCzjl();
            BeanUtils.copyProperties(tbKdjFd, tbKdjZdjCzjl);

            if (kdj == null) {
                tbKdjZdjCzjl.setYje("0");
            } else {
                tbKdjFd.setYje(kdj.getBjje());

                tbKdj.setId(kdj.getId());
                if (kdj.getBjje() != null && !"".equals(kdj.getBjje())) {
                    tbKdj.setBjyje(kdj.getBjje());
                    tbKdjZdjCzjl.setYje(kdj.getBjje());
                } else {
                    tbKdjZdjCzjl.setYje("0");
                }
                tbKdjService.updateById(tbKdj);
            }
            tbKdjZdjCzjl.setTime(new Date());
            tbKdjZdjCzjl.setUserid(employee != null ? employee.getUserid() : null);
            tbKdjZdjCzjl.setName(employee != null ? employee.getName() : null);
            tbKdjZdjCzjlService.save(tbKdjZdjCzjl);
        }
    }

    /**
     * 提交底价上传和修改
     */
    @RequestMapping(value = "tjDjTask")
    public void tjDjTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("userid");
        if (datas == null) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        Date rq = jsonObject.getDate("rq");
        String cc = jsonObject.getString("cc");
        Double je = jsonObject.getDouble("je");
        double v = je + 200;
        double v1 = je + 400;

        TbKdj tbKdj = JSON.toJavaObject(jsonObject, TbKdj.class);

        if ("白".equals(cc)) {
            Calendar c1 = Calendar.getInstance();
            c1.setTime(rq);
            c1.set(Calendar.MINUTE, 0);
            c1.set(Calendar.SECOND, 0);
            c1.set(Calendar.MILLISECOND, 0);
            c1.add(Calendar.HOUR, 10);
            tbKdj.setXjctime(c1.getTime());
            Calendar c2 = Calendar.getInstance();
            c2.setTime(rq);
            c2.set(Calendar.MINUTE, 0);
            c2.set(Calendar.SECOND, 0);
            c2.set(Calendar.MILLISECOND, 0);
            c2.add(Calendar.HOUR, 17);
            tbKdj.setXtctime(c2.getTime());
        } else {
            Calendar c1 = Calendar.getInstance();
            c1.setTime(rq);
            c1.set(Calendar.MINUTE, 0);
            c1.set(Calendar.SECOND, 0);
            c1.set(Calendar.MILLISECOND, 0);
            c1.add(Calendar.HOUR, 18);
            tbKdj.setXjctime(c1.getTime());
            Calendar c2 = Calendar.getInstance();
            c2.setTime(rq);
            c2.set(Calendar.DAY_OF_MONTH, c2.get(Calendar.DAY_OF_MONTH) + 1);
            c2.set(Calendar.MINUTE, 0);
            c2.set(Calendar.SECOND, 0);
            c2.set(Calendar.MILLISECOND, 0);
            c2.add(Calendar.HOUR, 8);
            tbKdj.setXtctime(c2.getTime());
        }
        tbKdj.setJjje(v + "");
        tbKdj.setBjje(v1 + "");
        TbKdj tbKdj1 = tbKdjService.queryByBsAndCcAndRq(tbKdj.getVid(), tbKdj.getCc(), tbKdj.getRq());
        if (tbKdj1 != null) {
            tbKdj.setId(tbKdj1.getId());
            tbKdj.setYje(tbKdj1.getJe());
            tbKdj.setJjyje(tbKdj1.getJjje());
            tbKdj.setBjyje(tbKdj1.getBjje());
            double je1 = Double.parseDouble(tbKdj.getJe());
            double yje1 = Double.parseDouble(tbKdj.getYje());
            tbKdj.setStatu(je1 > yje1 ? "1" : je1 < yje1 ? "2" : "0");
            tbKdjService.updateById(tbKdj);
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
            try {
                TbKdjFdCzjl tbKdjFdCzjl = new TbKdjFdCzjl();
                BeanUtils.copyProperties(tbKdj, tbKdjFdCzjl);
                tbKdjFdCzjl.setJe(tbKdj.getJe());
                tbKdjFdCzjl.setYje(tbKdj1.getJe());
                if (employee != null) {
                    tbKdjFdCzjl.setUserid(employee.getUserid());
                    tbKdjFdCzjl.setName(employee.getName());
                }
                tbKdjFdCzjl.setTime(new Date());
                tbKdjFdCzjlService.save(tbKdjFdCzjl);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                TbKdjJzxCzjl tbKdjJzxCzjl = new TbKdjJzxCzjl();
                BeanUtils.copyProperties(tbKdj, tbKdjJzxCzjl);
                tbKdjJzxCzjl.setJe(tbKdj.getJjje());
                tbKdjJzxCzjl.setYje(tbKdj1.getJjje());
                if (employee != null) {
                    tbKdjJzxCzjl.setUserid(employee.getUserid());
                    tbKdjJzxCzjl.setName(employee.getName());
                }
                tbKdjJzxCzjl.setTime(new Date());
                tbKdjJzxCzjlService.save(tbKdjJzxCzjl);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                TbKdjZdjCzjl tbKdjZdjCzjl = new TbKdjZdjCzjl();
                BeanUtils.copyProperties(tbKdj, tbKdjZdjCzjl);
                tbKdjZdjCzjl.setJe(tbKdj.getJjje());
                tbKdjZdjCzjl.setYje(tbKdj1.getJjje());
                if (employee != null) {
                    tbKdjZdjCzjl.setUserid(employee.getUserid());
                    tbKdjZdjCzjl.setName(employee.getName());
                }
                tbKdjZdjCzjl.setTime(new Date());
                tbKdjZdjCzjlService.save(tbKdjZdjCzjl);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            tbKdj.setDycje(tbKdj.getJe());
            tbKdj.setDycjjje(tbKdj.getJjje());
            tbKdj.setDycbjje(tbKdj.getBjje());
            tbKdjService.save(tbKdj);
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
            try {
                TbKdjFdCzjl tbKdjFdCzjl = new TbKdjFdCzjl();
                BeanUtils.copyProperties(tbKdj, tbKdjFdCzjl);
                tbKdjFdCzjl.setJe(tbKdj.getJe());
                if (employee != null) {
                    tbKdjFdCzjl.setUserid(employee.getUserid());
                    tbKdjFdCzjl.setName(employee.getName());
                }
                tbKdjFdCzjl.setTime(new Date());
                tbKdjFdCzjlService.save(tbKdjFdCzjl);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                TbKdjJzxCzjl tbKdjJzxCzjl = new TbKdjJzxCzjl();
                BeanUtils.copyProperties(tbKdj, tbKdjJzxCzjl);
                tbKdjJzxCzjl.setJe(tbKdj.getJjje());
                if (employee != null) {
                    tbKdjJzxCzjl.setUserid(employee.getUserid());
                    tbKdjJzxCzjl.setName(employee.getName());
                }
                tbKdjJzxCzjl.setTime(new Date());
                tbKdjJzxCzjlService.save(tbKdjJzxCzjl);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                TbKdjZdjCzjl tbKdjZdjCzjl = new TbKdjZdjCzjl();
                BeanUtils.copyProperties(tbKdj, tbKdjZdjCzjl);
                tbKdjZdjCzjl.setJe(tbKdj.getJjje());
                if (employee != null) {
                    tbKdjZdjCzjl.setUserid(employee.getUserid());
                    tbKdjZdjCzjl.setName(employee.getName());
                }
                tbKdjZdjCzjl.setTime(new Date());
                tbKdjZdjCzjlService.save(tbKdjZdjCzjl);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 跨场客单价修改审批提交异步
     */
    @RequestMapping(value = "kdjSpXgTask")
    public void kdjSpXgTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return;
        }
        String userid = request.getParameter("userid");
        JSONObject jsonObject = JSONObject.parseObject(datas);

        Double je = jsonObject.getDouble("je");
        String vid = jsonObject.getString("vid");
        String vname = jsonObject.getString("vname");
        String cc = jsonObject.getString("cc");
        Double dqxj = jsonObject.getDouble("dqxj");
        //判断开始时间0
        Date ksdate = jsonObject.getDate("kstime");
        Date jsdate = jsonObject.getDate("jstime");

        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);


        Map<String, Object> map1 = new HashMap<>();
        map1.put("vid", vid);
        map1.put("jcsj", ksdate);
        map1.put("tcsj", jsdate);
        List<TbKdj> list1 = weiLianService.queryYyKdj(map1);
        if (list1.size() > 0) {
            list1.forEach(l -> {
                WlgbKdjSqjl wlgbKdjSqjl = new WlgbKdjSqjl();
                wlgbKdjSqjl.setCc(l.getCc());
                wlgbKdjSqjl.setVid(l.getVid());
                wlgbKdjSqjl.setJctime(l.getXjctime());
                wlgbKdjSqjl.setTctime(l.getXtctime());
                wlgbKdjSqjl.setYje(Double.parseDouble(l.getJe()));
                wlgbKdjSqjl.setVname(l.getVname());


                double sum = je / list1.size();
                int tmp = (int) (sum * 100);
                sum = tmp / 100.0;
                wlgbKdjSqjl.setSqje(sum);

                wlgbKdjSqjl.setSqr(dingdingEmployee != null ? dingdingEmployee.getName() : null);
                wlgbKdjSqjl.setSqrid(dingdingEmployee != null ? dingdingEmployee.getUserid() : null);
                wlgbKdjSqjlService.save(wlgbKdjSqjl);
            });
        } else {
            WlgbKdjSqjl wlgbKdjSqjl = new WlgbKdjSqjl();
            wlgbKdjSqjl.setCc(cc);
            wlgbKdjSqjl.setVid(vid);
            wlgbKdjSqjl.setJctime(ksdate);
            wlgbKdjSqjl.setTctime(jsdate);
            wlgbKdjSqjl.setYje(dqxj);
            wlgbKdjSqjl.setVname(vname);
            wlgbKdjSqjl.setSqje(je);
            wlgbKdjSqjl.setSqr(dingdingEmployee != null ? dingdingEmployee.getName() : null);
            wlgbKdjSqjl.setSqrid(dingdingEmployee != null ? dingdingEmployee.getUserid() : null);
            wlgbKdjSqjlService.save(wlgbKdjSqjl);
        }

        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(vid);

        TbKdjFd tbkdjFd = new TbKdjFd();
        tbkdjFd.setVname(tbVilla.getVname());
        tbkdjFd.setVid(vid);

        //判断是否民宿
        Integer sfMs = weiLianService.querySfMs(vid);

        try {
            if (sfMs > 0) {
                xgKdjMs(tbkdjFd, dingdingEmployee, jsonObject, je);
            } else {
                xgKdj(tbkdjFd, dingdingEmployee, jsonObject, je);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 修改普通客单价
     *
     * @param tbKdjFd          客单价对象
     * @param dingdingEmployee 操作人
     * @param jsonObject       表单提交数据
     * @param je               申请金额
     */
    public void xgKdj(TbKdjFd tbKdjFd, DingdingEmployee dingdingEmployee, JSONObject jsonObject, Double je) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        List<JSONObject> list = new ArrayList<>();
        //判断开始时间0
        Date ksdate = jsonObject.getDate("kstime");
        Date jsdate = jsonObject.getDate("jstime");
        do {
            c1.setTime(ksdate);
            c2.setTime(c1.getTime());
            if (c1.get(Calendar.HOUR_OF_DAY) == 10) {
                JSONObject map = new JSONObject();

                map.put("kstime", c1.getTime());

                c1.set(Calendar.HOUR_OF_DAY, 10);
                c2.set(Calendar.HOUR_OF_DAY, 17);
                map.put("jstime", c2.getTime());
                list.add(map);
                c1.set(Calendar.HOUR_OF_DAY, 18);
            }
            if (jsdate.getTime() <= c2.getTime().getTime()) {
                break;
            }
            if (c1.get(Calendar.HOUR_OF_DAY) == 18) {
                JSONObject map = new JSONObject();
                c2.setTime(c1.getTime());
                map.put("kstime", c1.getTime());
                c2.set(Calendar.HOUR_OF_DAY, 8);
                c2.set(Calendar.DAY_OF_MONTH, c2.get(Calendar.DAY_OF_MONTH) + 1);
                map.put("jstime", c2.getTime());
                list.add(map);
                c1.set(Calendar.HOUR_OF_DAY, 10);
            }
            c1.set(Calendar.DAY_OF_MONTH, c1.get(Calendar.DAY_OF_MONTH) + 1);
            ksdate = c1.getTime();
        } while (ksdate.getTime() < jsdate.getTime());

        for (JSONObject l : list) {
            double pjJe = je / list.size();
            int tmp = (int) (pjJe * 100);
            pjJe = tmp / 100.0;

            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");

            Double jj = pjJe + 200;
            BigDecimal bd1 = new BigDecimal(jj);
            jj = bd1.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();


            Double bj = pjJe + 400;
            BigDecimal bd2 = new BigDecimal(bj);
            bj = bd2.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            //留取小数点后两位

            String rq = sdf1.format(l.getDate("kstime"));

            //场次
            Date kssj = l.getDate("kstime");
            String cc = "白";
            if (kssj.getHours() == 18) {
                cc = "晚";
            }

            TbKdj tbKdj = tbKdjService.queryByBsAndCcAndRqAndJcAndTc(tbKdjFd.getVid(), cc, rq, l.getDate("kstime"), l.getDate("jstime"));
            TbKdj tbKdj1 = new TbKdj();
            if (!(Optional.ofNullable(tbKdj).isPresent())) {
                tbKdj1.setRq(rq);
                tbKdj1.setCc(cc);
                tbKdj1.setXjctime(l.getDate("kstime"));
                tbKdj1.setXtctime(l.getDate("jstime"));
                tbKdj1.setVid(tbKdjFd.getVid());
                tbKdj1.setVname(tbKdjFd.getVname());
                tbKdj1.setJe(pjJe + "");
                tbKdj1.setYje("0");
                tbKdj1.setJjje(Double.valueOf(Math.round(jj)).toString());
                tbKdj1.setJjyje("0");
                tbKdj1.setStatu("0");
                tbKdj1.setBjje(Double.valueOf(Math.round(bj)).toString());
                tbKdj1.setBjyje("0");

                tbKdjService.save(tbKdj1);

            } else {
                tbKdj.setYje(tbKdj.getJe());
                tbKdj.setJjyje(tbKdj.getJjje());
                tbKdj.setBjyje(tbKdj.getBjje());
                if (pjJe > Double.parseDouble(tbKdj.getJe())) {
                    tbKdj.setStatu("1");
                } else if (pjJe < Double.parseDouble(tbKdj.getJe())) {
                    tbKdj.setStatu("2");
                }
                tbKdj.setJe(pjJe + "");
                tbKdj.setJjje(Double.valueOf(Math.round(jj)).toString());
                tbKdj.setBjje(Double.valueOf(Math.round(bj)).toString());
                tbKdjService.updateById(tbKdj);
                tbKdj1 = tbKdj;
            }
            TbKdjFdCzjl tbKdjFdCzjl = new TbKdjFdCzjl();
            tbKdjFdCzjl.setVid(tbKdj1.getVid());
            tbKdjFdCzjl.setVname(tbKdj1.getVname());
            tbKdjFdCzjl.setCc(tbKdj1.getCc());
            tbKdjFdCzjl.setXjctime(tbKdj1.getXjctime());
            tbKdjFdCzjl.setUserid(dingdingEmployee != null ? dingdingEmployee.getUserid() : null);
            tbKdjFdCzjl.setXtctime(tbKdj1.getXtctime());
            tbKdjFdCzjl.setTime(new Date());
            tbKdjFdCzjl.setStatu(tbKdj1.getStatu());
            tbKdjFdCzjl.setName(dingdingEmployee != null ? dingdingEmployee.getName() : null);
            tbKdjFdCzjl.setYje(tbKdj1.getYje());
            tbKdjFdCzjl.setJe(tbKdj1.getJe());
            tbKdjFdCzjl.setRq(tbKdj1.getRq());
            tbKdjFdCzjlService.save(tbKdjFdCzjl);
        }


    }

    /**
     * 修改民宿客单价
     *
     * @param tbKdjFd          客单价对象
     * @param dingdingEmployee 操作人
     * @param jsonObject       表单提交数据
     * @param je               申请金额
     */
    public void xgKdjMs(TbKdjFd tbKdjFd, DingdingEmployee dingdingEmployee, JSONObject jsonObject, Double je) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        List<JSONObject> list = new ArrayList<>();
        //判断开始时间0
        Date ksdate = jsonObject.getDate("kstime");
        Date jsdate = jsonObject.getDate("jstime");
        do {
            c1.setTime(ksdate);
            c2.setTime(c1.getTime());
            if (c1.get(Calendar.HOUR_OF_DAY) == 14) {
                JSONObject map = new JSONObject();
                c2.setTime(c1.getTime());
                map.put("kstime", c1.getTime());
                c2.set(Calendar.DAY_OF_MONTH, c2.get(Calendar.DAY_OF_MONTH) + 1);
                c2.set(Calendar.HOUR_OF_DAY, 12);
                map.put("jstime", c2.getTime());
                list.add(map);
                c1.set(Calendar.HOUR_OF_DAY, 14);
            }
            c1.set(Calendar.DAY_OF_MONTH, c1.get(Calendar.DAY_OF_MONTH) + 1);
            ksdate = c1.getTime();
        } while (ksdate.getTime() < jsdate.getTime());

        for (JSONObject l : list) {
            Double pjJe = je / list.size();
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
            BigDecimal bd = new BigDecimal(pjJe);
            pjJe = bd.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

            Double jj = pjJe + 200;
            BigDecimal bd1 = new BigDecimal(jj);
            jj = bd1.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();


            Double bj = pjJe + 300;
            BigDecimal bd2 = new BigDecimal(bj);
            bj = bd2.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            //留取小数点后两位

            String rq = sdf1.format(l.getDate("kstime"));


            TbKdj tbKdj = tbKdjService.queryByBsAndRqAndJcAndTc(tbKdjFd.getVid(), rq, l.getDate("kstime"), l.getDate("jstime"));
            TbKdj tbKdj1 = new TbKdj();
            if (!(Optional.ofNullable(tbKdj).isPresent())) {
                tbKdj1.setRq(rq);
                tbKdj1.setXjctime(l.getDate("kstime"));
                tbKdj1.setXtctime(l.getDate("jstime"));
                tbKdj1.setVid(tbKdjFd.getVid());
                tbKdj1.setVname(tbKdjFd.getVname());
                tbKdj1.setJe(pjJe.toString());
                tbKdj1.setYje("0");
                tbKdj1.setJjje(Double.valueOf(Math.round(jj)).toString());
                tbKdj1.setJjyje("0");
                tbKdj1.setStatu("0");
                tbKdj1.setBjje(Double.valueOf(Math.round(bj)).toString());
                tbKdj1.setBjyje("0");
                tbKdjService.save(tbKdj1);

            } else {
                tbKdj.setYje(tbKdj.getJe());
                tbKdj.setJjyje(tbKdj.getJjje());
                tbKdj.setBjyje(tbKdj.getBjje());
                if (pjJe > Double.parseDouble(tbKdj.getJe())) {
                    tbKdj.setStatu("1");
                } else if (pjJe < Double.parseDouble(tbKdj.getJe())) {
                    tbKdj.setStatu("2");
                }
                tbKdj.setJe(pjJe.toString());
                tbKdj.setJjje(Double.valueOf(Math.round(jj)).toString());
                tbKdj.setBjje(Double.valueOf(Math.round(bj)).toString());
                tbKdjService.updateById(tbKdj);
                tbKdj1 = tbKdj;
            }
            TbKdjFdCzjl tbKdjFdCzjl = new TbKdjFdCzjl();
            tbKdjFdCzjl.setVid(tbKdj1.getVid());
            tbKdjFdCzjl.setVname(tbKdj1.getVname());
            tbKdjFdCzjl.setCc(tbKdj1.getCc());
            tbKdjFdCzjl.setXjctime(tbKdj1.getXjctime());
            tbKdjFdCzjl.setUserid(dingdingEmployee != null ? dingdingEmployee.getUserid() : null);
            tbKdjFdCzjl.setXtctime(tbKdj1.getXtctime());
            tbKdjFdCzjl.setTime(new Date());
            tbKdjFdCzjl.setStatu(tbKdj1.getStatu());
            tbKdjFdCzjl.setName(dingdingEmployee != null ? dingdingEmployee.getName() : null);
            tbKdjFdCzjl.setYje(tbKdj1.getYje());
            tbKdjFdCzjl.setJe(tbKdj1.getJe());
            tbKdjFdCzjl.setRq(tbKdj1.getRq());
            tbKdjFdCzjlService.save(tbKdjFdCzjl);
        }
    }


    /**
     * 新增crm信息发送工作通知
     */
    public void xzCrmGzTz(String xzr, String khwxh, String fzr, String khdh, String userid, String fzrid, CrmQbkh crmQbkh) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (userid.equals(fzrid)) {
            String text = xzr + "您新增了一条信息";
            text += "\n\n负责人：" + fzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
                text += "\n客户电话：" + (khdh != null ? khdh : "");

            } else {
                text += "\n客户电话：" + (khdh != null ? khdh : "");
                text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = xzr + "您新增了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text2 = xzr + "新增了一条信息给" + fzr;
            text2 += "\n\n负责人：" + fzr;
            text2 += "\n客户电话：" + (khdh != null ? khdh : "");
            text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 修改crm信息发送工作通知
     */
    public void xgCrmGzTz(String xgr, String khwxh, String fzr, String khdh, String userid, String fzrid, CrmQbkh crmQbkh) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (userid.equals(fzrid)) {
            String text = xgr + "您修改了一条信息";
            text += "\n\n负责人：" + fzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
                text += "\n客户电话：" + (khdh != null ? khdh : "");

            } else {
                text += "\n客户电话：" + (khdh != null ? khdh : "");
                text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = xgr + "您修改了一条信息";
            text += "\n\n负责人：" + fzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
                text += "\n客户电话：" + (khdh != null ? khdh : "");

            } else {
                text += "\n客户电话：" + (khdh != null ? khdh : "");
                text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text2 = xgr + "修改了一条" + fzr + "的信息";
            text2 += "\n\n负责人：" + fzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text2 += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
                text2 += "\n客户电话：" + (khdh != null ? khdh : "");

            } else {
                text2 += "\n客户电话：" + (khdh != null ? khdh : "");
                text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 删除crm信息发送工作通知
     */
    public void scCrmGzTz(String scr, String khwxh, String fzr, String khdh, String userid, String fzrid) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (userid.equals(fzrid)) {
            String text = scr + "您删除了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = scr + "您删除了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text2 = scr + "删除了一条" + fzr + "的信息";
            text2 += "\n\n负责人：" + fzr;
            text2 += "\n客户电话：" + (khdh != null ? khdh : "");
            text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 分配crm信息发送工作通知
     */
    public void fpCrmGzTz(String fpr, String qfzr, String qfzrid, String fzr, String khdh, String userid, String fzrid, String khwxh) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (userid.equals(fzrid) && qfzrid.equals(userid)) {
            String text = fpr + "您分配了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n前负责人：" + qfzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = fpr + "您分配了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n前负责人：" + qfzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text2 = fpr + "分配了一条信息给" + fzr;
            text2 += "\n\n负责人：" + fzr;
            text += "\n前负责人：" + qfzr;
            text2 += "\n客户电话：" + (khdh != null ? khdh : "");
            text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text3 = fpr + "分配了一条" + qfzr + "的信息给" + fzr;
            text3 += "\n\n负责人：" + fzr;
            text3 += "\n前负责人：" + qfzr;
            text3 += "\n客户电话：" + (khdh != null ? khdh : "");
            text3 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text3 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, qfzrid, text3);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 捞取crm信息发送工作通知
     */
    public void lqCrmGzTz(String lqr, String khwxh, String fzr, String khdh, String userid, String fzrid) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (userid.equals(fzrid)) {
            String text = lqr + "您捞取了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = lqr + "您捞取了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text2 = lqr + "捞取了一条信息给" + fzr;
            text2 += "\n\n负责人：" + fzr;
            text2 += "\n客户电话：" + (khdh != null ? khdh : "");
            text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 移交crm信息发送工作通知
     */
    public void yjCrmGzTz(String yjr, String qfzr, String qfzrid, String khwxh, String fzr, String khdh, String userid, String fzrid, CrmQbkh crmQbkh) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (userid.equals(fzrid) && qfzrid.equals(userid)) {
            String text = yjr + "您移交了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n前负责人：" + qfzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text += "\n客户昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
            } else {
                text += "\n客户电话：" + (khdh != null ? khdh : "");
                text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = yjr + "您移交了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n前负责人：" + qfzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
            } else {
                text += "\n客户电话：" + (khdh != null ? khdh : "");
                text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text2 = yjr + "移交了一条信息给" + fzr;
            text2 += "\n\n负责人：" + fzr;
            text2 += "\n前负责人：" + qfzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text2 += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
            } else {
                text2 += "\n客户电话：" + (khdh != null ? khdh : "");
                text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text3 = yjr + "移交了一条" + qfzr + "的信息给" + fzr;
            text3 += "\n\n负责人：" + fzr;
            text3 += "\n前负责人：" + qfzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text3 += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
            } else {
                text3 += "\n客户电话：" + (khdh != null ? khdh : "");
                text3 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text3 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, qfzrid, text3);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 公海池crm信息发送工作通知
     */
    public void ghcCrmGzTz(String thr, String khwxh, String fzr, String khdh, String userid) {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String text = thr + "您退回了一条信息至公海池";
        text += "\n\n负责人：" + fzr;
        text += "\n客户电话：" + (khdh != null ? khdh : "");
        text += "\n客户微信：" + (khwxh != null ? khwxh : "");
        text += "\n送达时间：" + df2.format(new Date());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        try {
            DingDBConfig.sendGztzText(dingkey, userid, text);
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "scXyd")
    public Result scXyd(String xid) {
        TbXyd tbXyd = tbXydService.queryById(xid);
        //电子协议单生成
        try {
            dzXyd(tbXyd);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 求职者提交面试信息异步
     */
    @RequestMapping(value = "qzzTjXxTask")
    public void qzzTjXxTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        jsonObject.put("jljd", "1");
        jsonObject.put("zhusu", "未定");
        jsonObject.put("sfms", "0");
        jsonObject.put("mianshi", "未面试");
        jsonObject.put("msjgtz", "0");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_J7KY7FDIH52I23EK327R");
        ydAppkey.setToken("E1666M91QHIQ9M3Y37EYM88SNWFU20MQCX7PKW5");
        GatewayResult gatewayResult;
        try {
            gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObject.toJSONString(), "012412221639786136545", "FORM-BY866R818HIQJA1I5VHXB5XQG28K3J2JRY7PKZ2");
        } catch (Exception e) {
            gatewayResult = new GatewayResult();
            e.printStackTrace();
        }
    }

    /**
     * 招聘表单提交异步
     */
    @RequestMapping(value = "tjMsJlTask")
    public void tjMsJlTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String formInstId = request.getParameter("formInstId");
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_J7KY7FDIH52I23EK327R");
        ydAppkey.setToken("E1666M91QHIQ9M3Y37EYM88SNWFU20MQCX7PKW5");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        HrMsz hrMsz = JSONObject.toJavaObject(jsonObject, HrMsz.class);
        JSONArray array = jsonObject.getJSONArray("tupian0");
        if (array != null) {
            if (array.size() > 0) {
                for (int i = 0; i < array.size(); i++) {

                    GatewayResult fileUrl = new GatewayResult();
                    try {
                        fileUrl = YdConfig.getFileUrl(token, ydAppkey, array.getJSONObject(0).getString("url"));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //获取成功才进入赋值
                    if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                        String url = fileUrl.getResult();
                        String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "rsjl.jpg");
                        //将图片下载到本地
                        downloadPicture(url, path);
                        //上传到阿里云
                        String tp3 = alyTp(path);
                        if (i == 0) {
                            hrMsz.setTupian0(tp3);
                        }
                        if (i == 1) {
                            hrMsz.setTupian1(tp3);
                        }
                        if (i == 2) {
                            hrMsz.setTupian2(tp3);
                        }
                    } else {
                        if (i == 0) {
                            hrMsz.setTupian0(null);
                        }
                        if (i == 1) {
                            hrMsz.setTupian1(null);
                        }
                        if (i == 2) {
                            hrMsz.setTupian2(null);
                        }
                    }
                }
            }
        }
        //邀请人
        if (hrMsz.getYyrname() != null && !"".equals(hrMsz.getYyrname())) {
            JSONArray array1 = JSONObject.parseArray(hrMsz.getYyrname());
            if (array1 != null && array1.size() > 0) {
                String string = array1.getString(0);
                if (string != null && !"".equals(string)) {
                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(string);
                    if (employee != null) {
                        hrMsz.setYyrname(employee.getName());
                        hrMsz.setUserid(employee.getUserid());
                    }
                }
            }
        }
        //面试官
        if (hrMsz.getMsgname() != null && !"".equals(hrMsz.getMsgname())) {
            JSONArray array1 = JSONObject.parseArray(hrMsz.getMsgname());
            if (array1 != null && array1.size() > 0) {
                String string = array1.getString(0);
                if (string != null && !"".equals(string)) {
                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(string);
                    if (employee != null) {
                        hrMsz.setMsgname(employee.getName());
                        hrMsz.setMsgid(employee.getUserid());
                    }
                }
            }
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        //生日
        if (hrMsz.getAge() != null && !"".equals(hrMsz.getAge())) {
            Date age = jsonObject.getDate("age");
            hrMsz.setAge(df.format(age));
        }
        //到岗时间
        if (hrMsz.getDaogangdate() != null && !"".equals(hrMsz.getDaogangdate())) {
            Date daogangdate = jsonObject.getDate("daogangdate");
            hrMsz.setDaogangdate(df.format(daogangdate));
        }
        //面试时间
        if (hrMsz.getMsdata() != null && !"".equals(hrMsz.getMsdata())) {
            Date msdata = jsonObject.getDate("msdata");
            hrMsz.setMsdata(df.format(msdata));
        }
        //负责人
        if (hrMsz.getGwbmfzr() != null && !"".equals(hrMsz.getGwbmfzr())) {
            JSONArray array1 = JSONObject.parseArray(hrMsz.getGwbmfzr());
            if (array1 != null && array1.size() > 0) {
                String string = array1.getString(0);
                if (string != null && !"".equals(string)) {
                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(string);
                    if (employee != null) {
                        hrMsz.setGwbmfzr(employee.getName());
                        hrMsz.setGwbmfzrid(employee.getUserid());
                    }
                }
            }
        }
        //岗位
        if (hrMsz.getYxgw() != null && !"".equals(hrMsz.getYxgw())) {
            HrGwCity hrGwCity = new HrGwCity();
            hrGwCity.setFgw(hrMsz.getYxgw());
            hrGwCity.setZcity(hrMsz.getGwcity());
            HrGwCity hrGwCity1 = hrGwCityService.queryOneByHrGwCity(hrGwCity);
            if (hrGwCity1 != null) {
                hrMsz.setYxgw(hrGwCity1.getFgwid());
                hrMsz.setGwcity(hrGwCity1.getZcityid());
            }
        }
        hrMsz.setMqzt("0");
        hrMsz.setTjdate(new Date());
        System.out.println(hrMsz);
        hrMszService.save(hrMsz);

        Integer lb = weiLianDdXcxService.queryYxbTzLb(hrMsz.getYxgw());
        if (lb > 0) {
            List<String> list = weiLianDdXcxService.queryYxBmHrTz();
            if (list.size() > 0) {
                String zpzRy = "";
                for (int i = 0; i < list.size(); i++) {
                    zpzRy += list.get(i);
                    if (i != list.size() - 1) {
                        zpzRy += ",";
                    }
                }
                Dingkey dingkey1 = new Dingkey();
                dingkey1.setAgentId("716099296");
                dingkey1.setAppkey("dinga4cqxwu9my5ybjhg");
                dingkey1.setAppsecret("3bgstQ9bJw96xDCV4wYNDe6L6JF52bBVGPYyD7NxqYGqG2Nl5MkJbiWE28uyYjq3");
                String content = "新增招聘者信息！";
                HrGwCity hrGwCity = new HrGwCity();
                hrGwCity.setFgwid(hrMsz.getYxgw());
                hrGwCity.setZcityid(hrMsz.getGwcity());
                HrGwCity hrGwCity1 = hrGwCityService.queryOneByHrGwCity(hrGwCity);
                content += "\n\n姓名：" + hrMsz.getName();
                content += "\n\n电话：" + hrMsz.getTel();
                content += "\n\n性别：" + (hrMsz.getGender() != null && !"".equals(hrMsz.getGender()) ? "2".equals(hrMsz.getGender()) ? "女" : "男" : "男");
                if (hrGwCity1 != null) {
                    content += "\n\n岗位：" + hrGwCity1.getFgw();
                    content += "\n\n城市/名称：" + hrGwCity1.getZcity();
                }
                content += "\n\n送达时间：";
                try {
                    DingDBConfig.sendGztz1(zpzRy, dingkey1, "招聘系统来新人啦", content, "https://jztdpp.aliwork.com/APP_J7KY7FDIH52I23EK327R/formDetail/FORM-BY866R818HIQJA1I5VHXB5XQG28K3J2JRY7PKZ2?formInstId=" + formInstId);
                } catch (ApiException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 招聘表单修改异步
     */
    @RequestMapping(value = "xgMsXxTask")
    public void xgMsXxTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("userid");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        HrMsz hrMsz = JSON.toJavaObject(jsonObject, HrMsz.class);
        HrMsz hrMsz2 = new HrMsz();
        hrMsz2.setSfsc("0");
        hrMsz2.setMsbh(hrMsz.getMsbh());
        HrMsz hrMsz1 = hrMszService.queryOneByHrMsz(hrMsz2);
        if (hrMsz1 == null) {
            return;
        }
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_J7KY7FDIH52I23EK327R");
        ydAppkey.setToken("E1666M91QHIQ9M3Y37EYM88SNWFU20MQCX7PKW5");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        JSONArray array = jsonObject.getJSONArray("tupian0");
        if (array != null) {
            if (array.size() > 0) {
                for (int i = 0; i < array.size(); i++) {
                    GatewayResult fileUrl = new GatewayResult();
                    try {
                        fileUrl = YdConfig.getFileUrl(token, ydAppkey, array.getJSONObject(0).getString("url"));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //获取成功才进入赋值
                    if (fileUrl.getSuccess() != null && fileUrl.getSuccess()) {
                        String url = fileUrl.getResult();
                        String path = FileConfig.getFileAbsolutePath("static" + File.separator + "img", "rsjl.jpg");
                        //将图片下载到本地
                        downloadPicture(url, path);
                        //上传到阿里云
                        String tp3 = alyTp(path);
                        if (i == 0) {
                            hrMsz.setTupian0(tp3);
                        }
                        if (i == 1) {
                            hrMsz.setTupian1(tp3);
                        }
                        if (i == 2) {
                            hrMsz.setTupian2(tp3);
                        }
                    } else {
                        if (i == 0) {
                            hrMsz.setTupian0(null);
                        }
                        if (i == 1) {
                            hrMsz.setTupian1(null);
                        }
                        if (i == 2) {
                            hrMsz.setTupian2(null);
                        }
                    }
                }
            }
        }
        //邀请人
        if (hrMsz.getYyrname() != null && !"".equals(hrMsz.getYyrname())) {
            JSONArray array1 = JSONObject.parseArray(hrMsz.getYyrname());
            if (array1 != null && array1.size() > 0) {
                String string = array1.getString(0);
                if (string != null && !"".equals(string)) {
                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(string);
                    if (employee != null) {
                        hrMsz.setYyrname(employee.getName());
                        hrMsz.setUserid(employee.getUserid());
                    }
                }
            }
        }
        //面试官
        if (hrMsz.getMsgname() != null && !"".equals(hrMsz.getMsgname())) {
            JSONArray array1 = JSONObject.parseArray(hrMsz.getMsgname());
            if (array1 != null && array1.size() > 0) {
                String string = array1.getString(0);
                if (string != null && !"".equals(string)) {
                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(string);
                    if (employee != null) {
                        hrMsz.setMsgname(employee.getName());
                        hrMsz.setMsgid(employee.getUserid());
                    }
                }
            }
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        //生日
        if (hrMsz.getAge() != null && !"".equals(hrMsz.getAge())) {
            Date age = jsonObject.getDate("age");
            hrMsz.setAge(df.format(age));
        }
        //到岗时间
        if (hrMsz.getDaogangdate() != null && !"".equals(hrMsz.getDaogangdate())) {
            Date daogangdate = jsonObject.getDate("daogangdate");
            hrMsz.setDaogangdate(df.format(daogangdate));
        }
        //面试时间
        if (hrMsz.getMsdata() != null && !"".equals(hrMsz.getMsdata())) {
            Date msdata = jsonObject.getDate("msdata");
            hrMsz.setMsdata(df.format(msdata));
        }
        //负责人
        if (hrMsz.getGwbmfzr() != null && !"".equals(hrMsz.getGwbmfzr())) {
            JSONArray array1 = JSONObject.parseArray(hrMsz.getGwbmfzr());
            if (array1 != null && array1.size() > 0) {
                String string = array1.getString(0);
                if (string != null && !"".equals(string)) {
                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(string);
                    if (employee != null) {
                        hrMsz.setGwbmfzr(employee.getName());
                        hrMsz.setGwbmfzrid(employee.getUserid());
                    }
                }
            }
        }
        //岗位
        if (hrMsz.getYxgw() != null && !"".equals(hrMsz.getYxgw())) {
            HrGwCity hrGwCity = new HrGwCity();
            hrGwCity.setFgw(hrMsz.getYxgw());
            hrGwCity.setZcity(hrMsz.getGwcity());
            HrGwCity hrGwCity1 = hrGwCityService.queryOneByHrGwCity(hrGwCity);
            if (hrGwCity1 != null) {
                hrMsz.setYxgw(hrGwCity1.getFgwid());
                hrMsz.setGwcity(hrGwCity1.getZcityid());
            }
        }

        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        if (employee != null) {
            hrMsz.setXgrid(employee.getUserid());
        }
        hrMsz.setXgsj(DateFormatConfig.df1(new Date()));
        hrMsz.setId(hrMsz1.getId());
        if ("已通过".equals(hrMsz.getMianshi()) || "未通过".equals(hrMsz.getMianshi())) {
            if (!"已通过".equals(hrMsz1.getMianshi()) && !"未通过".equals(hrMsz1.getMianshi())) {
                hrMsz.setMsgtjsj(DateFormatConfig.df1(new Date()));
            }
        }
        hrMszService.updateById(hrMsz);

        if (!(hrMsz.getMsgname() != null ? hrMsz.getMsgname() : "").equals(hrMsz1.getMsgname())) {
            if (hrMsz.getMsgid() != null && !"".equals(hrMsz.getMsgid())) {
                DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(hrMsz.getMsgid());
                if (employee1 != null) {
                    HrMsgjl hrMsgjl = new HrMsgjl();
                    hrMsgjl.setMszid(hrMsz.getId() + "");
                    hrMsgjl.setMsgid(employee1.getUserid());
                    hrMsgjl.setMsgxm(employee1.getName());
                    hrMsgjlService.save(hrMsgjl);
                }
            }
        }
    }

    @RequestMapping(value = "tjCy")
    public Result tjCy() {
//        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
//        String token = null;
//        try {
//            token = DingToken.token(dingkey);
//        } catch (ApiException e) {
//            e.printStackTrace();
//        }
//        String finalToken = token;
//        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
//        JSONObject jsonObjectQuery = new JSONObject();
//        jsonObjectQuery.put("xfd", "159909317438346");
//        GatewayResult gatewayResult = null;
//        try {
//            gatewayResult = YdConfig.queryBdSlData(1, ydAppkey, token, jsonObjectQuery.toJSONString(), "FORM-0KYJ7URV0JWMWJ5OYJY4U11XWV4R1YLX5Q4KK6");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        if(gatewayResult.getSuccess()){
//            JSONObject jsonObject1 = JSONObject.parseObject(gatewayResult.getResult());
//            JSONArray jsonArray = jsonObject1.getJSONArray("data");
//            if(jsonArray != null && jsonArray.size() > 0){
//                jsonArray.forEach(l->{
//                    JSONObject item = (JSONObject) l;
//                    JSONObject formData = item.getJSONObject("formData");
//                    JSONArray cyzbd = formData.getJSONArray("cyzbd");
//                    System.out.println(item);
//                });
//            }
//        }
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
//        String token = null;
//        try {
//            token = DingToken.token(dingkey);
//        } catch (ApiException e) {
//            e.printStackTrace();
//        }
        JSONObject jsonObjectQuery = new JSONObject();
        String[] arr = {"1696003200000"};
        jsonObjectQuery.put("xddbh", "202309151420141721");
        jsonObjectQuery.put("xsfdc", "1");
//        String finalToken = token;
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");

        String datas = YdConfig.getDatas(ydAppkey, jsonObjectQuery.toJSONString(), "FORM-0KYJ7URV0JWMWJ5OYJY4U11XWV4R1YLX5Q4KK6", "1");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        JSONObject result = jsonObject.getJSONObject("result");
        JSONArray data1 = result.getJSONArray("data");
        data1.forEach(l1 -> {
            JSONObject data = (JSONObject) l1;
            JSONObject formData = data.getJSONObject("formData");
            JSONArray cyzbd = formData.getJSONArray("cyzbd");
            String xddbh = formData.getString("xddbh");
            TbXyd tbXyd = tbXydService.queryByDdBh(xddbh);
            TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
            wlgbOrderCyjlService.clearCyJl(xddbh);
            wlgbEatTcjlService.clearData(xddbh);
            wlgbEatCpjlService.clearData(xddbh);
            wlgbEatScjlService.clearData(xddbh);
            cyzbd.forEach(l -> {
                JSONObject item = (JSONObject) l;
                String cySx = item.getString("selectField_lnldd87u");
                if ("直营".equals(cySx)) {
                    String cymc = item.getString("cymc");
                    YdAppkey ydAppkey1 = new YdAppkey();
                    ydAppkey1.setAppkey("APP_JPQRCIS3ASCKF4BJ7UPZ");
                    ydAppkey1.setToken("3J966U61TOUD8M1M8X4WX810EW5G26HBR90MLW2");
                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("textField_lmtyh13t", cymc);
                    jsonObject1.put("textField_lmu279t4", tbVilla.getVcyqybh());
                    //套餐
                    String zzBdSl = YdConfig.getDatas(ydAppkey1, jsonObject1.toJSONString(), "FORM-IQ8666B1LKDELZNWBUSLP7SQVQYE3I2TGYTML9", "1");
                    JSONObject jsonObject2 = JSONObject.parseObject(zzBdSl);
                    JSONObject result2 = jsonObject2.getJSONObject("result");
                    if (result2.getJSONArray("data") != null && result2.getJSONArray("data").size() > 0) {
                        JSONObject data2 = result2.getJSONArray("data").getJSONObject(0);
                        JSONObject data3 = data2.getJSONObject("formData");
                        String tcbh = data3.getString("textField_lmtyh13s");
                        String tcmc = data3.getString("textField_lmtyh13t");
                        String jqmc = data3.getString("textField_lmu279t5");
                        String jqbh = data3.getString("textField_lmu279t4");
                        String tclx = data3.getString("radioField_lmtyh13x");

                        WlgbEatTcjl wlgbEatTcjl = new WlgbEatTcjl();
                        wlgbEatTcjl.setDdbh(xddbh);
                        wlgbEatTcjl.setTcbh(tcbh);
                        wlgbEatTcjl.setTcmc(tcmc);
                        wlgbEatTcjl.setTclx(tclx);
                        wlgbEatTcjl.setJqmc(jqmc);
                        wlgbEatTcjl.setJqbh(jqbh);
                        wlgbEatTcjl.setSj(data3.getDouble("numberField_lmub9h4u"));
                        wlgbEatTcjl.setCb(data3.getDouble("numberField_lmub9h4v"));
                        wlgbEatTcjl.setMl(data3.getDouble("numberField_lncq88zx"));
                        wlgbEatTcjlService.save(wlgbEatTcjl);

                        JSONArray cpzbd = data3.getJSONArray("tableField_lmtyh13u");
                        for (Object cp : cpzbd) {
                            JSONObject cp1 = (JSONObject) cp;
                            String cpbh = cp1.getString("textField_lnecrr3f");
                            if (cpbh != null && !"".equals(cpbh)) {
                                //菜品
                                String cpmc = cp1.getString("textField_lnofmvc3");
                                JSONObject jsonObject3 = new JSONObject();
                                jsonObject3.put("textField_lmtx9dew", cpbh);
                                jsonObject3.put("textField_lmtx9des", cpmc);
                                String zzBdSl1 = YdConfig.getDatas(ydAppkey1, jsonObject3.toJSONString(), "FORM-AC666081DPDEOL5BAFFF1BOR6GNO2SF59XTML5", "1");
                                JSONObject jsonObject4 = JSONObject.parseObject(zzBdSl1);
                                JSONObject result3 = jsonObject4.getJSONObject("result");
                                JSONArray data4 = result3.getJSONArray("data");
                                for (Object cp2 : data4) {
                                    JSONObject cp21 = (JSONObject) cp2;
                                    JSONObject data5 = cp21.getJSONObject("formData");
                                    WlgbEatCpjl wlgbEatCpjl = new WlgbEatCpjl();
                                    wlgbEatCpjl.setDdbh(xddbh);
                                    wlgbEatCpjl.setTcbh(tcbh);
                                    wlgbEatCpjl.setTcmc(tcmc);
                                    wlgbEatCpjl.setCpbh(data5.getString("textField_lmtx9dew"));
                                    wlgbEatCpjl.setCpmc(data5.getString("textField_lmtx9des"));
                                    wlgbEatCpjl.setCplx(data5.getString("radioField_lmtyjk43"));
                                    wlgbEatCpjl.setJqmc(data5.getString("textField_lmu279t5"));
                                    wlgbEatCpjl.setJqbh(data5.getString("textField_lmu279t4"));
                                    wlgbEatCpjl.setCbhj(data5.getDouble("numberField_lmtx9df6"));
                                    wlgbEatCpjl.setJysj(data5.getDouble("numberField_lmtx9df7"));
                                    wlgbEatCpjl.setMll(data5.getDouble("numberField_lmtx9df8"));
                                    wlgbEatCpjlService.save(wlgbEatCpjl);

                                    JSONArray data5JSONArray = data5.getJSONArray("tableField_lmtx9dey");
                                    for (Object yl : data5JSONArray) {
                                        JSONObject yl1 = (JSONObject) yl;
                                        String gys = yl1.getString("selectField_lnjq3cqd");
                                        String gysbh = yl1.getString("textField_lnohuoyy");
                                        String scmc = yl1.getString("selectField_lnjq3cqe");
                                        String scbh = yl1.getString("textField_lnjy24ct");
                                        Double ylNum = yl1.getDouble("numberField_lnl3qdkf");
                                        Double zxdj = yl1.getDouble("numberField_lnjy24cs");
                                        String dw = yl1.getString("textField_lnl3qdke");
                                        Double cb = yl1.getDouble("numberField_lnl3qdkd");
                                        Double price = yl1.getDouble("numberField_lnjqa159");
//                                        System.out.println(yl1);

                                        WlgbEatScjl wlgbEatScjl = new WlgbEatScjl();
                                        wlgbEatScjl.setDdbh(xddbh);
                                        wlgbEatScjl.setTcbh(tcbh);
                                        wlgbEatScjl.setTcmc(tcmc);
                                        wlgbEatScjl.setCpbh(cpbh);
                                        wlgbEatScjl.setCpmc(cpmc);
                                        wlgbEatScjl.setGys(gys);
                                        wlgbEatScjl.setGysbh(gysbh);
                                        wlgbEatScjl.setScmc(scmc);
                                        wlgbEatScjl.setScbh(scbh);
                                        wlgbEatScjl.setYlNum(ylNum);
                                        wlgbEatScjl.setMinPrice(zxdj);
                                        wlgbEatScjl.setDw(dw);
                                        wlgbEatScjl.setPrice(price);
                                        wlgbEatScjl.setCb(cb);
                                        wlgbEatScjlService.save(wlgbEatScjl);
                                    }
                                }
                            }
                        }
                    }
                }
            });
        });

        return Result.OK();
    }

    @RequestMapping(value = "test2")
    public Result test2() {
        YdAppkey ydAppkey1 = new YdAppkey();
        ydAppkey1.setAppkey("APP_JPQRCIS3ASCKF4BJ7UPZ");
        ydAppkey1.setToken("3J966U61TOUD8M1M8X4WX810EW5G26HBR90MLW2");
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("tableField_lmtyh13u", "CP0247");
        //套餐
        String zzBdSl = YdConfig.getDatas(ydAppkey1, jsonObject1.toJSONString(), "FORM-IQ8666B1LKDELZNWBUSLP7SQVQYE3I2TGYTML9", "1");
        JSONObject jsonObject2 = JSONObject.parseObject(zzBdSl);
        JSONObject result2 = jsonObject2.getJSONObject("result");
        JSONArray data = result2.getJSONArray("data");
        System.out.println(data);
        return Result.OK();

    }

    /**
     * 菜品信息修改异步
     */
    @RequestMapping(value = "editCpxxTask")
    public void editCpxxTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String cpbh = jsonObject.getString("textField_lmtx9dew");
        String jqbh = jsonObject.getString("textField_lmu279t4");

        YdAppkey ydAppkey1 = new YdAppkey();
        ydAppkey1.setAppkey("APP_JPQRCIS3ASCKF4BJ7UPZ");
        ydAppkey1.setToken("3J966U61TOUD8M1M8X4WX810EW5G26HBR90MLW2");
        JSONObject jsonObject1 = new JSONObject();
        //菜品子表单（通过菜品编号）
        jsonObject1.put("tableField_lmtyh13u", cpbh);
        //餐饮集群编号
        jsonObject1.put("textField_lmu279t4", jqbh);
        //套餐
        String zzBdSl = YdConfig.getDatas(ydAppkey1, jsonObject1.toJSONString(), "FORM-IQ8666B1LKDELZNWBUSLP7SQVQYE3I2TGYTML9", "1");
        JSONObject jsonObject2 = JSONObject.parseObject(zzBdSl);
        JSONObject result2 = jsonObject2.getJSONObject("result");
        JSONArray data = result2.getJSONArray("data");
        for (Object l : data) {
            JSONObject j = (JSONObject) l;
            //套餐实例id
            String formInstId = j.getString("formInstId");
            JSONObject formData = j.getJSONObject("formData");
            JSONArray cpZbd = formData.getJSONArray("tableField_lmtyh13u");
            double sum = 0.0;
            for (int i = 0; i < cpZbd.size(); i++) {
                JSONObject cp = cpZbd.getJSONObject(i);
                String cpbh1 = cp.getString("textField_lnecrr3f");
                if (cpbh.equals(cpbh1)) {
                    //修改表单关联名称
                    try {
                        String cpForm = cp.getString("associationFormField_lmtyh13w_id");
                        if (cpForm != null && !"".equals(cpForm)) {
                            cpForm = cpForm.substring(1, cpForm.length() - 1);
                            cpForm = cpForm.replaceAll("\\\\", "");
                        }
                        JSONArray objects = JSONObject.parseArray(cpForm);
                        if (objects != null && objects.size() > 0) {
                            JSONObject jsonObject3 = objects.getJSONObject(0);
                            jsonObject3.put("title", jsonObject.getString("textField_lmtx9des"));
                            objects.set(0, jsonObject3);
                        }
                        cp.put("associationFormField_lmtyh13w", objects);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //菜品名称
                    cp.put("textField_lnofmvc3", jsonObject.getString("textField_lmtx9des"));
                    //成本
                    cp.put("numberField_lnecrr3i", jsonObject.getDouble("numberField_lmtx9df6"));
                    //售价
                    cp.put("numberField_lnecrr3k", jsonObject.getDouble("numberField_lmtx9df7"));

                    sum += jsonObject.getDouble("numberField_lmtx9df6") != null ? jsonObject.getDouble("numberField_lmtx9df6") : 0.0;
                } else {
                    sum += cp.getDouble("numberField_lnecrr3i") != null ? cp.getDouble("numberField_lnecrr3i") : 0.0;
                }
                cpZbd.set(i, cp);
            }
            double sj = formData.getDouble("numberField_lmub9h4u") != null ? formData.getDouble("numberField_lmub9h4u") : 0.0;
            JSONObject jsonObject3 = new JSONObject();
            jsonObject3.put("tableField_lmtyh13u", cpZbd);
            //成本
            jsonObject3.put("numberField_lmub9h4v", sum);
            jsonObject3.put("numberField_lncq88zx", (sj - sum) / sj);
            String gxbd = YdConfig.gxbd(jsonObject3.toJSONString(), ydAppkey1.getAppkey(), ydAppkey1.getToken(), formInstId);
            System.out.println(gxbd);
        }
    }

    /**
     * 食材修改异步
     */
    @RequestMapping(value = "editScXxTask")
    public void editScXxTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);

        //食材编号
        String scbh = jsonObject.getString("textField_lmsi1qmd");
        //食材集群
        String scjq = jsonObject.getString("textField_lnwyo4qj");

        //菜品
        YdAppkey ydAppkey1 = new YdAppkey();
        ydAppkey1.setAppkey("APP_JPQRCIS3ASCKF4BJ7UPZ");
        ydAppkey1.setToken("3J966U61TOUD8M1M8X4WX810EW5G26HBR90MLW2");
        JSONObject jsonObject1 = new JSONObject();
        //菜品子表单（通过食材编号）
        jsonObject1.put("tableField_lmtx9dey", scbh);
        //菜品集群
        jsonObject1.put("textField_lmu279t4", scjq);
        String zzBdSl = YdConfig.getDatas(ydAppkey1, jsonObject1.toJSONString(), "FORM-AC666081DPDEOL5BAFFF1BOR6GNO2SF59XTML5", "1");
        JSONObject jsonObject2 = JSONObject.parseObject(zzBdSl);
        JSONObject result2 = jsonObject2.getJSONObject("result");
        JSONArray data = result2.getJSONArray("data");
        for (Object cps : data) {
            JSONObject cp = (JSONObject) cps;
            //套餐实例id
            String formInstId = cp.getString("formInstId");
            JSONObject data1 = cp.getJSONObject("formData");
            JSONArray yl = data1.getJSONArray("tableField_lmtx9dey");
            Double sum = 0.0;
            for (int i = 0; i < yl.size(); i++) {
                JSONObject sc = yl.getJSONObject(i);
                String scbh1 = sc.getString("textField_lnjy24ct");
                if (scbh.equals(scbh1)) {
                    //用量
                    Double scYl = sc.getDouble("numberField_lnl3qdkf");
                    //供应商
                    sc.put("selectField_lnjq3cqd", jsonObject.getString("textField_lnjqa155"));
                    //供应商编号
                    sc.put("textField_lnohuoyy", jsonObject.getString("textField_lnjqa156"));
                    //食材名称
                    sc.put("selectField_lnjq3cqe", jsonObject.getString("textField_lnigonxx"));
                    //最小单价
                    sc.put("numberField_lnjy24cs", jsonObject.getDouble("numberField_lnjys6da"));
                    //单位
                    sc.put("textField_lnl3qdke", jsonObject.getString("selectField_lnjqa158"));
                    //单价
                    sc.put("numberField_lnjqa159", jsonObject.getDouble("numberField_lnjqa159"));
                    //成本
                    double cb = (scYl != null ? scYl : 0.0) * (jsonObject.getDouble("numberField_lnjys6da") != null ? jsonObject.getDouble("numberField_lnjys6da") : 0.0);
                    sc.put("numberField_lnl3qdkd", cb);
                    sum += cb;
                    yl.set(i, sc);
                } else {
                    sum += sc.getDouble("numberField_lnl3qdkd") != null ? sc.getDouble("numberField_lnl3qdkd") : 0.0;
                }
            }
            double sj = data1.getDouble("numberField_lmtx9df7") != null ? data1.getDouble("numberField_lmtx9df7") : 0.0;
            JSONObject jsonObject3 = new JSONObject();
            jsonObject3.put("tableField_lmtx9dey", yl);
            //成本
            jsonObject3.put("numberField_lmtx9df6", sum);
            jsonObject3.put("numberField_lmtx9df8", (sj - sum) / sj);
            String gxbd = YdConfig.gxbd(jsonObject3.toJSONString(), ydAppkey1.getAppkey(), ydAppkey1.getToken(), formInstId);
            System.out.println(gxbd);
        }
    }

    @RequestMapping(value = "blDj")
    public Result blDj(String ysbh, String lsbh) {
        WlgbHxyhDzjl wlgbHxyhDzjl = wlgbHxyhDzjlService.queryByYsBhAndLsBh(ysbh, lsbh);
        if (wlgbHxyhDzjl == null) {
            return Result.error("到账记录找不到");
        }
        String outTradeNo = wlgbHxyhDzjl.getOutTradeNo();
        if (outTradeNo != null && outTradeNo.contains("ZFB")) {
            outTradeNo = outTradeNo.replace("ZFB", "");
        }
        if (outTradeNo != null && outTradeNo.contains("WX")) {
            outTradeNo = outTradeNo.replace("WX", "");
        }
        WlgbJdDjLsjlb wlgbJdDjLsjlb1 = wlgbJdDjLsjlbService.queryBySkBhAndSfSc(outTradeNo, 0);
        if (wlgbJdDjLsjlb1 == null) {
            return Result.error("临时表找不到");
        }
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        Date date = new Date();
        SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            date = df1.parse(wlgbHxyhDzjl.getNotifyTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        wlgbJdDjLsjlb1.setSfxyld(0);
        wlgbJdDjLsjlb1.setSfbjdjwk(0);
        wlgbJdDjLsjlb1.setSfdz(1);
        wlgbJdDjLsjlb1.setDzsj(date);
        wlgbJdDjLsjlb1.setSfhk(1);

        List<WlgbJdDjbbd> list1 = wlgbJdDjbbdService.queryByLshAndSfScAndDjLx(wlgbJdDjLsjlb1.getLsh(), 1);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        wlgbJdDjLsjlb1.setSfbjdj(0);

        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }

        if (list1.size() > 0) {
            wlgbJdDjLsjlb1.setSfxyld(1);
            wlgbJdDjLsjlb1.setSfbjdjwk(1);
            String finalToken = token;
            String finalOutTradeNo = outTradeNo;
            list1.forEach(l -> {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("sfxyld", 1);
                jsonObject1.put("sfbjdjwk", 1);
                for (int i = 0; i < 5; i++) {
                    GatewayResult gatewayResult;
                    try {
                        gatewayResult = DingBdLcConfig.xgBdSl(finalToken, ydAppkey, l.getFqrId(), l.getFormInstId(), jsonObject1.toJSONString());
                    } catch (Exception e) {
                        e.printStackTrace();
                        gatewayResult = new GatewayResult();
                    }
                    Boolean success = gatewayResult.getSuccess();
                    if (success != null && !success) {
                        if (i == 4) {
                            try {
                                String context1 = "定金是补交到账出错，收款编号：" + finalOutTradeNo + "，错误原因：" + gatewayResult.toString();
                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
//                                    DingDBConfig.sendGztzText(dingkey, "159909317438346", context1);
                            } catch (ApiException e) {
                                e.printStackTrace();
                            }
                        }
                    } else {
                        break;
                    }
                }
            });
            wlgbJdDjLsjlb1.setSfbjdj(1);
        }
        wlgbJdDjLsjlb1.setSkbh(wlgbHxyhDzjl.getOutTradeNo());

        WlgbJdDjbbd wlgbJdDjbbd = new WlgbJdDjbbd();
        BeanUtils.copyProperties(wlgbJdDjLsjlb1, wlgbJdDjbbd);
        JSONObject jsonObjectlc = fqDjBdData(wlgbJdDjbbd);
        //定金表唯一标识
        jsonObjectlc.put("djwybs", IdConfig.uuId());


        String formId = "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1";

        GatewayResult gatewayResult = null;
        for (int i = 0; i < 5; i++) {
            try {
                gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObjectlc.toString(), wlgbJdDjbbd.getFqrId(), formId);
            } catch (Exception e) {
                e.printStackTrace();
                gatewayResult = new GatewayResult();
            }
            Boolean success = gatewayResult.getSuccess();
            if (success != null && !success) {
                if (i == 4) {
                    try {
                        String context1 = "定金到账同步表单出错了4，收款编号：" + outTradeNo + "，错误原因：" + gatewayResult.toString();
                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                break;
            }
        }

        return Result.OK();
    }

    /**
     * 提交预定单异步
     */
    @RequestMapping(value = "saveYddTask")
    public void saveYddTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return;
        }
        JSONObject jsonObject2 = JSONObject.parseObject(datas);
        if (jsonObject2 == null || jsonObject2.size() == 0) {
            return;
        }
        String khxm = jsonObject2.getString("khxm");
        String khdh = jsonObject2.getString("khdh");
        String fddh = jsonObject2.getString("fddh");
        String khsfz = jsonObject2.getString("khsfz");
        String dwmc = jsonObject2.getString("dwmc");
        String cdf = jsonObject2.getString("cdf");
        String hfyj = jsonObject2.getString("hfyj");
        String jcsj = jsonObject2.getString("jcsj");
        String tcsj = jsonObject2.getString("tcsj");
        String bsmc = jsonObject2.getString("bsmc");
        String rs = jsonObject2.getString("rs");
        String hps = jsonObject2.getString("hps");
        String hpssl = jsonObject2.getString("hpssl");
        String ch = jsonObject2.getString("ch");
        String chsl = jsonObject2.getString("chsl");
        String cy = jsonObject2.getString("cy");
        String cysl = jsonObject2.getString("cysl");
        String bx = jsonObject2.getString("bx");
        String bxsl = jsonObject2.getString("bxsl");
        String sy = jsonObject2.getString("sy");
        String sysl = jsonObject2.getString("sysl");
        String ddbh = jsonObject2.getString("ddbh");
        String sysxmc = jsonObject2.getString("sysxmc");
        String chmc = jsonObject2.getString("chmc");
        String cymc = jsonObject2.getString("cymc");
        String bxmc = jsonObject2.getString("bxmc");
        String hpsmc = jsonObject2.getString("hpsmc");
        String accountid = jsonObject2.getString("accountid");
        CrmQbkh crmQbkh = new CrmQbkh();
        crmQbkh.setQkhdh(khdh);
        crmQbkh.setQxxzt("0");
        CrmQbkh crmQbkh1 = crmQbkhService.queryCrmQbKhByCrmQbKh(crmQbkh);
        Date xjctime = new Date(jcsj.replaceAll("-", "/"));
        Date xtctime = new Date(tcsj.replaceAll("-", "/"));
        String bsid = bsmc;
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(bsid);
        if (villa == null) {
            villa = weiLianService.queryVillaByVname(bsid);
            if (villa != null) {
                bsid = villa.getVid();
            }
        }
        //协议单撞单判断
        TbXyd tbXyd = new TbXyd();
        tbXyd.setXbsmc(bsid);
        tbXyd.setXjctime(xjctime);
        tbXyd.setXtctime(xtctime);
        if (tbXyd.getXjctime().getTime() >= tbXyd.getXtctime().getTime()) {
            return;
        }
        Integer countXydSfZd = weiLianService.queryCountXydSfZd(tbXyd);
        if (countXydSfZd > 0) {
            return;
        }
        //预留撞单
        WlgbYlxd wlgbYlxd = new WlgbYlxd();
        wlgbYlxd.setXbsmc(bsid);
        wlgbYlxd.setXjctime(tbXyd.getXjctime());
        wlgbYlxd.setXtctime(tbXyd.getXtctime());
        Integer countWlgbYlxdSfZd = weiLianService.queryCountWlgbYlxdSfZd(wlgbYlxd);
        if (countWlgbYlxdSfZd > 0) {
            return;
        }
        //预定单撞单
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXbsmc(bsid);
        tbYddXyd.setXjctime(tbXyd.getXjctime());
        tbYddXyd.setXtctime(tbXyd.getXtctime());
        Integer countTbYddXydSfZd = weiLianService.queryCountTbYddXydSfZd(tbYddXyd);
        if (countTbYddXydSfZd > 0) {
            return;
        }
        String id = IdConfig.uuId();
        String tjname = jsonObject2.getString("tjname");
        TbKhlyxz tbKhlyxz = weiLianService.queryKhLyXzByLidOrLname(tjname);
        JSONObject jsonObject1 = new JSONObject();
        if (tbKhlyxz != null) {
            jsonObject1.put("tdxzmc", tbKhlyxz.getLname());
            jsonObject1.put("xtdxz", tbKhlyxz.getLid());
            tbYddXyd.setXtdxz(tbKhlyxz.getLid());
        }
        jsonObject1.put("xddbh", ddbh);
        jsonObject1.put("xid", id);
        jsonObject1.put("xbsmc", bsid);
        jsonObject1.put("xbsmc2", bsid);
        jsonObject1.put("bsname", villa != null ? villa.getVname() : null);
        DingdingEmployee ding;
        if (crmQbkh1 != null) {
            jsonObject1.put("qqid", crmQbkh1.getCrmbh());
            tbYddXyd.setQqid(crmQbkh1.getCrmbh());
            ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(crmQbkh1.getQfzrid());
            if (ding == null) {
                if (isEmpty(fddh)) {
                    ding = weiLianDdXcxService.queryDingdingEmployeeByUserId("15349026426046931");
                } else {
                    ding = weiLianDdXcxService.queryDingdingEmployeeBySJH(fddh);
                }
            }
        } else {
            if (isEmpty(fddh)) {
                ding = weiLianDdXcxService.queryDingdingEmployeeByUserId("15349026426046931");
            } else {
                ding = weiLianDdXcxService.queryDingdingEmployeeBySJH(fddh);
            }
        }
        tbYddXyd.setXbsname(villa != null ? villa.getVname() : null);
        tbYddXyd.setXfd(ding.getName());
        tbYddXyd.setXfdid(ding.getUserid());
        tbYddXyd.setXfddh(ding.getMobile());
        tbYddXyd.setXzk(khxm);
        tbYddXyd.setXzkdh(khdh);
        tbYddXyd.setXzksfz(khsfz);
        tbYddXyd.setXdwmc(dwmc);
        tbYddXyd.setXrs(rs != null ? Integer.parseInt(rs) : null);
        tbYddXyd.setXqkzj(cdf != null ? Double.parseDouble(cdf) : null);

        tbYddXyd.setXhpssl(hpssl != null && !"".equals(hpssl) ? Integer.parseInt(hpssl) : 0);
        tbYddXyd.setXhpsdj(hps != null && !"".equals(hps) ? Double.parseDouble(hps) : 0.0);
        tbYddXyd.setXhpsfy(tbYddXyd.getXhpssl() * tbYddXyd.getXhpsdj());

        tbYddXyd.setXsysl(sysl != null && !"".equals(sysl) ? Integer.parseInt(sysl) : 0);
        tbYddXyd.setXsydj(sy != null && !"".equals(sy) ? Double.parseDouble(sy) : 0.0);
        tbYddXyd.setXsysxze(tbYddXyd.getXsysl() * tbYddXyd.getXsydj());

        tbYddXyd.setXchsl(chsl != null && !"".equals(chsl) ? Integer.parseInt(chsl) : 0);
        tbYddXyd.setXchdj(ch != null && !"".equals(ch) ? Double.parseDouble(ch) : 0.0);
        tbYddXyd.setXchze(tbYddXyd.getXchsl() * tbYddXyd.getXchdj());

        tbYddXyd.setXztze(tbYddXyd.getXchze() + tbYddXyd.getXsysxze());

        tbYddXyd.setXdcsl(cysl != null && !"".equals(cysl) ? Integer.parseInt(cysl) : 0);
        tbYddXyd.setXdcdj(cy != null && !"".equals(cy) ? Double.parseDouble(cy) : 0.0);
        tbYddXyd.setXdcze(tbYddXyd.getXdcsl() * tbYddXyd.getXdcdj());

        tbYddXyd.setXbxrs(bxsl != null && !"".equals(bxsl) ? Integer.parseInt(bxsl) : 0);
        tbYddXyd.setXbxdj(bx != null && !"".equals(bx) ? Double.parseDouble(bx) : 0.0);
        tbYddXyd.setXbxzje(tbYddXyd.getXbxrs() * tbYddXyd.getXbxdj());

        tbYddXyd.setXddbh(ddbh);
        tbYddXyd.setXid(id);
        jsonObject1.put("xfd", ding.getUserid());
        jsonObject1.put("xfddh", ding.getMobile());
        jsonObject1.put("xzk", khxm);
        jsonObject1.put("xzkdh", khdh);
        jsonObject1.put("xjctime2", xjctime);
        jsonObject1.put("xjctime", xjctime);
        jsonObject1.put("xtctime", xtctime);
        jsonObject1.put("xzksfz", khsfz);
        jsonObject1.put("xdwmc", dwmc);
        jsonObject1.put("xrs", rs);
        jsonObject1.put("xqkzj", cdf);
        jsonObject1.put("xhfyj", hfyj);
        jsonObject1.put("xsfhps", tbYddXyd.getXhpsfy() > 0 ? 1 : 0);
        jsonObject1.put("xhpsfy", tbYddXyd.getXhpsfy());
        jsonObject1.put("xztze", tbYddXyd.getXztze());
        jsonObject1.put("numberField_lma6qozq", 0);
        if (tbYddXyd.getXchze() > 0) {
            jsonObject1.put("sfxzch", 1);
            List<JSONObject> list = new ArrayList<>();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("chgmsl", tbYddXyd.getXchsl());
            jsonObject.put("sj", tbYddXyd.getXchdj());
            jsonObject.put("chzje", tbYddXyd.getXchze());
            jsonObject.put("chbh", tbYddXyd.getXddbh());
            list.add(jsonObject);

            jsonObject1.put("chtczbd", list);
            jsonObject1.put("numberField_lma6qozq", tbYddXyd.getXchze());
        } else {
            jsonObject1.put("sfxzch", 0);
        }
        jsonObject1.put("numberField_lma6qozp", 0);
        if (tbYddXyd.getXsysxze() > 0) {
            jsonObject1.put("sftjchdp", 1);
            List<JSONObject> list = new ArrayList<>();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tcmc1", "摄影摄像");
            jsonObject.put("chgmsl1", tbYddXyd.getXsysl());
            jsonObject.put("sj1", tbYddXyd.getXsydj());
            jsonObject.put("chzje1", tbYddXyd.getXsysxze());
            jsonObject.put("chbh1", tbYddXyd.getXddbh());
            list.add(jsonObject);

            jsonObject1.put("chtczbd1", list);
            jsonObject1.put("numberField_lma6qozp", tbYddXyd.getXsysxze());
        } else {
            jsonObject1.put("sftjchdp", 0);
        }
        jsonObject1.put("xdcze", tbYddXyd.getXdcze());
        if (tbYddXyd.getXdcze() > 0) {
            jsonObject1.put("xsfdc", 1);
            List<JSONObject> list = new ArrayList<>();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("cysj", tbYddXyd.getXdcdj());
            jsonObject.put("cygmsl", tbYddXyd.getXdcsl());
            jsonObject.put("cyje", tbYddXyd.getXdcze());
            jsonObject.put("cybs", "xcx" + DateFormatConfig.dfSjc());
            jsonObject.put("bm", villa != null ? villa.getVcyqybh() : "");
            list.add(jsonObject);
            jsonObject1.put("cyzbd", list);
        } else {
            jsonObject1.put("xsfdc", 0);
        }
        jsonObject1.put("xbxzje", tbYddXyd.getXbxzje());
        if (tbYddXyd.getXbxzje() > 0) {
            jsonObject1.put("xsfbx", 1);
            jsonObject1.put("xbxrs", tbYddXyd.getXbxrs());
            jsonObject1.put("xbxdj", tbYddXyd.getXbxdj());
        } else {
            jsonObject1.put("xsfbx", 0);
        }
        //场次赋值判断
        try {
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
            if (df.format(xjctime).equals(df.format(xtctime))) {
                if (xjctime.getHours() == 10 && xtctime.getHours() == 17) {
                    jsonObject1.put("cc", 1);
                } else {
                    jsonObject1.put("cc", 5);
                }
            } else {
                Calendar c = Calendar.getInstance();
                c.setTime(xjctime);
                if (df.format(c.getTime()).equals(df.format(xtctime))) {
                    if (xjctime.getHours() == 18 && xtctime.getHours() == 8) {
                        jsonObject1.put("cc", 2);
                    } else if (xjctime.getHours() == 10 && xtctime.getHours() == 8) {
                        jsonObject1.put("cc", 3);
                    } else if (xjctime.getHours() == 18 && xtctime.getHours() == 17) {
                        jsonObject1.put("cc", 4);
                    } else {
                        jsonObject1.put("cc", 5);
                    }
                } else {
                    jsonObject1.put("cc", 5);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //增值名称辅助信息
        jsonObject1.put("textField_ltoijeu0", hpsmc);
        jsonObject1.put("textField_ltoijeu1", chmc);
        jsonObject1.put("textField_ltoijeu2", sysxmc);
        jsonObject1.put("textField_ltoijeu3", cymc);
        jsonObject1.put("textField_ltoijeu5", bxmc);
        //预定单转协议单需要设置一些默认值
        jsonObject1.put("sfbj", "否");
        jsonObject1.put("xsfsywxfwdddje", "0");
        jsonObject1.put("xzdcl", "0");
        jsonObject1.put("xsfzrcs", "0");
        jsonObject1.put("xsfxyjbs", "0");
        jsonObject1.put("sfcxptxd", "1");
        jsonObject1.put("xsfsc", "0");
//        jsonObject1.put("xdjtype", "1");
        jsonObject1.put("xiskfp", "0");
        jsonObject1.put("xsfms", "0");
        jsonObject1.put("xsfbdf", "0");
        jsonObject1.put("xsfbwsf", "0");
        jsonObject1.put("xsfbcfsyf", "0");
        jsonObject1.put("xisxzcj", "1");
        jsonObject1.put("xsfhxgjcj", "1");
        jsonObject1.put("xyxxt", "1");
        jsonObject1.put("sftscc", "0");
        jsonObject1.put("xzfts", 0);
        jsonObject1.put("xhpts", 0);
        jsonObject1.put("xjzts", 0);
        jsonObject1.put("xspyhq", 0);
        jsonObject1.put("xzshyksl", 0);
        jsonObject1.put("xcdzjbj", 0);
        jsonObject1.put("xzzfybj", 0);
        jsonObject1.put("xbjdj", 0);
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_RDI3PDV2X43HWZM6ACGS");
        ydAppkey.setToken("9X866BD1IR0ICG8C8T6NI4AKCWXX36BCVLVRL04");

        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdBd ydBd = new YdBd();
        ydBd.setFormid("FORM-F2F68383408D4FF19BB1EC8B2DC007ECF0SE");
        ydBd.setCode("TPROC--WV866IC1PX1I2DOKAU97B83FK7M62MVYWLVRL0");
        GatewayResult gatewayResult;
        try {
            gatewayResult = DingBdLcConfig.fqXzLcSl(token, ydAppkey, "012412221639786136545", ydBd.getFormid(), ydBd.getCode(), jsonObject1.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
            gatewayResult = new GatewayResult();
        }
        System.out.println(gatewayResult);
        tbYddXyd.setXslid(gatewayResult.getResult());
        //在这里设置定金二维码生成、失效时间是因为小程序端需要截止时间
        tbYddXyd.setXdjurltime(new Date());
        Calendar c = Calendar.getInstance();
        c.setTime(tbYddXyd.getXdjurltime());
        c.add(Calendar.HOUR_OF_DAY, 2);
        tbYddXyd.setXdjsxtime(c.getTime());
        System.out.println("失效时间" + c.getTime());
        tbYddXyd.setAccountid(accountid);
        tbYddXydService.save(tbYddXyd);

        //设置操作日志
        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXlid(tbYddXyd.getXid());
        wlgbXydLog.setXltime(new Date());
        wlgbXydLog.setXluserid("客户");
        wlgbXydLog.setXlname("客户小程序提交");
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXltext("已提交，客服处理中");
        wlgbXydLogService.save(wlgbXydLog);
    }

    /**
     * 重新生成预定单定金二维码异步
     */
    @RequestMapping(value = "yddDjEwmScTask")
    public void yddDjEwmScTask(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        if (ddbh == null || "".equals(ddbh)) {
            return;
        }
        TbYddXyd tbYddXyd2 = new TbYddXyd();
        tbYddXyd2.setXsfsc(0);
        tbYddXyd2.setXddbh(ddbh);
        TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd2);
        if (tbYddXyd1 == null) {
            return;
        }
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String skbh = df1.format(new Date());
        String url = ddxturl + "/ysdjtb/wlgb/xyd/yddPdFs?yddBh=" + skbh;
        //收款二维码
        String ewm = null;
        try {
            ewm = scEwm(url);
        } catch (Exception e) {
            e.printStackTrace();
        }

        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXid(tbYddXyd1.getXid());
        tbYddXyd.setXdjurl(ewm);
        tbYddXyd.setXdjurltime(new Date());
        tbYddXyd.setXskbh(skbh);
        Calendar c = Calendar.getInstance();
        c.setTime(tbYddXyd.getXdjurltime());
        c.add(Calendar.HOUR_OF_DAY, 2);
        tbYddXyd.setXdjsxtime(c.getTime());
        System.out.println("失效时间" + c.getTime());
        ;
        tbYddXydService.updateById(tbYddXyd);
        //设置操作日志
        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXlid(tbYddXyd2.getXid());
        wlgbXydLog.setXltime(new Date());
        wlgbXydLog.setXluserid("客户操作");
        wlgbXydLog.setXlname("客户操作");
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXltext("重新生成预定单定金二维码");
        wlgbXydLogService.save(wlgbXydLog);

        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        Map<String, Object> map = new HashMap<>();
        map.put("imageField_lrvm7zl6", YdConfig.setTpList(ewm, "支付二维码"));
        map.put("dateField_lrvm7zl7", tbYddXyd.getXdjurltime());

        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_RDI3PDV2X43HWZM6ACGS");
        ydAppkey.setToken("9X866BD1IR0ICG8C8T6NI4AKCWXX36BCVLVRL04");

        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = LcConfig.gxLcSl("012412221639786136545", map, ydAppkey, tbYddXyd2.getXslid(), token);
        System.out.println("gatewayResult === " + gatewayResult);

        String text = "您发起的预定单二维码收款已重新生成了二维码！";
        text += "\n\n订单编号：" + tbYddXyd1.getXddbh();
        text += "\n门店：" + tbYddXyd1.getXbsname();
        text += "\n客户姓名：" + tbYddXyd1.getXzk();
        text += "\n客户电话：" + tbYddXyd1.getXzkdh();
        text += "\n进场时间：" + DateFormatConfig.df3(tbYddXyd1.getXjctime());
        text += "\n退场时间：" + DateFormatConfig.df3(tbYddXyd1.getXtctime());
        text += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
        try {
            DingDBConfig.sendGztzText(dingkey, tbYddXyd1.getXfdid(), text);
        } catch (ApiException apiException) {
            apiException.printStackTrace();
        }

    }

    /**
     * 小程序取消预定单
     */
    @RequestMapping(value = "deleteYddTask")
    public void deleteYddTask(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXsfsc(0);
        tbYddXyd.setXddbh(ddbh);
        TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd);
        if (tbYddXyd1 == null) {
            return;
        }
        if ("已提交，客服处理中".equals(tbYddXyd1.getXstatu()) || "已处理，待交定金".equals(tbYddXyd1.getXstatu())) {
            TbYddXyd tbYddXyd2 = new TbYddXyd();
            tbYddXyd2.setXid(tbYddXyd1.getXid());
            tbYddXyd2.setXsfsc(1);
            tbYddXyd2.setXstatu("已取消");
            tbYddXyd2.setXscer("小程序取消");
            tbYddXydService.deleteByTbYddXyd(tbYddXyd2);
            System.out.println("已取消预定单" + ddbh);
            //设置操作日志
            WlgbXydLog wlgbXydLog = new WlgbXydLog();
            wlgbXydLog.setXlid(tbYddXyd2.getXid());
            wlgbXydLog.setXltime(new Date());
            wlgbXydLog.setXluserid("客户操作");
            wlgbXydLog.setXlname("客户操作");
            wlgbXydLog.setXlid(IdConfig.uuId());
            wlgbXydLog.setXltext("已取消预定单");
            wlgbXydLogService.save(wlgbXydLog);

            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");

            String text = "客户主动取消预定单！";
            text += "\n\n订单编号：" + tbYddXyd1.getXddbh();
            text += "\n门店：" + tbYddXyd1.getXbsname();
            text += "\n客户姓名：" + tbYddXyd1.getXzk();
            text += "\n客户电话：" + tbYddXyd1.getXzkdh();
            text += "\n进场时间：" + DateFormatConfig.df3(tbYddXyd1.getXjctime());
            text += "\n退场时间：" + DateFormatConfig.df3(tbYddXyd1.getXtctime());
            text += "\n送达时间：" + DateFormatConfig.df1(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, tbYddXyd1.getXfdid(), text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            YdAppkey ydAppkey = new YdAppkey();
            ydAppkey.setAppkey("APP_RDI3PDV2X43HWZM6ACGS");
            ydAppkey.setToken("9X866BD1IR0ICG8C8T6NI4AKCWXX36BCVLVRL04");

            String token = null;
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            GatewayResult gatewayResult = new GatewayResult();
            try {
                gatewayResult = YdConfig.lcZz(ydAppkey, token, tbYddXyd1.getXslid());
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println(gatewayResult);
            if (!gatewayResult.getSuccess()) {
                try {
                    String context1 = "客户主动取消预定单终止流程失败了，订单编号：" + tbYddXyd1.getXddbh() + "，错误原因：" + gatewayResult.toString();
                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                } catch (ApiException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 审批取消预定单
     */
    @RequestMapping(value = "qxYddTask")
    public void qxYddTask(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        String userId = request.getParameter("userId");
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXsfsc(0);
        tbYddXyd.setXddbh(ddbh);
        TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd);
        if (tbYddXyd1 == null) {
            return;
        }
        if ("已提交，客服处理中".equals(tbYddXyd1.getXstatu()) || "已处理，待交定金".equals(tbYddXyd1.getXstatu())) {
            DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);
            TbYddXyd tbYddXyd2 = new TbYddXyd();
            tbYddXyd2.setXid(tbYddXyd1.getXid());
            tbYddXyd2.setXsfsc(1);
            tbYddXyd2.setXstatu("已取消");
            tbYddXyd2.setXscer(ding != null ? ding.getName() : null);
            tbYddXyd2.setXscerid(ding != null ? ding.getUserid() : null);
            tbYddXydService.deleteByTbYddXyd(tbYddXyd2);
            System.out.println("已取消预定单" + ddbh);
            //设置操作日志
            WlgbXydLog wlgbXydLog = new WlgbXydLog();
            wlgbXydLog.setXlid(tbYddXyd2.getXid());
            wlgbXydLog.setXltime(new Date());
            wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
            wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
            wlgbXydLog.setXlid(IdConfig.uuId());
            wlgbXydLog.setXltext("已取消预定单");
            wlgbXydLogService.save(wlgbXydLog);

            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");

            String text = "您已取消预定单！";
            text += "\n\n订单编号：" + tbYddXyd1.getXddbh();
            text += "\n门店：" + tbYddXyd1.getXbsname();
            text += "\n客户姓名：" + tbYddXyd1.getXzk();
            text += "\n客户电话：" + tbYddXyd1.getXzkdh();
            text += "\n进场时间：" + DateFormatConfig.df3(tbYddXyd1.getXjctime());
            text += "\n退场时间：" + DateFormatConfig.df3(tbYddXyd1.getXtctime());
            text += "\n送达时间：" + DateFormatConfig.df1(new Date());
            try {
                DingDBConfig.sendGztzText(dingkey, tbYddXyd1.getXfdid(), text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String post = null;
            try {
                Map<String, String> map = new HashMap<>();
                map.put("ddbh", tbYddXyd.getXddbh());
                map.put("zt", tbYddXyd2.getXstatu());
                //总价
                map.put("zj", tbYddXyd1.getXhfyj() + tbYddXyd1.getXztze() + tbYddXyd1.getXdcze() + tbYddXyd1.getXhpsfy() + tbYddXyd1.getXbxzje() + "");
                post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/b2e50678-62d2-4d65-a042-27427265a26f", map);
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    String context1 = "业务员取消预定单将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，错误原因：" + e;
                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                } catch (ApiException e1) {
                    e1.printStackTrace();
                }
            }
            try {
                if (post != null && !"".equals(post)) {
                    JSONObject jsonObject1 = JSONObject.parseObject(post);
                    String jg = jsonObject1.getString("jg");
                    if (!"ok".equalsIgnoreCase(jg)) {
                        try {
                            String context1 = "业务员取消预定单将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                    }
                }
            } catch (Exception e) {
                try {
                    String context1 = "业务员取消预定单将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                } catch (ApiException e1) {
                    e1.printStackTrace();
                }
                e.printStackTrace();
            }
        }
    }


    /**
     * 预定单提交至协议单
     */
    @RequestMapping(value = "yddSaveXydTask")
    public void yddSaveXydTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String xid = jsonObject.getString("xid");
        String xsendder = jsonObject.getString("xsendder");
        String xddbh = jsonObject.getString("xddbh");
        String userId = request.getParameter("userId");
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXsfsc(1);
        tbYddXyd.setXid(xid);
        tbYddXydService.deleteByTbYddXyd(tbYddXyd);
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("下单");
        GatewayResult gatewayResult = new GatewayResult();
        try {
            gatewayResult = YdConfig.xzBdSl(token, ydAppkey, jsonObject.toJSONString(), xsendder != null && !"".equals(xsendder) ? xsendder : userId, ydBd.getFormid());
        } catch (Exception e) {
            e.printStackTrace();
            try {
                String context1 = "预定单完成同步至协议单出错，订单编号：" + xddbh + "，错误原因：" + e;
                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
            } catch (ApiException e1) {
                e1.printStackTrace();
            }
        }
        System.out.println(gatewayResult);
        if (!gatewayResult.getSuccess()) {
            tbYddXyd.setXsfsc(0);
            tbYddXyd.setXid(xid);
            tbYddXydService.updateById(tbYddXyd);
            try {
                String context1 = "预定单完成同步至协议单出错，订单编号：" + xddbh + "，错误原因：" + gatewayResult;
                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
            } catch (ApiException e1) {
                e1.printStackTrace();
            }
        }
        //设置操作日志
        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXlid(xid);
        wlgbXydLog.setXltime(new Date());
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXltext("预定单提交至协议单");
        wlgbXydLogService.save(wlgbXydLog);
    }

    /**
     * 业务员审核处理——生成定金二维码
     */
    @RequestMapping(value = "editYddTask")
    public void editYddTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userId = request.getParameter("userId");
        if (datas == null || "".equals(datas)) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        TbYddXyd tbYddXyd = JSONObject.toJavaObject(jsonObject, TbYddXyd.class);

        TbYddXyd tbYddXyd1 = new TbYddXyd();
        tbYddXyd1.setXsfsc(0);
        tbYddXyd1.setXid(tbYddXyd.getXid());
        TbYddXyd tbYddXyd2 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd1);
        if (tbYddXyd2 == null) {
            return;
        }
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        if (tbYddXyd.getXdjtype() != null && !"".equals(tbYddXyd.getXdjtype())) {
            if ("1".equals(tbYddXyd.getXdjtype())) {
                String url = ddxturl + "/ysdjtb/wlgb/xyd/yddPdFs?yddBh=" + tbYddXyd2.getXddbh();
                //收款二维码
                String ewm = null;
                try {
                    ewm = scEwm(url);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                tbYddXyd.setXdjurl(ewm);
                Map<String, Object> map = new HashMap<>();
                map.put("imageField_lrvm7zl6", YdConfig.setTpList(ewm, "支付二维码"));
                map.put("dateField_lrvm7zl7", tbYddXyd.getXdjurltime());

                YdAppkey ydAppkey = new YdAppkey();
                ydAppkey.setAppkey("APP_RDI3PDV2X43HWZM6ACGS");
                ydAppkey.setToken("9X866BD1IR0ICG8C8T6NI4AKCWXX36BCVLVRL04");

                String token = null;
                try {
                    token = DingToken.token(dingkey);
                } catch (ApiException e) {
                    e.printStackTrace();
                }
                GatewayResult gatewayResult = LcConfig.gxLcSl("012412221639786136545", map, ydAppkey, tbYddXyd2.getXslid(), token);
                System.out.println("gatewayResult === " + gatewayResult);
            } else {
                String ptlx = jsonObject.getString("radioField_ltvcwoxj");
                if (ptlx != null && !"".equals(ptlx)) {
                    tbYddXyd.setQmpttype(ptlx);
                    if ("抖音".equals(ptlx)) {
                        tbYddXyd.setDymdid(jsonObject.getString("dymdxz"));
                    } else {
                        tbYddXyd.setMtmdid(jsonObject.getString("mdxz"));
                    }
                }
            }
        }
        if (tbYddXyd.getXfd() != null && !"".equals(tbYddXyd.getXfd())) {
            String userId1 = CommonConfig.ydRyZjJq(tbYddXyd.getXfd());
            DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
            if (dingdingEmployee != null) {
                tbYddXyd.setXfd(dingdingEmployee.getName());
                tbYddXyd.setXfdid(dingdingEmployee.getUserid());
            }
        }
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);

        tbYddXyd.setXdjurltime(new Date());
        tbYddXyd.setXskbh(tbYddXyd2.getXddbh());
        Calendar c = Calendar.getInstance();
        c.setTime(tbYddXyd.getXdjurltime());
        c.add(Calendar.HOUR_OF_DAY, 2);
        tbYddXyd.setXdjsxtime(c.getTime());
        System.out.println("失效时间" + c.getTime());
        tbYddXyd.setXstatu("已处理，待交定金");
        tbYddXydService.updateById(tbYddXyd);
        //设置操作日志
        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXlid(tbYddXyd2.getXid());
        wlgbXydLog.setXltime(new Date());
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXltext("已处理，待交定金");
        wlgbXydLogService.save(wlgbXydLog);
        String post = null;
        try {
            Map<String, String> map = new HashMap<>();
            map.put("ddbh", tbYddXyd2.getXddbh());
            map.put("xhfyj", String.valueOf(tbYddXyd2.getXhfyj()));
            map.put("xhpsfy", String.valueOf(tbYddXyd2.getXhpsfy()));
            map.put("xztze", String.valueOf(tbYddXyd2.getXztze()));
            map.put("xdcze", String.valueOf(tbYddXyd2.getXdcze()));
            map.put("xbxzje", String.valueOf(tbYddXyd2.getXbxzje()));
            map.put("xsysxze", String.valueOf(tbYddXyd2.getXsysxze()));
            map.put("zt", tbYddXyd.getXstatu());
            //总价
            map.put("zj", tbYddXyd2.getXhfyj() + tbYddXyd2.getXztze() + tbYddXyd2.getXdcze() + tbYddXyd2.getXhpsfy() + tbYddXyd2.getXbxzje() + "");
            post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/b2e50678-62d2-4d65-a042-27427265a26f", map);
        } catch (Exception e) {
            e.printStackTrace();
            try {
                String context1 = "业务员审核预定单将状态更新至小程序出错了，订单编号：" + tbYddXyd2.getXddbh() + "，错误原因：" + e;
                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
            } catch (ApiException e1) {
                e1.printStackTrace();
            }
        }
        try {
            if (post != null && !"".equals(post)) {
                JSONObject jsonObject1 = JSONObject.parseObject(post);
                String jg = jsonObject1.getString("jg");
                if (!"ok".equalsIgnoreCase(jg)) {
                    try {
                        String context1 = "业务员审核预定单将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd2.getXddbh() + "，请求结果：" + post;
                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e1) {
                        e1.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            try {
                String context1 = "业务员审核预定单将状态更新至小程序出错了，订单编号：" + tbYddXyd2.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
            } catch (ApiException e1) {
                e1.printStackTrace();
            }
            e.printStackTrace();
        }

        //通过微信的服务通知给客户发送消息,通知客户去付定金
        try {
            System.out.println("************" + tbYddXyd.getXysdj());
            Map<String, String> map2 = new HashMap<>();
            map2.put("character_string3", tbYddXyd.getXddbh());
            map2.put("amount2", String.valueOf(tbYddXyd.getXysdj()));
            map2.put("time7", DateFormatConfig.df1(tbYddXyd2.getCreateTime()));
            map2.put("accountId", tbYddXyd2.getAccountid());
            map2.put("templateId", "w10VsOvg74aAEFy3YQvHpILdEJBzQfA5RfIier--4eo");
            post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/3d016937-2c0c-4899-8b2a-07b0db3260b1", map2);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


    }

    /**
     * 提交券码进行验券异步
     */
    @RequestMapping(value = "tjYddQmTask")
    public void tjYddQmTask(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        if (ddbh == null || "".equals(ddbh)) {
            return;
        }
        String qm = request.getParameter("qm");
        if (qm == null || "".equals(qm)) {
            return;
        }
        TbYddXyd tbYddXyd = new TbYddXyd();
        tbYddXyd.setXsfsc(0);
        tbYddXyd.setXddbh(ddbh);
        TbYddXyd tbYddXyd1 = tbYddXydService.queryTbYddXydByTbYddXyd(tbYddXyd);
        if (tbYddXyd1 == null) {
            return;
        }

        TbYddXyd tbYddXyd2 = new TbYddXyd();
        tbYddXyd2.setXid(tbYddXyd1.getXid());
        tbYddXyd2.setQm(qm);
        tbYddXydService.updateById(tbYddXyd2);

        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_RDI3PDV2X43HWZM6ACGS");
        ydAppkey.setToken("9X866BD1IR0ICG8C8T6NI4AKCWXX36BCVLVRL04");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        JSONObject map = new JSONObject();
        map.put("textField_ltx11j1s", qm);

        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }


        GatewayResult gatewayResult = new GatewayResult();
        try {
            gatewayResult = YdConfig.querySpJl(token, ydAppkey, tbYddXyd1.getXslid());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (gatewayResult.getSuccess()) {
            JSONArray objects = JSONObject.parseArray(gatewayResult.getResult());
            for (Object obj : objects) {
                JSONObject jsonObject1 = (JSONObject) obj;
                if ("TODO".equals(jsonObject1.getString("type")) && "客户提交验券券码".equals(jsonObject1.getString("showName"))) {
                    System.out.println("查到任务");
                    long taskId = jsonObject1.getLong("taskId");
                    //准备执行任务
                    GatewayResult gatewayResult1 = null;
                    try {
                        gatewayResult1 = YdConfig.zxYdSpJdRw(token, tbYddXyd1.getXslid(), ydAppkey, "客户已提交券码", map.toJSONString(), taskId, "012412221639786136545");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    System.out.println(gatewayResult1);
                }
            }
        }
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbYddXyd1.getXfdid());
        if ("新美大".equals(tbYddXyd1.getQmpttype())) {
            //数据库中所有的token
            List<WlgbMtToken> tokenlist = wlgbMtTokenService.queryAllList();
            WlgbMtMd wlgbMtMd = wlgbMtMdService.queryByShopUuId(tbYddXyd1.getMtmdid());
            //判断有没有券验券成功
            WlgbMtJl wlgbMtJl = new WlgbMtJl();
            wlgbMtJl.setId(IdConfig.uuId());
            wlgbMtJl.setYqr(ding != null ? ding.getUserid() : null);
            wlgbMtJl.setYqrxm(ding != null ? ding.getName() : null);
            wlgbMtJl.setMtqh(qm);
            wlgbMtJl.setYqsj(new Date());
            boolean bb = true;
            double sum = 0.0;
            String cgqm = "";
            String dm = "";
            YdAppkey ydAppkey1 = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
            for (WlgbMtToken f : tokenlist) {
                if (!isEmpty(f.getAppkey())) {
                    //session适用店铺查询接口
                    TuangouReceiptPrepareResponse tp = jyQm(f.getAppkey(), f.getAppsecret(), f.getToken(), qm, tbYddXyd1.getMtmdid(), null);
                    try {
                        if (tp.getCode() == 200) {
                            TuangouReceiptPrepareResponseEntity data = tp.getData();
                            if (data != null) {
                                if (data.getCount() != null) {
                                    //进行验券
                                    String id = IdConfig.uuId();
                                    TuangouReceiptConsumeResponse tt = MtConfig.getYQ(f.getAppkey(), f.getAppsecret(), f.getToken(), id, qm, data.getCount() != null ? data.getCount() : 0, tbYddXyd1.getMtmdid(), f.getZh(), f.getRemake());
                                    try {
                                        if (tt.getCode() == 200) {
                                            dm = f.getZh();
                                            if (dm != null && !"".equals(dm)) {
                                                if (dm.length() > 0) {
                                                    dm = dm.substring(dm.length() - 3);
                                                }
                                            }
                                            if (tt.getData().size() > 0) {
                                                List<TuangouReceiptPreparePaymentDetail> paymentDetail = tt.getData().get(0).getPayment_detail();
                                                double sum1s = 0;
                                                for (TuangouReceiptPreparePaymentDetail tuangouReceiptPreparePaymentDetail : paymentDetail) {
                                                    if (tuangouReceiptPreparePaymentDetail.getAmount_type() != 8 && tuangouReceiptPreparePaymentDetail.getAmount_type() != 16 && tuangouReceiptPreparePaymentDetail.getAmount_type() != 17 && tuangouReceiptPreparePaymentDetail.getAmount_type() != 18 && tuangouReceiptPreparePaymentDetail.getAmount_type() != 22) {
                                                        sum1s += tuangouReceiptPreparePaymentDetail.getAmount().setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                                                    }
                                                }
                                                sum = sum1s;
                                            }
                                            double sum2 = 0.0;
                                            for (int i = 0; i < tt.getData().size(); i++) {
                                                String receiptCode = tt.getData().get(i).getReceipt_code();
                                                WlgbMtLog wlgbMtLog = MtConfig.saveMtLog(tbYddXyd1.getXddbh(), null, ding, receiptCode, tt.getData().get(i), wlgbMtMd, tbYddXyd1.getQqid(), tbYddXyd1.getXzkdh());
                                                wlgbMtLog.setId(IdConfig.uuId());
                                                wlgbMtLogService.save(wlgbMtLog);
                                                WlgbMtJl wlgbMtJl1 = new WlgbMtJl();
                                                wlgbMtJl1.setId(IdConfig.uuId());
                                                wlgbMtJl1.setYqr(ding != null ? ding.getUserid() : null);
                                                wlgbMtJl1.setYqrxm(ding != null ? ding.getName() : null);
                                                wlgbMtJl1.setMtqh(tt.getData().get(i).getReceipt_code());
                                                wlgbMtJl1.setYqsj(new Date());
                                                wlgbMtJl1.setYqjg("验券成功");
                                                wlgbMtJl1.setDkje(tt.getData().get(i).getDeal_price());
                                                wlgbMtJl1.setMtyqjg(tt.getMsg());
                                                wlgbMtJl1.setDdbh(tbYddXyd1.getXddbh());
                                                wlgbMtJlService.save(wlgbMtJl1);
                                                JSONObject jsonObject1 = new JSONObject();
                                                jsonObject1.put("lsh", tbYddXyd1.getXddbh());
                                                double sum1 = sum / (data.getCount() != null ? data.getCount() : 0);
                                                BigDecimal bigDecimal = new BigDecimal(sum1);
                                                jsonObject1.put("je", bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                                                sum2 += bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                                                if (i == tt.getData().size() - 1) {
                                                    if (sum2 < sum) {
                                                        double ce = sum - sum2;
                                                        double bce = sum1 + ce;
                                                        BigDecimal bigDecimal2 = new BigDecimal(bce);
                                                        jsonObject1.put("je", bigDecimal2.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                                                    }
                                                }
                                                jsonObject1.put("fqr", ding != null ? ding.getUserid() : null);
                                                jsonObject1.put("fqrId", ding != null ? ding.getUserid() : null);
                                                jsonObject1.put("fqrName", ding != null ? ding.getName() : null);
                                                jsonObject1.put("fqsj", new Date());
                                                jsonObject1.put("djlx", 2);
                                                jsonObject1.put("sspt", 1);
                                                jsonObject1.put("mtmd", tbYddXyd1.getMtmdid());
                                                jsonObject1.put("hqqm", qm);
                                                jsonObject1.put("yqsfwc", 1);
                                                jsonObject1.put("yqjg", 1);
                                                jsonObject1.put("yqqm", receiptCode);
                                                jsonObject1.put("yqsj", new Date());
                                                jsonObject1.put("dm", dm);
                                                jsonObject1.put("qmsj", 1);
                                                jsonObject1.put("yqr", ding != null ? ding.getUserid() : null);
                                                jsonObject1.put("yqrId", ding != null ? ding.getUserid() : null);
                                                jsonObject1.put("yqrName", ding != null ? ding.getName() : null);
                                                jsonObject1.put("sfsc", 0);
                                                jsonObject1.put("sftk", 0);
                                                jsonObject1.put("sfsddj", 0);
                                                jsonObject1.put("sfscdj", 0);
                                                jsonObject1.put("khdh", tbYddXyd1.getXzkdh());
                                                jsonObject1.put("crmbh", tbYddXyd1.getQqid());
                                                jsonObject1.put("sfbjdj", 0);
                                                jsonObject1.put("sfxd", 1);
                                                jsonObject1.put("ddbh", tbYddXyd1.getXddbh());
                                                jsonObject1.put("xdsj", new Date());
                                                jsonObject1.put("ytje", 0);
                                                jsonObject1.put("sfbjdjwk", 0);
                                                jsonObject1.put("sfxyld", 0);
                                                //定金表唯一标识
                                                jsonObject1.put("djwybs", IdConfig.uuId());
                                                GatewayResult gatewayResult1 = null;
                                                for (int j = 0; j < 5; j++) {
                                                    gatewayResult1 = DingBdLcConfig.xzBdSl(token, ydAppkey1, jsonObject1.toJSONString(), ding != null ? ding.getUserid() : "012412221639786136545", "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1");
                                                    if (gatewayResult1.getSuccess() != null && !gatewayResult1.getSuccess()) {
                                                        if (j == 4) {
                                                            try {
                                                                String context1 = "验券成功同步表单出错了，券码：" + receiptCode + "，错误原因：" + gatewayResult1.toString();
                                                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                                            } catch (ApiException e) {
                                                                e.printStackTrace();
                                                            }
                                                        }
                                                    } else {
                                                        break;
                                                    }
                                                }
                                                bb = false;
                                                if (!"".equals(cgqm)) {
                                                    cgqm += ",";
                                                }
                                                cgqm += receiptCode;
                                            }
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            if (bb) {
                wlgbMtJl.setDdbh(tbYddXyd1.getXddbh());
                wlgbMtJl.setYqjg("不存在券码或已经验过了");
                wlgbMtJlService.save(wlgbMtJl);
                String text = "预定单客户提交的新美大验券，验券失败了！";
                text += "\n\n券码：" + qm;
                text += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
                try {
                    DingDBConfig.sendGztzText(dingkey, ding != null ? ding.getUserid() : null, text);
                } catch (ApiException e) {
                    e.printStackTrace();
                }
            } else {
                TbYddXyd tbYddXyd3 = new TbYddXyd();
                tbYddXyd3.setXid(tbYddXyd.getXid());
                tbYddXyd3.setXstatu("已交定金，等待店长主动联系您");
                tbYddXydService.updateById(tbYddXyd3);
                BigDecimal bd = new BigDecimal(sum);
                sum = bd.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                String text = "预定单客户提交的新美大验券，验券完成了！";
                text += "\n\n券码：" + cgqm;
                text += "\n金额：" + sum;
                text += "\n代码：" + dm;
                text += "\n\n送达时间：" + DateFormatConfig.df1(new Date());

                try {
                    DingDBConfig.sendGztzText(dingkey, ding != null ? ding.getUserid() : null, text);
                } catch (ApiException e) {
                    e.printStackTrace();
                }
                String post = null;
                try {
                    Map<String, String> map2 = new HashMap<>();
                    map2.put("ddbh", tbYddXyd1.getXddbh());
                    map2.put("zt", tbYddXyd3.getXstatu());
                    //总价
                    map2.put("zj", tbYddXyd1.getXhfyj() + tbYddXyd1.getXztze() + tbYddXyd1.getXdcze() + tbYddXyd1.getXhpsfy() + tbYddXyd1.getXbxzje() + "");
                    post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/b2e50678-62d2-4d65-a042-27427265a26f", map2);
                } catch (Exception e) {
                    e.printStackTrace();
                    try {
                        String context1 = "抖音验券成功将状态更新至小程序出错了，订单编号：" + tbYddXyd1.getXddbh() + "，错误原因：" + e;
                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e1) {
                        e1.printStackTrace();
                    }
                }
                try {
                    if (post != null && !"".equals(post)) {
                        JSONObject jsonObject1 = JSONObject.parseObject(post);
                        String jg = jsonObject1.getString("jg");
                        if (!"ok".equalsIgnoreCase(jg)) {
                            try {
                                String context1 = "美团验券成功将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post;
                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                            } catch (ApiException e1) {
                                e1.printStackTrace();
                            }
                        }
                    }
                } catch (Exception e) {
                    try {
                        String context1 = "美团验券成功将状态更新至小程序出错了，订单编号：" + tbYddXyd1.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e1) {
                        e1.printStackTrace();
                    }
                    e.printStackTrace();
                }

                JSONObject map1 = new JSONObject();
                //验券日期
                map1.put("xyqrq", new Date());
                //验券人
                map1.put("xyqr", tbYddXyd2.getXfd());
                //代码
                map.put("xdm", dm);
                //券码
                map.put("xfkm", cgqm);
                //验券金额
                map.put("xysdj", sum);


                GatewayResult gatewayResult2 = new GatewayResult();
                try {
                    gatewayResult2 = YdConfig.querySpJl(token, ydAppkey, tbYddXyd1.getXslid());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (gatewayResult2.getSuccess()) {
                    JSONArray objects = JSONObject.parseArray(gatewayResult2.getResult());
                    for (Object obj : objects) {
                        JSONObject jsonObject1 = (JSONObject) obj;
                        if ("TODO".equals(jsonObject1.getString("type")) && "验券".equals(jsonObject1.getString("showName"))) {
                            System.out.println("查到任务");
                            long taskId = jsonObject1.getLong("taskId");
                            //准备执行任务
                            GatewayResult gatewayResult1 = null;
                            try {
                                gatewayResult1 = YdConfig.zxYdSpJdRw(token, tbYddXyd1.getXslid(), ydAppkey, "客户提交新美大验券成功", map1.toJSONString(), taskId, "012412221639786136545");
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            System.out.println(gatewayResult1);
                        }
                    }
                }
            }
        } else {
            String encrypteddata = "";
            String poiid = tbYddXyd1.getDymdid();
            if (isEmpty(poiid)) {
                return;
            }
            YdAppkey ydAppkey1 = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");

            String dingToken = null;
            try {
                dingToken = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            //抖音主体账号
            List<FwqDyAccount> lll = fwqDyAccountService.selectAll();
            String finalDingToken = dingToken;
            String finalToken = token;
            lll.forEach(l -> {
                String str = getToken(l.getAccountid());
                List<String> enlist = new ArrayList<>();
                Map<String, String> paramMap = new HashMap<>();
                //code：抖音券码  （encrypted_data/code必须二选一）
                paramMap.put("code", qm);
                paramMap.put("encrypted_data", encrypteddata);
                JSONObject json = null;
                try {
                    json = (JSONObject) JSONObject.parse(HttpClientUtil.getdy(douyinurl + "/goodlife/v1/fulfilment/certificate/prepare/", paramMap, str));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                JSONObject jsonextra = (JSONObject) json.get("extra");

                System.out.println(jsonextra + "------------验券结果的返回--------------");
                //工作通知需要使用参数
                boolean b = false;
                double je = 0.0;
                if ("0".equals(jsonextra.getString("error_code"))) {
                    JSONObject jsondata = (JSONObject) json.get("data");
                    String errcode = jsondata.getString("error_code");
                    if ("0".equals(errcode)) {
                        JSONArray jsoncerArray = jsondata.getJSONArray("certificates");
                        String orderid = jsondata.getString("order_id");
                        //删掉该订单的所有 已经验券准备成功，但未验券的券号
                        List<FwqDyYq> ll = fwqDyYqService.selectByOrderid(orderid);
                        ll.forEach(i -> {
                            fwqDyYqService.deleteByPrimaryKey(i);
                        });
                        //重新循环添加验券准备
                        for (Object o : jsoncerArray) {
                            JSONObject cerjson = (JSONObject) o;
                            String encryptedcode = cerjson.getString("encrypted_code");
                            FwqDyYq fwqDyYq = new FwqDyYq();
                            fwqDyYq.setCreateTime(new Date());
                            String verifytoken = jsondata.getString("verify_token");
                            fwqDyYq.setVerifytoken(verifytoken);
                            fwqDyYq.setOrderid(orderid);
                            enlist.add(encryptedcode);
                            fwqDyYq.setEncryptedcode(encryptedcode);
                            fwqDyYq.setExpiretime(cerjson.getString("expire_time"));
                            JSONObject skujson = cerjson.getJSONObject("sku");
                            fwqDyYq.setSkutitle(skujson.getString("title"));
                            fwqDyYq.setSkugroupontype(skujson.getString("groupon_type"));
                            JSONObject amountjson = cerjson.getJSONObject("amount");
                            fwqDyYq.setOriginalamount(amountjson.getDouble("original_amount") / 100.00);
                            fwqDyYq.setListmarketamount(amountjson.getDouble("list_market_amount") / 100.00);
                            //pay_amount 用户实付金额  公司要求以券码实际金额为准
                            fwqDyYq.setPayamount(amountjson.getDouble("pay_amount") / 100.00);
                            //coupon_pay_amount 券码实际金额
                            fwqDyYq.setCouponpayamount(amountjson.getDouble("coupon_pay_amount") / 100.00);
                            fwqDyYq.setSfsc("0");
                            fwqDyYq.setZt("0");
                            fwqDyYqService.save(fwqDyYq);
                            String[] arr = enlist.toArray(new String[0]);
                            try {
                                b = verify(verifytoken, poiid, arr, str, tbYddXyd1, finalDingToken, ding, dingkey, ydAppkey1, qm);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                            je = fwqDyYq.getCouponpayamount();
                        }
                    }
                }

                String text = "您提交的抖音验券，'" + l.getBz() + "' 验券" + (b ? "完成" : "失败") + "了！";
                text += "\n\n券码：" + qm;
                if (b) {
                    text += "\n金额：" + je + "(元)";
                    text += "\n代码：抖音";
                }
                text += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
                try {
                    DingDBConfig.sendGztzText(dingkey, tbYddXyd1.getXfdid(), text);
                } catch (ApiException e) {
                    e.printStackTrace();
                }
                if (b) {
                    TbYddXyd tbYddXyd3 = new TbYddXyd();
                    tbYddXyd3.setXid(tbYddXyd.getXid());
                    tbYddXyd3.setXstatu("已交定金，等待店长主动联系您");
                    tbYddXydService.updateById(tbYddXyd3);

                    String post = null;
                    try {
                        Map<String, String> map2 = new HashMap<>();
                        map2.put("ddbh", tbYddXyd1.getXddbh());
                        map2.put("zt", tbYddXyd3.getXstatu());
                        //总价
                        map2.put("zj", tbYddXyd1.getXhfyj() + tbYddXyd1.getXztze() + tbYddXyd1.getXdcze() + tbYddXyd1.getXhpsfy() + tbYddXyd1.getXbxzje() + "");
                        post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/b2e50678-62d2-4d65-a042-27427265a26f", map2);
                    } catch (Exception e) {
                        e.printStackTrace();
                        try {
                            String context1 = "抖音验券成功将状态更新至小程序出错了，订单编号：" + tbYddXyd1.getXddbh() + "，错误原因：" + e;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                    }
                    try {
                        if (post != null && !"".equals(post)) {
                            JSONObject jsonObject1 = JSONObject.parseObject(post);
                            String jg = jsonObject1.getString("jg");
                            if (!"ok".equalsIgnoreCase(jg)) {
                                try {
                                    String context1 = "抖音验券成功将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post;
                                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                } catch (ApiException e1) {
                                    e1.printStackTrace();
                                }
                            }
                        }
                    } catch (Exception e) {
                        try {
                            String context1 = "抖音验券成功将状态更新至小程序出错了，订单编号：" + tbYddXyd1.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                        e.printStackTrace();
                    }

                    JSONObject map1 = new JSONObject();
                    //验券日期
                    map1.put("xyqrq", new Date());
                    //验券人
                    map1.put("xyqr", tbYddXyd2.getXfd());
                    //代码
                    map.put("xdm", "抖音");
                    //券码
                    map.put("xfkm", qm);
                    //验券金额
                    map.put("xysdj", je);


                    GatewayResult gatewayResult2 = new GatewayResult();
                    try {
                        gatewayResult2 = YdConfig.querySpJl(finalToken, ydAppkey, tbYddXyd1.getXslid());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (gatewayResult2.getSuccess()) {
                        JSONArray objects = JSONObject.parseArray(gatewayResult2.getResult());
                        for (Object obj : objects) {
                            JSONObject jsonObject1 = (JSONObject) obj;
                            if ("TODO".equals(jsonObject1.getString("type")) && "验券".equals(jsonObject1.getString("showName"))) {
                                System.out.println("查到任务");
                                long taskId = jsonObject1.getLong("taskId");
                                //准备执行任务
                                GatewayResult gatewayResult1 = null;
                                try {
                                    gatewayResult1 = YdConfig.zxYdSpJdRw(finalToken, tbYddXyd1.getXslid(), ydAppkey, "客户提交抖音验券成功", map1.toJSONString(), taskId, "012412221639786136545");
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                System.out.println(gatewayResult1);
                            }
                        }
                    }
                }
            });
        }

    }

    /**
     * 验券，必须包含参数verify_token(唯一标识)，poi_id(门店id，随便一个门店都可以直接验券)
     *
     * @return
     * @throws IOException
     */
    public boolean verify(String verifytoken, String poiid, String[] encryptedcodes, String token, TbYddXyd tbYddXyd, String dingToken, DingdingEmployee ding, Dingkey dingkey, YdAppkey ydAppkey, String qm) throws IOException {
        boolean b = false;
        if (isEmpty(verifytoken) || isEmpty(poiid)) {
            return b;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("verify_token", verifytoken);
        jsonObject.put("poi_id", poiid);
        jsonObject.put("encrypted_codes", encryptedcodes);
        JSONObject json = (JSONObject) JSONObject.parse(HttpClientUtil.postdy(douyinurl + "/goodlife/v1/fulfilment/certificate/verify/", jsonObject.toString(), token));

        JSONObject jsonextra = (JSONObject) json.get("extra");
        if ("0".equals(jsonextra.getString("error_code"))) {
            JSONObject jsondata = (JSONObject) json.get("data");
            String errcode = jsondata.getString("error_code");
            if ("0".equals(errcode)) {
                JSONArray jsonverArray = jsondata.getJSONArray("verify_results");
                for (Object o : jsonverArray) {
                    JSONObject verjson = (JSONObject) o;
                    String result = verjson.getString("result");
                    if ("0".equals(result)) {
                        FwqDyYq fwqDyYq = fwqDyYqService.selectByOrderidAndZt(verjson.getString("order_id"), verifytoken, verjson.getString("code"));
                        fwqDyYq.setZt("1");
                        fwqDyYqService.update(fwqDyYq);
                        System.out.println("抖音验券完成/客户已履约");

                        //前端传过来的，需要放入定金表单
                        String crmbh = tbYddXyd.getQqid();
                        String khdh = tbYddXyd.getXzkdh();
                        String lsh = tbYddXyd.getXddbh();

                        JSONObject jsonObject2 = new JSONObject();
                        jsonObject2.put("lsh", lsh);
                        jsonObject2.put("je", fwqDyYq.getCouponpayamount());
                        jsonObject2.put("fqr", ding != null ? ding.getUserid() : null);
                        jsonObject2.put("fqrId", ding != null ? ding.getUserid() : null);
                        jsonObject2.put("fqrName", ding != null ? ding.getName() : null);
                        jsonObject2.put("fqsj", new Date());
                        jsonObject2.put("djlx", 2);
                        jsonObject2.put("sspt", 3);
                        jsonObject2.put("mtmd", poiid);
                        jsonObject2.put("hqqm", qm);
                        jsonObject2.put("yqsfwc", 1);
                        jsonObject2.put("yqjg", 1);
                        jsonObject2.put("yqqm", qm);
                        jsonObject2.put("yqsj", new Date());
                        jsonObject2.put("dm", "抖音");
                        jsonObject2.put("qmsj", 1);
                        jsonObject2.put("yqr", ding != null ? ding.getUserid() : null);
                        jsonObject2.put("yqrId", ding != null ? ding.getUserid() : null);
                        jsonObject2.put("yqrName", ding != null ? ding.getName() : null);
                        jsonObject2.put("sfsc", 0);
                        jsonObject2.put("sftk", 0);
                        jsonObject2.put("sfsddj", 0);
                        jsonObject2.put("sfscdj", 0);
                        jsonObject2.put("khdh", khdh);
                        jsonObject2.put("crmbh", crmbh);
                        jsonObject2.put("sfbjdj", 0);
                        jsonObject2.put("sfxd", 0);
                        jsonObject2.put("ytje", 0);
                        jsonObject2.put("sfbjdjwk", 0);
                        jsonObject2.put("sfxyld", 0);
                        //定金表唯一标识
                        jsonObject2.put("djwybs", IdConfig.uuId());
                        GatewayResult gatewayResult = null;
                        for (int j = 0; j < 5; j++) {
                            try {
                                gatewayResult = DingBdLcConfig.xzBdSl(dingToken, ydAppkey, jsonObject2.toJSONString(), ding != null ? ding.getUserid() : "012412221639786136545", "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1");
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                                if (j == 4) {
                                    try {
                                        String context1 = "抖音验券成功同步表单出错了，券码：" + qm + "，错误原因：" + gatewayResult.toString();
                                        DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                    } catch (ApiException e) {
                                        e.printStackTrace();
                                    }
                                }
                            } else {
                                break;
                            }
                        }

                        b = true;
                    }
                }
            }
        }

        return b;
    }

    /**
     * 如果过期就自动更新
     *
     * @param accountid 抖音本地生活商家账号ID
     * @return
     */
    public String getToken(String accountid) {
        List<FwqDyToken> list = fwqDyTokenService.selectByAccountid(accountid);
        boolean flag = true;
        String str = "";
        for (FwqDyToken fwqDyToken : list) {
            int result = fwqDyToken.getSxTime().compareTo(new Date());
            if (result > 0) {
                flag = false;
                str = fwqDyToken.getToken();
            } else {
                fwqDyTokenService.deleteByPrimaryKey(fwqDyToken);
            }
        }
        if (flag) {
            FwqDyAccount fwqDyAccount = fwqDyAccountService.selectByAccountid(accountid).get(0);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("client_key", fwqDyAccount.getClientkey());
            jsonObject.put("client_secret", fwqDyAccount.getClientsecret());
            jsonObject.put("grant_type", "client_credential");
            try {
                JSONObject json = (JSONObject) JSONObject.parse(HttpClientUtil.postdy(douyinurl + "/oauth/client_token/", jsonObject.toString(), null));
                if ("success".equals(json.getString("message"))) {
                    JSONObject data = json.getJSONObject("data");
                    str = data.getString("access_token");
                    FwqDyToken fwqDyToken = new FwqDyToken();
                    fwqDyToken.setCreateTime(new Date());
                    fwqDyToken.setToken(str);
                    fwqDyToken.setSxTime(new Date(System.currentTimeMillis() + 7200 * 1000));
                    fwqDyTokenService.save(fwqDyToken);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return str;
    }
}
