package com.wlgb.controller;

import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.DingQunSend;
import com.wlgb.config.Dingkey;
import com.wlgb.config.Result;
import com.wlgb.service.WeiLianDdXcxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@Slf4j
@RequestMapping(value = "/yibu/huidiao")
public class YiBuHuiDiaoConreoller {
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;

    @RequestMapping(value = "huidiao")
    public Result huidiao() throws ApiException {
        Dingkey dingkey1 = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        DingQunSend.send("异步接口失败：", dingkey1, "chate5bd183f6a0ebdd18e057550c487e5cc");
        return Result.OK();
    }
}
