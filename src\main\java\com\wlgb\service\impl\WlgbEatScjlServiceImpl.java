package com.wlgb.service.impl;

import com.wlgb.entity.WlgbEatScjl;
import com.wlgb.mapper.WlgbEatScjlMapper;
import com.wlgb.service.WlgbEatScjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/13 19:11
 */
@Service
public class WlgbEatScjlServiceImpl implements WlgbEatScjlService {
    @Resource
    private WlgbEatScjlMapper wlgbEatScjlMapper;

    @Override
    public void save(WlgbEatScjl wlgbEatScjl) {
        wlgbEatScjl.setCreateTime(new Date());
        wlgbEatScjlMapper.insertSelective(wlgbEatScjl);
    }

    @Override
    public void clearData(String ddbh) {
        WlgbEatScjl wlgbEatScjl = new WlgbEatScjl();
        wlgbEatScjl.setSfsc(0);
        wlgbEatScjl.setDdbh(ddbh);
        List<WlgbEatScjl> list = wlgbEatScjlMapper.select(wlgbEatScjl);
        list.forEach(l->{
            WlgbEatScjl wlgbEatScjl1 = new WlgbEatScjl();
            wlgbEatScjl1.setSfsc(1);
            wlgbEatScjl1.setId(l.getId());
            wlgbEatScjl1.setUpdateTime(new Date());
            wlgbEatScjlMapper.updateByPrimaryKeySelective(wlgbEatScjl1);
        });
    }
}
