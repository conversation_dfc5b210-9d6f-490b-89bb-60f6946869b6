package com.wlgb.config;



import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Maps;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;

public class DingDingUtil {
    //发送超时时间10s
    private static final int TIME_OUT = 10000;

    /**
     *
     * @param webhook
     * @param secret     安全设置 【方式二，加签 ，创建机器人时选择加签 secret以SE开头】
     * @param content    发送内容
     * @param mobileList 通知具体人的手机号码列表 （可选）
     * @return
     */
    public static String sendMsg(String webhook, String secret, String content, List<String> mobileList, Boolean isAtAll) {
        try {
            //钉钉机器人地址（配置机器人的webhook）
            if (!StringUtils.isEmpty(secret)) {
                Long timestamp = System.currentTimeMillis();
                String sign = getSign(timestamp, secret);
                webhook = new StringBuilder(webhook)
                        .append("&timestamp=")
                        .append(timestamp)
                        .append("&sign=")
                        .append(sign)
                        .toString();
            }
            //是否通知所有人
            //组装请求内容
            String reqStr = buildReqStr(content, isAtAll, mobileList);
            //推送消息（http请求）
            String result = postJson(webhook, reqStr);
            return result;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 组装请求报文
     * 发送消息类型 text
     *
     * @param content
     * @return
     */
    private static String buildReqStr(String content, boolean isAtAll, List<String> mobileList) {
        //消息内容
        Map<String, String> contentMap = Maps.newHashMap();
        contentMap.put("content", content);
        //通知人
        Map<String, Object> atMap = Maps.newHashMap();
        //1.是否通知所有人
        atMap.put("isAtAll", isAtAll);
        //2.通知具体人的手机号码列表
        atMap.put("atMobiles", mobileList);
        Map<String, Object> reqMap = Maps.newHashMap();
        reqMap.put("msgtype", "text");
        reqMap.put("text", contentMap);
        reqMap.put("at", atMap);
        return JSON.toJSONString(reqMap);
    }

    /**
     * 发送消息类型 markdown
     *
     */
    public static String sendMark(String webhook, String secret, String title,String text, List<String> mobileList, Boolean isAtAll) {
        try {
            //钉钉机器人地址（配置机器人的webhook）
            if (!StringUtils.isEmpty(secret)) {
                Long timestamp = System.currentTimeMillis();
                String sign = getSign(timestamp, secret);
                webhook = new StringBuilder(webhook)
                        .append("&timestamp=")
                        .append(timestamp)
                        .append("&sign=")
                        .append(sign)
                        .toString();
            }
            //是否通知所有人

            //组装请求内容
            String reqStr = markdownReqStr(title,text ,isAtAll, mobileList);
            //推送消息（http请求）
            String result = postJson(webhook, reqStr);
            return result;
        } catch (Exception e) {
            return null;
        }
    }
    /**
     * 组装请求报文
     * 发送消息类型 markdown
     *
     * @return
     */
    private static String markdownReqStr(String title,String text, boolean isAtAll, List<String> mobileList) {
        //消息内容
        Map<String, String> contentMap = Maps.newHashMap();
        contentMap.put("title", title);
        contentMap.put("text",text);
        //通知人
        Map<String, Object> atMap = Maps.newHashMap();
        //1.是否通知所有人
        atMap.put("isAtAll", isAtAll);
        //2.通知具体人的手机号码列表
        atMap.put("atMobiles", mobileList);
        Map<String, Object> reqMap = Maps.newHashMap();
        reqMap.put("msgtype", "markdown");
        reqMap.put("markdown", contentMap);
        reqMap.put("at", atMap);
        return JSON.toJSONString(reqMap);
    }

    /**
     * 发送消息类型 markdown
     *
     */
    public static String sendLink(String webhook, String secret, String title,String text,String picUrl,String messageUrl ) {
        try {
            //钉钉机器人地址（配置机器人的webhook）
            if (!StringUtils.isEmpty(secret)) {
                Long timestamp = System.currentTimeMillis();
                String sign = getSign(timestamp, secret);
                webhook = new StringBuilder(webhook)
                        .append("&timestamp=")
                        .append(timestamp)
                        .append("&sign=")
                        .append(sign)
                        .toString();
            }
//            System.out.println("webhook:" + webhook);
            //是否通知所有人
           /* boolean isAtAll = false;*/
            //组装请求内容
            String reqStr = linkReqStr(title,text ,picUrl, messageUrl);
            //推送消息（http请求）
            String result = postJson(webhook, reqStr);
//            log.info("推送结果result == " + result);
            return result;
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 发送消息类型 link
     *
     */
    private static String linkReqStr(String title,String text,String picUrl,String messageUrl) {
        //消息内容
        Map<String, String> contentMap = Maps.newHashMap();
        contentMap.put("title", title);
        contentMap.put("text",text);
        contentMap.put("picUrl",picUrl);
        contentMap.put("messageUrl",messageUrl);
        Map<String, Object> reqMap = Maps.newHashMap();
        reqMap.put("msgtype", "link");
        reqMap.put("link", contentMap);
        return JSON.toJSONString(reqMap);
    }


    /**
     * 发送消息类型 整体跳转ActionCard类型
     *
     */
    public static String sendActionCard(String webhook, String secret, String title,String text,String singleTitle,String singleURL,String btnOrientation) {
        try {
            //钉钉机器人地址（配置机器人的webhook）
            if (!StringUtils.isEmpty(secret)) {
                Long timestamp = System.currentTimeMillis();
                String sign = getSign(timestamp, secret);
                webhook = new StringBuilder(webhook)
                        .append("&timestamp=")
                        .append(timestamp)
                        .append("&sign=")
                        .append(sign)
                        .toString();
            }
            System.out.println("webhook:" + webhook);
            //是否通知所有人
            /* boolean isAtAll = false;*/
            //组装请求内容
            String reqStr = actionCardReqStr(title, text, singleTitle, singleURL,btnOrientation);
            //推送消息（http请求）
            String result = postJson(webhook, reqStr);
            return result;
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 发送消息类型 整体跳转ActionCard类型
     *
     */
    private static String actionCardReqStr(String title,String text,String singleTitle,String singleURL,String btnOrientation) {
        //消息内容
        Map<String, String> contentMap = Maps.newHashMap();
        contentMap.put("title", title);
        contentMap.put("text",text);
        contentMap.put("singleTitle",singleTitle);
        contentMap.put("singleURL",singleURL);
        contentMap.put("btnOrientation",btnOrientation);
        Map<String, Object> reqMap = Maps.newHashMap();
        reqMap.put("msgtype", "actionCard");
        reqMap.put("actionCard", contentMap);
        return JSON.toJSONString(reqMap);
    }




    private static String postJson(String url, String reqStr) {
        String body = null;
        try {
            body = HttpRequest.post(url).body(reqStr).timeout(TIME_OUT).execute().body();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return body;
    }

    /**
     * 自定义机器人获取签名
     * 创建机器人时选择加签获取secret以SE开头
     *
     * @param timestamp
     * @return
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     * @throws InvalidKeyException
     */
    private static String getSign(Long timestamp, String secret) throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
        return sign;
    }

}
