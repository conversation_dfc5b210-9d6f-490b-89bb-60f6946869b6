package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdLcxmcgdfksqjl;
import com.wlgb.mapper.WlgbJdLcxmcgdfksqjlMapper;
import com.wlgb.service.WlgbJdLcxmcgdfksqjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/20 0:05
 */
@Service
public class WlgbJdLcxmcgdfksqjlServiceImpl implements WlgbJdLcxmcgdfksqjlService {
    @Resource
    private WlgbJdLcxmcgdfksqjlMapper wlgbJdLcxmcgdfksqjlMapper;

    @Override
    public void save(WlgbJdLcxmcgdfksqjl wlgbJdLcxmcgdfksqjl) {
        wlgbJdLcxmcgdfksqjl.setCreateTime(new Date());
        wlgbJdLcxmcgdfksqjl.setId(IdConfig.uuId());
        wlgbJdLcxmcgdfksqjlMapper.insertSelective(wlgbJdLcxmcgdfksqjl);
    }

    @Override
    public void updateById(WlgbJdLcxmcgdfksqjl wlgbJdLcxmcgdfksqjl) {
        wlgbJdLcxmcgdfksqjl.setUpdateTime(new Date());
        wlgbJdLcxmcgdfksqjlMapper.updateByPrimaryKeySelective(wlgbJdLcxmcgdfksqjl);
    }

    @Override
    public WlgbJdLcxmcgdfksqjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd) {
        Example example = new Example(WlgbJdLcxmcgdfksqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdLcxmcgdfksqjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdLcxmcgdfksqjl> queryListByWlgbJdLcxmcgdfksqjl(WlgbJdLcxmcgdfksqjl wlgbJdLcxmcgdfksqjl) {
        return wlgbJdLcxmcgdfksqjlMapper.select(wlgbJdLcxmcgdfksqjl);
    }
}
