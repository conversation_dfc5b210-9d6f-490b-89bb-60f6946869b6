<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlgb.mapper1.WeiLianDaiBanMapper">
    <!-- 直营店长 -->
    <select id="queryCsRyByUserId" resultType="com.wlgb.entity.Csry" parameterType="java.lang.String">
        select
            id, userid, name, mobile, bm, bmid
        from
            csry
        where
            userid = #{userid}
    </select>
    <!-- 加盟店长 -->
    <select id="queryCsRyJmByUserId" resultType="com.wlgb.entity.Csry" parameterType="java.lang.String">
        select
            id, userid, name, mobile, bm, bmid
        from
            csry_jm
        where
            userid = #{userid}
    </select>
    <!-- 查询是否发送回访 -->
    <select id="queryNotFsHf" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT
        count( 1 )
        FROM
        weiliandaiban.oapiworkrecord
        <where>
            and id IN (
            SELECT DISTINCT
            tid
            FROM
            weiliandaiban.formitem
            WHERE
            title = '订单编号'
            AND content = ( SELECT xddbh FROM weilian.tb_xyd WHERE xid = #{xid} )
            )
            AND send_statu = 1
        </where>
    </select>
    <!-- 清空待办列表 -->
    <select id="qkDbLb">
        TRUNCATE TABLE `wlgb_dbwcw`
    </select>


    <!-- 查询发送回访列表铁军 -->
    <select id="queryNotKfList" resultType="com.wlgb.entity.vo.DdhfUtil" parameterType="java.util.Map">
        select
        xddbh, xfd, xzk, time, url, ewm, ewmmz, khsfwc,xzkdh
        from
        sj_hfjl
        <where>
            <if test="search != null and search != ''">
                and
                (xzk like CONCAT('%',#{search},'%') or xfd like CONCAT('%',#{search},'%'))
            </if>
            <if test="isKhsfwc != null and isKhsfwc != ''">
                and
                (khsfwc = #{isKhsfwc})
            </if>
            and xfd not in (select xm from weilian.fwq_bbb2 where bmzx = 1)
            and date_format(time, '%Y-%m-%d') > date_format('2021-07-20', '%Y-%m-%d')
        </where>
        ORDER BY time DESC
        limit #{help.pageNum}, #{help.pageSize}
    </select>

    <!-- 查询发送回访列表铁军 -->
    <select id="queryNotKfListCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
        count(1)
        from
        sj_hfjl
        <where>
            <if test="search != null and search != ''">
                and
                (xzk like CONCAT('%',#{search},'%') or xfd like CONCAT('%',#{search},'%'))
            </if>
            <if test="isKhsfwc != null and isKhsfwc != ''">
                and
                (khsfwc = #{isKhsfwc})
            </if>
            and xfd not in (select xm from weilian.fwq_bbb2 where bmzx = 1)
            and date_format(time, '%Y-%m-%d') > date_format('2021-07-20', '%Y-%m-%d')
        </where>
    </select>

    <!-- 查询发送回访列表营销部 -->
    <select id="queryNotKfListYwjl" resultType="java.util.Map" parameterType="java.util.Map">
        select
        *
        from
        sj_hfjl
        <where>
            <if test="search != null and search != ''">
                and
                (xzk like CONCAT('%',#{search},'%') or xfd like CONCAT('%',#{search},'%'))
            </if>
            <if test="name != null and name != ''">
                and xfd = #{name}
            </if>
            <if test="list != null and list.size() > 0">
                and city in
                <foreach item="item" index="index" collection="list"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and khsfwc = '否'
            and xfd in (select xm from weilian.fwq_bbb2 where bmzx = 1)
            and date_format(time, '%Y-%m-%d') > date_format('2021-07-20', '%Y-%m-%d')
        </where>
        ORDER BY time DESC
        limit #{help.pageNum}, #{help.pageSize}
    </select>

    <!-- 查询发送回访列表营销部 -->
    <select id="queryNotKfListCountYwjl" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
        count(1)
        from
        sj_hfjl
        <where>
            <if test="search != null and search != ''">
                and
                (xzk like CONCAT('%',#{search},'%') or xfd like CONCAT('%',#{search},'%'))
            </if>
            <if test="name != null and name != ''">
                and xfd = #{name}
            </if>
            <if test="list != null and list.size() > 0">
                and city in
                <foreach item="item" index="index" collection="list"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and khsfwc = '否'
            and xfd in (select xm from weilian.fwq_bbb2 where bmzx = 1)
            and date_format(time, '%Y-%m-%d') > date_format('2021-07-20', '%Y-%m-%d')
        </where>
    </select>

    <!-- 修改回访状态 -->
    <update id="updateHfZt" parameterType="java.lang.String">
        update weiliandaiban.sj_hfjl set khsfwc = '是' where xid = #{xid}
    </update>

    <!-- 修改回访执行人 -->
    <update id="zjZxr" parameterType="java.util.Map">
        update sj_hfjl set xfd = #{jsr} where xddbh = #{ddbh}
    </update>
</mapper>