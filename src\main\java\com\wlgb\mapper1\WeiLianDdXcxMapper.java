package com.wlgb.mapper1;

import com.alibaba.fastjson.JSONObject;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/12/11 21:11
 * @Version 1.0
 */
@Mapper
public interface WeiLianDdXcxMapper {
    @Select("select * from wlgb_hxyh_dzjl where out_trade_no not in (select skbh from wlgb_jd_djbbd) and DATE_FORMAT(create_time,'%Y-%m-%d') > DATE_FORMAT('2022-12-01','%Y-%m-%d')")
    List<WlgbHxyhDzjl> queryDzWtb();

    @Select("select * from wlgb_jd_djbbd where sfsc = 0 and sfxd = 0")
    List<Map<String, Object>> queryXdWxgZt();

    @Select("select * from weilian.tb_xyd where xddbh = #{xddbh}")
    TbXyd queryXydByDdBh(@Param("xddbh") String xddbh);

    @Select("select count(1) from weilian.tb_qrd where qxydid = #{qxydid}")
    Integer queryQrdSfCz(@Param("qxydid") String qxydid);

    @Select("select qgsr from weilian.tb_qrd where qxydid = #{qxydid}")
    Double queryQrdGsSr(@Param("qxydid") String qxydid);

    @Select("SELECT * FROM `wlgb_mt_log` where DATE_FORMAT(yqsj,'%Y-%m-%d') >= DATE_FORMAT('2023-01-01','%Y-%m-%d') " +
            "and qh not in (select yqqm from wlgb_jd_djbbd where djlx = 2) ORDER BY yqsj desc")
    List<WlgbMtLog> queryXsDjWlr();

    @Select("select ddbh from wlgb_mt_jl where mtqh = #{qm} and yqjg = '验券成功'")
    String queryYqJl(@Param("qm") String qm);

    @Select("SELECT open_shop_uuid,zh FROM `wlgb_mt_md` where shopname = #{yqmd} and shopaddress = #{yqmddz} and sfsc" +
            " = 0")
    JSONObject queryMtMd(WlgbMtLog wlgbMtLog);

    @Select("select * from wlgb_hxyh_dzjl where out_trade_no like CONCAT('%',#{skbh},'%') and out_trade_no not in " +
            "(select skbh " +
            "from " +
            "wlgb_jd_djbbd) and " +
            "DATE_FORMAT(create_time,'%Y-%m-%d') > DATE_FORMAT('2022-12-01','%Y-%m-%d')")
    WlgbHxyhDzjl queryXxDjBySkBh(@Param("skbh") String skbh);

    @Select("SELECT * FROM `wlgb_mt_log` where DATE_FORMAT(yqsj,'%Y-%m-%d') >= DATE_FORMAT('2023-01-01','%Y-%m-%d') " +
            "and qh = #{yqqm} and qh not in (select yqqm from wlgb_jd_djbbd where djlx = 2 and yqqm = #{yqqm}) ORDER " +
            "BY yqsj desc")
    WlgbMtLog queryXsDjByQh(@Param("yqqm") String yqqm);

    @Select("select * from wlgb_jd_djbbd where lsh = #{lsh} and sfxd = 0")
    List<WlgbJdDjbbd> queryDjYxdWxgByLsh(@Param("lsh") String lsh);

    @Update("update weilian.crm_qbkh set qxxzt = 2 where crmbh = #{crmbh}")
    void updateCrmXxZt(@Param("crmbh") String crmbh);

    @Select("SELECT *  FROM weilian.tb_xyd  WHERE xsfsc = 0 AND xkhwczt NOT IN ( 2, 3 ) AND date_format( date_sub( " +
            "date_format( xjctime, '%Y%m%d' ), INTERVAL 1 DAY ), '%Y%m%d' ) = date_format(curdate(),'%Y%m%d')")
    List<TbXyd> queryXyEcGd();

    Integer queryBsCount(@Param("vid") String vid);

    @Select("select * from weiliandaiban.csry where userid = #{userid}")
    Csry queryCsRyByUserId(@Param("userid") String userId);

    @Select("select * from weiliandaiban.csry_jm where userid = #{userid}")
    Csry queryCsRyJmByUserId(@Param("userid") String userId);

    @Select("select count(1) from wlgb_jdb_one where xid = #{xid}")
    Integer queryJdbOneCountByXid(@Param("xid") String xid);

    @Select("select * from weilian.tb_villa where vid = #{vid}")
    TbVilla queryTbVillaById(@Param("vid") String vid);

    @Select("select * from weilian.tb_villa where vid = #{vidorvname} or vname = #{vidorvname} ")
    TbVilla queryTbVillaByIdOrVname(@Param("vidorvname") String vidorvname);

    @Select("select id,appkey,appsecret,agent_id as agentId,name from dingkey where id = #{id}")
    Dingkey queryDingKeyById(@Param("id") String id);

    @Select("select * from weilian.dingding_employee where userid = #{userid}")
    DingdingEmployee queryDingdingEmployeeByUserId(@Param("userid") String userId);

    @Select("select * from weilian.dingding_employee where name = #{name}")
    DingdingEmployee queryDingdingEmployeeByUserName(@Param("name") String name);


    @Select("select * from weilian.dingding_employee where mobile = #{mobile}")
    DingdingEmployee queryDingdingEmployeeBySJH(@Param("mobile") String mobile);


    @Select("select * from weiliandaiban.dzxyd where xid = #{xid}")
    Dzxyd queryDzXydByXid(@Param("xid") String xid);

    @Select("select * from yd_appkey where bz = #{bz}")
    YdAppkey queryYdAppKeyByBz(@Param("bz") String bz);

    @Select("select * from yd_bd where bz = #{bz}")
    YdBd queryYdBdByBz(@Param("bz") String bz);

    @Select("select count(1) from wlgb_jdb_two where xid = #{xid}")
    Integer queryJdbTwoCountByXid(@Param("xid") String xid);

    @Select("SELECT tx.*,tv.vname,tv.VNAME,tv.PID FROM weilian.tb_xyd tx LEFT JOIN weilian.tb_villa tv  ON tx.XBSMC=tv.vid   WHERE tx.xsfsc = 0 AND tx.xbsmc IN ( SELECT vid FROM weilian.tb_villa WHERE vsfsc = 0 AND" +
            " pid IN ( SELECT userid FROM weiliandaiban.csry )) AND tx.XJCTIME BETWEEN DATE_FORMAT(#{jc1},'%Y-%m-%d %H')" +
            " and  DATE_FORMAT(#{jc2},'%Y-%m-%d %H') and tx.xid not in (select ddid from wlgb_dksljl where bz = '进场及消费')")
    List<TbXyd> queryNotFsJcLc(Map<String, Object> map);

    @Select("SELECT * FROM weilian.tb_xyd WHERE xsfsc = 0 AND XJCTIME BETWEEN DATE_FORMAT(#{jc1}," +
            "'%Y-%m-%d %H') and  DATE_FORMAT(#{jc2},'%Y-%m-%d %H') and xid not in (select xid from wlgb_bdjl where bz = '对账表单提交')")
    List<TbXyd> queryNotFsDzBd(Map<String, Object> map);

    @Select("select count(1) from wlgb_notecgd where sfsc = 0 and xid = #{xid}")
    Integer queryNotEcGdCountByXid(@Param("xid") String xid);

    @Select("select ifnull(sum(ifnull(jjje,0)), 0) from weilian.tb_kdj where vid = #{vid} " +
            "and ((DATE_FORMAT(xjctime,'%Y-%m-%d %H:%i:%s') BETWEEN DATE_FORMAT(#{jcsj},'%Y-%m-%d %H:%i:%s') " +
            "and DATE_FORMAT(#{tcsj},'%Y-%m-%d %H:%i:%s')) or (DATE_FORMAT(xtctime,'%Y-%m-%d %H:%i:%s') BETWEEN " +
            "DATE_FORMAT(#{jcsj},'%Y-%m-%d %H:%i:%s') and DATE_FORMAT(#{tcsj},'%Y-%m-%d %H:%i:%s')))")
    Double queryKdjJzj(Map<String, Object> map);

    @Select("SELECT * FROM weilian.tb_villa WHERE vsfsc ='0' AND pid IN ( SELECT userid FROM weiliandaiban.csry ) " +
            "AND vid NOT IN (SELECT vid FROM `weilian-ddxcx`.wlgb_not_villa WHERE sfsc = 0 " +
            "AND ( curdate() BETWEEN starTime AND endTime) AND wcdb = 0)")
    List<TbVilla> queryEhDbBsList();

    @Select("select * from weilian.wlgb_hr_qun where sfsc = 0")
    List<WlgbHrQun> queryHrQunList();

    List<HrLsZy> queryHrLsZy(Map<String, Object> map);

    List<HrLsZy> queryHrLsJl(Map<String, Object> map);


    List<WlgbJdDjbbdVo> QueryMtXsList(Map<String, Object> map);

    Integer CoutMtXsList(Map<String, Object> map);

    List<WlgbJdDjbbd> queryDjbDataByFormIdList(Map<String, Object> map);

    List<WlgbJdDjLsjlb> queryDzWhkList(Map<String, Object> map);

    Integer countDzWhkList(Map<String, Object> map);

    List<WlgbJdDjbbd> queryXsDj(Map<String, Object> map);

    Integer queryXsDjCount(Map<String, Object> map);


    @Select("SELECT  SUM(IFNULL(XDYJ, 0)) as xdyj  FROM fwq_thb_d1c_jt_copy1 WHERE city=#{city} AND vxz='直营' " +
            "and FD IN (SELECT xm FROM weilian.fwq_bbb2 WHERE bmzx=1 and bmmc=#{bmmc})" +
            "and ( date_format(xdsj, '%Y-%m-%d %H:%i:%s') between date_format(#{kssj}, '%Y-%m-%d %H:%i:%s')   and   date_format(#{jssj}, '%Y-%m-%d %H:%i:%s') )")
    List<Fwqthbd1cjtcopy1> queryYj(Map<String, Object> map);

    WlgbJmsbb queryJmsBbByVid(@Param("vid") String vid);

    Integer queryNotQx(@Param("userid") String userId);

    Integer querySfSqKdj(Map<String, Object> map);

    void zxKdjHfGc(Map<String, Object> map);

    Integer queryDdCz(@Param("xid") String xid);

    Integer queryByXidDzJlCount(@Param("xid") String xid);

    void insertDzHfJl(WlgbQrdDzjl wlgbQrdDzjl);

    List<WlgbQrdDzjl> queryFsHf();

    void updateXgZt(WlgbQrdDzjl wlgbQrdDzjl);

    List<WlgbNotVilla> queryGqGl();

    List<DkBbJqr> queryLcBbJqr();

    List<String> queryJqrBbAt(@Param("city") String city);

    void zxCdf();

    void zxCdf2(@Param("xddbh") String xddbh);

    List<JSONObject> queryCdfList();

    List<Map<String, Object>> queryProductByTypeList(Map<String, Object> map);

    Integer queryProductByTypeCount(Map<String, Object> map);

    Integer querySpLbCount(@Param("processcode") String processCode);


    List<WlgbHxyhDzjl> queryBdj();

    JSONObject queryCnQyByBmId(@Param("bmid") String bmid);

    void zxPyqBbSj();

    List<Map<String, Object>> queryBbSjRy();

    List<Map<String, Object>> queryBbSjBm();

    String queryBsKySjByBsMc(@Param("bsmc") String bsmc);

    List<WlgbJdDjbbd> queryDgZzWscJdList();

    List<JSONObject> queryYhxQm();

    void qkJdFyKmTable();

    List<Map<String, Object>> queryHrGzRzFsr();

    void zxKdjSj(@Param("start") String start, @Param("end") String end);

    void zxKdjSjMs(@Param("start") String start, @Param("end") String end);

    List<Map<String, Object>> queryKdjZsZdj();

    List<Map<String, Object>> queryBsHzbZdj();

    List<Map<String, Object>> queryRqZzbZdj();

    WlgbJdDjbbd queryDjJlByLsh(@Param("lsh") String lsh);

    List<com.wlgb.config.Select> queryFyKmSelect();

    void insertCpJl(Map<String, Object> map);

    List<DdhfUtil> queryYwJlHfJl(Map<String, Object> map);

    Integer queryYwJlHfJlCount(Map<String, Object> map);

    Integer queryYxbTzLb(@Param("lbid") String lbid);

    List<String> queryYxBmHrTz();

    String queryZpbRy();

    List<Map<String, Object>> queryDtSj();

    List<Map<String, Object>> queryDySj();

    List<Map<String, Object>> queryDzSj();

    List<Map<String, Object>> querySzSj();

    List<Map<String, Object>> queryDyySj();

    List<Map<String, Object>> queryOldyySj();

    List<Map<String, Object>> queryDnnSj();

    List<Map<String, Object>> querySnnSj();

    List<Map<String, Object>> queryYgwyx();

    List<Map<String, Object>> queryNgwyx();

    List<Map<String, Object>> queryBmmb();

    List<Map<String, Object>> queryGrmb();

    List<Map<String, Object>> queryYbmmb();

    List<Map<String, Object>> querySgrmb();

    void insertSc(WlgbNotVilla wlgbNotVilla);

    void deleteSc(@Param("vid") String vid);

    Integer queryByVidCount(@Param("vid") String vid);

    void updateSc(WlgbNotVilla wlgbNotVilla);

    void zxXCdf(@Param("bsxz") String bsxz, @Param("xzkssj") String xzkssj, @Param("xzjssj") String xzjssj);

    List<Map<String, Object>> queryDzyXCdf();

    List<WlgbOrderCyjl> queryCyJlByz();

    List<String> queryCyJlDzByz();

    String queryProcessTypeByProcessCode(@Param("processCode") String processCode);

    List<YjDzVo> queryYjWkZfJlByDdBh(@Param("ddbh") String ddbh);

    List<SjCdfNewJl> querySjCdfNewJl(Map<String, Object> map);

    Integer CoutSjCdfNewJlList(Map<String, Object> map);

    void insertSjCdfNewJl(SjCdfNewJl sjCdfNewJl);

    List<JSONObject> querySpList(Map<String, Object> map);

    Integer querySpListCount(Map<String, Object> map);


}
