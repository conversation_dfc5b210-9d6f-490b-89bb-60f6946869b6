package com.wlgb.tool;


import com.aliyun.tea.TeaException;

public class GenerateUserAccessToken {

    private static String AccessKeyId="";
    private static String AccessKeySecret="";
    /**
     * 使用AK&SK初始化账号Client
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.chatbot20220408.Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "chatbot.cn-shanghai.aliyuncs.com";
        return new com.aliyun.chatbot20220408.Client(config);
    }

    public static void setToken() throws Exception {
        // 工程代码泄露可能会导致AccessKey泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html
        com.aliyun.chatbot20220408.Client client = GenerateUserAccessToken.createClient(AccessKeyId, AccessKeySecret);
        com.aliyun.chatbot20220408.models.GenerateUserAccessTokenRequest generateUserAccessTokenRequest = new com.aliyun.chatbot20220408.models.GenerateUserAccessTokenRequest()
                .setNick("疯癫")
                .setForeignId("123456")
                .setExpireTime(3600);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions()
                .setReadTimeout(36000)
                .setConnectTimeout(36000)
                .setAutoretry(false);
        try {
            // 复制代码运行请自行打印 API 的返回值
            client.generateUserAccessTokenWithOptions(generateUserAccessTokenRequest, runtime);
        } catch (TeaException error) {
            // 如有需要，请打印 error
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 如有需要，请打印 error
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
    }

}
