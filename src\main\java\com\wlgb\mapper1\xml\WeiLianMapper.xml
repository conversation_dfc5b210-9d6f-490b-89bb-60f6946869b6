<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlgb.mapper1.WeiLianMapper">
    <!-- 查询协议单是否撞单 -->
    <select id="queryCountXydSfZd" resultType="java.lang.Integer" parameterType="com.wlgb.entity.TbXyd">
        SELECT count(1) FROM tb_xyd
        WHERE
        xsfsc = 0
        and XBSMC = #{xbsmc}
        and xkhwczt != 2
        <if test="xid != null and xid != ''">
            and xid != #{xid}
        </if>
        and
        NOT (
        (date_format(XTCTIME,'%Y-%m-%d %H:%i') &lt; date_format(#{xjctime},'%Y-%m-%d %H:%i')
        OR ( date_format(XJCTIME,'%Y-%m-%d %H:%i') &gt; date_format(#{xtctime},'%Y-%m-%d %H:%i'))
        )
        )
    </select>

    <select id="queryCountXydSfZd2" resultType="java.lang.Integer" parameterType="com.wlgb.entity.TbXyd">
        SELECT count(1) FROM tb_xyd
        WHERE
        xsfsc = 0
        and XBSMC = #{xbsmc}
        and xkhwczt != 2
        <if test="xddbh != null and xddbh != ''">
            and xddbh != #{xddbh}
        </if>
        and
        NOT (
        (date_format(XTCTIME,'%Y-%m-%d %H:%i') &lt; date_format(#{xjctime},'%Y-%m-%d %H:%i')
        OR ( date_format(XJCTIME,'%Y-%m-%d %H:%i') &gt; date_format(#{xtctime},'%Y-%m-%d %H:%i'))
        )
        )
    </select>
    <!-- 查询预留单是否撞单 -->
    <select id="queryCountWlgbYlxdSfZd" resultType="java.lang.Integer" parameterType="com.wlgb.entity.WlgbYlxd">
        SELECT count(1) FROM wlgb_ylxd
        WHERE
        sfsc = 0
        and xbsmc = #{xbsmc}
        <if test="xid != null and xid != ''">
            and xid != #{xid}
        </if>
        and
        NOT (
        (date_format(XTCTIME,'%Y-%m-%d %H:%i') &lt; date_format(#{xjctime},'%Y-%m-%d %H:%i')
        OR ( date_format(XJCTIME,'%Y-%m-%d %H:%i') &gt; date_format(#{xtctime},'%Y-%m-%d %H:%i'))
        )
        )
    </select>
    <!-- 查询预定单是否撞单 -->
    <select id="queryCountTbYddXydSfZd" resultType="java.lang.Integer" parameterType="com.wlgb.entity.TbYddXyd">
        SELECT count(1) FROM tb_ydd_xyd
        WHERE
        xsfsc = 0
        and xbsmc = #{xbsmc}
        <if test="xid != null and xid != ''">
            and xid != #{xid}
        </if>
        and
        NOT (
        (date_format(XTCTIME,'%Y-%m-%d %H:%i') &lt; date_format(#{xjctime},'%Y-%m-%d %H:%i')
        OR ( date_format(XJCTIME,'%Y-%m-%d %H:%i') &gt; date_format(#{xtctime},'%Y-%m-%d %H:%i'))
        )
        )
    </select>
    <!-- 根据时间获取场次 -->
    <select id="queryPdCc" resultType="java.lang.Integer" parameterType="java.util.Map">
        CALL XYD_PDCC(#{vid}, #{jcsj}, #{tcsj})
    </select>
    <!-- 查询客单价价格 -->
    <select id="queryKdjZe" parameterType="java.util.Map" resultType="java.lang.Double">
        select
            TRUNCATE(ifnull(sum(ifnull(je,0)), 0),2)
        from
            tb_kdj
        where
            vid = #{vid}
        and
        (
            (DATE_FORMAT(xjctime,'%Y-%m-%d %H:%i:%s') BETWEEN DATE_FORMAT(#{jcsj},'%Y-%m-%d %H:%i:%s') and DATE_FORMAT(#{tcsj},'%Y-%m-%d %H:%i:%s'))
        or
            (DATE_FORMAT(xtctime,'%Y-%m-%d %H:%i:%s') BETWEEN DATE_FORMAT(#{jcsj},'%Y-%m-%d %H:%i:%s') and DATE_FORMAT(#{tcsj},'%Y-%m-%d %H:%i:%s'))
        )
    </select>
    <!-- 根据别墅id查询全部上级的id -->
    <select id="queryIdsByVid" resultType="com.wlgb.config.VillaIDsUtil" parameterType="java.lang.String">
        select
            v.vid, f.fid, c.cid, z.sid
        from
            tb_villa v
        left join
            tb_csfq f
        on
            v.fid = f.fid
        left join
            tb_csfgs c
        on
            f.cid = c.cid
        left join
            tb_zgsf z
        on
            c.sid = z.sid
        where
            v.vid = #{vid}
        and
            v.vsfsc = 0
    </select>
    <!-- 查询客户来源和性质 -->
    <select id="queryKhLyXzByLidOrLname" resultType="com.wlgb.entity.TbKhlyxz" parameterType="java.lang.String">
        select * from tb_khlyxz where lid = #{khxz} or lname = #{khxz}
    </select>
    <!-- 根据房东名字部门 -->
    <select id="queryBbb2ByXm" resultType="com.wlgb.entity.vo.FwqBbb2" parameterType="java.lang.String">
        select
            *
        from
            fwq_bbb2
        where
            xm = #{xm}
        limit 1
    </select>
    <!-- 根据房东姓名查昵称 -->
    <select id="queryBbbByXfd" resultType="com.wlgb.entity.vo.FwqBbb" parameterType="java.lang.String">
        select * from fwq_bbb where xfd=#{xfd} limit 1
    </select>
    <!-- 执行修改协议单场次 -->
    <select id="zxZjCc" parameterType="java.lang.String">
        CALL XYD_ZJCC(#{xid})
    </select>
    <!-- 查询客单价专员id -->
    <select id="queryKdjZy" resultType="java.lang.String">
        select
            GROUP_CONCAT(userid)
        from
            tb_kdj_zy
        where
            sfsc = '0'
        limit 1
    </select>
    <!-- 查询客单价信息 -->
    <select id="queryKdjXx" parameterType="java.util.Map" resultType="java.util.Map">
        select
            IFNULL(sum(IFNULL(je,0)), 0) as dj,IFNULL(sum(IFNULL(jjje, 0)), 0) as jzx, IFNULL(sum(IFNULL(bjje,0)), 0) as bj,IFNULL(sum(IFNULL(dycje,0)),0) as dycje,IFNULL(SUM(IFNULL(dycjjje,0)),0) as dycjjje,IFNULL(SUM(IFNULL(dycbjje,0)),0) as dycbjje
        from
            tb_kdj
        where
            vid = #{vid}
        and
            ((DATE_FORMAT(xjctime,'%Y-%m-%d %H:%i:%s') BETWEEN DATE_FORMAT(#{jcsj},'%Y-%m-%d %H:%i:%s') and DATE_FORMAT(#{tcsj},'%Y-%m-%d %H:%i:%s')) or (DATE_FORMAT(xtctime,'%Y-%m-%d %H:%i:%s') BETWEEN DATE_FORMAT(#{jcsj},'%Y-%m-%d %H:%i:%s') and DATE_FORMAT(#{tcsj},'%Y-%m-%d %H:%i:%s')))
    </select>

    <!--查询房东当前月的下单数量-->
    <select id="queryXydMothCountByXfd" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        tb_xyd
        <where>
            and
            xfd=#{xfd}
            and
            date_format(xkhtjtime,'%Y-%m') = date_format(now(),'%Y-%m')
            and xsfsc = 0
            and xkhwczt not in (2, 3)
        </where>
    </select>

    <!--查询房东当日的下单数量-->
    <select id="queryXydDateCountByXfd" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        tb_xyd
        <where>
            and
            xfd=#{xfd}
            and
            date_format(xkhtjtime,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
            and xsfsc = 0
            and xkhwczt not in (2, 3)
        </where>
    </select>
    <!--查询房东昨天的下单数量-->
    <select id="queryXydYesCountByXfd" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        tb_xyd
        <where>
            and
            xfd=#{xfd}
            and
            DATEDIFF(now(),xkhtjtime) = 1
            and xsfsc = 0
            and xkhwczt not in (2, 3)
        </where>
    </select>
    <!-- 根据房东查询当前月业绩 -->
    <select id="queryXdYjByXfd" resultType="java.lang.Double" parameterType="java.lang.String">
        select
            truncate(sum(if(xhfyj is not null and xhfyj != '', xhfyj, 0) +
                         if(XDCZE is not null and XDCZE != '', XDCZE, 0) +
                         if(XSKZE is not null and XSKZE != '', XSKZE, 0) +
                         if(XZTZE is not null and XZTZE != '', XZTZE, 0) +
                         if(XBXZJE is not null and XBXZJE != '', XBXZJE, 0) +
                         if(XHPSFY is not null and XHPSFY != '', XHPSFY, 0) +
                         if(XZRCSFY is not null and XZRCSFY != '', XZRCSFY, 0)+
                         if(xsplsze is not null and xsplsze != '', xsplsze, 0)
                     ), 2) as cdf
        from
            tb_xyd
        where
            xfd = #{name}
        and
            xsfsc = '0'
        and
            xkhwczt not in (2,3)
        and
            DATE_FORMAT(XKHTJTIME,'%Y%m') =  DATE_FORMAT(now(),'%Y%m')
    </select>

    <select id="zxczgc">
        CALL MRXDYJ()
    </select>
    <!-- 查询部门业绩 -->
    <select id="queryBmYj" resultType="com.wlgb.entity.vo.FwqBbb3" parameterType="com.wlgb.entity.vo.FwqBbb2">
        select
            *
        from
            fwq_bbb3
        where
            bm = #{bm} and bmzx = #{bmzx}
        limit 1
    </select>
    <!-- 查询基准价 -->
    <select id="queryKdjJzj" parameterType="java.util.Map" resultType="java.lang.Double">
        select
            ifnull(sum(ifnull(jjje,0)), 0)
        from
            tb_kdj
        where
            vid = #{vid}
        and
        (
            (DATE_FORMAT(xjctime,'%Y-%m-%d %H:%i:%s') BETWEEN DATE_FORMAT(#{jcsj},'%Y-%m-%d %H:%i:%s') and DATE_FORMAT(#{tcsj},'%Y-%m-%d %H:%i:%s'))
        or
            (DATE_FORMAT(xtctime,'%Y-%m-%d %H:%i:%s') BETWEEN DATE_FORMAT(#{jcsj},'%Y-%m-%d %H:%i:%s') and DATE_FORMAT(#{tcsj},'%Y-%m-%d %H:%i:%s'))
        )
    </select>
    <!-- 查询轰趴师艾特人 -->
    <select id="queryHpsAt" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT CONCAT(xzyj,'') as xzyj from fwq_bbb COLUMNS WHERE   xfd like CONCAT(#{cs},'%')
    </select>
    <!-- 查询本月下单转发条数 -->
    <select id="queryXfdXdZfTs" resultType="java.lang.Integer" parameterType="java.lang.String">
        select IFNULL(sum(IFNULL(XZFTS,0)),0) from tb_xyd where xsfsc = 0 and DATE_FORMAT(XKHTJTIME,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and xzfts > 0 and xfd = #{xfd}
    </select>
    <!-- 查询本月实际转发条数 -->
    <select id="queryQrdSjZfTs" resultType="java.lang.Integer" parameterType="java.lang.String">
        select IFNULL(sum(IFNULL(QSZTS,0)),0) from tb_xyd x LEFT JOIN tb_qrd q on x.xid = q.qxydid where xsfsc = 0 and DATE_FORMAT(QRSJ,'%Y-%m') = DATE_FORMAT(CURDATE(),'%Y-%m') and QSZTS > 0 and xfd = #{xfd}
    </select>
    <!-- 是否是加盟别墅 -->
    <select id="queryBsSfZn" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
            count(1)
        from
            tb_jmsbsxd
        where
            vid = #{vid}
        and
            sfsc = '0'
    </select>
    <!-- 是否是外部投资人 -->
    <select id="queryFdZn" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
            count(1)
        from
            tb_jmsxx
        where
            name = #{name}
        and
            sfsc = '0'
    </select>
    <!-- 根据协议单id查询确认单数据 -->
    <select id="queryQrdByXidAndSfSc" resultType="com.wlgb.entity.TbQrd" parameterType="java.lang.String">
        select * from  tb_qrd where qsfsc = '0' and qxydid = #{xid}
    </select>
    <!-- 根据城市名称去获取群id -->
    <select id="queryQun" resultType="com.wlgb.entity.vo.FwqQunid" parameterType="java.lang.String">
        select
            *
        from
            fwq_qunid
        where
            qname = #{city}
        limit 1
    </select>

    <!-- 根据姓名查询钉钉表信息 -->
    <select id="queryDingDingByName" resultType="com.wlgb.config.DingdingEmployee" parameterType="java.lang.String">
        select * from weilian.dingding_employee where name = #{name} limit 1
    </select>

    <!-- 根据姓名查询钉钉表信息 -->
    <select id="queryDingDingById" resultType="com.wlgb.config.DingdingEmployee" parameterType="java.lang.String">
        select * from weilian.dingding_employee where userid = #{id} limit 1
    </select>

    <!-- 查询所有无场且未做无场工作的别墅信息 -->
    <select id="queryWcJjc" resultType="java.util.Map">
        select v.vid,concat_ws('-',z.sname,c.cname,f.fname,v.vname) as name from tb_villa v left join
            tb_csfq f
        on
            v.fid = f.fid
        left join
            tb_csfgs c
        on
            f.cid = c.cid
        left join
            tb_zgsf z
        on
            c.sid = z.sid where vsfsc = 0 and vid in (select
             DISTINCT vid
        from
            tb_villa
        where
				v.vsfsc = 0
				and
            v.vid
        not in
            (select XBSMC from tb_xyd where
                (
                    (
                        date_format(concat_ws(' ',date_format(now(),'%Y-%m-%d'),'10'),'%Y-%m-%d %H') BETWEEN date_format(XJCTIME,'%Y-%m-%d %H') and date_format(XTCTIME,'%Y-%m-%d %H')
                    or
                        date_format(concat_ws(' ',date_format(now(),'%Y-%m-%d'),'17'),'%Y-%m-%d %H') BETWEEN date_format(XJCTIME,'%Y-%m-%d %H') and date_format(XTCTIME,'%Y-%m-%d %H')
                    )
				or
					(
                        date_format(XJCTIME,'%Y-%m-%d %H') BETWEEN date_format(concat_ws(' ',date_format(now(),'%Y-%m-%d'),'10'),'%Y-%m-%d %H') and date_format(concat_ws(' ',date_format(now(),'%Y-%m-%d'),'17'),'%Y-%m-%d %H')
                    or
                        date_format(XTCTIME,'%Y-%m-%d %H') BETWEEN date_format(concat_ws(' ',date_format(now(),'%Y-%m-%d'),'10'),'%Y-%m-%d %H') and date_format(concat_ws(' ',date_format(now(),'%Y-%m-%d'),'17'),'%Y-%m-%d %H')
                    )
				)
				and
				    xsfsc = 0
				and
				    xkhwczt not in (2,3)
			)
		and
            pid
        not in
            (select pid from tb_villa where vid in (select vid from wlgb_wcgz where to_days(time) = to_days(now())) and vsfsc = 0)
        and
            pid != 'null'
        and
            pid != ''
        and
            pid is not null
        and
            vsfsc = 0)
    </select>

    <!-- 无场电表，根据时间段查询无场别墅 -->
    <select id="queryTodayWcMd" resultType="com.wlgb.entity.TbVilla" parameterType="java.util.Map">
        SELECT
            *
        FROM
            tb_villa
        WHERE
            vsfsc = 0
            AND vid IN (
            SELECT
                vid
            FROM
                tb_villa
            WHERE
                vsfsc = 0
            AND pid IN ( SELECT userid FROM weiliandaiban.csry ))
            AND vid NOT IN (
            SELECT
                xbsmc
            FROM
                tb_xyd
            WHERE
                xsfsc = 0
                and
                    xkhwczt not in (2,3)
                and
                    NOT (
                        (date_format(xtctime,'%Y-%m-%d %H:%i') &lt; date_format(#{jc},'%Y-%m-%d %H:%i')
                        OR ( date_format(xjctime,'%Y-%m-%d %H:%i') &gt; date_format(#{tc},'%Y-%m-%d %H:%i'))
                        )
                    )
                AND xbsmc IN (
                    SELECT
                        vid
                    FROM
                        tb_villa
                    WHERE
                    vsfsc = 0
                    AND pid IN ( SELECT userid FROM weiliandaiban.csry )
                )
            )
            and vid not in (select vid from `weilian-ddxcx`.wlgb_not_villa where sfsc = 0 and (curdate() between starTime and endTime) and wcdb = 0)
            and vid not in (select vid from `weilian-ddxcx`.wlgb_not_lc where sfsc = 0)
    </select>
    <!-- 查询是不是加盟商 -->
    <select id="queryBsUserIdSfJms" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
            count(1)
        from
            tb_bsxl_jmsid
        where
            sfsc = 0
        and
            userid = #{userid}
    </select>

    <!-- 加盟商下单别墅下拉 -->
    <select id="queryXdBsXlJms" resultType="com.wlgb.config.Select" parameterType="java.lang.String">
        SELECT
            VID as value,VNAME as text, false as defaultChecked
        FROM
            tb_villa
        WHERE
            vsfsc = 0
        and
            vid in (select vid from tb_bsxl_jmsid where sfsc = 0 and userid = #{userid})
        ORDER BY convert(`vname`  using gbk) asc
    </select>

    <!-- 下单别墅下拉 -->
    <select id="queryXdBsXl" resultType="com.wlgb.config.Select">
        SELECT
            VID as value,VNAME as text, false as defaultChecked
        FROM
            tb_villa
        WHERE
            vsfsc = 0
        ORDER BY convert(`vname`  using gbk) asc
    </select>

    <!-- 下单客户来源与性质下拉 -->
    <select id="queryXdLyOrXzXl" resultType="com.wlgb.config.Select" parameterType="java.lang.Integer">
        SELECT
            LID as value,LNAME as text, false as defaultChecked
        FROM
            tb_khlyxz
        where
            LTYPE = #{type}
        ORDER BY convert(`LNAME` using gbk) asc
    </select>
    <!-- 活动策划下拉 -->
    <select id="queryXdHdCh" resultType="com.wlgb.config.Select">
        SELECT
            id as value, name as text, false as defaultChecked
        FROM
            tb_hdch
        ORDER BY id asc
    </select>

    <select id="queryYldList" resultType="com.wlgb.entity.WlgbYlxd" parameterType="java.util.Map">
        select * from wlgb_ylxd
        <where>
            <if test="xbsname != null and xbsname != ''">
                and xbsname like CONCAT('%',#{xbsname},'%')
            </if>
            and sfsc=#{type}
        </where>
        order by ytitime desc
        limit #{help.pageNum}, #{help.pageSize}
    </select>

    <select id="queryYldCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(1) from wlgb_ylxd
        <where>
            <if test="xbsname != null and xbsname != ''">
                and xbsname like CONCAT('%',#{xbsname},'%')
            </if>
            and sfsc=#{type}
        </where>
    </select>

    <!--  查询全部城市 -->
    <select id="queryVillaCity" resultType="com.wlgb.config.Select">
        select city as text, city as value,false as defaultChecked from tb_villa where city not in ('测试') and city is not null and vsfsc = 0 group by city
    </select>

    <!-- 已删协议单列表 -->
    <select id="queryScXydList" resultType="com.wlgb.entity.TbXyd" parameterType="java.util.Map">
        SELECT
        x.xid, vname as
        xbsmc,xjctime,xjcflag,xtctime,xtcflag,xfd,xfddh,xzk,xzkdh,xzksfz,xtdxz,xdwmc,xkhly,xrs,xcudrfy,xysdj,xqkzj,xzzsj,
        xzzhlw,xzdxxrs,
        xzzys,xfkm,xbdc,xbdje,xbdsl,xskje,xsksl,xztje,xztsl,xisxzcj,xcjr,xzfts,xhpts,xhfyj,xsxbz,xstatu,xsendtime,
        xsendder,xdzid,xddbh,xzshyksl,
        xisqrgd,cshenfen,company,cfenqu,xpdfpath,ximagepath,xddtype,xdjtype,xiskfp,xxgzid,xgsj,xyxxt,
        xccjg,xdm,sftscc,xsfhps,xxsdj,xxxdj,xdcze,
        xskze,xztze,xlbjf,xzdcl,xyqr,xbjdj,xbjxx,xisdz,xxdxm,xsfsc,xxszj,xscsj, d.name as xscz,xscyy,
        xxgorxt,xhpsfy,xsfzrcs,xzrcsfy,xzrcsjg,xzrcsrs,xhpsfymx,
        xdhbm,xchjl,xhpsxm,xhpspj,xbjdjlx,xxxbj,xxsbj,xyszzdj,xzzfybj,xcdzjbj,
        xhpscjr,xddlxxz,xjzts,xchcjr,xsfhxgjcj,xhxcjr,xzjcc,xxdsd,xxddh,
        xxdsjxm,xsfbx,xbxrs,xbxdj,xbxzje,xxdsscs,xbxzc,xbxcjr,xbxbjsj,
        xbxbjje,xkhxdly,xkhwczt,xzrcscjr,xdccjr,xskcjr,xhpszcgz,xhpsxj,xhpszcqt,
        xhpsbz,xspyhq,xsfbb,xtcxz,xkhtjtime,xfdssbm,xsendtime1,
        xchfzcjr,xdcfzcjr,xskfzcjr,xptsjrzsj,xfdgh,xxdsjxmgh,xbxcjrgh,xchcjrgh,xhpscjrgh,
        xdccjrgh,xskcjrgh,xcjrgh,xhxcjrgh,xtdj,xptxx,
        status,time_out,xchjlid,sfwcyxhf,sfthysj,khsffk,khhfzt,qqid,sfsqkp,sfdyjk,sfcxptxd,xsfsywxfwdddje
        FROM
        tb_xyd x
        LEFT JOIN
        tb_villa v
        on
        x.xbsmc = v.vid
        LEFT JOIN
        dingding_employee d
        on
        x.XSCZ = d.userid
        <where>
            and
            xsfsc = 1
            and
            xkhwczt not in (2,3)
            <if test="search != null and search != ''">
                and
                (xzk like CONCAT('%',#{search},'%') or xzkdh like CONCAT('%',#{search},'%') or vname like
                CONCAT('%',#{search},'%') or xfd like CONCAT('%',#{search},'%') or xfddh like CONCAT('%',#{search},'%'))
            </if>
        </where>
        ORDER BY xscsj DESC
        limit #{help.pageNum}, #{help.pageSize}
    </select>
    <select id="queryScXydCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT
        count(1)
        FROM
        tb_xyd x
        LEFT JOIN
        tb_villa v
        on
        x.xbsmc = v.vid
        <where>
            and
            xsfsc = 1
            and
            xkhwczt not in (2,3)
            <if test="search != null and search != ''">
                and
                (xzk like CONCAT('%',#{search},'%') or xzkdh like CONCAT('%',#{search},'%') or vname like
                CONCAT('%',#{search},'%') or xfd like CONCAT('%',#{search},'%') or xfddh like CONCAT('%',#{search},'%'))
            </if>
        </where>
    </select>

    <!-- 已删确认单列表 -->
    <select id="queryScQrdList" resultType="com.wlgb.entity.vo.TbQrdVo" parameterType="java.util.Map">
        select
        *
        from
        tb_qrd q
        left join
        tb_xyd x
        on
        q.qxydid = x.xid
        where
        q.qsfsc = 1
        and
        x.xsfsc = 1
        and
        x.xkhwczt not in (2,3)
        <if test="search != null and search != ''">
            and
            (x.xzk like CONCAT('%',#{search},'%') or x.xzkdh like CONCAT('%',#{search},'%'))
        </if>
        limit #{help.pageNum}, #{help.pageSize}
    </select>

    <select id="queryScQrdCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
        count(1)
        from
        tb_qrd q
        left join
        tb_xyd x
        on
        q.qxydid = x.xid
        where
        q.qsfsc = 1
        and
        x.xsfsc = 1
        and
        x.xkhwczt not in (2,3)
        <if test="search != null and search != ''">
            and
            (x.xzk like CONCAT('%',#{search},'%') or x.xzkdh like CONCAT('%',#{search},'%'))
        </if>
    </select>

    <!-- 查询区域 -->
    <select id="queryQyUtil" resultType="com.wlgb.entity.vo.QyUtil" parameterType="java.util.Map">
        select jid,jname,jbz,cid,jcolor from tb_csjq where jid = #{jid}
    </select>

    <!-- 查询带看播报机器人 -->
    <select id="queryDkBb" resultType="com.wlgb.config.DkBbJqr" parameterType="java.util.Map">
        select
            id,webhook,secret,city,sfsc
        from
            tb_dk_jqr
        where
            city = #{city}
        and
            sfsc = 0
    </select>

    <!-- 查询带看详情 -->
    <select id="queryDkXq" resultType="com.wlgb.entity.TbDk" parameterType="java.lang.String">
        select
        *
        from
        tb_dk
        <where>
            and sfsc = 0
            and dkddbh = #{dkddbh}
        </where>
        LIMIT 1
    </select>

    <!-- 根据别墅id获取别墅店长及政委 -->
    <select id="queryZwAndDz" resultType="com.wlgb.entity.vo.ZwAndDz" parameterType="java.lang.String">
        select
            v.vid, v.vname, e.name as zwname, e.userid as zwid, e.mobile as zwtel, ee.name as zbdzname, ee.userid as zbdzid, ee.mobile as zbdztel, v.city as city
        from
            tb_villa v
        left join
            wlgb_zw z
        on
            v.vid = z.bsid
        left join
            dingding_employee e
        on
            z.ding_userId = e.userid

        left join
            dingding_employee ee
        on
            v.pid = ee.userid
        where
            v.vid = #{vid}
    </select>

    <!-- 带看别墅下拉 -->
    <select id="queryBsMcIdZw" resultType="com.wlgb.entity.vo.BsZw" parameterType="java.util.Map">
        select
        v.vid as value, v.vname as text, e.name, e.userid, e.mobile
        from
        tb_villa v
        left join
        wlgb_zw z
        on
        v.vid = z.bsid
        left join
        dingding_employee e
        on
        z.ding_userId = e.userid
        where
        v.vsfsc = 0
        <if test="search != null and search != ''">
            and v.vname like CONCAT('%',#{search},'%')
        </if>
        and
        v.city in (select city from `weilian-ddxcx`.wlgb_bscs where sfsc = 0)
        and
        v.vid not in (select vid from `weilian-ddxcx`.wlgb_bscs_notdk where sfsc = 0)
        and
        v.vxz = '直营'
        ORDER BY convert(`vname` using gbk) asc
    </select>

    <!-- 根据门店名称查询别墅信息 -->
    <select id="queryVillaByVname" parameterType="java.lang.String" resultType="com.wlgb.entity.TbVilla">
        select * from tb_villa where vname = #{vname} limit 1
    </select>


    <select id="queryKyQnMdList" resultType="com.wlgb.entity.TbVilla" parameterType="java.util.Map">
        SELECT
        *
        FROM
        `tb_villa`
        <where>
            and kytime is not null
            <if test="time != null">
                and
                DATE_FORMAT(#{time}, '%Y-%m-%d') > DATE_FORMAT(kytime, '%Y-%m-%d')
                and
                if( gdtime is null , 1=1, DATE_FORMAT(gdtime,'%Y-%m-%d') > DATE_FORMAT(#{time},'%Y-%m-%d') )
            </if>
            <if test="list != null and list != ''">
                and vmdsstz in
                <foreach item="item" index="index" collection="list"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="list1 != null and list1 != ''">
                and vname not in
                <foreach item="item" index="index" collection="list1"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="list2 != null and list2 != ''">
                and city in
                <foreach item="item" index="index" collection="list2"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and vsfft = '1'
        </where>
    </select>

    <select id="queryKyQnMdCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT
        COUNT(1)
        FROM
        `tb_villa`
        <where>
            and kytime is not null
            <if test="time != null">
                and
                DATE_FORMAT(#{time}, '%Y-%m-%d') > DATE_FORMAT(kytime, '%Y-%m-%d')
                and
                if( gdtime is null , 1=1, DATE_FORMAT(gdtime,'%Y-%m-%d') > DATE_FORMAT(#{time},'%Y-%m-%d') )
            </if>
            <if test="list != null and list != ''">
                and vmdsstz in
                <foreach item="item" index="index" collection="list"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="list1 != null and list1 != ''">
                and vname not in
                <foreach item="item" index="index" collection="list1"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bsmc != null and bsmc != ''">
                and vname = #{bsmc}
            </if>
            <if test="list2 != null and list2 != ''">
                and city in
                <foreach item="item" index="index" collection="list2"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and vsfft = '1'
        </where>
    </select>

    <!--crm城市下拉-->
    <select id="queryCrmCsList" resultType="com.wlgb.entity.CrmCs">
        select
            csid, csname
        from
            crm_cs
        where
            cszt = 0
        order by
            cspx
        asc
    </select>

    <!--crm客户信息状态-->
    <select id="queryCrmKhZtList" resultType="com.wlgb.entity.CrmKhzt" parameterType="java.lang.Integer">
        select
            *
        from
            crm_khzt
        where
            sfsc = 0
        and
            lxid = #{lxid}
        ORDER BY zid asc
    </select>

    <!-- 判断电话号码去重 -->
    <select id="queryCrmDhSfcz" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT
        count(qid)
        FROM
        crm_qbkh
        where
        qxxzt != 3
        and
        (qkhdh = #{dhhm} or qkhsj = #{dhhm} or qkhhm = #{dhhm})
        <if test="qid != null and qid != '' and qid != '0'">
            and qid != #{qid}
        </if>
    </select>

    <!-- crm微信号查重 -->
    <select id="queryCrmWxhCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(1) from crm_qbkh where qkhwxh = #{qkhwxh} and YEAR(qcjsj) > 2019 and qxxzt in ('1','0', '2')
    </select>

    <!-- crm电话号码查询 -->
    <select id="queryCrmHmCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(1) from crm_qbkh where (qkhdh = #{dhhm} or qkhsj = #{dhhm} or qkhhm = #{dhhm}) and YEAR(qcjsj) > 2019 and qxxzt in ('1','0', '2')
    </select>

    <!-- 根据城市名称获取区域 -->
    <select id="queryCrmCsSfqByCsName" resultType="com.wlgb.entity.CrmCsfq" parameterType="java.lang.String">
        SELECT * from crm_csfq where csname = #{csname}
    </select>

    <!-- 插入crm版本号 -->
    <select id="queryCrmBbh" resultType="java.lang.String">
        select bbh from crm_bbh where bbh=(select max(bbh) from crm_bbh) LIMIT 1;
    </select>

    <!-- 查询Crm全部数据导出使用 -->
    <select id="queryCrmDataImport" resultType="com.wlgb.entity.CrmQbkh" parameterType="java.util.Map">
        select
        *
        from
        crm_qbkh q
        <if test="qcjsj1 != null and qcjsj1 != ''">
            left join crm_qkh_yjjl y ON q.crmbh = y.crmbh
        </if>
        where
        YEAR(qcjsj) > 2019
        <if test="qcjr != null and qcjr != ''">
            and q.qcjr like concat('%', #{qcjr}, '%')
        </if>
        <if test="qkhdh != null and qkhdh != ''">
            and q.qkhdh like concat('%', #{qkhdh}, '%')
        </if>
        <if test="qkhwxm != null and qkhwxm != ''">
            and q.qkhwxm like concat('%', #{qkhwxm}, '%')
        </if>
        <if test="qkhwxh != null and qkhwxh != ''">
            and q.qkhwxh like concat('%', #{qkhwxh}, '%')
        </if>
        <if test="qdhbm != null and qdhbm != ''">
            and q.qdhbm like concat('%', #{qdhbm}, '%')
        </if>

        <if test="qxyhhr != null and qxyhhr != ''">
            and q.qxyhhr =#{qxyhhr}
        </if>
        <if test="qxhstgr != null and qxhstgr != ''">
            and q.qxhstgr = #{qxhstgr}
        </if>

        <if test="qxgsj1 != null and qxgsj1 != ''">
            and q.qxgsj between #{qxgsj1} and #{qxgsj2}
        </if>
        <if test="qcjsj1 != null and qcjsj1 != ''">
            and (q.qcjsj between #{qcjsj1} and #{qcjsj2} OR y.cjsj between #{qcjsj1} and #{qcjsj2})
        </if>
        <if test="qkhydsj1 != null and qkhydsj1 != ''">
            and q.qkhydsj between #{qkhydsj1} and #{qkhydsj2}
        </if>
        <if test="qxxzt != null and qxxzt != ''">
            and q.qxxzt = #{qxxzt}
        </if>
        <if test="qcs != null and qcs != ''">
            and q.qcs = #{qcs}
        </if>
        <if test="qy != null and qy != ''">
            and q.fqname in
            <foreach collection="qy" item="qy" index="index" open="(" close=")" separator=",">
                #{qy}
            </foreach>
        </if>
        <if test="qqdly != null and qqdly != ''">
            and q.qqdly in
            <foreach collection="qqdly" item="qqdly" index="index" open="(" close=")" separator=",">
                #{qqdly}
            </foreach>
        </if>

        <if test="qfzr != null and qfzr != ''">
            and q.qfzr in
            <foreach collection="qfzr" item="qfzr" index="index" open="(" close=")" separator=",">
                #{qfzr}
            </foreach>
        </if>
        <if test="help != null and help != ''">
            limit #{help.pageNum}, #{help.pageSize}
        </if>
    </select>

    <select id="queryCrmDataImportCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select
        count(1)
        from
        crm_qbkh q
        <if test="qcjsj1 != null and qcjsj1 != ''">
            left join crm_qkh_yjjl y ON q.crmbh = y.crmbh
        </if>
        where
        YEAR(qcjsj) > 2019
        <if test="qcjr != null and qcjr != ''">
            and q.qcjr like concat('%', #{qcjr}, '%')
        </if>
        <if test="qkhdh != null and qkhdh != ''">
            and q.qkhdh like concat('%', #{qkhdh}, '%')
        </if>
        <if test="qkhwxm != null and qkhwxm != ''">
            and q.qkhwxm like concat('%', #{qkhwxm}, '%')
        </if>
        <if test="qkhwxh != null and qkhwxh != ''">
            and q.qkhwxh like concat('%', #{qkhwxh}, '%')
        </if>
        <if test="qdhbm != null and qdhbm != ''">
            and q.qdhbm like concat('%', #{qdhbm}, '%')
        </if>

        <if test="qxgsj1 != null and qxgsj1 != ''">
            and q.qxgsj between #{qxgsj1} and #{qxgsj2}
        </if>
        <if test="qcjsj1 != null and qcjsj1 != ''">
            and (q.qcjsj between #{qcjsj1} and #{qcjsj2} OR y.cjsj between #{qcjsj1} and #{qcjsj2})
        </if>
        <if test="qkhydsj1 != null and qkhydsj1 != ''">
            and q.qkhydsj between #{qkhydsj1} and #{qkhydsj2}
        </if>


        <if test="qcs != null and qcs != ''">
            and q.qcs = #{qcs}
        </if>
        <if test="qy != null and qy != ''">
            and q.fqname in
            <foreach collection="qy" item="qy" index="index" open="(" close=")" separator=",">
                #{qy}
            </foreach>
        </if>
        <if test="qqdly != null and qqdly != ''">
            and q.qqdly in
            <foreach collection="qqdly" item="qqdly" index="index" open="(" close=")" separator=",">
                #{qqdly}
            </foreach>
        </if>

        <if test="qfzr != null and qfzr != ''">
            and q.qfzr in
            <foreach collection="qfzr" item="qfzr" index="index" open="(" close=")" separator=",">
                #{qfzr}
            </foreach>
        </if>
        <if test="qxxzt != null and qxxzt != ''">
            and q.qxxzt = #{qxxzt}
        </if>
    </select>

    <!-- 根据区域名称查询区域id -->
    <select id="queryCmrCsFqIdByFqName" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT csfqid FROM crm_csfq WHERE fqname = #{fqname}
    </select>

    <!-- 根据部门id查询下面部门列表 -->
    <select id="queryCrmBmCxByBmDa" parameterType="java.lang.String" resultType="com.wlgb.entity.CrmBmxq">
        select * from crm_bmxq where bmid = #{bmda} or bmid2 = #{bmda} or bmid3 = #{bmda} or bmid4 = #{bmda} or bmid5 = #{bmda} or bmid6 = #{bmda} or bmid7 = #{bmda} or bmid8 = #{bmda} or bmid9 = #{bmda} or bmid10 = #{bmda} or bmid11 = #{bmda} or bmid12 = #{bmda} or bmid13 = #{bmda} or bmid14 = #{bmda} or bmid15 = #{bmda}
    </select>

    <select id="queryCrmBmCxByUserid" parameterType="java.lang.String" resultType="com.wlgb.entity.CrmBmxq">
        select * from crm_bmxq where userid = #{userid}  LIMIT 1
    </select>

    <!-- 根据查重条件查询查重记录 -->
    <select id="queryCrmCcSjByCcData" resultType="com.wlgb.entity.CrmQbkh" parameterType="java.lang.String">
        select qxxzt, qfzr, qkhdh, qkhwxh, qdhbm, qqdly,qyjsj,qcjsj,slid from crm_qbkh where (qkhwxh like CONCAT(CONCAT('%', #{cc}), '%')  or qkhdh like CONCAT(CONCAT('%', #{cc}), '%')  or qkhsj like CONCAT(CONCAT('%', #{cc}), '%')  or qkhhm =CONCAT(CONCAT('%', #{cc}), '%'))  and qxxzt in ('1','0', '2') and  YEAR(qcjsj) > 2019
    </select>

    <!-- 查询全部crm分区 -->
    <select id="queryCrmCsFqList" resultType="com.wlgb.entity.CrmCsfq">
        SELECT * FROM crm_csfq
    </select>

    <!-- 店长私下对账订单 -->
    <select id="querySxDzZbDz" resultType="java.util.Map">
        SELECT
            x.xddbh,xzk,q.qzbdz,v.city,v.vname,q.qrsj
        FROM
            tb_xyd x
            LEFT JOIN tb_qrd q ON x.xid = q.QXYDID
            LEFT JOIN tb_villa v ON x.xbsmc = v.vid
        WHERE
            xid NOT IN (
            SELECT
                xid
            FROM
                tb_xyd x
                LEFT JOIN `weilian-ddxcx`.wlgb_xyd_log xl ON x.xid = xl.xlxid
            WHERE
                xsfsc = 0
                AND DATE_FORMAT( XTCTIME, '%Y-%m-%d %H:%i:%s' ) >= DATE_FORMAT( concat_ws( ' ', date_format( date_add( now(), INTERVAL - 1 DAY ), '%Y-%m-%d' ), '12:00:00' ), '%Y-%m-%d %H:%i:%s' )
                AND DATE_FORMAT( concat_ws( ' ', date_format( CURDATE(), '%Y-%m-%d' ), '12:00:00' ), '%Y-%m-%d %H:%i:%s' ) > DATE_FORMAT( XTCTIME, '%Y-%m-%d %H:%i:%s' )
                AND xbsmc IN (
                SELECT
                    vid
                FROM
                    tb_villa
                WHERE
                    vsfsc = 0
                AND pid IN ( SELECT userid FROM weiliandaiban.csry ))
                AND ( xltext = '转账-同步数据-修改' OR xltext = '转账-同步数据-新增' )
            GROUP BY
                xid
            )
            AND xsfsc = 0
            AND DATE_FORMAT( XTCTIME, '%Y-%m-%d %H:%i:%s' ) >= DATE_FORMAT( concat_ws( ' ', date_format( date_add( now(), INTERVAL - 1 DAY ), '%Y-%m-%d' ), '12:00:00' ), '%Y-%m-%d %H:%i:%s' )
            AND DATE_FORMAT( concat_ws( ' ', date_format( CURDATE(), '%Y-%m-%d' ), '12:00:00' ), '%Y-%m-%d %H:%i:%s' ) > DATE_FORMAT( XTCTIME, '%Y-%m-%d %H:%i:%s' )
            AND xbsmc IN (
            SELECT
                vid
            FROM
                tb_villa
            WHERE
                vsfsc = 0
            AND pid IN ( SELECT userid FROM weiliandaiban.csry ))
            AND xbsmc not in (select vid from `weilian-ddxcx`.wlgb_not_lc where sfsc =  0)
            AND qsfsc = 0
            AND sfwcdz = 1
        ORDER BY
            XTCTIME
    </select>

    <!-- 昨天下单未包含转发和点赞数 -->
    <select id="queryYxbXdWzf" resultType="java.util.Map">
        SELECT
            xfd AS name,
            bmmc AS bm,
            v.vname AS bs,
            XDDBH AS ddbh,
            XJCTIME AS jcsj,
            XTCTIME AS tcsj,
            XKHTJTIME AS xdsj,
            IFNULL( XZFTS, 0 ) AS zfts,
            IFNULL( XJZTS, 0 ) AS jzts
        FROM
            tb_xyd x
            LEFT JOIN fwq_bbb2 b ON x.xfd = b.xm
            LEFT JOIN tb_villa v ON x.xbsmc = v.vid
        WHERE
            xsfsc = 0
            AND XKHWCZT NOT IN ( 2, 3 )
            AND DATE_FORMAT( XKHTJTIME, '%Y-%m-%d' ) = DATE_FORMAT( date_add(now(),interval-1 day), '%Y-%m-%d' )
            AND xfd IN ( SELECT xm FROM fwq_bbb2 WHERE bmzx = 1 )
            AND IFNULL( XZFTS, 0 ) = 0
            AND IFNULL( XJZTS, 0 ) = 0
        ORDER BY
            XKHTJTIME
    </select>

    <!-- 查询全部钉钉信息表数据 -->
    <select id="queryDingdingEmployeeList" resultType="com.wlgb.config.DingdingEmployee">
        select * from dingding_employee
    </select>

    <!-- 查询全部crm人员信息表 -->
    <select id="queryCrmUserList" resultType="com.wlgb.entity.CrmUser">
        select * from crm_user
    </select>
    <!-- 清空crm部门详情表 -->
    <select id="qkCrmBmXqTable">
        truncate table crm_bmxq
    </select>
    <!-- 清空crm部门表 -->
    <select id="qkCrmBmTable">
        truncate table crm_bm
    </select>
    <!-- 清空crm用户备份表 -->
    <select id="qkCrmUserBackupsTable">
        truncate table crm_user_backups
    </select>
    <!-- 将crm_user表数据赋值给备份表 方便比对筛选-->
    <select id="bfCrmUserInBackups">
        insert  into crm_user_backups (userid,name,mobile,avatar ) select userid,name,mobile,avatar from crm_user
    </select>
    <!-- 清空crm用户copy表 -->
    <select id="qkCrmUserCopyTable">
        truncate table crm_user_copy
    </select>
    <!-- 保存crm_user_copy表 -->
    <insert id="saveCrmUserCopy" parameterType="com.wlgb.entity.CrmUser">
        insert into crm_user_copy(
        <if test="userid !=null">
            userid
        </if>
        <if test="name !=null">
            ,name
        </if>
        <if test="avatar !=null">
            ,avatar
        </if>
        <if test="mobile !=null">
           ,mobile
        </if>
        )values (
        <if test="userid !=null">
            #{userid,jdbcType =VARCHAR}
        </if>
        <if test="name !=null">
            ,#{name,jdbcType =VARCHAR}
        </if>
        <if test="avatar !=null">
            ,#{avatar,jdbcType =VARCHAR}
        </if>
        <if test="mobile !=null">
            ,#{mobile,jdbcType =VARCHAR}
        </if>
        )
    </insert>
    <!-- 删除crm用户copy重复数据 -->
    <delete id="delCrmUserCopy">
        DELETE FROM crm_user_copy WHERE id NOT IN ( SELECT id FROM ( SELECT min( id ) AS id FROM crm_user_copy GROUP BY userid ) AS b )
    </delete>
    <!-- 清空crm用户表 -->
    <select id="qkCrmUserTable">
        truncate table crm_user
    </select>

    <!--5、将crm_user_copy表的数据赋值给crm_user-->
    <insert id="saveCrmUser">
        insert  into crm_user (userid,name,mobile,avatar ) select userid,name,mobile,avatar from crm_user_copy;
    </insert>

    <insert id="saveDingdingEmployee" parameterType="com.wlgb.entity.Demployee">
        insert into dingding_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="userid !=null">
            userid,
        </if>
        <if test="name !=null">
            name,
        </if>
        <if test="departid !=null">
            departid,
        </if>
        <if test="active !=null">
            active,
        </if>
        <if test="avatar !=null">
            avatar,
        </if>
        <if test="position !=null">
            position,
        </if>
        <if test="mobile !=null">
            mobile,
        </if>
        <if test="tel !=null">
            tel,
        </if>
        <if test="workplace !=null">
            workplace,
        </if>
        <if test="remark !=null">
            remark,
        </if>
        <if test="email !=null">
            email,
        </if>
        <if test="jobnumber !=null">
            jobnumber,
        </if>
        </trim>
            values
        <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="userid !=null">
            #{userid},
        </if>
        <if test="name !=null">
            #{name},
        </if>
        <if test="departid !=null">
            #{departid},
        </if>
        <if test="active !=null">
            #{active},
        </if>
        <if test="avatar !=null">
            #{avatar},
        </if>
        <if test="position !=null">
            #{position},
        </if>
        <if test="mobile !=null">
            #{mobile},
        </if>
        <if test="tel !=null">
            #{tel},
        </if>
        <if test="workplace !=null">
            #{workplace},
        </if>
        <if test="remark !=null">
            #{remark},
        </if>
        <if test="email !=null">
            #{email},
        </if>
        <if test="jobnumber !=null">
            #{jobnumber},
        </if>
        </trim>
    </insert>

    <update id="updateDingdingEmployee" parameterType="com.wlgb.entity.Demployee">
        update dingding_employee
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="departid != null">departid = #{departid},</if>
            <if test="active != null">active = #{active},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="position != null">position = #{position},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="tel != null">tel = #{tel},</if>
            <if test="workplace != null">workplace = #{workplace},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="email != null">email = #{email},</if>
            <if test="jobnumber != null">jobnumber = #{jobnumber},</if>
        </set>
        where userid = #{userid}
    </update>

    <select id="queryTbpersonById" resultType="com.wlgb.entity.Tbperson" parameterType="java.lang.String">
        select * from tb_person where pid = #{id} limit 1
    </select>

    <insert id="saveTbperson" parameterType="com.wlgb.entity.Tbperson">
        insert into tb_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pid !=null">
                pid,
            </if>
            <if test="ptx !=null">
                ptx,
            </if>
            <if test="pname !=null">
                pname,
            </if>
            <if test="psfid !=null">
                psfid,
            </if>
            <if test="pgsid !=null">
                pgsid,
            </if>
            <if test="pfqid !=null">
                pfqid,
            </if>
            <if test="pgw !=null">
                pgw,
            </if>
            <if test="pqx !=null">
                pqx,
            </if>
            <if test="dduserid !=null">
                dduserid,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pid !=null">
                #{pid},
            </if>
            <if test="ptx !=null">
                #{ptx},
            </if>
            <if test="pname !=null">
                #{pname},
            </if>
            <if test="psfid !=null">
                #{psfid},
            </if>
            <if test="pgsid !=null">
                #{pgsid},
            </if>
            <if test="pfqid !=null">
                #{pfqid},
            </if>
            <if test="pgw !=null">
                #{pgw},
            </if>
            <if test="pqx !=null">
                #{pqx},
            </if>
            <if test="dduserid !=null">
                #{dduserid},
            </if>
        </trim>
    </insert>

    <update id="updateTbperson" parameterType="com.wlgb.entity.Tbperson">
        update tb_person
        <set>
            <if test="ptx != null">ptx = #{ptx},</if>
            <if test="pname != null">pname = #{pname},</if>
            <if test="psfid != null">psfid = #{psfid},</if>
            <if test="pgsid != null">pgsid = #{pgsid},</if>
            <if test="pfqid != null">pfqid = #{pfqid},</if>
            <if test="pgw != null">pgw = #{pgw},</if>
            <if test="pqx != null">pqx = #{pqx},</if>
            <if test="dduserid != null">dduserid = #{dduserid},</if>
        </set>
        where pid = #{pid}
    </update>

    <select id="querySysUserById" resultType="com.wlgb.entity.SysUser" parameterType="java.lang.String">
        select * from sys_user where USER_ID = #{user_id} limit 1
    </select>

    <insert id="saveSysUser" parameterType="com.wlgb.entity.SysUser">
        insert into sys_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="user_id !=null">
                user_id,
            </if>
            <if test="username !=null">
                username,
            </if>
            <if test="password !=null">
                password,
            </if>
            <if test="name !=null">
                name,
            </if>
            <if test="ridhts !=null">
                ridhts,
            </if>
            <if test="role_id !=null">
                role_id,
            </if>
            <if test="status !=null">
                status,
            </if>
            <if test="bz !=null">
                bz,
            </if>
            <if test="skin !=null">
                skin,
            </if>
            <if test="number !=null">
                number,
            </if>
            <if test="phone !=null">
                phone,
            </if>
            <if test="department_id !=null">
                department_id,
            </if>
            <if test="corpid !=null">
                corpid,
            </if>
            <if test="quanxianzpd !=null">
                quanxianzpd,
            </if>
            <if test="mima !=null">
                mima,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="user_id !=null">
                #{user_id},
            </if>
            <if test="username !=null">
                #{username},
            </if>
            <if test="password !=null">
                #{password},
            </if>
            <if test="name !=null">
                #{name},
            </if>
            <if test="ridhts !=null">
                #{ridhts},
            </if>
            <if test="role_id !=null">
                #{role_id},
            </if>
            <if test="status !=null">
                #{status},
            </if>
            <if test="bz !=null">
                #{bz},
            </if>
            <if test="skin !=null">
                #{skin},
            </if>
            <if test="number !=null">
                #{number},
            </if>
            <if test="phone !=null">
                #{phone},
            </if>
            <if test="department_id !=null">
                #{department_id},
            </if>
            <if test="corpid !=null">
                #{corpid},
            </if>
            <if test="quanxianzpd !=null">
                #{quanxianzpd},
            </if>
            <if test="mima !=null">
                #{mima},
            </if>
        </trim>
    </insert>

    <update id="updateSysUser" parameterType="com.wlgb.entity.SysUser">
        update sys_user
        <set>
            <if test="username != null">username = #{username},</if>
            <if test="name != null">name = #{name},</if>
            <if test="bz != null">bz = #{bz},</if>
            <if test="number != null">number = #{number},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="department_id != null">department_id = #{department_id},</if>
        </set>
        where user_id = #{user_id}
    </update>

    <!-- 查询发起对账表单数量 -->
    <select id="queryFsDzbdSl" resultType="java.lang.Integer">
        select count(1) from tb_xyd where DATE_FORMAT(XJCTIME,'%Y-%m-%d') >= DATE_FORMAT(DATE_SUB(CURDATE(), interval 30 DAY), '%Y-%m-%d') and  DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') > DATE_FORMAT(XJCTIME,'%Y-%m-%d %H:%i:%s') and xsfsc = 0 and xid in (select xid from `weilian-ddxcx`.wlgb_bdjl where bz = '对账表单提交' and slid is not null and slid != '')
    </select>
    <!-- 查询实际订单数量 -->
    <select id="querySyDdSl" resultType="java.lang.Integer">
        select count(1) from tb_xyd where DATE_FORMAT(XJCTIME,'%Y-%m-%d') >= DATE_FORMAT(DATE_SUB(CURDATE(), interval 30 DAY), '%Y-%m-%d') and  DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') > DATE_FORMAT(XJCTIME,'%Y-%m-%d %H:%i:%s') and xsfsc = 0
    </select>

    <!-- 查询抖音30天超时数据 -->
    <select id="queryDyDataMonth" resultType="com.wlgb.entity.FwqThbD9c">
        SELECT * FROM `fwq_thb_d9c` where hptype = '抖音视频' and sfwc = '0' and DATE_FORMAT(sstime,'%Y-%m-%d') = DATE_FORMAT(DATE_ADD(CURDATE(),INTERVAL -30 day),'%Y-%m-%d')
    </select>

    <!-- 查询招聘今日到面人数 -->
    <select id="queryHrJrDmByYyName" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
            count(1)
        from
            hr_msz
        where
            sfsc = '0'
        and
            YYRname = #{name}
        and
            DATE_FORMAT(TJdate,'%Y-%m-%d') = DATE_FORMAT(CURDATE(),'%Y-%m-%d')
    </select>
    <!-- 查询招聘今日通过人数 -->
    <select id="queryHrJrTgByYyName" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
            count(1)
        from
            hr_msz
        where
            sfsc = '0'
        and
            YYRname = #{name}
        and
            DATE_FORMAT(TJdate,'%Y-%m-%d') = DATE_FORMAT(CURDATE(),'%Y-%m-%d')
        and
            mianshi = '已通过'
    </select>

    <!-- 招聘异常数据 -->
    <select id="queryHrYcSj" resultType="com.wlgb.entity.HrMsz">
        SELECT
            YYRname as yyrname,TJdate as tjdate,h.Msdata as msdata,c.fgw as yxgw,c.zcity as gwcity,name,if(jljd = '1', '简历未查阅', if(msjgtz = '0' and mianshi = '已通过', '简历进度为面试通过，但面试结果通知为否',if(jljd = '11','试岗期已大于7天','简历进度为面试通过，到岗日期早于当天'))) as beizhu
        FROM
            `hr_msz` h LEFT JOIN hr_gw_city c on h.gwcity = c.zcityid
        where
            h.sfsc = '0'
        and ((JLJD = '1' and date(TJdate) >= DATE_SUB(CURDATE(), INTERVAL 2 DAY) ) or (mianshi = '已通过' and msjgtz = '0' and date(TJdate) >= DATE_SUB(CURDATE(), INTERVAL 2 DAY)) or (jljd = '7' and DATE_FORMAT(CURDATE(),'%Y-%m-%d') >= DATE_FORMAT(DaoGangdate,'%Y-%m-%d')) or (jljd = '11' and DATEDIFF(DATE_FORMAT(CURDATE(),'%Y-%m-%d'), DATE_FORMAT(DaoGangdate,'%Y-%m-%d'))> 7)) ORDER BY TJdate asc
    </select>

    <!-- 客单价别墅下拉 -->
    <select id="queryKdjVilla" parameterType="java.util.Map" resultType="com.wlgb.config.Select">
        select
        vid as value, vname as text, false as defaultChecked
        from
        tb_villa
        where
        vid in (SELECT vid FROM `tb_kdj` where DATE_FORMAT(xjctime,'%Y-%m-%d %H:%i') >= DATE_FORMAT(#{jcsj},'%Y-%m-%d
        %H:%i') and DATE_FORMAT(#{tcsj},'%Y-%m-%d %H:%i') >= DATE_FORMAT(xtctime,'%Y-%m-%d %H:%i') and cc is not null
        GROUP BY vid)
        <if test="xz == 1">
            and vxz = '直营'
        </if>
        <if test="xz == 2">
            and vxz != '直营'
        </if>
        ORDER BY convert(`VNAME` using gbk) asc
    </select>

    <!-- 根据别墅进退场时间查询时间段内客单价情况 -->
    <select id="queryTbKdjByBsAndJcAndTc" resultType="com.wlgb.entity.TbKdj" parameterType="java.util.Map">
        SELECT id,xjctime,xtctime,vid,vname,rq,sum(ifnull(je,0)) as je,sum(ifnull(jjje,0)) as jjje,sum(ifnull(bjje,0)) as bjje
        FROM `tb_kdj`
        where vid=#{bs}
          and (xjctime between date_format(#{start},'%Y-%m-%d %H:%i:%s') and date_format(#{end},'%Y-%m-%d %H:%i:%s'))
          and (xtctime between date_format(#{start},'%Y-%m-%d %H:%i:%s') and date_format(#{end},'%Y-%m-%d %H:%i:%s'))
    </select>

    <!-- 查询时间段内所有的客单价信息 -->
    <select id="queryYyKdj" parameterType="java.util.Map" resultType="com.wlgb.entity.TbKdj">
        SELECT
            *
        FROM weilian.tb_kdj
        WHERE
            vid = #{vid}
        and
            NOT (
                (date_format(xtctime,'%Y-%m-%d %H:%i') &lt; date_format(#{jcsj}, '%Y-%m-%d %H:%i')
                OR ( date_format(xjctime,'%Y-%m-%d %H:%i') > date_format(#{tcsj}, '%Y-%m-%d %H:%i'))
                )
            )
    </select>

    <!-- 判断是否是民宿 -->
    <select id="querySfMs" parameterType="java.lang.String" resultType="java.lang.Integer">
        select
         count(1)
        from
            tb_kdj_villa
        where
            sfsc = 0
        and
            vid = #{vid}
    </select>

    <!-- 批量修改客单价 -->
    <select id="zxKdjPlXg" parameterType="java.util.Map" resultType="java.util.Map">
        CALL `客单价批量修改`(#{jcsj}, #{tcsj}, #{je}, #{type}, #{name}, #{userId}, #{ms})
    </select>

    <!-- 别墅分区下拉 -->
    <select id="queryCsFqSelect" resultType="com.wlgb.config.Select">
        SELECT
            FID as value,FNAME as text, false as defaultChecked
        FROM
            tb_csfq
        ORDER BY convert(`FNAME`  using gbk) asc
    </select>

    <!-- 别墅集群下拉 -->
    <select id="queryCsJqSelect" resultType="com.wlgb.config.Select">
        SELECT
            jid as value,JNAME as text, false as defaultChecked
        FROM
            tb_csjq
        ORDER BY convert(`JNAME`  using gbk) asc
    </select>

    <!-- 别墅播报城市下拉 -->
    <select id="queryBbCsSelect" resultType="com.wlgb.config.Select">
        SELECT
            qname as value,qname as text, false as defaultChecked
        FROM
            `fwq_qunid`
        ORDER BY convert(`qname`  using gbk) asc
    </select>

    <!-- 别墅分公司下拉 -->
    <select id="queryFgsSelect" resultType="com.wlgb.config.Select">
        SELECT
            CID as value, CNAME as text, false as defaultChecked
        FROM
            `tb_csfgs`
        ORDER BY convert(`CNAME`  using gbk) asc
    </select>

    <!-- 别墅管理数据列表 -->
    <select id="queryBsWhList" resultType="com.wlgb.entity.vo.TbVillaVo" parameterType="java.util.Map">
        select
        v.*,e.name as zbdz,c.FNAME as fq,j.JNAME as jq
        from
        tb_villa v LEFT JOIN dingding_employee e on v.PID = e.userid LEFT JOIN tb_csfq c on v.FID = c.FID LEFT JOIN
        tb_csjq j on v.JID = j.jid
        <where>
            and vsfsc = '0'
            <if test="vname != null and vname != ''">
                and vname like CONCAT('%',#{vname},'%')
            </if>
            <if test="vxz != null and vxz != ''">
                and vxz = #{vxz}
            </if>
            <if test="vmdsstz != null and vmdsstz != ''">
                and vmdsstz = #{vmdsstz}
            </if>
            <if test="vsfft != null and vsfft != ''">
                and vsfft = #{vsfft}
            </if>
            <if test="vcyqy != null and vcyqy != ''">
                and vcyqy = #{vcyqy}
            </if>
        </where>
        ORDER BY convert(`VNAME` using gbk) asc
        limit #{help.pageNum}, #{help.pageSize}
    </select>
    <select id="queryBsWhListCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
        count(1)
        from
        tb_villa
        <where>
            and vsfsc = '0'
            <if test="vname != null and vname != ''">
                and vname like CONCAT('%',#{vname},'%')
            </if>
            <if test="vxz != null and vxz != ''">
                and vxz = #{vxz}
            </if>

            <if test="vmdsstz != null and vmdsstz != ''">
                and vmdsstz = #{vmdsstz}
            </if>
            <if test="vsfft != null and vsfft != ''">
                and vsfft = #{vsfft}
            </if>
            <if test="vcyqy != null and vcyqy != ''">
                and vcyqy = #{vcyqy}
            </if>
        </where>
    </select>

    <!-- 别墅数据导出 -->
    <select id="queryBsListXz" resultType="com.wlgb.entity.vo.TbVillaVo" parameterType="java.util.Map">
        select
        v.*,e.name as zbdz,c.FNAME as fq,j.JNAME as jq
        from
        tb_villa v LEFT JOIN dingding_employee e on v.PID = e.userid LEFT JOIN tb_csfq c on v.FID = c.FID LEFT JOIN
        tb_csjq j on v.JID = j.jid
        <where>
            and vsfsc = '0'
            <if test="vname != null and vname != ''">
                and vname like CONCAT('%',#{vname},'%')
            </if>
            <if test="vxz != null and vxz != ''">
                and vxz = #{vxz}
            </if>
        </where>
        ORDER BY convert(`VNAME` using gbk) asc
    </select>

    <!-- 别墅分区数据列表 -->
    <select id="queryFqWhList" resultType="com.wlgb.entity.vo.TbCsfqVo" parameterType="java.util.Map">
        select
        f.*,c.CNAME as fgsmc
        from
        tb_csfq f LEFT JOIN tb_csfgs c ON f.CID = c.CID
        <where>
            <if test="fname != null and fname != ''">
                and fname like CONCAT('%', #{fname}, '%')
            </if>
            <if test="cid != null and cid != ''">
                and f.cid = #{cid}
            </if>
        </where>
        ORDER BY convert(`FNAME` using gbk) asc
        limit #{help.pageNum}, #{help.pageSize}
    </select>
    <select id="queryFqWhListCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
        count(1)
        from
        tb_csfq
        <where>
            <if test="fname != null and fname != ''">
                and fname like CONCAT('%', #{fname}, '%')
            </if>
            <if test="cid != null and cid != ''">
                and cid = #{cid}
            </if>
        </where>
    </select>

    <!-- 别墅集群数据列表 -->
    <select id="queryJqWhList" parameterType="java.util.Map" resultType="com.wlgb.entity.vo.TbCsjqVo">
        select
        j.*,c.CNAME as fgsmc
        from
        tb_csjq j LEFT JOIN tb_csfgs c ON j.CID = c.CID
        <where>
            <if test="jname != null and jname != ''">
                and JNAME like CONCAT('%', #{jname}, '%')
            </if>
            <if test="cid != null and cid != ''">
                and j.cid = #{cid}
            </if>
        </where>
        ORDER BY convert(`JNAME` using gbk) asc
        limit #{help.pageNum}, #{help.pageSize}
    </select>
    <select id="queryJqWhListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select
        count(1)
        from
        tb_csjq
        <where>
            <if test="jname != null and jname != ''">
                and jname like CONCAT('%', #{jname}, '%')
            </if>
            <if test="cid != null and cid != ''">
                and cid = #{cid}
            </if>
        </where>
    </select>

    <!-- 小红书和抖音下单业绩播报 -->
    <select id="queryXhsAndDyXdYj" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.util.Map">
        SELECT
        xfd,
        bmmc,
        sum( Xzrcsfy + Xdcze + Xskze + xbxzje + Xztze + Xhpsfy + Xhfyj ) AS zje
        FROM
        tb_xyd t
        LEFT JOIN fwq_bbb2 b ON t.XFDSSBM = b.bm
        AND t.xfd = b.xm
        WHERE
        xkhly in
        <foreach collection="ly" item="ly" index="index" open="(" close=")" separator=",">
            #{ly}
        </foreach>
        <if test="type == 1">
            AND DATE_FORMAT( XKHTJTIME, '%Y-%m' ) = DATE_FORMAT( CURDATE(), '%Y-%m' )
        </if>
        <if test="type == 2">
            AND DATE_FORMAT( XKHTJTIME, '%Y-%m-%d' ) = DATE_FORMAT( CURDATE(), '%Y-%m-%d' )
        </if>
        AND xsfsc = 0
        AND bmzx = 2
        GROUP BY
        <if test="grOrBm == 1">
            XFDSSBM
        </if>
        <if test="grOrBm == 2">
            xfd
        </if>
        ORDER BY zje desc
    </select>

    <!-- 小红书和抖音消费业绩播报 -->
    <select id="queryXhsAndDyXFYj" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.util.Map">
        SELECT
        xfd,
        bmmc,
        sum( Xzrcsfy + QDCXF + QSKTC + xbxzje + QCHFW + Xhpsfy + Xhfyj ) AS zje
        FROM
        tb_xyd t
        LEFT JOIN fwq_bbb2 b ON t.XFDSSBM = b.bm
        AND t.xfd = b.xm
        LEFT JOIN tb_qrd q ON t.xid = q.QXYDID
        WHERE
        xkhly in
        <foreach collection="ly" item="ly" index="index" open="(" close=")" separator=",">
            #{ly}
        </foreach>
        xkhly IN ( '80ca5a42c2774390bcc314e02c6a034d', 'a33c818bda9d4793b07fbc92e7a55f2e' )
        AND DATE_FORMAT( QRSJ, '%Y-%m' ) = DATE_FORMAT( CURDATE(), '%Y-%m' )
        AND xsfsc = 0
        AND qsfsc = 0
        AND bmzx = 2
        GROUP BY
        <if test="grOrBm == 1">
            bm
        </if>
        <if test="grOrBm == 2">
            xm
        </if>
        ORDER BY zje desc
    </select>

    <!-- 岗位下拉列表（父岗位） -->
    <select id="queryGwLb" resultType="com.wlgb.config.Select">
        select
            fgwid as value, fgw as text, false as defaultChecked
        from
            hr_gw_city
        where
            sfsc = 0
        group by fgw
        order by gwpx asc
    </select>

    <!-- 根据父岗位id查询岗位列表 -->
    <select id="queryGw" resultType="com.wlgb.config.Select" parameterType="java.lang.String">
        select
            zcityid as value, zcity as text, false as defaultChecked
        from
            hr_gw_city
        where
            sfsc = 0
        and
            fgwid = #{id}
        order by gwpx asc
    </select>

    <!-- 招聘渠道 -->
    <select id="queryQd" resultType="com.wlgb.config.Select">
        select
            id as value, qudao as text, false as defaultChecked
        from
            hr_genderqudao
        order by genderQuDao asc
    </select>

    <!-- 招聘微信 -->
    <select id="queryWx" resultType="com.wlgb.config.Select">
        select
            id as value, weixin as text, false as defaultChecked
        from
            hr_genderweixin
        where sfsc = 0
        order by genderWeiXin asc
    </select>

    <!-- 简历进度 -->
    <select id="queryJlJd" resultType="com.wlgb.config.Select">
        select
            id as value, jljd as text, false as defaultChecked
        from
            hr_jljd
        order by genderjljd asc
    </select>

    <!-- 现有岗位列表 -->
    <select id="queryXyGw" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT * FROM `hr_gw_city`
        <where>
            and sfsc = 0
            <if test="search != null and search != ''">
                and (fgw like concat('%',#{search},'%') or zcity like concat('%',#{search},'%'))
            </if>
        </where>
        order by gwpx
        limit #{help.pageNum}, #{help.pageSize}
    </select>
    <select id="queryXyGwCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT count(1) FROM `hr_gw_city`
        <where>
            and sfsc = 0
            <if test="search != null and search != ''">
                and (fgw like concat('%',#{search},'%') or zcity like concat('%',#{search},'%'))
            </if>
        </where>
    </select>

    <!-- 暂停岗位列表 -->
    <select id="queryZtGw" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT * FROM `hr_gw_city`
        <where>
            and sfsc = 1
            <if test="search != null and search != ''">
                and (fgw like concat('%',#{search},'%') or zcity like concat('%',#{search},'%'))
            </if>
        </where>
        order by gwpx
        limit #{help.pageNum}, #{help.pageSize}
    </select>
    <select id="queryZtGwCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT count(1) FROM `hr_gw_city`
        <where>
            and sfsc = 1
            <if test="search != null and search != ''">
                and (fgw like concat('%',#{search},'%') or zcity like concat('%',#{search},'%'))
            </if>
        </where>
    </select>


    <select id="queryJrDm" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
            count(1)
        from
            hr_msz
        where
            sfsc = '0'
        and
            YYRname = #{name}
        and
            DATE_FORMAT(TJdate,'%Y-%m-%d') = DATE_FORMAT(CURDATE(),'%Y-%m-%d')
    </select>

    <select id="queryJrTg" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
            count(1)
        from
            hr_msz
        where
            sfsc = '0'
        and
            YYRname = #{name}
        and
            DATE_FORMAT(TJdate,'%Y-%m-%d') = DATE_FORMAT(CURDATE(),'%Y-%m-%d')
        and
            mianshi = '已通过'
    </select>


    <select id="queryBmZpQk" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT
        gwbmfzr as fzr,count(1) as msrs,count(if(mianshi = '已通过', true, null)) as
        tgrs,CONCAT(TRUNCATE(IFNULL(count(if(mianshi = '已通过', true, null))/count(1),0)*100,2), '%') as tgl,
        count(if(jljd = '11' or jljd = '10' or jljd = '9' or jljd = '12', true, null)) as dgrs,
        CONCAT(TRUNCATE(IFNULL(count(if(jljd = '11' or jljd = '10' or jljd = '9' or jljd = '12', true,
        null))/count(if(mianshi = '已通过', true, null)),0)*100,2),'%') as dgl,
        count(if(jljd = '12', true, null)) as rzrs, CONCAT(TRUNCATE(IFNULL(count(if(jljd = '12', true,
        null))/count(if(jljd = '11' or jljd = '10' or jljd = '9' or jljd = '12', true, null)),0)*100,2),'%') as rzl,
        count(if(jljd = '8', true, null)) as fqsgrs,CONCAT(TRUNCATE(IFNULL(count(if(jljd = '8', true,
        null))/count(if(mianshi = '已通过', true, null)),0)*100,2),'%') as fqsgl
        FROM
        `hr_msz`
        where
        sfsc = 0
        <if test="start != null">
            and DATE_FORMAT(TJdate, '%Y-%m-%d') >= DATE_FORMAT(#{start}, '%Y-%m-%d')
        </if>
        <if test="end != null">
            and DATE_FORMAT(#{end}, '%Y-%m-%d') >= DATE_FORMAT(TJdate, '%Y-%m-%d')
        </if>
        and
        gwbmfzr is not null and gwbmfzr != ''
        GROUP BY gwbmfzr
        ORDER BY count(1) desc,
        count(if(mianshi = '已通过', true, null)) desc,
        count(if(jljd = '11' or jljd = '10' or jljd = '9' or jljd = '12', true, null)) desc,
        count(if(jljd = '12', true, null)) desc,
        convert(`gwbmfzr` using gbk) asc
    </select>

    <select id="queryBmFqSg" parameterType="java.util.Map" resultType="java.util.Map">
        select
        gwbmfzr as fzr,count(1) as fqsgrs,count(if(fqyyids like CONCAT('%','1','%'), true, null)) as
        kgys,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like CONCAT('%','1','%'), true, null))/(count(if(fqyyids like
        CONCAT('%','1','%'), true, null))+count(if(fqyyids like CONCAT('%','2','%'), true, null))+count(if(fqyyids like
        CONCAT('%','3','%'), true, null))+count(if(fqyyids like CONCAT('%','4','%'), true, null))+count(if(fqyyids like
        CONCAT('%','5','%'), true, null))+count(if(fqyyids like CONCAT('%','6','%'), true, null))+count(if(fqyyids like
        CONCAT('%','7','%'), true, null))),0)*100,2),'%') as kgyszb,
        count(if(fqyyids like CONCAT('%','2','%'), true, null)) as ghdgz,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','2','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        ghdgzzb,
        count(if(fqyyids like CONCAT('%','3','%'), true, null)) as hj,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','3','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        hjzb,
        count(if(fqyyids like CONCAT('%','4','%'), true, null)) as xz,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','4','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        xzzb,
        count(if(fqyyids like CONCAT('%','5','%'), true, null)) as gznr,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','5','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        gznrzb,
        count(if(fqyyids like CONCAT('%','6','%'), true, null)) as jq,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','6','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        jqzb,
        count(if(fqyyids like CONCAT('%','7','%'), true, null)) as qt,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','7','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        qtzb,
        count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like CONCAT('%','2','%'), true,
        null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like CONCAT('%','4','%'), true,
        null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like CONCAT('%','6','%'), true,
        null))+count(if(fqyyids like CONCAT('%','7','%'), true, null)) as fqyyzs
        FROM
        `hr_msz`
        where
        sfsc = 0
        <if test="start != null">
            and DATE_FORMAT(TJdate, '%Y-%m-%d') >= DATE_FORMAT(#{start}, '%Y-%m-%d')
        </if>
        <if test="end != null">
            and DATE_FORMAT(#{end}, '%Y-%m-%d') >= DATE_FORMAT(TJdate, '%Y-%m-%d')
        </if>
        and gwbmfzr is not null and gwbmfzr != ''
        and JLJD = '8' and fqyyids is not null and fqyyids != ''
        GROUP BY gwbmfzr
        ORDER BY count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like CONCAT('%','2','%'),
        true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like CONCAT('%','4','%'),
        true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like CONCAT('%','6','%'),
        true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null)) desc,
        convert(`gwbmfzr` using gbk) asc
    </select>

    <select id="queryBmZpQkByFzr" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT
        #{gwfzr} as fzr,count(1) as msrs,count(if(mianshi = '已通过', true, null)) as
        tgrs,CONCAT(TRUNCATE(IFNULL(count(if(mianshi = '已通过', true, null))/count(1),0)*100,2), '%') as tgl,
        count(if(jljd = '11' or jljd = '10' or jljd = '9' or jljd = '12', true, null)) as dgrs,
        CONCAT(TRUNCATE(IFNULL(count(if(jljd = '11' or jljd = '10' or jljd = '9' or jljd = '12', true,
        null))/count(if(mianshi = '已通过', true, null)),0)*100,2),'%') as dgl,
        count(if(jljd = '12', true, null)) as rzrs, CONCAT(TRUNCATE(IFNULL(count(if(jljd = '12', true,
        null))/count(if(jljd = '11' or jljd = '10' or jljd = '9' or jljd = '12', true, null)),0)*100,2),'%') as rzl,
        count(if(jljd = '8', true, null)) as fqsgrs,CONCAT(TRUNCATE(IFNULL(count(if(jljd = '8', true,
        null))/count(if(mianshi = '已通过', true, null)),0)*100,2),'%') as fqsgl
        FROM
        `hr_msz`
        where
        sfsc = 0
        <if test="start != null">
            and DATE_FORMAT(TJdate, '%Y-%m-%d') >= DATE_FORMAT(#{start}, '%Y-%m-%d')
        </if>
        <if test="end != null">
            and DATE_FORMAT(#{end}, '%Y-%m-%d') >= DATE_FORMAT(TJdate, '%Y-%m-%d')
        </if>
        and
        gwbmfzr is not null and gwbmfzr != ''
        and gwbmfzr = #{gwfzr}
        ORDER BY count(1) desc,
        count(if(mianshi = '已通过', true, null)) desc,
        count(if(jljd = '11' or jljd = '10' or jljd = '9' or jljd = '12', true, null)) desc,
        count(if(jljd = '12', true, null)) desc,
        convert(`gwbmfzr` using gbk) asc
    </select>

    <select id="queryBmFqSgByFzr" parameterType="java.util.Map" resultType="java.util.Map">
        select
        #{gwfzr} as fzr,count(1) as fqsgrs,count(if(fqyyids like CONCAT('%','1','%'), true, null)) as
        kgys,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like CONCAT('%','1','%'), true, null))/(count(if(fqyyids like
        CONCAT('%','1','%'), true, null))+count(if(fqyyids like CONCAT('%','2','%'), true, null))+count(if(fqyyids like
        CONCAT('%','3','%'), true, null))+count(if(fqyyids like CONCAT('%','4','%'), true, null))+count(if(fqyyids like
        CONCAT('%','5','%'), true, null))+count(if(fqyyids like CONCAT('%','6','%'), true, null))+count(if(fqyyids like
        CONCAT('%','7','%'), true, null))),0)*100,2),'%') as kgyszb,
        count(if(fqyyids like CONCAT('%','2','%'), true, null)) as ghdgz,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','2','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        ghdgzzb,
        count(if(fqyyids like CONCAT('%','3','%'), true, null)) as hj,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','3','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        hjzb,
        count(if(fqyyids like CONCAT('%','4','%'), true, null)) as xz,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','4','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        xzzb,
        count(if(fqyyids like CONCAT('%','5','%'), true, null)) as gznr,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','5','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        gznrzb,
        count(if(fqyyids like CONCAT('%','6','%'), true, null)) as jq,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','6','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        jqzb,
        count(if(fqyyids like CONCAT('%','7','%'), true, null)) as qt,CONCAT(TRUNCATE(IFNULL(count(if(fqyyids like
        CONCAT('%','7','%'), true, null))/(count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like
        CONCAT('%','2','%'), true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like
        CONCAT('%','4','%'), true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like
        CONCAT('%','6','%'), true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null))),0)*100,2),'%') as
        qtzb,
        count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like CONCAT('%','2','%'), true,
        null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like CONCAT('%','4','%'), true,
        null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like CONCAT('%','6','%'), true,
        null))+count(if(fqyyids like CONCAT('%','7','%'), true, null)) as fqyyzs
        FROM
        `hr_msz`
        where
        sfsc = 0
        <if test="start != null">
            and DATE_FORMAT(TJdate, '%Y-%m-%d') >= DATE_FORMAT(#{start}, '%Y-%m-%d')
        </if>
        <if test="end != null">
            and DATE_FORMAT(#{end}, '%Y-%m-%d') >= DATE_FORMAT(TJdate, '%Y-%m-%d')
        </if>
        and gwbmfzr is not null and gwbmfzr != ''
        and JLJD = '8' and fqyyids is not null and fqyyids != ''
        and gwbmfzr = #{gwfzr}
        ORDER BY count(if(fqyyids like CONCAT('%','1','%'), true, null))+count(if(fqyyids like CONCAT('%','2','%'),
        true, null))+count(if(fqyyids like CONCAT('%','3','%'), true, null))+count(if(fqyyids like CONCAT('%','4','%'),
        true, null))+count(if(fqyyids like CONCAT('%','5','%'), true, null))+count(if(fqyyids like CONCAT('%','6','%'),
        true, null))+count(if(fqyyids like CONCAT('%','7','%'), true, null)) desc,
        convert(`gwbmfzr` using gbk) asc
    </select>

    <!-- 三天内退场订单 -->
    <select id="queryXydXxByVidThreeDay" parameterType="java.util.Map" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            xddbh,xbsmc,xid,vname,DATE_FORMAT( XJCTIME, '%Y-%m-%d %H:%i' ) AS xjctime,DATE_FORMAT( XTCTIME, '%Y-%m-%d %H:%i' ) AS xtctime,xfd,xzk,xzkdh,xhfyj,xdcze
        FROM
            tb_xyd x
            LEFT JOIN tb_qrd q ON x.xid = q.QXYDID
            LEFT JOIN tb_villa v ON x.xbsmc = v.vid
        WHERE
            q.QRSJ IS NULL
            AND x.XSFSC = 0
            AND DATE_FORMAT( XTCTIME, '%Y-%m-%d' ) >= DATE_FORMAT( CURDATE(), '%Y-%m-%d' )
            AND DATE_FORMAT( DATE_ADD( CURDATE(), INTERVAL #{sj} DAY ), '%Y-%m-%d' ) >= DATE_FORMAT( XTCTIME, '%Y-%m-%d' )
            AND XBSMC = #{vid}
        ORDER BY
            XTCTIME ASC
    </select>

    <!-- 根据区域名称查询区域信息 -->
    <select id="queryCyQyOneByQyName" resultType="com.alibaba.fastjson.JSONObject" parameterType="java.lang.String">
        select * from tb_cyqy where qyname = #{qy}
    </select>

    <!-- 查询超过30天的好评数据 -->
    <select id="queryHpSlCgMonth" resultType="com.wlgb.entity.Hpsl">
        select * from ldt_hpsl
        where sfwc = 0
        and cjtime is not null
        and DATE_FORMAT(cjtime,'%Y-%m-%d') = DATE_FORMAT(DATE_ADD(CURDATE(),INTERVAL -30 day),'%Y-%m-%d')
    </select>

    <!-- 根据钉钉部门id查询部门信息 -->
    <select id="queryDingdingDeparTment" resultType="com.wlgb.config.DingdingDepartment" parameterType="java.lang.String">
        SELECT * FROM dingding_department where dingdingid = #{bmid} limit 1
    </select>

    <insert id="saveDingdingDeparTment" parameterType="com.wlgb.entity.Department">
        insert into dingding_department
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id !=null">
                id,
            </if>
            <if test="dingdingid !=null">
                dingdingid,
            </if>
            <if test="departname !=null">
                departname,
            </if>
            <if test="parentid !=null">
                parentid,
            </if>
            <if test="corpid !=null">
                corpid,
            </if>
            <if test="zgid !=null">
                zgid,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id !=null">
                #{id},
            </if>
            <if test="dingdingid !=null">
                #{dingdingid},
            </if>
            <if test="departname !=null">
                #{departname},
            </if>
            <if test="parentid !=null">
                #{parentid},
            </if>
            <if test="corpid !=null">
                #{corpid},
            </if>
            <if test="zgid !=null">
                #{zgid},
            </if>
        </trim>
    </insert>

    <update id="updateDingdingDeparTment" parameterType="com.wlgb.entity.Department">
        update dingding_department
        <set>
            <if test="id != null">id = #{id},</if>
            <if test="departname != null">departname = #{departname},</if>
            <if test="parentid != null">parentid = #{parentid},</if>
            <if test="corpid != null">corpid = #{corpid},</if>
            <if test="zgid != null">zgid = #{zgid},</if>
        </set>
        where dingdingid = #{dingdingid}
    </update>

    <!-- 校代首单查询 -->
    <select id="queryXdSd" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(1) from tb_xyd where xsfsc = 0 and xzdcl = '4' and XXDDH = #{xddh} and XXDXM = #{xdxm}
    </select>

    <select id="queryWnl" resultType="com.wlgb.entity.FwqWnl" parameterType="java.lang.String" >
        SELECT * FROM fwq_wnl  where
        0=0
        <if test="nian != null and nian != ''">
            and nian = #{nian}
        </if>
        <if test="yue != null and yue != ''">
            and yue = #{yue}
        </if>
    </select>

    <select id="deleteWnlAll" resultType="com.wlgb.entity.FwqWnl" parameterType="java.lang.String" >
        TRUNCATE  fwq_wnl
    </select>


    <insert id="saveFwqXdjcDzmrlc" parameterType="com.wlgb.entity.FwqXdjcDzmrlc">
        insert into fwq_xdjc_dzmrlc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id !=null">
                id,
            </if>
            <if test="userid !=null">
                userid,
            </if>
            <if test="username !=null">
                username,
            </if>
            <if test="time !=null">
                time,
            </if>
            <if test="kaiguan !=null">
                kaiguan,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id !=null">
                #{id},
            </if>
            <if test="userid !=null">
                #{userid},
            </if>
            <if test="username !=null">
                #{username},
            </if>
            <if test="time !=null">
                #{time},
            </if>
            <if test="kaiguan !=null">
                #{kaiguan},
            </if>
        </trim>
    </insert>

    <select id="queryFwqXdjcDzmrlc" resultType="com.wlgb.entity.FwqXdjcDzmrlc" parameterType="java.lang.String" >
        SELECT * FROM fwq_xdjc_dzmrlc  where
        0=0
        <if test="userid != null and userid != ''">
            and userid = #{userid}
        </if>
        <if test="username != null and username != ''">
            and username = #{username}
        </if>

        ORDER BY id DESC
    </select>

    <insert id="saveFwqXdjcDzmrlcFsjl" parameterType="com.wlgb.entity.FwqXdjcDzmrlcFsjl">
        insert into fwq_xdjc_dzmrlc_fsjl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id !=null">
                id,
            </if>
            <if test="fsjg !=null">
                fsjg,
            </if>
            <if test="time !=null">
                time,
            </if>
            <if test="userid !=null">
                userid,
            </if>
            <if test="username !=null">
                username,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id !=null">
                #{id},
            </if>
            <if test="fsjg !=null">
                #{fsjg},
            </if>
            <if test="time !=null">
                #{time},
            </if>
            <if test="userid !=null">
                #{userid},
            </if>
            <if test="username !=null">
                #{username},
            </if>
        </trim>
    </insert>

    <select id="queryFwqXdjcDzmrlcFsjl" resultType="com.wlgb.entity.FwqXdjcDzmrlcFsjl" parameterType="java.lang.String" >
        SELECT * FROM fwq_xdjc_dzmrlc_fsjl where   0=0
        <if test="userid != null and userid != ''">
            and userid = #{userid}
        </if>
        <if test="username != null and username != ''">
            and username = #{username}
        </if>
        <if test="userid != null and userid != ''">
            and userid = #{userid}
        </if>
        <if test="stime != null and stime != ''  and etime != null and etime != ''">
            AND DATE_FORMAT( time, '%Y-%m-%d' ) >= DATE_FORMAT(#{stime}, '%Y-%m-%d' )
            AND DATE_FORMAT( #{etime}, '%Y-%m-%d' ) >= DATE_FORMAT( time, '%Y-%m-%d' )
        </if>
        ORDER BY id DESC
    </select>


</mapper>
