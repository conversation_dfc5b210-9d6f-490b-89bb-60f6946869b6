package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlgb.entity.TbYddAitj;
import com.wlgb.entity.TbYddAitjXx;
import com.wlgb.service2.TbYddAitjService;
import com.wlgb.service2.TbYddAitjXxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/ydd/aitj")
public class YddAiTjController {
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;
    @Autowired
    private TbYddAitjXxService tbYddAitjXxService;
    @Autowired
    private TbYddAitjService tbYddAitjService;

    /**
     * 将AI推荐保存起来
     */
    @RequestMapping(value = "saveaitj")
    public void saveaitj(@RequestBody String jsonBody) {
        try {
            // 解析外部JSON对象
            JSONObject outerJson = JSON.parseObject(jsonBody);
            String innerJsonString = outerJson.getString("data");
            // 去除转义字符
            String unescapedJsonString = innerJsonString.replace("\\\"", "\"");
            // 解析内部JSON数组字符串为TbYddAitjXx对象列表
            List<TbYddAitjXx> questions = JSON.parseArray(unescapedJsonString, TbYddAitjXx.class);
            // 打印结果
            for (TbYddAitjXx question : questions) {
                tbYddAitjXxService.save(question);
                System.out.println(question);
            }
            TbYddAitj tbYddAitj = new TbYddAitj();
            tbYddAitj.setUuid(questions.get(0).getUuid());
            tbYddAitj.setUserid(questions.get(0).getUserid());
            tbYddAitj.setCreateTime(new Date());
            tbYddAitjService.save(tbYddAitj);
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 打印请求信息到控制台
        System.out.println("jsonBody: " + jsonBody);
    }


}
