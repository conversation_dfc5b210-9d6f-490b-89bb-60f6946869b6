package com.wlgb.service3.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.Formitem;
import com.wlgb.mapper.FormitemMapper;
import com.wlgb.service3.FormitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年06月05日 11:16
 */
@Service
@DS("third")
public class FormitemServiceImpl implements FormitemService {
    @Resource
    private FormitemMapper formitemMapper;

    @Override
    public void save(Formitem formitem) {
        formitemMapper.insertSelective(formitem);
    }

    @Override
    public void saveBatch(List<Formitem> list) {
        list.forEach(l -> formitemMapper.insertSelective(l));
    }
}
