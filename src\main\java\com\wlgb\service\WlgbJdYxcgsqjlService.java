package com.wlgb.service;

import com.wlgb.entity.WlgbJdYxcgsqjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/30 10:25
 */
public interface WlgbJdYxcgsqjlService {
    void save(WlgbJdYxcgsqjl wlgbJdYxcgsqjl);

    void updateById(WlgbJdYxcgsqjl wlgbJdYxcgsqjl);

    WlgbJdYxcgsqjl queryBySpBh(String spBh);

    WlgbJdYxcgsqjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd);

    List<WlgbJdYxcgsqjl> queryBySfLrJd(Integer sfLrJd);
}
