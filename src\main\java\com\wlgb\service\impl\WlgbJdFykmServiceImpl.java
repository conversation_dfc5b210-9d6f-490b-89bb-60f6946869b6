package com.wlgb.service.impl;

import com.wlgb.entity.WlgbJdFykm;
import com.wlgb.mapper.WlgbJdFykmMapper;
import com.wlgb.service.WlgbJdFykmService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/16 19:18
 */
@Service
public class WlgbJdFykmServiceImpl implements WlgbJdFykmService {
    @Resource
    private WlgbJdFykmMapper wlgbJdFykmMapper;

    @Override
    public void saveBatch(List<WlgbJdFykm> list) {
        list.forEach(l->{
            wlgbJdFykmMapper.insertSelective(l);
        });
    }
}
