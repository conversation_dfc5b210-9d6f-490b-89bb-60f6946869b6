package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbXsdjbjjl;
import com.wlgb.entity.WlgbXxbjdjjl;
import com.wlgb.mapper.WlgbXxbjdjjlMapper;
import com.wlgb.service.WlgbXxbjdjjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月14日 1:59
 */
@Service
public class WlgbXxbjdjjlServiceImpl implements WlgbXxbjdjjlService {
    @Resource
    private WlgbXxbjdjjlMapper wlgbXxbjdjjlMapper;

    @Override
    public void save(WlgbXxbjdjjl wlgbXxbjdjjl) {
        wlgbXxbjdjjl.setCreateTime(new Date());
        wlgbXxbjdjjl.setId(IdConfig.uuId());
        wlgbXxbjdjjlMapper.insertSelective(wlgbXxbjdjjl);
    }

    @Override
    public void updateById(WlgbXxbjdjjl wlgbXxbjdjjl) {
        wlgbXxbjdjjl.setUpdateTime(new Date());
        wlgbXxbjdjjlMapper.updateByPrimaryKeySelective(wlgbXxbjdjjl);
    }

    @Override
    public List<WlgbXxbjdjjl> queryByXidAndSfSc(String xid) {
        Example example = new Example(WlgbXsdjbjjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xid", xid);
        criteria.andEqualTo("sfsc", 0);
        return wlgbXxbjdjjlMapper.selectByExample(example);
    }
}
