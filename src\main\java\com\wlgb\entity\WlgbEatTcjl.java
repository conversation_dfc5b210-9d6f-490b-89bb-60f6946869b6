package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/13 18:43
 */
@Data
@Table(name = "wlgb_eat_tcjl")
public class WlgbEatTcjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**创建时间*/
    private Date createTime;
    /**修改时间*/
    private Date updateTime;
    /**订单编号*/
    private String ddbh;
    /**套餐编号*/
    private String tcbh;
    /**套餐名称*/
    private String tcmc;
    /**菜品编号*/
    private String tclx;
    /**集群名称*/
    private String jqmc;
    /**集群编号*/
    private String jqbh;
    /**售价*/
    private Double sj;
    /**成本*/
    private Double cb;
    /**毛利*/
    private Double ml;
    /**是否删除(0:否，1:是)*/
    private Integer sfsc;
}
