package com.wlgb.controller;

import cn.hutool.core.date.ChineseDate;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlgb.config.*;
import com.wlgb.entity.FwqWnl;
import com.wlgb.entity.FwqWnlJson;
import com.wlgb.entity.vo.TbVillaVo;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.FwqWnlJsonService;
import com.wlgb.service2.FwqWnlService;
import com.wlgb.service2.WeiLianService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

@RestController
@RequestMapping(value = "/fwq/zion")
public class FwqWNL {
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private FwqWnlService fwqWnlService;
    @Autowired
    private FwqWnlJsonService fwqWnlJsonService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;


    /**
     * 将英文星期转换为中文
     */
    private static String getChineseWeekday(DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case MONDAY:
                return "一";
            case TUESDAY:
                return "二";
            case WEDNESDAY:
                return "三";
            case THURSDAY:
                return "四";
            case FRIDAY:
                return "五";
            case SATURDAY:
                return "六";
            case SUNDAY:
                return "日";
            default:
                throw new IllegalArgumentException("Invalid DayOfWeek");
        }
    }

    /**
     * 判断某个日期是否存在实体中
     *
     * @param ljsonList
     * @param numberToFind
     * @return
     */
    public Optional<FwqWnlJson> findEntityInList(List<FwqWnlJson> ljsonList, String numberToFind) {
        return ljsonList.stream()
                .filter(entity -> entity.getRq().equals(numberToFind))
                .findFirst();
    }


    /**
     * 生成万年历
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "scwnl")
    public Result scwnl(HttpServletRequest request) {
        fwqWnlService.deleteWnlAll();
        List<FwqWnlJson> ljsonList = fwqWnlJsonService.selectAll();
        LocalDate startDate = LocalDate.of(2020, 1, 1);
        LocalDate endDate = LocalDate.of(2025, 1, 1);
        // 生成日期范围内的每一天
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            FwqWnl fwqWnl = new FwqWnl();
            Date today = Date.from(date.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant());
            Integer days = Integer.parseInt(String.valueOf(date).replaceAll("-", ""));
            fwqWnl.setRq(today);
            fwqWnl.setXq(getChineseWeekday(date.getDayOfWeek()));
            // 这里需要补充农历和节日信息，可以通过外部API或数据库查询
            ChineseDate chineseDate = new ChineseDate(today);
            fwqWnl.setNl(chineseDate.getChineseDay());
            fwqWnl.setJr(chineseDate.getFestivals());
            Optional<FwqWnlJson> result = findEntityInList(ljsonList, days + "");
            // 如果存在，获取实体类实例
            if (result.isPresent()) {
                FwqWnlJson foundEntity = result.get();
                // 处理找到的实体
                fwqWnl.setXj(foundEntity.getSftx());
            } else {
                // 如果不存在，相当于返回false
                fwqWnl.setXj("");
            }
            fwqWnl.setNian(date.getYear() + "");
            fwqWnl.setYue(date.getMonthValue() + "");
            fwqWnl.setRi(date.getDayOfMonth() + "");
            fwqWnl.setPaixu(days);
            fwqWnlService.save(fwqWnl);
        }
        return Result.OK();
    }

    @RequestMapping(value = "ckwnl")
    public Result ckwnl(HttpServletRequest request) {
        String nian = request.getParameter("nian");
        String yue = request.getParameter("yue");
        List<FwqWnl> l = weiLianService.queryWnl(nian, yue);
        return Result.OK(l);
    }

    @RequestMapping(value = "scwnljson")
    public Result scwnljson(HttpServletRequest request) {
        String wjm = request.getParameter("wjm");
        HashMap<String, Boolean> map = new HashMap<>();
        // JSON文件路径
        String filePath = FileConfig.getFileAbsolutePath2("static" + File.separator + "wnljson" + File.separator + wjm + ".json");
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            StringBuilder contentBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                contentBuilder.append(line);
            }
            String jsonStr = contentBuilder.toString(); // 从文件读取的JSON字符串
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            // 获取holiday对象
            JSONObject holidayObject = jsonObject.getJSONObject("holiday");
            if (holidayObject != null) {
                FwqWnlJson fileJson = new FwqWnlJson();
                // 遍历holiday对象的每一个条目
                for (String key : holidayObject.keySet()) {
                    JSONObject holidayItem = holidayObject.getJSONObject(key);
                    // 获取并打印date和holiday字段
                    String date = holidayItem.getString("date").replaceAll("-", "");
                    boolean isHoliday = holidayItem.getBoolean("holiday");
                    fileJson.setRq(date);
                    fileJson.setSftx(isHoliday ? "休" : "班");
                    fwqWnlJsonService.save(fileJson);
                }
            } else {
                System.out.println("holiday对象不存在");
            }
        } catch (IOException e) {
            e.printStackTrace();
            System.err.println("读取文件时发生错误");
        }
        return Result.OK();
    }

    @RequestMapping(value = "test")
    public Result test() throws Exception {
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("测试");
        //获取钉钉key
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = DingToken.token(dingkey);
        List<String> list2 = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("textField_lz6de2y1", "0000001" + i);
            jsonObject1.put("employeeField_lz6de2y2", "15349026426046931");

            list2.add(jsonObject1.toJSONString());
        }
        GatewayResult gatewayResult = DingBdLcConfig.plxzBdSl(token, ydAppkey, list2, "15349026426046931", "FORM-2C7D3956D1234BB298680F55C735F8D6EF7S");
        int qmsum = Integer.parseInt(gatewayResult.getResult());
        System.out.println(qmsum);
        return Result.OK(gatewayResult);
    }

}
