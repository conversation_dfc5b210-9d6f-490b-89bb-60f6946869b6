package com.wlgb.service.impl;

import com.wlgb.entity.WlgbHxyhDzjl;
import com.wlgb.mapper.WlgbHxyhDzjlMapper;
import com.wlgb.service.WlgbHxyhDzjlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月03日 23:52
 */
@Service
@Slf4j
public class WlgbHxyhDzjlServiceImpl implements WlgbHxyhDzjlService {
    @Resource
    private WlgbHxyhDzjlMapper wlgbHxyhDzjlMapper;

    @Override
    public void save(WlgbHxyhDzjl wlgbHxyhDzjl) {
        wlgbHxyhDzjl.setCreateTime(new Date());
        boolean cfTest = false;
        try {
            wlgbHxyhDzjlMapper.insertSelective(wlgbHxyhDzjl);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbHxyhDzjlMapper.insertSelective(wlgbHxyhDzjl);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，insert：参数" + wlgbHxyhDzjl.toString());
                    }
                }
            }
        }
    }

    @Override
    public void updateById(WlgbHxyhDzjl wlgbHxyhDzjl) {
        wlgbHxyhDzjl.setUpdateTime(new Date());
        boolean cfTest = false;
        try {
            wlgbHxyhDzjlMapper.updateByPrimaryKeySelective(wlgbHxyhDzjl);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbHxyhDzjlMapper.updateByPrimaryKeySelective(wlgbHxyhDzjl);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，update：参数" + wlgbHxyhDzjl.toString());
                    }
                }
            }
        }
    }

    @Override
    public WlgbHxyhDzjl queryByYsBhAndLsBh(String ysBh, String lsBh) {
        Example example = new Example(WlgbHxyhDzjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tradeNo", ysBh);
        criteria.andEqualTo("outTradeNo", lsBh);
        return wlgbHxyhDzjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbHxyhDzjl> queryBdj() {
        Example example = new Example(WlgbHxyhDzjl.class);
        Example.Criteria criteria = example.createCriteria();
//        criteria.andCondition()
        criteria.andCondition("sflrjd = 0");
        return wlgbHxyhDzjlMapper.selectByExample(example);
    }

    @Override
    public List<WlgbHxyhDzjl> queryListByWlgbHxyhDzjl(WlgbHxyhDzjl wlgbHxyhDzjl) {
        return wlgbHxyhDzjlMapper.select(wlgbHxyhDzjl);
    }

    @Override
    public WlgbHxyhDzjl queryById(String id) {
        return wlgbHxyhDzjlMapper.selectByPrimaryKey(id);
    }
}
