package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.Bjjl;
import com.wlgb.mapper.BjjlMapper;
import com.wlgb.service2.BjjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/26 18:23
 */
@Service
@DS(value = "second")
public class BjjlServiceImpl implements BjjlService {
    @Resource
    private BjjlMapper bjjlMapper;

    @Override
    public void save(Bjjl bjjl) {
        bjjlMapper.insertSelective(bjjl);
    }
}
