package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdBxspjl;
import com.wlgb.mapper.WlgbJdBxspjlMapper;
import com.wlgb.service.WlgbJdBxspjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 20:50
 */
@Service
public class WlgbJdBxspjlServiceImpl implements WlgbJdBxspjlService {
    @Resource
    private WlgbJdBxspjlMapper wlgbJdBxspjlMapper;

    @Override
    public void save(WlgbJdBxspjl wlgbJdBxspjl) {
        wlgbJdBxspjl.setId(IdConfig.uuId());
        wlgbJdBxspjl.setCreateTime(new Date());
        wlgbJdBxspjlMapper.insertSelective(wlgbJdBxspjl);
    }

    @Override
    public void updateById(WlgbJdBxspjl wlgbJdBxspjl) {
        wlgbJdBxspjl.setUpdateTime(new Date());
        wlgbJdBxspjlMapper.updateByPrimaryKeySelective(wlgbJdBxspjl);
    }

    @Override
    public WlgbJdBxspjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd) {
        Example example = new Example(WlgbJdBxspjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdBxspjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdBxspjl> queryListByWlgbJdBxspjl(WlgbJdBxspjl wlgbJdBxspjl) {
        return wlgbJdBxspjlMapper.select(wlgbJdBxspjl);
    }
}
