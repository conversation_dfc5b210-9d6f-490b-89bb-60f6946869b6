package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月17日 20:40
 */
@Data
@Table(name = "wlgb_jd_djbbd")
public class WlgbJdDjbbd {
    /**
     * 主键
     */
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
    /**
     * 创建人
     */
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
    /**
     * 更新人
     */
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    private java.lang.String sysOrgCode;
    /**
     * 流水号
     */
    private java.lang.String lsh;
    /**
     * 金额
     */
    private java.lang.Double je;
    /**
     * 收款编号
     */
    private java.lang.String skbh;
    /**
     * CRM编号
     */
    private java.lang.String crmbh;
    /**
     * 客户电话
     */
    private java.lang.String khdh;
    /**
     * 发起人
     */
    private java.lang.String fqrName;
    /**
     * 发起人id
     */
    private java.lang.String fqrId;
    /**
     * 发起时间
     */
    private java.util.Date fqsj;
    /**
     * 定金类型(1:线下；2：线上)
     */
    private java.lang.Integer djlx;
    /**
     * 二维码
     */
    private java.lang.String ewm;
    /**
     * 是否到账(1:是；0：否)
     */
    private java.lang.Integer sfdz;
    /**
     * 到账时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private java.util.Date dzsj;
    /**
     * 所属平台(1:新美大；2：平台/短租)
     */
    private java.lang.Integer sspt;
    /**
     * 美团门店
     */
    private java.lang.String mtmd;
    /**
     * 获取券码
     */
    private java.lang.String hqqm;
    /**
     * 验券是否完成（1：是；0：否）
     */
    private java.lang.Integer yqsfwc;
    /**
     * 验券结果（1:成功；2：失败）
     */
    private java.lang.Integer yqjg;
    /**
     * 验券券码
     */
    private java.lang.String yqqm;
    /**
     * 验券时间（平台输入买券时间）
     */
    private java.util.Date yqsj;
    /**
     * 代码
     */
    private java.lang.String dm;
    /**
     * 券码数量
     */
    private java.lang.Integer qmsj;
    /**
     * 佣金
     */
    private java.lang.Double yj;
    /**
     * 验券人
     */
    private java.lang.String yqr;
    /**
     * 是否退款（1：是；0：否）
     */
    private java.lang.Integer sftk;
    /**
     * 短租是否回款（1：是；0：否）
     */
    private java.lang.Integer sfhk;
    /**
     * 是否刷单定金（1：是；0：否）
     */
    private java.lang.Integer sfsddj;
    /**
     * 是否删除定金（1：是；0：否）
     */
    private java.lang.Integer sfscdj;
    /**
     * 是否下单（1：是；0：否）
     */
    private java.lang.Integer sfxd;
    /**
     * 是否对公转账（1：是；0：否）
     */
    private java.lang.Integer sfdgzz;
    /**
     * 是否删除（1：是；0：否）
     */
    private java.lang.Integer sfsc;
    /**
     * 已退金额
     */
    private java.lang.Double ytje;
    /**
     * 订单编号
     */
    private java.lang.String ddbh;
    /**
     * 表单实例id
     */
    @Column(name = "form_inst_id")
    private java.lang.String formInstId;
    /**
     * 身份证
     */
    private String sfz;
    /**
     * 下单时间
     */
    private java.util.Date xdsj;
    /**
     * 是否下预留单  1是  0否
     */
    private java.lang.Integer sfxyld;
    /**
     * 是否补交定金尾款  1是  0否
     */
    private java.lang.Integer sfbjdjwk;
    /**
     * 是否补交定金  1是  0否
     */
    private java.lang.Integer sfbjdj;
    /**
     * 删除时间
     */
    private java.util.Date scsj;
    /**
     * 删除人
     */
    private java.lang.String scr;
    /**
     * 删除人id
     */
    private java.lang.String scrid;
    /**
     * 是否录入金蝶
     */
    private java.lang.Integer sflrjd;
    /**
     * 回款金额
     */
    private java.lang.Double hkje;
    /**
     * 已回款金额
     */
    private java.lang.Double yhkje;
    /**
     * 定金唯一标识
     */
    private java.lang.String djwybs;
}
