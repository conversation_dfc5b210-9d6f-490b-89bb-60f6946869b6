package com.wlgb.service3.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.Oapiworkrecord;
import com.wlgb.mapper.OapiworkrecordMapper;
import com.wlgb.service3.OapiworkrecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年06月05日 11:20
 */
@Service
@DS("third")
public class OapiworkrecordServiceImpl implements OapiworkrecordService {
    @Resource
    private OapiworkrecordMapper oapiworkrecordMapper;

    @Override
    public void save(Oapiworkrecord oapiworkrecord) {
        oapiworkrecordMapper.insertSelective(oapiworkrecord);
    }
}
