package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "fwq_thb_d9c")
public class FwqThbD9c {
    @Id
    @KeySql(useGeneratedKeys = true)
    private int id;
    private String qy;
    private String city;
    private String xm;
    private Date sstime;
    private Double dyts;
    private Double xhsts;
    private Double dtts;
    private Double mrtjhys;
    private String vid;
    private String vname;
    private String wybs;
    private String slid;
    private String hptype;
    private String lcnum;
    private String sfwc;
    private String sprid;
}
