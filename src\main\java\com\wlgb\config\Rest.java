package com.wlgb.config;

import lombok.Data;

import java.util.List;

@Data
public class Rest {
    private Content content;
    private Boolean success;
    private String errorMsg;
    private String errorCode;
    private String errorLevel;

    public static Rest success(List list) {
        Rest rest = new Rest();
        Content content = new Content();
        content.setData(list);
        content.setTotalCount(list == null ? 0 : list.size());
        content.setCurrentPage(1);
        rest.content = content;
        rest.success = true;
        rest.setErrorCode("");
        rest.setErrorMsg("");
        rest.setErrorLevel("");
        return rest;
    }
}