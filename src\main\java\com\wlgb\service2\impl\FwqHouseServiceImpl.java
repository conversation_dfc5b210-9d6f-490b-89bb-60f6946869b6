package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqSmile;
import com.wlgb.entity.beike.FwqHouse;
import com.wlgb.mapper.FwqHouseMapper;
import com.wlgb.mapper.FwqSmileMapper;
import com.wlgb.service2.FwqHouseService;
import com.wlgb.service2.FwqSmileService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@DS(value = "second")
public class FwqHouseServiceImpl implements FwqHouseService {
    @Resource
    private FwqHouseMapper fwqHouseMapper;

    @Override
    public void save(FwqHouse fwqHouse) {
        fwqHouseMapper.insertSelective(fwqHouse);
    }

    @Override
    public List<FwqHouse> selectAll(String title) {
        Example example = new Example(FwqHouse.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("title", title);
        return fwqHouseMapper.selectByExample(example);
    }
}
