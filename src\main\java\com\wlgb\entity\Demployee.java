package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: dingding_employee
 */
@Data
@Table(name = "dingding_employee")
public class Demployee {
    @Id
    @KeySql(useGeneratedKeys = true)
    /**钉钉对应的id*/
    private String userid;
    /**
     * 钉钉姓名
     */
    private String name;
    /**
     * 部门id 如果员工属于多个部门则用|隔开
     */
    private String departid;
    /**
     * active
     */
    private Integer active;
    /**
     * avatar
     */
    private String avatar;
    /**
     * position
     */
    private String position;
    /**
     * mobile
     */
    private String mobile;
    /**
     * tel
     */
    private String tel;
    /**
     * workplace
     */
    private String workplace;
    /**
     * remark
     */
    private String remark;
    /**
     * email
     */
    private String email;
    /**
     * jobnumber
     */
    private String jobnumber;
    /**
     * dingid
     */
    private String dingid;
}
