package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.vo.FwqBbb;
import com.wlgb.mapper.FwqBbbMapper;
import com.wlgb.service2.FwqBbbService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/16 16:21
 */
@Service
@DS(value = "second")
public class FwqBbbServiceImpl implements FwqBbbService {
    @Resource
    private FwqBbbMapper fwqBbbMapper;

    @Override
    public void save(FwqBbb fwqBbb) {
        fwqBbbMapper.insertSelective(fwqBbb);
    }
}
