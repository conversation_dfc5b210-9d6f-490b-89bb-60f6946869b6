package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbKdj;
import com.wlgb.mapper.TbKdjMapper;
import com.wlgb.service2.TbKdjService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27 15:42
 */
@Service
@DS(value = "second")
public class TbKdjServiceImpl implements TbKdjService {
    @Resource
    private TbKdjMapper tbKdjMapper;

    @Override
    public void save(TbKdj tbKdj) {
        tbKdjMapper.insertSelective(tbKdj);
    }

    @Override
    public void updateById(TbKdj tbKdj) {
        tbKdjMapper.updateByPrimaryKeySelective(tbKdj);
    }

    @Override
    public TbKdj queryByBsAndCcAndRq(String bs, String cc, String rq) {
        Example example = new Example(TbKdj.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("vid", bs);
        criteria.andEqualTo("cc", cc);
        criteria.andEqualTo("rq", rq);
        return tbKdjMapper.selectOneByExample(example);
    }

    @Override
    public TbKdj queryByBsAndCcAndRqAndJcAndTc(String bs, String cc, String rq, Date jc, Date tc) {
        Example example = new Example(TbKdj.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("vid", bs);
        criteria.andEqualTo("cc", cc);
        criteria.andEqualTo("rq", rq);
        criteria.andEqualTo("xjctime", jc);
        criteria.andEqualTo("xtctime", tc);
        return tbKdjMapper.selectOneByExample(example);
    }

    @Override
    public TbKdj queryByBsAndRqAndJcAndTc(String bs, String rq, Date jc, Date tc) {
        Example example = new Example(TbKdj.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("vid", bs);
        criteria.andEqualTo("rq", rq);
        criteria.andEqualTo("xjctime", jc);
        criteria.andEqualTo("xtctime", tc);
        return tbKdjMapper.selectOneByExample(example);
    }
}
