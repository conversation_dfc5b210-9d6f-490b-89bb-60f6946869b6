package com.wlgb.config;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiChatSendRequest;
import com.dingtalk.api.response.OapiChatSendResponse;
import com.taobao.api.ApiException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/1 10:40
 */
public class DingQunSend {

    public static void sendQun(Dingkey dingkey, String mediaId, String chatId) throws ApiException {
//        send(text, dingkey, chatId);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/chat/send");
        OapiChatSendRequest req = new OapiChatSendRequest();
        req.setChatid(chatId);
        OapiChatSendRequest.Msg msg = new OapiChatSendRequest.Msg();
        msg.setMsgtype("image");
        OapiChatSendRequest.Image obj1 = new OapiChatSendRequest.Image();
        obj1.setMediaId(mediaId);
        msg.setImage(obj1);
        req.setMsg(msg);
        OapiChatSendResponse rsp = client.execute(req, DingToken.token(dingkey));
        System.out.println(rsp.getBody());
    }

    public static void sendQunDown(Dingkey dingkey, String text, String chatId, String title, String url) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/chat/send");
        OapiChatSendRequest req = new OapiChatSendRequest();
        req.setChatid(chatId);
        OapiChatSendRequest.Msg msg = new OapiChatSendRequest.Msg();
        OapiChatSendRequest.Link link = new OapiChatSendRequest.Link();
        OapiChatSendRequest.ActionCard actionCard = new OapiChatSendRequest.ActionCard();
        actionCard.setSingleTitle(title);
        actionCard.setTitle(title);
        actionCard.setSingleUrl(url);
//        actionCard.setAgentid(dingkey.getAgentId());
        OapiChatSendRequest.Markdown markdown = new OapiChatSendRequest.Markdown();
        markdown.setText(text);
        markdown.setTitle(title);
        actionCard.setMarkdown(text);
//        link.setTitle(title);
//        link.setText(text);
//        link.setPicUrl("https://gimg2.baidu.com/image_search/src=http%3A%2F%2F5b0988e595225.cdn.sohucs.com%2Fimages%2F20171122%2F3bba276396374b9f8dacde86cbcff89d.jpeg&refer=http%3A%2F%2F5b0988e595225.cdn.sohucs.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1654083172&t=fe91bf8988503bd9e2c004c4a2b49e0f");
//        link.setMessageUrl("dingtalk://dingtalkclient/page/link?url=" + url);
        msg.setActionCard(actionCard);
//        msg.setLink(link);
//        msg.setActionCard(actionCard);
//        msg.setMarkdown(markdown);
        msg.setMsgtype("action_card");
        req.setMsg(msg);
        OapiChatSendResponse rsp = client.execute(req, DingToken.token(dingkey));
        System.out.println(rsp.getBody());
    }

    public static void sendMarkDown(Dingkey dingkey, String text, String chatId, String title, String url) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/chat/send");
        OapiChatSendRequest req = new OapiChatSendRequest();
        req.setChatid(chatId);
        OapiChatSendRequest.Msg msg = new OapiChatSendRequest.Msg();
//        OapiChatSendRequest.Link link = new OapiChatSendRequest.Link();
//        OapiChatSendRequest.ActionCard actionCard = new OapiChatSendRequest.ActionCard();
//        actionCard.setSingleTitle(title);
//        actionCard.setTitle(title);
//        actionCard.setSingleUrl(url);
//        actionCard.setAgentid(dingkey.getAgentId());
        OapiChatSendRequest.Markdown markdown = new OapiChatSendRequest.Markdown();
        markdown.setText(text);
        markdown.setTitle(title);
        msg.setMarkdown(markdown);
        msg.setMsgtype("markdown");
        req.setMsg(msg);
        OapiChatSendResponse rsp = client.execute(req, DingToken.token(dingkey));
        System.out.println(rsp.getBody());
    }

    public static void sendQun1(Dingkey dingkey, String mediaId, String chatId) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/chat/send");
        OapiChatSendRequest req = new OapiChatSendRequest();
        req.setChatid(chatId);
        OapiChatSendRequest.Msg msg = new OapiChatSendRequest.Msg();
        msg.setMsgtype("image");
        OapiChatSendRequest.Image obj1 = new OapiChatSendRequest.Image();
        obj1.setMediaId(mediaId);
        msg.setImage(obj1);
        req.setMsg(msg);
        OapiChatSendResponse rsp = client.execute(req, DingToken.token(dingkey));
        System.out.println(rsp.getBody());
    }

    public static void send(String content, Dingkey dingkey, String chatId) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/chat/send");
        OapiChatSendRequest req = new OapiChatSendRequest();
        req.setChatid(chatId);
        OapiChatSendRequest.Msg msg = new OapiChatSendRequest.Msg();
        msg.setMsgtype("text");
        OapiChatSendRequest.Text text = new OapiChatSendRequest.Text();
        text.setContent(content);
        msg.setText(text);
        req.setMsg(msg);
        OapiChatSendResponse rsp = client.execute(req, DingToken.token(dingkey));
        System.out.println(rsp.getBody());
    }
}
