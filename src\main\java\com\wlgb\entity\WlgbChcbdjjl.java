package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/1 20:23
 */
@Data
@Table(name = "wlgb_chcbdjjl")
public class WlgbChcbdjjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**实例id*/
    private String slid;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**创建人*/
    private String creater;
    /**修改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**修改人*/
    private String updater;
    /**删除时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date deleteTime;
    /**删除人*/
    private String deleter;
    /**采购员*/
    private String cgy;
    /**采购员id*/
    private String cgyid;
    /**订单编号*/
    private String ddbh;
    /**别墅id*/
    private String bsid;
    /**别墅名称*/
    private String bsmc;
    /**执行日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date zxtime;
    /**性质*/
    private String xz;
    /**谁制作(制作类型)*/
    private String zztype;
    /**城市*/
    private String city;
    /**场次*/
    private String cc;
    /**是否周末*/
    private String sfzm;
    /**部门*/
    private String bm;
    /**部门id*/
    private String bmid;
    /**轰趴顾问*/
    private String hpgw;
    /**轰趴顾问id*/
    private String hpgwid;
    /**店长id*/
    private String dz;
    /**店长id*/
    private String dzid;
    /**策划金额收入*/
    private Double chjesr;
    /**成本*/
    private Double cb;
    /**利润*/
    private Double lr;
    /**供应商*/
    private String gys;
    /**是否删除(0:否，1:是)*/
    private Integer sfsc;
}
