package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.WlgbYlxd;
import com.wlgb.mapper.WlgbYlxdMapper;
import com.wlgb.service2.WlgbYlxdService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月13日 23:14
 */
@Service
@DS(value = "second")
public class WlgbYlxdServiceImpl implements WlgbYlxdService {
    @Resource
    private WlgbYlxdMapper wlgbYlxdMapper;

    @Override
    public WlgbYlxd queryByDdBhAndSfSc(String ddbh, Integer sfsc) {
        Example example = new Example(WlgbYlxd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xddbh", ddbh);
        criteria.andEqualTo("sfsc", sfsc);
        return wlgbYlxdMapper.selectOneByExample(example);
    }

    @Override
    public void save(WlgbYlxd wlgbYlxd) {
        wlgbYlxd.setCreateTime(new Date());
        wlgbYlxdMapper.insertSelective(wlgbYlxd);
    }

    @Override
    public void updateById(WlgbYlxd wlgbYlxd) {
        wlgbYlxd.setUpdateTime(new Date());
        wlgbYlxdMapper.updateByPrimaryKeySelective(wlgbYlxd);
    }

    @Override
    public List<WlgbYlxd> list() {
        Example example = new Example(WlgbYlxd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sfsc", 0);
        return wlgbYlxdMapper.selectByExample(example);
    }

    @Override
    public WlgbYlxd queryByXid(String xid) {
        Example example = new Example(WlgbYlxd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xid", xid);
        return wlgbYlxdMapper.selectOneByExample(example);
    }
}
