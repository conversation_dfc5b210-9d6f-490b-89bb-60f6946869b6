package com.wlgb.controller;

import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.service.WlgbJdYsdCdfjlService;
import com.wlgb.service2.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import java.util.List;

import static com.wlgb.config.Tools.isEmpty;

@RestController
@RequestMapping(value = "/wlgb/qrd")
public class QrdController {
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private TbQrdService tbQrdService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;
    @Autowired
    private WlgbJdYsdCdfjlService wlgbJdYsdCdfjlService;

    /**
     * 获取确认单详情，包括结算单图片
     */
    @RequestMapping(value = "getqrd")
    public Result getqrd(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        if (isEmpty(ddbh)) {
            return Result.error("订单编号不能为空");
        }
        TbXyd tbXyd = tbXydService.queryByDdBh(ddbh);
        if (tbXyd == null) {
            return Result.error("订单不存在");
        }
        TbQrd tbQrd = tbQrdService.queryQxydIdAndQsfSc(tbXyd.getXid());
        //处理结算单图片
        String jsdurl = tbQrd.getQimagepath();
        System.out.println(jsdurl + "++++++++++++++++++++++++++++++");
        return Result.OK(tbQrd);
    }

    /**
     * 查询已经上传金蝶的确认单数据
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "getscjdqrd")
    public Result getscjdqrd(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        List<WlgbJdYsdCdfjl> list = wlgbJdYsdCdfjlService.queryListByWlgbJdYsdCdfjl(ddbh);
        return Result.OK(list);
    }


}
