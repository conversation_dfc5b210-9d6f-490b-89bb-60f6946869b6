package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "fwq_dy_token")
public class FwqDyToken {
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
    private java.util.Date createTime;
    private java.lang.String token;
    private java.util.Date sxTime;
    private java.lang.String accountid;

}
