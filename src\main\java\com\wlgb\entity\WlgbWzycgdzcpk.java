package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 物资一线固定资产盘库表
 * @Author: jeecg-boot
 * @Date:   2021-09-07
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_wzycgdzcpk")
public class WlgbWzycgdzcpk {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**固定资产id*/
    private java.lang.String gdzcid;
	/**别墅名称*/
    private java.lang.String bsmc;
	/**别墅id*/
    private java.lang.String bsid;
	/**商品名称*/
    private java.lang.String wpmc;
	/**库存数量*/
    private java.lang.Integer kcsl;
	/**实盘数量*/
    private java.lang.Integer spsl;
	/**备注*/
    private java.lang.String bz;
	/**是否赔偿(否,是)*/
    private java.lang.String sfpc;
	/**是否生成赔偿清单(否，是)*/
    private java.lang.String sfscqd;
	/**盘库人名字*/
    private java.lang.String pkrname;
	/**盘库人id*/
    private java.lang.String pkrid;
	/**是否删除(0:否,1:是)*/
    private java.lang.Integer sfsc;

    /**盘点时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date pdtime;

    //数量
    private java.lang.Double cgnum;
    //备用数量
    private java.lang.Integer bysl;

    /**单价*/
    private java.lang.Double dj;
    /**单位*/
    private java.lang.String dw;

    /**负责人*/
    private java.lang.String fzr;

    /**负责人id*/
    private java.lang.String fzrid;

    private java.lang.String lsh;

    private java.lang.String syqy;

}
