package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 商品明细表
 * @Author: jeecg-boot
 * @Date:   2021-05-11
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_product")
public class WlgbProduct {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**商品名称*/
    private java.lang.String name;
	/**商品零售价*/
    private java.math.BigDecimal price;
	/**优惠前价格*/
    private java.math.BigDecimal yhqjg;
	/**成本价*/
    private java.math.BigDecimal cbj;
	/**备注*/
    private java.lang.String ms;
	/**商品图片*/
    private java.lang.String img;
	/**规格*/
    private java.lang.String gg;
	/**类型id*/
    private java.lang.String type;
	/**区域id*/
    private java.lang.String bm;
	/**库存*/
    private java.lang.Integer kc;
	/**是否删除（0：否，1：是）*/
    private java.lang.Integer sfsc;
    /**商品编号*/
    private java.lang.String spbh;
    /**条形码*/
    private java.lang.String txm;
    /**品牌*/
    private java.lang.String pp;
}
