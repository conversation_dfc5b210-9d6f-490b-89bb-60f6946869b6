package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: tb_bslsh
 * @Author: jeecg-boot
 * @Date:   2021-10-10
 * @Version: V1.0
 */
@Data
@Table(name = "tb_bslsh")
public class TbBslsh {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
	/**别墅id*/
    private java.lang.String vid;
	/**别墅临时表，用于查询客单价*/
    private java.lang.String bz;
}
