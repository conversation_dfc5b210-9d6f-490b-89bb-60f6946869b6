package com.wlgb.config;

import com.wlgb.entity.TbVilla;
import com.wlgb.entity.TbXyd;
import com.wlgb.entity.WlgbDksljl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 进场及消费配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/01/10 08:46
 */
public class JcJXfLcConfig extends LcConfig {

    /**
     * 进场及消费流程发起
     *
     * @param l                协议单对象
     * @param villa            别墅
     * @param xydUrl           协议单图片地址
     * @param dingkey          钉钉小程序配置
     * @param employee         值班店长
     * @param ydAppkey         宜搭原因配置
     * @param ydBd             宜搭表单
     * @param dingdingEmployee 房东
     * @param wlgbDksljl1      原有流程
     * @return 宜搭返回
     */
    public static WlgbDksljl jcJXfLc(TbXyd l,
                                     TbVilla villa,
                                     String xydUrl,
                                     Dingkey dingkey,
                                     DingdingEmployee employee,
                                     Yd<PERSON><PERSON><PERSON> ydAppkey,
                                     YdBd ydBd,
                                     DingdingEmployee dingdingEmployee,
                                     WlgbDksljl wlgbDksljl1, String sfydd) {
        if (wlgbDksljl1 == null) {
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Map<String, Object> formDatamap = new HashMap<>();

            //是否预定单
            formDatamap.put("sfydd", sfydd);

            //是否使用未消费的定金
            formDatamap.put("xsfsywxfwdddje", l.getXsfsywxfwdddje() != null ? l.getXsfsywxfwdddje() : 0);
            //未消费定金的订单编号
            formDatamap.put("xwsywdddbh", l.getXwsywdddbh() != null ? l.getXwsywdddbh() : "");
            //未消费定金的金额
            formDatamap.put("xwsywdddsyje", l.getXwsywdddsyje() != null ? l.getXwsywdddsyje() : 0);

            //TODO 包服务开
            formDatamap.put("xsfbdf", l.getXsfbdf());
            formDatamap.put("xdfje", l.getXdfje());
            formDatamap.put("xsfbc", l.getXsfbc());
            formDatamap.put("xcf", l.getXcf());
            formDatamap.put("xsfbch", l.getXsfbch());
            formDatamap.put("xchje", l.getXchje());
            formDatamap.put("xsfbcc", l.getXsfbcc());
            formDatamap.put("xbcje", l.getXbcje());
            formDatamap.put("xsfbwsf", l.getXsfbwsf());
            formDatamap.put("xwsfje", l.getXwsfje());
            formDatamap.put("xsfbcfsyf", l.getXsfbcfsyf());
            formDatamap.put("xcfsyfje", l.getXcfsyfje());
            //TODO 包服务关

            // TODO 有关剧本杀（如果不需要上线要注释）
            formDatamap.put("xjbszje", l.getXjbszje() != null ? l.getXjbszje() : 0);
            // TODO 有关剧本杀（如果不需要上线要注释）

            //扯皮开支
            formDatamap.put("qcpkz", 0);

            //转回公司场地费尾款
            formDatamap.put("cdfwkje", 0);
            //转回公司增值尾款
            formDatamap.put("zzwkje", 0);
            //实际转发数
            formDatamap.put("qszts", 0);
            //实际发评数
            formDatamap.put("qspts", 0);
            //延时费用
            formDatamap.put("qysf", 0);
            //厨房使用费用
            formDatamap.put("qcfsyf", 0);
            //收取卫生费用
            formDatamap.put("qwsf", 0);
            //赔偿费用
            formDatamap.put("qbcf", 0);
            //代购收入
            formDatamap.put("qdgsr", 0);
            //代购成本
            formDatamap.put("qdgcb", 0);
            //代购利润
            formDatamap.put("qdgsc", 0);
            //实际消费总金额
            formDatamap.put("qdcxf", 0);
            //客户商品消费总金额
            formDatamap.put("qspxf", 0);
            //分公司经费
            formDatamap.put("qfgsjf", 0);
            //应转公司商品金额
            formDatamap.put("qjnje", 0);
            //校代金额
            formDatamap.put("qxdje", 0);
            //校代支出
            formDatamap.put("xdzc", 0);
            //保险支出
            formDatamap.put("xbxzc", 0);
            //轰趴师工资支出
            formDatamap.put("xhpszcgz", 0);
            //轰趴师其他支出
            formDatamap.put("xhpszcqt", 0);
            //剧本杀支出
            formDatamap.put("qjbszc", 0);
            //客户业绩开支
            formDatamap.put("qkhkz", 0);
            //店长业绩开支
            formDatamap.put("qdzkz", 0);
            //业绩开支
            formDatamap.put("qbcje", 0);
            //啤酒开支
            formDatamap.put("qpjkz", 0);
            //其他费用
            formDatamap.put("qcdzs", 0);
            //餐饮总成本
            formDatamap.put("cyzcb", 0);

            //客户姓名
            formDatamap.put("qkhxm", l.getXzk());
            //客户身份证
            formDatamap.put("qkhsfzh", l.getXzksfz());

            //自助餐成本
            formDatamap.put("qzzccb", 0);

            //自助餐收入
            formDatamap.put("qzzcsr", 0);
            //策划其他收入
            formDatamap.put("qchqtsr", 0);

            //需要和数据库保持一致
            formDatamap.put("aid", l.getXddbh());
            //性质（运营查数据）
            formDatamap.put("vxz", villa.getVxz());
            //运营部门
            formDatamap.put("DzSsBm", villa.getVbsssbm() != null ? villa.getVbsssbm().replace("(", "").replace(")", "") : "");
            //进场及消费唯一标识符
            formDatamap.put("jcjxfid", IdConfig.uuId());
            //策划外包金额
            formDatamap.put("qchwbje", 0);
            //策划外包成本
            formDatamap.put("qchwbcb", 0);
            //策划外包利润
            formDatamap.put("qchwblr", 0);
            //当前流程的名字
            formDatamap.put("aname", l.getXfd() + "发起了'进场及消费申请',进场:" + df2.format(l.getXjctime()) + ",退场:" + df2.format(l.getXtctime()) + ",别墅:" + villa.getVname() + ",请您立即处理");
            //电子协议单图片
            formDatamap.put("aimg", YdConfig.setTpList(xydUrl, "协议单图片"));
            //别墅id
            formDatamap.put("bs", l.getXbsmc());
            formDatamap.put("xzkdh", l.getXzkdh());
            //定金类型：收的钱
            formDatamap.put("xysdj", l.getXysdj() != null ? l.getXysdj() : 0);
            formDatamap.put("xxsdj", l.getXxsdj() != null ? l.getXxsdj() : 0);
            formDatamap.put("xxxdj", l.getXxxdj() != null ? l.getXxxdj() : 0);
            //定金类型
            formDatamap.put("xdjtype", l.getXdjtype() != null ? l.getXdjtype() : "1");
            //场地费原价
            formDatamap.put("xqkzj", l.getXqkzj() != null ? l.getXqkzj() : 0);
            //人数
            formDatamap.put("xrs", l.getXrs() != null ? l.getXrs() : 0);
            //超出人数/元
            formDatamap.put("xcudrfy", l.getXcudrfy() != null ? l.getXcudrfy() : 0);
            //转发优惠至金额
            formDatamap.put("xhfyj", l.getXhfyj() != null ? l.getXhfyj() : 0);
            //保险总额
            formDatamap.put("xbxzje", l.getXbxzje() != null ? l.getXbxzje() : 0);
            //保险单价
            formDatamap.put("xbxdj", l.getXbxdj() != null ? l.getXbxdj() : 0);
            //保险人数
            formDatamap.put("xbxrs", l.getXbxrs() != null ? l.getXbxrs() : 0);
            //主题总额
            formDatamap.put("xztze", l.getXztze() != null ? l.getXztze() : 0);
            //轰趴师费用
            formDatamap.put("xhpsfy", l.getXhpsfy() != null ? l.getXhpsfy() : 0);
            //轰趴师姓名
            formDatamap.put("xhpsxm", l.getXhpsxm());
            //Cs真人费用
            formDatamap.put("xzrcsfy", l.getXzrcsfy() != null ? l.getXzrcsfy() : 0);
            //订餐总额
            formDatamap.put("xdcze", l.getXdcze() != null ? l.getXdcze() : 0);
            //烧烤总额
            formDatamap.put("xskze", l.getXskze() != null ? l.getXskze() : 0);
            //好评数
            formDatamap.put("xhpts", l.getXhpts() != null ? l.getXhpts() : 0);
            //共转数
            formDatamap.put("xzfts", l.getXzfts() != null ? l.getXzfts() : 0);
            //集赞数
            formDatamap.put("xjzts", l.getXjzts() != null ? l.getXjzts() : 0);
            //商品优惠券数
            formDatamap.put("xspyhq", l.getXspyhq() != null ? l.getXspyhq() : 0);
            //赠送的会员卡数
            formDatamap.put("xzshyksl", l.getXzshyksl() != null && !"".equals(l.getXzshyksl()) ? l.getXzshyksl() : 0);
            //协议单id
            formDatamap.put("xid", l.getXid());
            //客户姓名
            formDatamap.put("xzk", l.getXzk());
            //别墅名字
            formDatamap.put("bsmc", villa.getVname());
            //进场时间
            formDatamap.put("qjctime", l.getXjctime());
            //退场时间
            formDatamap.put("qtctime", l.getXtctime());
            //进场时间
            formDatamap.put("jcsj", l.getXjctime());
            //退场时间
            formDatamap.put("tcsj", l.getXtctime());
            //进货商品是否补充
            formDatamap.put("spbc", 1);
            //巡场是否异常
            formDatamap.put("fxycqk", 0);
            //巡场是否平台好评
            formDatamap.put("pthp", 1);
            //巡场是否完成防扰民
            formDatamap.put("frmdk", 1);
            //退场后卫生是否异常
            formDatamap.put("sbws", 0);
            //是否有扯皮
            formDatamap.put("tczp", 0);
            //客户是否有意见
            formDatamap.put("khjy", 0);
            //补交定金
            formDatamap.put("xbjdj", l.getXbjdj() != null ? l.getXbjdj() : 0);
            //城市
            String city = villa.getCszq() != null ? !"".equals(villa.getCszq()) ? villa.getCszq() : villa.getCity() : villa.getCity();
            formDatamap.put("city", city);
            String jcjxfzxr = employee != null ? employee.getUserid() : null;
            //流程执行人的ID
            formDatamap.put("jcjxfzxr", jcjxfzxr != null && !"".equals(jcjxfzxr) ? jcjxfzxr : villa.getPid());

            //值班店长
            formDatamap.put("zbdz", employee != null ? employee.getUserid() : villa.getPid());
            GatewayResult gatewayResult = queryYdLcXzRc(l, "下单发起进场及消费申请", formDatamap, "012412221639786136545", dingkey, ydAppkey, ydBd);
            WlgbDksljl wlgbDksljl = new WlgbDksljl();
            if (gatewayResult != null) {
                wlgbDksljl.setSlid(gatewayResult.getResult());
            }
            wlgbDksljl.setBz("进场及消费");
            wlgbDksljl.setId(IdConfig.uuId());
            wlgbDksljl.setTjsj(new Date());
            wlgbDksljl.setJsr(villa.getPid());
            wlgbDksljl.setDdid(l.getXid());
            wlgbDksljl.setBsid(l.getXbsmc());
            if (dingdingEmployee != null) {
                wlgbDksljl.setTjrid(dingdingEmployee.getUserid());
                wlgbDksljl.setTjr(dingdingEmployee.getName());
            } else {
                wlgbDksljl.setTjr("系统定时任务发送");
            }
            return wlgbDksljl;
        }
        return null;
    }
}
