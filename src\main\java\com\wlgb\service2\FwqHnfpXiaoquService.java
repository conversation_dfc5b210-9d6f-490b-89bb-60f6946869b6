package com.wlgb.service2;

import com.wlgb.entity.FwqHnfp;
import com.wlgb.entity.FwqHnfpXiaoqu;

import java.util.List;

public interface FwqHnfpXiaoquService {
    void save(FwqHnfpXiaoqu fwqHnfpXiaoqu);

    void deleteByPrimaryKey(FwqHnfpXiaoqu fwqHnfpXiaoqu);

    FwqHnfpXiaoqu selectOneByName(String url);

    void update(FwqHnfpXiaoqu fwqHnfpXiaoqu);

    List<FwqHnfpXiaoqu> selectAllBySousuo(String sousuo, int pageNum, int pageSize);
}
