package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "wlgb_djsljlcc")
@Data
public class WlgbDjsljlcc {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**收款编号*/
    private java.lang.String skbh;
	/**收款名称*/
    private java.lang.String skmc;
	/**实例id*/
    private java.lang.String slid;
	/**操作人*/
    private java.lang.String czr;
	/**操作人id*/
    private java.lang.String czrid;
	/**收款金额*/
    private java.lang.Double skje;
	/**二维码*/
    private java.lang.String ewm;
	/**类型*/
    private java.lang.String type;
	/**订单编号*/
    private java.lang.String ddbh;
}