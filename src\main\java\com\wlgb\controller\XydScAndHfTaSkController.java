package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.*;
import com.wlgb.service.*;
import com.wlgb.service2.TbQrdService;
import com.wlgb.service2.TbXydService;
import com.wlgb.service2.TbYddXydService;
import com.wlgb.service2.WeiLianService;
import com.wlgb.service3.DzxydService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: Class
 * @author: fwq
 * @date: 2024年05月27日 14:56
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/xydscAndhftask")
public class XydScAndHfTaSkController {
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private OssFileService ossFileService;
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WlgbXydLogService wlgbXydLogService;
    @Autowired
    private WlgbXxbjdjjlService wlgbXxbjdjjlService;
    @Autowired
    private WlgbXsdjbjjlService wlgbXsdjbjjlService;
    @Autowired
    private WlgbBdjlService wlgbBdjlService;
    @Autowired
    private DzxydService dzxydService;
    @Autowired
    private JqrConfig jqrConfig;
    @Autowired
    private WlgbDksljlService wlgbDksljlService;
    @Autowired
    private WlgbJdDjbbdService wlgbJdDjbbdService;
    @Autowired
    private TbQrdService tbQrdService;
    @Autowired
    private TbYddXydService tbYddXydService;
    @Autowired
    private WlgbOrderCyjlService wlgbOrderCyjlService;
    @Autowired
    private WlgbEatTcjlService wlgbEatTcjlService;
    @Autowired
    private WlgbEatCpjlService wlgbEatCpjlService;
    @Autowired
    private WlgbEatScjlService wlgbEatScjlService;

    /**
     * 删单异步
     */
    @RequestMapping(value = "scDdTask")
    public void scDdTask(HttpServletRequest request) throws ApiException {
        String userid = request.getParameter("userid");
        String datas = request.getParameter("datas");
        JSONObject jsonObject1 = JSONObject.parseObject(datas);
        TbXyd xyd = JSONObject.toJavaObject(jsonObject1, TbXyd.class);
        String datasXid = request.getParameter("datasXid");
        TbXyd tbXyd = tbXydService.queryById(xyd.getXid() != null && !"".equals(xyd.getXid()) ? xyd.getXid() : datasXid);
        TbXyd tbXyd1 = new TbXyd();
        tbXyd1.setXid(tbXyd.getXid());
        tbXyd1.setXsfsc("1");
        tbXyd1.setXscyy("4");
        tbXyd1.setXscz(userid);
        System.out.println("删除人：" + userid);
        tbXyd1.setXscsj(new Date());
        tbXydService.updateById(tbXyd1);
        List<WlgbDksljl> list = wlgbDksljlService.queryByXid(tbXyd.getXid());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        list.forEach(l -> {
            queryYdLcScRc(tbXyd, "删单删除" + l.getBz(), l.getSlid(), userid, dingkey);
            wlgbDksljlService.removeById(l.getId());
        });
        List<WlgbBdjl> list1 = wlgbBdjlService.queryByXid(tbXyd.getXid());
        list1.forEach(l -> {
            if (!"下单表单提交".equals(l.getBz())) {
                scBdSl(l.getSlid());
            }
            wlgbBdjlService.removeById(l.getId());
        });
        Map<String, Object> map = new HashMap<>();
        map.put("vid", tbXyd.getXbsmc());
        map.put("jctime", tbXyd.getXjctime());
        map.put("tctime", tbXyd.getXtctime());
        Integer sqKdj = weiLianDdXcxService.querySfSqKdj(map);
        if (sqKdj > 0) {
            weiLianDdXcxService.zxKdjHfGc(map);
        }
        TbQrd tbQrd = weiLianService.queryQrdByXidAndSfSc(tbXyd.getXid());
        if (tbQrd != null) {
            TbQrd tbQrd1 = new TbQrd();
            tbQrd1.setQid(tbQrd.getQid());
            tbQrd1.setQsfsc("1");
            tbQrd1.setQscz(userid);
            tbQrd1.setQscsj(new Date());
            tbQrd1.setQscyy("4");
            tbQrdService.updateById(tbQrd1);
        }
        DingdingEmployee employee = weiLianService.queryDingDingByName(tbXyd.getXfd());
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        Map<String, Object> map1 = null;
        try {
            map1 = dzXyd(tbXyd);
        } catch (Exception e) {
            e.printStackTrace();
        }

        String png = map1.get("png").toString();
        String filePath = FileConfig.getFileAbsolutePath2("static" + File.separator + "img" + File.separator + png);
        File file = new File(filePath);
        String dingpan = "";
        try {
            dingpan = DingPan.dingpan(file, dingkey);
        } catch (Exception e) {
            e.printStackTrace();
            try {
                DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : tbXyd.getXfd()) + "删单，订单编号”" + tbXyd.getXddbh() + "“协议单上传钉盘报错了" + e.getMessage());
            } catch (ApiException apiException) {
                apiException.printStackTrace();
            }
        }
        //判断是不是老订单
        Boolean b = true;
        Integer ddCz = weiLianDdXcxService.queryDdCz(tbXyd.getXid());
        if (ddCz > 0) {
            List<String> list2 = new ArrayList<>();
            list2.add("15349026426046931");
            list2.add("15531676330599999");
            list2.add("159909317438346");
            for (String l : list2) {
                if (l.equals(userid)) {
                    b = false;
                    break;
                }
            }
        }
        //如果是在限制内不用播报
        if (b) {
            // 轰趴师播报
            if (tbXyd.getXhpsfy() != null) {
                if (tbXyd.getXhpsfy() > 0) {
                    try {
                        hps(tbXyd, "此轰趴师订单已被删除", dingkey, dingpan);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            // 策划播报
            if (tbXyd.getXztze() != null) {
                if (tbXyd.getXztze() > 0) {
                    try {
                        ch(tbXyd, "此策划订单已被删除");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            String userList = employee != null ? employee.getUserid() : "";
            TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
            if (villa.getPid() != null && !"".equals(villa.getPid()) && !"null".equalsIgnoreCase(villa.getPid())) {
                if (employee != null) {
                    if (!villa.getPid().equals(employee.getUserid())) {
                        userList += "," + villa.getPid();
                    }
                } else {
                    userList += villa.getPid();
                }
            }

            String text = (ding != null ? ding.getName() : "") + "删除了订单！";
            xyd = tbXydService.queryById(tbXyd.getXid());
            text += "\n\n别墅：" + villa.getVname();
            text += "\n客户姓名：" + xyd.getXzk();
            text += "\n客户电话：" + xyd.getXzkdh();
            text += "\n场地费：" + xyd.getXqkzj() + "元";
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            text += "\n进场时间：" + df2.format(xyd.getXjctime());
            text += "\n退场时间：" + df2.format(xyd.getXtctime());
            Calendar calendar1 = Calendar.getInstance();
            text += "\n送达时间：" + df2.format(calendar1.getTime());
            DingDBConfig.sendGztzText(dingkey, userList, text);
            //加盟商播报
            WlgbJmsbb wlgbJmsbb = weiLianDdXcxService.queryJmsBbByVid(villa.getVid());
            if (wlgbJmsbb != null) {
                DingDBConfig.sendGztzText(dingkey, wlgbJmsbb.getBbrid(), text);
            }
        }

        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXltime(new Date());
        wlgbXydLog.setXltext("删除订单");
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : userid);
        wlgbXydLog.setXlxid(xyd.getXid());
        wlgbXydLogService.save(wlgbXydLog);
        List<WlgbJdDjbbd> list2 = wlgbJdDjbbdService.queryByDdBhAndSfSc(tbXyd.getXddbh());
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");

        TbYddXyd tbYddXyd = tbYddXydService.queryTbYddXydById(tbXyd.getXid());
        if (tbYddXyd != null) {
            TbYddXyd tbYddXyd1 = new TbYddXyd();
            tbYddXyd1.setXid(tbXyd.getXid());
            tbYddXyd1.setXsfsc(1);
            tbYddXyd1.setXstatu("已取消");
            String post = null;
            try {
                if (post != null && !"".equals(post)) {
                    JSONObject xcxJsonObject = JSONObject.parseObject(post);
                    String jg = xcxJsonObject.getString("jg");
                    if (!"ok".equalsIgnoreCase(jg)) {
                        try {
                            String context1 = "删单将状态更新至小程序请求结果出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e1) {
                            e1.printStackTrace();
                        }
                    }
                }
            } catch (Exception e) {
                try {
                    String context1 = "删单将状态更新至小程序出错了，订单编号：" + tbYddXyd.getXddbh() + "，请求结果：" + post + "，错误原因：" + e;
                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                } catch (ApiException e1) {
                    e1.printStackTrace();
                }
                e.printStackTrace();
            }
        }

        String token = DingToken.token(dingkey);
        list2.forEach(l -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("sfxd", 2);
            for (int i = 0; i < 5; i++) {
                GatewayResult gatewayResult = null;
                try {
                    gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, l.getFormInstId(), jsonObject.toJSONString());
                } catch (Exception e) {
                    gatewayResult = new GatewayResult();
                    e.printStackTrace();
                }
                if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                    if (i == 4) {
                        try {
                            DingQunSend.send((ding != null ? ding.getName() : tbXyd.getXfd()) + "正在删除修改定金状态，更新表单出错了，订单编号：" + tbXyd.getXddbh() + "，定金流水号：" + l.getLsh() + "，错误原因：" + gatewayResult.toString(), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    break;
                }
            }
        });

        //删除原有餐饮记录
        JSONObject jsonObjectQuery = new JSONObject();
        jsonObjectQuery.put("cybh", tbXyd.getXddbh());
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = YdConfig.queryBdSlData(1, ydAppkey, token, jsonObjectQuery.toJSONString(), "FORM-6Q866L81621TOWRCYSHTECVS90KY10URT5RSKW5");
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (gatewayResult.getSuccess()) {
            JSONObject jsonObject2 = JSONObject.parseObject(gatewayResult.getResult());
            JSONArray jsonArray = jsonObject2.getJSONArray("data");
            if (jsonArray != null && jsonArray.size() > 0) {
                jsonArray.forEach(l -> {
                    JSONObject item = (JSONObject) l;
                    String formInstanceId = item.getString("formInstanceId");
                    try {
                        DingBdLcConfig.scBdSl(token, ydAppkey, "012412221639786136545", formInstanceId);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }
        wlgbOrderCyjlService.clearCyJl(tbXyd.getXddbh());
        wlgbEatTcjlService.clearData(tbXyd.getXddbh());
        wlgbEatCpjlService.clearData(tbXyd.getXddbh());
        wlgbEatScjlService.clearData(tbXyd.getXddbh());
    }


    /**
     * 恢复订单异步
     */
    @RequestMapping(value = "hfDdTask")
    public void hfDdTask(HttpServletRequest request) {
        String userid = request.getParameter("userid");
        String xid = request.getParameter("xid");
        TbXyd tbXyd = tbXydService.queryById(xid);
        TbXyd tbXyd1 = new TbXyd();
        DingdingEmployee employee1 = weiLianService.queryDingDingByName(tbXyd.getXfd());
        if (employee1 == null) {
            DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId("012412221639786136545");
            if (employee != null) {
                tbXyd1.setXfd(employee.getName());
                DingdingEmployee employee2 = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd.getXsendder());
                if (employee2 != null) {
                    tbXyd1.setXsendder(employee2.getUserid());
                }
            }
        }
        tbXyd1.setXid(tbXyd.getXid());
        tbXyd1.setXsfsc("0");
        tbXyd1.setXgsj(new Date());
        tbXyd1.setXxgzid(userid);
        tbXydService.updateById(tbXyd1);

        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLog.setXltime(new Date());
        wlgbXydLog.setXlxid(tbXyd.getXid());
        DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        wlgbXydLog.setXlname(employee != null ? employee.getName() : null);
        wlgbXydLog.setXluserid(employee != null ? employee.getUserid() : userid);
        wlgbXydLog.setXltext("恢复订单");
        wlgbXydLogService.save(wlgbXydLog);
        TbXyd xyd = tbXydService.queryById(tbXyd.getXid());
        ddLc(xyd, userid);

        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);

        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");

        String bddz = "D:\\tomcat9\\webapps\\weilian3\\";
        File file = new File(bddz + xyd.getXimagepath());
        String dingpan = "";
        try {
            dingpan = DingPan.dingpan(file, dingkey);
        } catch (Exception e) {
            e.printStackTrace();
            try {
                DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : xyd.getXfd()) + "恢复订单，订单编号”" + xyd.getXddbh() + "“协议单上传钉盘报错了" + e.getMessage());
            } catch (ApiException apiException) {
                apiException.printStackTrace();
            }
        }

        // 轰趴师播报
        if (xyd.getXhpsfy() != null) {
            if (xyd.getXhpsfy() > 0) {
                try {
                    hps(xyd, xyd.getXfd() + "有新的轰趴师订单，请注意跟进", dingkey, dingpan);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        // 策划播报
        if (xyd.getXztze() != null) {
            if (xyd.getXztze() > 0) {
                ch(xyd, "有新的策划订单");
            }
        }

        // 剧本杀播报
        if (xyd.getXjbszje() != null) {
            if (xyd.getXjbszje() > 0) {
                try {
                    jbsBb(xyd.getXfd() + "有新的剧本杀订单，请注意跟进", dingkey, dingpan);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 删除宜搭流程容错机制
     *
     * @param tbXyd      协议单对象
     * @param name       操作名称
     * @param formInstId 实例id
     * @param userid     操作人id
     * @param dingkey    钉钉key
     */
    public void queryYdLcScRc(TbXyd tbXyd, String name, String formInstId, String userid, Dingkey dingkey) {
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        for (int i = 0; i < 5; i++) {
            GatewayResult gatewayResult;
            try {
                //不需要传id，默认使用系统机器人
                gatewayResult = scLcSl(formInstId, token, ydAppkey);
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                e.printStackTrace();
            }
            if (!gatewayResult.getSuccess()) {
                if (i == 4) {
                    try {
                        DingQunSend.send((ding != null ? ding.getName() : tbXyd.getXfd()) + "正在" + name + "，删除流程出错了，订单编号：" + tbXyd.getXddbh() + "，错误原因：" + gatewayResult.toString(), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                break;
            }
        }
    }

    /**
     * 删除流程实例
     *
     * @param slid     实例id
     * @param token    美团token
     * @param ydAppkey 宜搭配置
     * @return 返回结果
     */
    public GatewayResult scLcSl(String slid, String token, YdAppkey ydAppkey) {
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.scLcSl(token, ydAppkey, "012412221639786136545", slid);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return gatewayResult;
    }

    /**
     * 恢复订单发送至宜搭
     *
     * @param tbXyd2 协议单对象
     * @param userid 用户id
     */
    public void ddLc(TbXyd tbXyd2, String userid) {
        Map<String, Object> formDatamap = new HashMap<>();
        if (tbXyd2.getXsfys() != null && !"".equals(tbXyd2.getXysbz())) {
            formDatamap.put("xsfys", tbXyd2.getXsfys());
            if ("1".equals(tbXyd2.getXsfys())) {
                formDatamap.put("xysbz", tbXyd2.getXysbz());
            }
        } else {
            formDatamap.put("xsfys", 0);
        }

        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");

        //策划新模块
        String sfych = tbXyd2.getSfxzch() != null && !"".equals(tbXyd2.getSfxzch()) ? tbXyd2.getSfxzch() : tbXyd2.getXztze() > 0 ? "1" : "0";
        formDatamap.put("sfxzch", sfych);
        formDatamap.put("sftjchdp", tbXyd2.getSftjchdp() != null && !"".equals(tbXyd2.getSftjchdp()) ? tbXyd2.getSftjchdp() : "0");
        JSONObject jsonObject3 = new JSONObject();
        jsonObject3.put("ddbh", tbXyd2.getXddbh());
        String formUuid1 = "FORM-CA966X91TBHUZW34ZWRO42JK83FG2G33WQRUKS";

        formDatamap.put("sfcxptxd", tbXyd2.getSfcxptxd());
        formDatamap.put("xsfsywxfwdddje", tbXyd2.getXsfsywxfwdddje());
        if ("1".equals(tbXyd2.getXsfsywxfwdddje())) {
            formDatamap.put("xwsywdddbh", tbXyd2.getXwsywdddbh());
            formDatamap.put("xwsywdddsyje", tbXyd2.getXwsywdddsyje());
        }

        //TODO 包服务开
        formDatamap.put("xsfbdf", tbXyd2.getXsfbdf());
        formDatamap.put("xdfje", tbXyd2.getXdfje());
        formDatamap.put("xsfbc", tbXyd2.getXsfbc());
        formDatamap.put("xcf", tbXyd2.getXcf());
        formDatamap.put("xsfbch", tbXyd2.getXsfbch());
        formDatamap.put("xchje", tbXyd2.getXchje());
        formDatamap.put("xsfbcc", tbXyd2.getXsfbcc());
        formDatamap.put("xbcje", tbXyd2.getXbcje());
        formDatamap.put("xsfbwsf", tbXyd2.getXsfbwsf());
        formDatamap.put("xwsfje", tbXyd2.getXwsfje());
        formDatamap.put("xsfbcfsyf", tbXyd2.getXsfbcfsyf());
        formDatamap.put("xcfsyfje", tbXyd2.getXcfsyfje());
        //TODO 包服务关

        formDatamap.put("qqid", tbXyd2.getQqid());

        //房东
        DingdingEmployee employee = weiLianService.queryDingDingByName(tbXyd2.getXfd());


        //保险成交人
        if (tbXyd2.getXbxcjr() != null && !"".equals(tbXyd2.getXbxcjr()) && tbXyd2.getXbxcjr().length() > 0) {
            DingdingEmployee xbxcjr = weiLianService.queryDingDingByName(tbXyd2.getXbxcjr());
            if (xbxcjr != null) {
                formDatamap.put("xbxcjr", xbxcjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xbxcjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xbxcjr", userid);
                }
            }

        }

        //老板
        if (tbXyd2.getXlbjf() != null && !"".equals(tbXyd2.getXlbjf()) && tbXyd2.getXlbjf().length() > 0) {
            DingdingEmployee xlbjf = weiLianService.queryDingDingByName(tbXyd2.getXlbjf());
            if (xlbjf != null) {
                formDatamap.put("xbxcjr", xlbjf.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xlbjf", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xlbjf", userid);
                }
            }
        }

        //策划成交人
        if (tbXyd2.getXchcjr() != null && !"".equals(tbXyd2.getXchcjr()) && tbXyd2.getXchcjr().length() > 0) {
            DingdingEmployee xchcjr = weiLianService.queryDingDingByName(tbXyd2.getXchcjr());
            if (xchcjr != null) {
                formDatamap.put("xchcjr", xchcjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xchcjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xchcjr", userid);
                }
            }
        }

        //策划辅助成交人
        if (tbXyd2.getXchfzcjr() != null && !"".equals(tbXyd2.getXchfzcjr()) && tbXyd2.getXchfzcjr().length() > 0) {
            DingdingEmployee xchfzcjr = weiLianService.queryDingDingByName(tbXyd2.getXchfzcjr());
            if (xchfzcjr != null) {
                formDatamap.put("xchfzcjr", xchfzcjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xchfzcjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xchfzcjr", userid);
                }
            }
        }

        //烧烤成交人
        if (tbXyd2.getXskcjr() != null && !"".equals(tbXyd2.getXskcjr()) && tbXyd2.getXskcjr().length() > 0) {
            DingdingEmployee xskcjr = weiLianService.queryDingDingByName(tbXyd2.getXskcjr());
            if (xskcjr != null) {
                formDatamap.put("xskcjr", xskcjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xskcjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xskcjr", userid);
                }
            }
        }

        //烧烤辅助成交人
        if (tbXyd2.getXskfzcjr() != null && !"".equals(tbXyd2.getXskfzcjr()) && tbXyd2.getXskfzcjr().length() > 0) {
            DingdingEmployee xskfzcjr = weiLianService.queryDingDingByName(tbXyd2.getXskfzcjr());
            if (xskfzcjr != null) {
                formDatamap.put("xskfzcjr", xskfzcjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xskfzcjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xskfzcjr", userid);
                }
            }
        }

        //现场成交人
        if (tbXyd2.getXcjr() != null && !"".equals(tbXyd2.getXcjr()) && tbXyd2.getXcjr().length() > 0) {
            DingdingEmployee xcjr = weiLianService.queryDingDingByName(tbXyd2.getXcjr());
            if (xcjr != null) {
                formDatamap.put("xcjr", xcjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xcjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xcjr", userid);
                }
            }
        }


        //轰趴师成交人
        if (tbXyd2.getXhpscjr() != null && !"".equals(tbXyd2.getXhpscjr()) && tbXyd2.getXhpscjr().length() > 0) {
            DingdingEmployee xhpscjr = weiLianService.queryDingDingByName(tbXyd2.getXhpscjr());
            if (xhpscjr != null) {
                formDatamap.put("xhpscjr", xhpscjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xhpscjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xhpscjr", userid);
                }
            }
        }

        //轰趴师辅助成交人
        if (tbXyd2.getXhpsfzcjrid() != null && !"".equals(tbXyd2.getXhpsfzcjrid()) && tbXyd2.getXhpsfzcjrid().length() > 0) {
            DingdingEmployee xhpsfzcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd2.getXhpsfzcjrid());
            if (xhpsfzcjr != null) {
                formDatamap.put("xhpsfzcjr", xhpsfzcjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xhpsfzcjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xhpsfzcjr", userid);
                }
            }
        }

        //cs成交人
        if (tbXyd2.getXzrcscjr() != null && !"".equals(tbXyd2.getXzrcscjr()) && tbXyd2.getXzrcscjr().length() > 0) {
            DingdingEmployee xzrcscjr = weiLianService.queryDingDingByName(tbXyd2.getXzrcscjr());
            if (xzrcscjr != null) {
                formDatamap.put("xzrcscjr", xzrcscjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xzrcscjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xzrcscjr", userid);
                }
            }
        }

        //订餐成交人
        if (tbXyd2.getXdccjr() != null && !"".equals(tbXyd2.getXdccjr()) && tbXyd2.getXdccjr().length() > 0) {
            DingdingEmployee xdccjr = weiLianService.queryDingDingByName(tbXyd2.getXdccjr());
            if (xdccjr != null) {
                formDatamap.put("xdccjr", xdccjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xdccjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xdccjr", userid);
                }
            }
        }

        //订餐辅助成交人
        if (tbXyd2.getXdcfzcjr() != null && !"".equals(tbXyd2.getXdcfzcjr()) && tbXyd2.getXdcfzcjr().length() > 0) {
            DingdingEmployee xdcfzcjr = weiLianService.queryDingDingByName(tbXyd2.getXdcfzcjr());
            if (xdcfzcjr != null) {
                formDatamap.put("xdcfzcjr", xdcfzcjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xdcfzcjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xdcfzcjr", userid);
                }
            }
        }

        //后续成交人
        if (tbXyd2.getXhxcjr() != null && !"".equals(tbXyd2.getXhxcjr()) && tbXyd2.getXhxcjr().length() > 0) {
            DingdingEmployee xhxcjr = weiLianService.queryDingDingByName(tbXyd2.getXhxcjr());
            if (xhxcjr != null) {
                formDatamap.put("xhxcjr", xhxcjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xhxcjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xhxcjr", userid);
                }
            }
        }

        //策划经理
        if (tbXyd2.getXchjl() != null && !"".equals(tbXyd2.getXchjl()) && tbXyd2.getXchjl().length() > 0) {
            DingdingEmployee xchjlid = weiLianService.queryDingDingByName(tbXyd2.getXchjl());
            if (xchjlid != null) {
                formDatamap.put("xchjlid", xchjlid.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xchjlid", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xchjlid", userid);
                }
            }
            formDatamap.put("xchjl", tbXyd2.getXchjl() != null && !"".equals(tbXyd2.getXchjl()) ? tbXyd2.getXchjl() : null);
        }

        //剧本杀
        formDatamap.put("xsfxyjbs", tbXyd2.getXsfxyjbs() != null && !"".equals(tbXyd2.getXsfxyjbs()) ? tbXyd2.getXsfxyjbs() : 0);
        if ("1".equals(tbXyd2.getXsfxyjbs())) {
            formDatamap.put("xjbszje", tbXyd2.getXjbszje() != null ? tbXyd2.getXjbszje() : 0);
            formDatamap.put("xjbszxr", tbXyd2.getXjbszxr() != null && !"".equals(tbXyd2.getXjbszxr()) ? tbXyd2.getXjbszxr() : "");
            DingdingEmployee xjbscjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd2.getXjbscjrid());
            if (xjbscjr != null) {
                formDatamap.put("xjbscjr", xjbscjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xjbscjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xjbscjr", userid);
                }
            }
            DingdingEmployee xjbsfzcjr = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd2.getXjbsfzcjrid());
            if (xjbsfzcjr != null) {
                formDatamap.put("xjbsfzcjr", xjbsfzcjr.getUserid());
            } else {
                //离职选房东
                if (employee != null) {
                    formDatamap.put("xjbsfzcjr", employee.getUserid());
                } else {
                    //房东离职选当前用户
                    formDatamap.put("xjbsfzcjr", userid);
                }
            }
        }

        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");

        Dzxyd dzxyd = dzxydService.queryByXid(tbXyd2.getXid());
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd2.getXbsmc());
        Map<String, Object> map = new HashMap<>();
        try {
            map = dzXyd(tbXyd2);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (map.size() > 0) {
            tbXyd2.setXimagepath((String) map.get("png"));
        }
        String filePath = FileConfig.getFileAbsolutePath2("static" + File.separator + "img" + File.separator +
                tbXyd2.getXimagepath());
        File file = new File(filePath);
        String upload = "";
        try {
            FileInputStream fileInputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
            fileInputStream.close();
            upload = ossFileService.upload(multipartFile);
            if (upload != null && !"".equals(upload)) {
                upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
            }
        } catch (Exception e) {
            e.printStackTrace();
            try {
                DingDBConfig.sendGztzText(dingkey, "15349026426046931", (ding != null ? ding.getName() : tbXyd2.getXfd()) + "恢复订单，订单编号”" + tbXyd2.getXddbh() + "“电子协议单上传阿里云报错了");
            } catch (ApiException apiException) {
                apiException.printStackTrace();
            }
        }


        if (dzxyd == null) {
            Dzxyd dzxyd1 = new Dzxyd();
            dzxyd1.setXid(tbXyd2.getXid());
            dzxyd1.setUrl(upload);
            dzxyd1.setTip(IdConfig.uuId());
            dzxyd1.setTime(new Date());
            dzxydService.save(dzxyd1);
        } else {
            Dzxyd dzxyd1 = new Dzxyd();
            dzxyd1.setTip(dzxyd.getTip());
            dzxyd1.setUrl(upload);
            dzxydService.updateById(dzxyd1);
        }
        //需要和数据库保持一致
        formDatamap.put("aid", tbXyd2.getXddbh());
        //pngurl必须用阿里云oss地址，不要用服务器图片地址
        formDatamap.put("aimg", YdConfig.setTpList(upload, "协议单图片"));
        formDatamap.put("xzkdh", tbXyd2.getXzkdh());
        //发起人
        formDatamap.put("xsendder", tbXyd2.getXsendder());
        //是否订餐
        formDatamap.put("xsfdc", ((tbXyd2.getXdcze() != null ? tbXyd2.getXdcze() : 0) + (tbXyd2.getXskze() != null ? tbXyd2.getXskze() : 0)) > 0 ? 1 : 0);
        //修改人id
        formDatamap.put("xxgzid", tbXyd2.getXxgzid());
        //协议单id
        formDatamap.put("xid", tbXyd2.getXid());
        //订单编号
        formDatamap.put("xddbh", tbXyd2.getXddbh());
        //城市
        formDatamap.put("cs", villa != null ? villa.getCity() : null);
        DingdingEmployee employee2 = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa != null ? villa.getPid() : null);
        //值班店长
        formDatamap.put("zbdz", employee2 != null ? employee2.getUserid() : null);
        //进场日期
        formDatamap.put("xjctime2", tbXyd2.getXjctime());
        //场次判断
        Date date = tbXyd2.getXjctime();
        int hours = date.getHours();
        if (hours == 10) {
            Date date1 = tbXyd2.getXtctime();
            int hours1 = date1.getHours();
            if (hours1 == 17) {
                //白场
                formDatamap.put("cc", "1");
            } else if (hours1 == 8) {
                //全天1
                formDatamap.put("cc", "3");
            } else {
                //自定义
                formDatamap.put("cc", "5");
            }
        } else if (hours == 18) {
            Date date1 = tbXyd2.getXtctime();
            int hours1 = date1.getHours();
            if (hours1 == 8) {
                //晚场
                formDatamap.put("cc", "2");
            } else if (hours1 == 17) {
                //全天2
                formDatamap.put("cc", "4");
            } else {
                //自定义
                formDatamap.put("cc", "5");
            }
        } else {
            //自定义
            formDatamap.put("cc", "5");
        }


        //别墅名称
        formDatamap.put("xbsmc2", villa != null ? villa.getVid() : null);
        //别墅名称
        formDatamap.put("bsname", villa != null ? villa.getVname() : null);
        //别墅id
        formDatamap.put("xbsmc", tbXyd2.getXbsmc());
        //是否需要烧烤
        formDatamap.put("xsfsk", tbXyd2.getXskze() != null ? tbXyd2.getXskze() > 0 ? 1 : 0 : 0);
        //是否特殊场次
        formDatamap.put("sftscc", tbXyd2.getSftscc() != null ? tbXyd2.getSftscc() : 0);
        //进场时间
        formDatamap.put("xjctime", tbXyd2.getXjctime());
        //退场时间
        formDatamap.put("xtctime", tbXyd2.getXtctime());
        //是否现场成交
        formDatamap.put("xisxzcj", tbXyd2.getXisxzcj() != null ? tbXyd2.getXisxzcj() : 1);
        //订单类型
        formDatamap.put("xzdcl", tbXyd2.getXzdcl() != null ? tbXyd2.getXzdcl() : 0);
        //房东id
        formDatamap.put("xfd", employee != null ? employee.getUserid() : userid);
        //房东电话
        formDatamap.put("xfddh", tbXyd2.getXfddh());
        //租客姓名
        formDatamap.put("xzk", tbXyd2.getXzk());
        //租客电话
        formDatamap.put("xzkdh", tbXyd2.getXzkdh());
        //租客身份证
        formDatamap.put("xzksfz", tbXyd2.getXzksfz());
        //团队性质
        formDatamap.put("xtdxz", tbXyd2.getXtdxz());
        TbKhlyxz tbKhlyxz = weiLianService.queryKhLyXzByLidOrLname(tbXyd2.getXtdxz());
        if (tbKhlyxz != null) {
            formDatamap.put("tdxzmc", tbKhlyxz.getLname());
        }
        //单位名称
        formDatamap.put("xdwmc", tbXyd2.getXdwmc());
        //客户来源
        formDatamap.put("xkhly", tbXyd2.getXkhly());
        TbKhlyxz tbKhlyxz1 = weiLianService.queryKhLyXzByLidOrLname(tbXyd2.getXkhly());
        if (tbKhlyxz1 != null) {
            formDatamap.put("khlymc", tbKhlyxz1.getLname());
        }
        //是否删除（默认0）
        formDatamap.put("sfsc", 0);
        //人数
        formDatamap.put("xrs", tbXyd2.getXrs() != null ? tbXyd2.getXrs() : 0);
        //超出人数费用
        formDatamap.put("xcudrfy", tbXyd2.getXcudrfy() != null ? tbXyd2.getXcudrfy() : 0);
        //校代姓名
        formDatamap.put("xxdxm", tbXyd2.getXxdxm());
        //校代所属城市
        formDatamap.put("xxdsscs", tbXyd2.getXxdsscs());
        //校代上级姓名
        formDatamap.put("xxdsjxm", tbXyd2.getXxdsjxm());
        //电话编码
        formDatamap.put("xdhbm", tbXyd2.getXdhbm());
        //校代电话
        formDatamap.put("xxddh", tbXyd2.getXxddh());
        //是否开票
        formDatamap.put("xiskfp", tbXyd2.getXiskfp() != null && !"".equals(tbXyd2.getXiskfp()) ? tbXyd2.getXiskfp() : 0);
        //定金类型
        formDatamap.put("xdjtype", tbXyd2.getXdjtype());
        //定金类型
        formDatamap.put("xbjdjlx", tbXyd2.getXbjdjlx());
        //线上定金
        formDatamap.put("xxsdj", tbXyd2.getXxsdj() != null ? tbXyd2.getXxsdj() : 0);
        //线下定金
        formDatamap.put("xxxdj", tbXyd2.getXxxdj() != null ? tbXyd2.getXxxdj() : 0);
        //已收定金
        formDatamap.put("xysdj", tbXyd2.getXysdj() != null ? tbXyd2.getXysdj() : 0);
        //全款租金
        formDatamap.put("xqkzj", tbXyd2.getXqkzj() != null ? tbXyd2.getXqkzj() : 0);
        //转账时间
        formDatamap.put("xzzsj", tbXyd2.getXzzsj());
        //验券时间
        formDatamap.put("xyqrq", tbXyd2.getXyqrq());
        //转账后两位
        formDatamap.put("xzzhlw", tbXyd2.getXzzhlw());
        //付款码
        formDatamap.put("xfkm", tbXyd2.getXfkm());
        //验券人
        formDatamap.put("xyqr", tbXyd2.getXyqr());
        //代码
        formDatamap.put("xdm", tbXyd2.getXdm());
        //是否需要保险
        formDatamap.put("xsfbx", tbXyd2.getXsfbx() != null ? !"".equals(tbXyd2.getXsfbx()) ? tbXyd2.getXsfbx() : 0 : 0);
        //保险人数
        formDatamap.put("xbxrs", tbXyd2.getXbxrs() != null ? tbXyd2.getXbxrs() : 0);
        //保险单价
        formDatamap.put("xbxdj", tbXyd2.getXbxdj());
        //保险总额
        formDatamap.put("xbxzje", tbXyd2.getXbxzje() != null ? tbXyd2.getXbxzje() : 0);
        //策划总额
        formDatamap.put("xztze", tbXyd2.getXztze() != null ? tbXyd2.getXztze() : 0);

        //是否需要轰趴师
        formDatamap.put("xsfhps", tbXyd2.getXsfhps() != null ? tbXyd2.getXsfhps() : 0);
        //轰趴师姓名
        formDatamap.put("xhpsxm", tbXyd2.getXhpsxm());
        //轰趴师费用
        formDatamap.put("xhpsfy", tbXyd2.getXhpsfy() != null ? tbXyd2.getXhpsfy() : 0);
        //轰趴师费用明细
        formDatamap.put("xhpsfymx", tbXyd2.getXhpsfymx());
        //是否需要真人cs
        formDatamap.put("xsfzrcs", tbXyd2.getXsfzrcs() != null ? tbXyd2.getXsfzrcs() : 0);
        //cs教官
        formDatamap.put("xzrcsjg", tbXyd2.getXzrcsjg());
        //真人cs人数
        formDatamap.put("xzrcsrs", tbXyd2.getXzrcsrs() != null ? tbXyd2.getXzrcsrs() : 0);
        //真人cs费用
        formDatamap.put("xzrcsfy", tbXyd2.getXzrcsfy() != null ? tbXyd2.getXzrcsfy() : 0);
        //订餐金额
        formDatamap.put("xbdje", tbXyd2.getXbdje() != null ? tbXyd2.getXbdje() : 0);
        //订餐数量
        formDatamap.put("xbdsl", tbXyd2.getXbdsl() != null ? tbXyd2.getXbdsl() : 0);
        //订餐总额
        formDatamap.put("xdcze", tbXyd2.getXdcze() != null ? tbXyd2.getXdcze() : 0);
        //烧烤套餐金额
        formDatamap.put("xskje", tbXyd2.getXskje() != null ? tbXyd2.getXskje() : 0);
        //烧烤套餐数量
        formDatamap.put("xsksl", tbXyd2.getXsksl() != null ? tbXyd2.getXsksl() : 0);
        //烧烤总额
        formDatamap.put("xskze", tbXyd2.getXskze() != null ? tbXyd2.getXskze() : 0);
        //是否后续成交
        formDatamap.put("xsfhxgjcj", tbXyd2.getXsfhxgjcj() != null ? tbXyd2.getXsfhxgjcj() : 0);
        //是否允许协调
        formDatamap.put("xyxxt", tbXyd2.getXyxxt() != null ? tbXyd2.getXyxxt() : 0);
        //转发条数
        formDatamap.put("xzfts", tbXyd2.getXzfts() != null ? tbXyd2.getXzfts() : 0);
        //好评条数
        formDatamap.put("xhpts", tbXyd2.getXhpts() != null ? tbXyd2.getXhpts() : 0);
        //集赞条数
        formDatamap.put("xjzts", tbXyd2.getXjzts() != null ? tbXyd2.getXjzts() : 0);
        //恢复原价
        formDatamap.put("xhfyj", tbXyd2.getXhfyj() != null ? tbXyd2.getXhfyj() : 0);
        //小商品优惠券金额
        formDatamap.put("xspyhq", tbXyd2.getXspyhq() != null ? tbXyd2.getXspyhq() : 0);
        //赠送会员卡数量
        formDatamap.put("xzshyksl", tbXyd2.getXzshyksl() != null && !"".equals(tbXyd2.getXzshyksl()) ? tbXyd2.getXzshyksl() : 0);
        //事项备注
        formDatamap.put("xsxbz", tbXyd2.getXsxbz());
        //补交金额
        formDatamap.put("xbjdj", tbXyd2.getXbjdj() != null ? tbXyd2.getXbjdj() : 0);

        //给线上线下赋值
        if ("1".equals(tbXyd2.getXbjdjlx())) {
            //线上补交定金
            formDatamap.put("xxxbj", tbXyd2.getXbjdj() != null ? tbXyd2.getXbjdj() : 0);
        } else if ("2".equals(tbXyd2.getXbjdjlx())) {
            //线上补交定金
            formDatamap.put("xxsbj", tbXyd2.getXbjdj() != null ? tbXyd2.getXbjdj() : 0);
        } else {
            //线下补交定金
            formDatamap.put("xxxbj", tbXyd2.getXxxbj() != null ? tbXyd2.getXxxbj() : 0);
            //线上补交定金
            formDatamap.put("xxsbj", tbXyd2.getXxsbj() != null ? tbXyd2.getXxsbj() : 0);
        }


        //场地费补交
        formDatamap.put("xcdzjbj", tbXyd2.getXcdzjbj() != null ? tbXyd2.getXcdzjbj() : 0);
        //增值补交
        formDatamap.put("xzzfybj", tbXyd2.getXzzfybj() != null ? tbXyd2.getXzzfybj() : 0);
        //补交信息
        formDatamap.put("xbjxx", tbXyd2.getXbjxx() != null ? tbXyd2.getXbjxx() : "");

        //是否补交
        formDatamap.put("sfbj", tbXyd2.getXbjdj() != null ? tbXyd2.getXbjdj() > 0 ? "是" : "否" : "否");

        boolean b1 = false;
        if (tbXyd2.getXbjdj() != null && tbXyd2.getXbjdj() > 0) {
            List<WlgbXxbjdjjl> list = wlgbXxbjdjjlService.queryByXidAndSfSc(tbXyd2.getXid());
            List<JSONObject> list1 = new ArrayList<>();
            list.forEach(l -> {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("xxbjje", l.getDjje());
                jsonObject1.put("bjzzsj", l.getZzsj());
                jsonObject1.put("bjzfbhlw", l.getBjzfbhlw());
                list1.add(jsonObject1);
            });
            if (list.size() == 0) {
                if ("1".equals(tbXyd2.getXbjdjlx())) {
                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("xxbjje", tbXyd2.getXbjdj() != null ? tbXyd2.getXbjdj() : 0);
                    jsonObject1.put("bjzzsj", new Date());
                    jsonObject1.put("bjzfbhlw", "000000");
                    list1.add(jsonObject1);
                    b1 = true;
                }
                if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(tbXyd2.getXbjdjlx())) {
                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("xxbjje", tbXyd2.getXxxbj() != null ? tbXyd2.getXxxbj() : 0);
                    jsonObject1.put("bjzzsj", new Date());
                    jsonObject1.put("bjzfbhlw", "000000");
                    list1.add(jsonObject1);
                    b1 = true;
                }
            }
            formDatamap.put("xxbjzbd", list1);

            List<WlgbXsdjbjjl> list2 = wlgbXsdjbjjlService.queryByXidAndSfSc(tbXyd2.getXid());
            List<JSONObject> list3 = new ArrayList<>();
            list2.forEach(l -> {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("xsbjje", l.getBjje());
                jsonObject1.put("yqrq", l.getYqrq());
                jsonObject1.put("dm", l.getDm());
                jsonObject1.put("qm", l.getQm());
                jsonObject1.put("yqr", l.getYqr());
                list3.add(jsonObject1);
            });
            if (list2.size() == 0) {
                if ("2".equals(tbXyd2.getXbjdjlx())) {
                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("xsbjje", tbXyd2.getXbjdj() != null ? tbXyd2.getXbjdj() : 0);
                    jsonObject1.put("yqrq", new Date());
                    jsonObject1.put("qm", tbXyd2.getXbjxx() != null ? tbXyd2.getXbjxx() : "0000000000");
                    list3.add(jsonObject1);
                    b1 = true;
                }
                if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(tbXyd2.getXbjdjlx())) {
                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("xsbjje", tbXyd2.getXxsbj() != null ? tbXyd2.getXxsbj() : 0);
                    jsonObject1.put("yqrq", new Date());
                    jsonObject1.put("qm", tbXyd2.getXbjxx() != null ? tbXyd2.getXbjxx() : "0000000000");
                    list3.add(jsonObject1);
                    b1 = true;
                }
            }
            formDatamap.put("xsbjzbd", list3);
        }

        //发送补交通知
        if (b1) {
            String context = (ding != null ? ding.getName() : "") + "恢复了包含补交定金且补交时间不规范的订单，请注意跟进";
            context += "\n订单编号:" + tbXyd2.getXddbh();
            context += "\n房东:" + tbXyd2.getXfd();
            context += "\n客户:" + tbXyd2.getXzk();
            context += "\n进场时间:" + DateFormatConfig.df1(tbXyd2.getXjctime());
            context += "\n退场时间:" + DateFormatConfig.df1(tbXyd2.getXtctime());
            TbVilla villa1 = weiLianDdXcxService.queryTbVillaById(tbXyd2.getXbsmc());
            context += "\n别墅:" + (villa1 != null ? villa1.getVname() : "");

            List<String> list = new ArrayList<>();
            String wook = "https://oapi.dingtalk.com/robot/send?access_token=e29f36dcbd922722c9df0976e46de574e80ea96a1c788d96c9f1d7ef89865484";
            String se = "SEC5511d87046d65b5495122c9309fe913eb8e13970bbfca00ae7618807c2bc54f5";
            DingDingUtil.sendMsg(wook, se, context, list, false);

            String contexts = (ding != null ? ding.getName() : "") + "恢复了删除订单，且补交定金不规范，请前往办公平台订单系统跟进改正";
            contexts += "\n\n订单编号:" + tbXyd2.getXddbh();
            contexts += "\n房东:" + tbXyd2.getXfd();
            contexts += "\n客户:" + tbXyd2.getXzk();
            contexts += "\n进场时间:" + DateFormatConfig.df1(tbXyd2.getXjctime());
            contexts += "\n退场时间:" + DateFormatConfig.df1(tbXyd2.getXtctime());
            contexts += "\n别墅:" + (villa1 != null ? villa1.getVname() : "");
            contexts += "\n\n送达时间：" + DateFormatConfig.df1(new Date());

            try {
                DingDBConfig.sendGztzText(dingkey, userid, contexts);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }


        //增值定金
        formDatamap.put("xyszzdj", tbXyd2.getXyszzdj() != null && !"".equals(tbXyd2.getXyszzdj()) ? tbXyd2.getXyszzdj() : 0);

        WlgbBdjl wlgbBdjl = wlgbBdjlService.queryByXidAndBz(tbXyd2.getXid(), "下单表单提交");

        String context = (employee != null ? employee.getName() : "") + "正在恢复订单";
        context += "\n订单编号：" + tbXyd2.getXddbh();
        context += "\n房东：" + tbXyd2.getXfd();
        context += "\n客户：" + tbXyd2.getXzk();
        String web = "https://oapi.dingtalk.com/robot/send?access_token=1c0e9928ce9ec6ab12f98c53f643530ef4a802b9e7716cac34bc56253ae61bd3";
        String serct = "SEC76deaa3f937741cba678666c3c87e8f5bc47f39f939885116c61628d4f972026";
        DingDingUtil.sendMsg(web, serct, context, null, false);

        if (wlgbBdjl != null) {
            xgBdSl(formDatamap, wlgbBdjl.getSlid(), userid);
        } else {
            DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbXyd2.getXsendder());
            GatewayResult gatewayResult = xzBd(employee1 != null ? employee1.getUserid() : userid, formDatamap, "下单", 1, "");
        }

        //判断是不是老订单
        Boolean b = true;
        Integer ddCz = weiLianDdXcxService.queryDdCz(tbXyd2.getXid());
        if (ddCz > 0) {
            List<String> list2 = new ArrayList<>();
            list2.add("15349026426046931");
            list2.add("15531676330599999");
            list2.add("159909317438346");
            for (String l : list2) {
                if (l.equals(userid)) {
                    b = false;
                    break;
                }
            }
        }

        if (b) {
            String fdUserId = employee != null ? employee.getUserid() : "";
            DingdingEmployee employee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
            String userList = fdUserId;
            if (employee1 != null) {
                if (!fdUserId.equals(employee1.getUserid())) {
                    if (!"".equals(fdUserId)) {
                        userList += "," + employee1.getUserid();
                    } else {
                        userList += employee1.getUserid();
                    }
                }
            }

            //不在范围内的店长发送小程序的工作通知
            if (userList != null && !"".equals(userList) && userList.length() > 0) {
                String contexts = (ding != null ? ding.getName() : "") + "恢复了删除订单！";
                contexts += "\n\n别墅：" + villa.getVname();
                contexts += "\n\n房东：" + tbXyd2.getXfd();
                contexts += "\n\n店长：" + employee1.getName();
                contexts += "\n\n送达时间：";
                try {
                    DingDBConfig.sendGztz1(userList, dingkey, "电子协议单",
                            contexts, upload);
                } catch (ApiException e) {
                    e.printStackTrace();
                }
            }
            //加盟商播报
            WlgbJmsbb wlgbJmsbb = weiLianDdXcxService.queryJmsBbByVid(villa.getVid());
            if (wlgbJmsbb != null) {
                String contexts = (ding != null ? ding.getName() : "") + "恢复了删除订单！";
                contexts += "\n\n别墅：" + villa.getVname();
                contexts += "\n\n房东：" + tbXyd2.getXfd();
                contexts += "\n\n店长：" + employee1.getName();
                contexts += "\n\n送达时间：";
                try {
                    DingDBConfig.sendGztz1(wlgbJmsbb.getBbrid(), dingkey, "电子协议单",
                            contexts, upload);
                } catch (ApiException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 删除表单
     *
     * @param slid 实例id
     */
    public GatewayResult scBdSl(String slid) {
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.scBdSl(token, ydAppkey, "012412221639786136545", slid);
        } catch (Exception e) {
            gatewayResult = new GatewayResult();
            e.printStackTrace();
        }

        return gatewayResult;
    }


    /**
     * 发送新增表单
     *
     * @param userId 收取人id
     * @param map    表单内容
     * @param lx     表单类型
     */
    public GatewayResult xzBd(String userId, Map<String, Object> map, String lx, Integer type, String formUuId) {
        JSONObject json = new JSONObject(map);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        if (type == 1) {
            YdBd ydBd = weiLianDdXcxService.queryYdBdByBz(lx);
            formUuId = ydBd.getFormid();
        }
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, json.toJSONString(), userId, formUuId);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return gatewayResult;
    }

    /**
     * 生成电子协议单
     *
     * @param xyd 协议单数据
     */
    private Map<String, Object> dzXyd(TbXyd xyd) throws Exception {
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(xyd.getXbsmc());
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(villa.getPid());
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy年MM月dd日");
        String zpTime = "进场：" + DateFormatConfig.df1(xyd.getXjctime()) + "<br />退场：" + DateFormatConfig.df1(xyd.getXtctime());
        //除场地费之外的其他费用
        double v = (xyd.getXzrcsfy() != null ? xyd.getXzrcsfy() : 0) + (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXskze() != null ? xyd.getXskze() : 0) + (xyd.getXbxzje() != null ? xyd.getXbxzje() : 0) + (xyd.getXztze() != null ? xyd.getXztze() : 0) + (xyd.getXhpsfy() != null ? xyd.getXhpsfy() : 0) + (xyd.getXjbszje() != null ? xyd.getXjbszje() : 0);
        Map<String, Object> map = new HashMap<>();
        //苏州协议单设置
        map.put("cityType", "苏州".equals(villa.getCity()) ? "1" : "0");
        //订单编号
        map.put("ddbh", xyd.getXddbh());
        //别墅名字
        map.put("bsName", villa.getVname());
        //老板姓名
        map.put("lbxm", xyd.getXlbjf());
        //房东姓名
        map.put("fdName", xyd.getXfd());
        //房东电话
        map.put("fdTelphone", xyd.getXfddh());
        //租客姓名
        map.put("zkName", xyd.getXzk());
        //租客电话
        map.put("zkTelphone", xyd.getXzkdh());
        //租客身份证号码
        map.put("zksfz", xyd.getXzksfz());
        TbKhlyxz khlyxz = weiLianService.queryKhLyXzByLidOrLname(xyd.getXtdxz());
        //公司名称（性质）
        map.put("dwmc", xyd.getXdwmc() + (khlyxz != null ? "(" + khlyxz.getLname() + ")" : ""));
        TbKhlyxz tbKhlyxz = weiLianService.queryKhLyXzByLidOrLname(xyd.getXkhly());
        //客户来源
        map.put("khly", (tbKhlyxz != null ? tbKhlyxz.getLname() : ""));
        //租赁时间（进场时间与退场时间）
        map.put("zpTime", zpTime);
        //进场时间
        map.put("jxtime", "进场:" + DateFormatConfig.df1(xyd.getXjctime()));
        //退场时间
        map.put("tctime", "退场:" + DateFormatConfig.df1(xyd.getXtctime()));
        //人数
        map.put("rs", xyd.getXrs());
        //超出人数加收
        map.put("ccMoney", xyd.getXcudrfy());
        //支付宝订单号后六位
        String hlw = "";
        if (xyd.getXzzhlw() != null && !"".equals(xyd.getXzzhlw()) && xyd.getXzzhlw().length() > 0) {
            if (xyd.getXzzhlw().length() > 6) {
                hlw = xyd.getXzzhlw().substring(xyd.getXzzhlw().length() - 6);
            } else {
                hlw = xyd.getXzzhlw();
            }
        }
        //支付宝订单号后两位
        map.put("zfbddh", hlw);
        //支付宝转账定金时间
        map.put("zfbzzTime", xyd.getXzzsj() != null ? DateFormatConfig.df1(xyd.getXzzsj()) : "");
        //场地租赁费
        map.put("qkje", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0));
        //增值定金
        map.put("xyszzdj", (!xyd.getXyszzdj().isEmpty()) ? Double.parseDouble(xyd.getXyszzdj()) : 0);
        //增值费用补交
        map.put("xzzfybj", xyd.getXzzfybj() != null ? xyd.getXzzfybj() : 0);
        //剧本杀
        map.put("xjbs", xyd.getXjbszje() != null ? xyd.getXjbszje() : 0);
        //已收场地费定金=已收定金+场地费租金补交-增值定金
        double ysdj = (xyd.getXysdj() != null ? xyd.getXysdj() : 0) + (xyd.getXcdzjbj() != null ? xyd.getXcdzjbj() : 0) - (xyd.getXyszzdj() != null && !"".equals(xyd.getXyszzdj()) ? Double.parseDouble(xyd.getXyszzdj()) : 0);
        if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(xyd.getXdjtype())) {
            Double xwsywdddsyje = xyd.getXwsywdddsyje();
            if (xwsywdddsyje != null) {
                ysdj += xwsywdddsyje;
            }
        }
        map.put("xyscdfdj", ysdj);
        //代码
        String dm = "";
        if (xyd.getXdm() != null && !"".equals(xyd.getXdm()) && xyd.getXdm().length() > 0) {
            if (xyd.getXdm().length() > 6) {
                dm = xyd.getXdm().substring(0, 6);
            } else {
                dm = xyd.getXdm();
            }
        }
        map.put("dm", dm);
        //付款码
        String fkm = "";
        if (xyd.getXfkm() != null && !"".equals(xyd.getXfkm()) && xyd.getXfkm().length() > 0) {
            if (xyd.getXfkm().length() > 2) {
                fkm = xyd.getXfkm().substring(0, 2);
            } else {
                fkm = xyd.getXfkm();
            }
        }
        map.put("zfbfkm", fkm);
        //订餐+烧烤
        map.put("dctczjsktczj", (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXskze() != null ? xyd.getXskze() : 0));
        map.put("xdcze", (xyd.getXdcze() != null ? xyd.getXdcze() : 0));
        map.put("xskze", (xyd.getXskze() != null ? xyd.getXskze() : 0));
        //保险
        map.put("xbxzje", xyd.getXbxzje() != null ? xyd.getXbxzje() : 0);
        //策划
        map.put("zttczj", xyd.getXztze() != null ? xyd.getXztze() : 0);
        //轰趴师
        map.put("sfhps", xyd.getXhpsfy() != null ? xyd.getXhpsfy() : 0);
        //全款金额
        map.put("qkje3", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) + v);
        //完成条件后优惠至
        map.put("qkje2", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) - (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) > 0 ? (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0) - (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) : 0);
        //转发
        map.put("zgdst", xyd.getXzfts() != null ? xyd.getXzfts() : 0);
        //好评
        map.put("hpdst", xyd.getXhpts() != null ? xyd.getXhpts() : 0);
        //集赞
        map.put("jzst", xyd.getXjzts() != null ? xyd.getXjzts() : 0);
        //消费券
        map.put("xspyhq", xyd.getXspyhq() != null ? xyd.getXspyhq() : 0);
        //值班店长
        map.put("zbdzxm", dingdingEmployee != null ? dingdingEmployee.getName() : "");
        //值班店长电话
        map.put("zbdhxm", dingdingEmployee != null ? dingdingEmployee.getMobile() : "");
        //是否现场成交
        map.put("sfxccj", xyd.getXisxzcj() != null && xyd.getXisxzcj() == 0 ? "是" : "否");
        //是否泳池单
        map.put("sfycd", "否");
        //校代姓名
        map.put("xxdxm", xyd.getXxdxm());
        //赠送特权会员卡
        map.put("zstkhyk", xyd.getXzshyksl() != null ? xyd.getXzshyksl() : 0);
        //备注
        map.put("bzxx", xyd.getXsxbz());
        //甲方签名
        map.put("jfxm", xyd.getXisxzcj() != null && xyd.getXisxzcj() == 0 ? xyd.getXcjr() : xyd.getXfd());
        //乙方签名
        map.put("yfxm", xyd.getXzk());
        //日期
        map.put("rq", xyd.getXgsj() != null ? formatter1.format(xyd.getXgsj()) : formatter1.format(xyd.getXkhtjtime()));
        //别墅所在城市
        map.put("city", villa.getCity());
        log.info("*****进入xydSaveTask异步请求*准备生成图片了333******{}", map);
        //模板
        String tempName = FileConfig.getFileAbsolutePath2("static" + File.separator + "template.html");
        String context = PDFUtil.freeMarkerRender(map, tempName);
        String id = "DZHC" + DateFormatConfig.df2(new Date()) + IdConfig.uuId();
        String pdf = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
        String png = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".png");
        File newPdf = new File(pdf);
        if (!newPdf.exists()) {
            log.error("文件不存在pdf: {}", pdf);
            log.error("文件不存在png: {}", png);
            log.error("文件不存在context: {}", context);
        }
        Map<String, Object> map1 = new HashMap<>();
        try {
            // 生成pdf
            PDFUtil.createPdf(context, newPdf.getPath(),true);
            // 生成图片
            PDFUtil.pdfToImg(newPdf.getPath(), 1, png);
            log.info("*****进入xydSaveTask异步请求*生成图片成功******{}", newPdf.getPath());
            map1.put("png", id + ".png");
            // 删除pdf临时文件
            if (!newPdf.delete()) {
                log.warn("删除pdf临时文件报错: {}", newPdf.getPath());
            }
        } catch (Exception e) {
            log.error("生成PDF/png文件失败: ", e);
        }
        return map1;
    }

    /**
     * 修改表单实例
     *
     * @param id     要更新的表单数据ID
     * @param map    表单内容
     * @param userid 用户id
     */
    public GatewayResult xgBdSl(Map<String, Object> map, String id, String userid) {
        JSONObject json = new JSONObject(map);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            log.info("*****进入xydSaveTask异步请求*修改表单实例之获取token 失败******{}", e);
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, id, json.toJSONString());
        } catch (Exception e) {
            log.info("*****进入xydSaveTask异步请求*修改表单实例失败******{}", e);
        }
        return gatewayResult;
    }

    /**
     * 机器人发送策划群
     *
     * @param tbXyd 协议单
     * @param title 标题
     */
    public void ch(TbXyd tbXyd, String title) {
        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        TbKhlyxz khlyxz = weiLianService.queryKhLyXzByLidOrLname(tbXyd.getXtdxz());
        DingdingEmployee dingdingEmployee = null;
        if (tbVilla.getPid() != null && !"null".equalsIgnoreCase(tbVilla.getPid()) && !"".equals(tbVilla.getPid())) {
            dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tbVilla.getPid());
        }
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String context = "#### <font color=#ff0303 size=7>" + title + "</font>";
        context += "\n\n><font size=4>房东</font>:<" + tbXyd.getXfd() + ">";
        context += "\n\n><font size=4>别墅</font>:<<font color=#ff0303>" + tbVilla.getVname() + "</font>>";
        context += "\n\n><font size=4>场次</font>:<<font color=#ff0303>进场：" + df2.format(tbXyd.getXjctime()) + "</font>>";
        context += "\n\n><font size=4>场次</font>:<<font color=#ff0303>退场：" + df2.format(tbXyd.getXtctime()) + "</font>>";
        context += "\n\n><font size=4>性质</font>:<<font color=#ff0303>" + tbXyd.getXdwmc() + "(" + khlyxz.getLname() + ")</font>>";
        context += "\n\n><font size=4>策划金额</font>:<<font color=#ff0303>" + (tbXyd.getXztze() != null ? tbXyd.getXztze() : 0) + "</font>>";
        context += "\n\n><font size=4>策划经理</font>:<<font color=#ff0303>" + (tbXyd.getXchjl() != null ? tbXyd.getXchjl() : "") + "</font>>";
        context += "\n\n><font size=4>策划成交人</font>:<" + (tbXyd.getXchcjr() != null ? tbXyd.getXchcjr() : "") + ">";
        context += "\n\n><font size=4>策划辅助成交人</font>:<" + (tbXyd.getXchfzcjr() != null ? tbXyd.getXchfzcjr() : "") + ">";
        if (dingdingEmployee != null) {
            context += "\n\n><font size=4>值班店长</font>:<" + dingdingEmployee.getName() + ">";
        }
        context += "\n\n><font size=4>备注信息</font>:<" + tbXyd.getXsxbz() + ">";
        Calendar calendar1 = Calendar.getInstance();
        context += "\n\n> ###### " + df2.format(calendar1.getTime()) + "     [发送]";
        String wook = jqrConfig.getChwebhook();
        String se = null;
        //线下
        se = jqrConfig.getChkey();
        DingDingUtil.sendMark(wook, se, title, context, null, false);
    }

    /**
     * 机器人发送轰趴师群
     *
     * @param tbXyd   ---协议单
     * @param context 发送内容
     */
    public void hps(TbXyd tbXyd, String context, Dingkey dingkey, String dingpan) throws Exception {
        List<String> list = new ArrayList<>();
        String wook = jqrConfig.getHpswebhook();
        String se = null;
        //线上
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
        if (villa != null) {
            list = weiLianService.queryHpsAt(villa.getCity());
        }
        DingQunSend.sendQun1(dingkey, dingpan, "chatde3a0d1446cf4b2f99be143625faded5");
        DingDingUtil.sendMsg(wook, se, context, list, false);
    }

    /**
     * 剧本杀机器人播报轰趴师群
     *
     * @param context 发送内容
     */
    public void jbsBb(String context, Dingkey dingkey, String dingpan) throws Exception {
        List<String> list = new ArrayList<>();
        String wook = jqrConfig.getHpswebhook();
        String se = null;
        //线下
//        se = jqrConfig.getHpskey();
//        DingQunSend.sendQun1(dingkey, dingpan, "chate5bd183f6a0ebdd18e057550c487e5cc");
        //线上
        wook = jqrConfig.getHpswebhook();
        DingQunSend.sendQun1(dingkey, dingpan, "chatde3a0d1446cf4b2f99be143625faded5");
        DingDingUtil.sendMsg(wook, se, context, list, false);
    }

    /**
     * 电子协议单处理
     *
     * @param tbXyd 协议单对象
     * @return 协议单地址
     */
    public String dzXydCl(TbXyd tbXyd) throws Exception {
        Dzxyd dzxyd = dzxydService.queryByXid(tbXyd.getXid());
        String upload;
        if (dzxyd != null) {
            //电子协议单
            upload = dzxyd.getUrl();
        } else {
            String bddz = "D:\\tomcat9\\webapps\\weilian3\\";
            File file1 = new File(bddz + tbXyd.getXimagepath());
            //判断图片存在不存在,如果不存在将重新生成电子协议单
            if (!file1.exists()) {
                dzXyd(tbXyd);
            }
            File file = new File(bddz + tbXyd.getXimagepath());
            FileInputStream fileInputStream = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
            fileInputStream.close();
            upload = ossFileService.upload(multipartFile);
            if (upload != null && !"".equals(upload)) {
                upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
            }
            Dzxyd dzxyd1 = new Dzxyd();
            dzxyd1.setUrl(upload);
            dzxyd1.setTip(IdConfig.uuId());
            dzxyd1.setXid(tbXyd.getXid());
            dzxyd1.setTime(new Date());
            dzxydService.save(dzxyd1);
        }
        return upload;
    }

}
