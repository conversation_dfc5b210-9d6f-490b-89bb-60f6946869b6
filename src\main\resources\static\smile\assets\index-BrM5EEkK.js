function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["./pages-index-index.UeaAyDEp.js","./index-CQLtismy.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={},t=function(t,n,o){let r=Promise.resolve();if(n&&n.length>0){const t=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),s=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));r=Promise.all(n.map((n=>{if(n=function(e,t){return new URL(e,t).href}(n,o),n in e)return;e[n]=!0;const r=n.endsWith(".css"),i=r?'[rel="stylesheet"]':"";if(!!o)for(let e=t.length-1;e>=0;e--){const o=t[e];if(o.href===n&&(!r||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${n}"]${i}`))return;const a=document.createElement("link");return a.rel=r?"stylesheet":"modulepreload",r||(a.as="script",a.crossOrigin=""),a.href=n,s&&a.setAttribute("nonce",s),document.head.appendChild(a),r?new Promise(((e,t)=>{a.addEventListener("load",e),a.addEventListener("error",(()=>t(new Error(`Unable to preload CSS for ${n}`))))})):void 0})))}return r.then((()=>t())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))};
/**
* @vue/shared v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
function n(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const o={},r=[],i=()=>{},s=()=>!1,a=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),l=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},d=Object.prototype.hasOwnProperty,f=(e,t)=>d.call(e,t),h=Array.isArray,p=e=>"[object Map]"===x(e),g=e=>"[object Set]"===x(e),m=e=>"function"==typeof e,v=e=>"string"==typeof e,y=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,b=e=>(_(e)||m(e))&&m(e.then)&&m(e.catch),w=Object.prototype.toString,x=e=>w.call(e),S=e=>"[object Object]"===x(e),T=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,C=n(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),k=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},E=/-(\w)/g,L=k((e=>e.replace(E,((e,t)=>t?t.toUpperCase():"")))),O=/\B([A-Z])/g,M=k((e=>e.replace(O,"-$1").toLowerCase())),$=k((e=>e.charAt(0).toUpperCase()+e.slice(1))),A=k((e=>e?`on${$(e)}`:"")),P=(e,t)=>!Object.is(e,t),I=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},B=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},N=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let R;const F=()=>R||(R="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function j(e){if(h(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?V(o):j(o);if(r)for(const e in r)t[e]=r[e]}return t}if(v(e)||_(e))return e}const H=/;(?![^(]*\))/g,D=/:([^]+)/,W=/\/\*[^]*?\*\//g;function V(e){const t={};return e.replace(W,"").split(H).forEach((e=>{if(e){const n=e.split(D);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function z(e){let t="";if(v(e))t=e;else if(h(e))for(let n=0;n<e.length;n++){const o=z(e[n]);o&&(t+=o+" ")}else if(_(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const q=n("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function U(e){return!!e||""===e}const X=e=>v(e)?e:null==e?"":h(e)||_(e)&&(e.toString===w||!m(e.toString))?JSON.stringify(e,Y,2):String(e),Y=(e,t)=>t&&t.__v_isRef?Y(e,t.value):p(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[K(t,o)+" =>"]=n,e)),{})}:g(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>K(e)))}:y(t)?K(t):!_(t)||h(t)||S(t)?t:String(t),K=(e,t="")=>{var n;return y(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},J=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view","location-picker","location-view"].map((e=>"uni-"+e)),G=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map((e=>"uni-"+e)),Q=["list-item"].map((e=>"uni-"+e));function Z(e){if(-1!==Q.indexOf(e))return!1;const t="uni-"+e.replace("v-uni-","");return-1!==J.indexOf(t)||-1!==G.indexOf(t)}const ee=/^([a-z-]+:)?\/\//i,te=/^data:.*,.*/;function ne(e){return 0===e.indexOf("/")}function oe(e){return ne(e)?e:"/"+e}function re(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}let ie;function se(){return ie||(ie=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),ie)}function ae(e){return e&&(e.appContext?e.proxy:e)}function le(e){if(!e)return;let t=e.type.name;for(;t&&Z(M(t));)t=(e=e.parent).type.name;return e.proxy}function ce(e){return 1===e.nodeType}function ue(e){const t=se();if(t&&t.UTSJSONObject&&e instanceof t.UTSJSONObject){const n={};return t.UTSJSONObject.keys(e).forEach((t=>{n[t]=e[t]})),j(n)}if(e instanceof Map){const t={};return e.forEach(((e,n)=>{t[n]=e})),j(t)}if(v(e))return V(e);if(h(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=v(o)?V(o):ue(o);if(r)for(const e in r)t[e]=r[e]}return t}return j(e)}function de(e){let t="";const n=se();if(n&&n.UTSJSONObject&&e instanceof n.UTSJSONObject)n.UTSJSONObject.keys(e).forEach((n=>{e[n]&&(t+=n+" ")}));else if(e instanceof Map)e.forEach(((e,n)=>{e&&(t+=n+" ")}));else if(h(e))for(let o=0;o<e.length;o++){const n=de(e[o]);n&&(t+=n+" ")}else t=z(e);return t.trim()}function fe(e){return L(e.substring(5))}const he=re((e=>{e=e||(e=>e.tagName.startsWith("UNI-"));const t=HTMLElement.prototype,n=t.setAttribute;t.setAttribute=function(t,o){if(t.startsWith("data-")&&e(this)){(this.__uniDataset||(this.__uniDataset={}))[fe(t)]=o}n.call(this,t,o)};const o=t.removeAttribute;t.removeAttribute=function(t){this.__uniDataset&&t.startsWith("data-")&&e(this)&&delete this.__uniDataset[fe(t)],o.call(this,t)}}));function pe(e){return c({},e.dataset,e.__uniDataset)}const ge=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function me(e){return{passive:e}}function ve(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:pe(e),offsetTop:n,offsetLeft:o}}function ye(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function _e(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=ye(e[n])}catch(o){t[n]=e[n]}})),t}const be=/\+/g;function we(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(be," ");let r=e.indexOf("="),i=ye(r<0?e:e.slice(0,r)),s=r<0?null:ye(e.slice(r+1));if(i in t){let e=t[i];h(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}class xe{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Se=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onShareChat","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Te=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onShareChat","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Ce=[];const ke=re(((e,t)=>t(e))),Ee=function(){};Ee.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t||o[i]._id===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var Le=Ee;const Oe={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function Me(e,t,n){if(v(t)&&t.startsWith("@")){let r=e[t.replace("@","")]||t;switch(n){case"titleColor":r="black"===r?"#000000":"#ffffff";break;case"borderStyle":r=(o=r)&&o in Oe?Oe[o]:o}return r}var o;return t}function $e(e,t={},n="light"){const o=t[n],r={};return void 0!==o&&e?(Object.keys(e).forEach((i=>{const s=e[i];r[i]=S(s)?$e(s,t,n):h(s)?s.map((e=>"object"==typeof e?$e(e,t,n):Me(o,e))):Me(o,s,i)})),r):e}
/**
* @dcloudio/uni-h5-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ae,Pe;class Ie{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Ae,!e&&Ae&&(this.index=(Ae.scopes||(Ae.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=Ae;try{return Ae=this,e()}finally{Ae=t}}}on(){Ae=this}off(){Ae=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function Be(e){return new Ie(e)}class Ne{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=Ae){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,Ve();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),ze()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=He,t=Pe;try{return He=!0,Pe=this,this._runnings++,Re(this),this.fn()}finally{Fe(this),this._runnings--,Pe=t,He=e}}stop(){var e;this.active&&(Re(this),Fe(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function Re(e){e._trackId++,e._depsLength=0}function Fe(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)je(e.deps[t],e);e.deps.length=e._depsLength}}function je(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let He=!0,De=0;const We=[];function Ve(){We.push(He),He=!1}function ze(){const e=We.pop();He=void 0===e||e}function qe(){De++}function Ue(){for(De--;!De&&Ye.length;)Ye.shift()()}function Xe(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&je(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const Ye=[];function Ke(e,t,n){qe();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&Ye.push(o.scheduler)))}Ue()}const Je=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},Ge=new WeakMap,Qe=Symbol(""),Ze=Symbol("");function et(e,t,n){if(He&&Pe){let t=Ge.get(e);t||Ge.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=Je((()=>t.delete(n)))),Xe(Pe,o)}}function tt(e,t,n,o,r,i){const s=Ge.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&h(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||!y(n)&&n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":h(e)?T(n)&&a.push(s.get("length")):(a.push(s.get(Qe)),p(e)&&a.push(s.get(Ze)));break;case"delete":h(e)||(a.push(s.get(Qe)),p(e)&&a.push(s.get(Ze)));break;case"set":p(e)&&a.push(s.get(Qe))}qe();for(const l of a)l&&Ke(l,4);Ue()}const nt=n("__proto__,__v_isRef,__isVue"),ot=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(y)),rt=it();function it(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Ut(this);for(let t=0,r=this.length;t<r;t++)et(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Ut)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Ve(),qe();const n=Ut(this)[t].apply(this,e);return Ue(),ze(),n}})),e}function st(e){const t=Ut(this);return et(t,0,e),t.hasOwnProperty(e)}class at{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?Rt:Nt:r?Bt:It).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=h(e);if(!o){if(i&&f(rt,t))return Reflect.get(rt,t,n);if("hasOwnProperty"===t)return st}const s=Reflect.get(e,t,n);return(y(t)?ot.has(t):nt(t))?s:(o||et(e,0,t),r?s:Zt(s)?i&&T(t)?s:s.value:_(s)?o?Ht(s):jt(s):s)}}class lt extends at{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=Vt(r);if(zt(n)||Vt(n)||(r=Ut(r),n=Ut(n)),!h(e)&&Zt(r)&&!Zt(n))return!t&&(r.value=n,!0)}const i=h(e)&&T(t)?Number(t)<e.length:f(e,t),s=Reflect.set(e,t,n,o);return e===Ut(o)&&(i?P(n,r)&&tt(e,"set",t,n):tt(e,"add",t,n)),s}deleteProperty(e,t){const n=f(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&tt(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return y(t)&&ot.has(t)||et(e,0,t),n}ownKeys(e){return et(e,0,h(e)?"length":Qe),Reflect.ownKeys(e)}}class ct extends at{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const ut=new lt,dt=new ct,ft=new lt(!0),ht=e=>e,pt=e=>Reflect.getPrototypeOf(e);function gt(e,t,n=!1,o=!1){const r=Ut(e=e.__v_raw),i=Ut(t);n||(P(t,i)&&et(r,0,t),et(r,0,i));const{has:s}=pt(r),a=o?ht:n?Kt:Yt;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function mt(e,t=!1){const n=this.__v_raw,o=Ut(n),r=Ut(e);return t||(P(e,r)&&et(o,0,e),et(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function vt(e,t=!1){return e=e.__v_raw,!t&&et(Ut(e),0,Qe),Reflect.get(e,"size",e)}function yt(e){e=Ut(e);const t=Ut(this);return pt(t).has.call(t,e)||(t.add(e),tt(t,"add",e,e)),this}function _t(e,t){t=Ut(t);const n=Ut(this),{has:o,get:r}=pt(n);let i=o.call(n,e);i||(e=Ut(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?P(t,s)&&tt(n,"set",e,t):tt(n,"add",e,t),this}function bt(e){const t=Ut(this),{has:n,get:o}=pt(t);let r=n.call(t,e);r||(e=Ut(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&tt(t,"delete",e,void 0),i}function wt(){const e=Ut(this),t=0!==e.size,n=e.clear();return t&&tt(e,"clear",void 0,void 0),n}function xt(e,t){return function(n,o){const r=this,i=r.__v_raw,s=Ut(i),a=t?ht:e?Kt:Yt;return!e&&et(s,0,Qe),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function St(e,t,n){return function(...o){const r=this.__v_raw,i=Ut(r),s=p(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?ht:t?Kt:Yt;return!t&&et(i,0,l?Ze:Qe),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Tt(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Ct(){const e={get(e){return gt(this,e)},get size(){return vt(this)},has:mt,add:yt,set:_t,delete:bt,clear:wt,forEach:xt(!1,!1)},t={get(e){return gt(this,e,!1,!0)},get size(){return vt(this)},has:mt,add:yt,set:_t,delete:bt,clear:wt,forEach:xt(!1,!0)},n={get(e){return gt(this,e,!0)},get size(){return vt(this,!0)},has(e){return mt.call(this,e,!0)},add:Tt("add"),set:Tt("set"),delete:Tt("delete"),clear:Tt("clear"),forEach:xt(!0,!1)},o={get(e){return gt(this,e,!0,!0)},get size(){return vt(this,!0)},has(e){return mt.call(this,e,!0)},add:Tt("add"),set:Tt("set"),delete:Tt("delete"),clear:Tt("clear"),forEach:xt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=St(r,!1,!1),n[r]=St(r,!0,!1),t[r]=St(r,!1,!0),o[r]=St(r,!0,!0)})),[e,n,t,o]}const[kt,Et,Lt,Ot]=Ct();function Mt(e,t){const n=t?e?Ot:Lt:e?Et:kt;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(f(n,o)&&o in t?n:t,o,r)}const $t={get:Mt(!1,!1)},At={get:Mt(!1,!0)},Pt={get:Mt(!0,!1)},It=new WeakMap,Bt=new WeakMap,Nt=new WeakMap,Rt=new WeakMap;function Ft(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>x(e).slice(8,-1))(e))}function jt(e){return Vt(e)?e:Dt(e,!1,ut,$t,It)}function Ht(e){return Dt(e,!0,dt,Pt,Nt)}function Dt(e,t,n,o,r){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=Ft(e);if(0===s)return e;const a=new Proxy(e,2===s?o:n);return r.set(e,a),a}function Wt(e){return Vt(e)?Wt(e.__v_raw):!(!e||!e.__v_isReactive)}function Vt(e){return!(!e||!e.__v_isReadonly)}function zt(e){return!(!e||!e.__v_isShallow)}function qt(e){return Wt(e)||Vt(e)}function Ut(e){const t=e&&e.__v_raw;return t?Ut(t):e}function Xt(e){return Object.isExtensible(e)&&B(e,"__v_skip",!0),e}const Yt=e=>_(e)?jt(e):e,Kt=e=>_(e)?Ht(e):e;class Jt{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Ne((()=>e(this._value)),(()=>Qt(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Ut(this);return e._cacheable&&!e.effect.dirty||!P(e._value,e._value=e.effect.run())||Qt(e,4),Gt(e),e.effect._dirtyLevel>=2&&Qt(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function Gt(e){var t;He&&Pe&&(e=Ut(e),Xe(Pe,null!=(t=e.dep)?t:e.dep=Je((()=>e.dep=void 0),e instanceof Jt?e:void 0)))}function Qt(e,t=4,n){const o=(e=Ut(e)).dep;o&&Ke(o,t)}function Zt(e){return!(!e||!0!==e.__v_isRef)}function en(e){return nn(e,!1)}function tn(e){return nn(e,!0)}function nn(e,t){return Zt(e)?e:new on(e,t)}class on{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Ut(e),this._value=t?e:Yt(e)}get value(){return Gt(this),this._value}set value(e){const t=this.__v_isShallow||zt(e)||Vt(e);e=t?e:Ut(e),P(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Yt(e),Qt(this,4))}}const rn={get:(e,t,n)=>{return Zt(o=Reflect.get(e,t,n))?o.value:o;var o},set:(e,t,n,o)=>{const r=e[t];return Zt(r)&&!Zt(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function sn(e){return Wt(e)?e:new Proxy(e,rn)}function an(e,t,n,o){try{return o?e(...o):e()}catch(r){cn(r,t,n)}}function ln(e,t,n,o){if(m(e)){const r=an(e,t,n,o);return r&&b(r)&&r.catch((e=>{cn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(ln(e[i],t,n,o));return r}function cn(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void an(s,null,10,[e,r,i])}un(e,n,r,o)}function un(e,t,n,o=!0){console.error(e)}let dn=!1,fn=!1;const hn=[];let pn=0;const gn=[];let mn=null,vn=0;const yn=Promise.resolve();let _n=null;function bn(e){const t=_n||yn;return e?t.then(this?e.bind(this):e):t}function wn(e){hn.length&&hn.includes(e,dn&&e.allowRecurse?pn+1:pn)||(null==e.id?hn.push(e):hn.splice(function(e){let t=pn+1,n=hn.length;for(;t<n;){const o=t+n>>>1,r=hn[o],i=Cn(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),xn())}function xn(){dn||fn||(fn=!0,_n=yn.then(En))}function Sn(e,t,n=(dn?pn+1:0)){for(;n<hn.length;n++){const t=hn[n];if(t&&t.pre){if(e&&t.id!==e.uid)continue;hn.splice(n,1),n--,t()}}}function Tn(e){if(gn.length){const e=[...new Set(gn)].sort(((e,t)=>Cn(e)-Cn(t)));if(gn.length=0,mn)return void mn.push(...e);for(mn=e,vn=0;vn<mn.length;vn++)mn[vn]();mn=null,vn=0}}const Cn=e=>null==e.id?1/0:e.id,kn=(e,t)=>{const n=Cn(e)-Cn(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function En(e){fn=!1,dn=!0,hn.sort(kn);try{for(pn=0;pn<hn.length;pn++){const e=hn[pn];e&&!1!==e.active&&an(e,null,14)}}finally{pn=0,hn.length=0,Tn(),dn=!1,_n=null,(hn.length||gn.length)&&En()}}function Ln(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||o;let i=n;const s=t.startsWith("update:"),a=s&&t.slice(7);if(a&&a in r){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:s}=r[e]||o;s&&(i=n.map((e=>v(e)?e.trim():e))),t&&(i=n.map(N))}let l,c=r[l=A(t)]||r[l=A(L(t))];!c&&s&&(c=r[l=A(M(t))]),c&&ln(c,e,6,On(e,c,i));const u=r[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,ln(u,e,6,On(e,u,i))}}function On(e,t,n){if(1!==n.length)return n;if(m(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&f(o,"type")&&f(o,"timeStamp")&&f(o,"target")&&f(o,"currentTarget")&&f(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function Mn(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!m(e)){const o=e=>{const n=Mn(e,t,!0);n&&(a=!0,c(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(h(i)?i.forEach((e=>s[e]=null)):c(s,i),_(e)&&o.set(e,s),s):(_(e)&&o.set(e,null),null)}function $n(e,t){return!(!e||!a(t))&&(t=t.slice(2).replace(/Once$/,""),f(e,t[0].toLowerCase()+t.slice(1))||f(e,M(t))||f(e,t))}let An=null,Pn=null;function In(e){const t=An;return An=e,Pn=e&&e.type.__scopeId||null,t}function Bn(e,t=An,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&Pr(-1);const r=In(t);let i;try{i=e(...n)}finally{In(r),o._d&&Pr(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Nn(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:c,emit:u,render:d,renderCache:f,data:h,setupState:p,ctx:g,inheritAttrs:m}=e;let v,y;const _=In(e);try{if(4&n.shapeFlag){const e=r||o,t=e;v=Xr(d.call(t,e,f,i,p,h,g)),y=c}else{const e=t;0,v=Xr(e.length>1?e(i,{attrs:c,slots:a,emit:u}):e(i,null)),y=t.props?c:Rn(c)}}catch(w){Or.length=0,cn(w,e,1),v=Vr(Er)}let b=v;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=b;e.length&&7&t&&(s&&e.some(l)&&(y=Fn(y,s)),b=zr(b,y))}return n.dirs&&(b=zr(b),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&(b.transition=n.transition),v=b,In(_),v}const Rn=e=>{let t;for(const n in e)("class"===n||"style"===n||a(n))&&((t||(t={}))[n]=e[n]);return t},Fn=(e,t)=>{const n={};for(const o in e)l(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function jn(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!$n(n,i))return!0}return!1}function Hn(e,t){return Vn("components",e,!0,t)||e}const Dn=Symbol.for("v-ndc");function Wn(e){return v(e)?Vn("components",e,!1)||e:e||Dn}function Vn(e,t,n=!0,o=!1){const r=An||ei;if(r){const n=r.type;if("components"===e){const e=di(n,!1);if(e&&(e===t||e===L(t)||e===$(L(t))))return n}const i=zn(r[e]||n[e],t)||zn(r.appContext[e],t);return!i&&o?n:i}}function zn(e,t){return e&&(e[t]||e[L(t)]||e[$(L(t))])}const qn=Symbol.for("v-scx");function Un(e,t){return Kn(e,null,t)}const Xn={};function Yn(e,t,n){return Kn(e,t,n)}function Kn(e,t,{immediate:n,deep:r,flush:s,once:a,onTrack:l,onTrigger:c}=o){if(t&&a){const e=t;t=(...t)=>{e(...t),k()}}const d=ei,f=e=>!0===r?e:Qn(e,!1===r?1:void 0);let p,g,v=!1,y=!1;if(Zt(e)?(p=()=>e.value,v=zt(e)):Wt(e)?(p=()=>f(e),v=!0):h(e)?(y=!0,v=e.some((e=>Wt(e)||zt(e))),p=()=>e.map((e=>Zt(e)?e.value:Wt(e)?f(e):m(e)?an(e,d,2):void 0))):p=m(e)?t?()=>an(e,d,2):()=>(g&&g(),ln(e,d,3,[b])):i,t&&r){const e=p;p=()=>Qn(e())}let _,b=e=>{g=T.onStop=()=>{an(e,d,4),g=T.onStop=void 0}};if(ai){if(b=i,t?n&&ln(t,d,3,[p(),y?[]:void 0,b]):p(),"sync"!==s)return i;{const e=rr(qn);_=e.__watcherHandles||(e.__watcherHandles=[])}}let w=y?new Array(e.length).fill(Xn):Xn;const x=()=>{if(T.active&&T.dirty)if(t){const e=T.run();(r||v||(y?e.some(((e,t)=>P(e,w[t]))):P(e,w)))&&(g&&g(),ln(t,d,3,[e,w===Xn?void 0:y&&w[0]===Xn?[]:w,b]),w=e)}else T.run()};let S;x.allowRecurse=!!t,"sync"===s?S=x:"post"===s?S=()=>_r(x,d&&d.suspense):(x.pre=!0,d&&(x.id=d.uid),S=()=>wn(x));const T=new Ne(p,i,S),C=Ae,k=()=>{T.stop(),C&&u(C.effects,T)};return t?n?x():w=T.run():"post"===s?_r(T.run.bind(T),d&&d.suspense):T.run(),_&&_.push(k),k}function Jn(e,t,n){const o=this.proxy,r=v(e)?e.includes(".")?Gn(o,e):()=>o[e]:e.bind(o,o);let i;m(t)?i=t:(i=t.handler,n=t);const s=ri(this),a=Kn(r,i.bind(o),n);return s(),a}function Gn(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function Qn(e,t,n=0,o){if(!_(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),Zt(e))Qn(e.value,t,n,o);else if(h(e))for(let r=0;r<e.length;r++)Qn(e[r],t,n,o);else if(g(e)||p(e))e.forEach((e=>{Qn(e,t,n,o)}));else if(S(e))for(const r in e)Qn(e[r],t,n,o);return e}function Zn(e,t){if(null===An)return e;const n=ui(An)||An.proxy,r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,s,a,l=o]=t[i];e&&(m(e)&&(e={mounted:e,updated:e}),e.deep&&Qn(s),r.push({dir:e,instance:n,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function eo(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(Ve(),ln(l,n,8,[e.el,a,e,t]),ze())}}const to=Symbol("_leaveCb"),no=Symbol("_enterCb");const oo=[Function,Array],ro={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:oo,onEnter:oo,onAfterEnter:oo,onEnterCancelled:oo,onBeforeLeave:oo,onLeave:oo,onAfterLeave:oo,onLeaveCancelled:oo,onBeforeAppear:oo,onAppear:oo,onAfterAppear:oo,onAppearCancelled:oo},io={name:"BaseTransition",props:ro,setup(e,{slots:t}){const n=ti(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Co((()=>{e.isMounted=!0})),Lo((()=>{e.isUnmounting=!0})),e}();return()=>{const r=t.default&&fo(t.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1)for(const e of r)if(e.type!==Er){i=e;break}const s=Ut(e),{mode:a}=s;if(o.isLeaving)return lo(i);const l=co(i);if(!l)return lo(i);const c=ao(l,s,o,n);uo(l,c);const u=n.subTree,d=u&&co(u);if(d&&d.type!==Er&&!Fr(l,d)){const e=ao(d,s,o,n);if(uo(d,e),"out-in"===a)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&(n.effect.dirty=!0,n.update())},lo(i);"in-out"===a&&l.type!==Er&&(e.delayLeave=(e,t,n)=>{so(o,d)[String(d.key)]=d,e[to]=()=>{t(),e[to]=void 0,delete c.delayedLeave},c.delayedLeave=n})}return i}}};function so(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function ao(e,t,n,o){const{appear:r,mode:i,persisted:s=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:f,onAfterLeave:p,onLeaveCancelled:g,onBeforeAppear:m,onAppear:v,onAfterAppear:y,onAppearCancelled:_}=t,b=String(e.key),w=so(n,e),x=(e,t)=>{e&&ln(e,o,9,t)},S=(e,t)=>{const n=t[1];x(e,t),h(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},T={mode:i,persisted:s,beforeEnter(t){let o=a;if(!n.isMounted){if(!r)return;o=m||a}t[to]&&t[to](!0);const i=w[b];i&&Fr(e,i)&&i.el[to]&&i.el[to](),x(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=v||l,o=y||c,i=_||u}let s=!1;const a=e[no]=t=>{s||(s=!0,x(t?i:o,[e]),T.delayedLeave&&T.delayedLeave(),e[no]=void 0)};t?S(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t[no]&&t[no](!0),n.isUnmounting)return o();x(d,[t]);let i=!1;const s=t[to]=n=>{i||(i=!0,o(),x(n?g:p,[t]),t[to]=void 0,w[r]===e&&delete w[r])};w[r]=e,f?S(f,[t,s]):s()},clone:e=>ao(e,t,n,o)};return T}function lo(e){if(vo(e))return(e=zr(e)).children=null,e}function co(e){return vo(e)?e.children?e.children[0]:void 0:e}function uo(e,t){6&e.shapeFlag&&e.component?uo(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function fo(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let s=e[i];const a=null==n?s.key:String(n)+String(null!=s.key?s.key:i);s.type===Cr?(128&s.patchFlag&&r++,o=o.concat(fo(s.children,t,a))):(t||s.type!==Er)&&o.push(null!=a?zr(s,{key:a}):s)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}
/*! #__NO_SIDE_EFFECTS__ */function ho(e,t){return m(e)?(()=>c({name:e.name},t,{setup:e}))():e}const po=e=>!!e.type.__asyncLoader
/*! #__NO_SIDE_EFFECTS__ */;function go(e){m(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(((t,n)=>{a(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return ho({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=ei;if(l)return()=>mo(l,e);const t=t=>{c=null,cn(t,e,13,!o)};if(s&&e.suspense||ai)return d().then((t=>()=>mo(t,e))).catch((e=>(t(e),()=>o?Vr(o,{error:e}):null)));const a=en(!1),u=en(),f=en(!!r);return r&&setTimeout((()=>{f.value=!1}),r),null!=i&&setTimeout((()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),d().then((()=>{a.value=!0,e.parent&&vo(e.parent.vnode)&&(e.parent.effect.dirty=!0,wn(e.parent.update))})).catch((e=>{t(e),u.value=e})),()=>a.value&&l?mo(l,e):u.value&&o?Vr(o,{error:u.value}):n&&!f.value?Vr(n):void 0}})}function mo(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=Vr(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const vo=e=>e.type.__isKeepAlive;function yo(e,t){bo(e,"a",t)}function _o(e,t){bo(e,"da",t)}function bo(e,t,n=ei){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,xo(t,o,n),n){let e=n.parent;for(;e&&e.parent;)vo(e.parent.vnode)&&wo(o,t,n,e),e=e.parent}}function wo(e,t,n,o){const r=xo(t,e,o,!0);Oo((()=>{u(o[t],r)}),n)}function xo(e,t,n=ei,o=!1){if(n){if(r=e,Se.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return["onLoad","onShow"].indexOf(e)>-1}(e))){const o=n.proxy;ln(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Ve();const r=ri(n),i=ln(t,n,e,o);return r(),ze(),i});return o?i.unshift(s):i.push(s),s}var r}const So=e=>(t,n=ei)=>(!ai||"sp"===e)&&xo(e,((...e)=>t(...e)),n),To=So("bm"),Co=So("m"),ko=So("bu"),Eo=So("u"),Lo=So("bum"),Oo=So("um"),Mo=So("sp"),$o=So("rtg"),Ao=So("rtc");function Po(e,t=ei){xo("ec",e,t)}function Io(e,t,n={},o,r){if(An.isCE||An.parent&&po(An.parent)&&An.parent.isCE)return"default"!==t&&(n.name=t),Vr("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),$r();const s=i&&Bo(i(n)),a=Nr(Cr,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function Bo(e){return e.some((e=>!Rr(e)||e.type!==Er&&!(e.type===Cr&&!Bo(e.children))))?e:null}const No=e=>{if(!e)return null;if(si(e)){return ui(e)||e.proxy}return No(e.parent)},Ro=c(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>No(e.parent),$root:e=>No(e.root),$emit:e=>e.emit,$options:e=>qo(e),$forceUpdate:e=>e.f||(e.f=(e=>function(){e.effect.dirty=!0,wn(e.update)})(e)),$nextTick:e=>e.n||(e.n=bn.bind(e.proxy)),$watch:e=>Jn.bind(e)}),Fo=(e,t)=>e!==o&&!e.__isScriptSetup&&f(e,t),jo={get({_:e},t){const{ctx:n,setupState:r,data:i,props:s,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return r[t];case 2:return i[t];case 4:return n[t];case 3:return s[t]}else{if(Fo(r,t))return a[t]=1,r[t];if(i!==o&&f(i,t))return a[t]=2,i[t];if((u=e.propsOptions[0])&&f(u,t))return a[t]=3,s[t];if(n!==o&&f(n,t))return a[t]=4,n[t];Do&&(a[t]=0)}}const d=Ro[t];let h,p;return d?("$attrs"===t&&et(e,0,t),d(e)):(h=l.__cssModules)&&(h=h[t])?h:n!==o&&f(n,t)?(a[t]=4,n[t]):(p=c.config.globalProperties,f(p,t)?p[t]:void 0)},set({_:e},t,n){const{data:r,setupState:i,ctx:s}=e;return Fo(i,t)?(i[t]=n,!0):r!==o&&f(r,t)?(r[t]=n,!0):!f(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(s[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:s}},a){let l;return!!n[a]||e!==o&&f(e,a)||Fo(t,a)||(l=s[0])&&f(l,a)||f(r,a)||f(Ro,a)||f(i.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:f(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ho(e){return h(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let Do=!0;function Wo(e){const t=qo(e),n=e.proxy,o=e.ctx;Do=!1,t.beforeCreate&&Vo(t.beforeCreate,e,"bc");const{data:r,computed:s,methods:a,watch:l,provide:c,inject:u,created:d,beforeMount:f,mounted:p,beforeUpdate:g,updated:v,activated:y,deactivated:b,beforeDestroy:w,beforeUnmount:x,destroyed:S,unmounted:T,render:C,renderTracked:k,renderTriggered:E,errorCaptured:L,serverPrefetch:O,expose:M,inheritAttrs:$,components:A,directives:P,filters:I}=t;if(u&&function(e,t,n=i){h(e)&&(e=Ko(e));for(const o in e){const n=e[o];let r;r=_(n)?"default"in n?rr(n.from||o,n.default,!0):rr(n.from||o):rr(n),Zt(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}(u,o,null),a)for(const i in a){const e=a[i];m(e)&&(o[i]=e.bind(n))}if(r){const t=r.call(n,n);_(t)&&(e.data=jt(t))}if(Do=!0,s)for(const h in s){const e=s[h],t=m(e)?e.bind(n,n):m(e.get)?e.get.bind(n,n):i,r=!m(e)&&m(e.set)?e.set.bind(n):i,a=fi({get:t,set:r});Object.defineProperty(o,h,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(l)for(const i in l)zo(l[i],o,n,i);if(c){const e=m(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{or(t,e[t])}))}function B(e,t){h(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&Vo(d,e,"c"),B(To,f),B(Co,p),B(ko,g),B(Eo,v),B(yo,y),B(_o,b),B(Po,L),B(Ao,k),B($o,E),B(Lo,x),B(Oo,T),B(Mo,O),h(M))if(M.length){const t=e.exposed||(e.exposed={});M.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});C&&e.render===i&&(e.render=C),null!=$&&(e.inheritAttrs=$),A&&(e.components=A),P&&(e.directives=P);const N=e.appContext.config.globalProperties.$applyOptions;N&&N(t,e,n)}function Vo(e,t,n){ln(h(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function zo(e,t,n,o){const r=o.includes(".")?Gn(n,o):()=>n[o];if(v(e)){const n=t[e];m(n)&&Yn(r,n)}else if(m(e))Yn(r,e.bind(n));else if(_(e))if(h(e))e.forEach((e=>zo(e,t,n,o)));else{const o=m(e.handler)?e.handler.bind(n):t[e.handler];m(o)&&Yn(r,o,e)}}function qo(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach((e=>Uo(l,e,s,!0))),Uo(l,t,s)):l=t,_(t)&&i.set(t,l),l}function Uo(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&Uo(e,i,n,!0),r&&r.forEach((t=>Uo(e,t,n,!0)));for(const s in t)if(o&&"expose"===s);else{const o=Xo[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const Xo={data:Yo,props:Qo,emits:Qo,methods:Go,computed:Go,beforeCreate:Jo,created:Jo,beforeMount:Jo,mounted:Jo,beforeUpdate:Jo,updated:Jo,beforeDestroy:Jo,beforeUnmount:Jo,destroyed:Jo,unmounted:Jo,activated:Jo,deactivated:Jo,errorCaptured:Jo,serverPrefetch:Jo,components:Go,directives:Go,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const o in t)n[o]=Jo(e[o],t[o]);return n},provide:Yo,inject:function(e,t){return Go(Ko(e),Ko(t))}};function Yo(e,t){return t?e?function(){return c(m(e)?e.call(this,this):e,m(t)?t.call(this,this):t)}:t:e}function Ko(e){if(h(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Jo(e,t){return e?[...new Set([].concat(e,t))]:t}function Go(e,t){return e?c(Object.create(null),e,t):t}function Qo(e,t){return e?h(e)&&h(t)?[...new Set([...e,...t])]:c(Object.create(null),Ho(e),Ho(null!=t?t:{})):t}function Zo(){return{app:null,config:{isNativeTag:s,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let er=0;function tr(e,t){return function(n,o=null){m(n)||(n=c({},n)),null==o||_(o)||(o=null);const r=Zo(),i=new WeakSet;let s=!1;const a=r.app={_uid:er++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:hi,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&m(e.install)?(i.add(e),e.install(a,...t)):m(e)&&(i.add(e),e(a,...t))),a),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),a),component:(e,t)=>t?(r.components[e]=t,a):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,a):r.directives[e],mount(i,l,c){if(!s){const u=Vr(n,o);return u.appContext=r,!0===c?c="svg":!1===c&&(c=void 0),l&&t?t(u,i):e(u,i,c),s=!0,a._container=i,i.__vue_app__=a,a._instance=u.component,ui(u.component)||u.component.proxy}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,a),runWithContext(e){const t=nr;nr=a;try{return e()}finally{nr=t}}};return a}}let nr=null;function or(e,t){if(ei){let n=ei.provides;const o=ei.parent&&ei.parent.provides;o===n&&(n=ei.provides=Object.create(o)),n[e]=t,"app"===ei.type.mpType&&ei.appContext.app.provide(e,t)}else;}function rr(e,t,n=!1){const o=ei||An;if(o||nr){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:nr._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&m(t)?t.call(o&&o.proxy):t}}function ir(e,t,n,o=!1){const r={},i={};B(i,jr,1),e.propsDefaults=Object.create(null),sr(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:Dt(r,!1,ft,At,Bt):e.type.props?e.props=r:e.props=i,e.attrs=i}function sr(e,t,n,r){const[i,s]=e.propsOptions;let a,l=!1;if(t)for(let o in t){if(C(o))continue;const c=t[o];let u;i&&f(i,u=L(o))?s&&s.includes(u)?(a||(a={}))[u]=c:n[u]=c:$n(e.emitsOptions,o)||o in r&&c===r[o]||(r[o]=c,l=!0)}if(s){const t=Ut(n),r=a||o;for(let o=0;o<s.length;o++){const a=s[o];n[a]=ar(i,t,a,r[a],e,!f(r,a))}}return l}function ar(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=f(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&!s.skipFactory&&m(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const s=ri(r);o=i[n]=e.call(null,t),s()}}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==M(n)||(o=!0))}return o}function lr(e,t,n=!1){const i=t.propsCache,s=i.get(e);if(s)return s;const a=e.props,l={},u=[];let d=!1;if(!m(e)){const o=e=>{d=!0;const[n,o]=lr(e,t,!0);c(l,n),o&&u.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!a&&!d)return _(e)&&i.set(e,r),r;if(h(a))for(let r=0;r<a.length;r++){const e=L(a[r]);cr(e)&&(l[e]=o)}else if(a)for(const o in a){const e=L(o);if(cr(e)){const t=a[o],n=l[e]=h(t)||m(t)?{type:t}:c({},t);if(n){const t=fr(Boolean,n.type),o=fr(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||f(n,"default"))&&u.push(e)}}}const p=[l,u];return _(e)&&i.set(e,p),p}function cr(e){return"$"!==e[0]&&!C(e)}function ur(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function dr(e,t){return ur(e)===ur(t)}function fr(e,t){return h(t)?t.findIndex((t=>dr(t,e))):m(t)&&dr(t,e)?0:-1}const hr=e=>"_"===e[0]||"$stable"===e,pr=e=>h(e)?e.map(Xr):[Xr(e)],gr=(e,t,n)=>{if(t._n)return t;const o=Bn(((...e)=>pr(t(...e))),n);return o._c=!1,o},mr=(e,t,n)=>{const o=e._ctx;for(const r in e){if(hr(r))continue;const n=e[r];if(m(n))t[r]=gr(0,n,o);else if(null!=n){const e=pr(n);t[r]=()=>e}}},vr=(e,t)=>{const n=pr(t);e.slots.default=()=>n};function yr(e,t,n,r,i=!1){if(h(e))return void e.forEach(((e,o)=>yr(e,t&&(h(t)?t[o]:t),n,r,i)));if(po(r)&&!i)return;const s=4&r.shapeFlag?ui(r.component)||r.component.proxy:r.el,a=i?null:s,{i:l,r:c}=e,d=t&&t.r,p=l.refs===o?l.refs={}:l.refs,g=l.setupState;if(null!=d&&d!==c&&(v(d)?(p[d]=null,f(g,d)&&(g[d]=null)):Zt(d)&&(d.value=null)),m(c))an(c,l,12,[a,p]);else{const t=v(c),o=Zt(c);if(t||o){const r=()=>{if(e.f){const n=t?f(g,c)?g[c]:p[c]:c.value;i?h(n)&&u(n,s):h(n)?n.includes(s)||n.push(s):t?(p[c]=[s],f(g,c)&&(g[c]=p[c])):(c.value=[s],e.k&&(p[e.k]=c.value))}else t?(p[c]=a,f(g,c)&&(g[c]=a)):o&&(c.value=a,e.k&&(p[e.k]=a))};a?(r.id=-1,_r(r,n)):r()}}}const _r=function(e,t){var n;t&&t.pendingBranch?h(e)?t.effects.push(...e):t.effects.push(e):(h(n=e)?gn.push(...n):mn&&mn.includes(n,n.allowRecurse?vn+1:vn)||gn.push(n),xn())};function br(e){return function(e,t){F().__VUE__=!0;const{insert:n,remove:s,patchProp:a,forcePatchProp:l,createElement:u,createText:d,createComment:h,setText:p,setElementText:g,parentNode:m,nextSibling:v,setScopeId:y=i,insertStaticContent:_}=e,w=(e,t,n,o=null,r=null,i=null,s,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Fr(e,t)&&(o=te(e),J(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case kr:x(e,t,n,o);break;case Er:S(e,t,n,o);break;case Lr:null==e&&T(t,n,o,s);break;case Cr:H(e,t,n,o,r,i,s,a,l);break;default:1&d?O(e,t,n,o,r,i,s,a,l):6&d?D(e,t,n,o,r,i,s,a,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,s,a,l,re)}null!=u&&r&&yr(u,e&&e.ref,i,t||e,!t)},x=(e,t,o,r)=>{if(null==e)n(t.el=d(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&p(n,t.children)}},S=(e,t,o,r)=>{null==e?n(t.el=h(t.children||""),o,r):t.el=e.el},T=(e,t,n,o)=>{[e.el,e.anchor]=_(e.children,t,n,o,e.el,e.anchor)},k=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=v(e),n(e,o,r),e=i;n(t,o,r)},E=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),s(e),e=n;s(t)},O=(e,t,n,o,r,i,s,a,l)=>{"svg"===t.type?s="svg":"math"===t.type&&(s="mathml"),null==e?$(t,n,o,r,i,s,a,l):N(e,t,r,i,s,a,l)},$=(e,t,o,r,i,s,l,c)=>{let d,f;const{props:h,shapeFlag:p,transition:m,dirs:v}=e;if(d=e.el=u(e.type,s,h&&h.is,h),8&p?g(d,e.children):16&p&&P(e.children,d,null,r,i,wr(e,s),l,c),v&&eo(e,null,r,"created"),A(d,e,e.scopeId,l,r),h){for(const t in h)"value"===t||C(t)||a(d,t,null,h[t],s,e.children,r,i,ee);"value"in h&&a(d,"value",null,h.value,s),(f=h.onVnodeBeforeMount)&&Gr(f,r,e)}Object.defineProperty(d,"__vueParentComponent",{value:r,enumerable:!1}),v&&eo(e,null,r,"beforeMount");const y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(i,m);y&&m.beforeEnter(d),n(d,t,o),((f=h&&h.onVnodeMounted)||y||v)&&_r((()=>{f&&Gr(f,r,e),y&&m.enter(d),v&&eo(e,null,r,"mounted")}),i)},A=(e,t,n,o,r)=>{if(n&&y(e,n),o)for(let i=0;i<o.length;i++)y(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;A(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},P=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?Yr(e[c]):Xr(e[c]);w(null,l,t,n,o,r,i,s,a)}},N=(e,t,n,r,i,s,c)=>{const u=t.el=e.el;let{patchFlag:d,dynamicChildren:f,dirs:h}=t;d|=16&e.patchFlag;const p=e.props||o,m=t.props||o;let v;if(n&&xr(n,!1),(v=m.onVnodeBeforeUpdate)&&Gr(v,n,t,e),h&&eo(t,e,n,"beforeUpdate"),n&&xr(n,!0),f?R(e.dynamicChildren,f,u,n,r,wr(t,i),s):c||U(e,t,u,null,n,r,wr(t,i),s,!1),d>0){if(16&d)j(u,t,p,m,n,r,i);else if(2&d&&p.class!==m.class&&a(u,"class",null,m.class,i),4&d&&a(u,"style",p.style,m.style,i),8&d){const o=t.dynamicProps;for(let t=0;t<o.length;t++){const s=o[t],c=p[s],d=m[s];(d!==c||"value"===s||l&&l(u,s))&&a(u,s,c,d,i,e.children,n,r,ee)}}1&d&&e.children!==t.children&&g(u,t.children)}else c||null!=f||j(u,t,p,m,n,r,i);((v=m.onVnodeUpdated)||h)&&_r((()=>{v&&Gr(v,n,t,e),h&&eo(t,e,n,"updated")}),r)},R=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===Cr||!Fr(l,c)||70&l.shapeFlag)?m(l.el):n;w(l,c,u,null,o,r,i,s,!0)}},j=(e,t,n,r,i,s,c)=>{if(n!==r){if(n!==o)for(const o in n)C(o)||o in r||a(e,o,n[o],null,c,t.children,i,s,ee);for(const o in r){if(C(o))continue;const u=r[o],d=n[o];(u!==d&&"value"!==o||l&&l(e,o))&&a(e,o,d,u,c,t.children,i,s,ee)}"value"in r&&a(e,"value",n.value,r.value,c)}},H=(e,t,o,r,i,s,a,l,c)=>{const u=t.el=e?e.el:d(""),f=t.anchor=e?e.anchor:d("");let{patchFlag:h,dynamicChildren:p,slotScopeIds:g}=t;g&&(l=l?l.concat(g):g),null==e?(n(u,o,r),n(f,o,r),P(t.children||[],o,f,i,s,a,l,c)):h>0&&64&h&&p&&e.dynamicChildren?(R(e.dynamicChildren,p,o,i,s,a,l),(null!=t.key||i&&t===i.subTree)&&Sr(e,t,!0)):U(e,t,o,f,i,s,a,l,c)},D=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):W(t,n,o,r,i,s,l):V(e,t,l)},W=(e,t,n,r,i,s,a)=>{const l=e.component=function(e,t,n){const r=e.type,i=(t?t.appContext:e.appContext)||Qr,s={uid:Zr++,vnode:e,type:r,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,scope:new Ie(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:lr(r,i),emitsOptions:Mn(r,i),emit:null,emitted:null,propsDefaults:o,inheritAttrs:r.inheritAttrs,ctx:o,data:o,props:o,attrs:o,slots:o,refs:o,setupState:o,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};s.ctx={_:s},s.root=t?t.root:s,s.emit=Ln.bind(null,s),s.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(s);return s}(e,r,i);if(vo(e)&&(l.ctx.renderer=re),function(e,t=!1){t&&oi(t);const{props:n,children:o}=e.vnode,r=si(e);ir(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Ut(t),B(t,"_",n)):mr(t,e.slots={})}else e.slots={},t&&vr(e,t);B(e.slots,jr,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Xt(new Proxy(e.ctx,jo));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(et(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,r=ri(e);Ve();const i=an(o,e,0,[e.props,n]);if(ze(),r(),b(i)){if(i.then(ii,ii),t)return i.then((n=>{li(e,n,t)})).catch((t=>{cn(t,e,0)}));e.asyncDep=i}else li(e,i,t)}else ci(e,t)}(e,t):void 0;t&&oi(!1)}(l),l.asyncDep){if(i&&i.registerDep(l,z),!e.el){const e=l.subTree=Vr(Er);S(null,e,t,n)}}else z(l,e,t,n,i,s,a)},V=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||jn(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?jn(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!$n(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void q(o,t,n);o.next=t,function(e){const t=hn.indexOf(e);t>pn&&hn.splice(t,1)}(o.update),o.effect.dirty=!0,o.update()}else t.el=e.el,o.vnode=t},z=(e,t,n,o,r,s,a)=>{const l=()=>{if(e.isMounted){let{next:t,bu:n,u:o,parent:i,vnode:c}=e;{const n=Tr(e);if(n)return t&&(t.el=c.el,q(e,t,a)),void n.asyncDep.then((()=>{e.isUnmounted||l()}))}let u,d=t;xr(e,!1),t?(t.el=c.el,q(e,t,a)):t=c,n&&I(n),(u=t.props&&t.props.onVnodeBeforeUpdate)&&Gr(u,i,t,c),xr(e,!0);const f=Nn(e),h=e.subTree;e.subTree=f,w(h,f,m(h.el),te(h),e,r,s),t.el=f.el,null===d&&function({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,f.el),o&&_r(o,r),(u=t.props&&t.props.onVnodeUpdated)&&_r((()=>Gr(u,i,t,c)),r)}else{let i;const{el:a,props:l}=t,{bm:c,m:u,parent:d}=e,f=po(t);if(xr(e,!1),c&&I(c),!f&&(i=l&&l.onVnodeBeforeMount)&&Gr(i,d,t),xr(e,!0),a&&se){const n=()=>{e.subTree=Nn(e),se(a,e.subTree,e,r,null)};f?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const i=e.subTree=Nn(e);w(null,i,n,o,e,r,s),t.el=i.el}if(u&&_r(u,r),!f&&(i=l&&l.onVnodeMounted)){const e=t;_r((()=>Gr(i,d,e)),r)}(256&t.shapeFlag||d&&po(d.vnode)&&256&d.vnode.shapeFlag)&&(e.ba&&function(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}(e.ba),e.a&&_r(e.a,r)),e.isMounted=!0,t=n=o=null}},c=e.effect=new Ne(l,i,(()=>wn(u)),e.scope),u=e.update=()=>{c.dirty&&c.run()};u.id=e.uid,xr(e,!0),u()},q=(e,t,n)=>{t.component=e;const r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=Ut(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;sr(e,t,r,i)&&(c=!0);for(const i in a)t&&(f(t,i)||(o=M(i))!==i&&f(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=ar(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&f(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if($n(e.emitsOptions,s))continue;const u=t[s];if(l)if(f(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=L(s);r[t]=ar(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&tt(e,"set","$attrs")}(e,t.props,r,n),((e,t,n)=>{const{vnode:r,slots:i}=e;let s=!0,a=o;if(32&r.shapeFlag){const e=t._;e?n&&1===e?s=!1:(c(i,t),n||1!==e||delete i._):(s=!t.$stable,mr(t,i)),a=t}else t&&(vr(e,t),a={default:1});if(s)for(const o in i)hr(o)||null!=a[o]||delete i[o]})(e,t.children,n),Ve(),Sn(e),ze()},U=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:f,shapeFlag:h}=t;if(f>0){if(128&f)return void Y(c,d,n,o,r,i,s,a,l);if(256&f)return void X(c,d,n,o,r,i,s,a,l)}8&h?(16&u&&ee(c,r,i),d!==c&&g(n,d)):16&u?16&h?Y(c,d,n,o,r,i,s,a,l):ee(c,r,i,!0):(8&u&&g(n,""),16&h&&P(d,n,o,r,i,s,a,l))},X=(e,t,n,o,i,s,a,l,c)=>{t=t||r;const u=(e=e||r).length,d=t.length,f=Math.min(u,d);let h;for(h=0;h<f;h++){const o=t[h]=c?Yr(t[h]):Xr(t[h]);w(e[h],o,n,null,i,s,a,l,c)}u>d?ee(e,i,s,!0,!1,f):P(t,n,o,i,s,a,l,c,f)},Y=(e,t,n,o,i,s,a,l,c)=>{let u=0;const d=t.length;let f=e.length-1,h=d-1;for(;u<=f&&u<=h;){const o=e[u],r=t[u]=c?Yr(t[u]):Xr(t[u]);if(!Fr(o,r))break;w(o,r,n,null,i,s,a,l,c),u++}for(;u<=f&&u<=h;){const o=e[f],r=t[h]=c?Yr(t[h]):Xr(t[h]);if(!Fr(o,r))break;w(o,r,n,null,i,s,a,l,c),f--,h--}if(u>f){if(u<=h){const e=h+1,r=e<d?t[e].el:o;for(;u<=h;)w(null,t[u]=c?Yr(t[u]):Xr(t[u]),n,r,i,s,a,l,c),u++}}else if(u>h)for(;u<=f;)J(e[u],i,s,!0),u++;else{const p=u,g=u,m=new Map;for(u=g;u<=h;u++){const e=t[u]=c?Yr(t[u]):Xr(t[u]);null!=e.key&&m.set(e.key,u)}let v,y=0;const _=h-g+1;let b=!1,x=0;const S=new Array(_);for(u=0;u<_;u++)S[u]=0;for(u=p;u<=f;u++){const o=e[u];if(y>=_){J(o,i,s,!0);continue}let r;if(null!=o.key)r=m.get(o.key);else for(v=g;v<=h;v++)if(0===S[v-g]&&Fr(o,t[v])){r=v;break}void 0===r?J(o,i,s,!0):(S[r-g]=u+1,r>=x?x=r:b=!0,w(o,t[r],n,null,i,s,a,l,c),y++)}const T=b?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(S):r;for(v=T.length-1,u=_-1;u>=0;u--){const e=g+u,r=t[e],f=e+1<d?t[e+1].el:o;0===S[u]?w(null,r,n,f,i,s,a,l,c):b&&(v<0||u!==T[v]?K(r,n,f,2):v--)}}},K=(e,t,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void K(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void a.move(e,t,o,re);if(a===Cr){n(s,t,o);for(let e=0;e<c.length;e++)K(c[e],t,o,r);return void n(e.anchor,t,o)}if(a===Lr)return void k(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),n(s,t,o),_r((()=>l.enter(s)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>n(s,t,o),c=()=>{e(s,(()=>{a(),i&&i()}))};r?r(s,a,c):c()}else n(s,t,o)},J=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:f}=e;if(null!=a&&yr(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const h=1&u&&f,p=!po(e);let g;if(p&&(g=s&&s.onVnodeBeforeUnmount)&&Gr(g,t,e),6&u)Z(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);h&&eo(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,re,o):c&&(i!==Cr||d>0&&64&d)?ee(c,t,n,!1,!0):(i===Cr&&384&d||!r&&16&u)&&ee(l,t,n),o&&G(e)}(p&&(g=s&&s.onVnodeUnmounted)||h)&&_r((()=>{g&&Gr(g,t,e),h&&eo(e,null,t,"unmounted")}),n)},G=e=>{const{type:t,el:n,anchor:o,transition:r}=e;if(t===Cr)return void Q(n,o);if(t===Lr)return void E(e);const i=()=>{s(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){const{leave:t,delayLeave:o}=r,s=()=>t(n,i);o?o(e.el,i,s):s()}else i()},Q=(e,t)=>{let n;for(;e!==t;)n=v(e),s(e),e=n;s(t)},Z=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&I(o),r.stop(),i&&(i.active=!1,J(s,e,t,n)),a&&_r(a,t),_r((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)J(e[s],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el);let ne=!1;const oe=(e,t,n)=>{null==e?t._vnode&&J(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),ne||(ne=!0,Sn(),Tn(),ne=!1),t._vnode=e},re={p:w,um:J,m:K,r:G,mt:W,mc:P,pc:U,pbc:R,n:te,o:e};let ie,se;t&&([ie,se]=t(re));return{render:oe,hydrate:ie,createApp:tr(oe,ie)}}(e)}function wr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function xr({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Sr(e,t,n=!1){const o=e.children,r=t.children;if(h(o)&&h(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=Yr(r[i]),t.el=e.el),n||Sr(e,t)),t.type===kr&&(t.el=e.el)}}function Tr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Tr(t)}const Cr=Symbol.for("v-fgt"),kr=Symbol.for("v-txt"),Er=Symbol.for("v-cmt"),Lr=Symbol.for("v-stc"),Or=[];let Mr=null;function $r(e=!1){Or.push(Mr=e?null:[])}let Ar=1;function Pr(e){Ar+=e}function Ir(e){return e.dynamicChildren=Ar>0?Mr||r:null,Or.pop(),Mr=Or[Or.length-1]||null,Ar>0&&Mr&&Mr.push(e),e}function Br(e,t,n,o,r,i){return Ir(Wr(e,t,n,o,r,i,!0))}function Nr(e,t,n,o,r){return Ir(Vr(e,t,n,o,r,!0))}function Rr(e){return!!e&&!0===e.__v_isVNode}function Fr(e,t){return e.type===t.type&&e.key===t.key}const jr="__vInternal",Hr=({key:e})=>null!=e?e:null,Dr=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?v(e)||Zt(e)||m(e)?{i:An,r:e,k:t,f:!!n}:e:null);function Wr(e,t=null,n=null,o=0,r=null,i=(e===Cr?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Hr(t),ref:t&&Dr(t),scopeId:Pn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:An};return a?(Kr(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=v(n)?8:16),Ar>0&&!s&&Mr&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&Mr.push(l),l}const Vr=function(e,t=null,n=null,o=0,r=null,i=!1){e&&e!==Dn||(e=Er);if(Rr(e)){const o=zr(e,t,!0);return n&&Kr(o,n),Ar>0&&!i&&Mr&&(6&o.shapeFlag?Mr[Mr.indexOf(e)]=o:Mr.push(o)),o.patchFlag|=-2,o}s=e,m(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?qt(e)||jr in e?c({},e):e:null}(t);let{class:e,style:n}=t;e&&!v(e)&&(t.class=de(e)),_(n)&&(qt(n)&&!h(n)&&(n=c({},n)),t.style=ue(n))}const a=v(e)?1:(e=>e.__isSuspense)(e)?128:(e=>e.__isTeleport)(e)?64:_(e)?4:m(e)?2:0;return Wr(e,t,n,o,r,a,i,!0)};function zr(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?Jr(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Hr(a),ref:t&&t.ref?n&&r?h(r)?r.concat(Dr(t)):[r,Dr(t)]:Dr(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Cr?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&zr(e.ssContent),ssFallback:e.ssFallback&&zr(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function qr(e=" ",t=0){return Vr(kr,null,e,t)}function Ur(e="",t=!1){return t?($r(),Nr(Er,null,e)):Vr(Er,null,e)}function Xr(e){return null==e||"boolean"==typeof e?Vr(Er):h(e)?Vr(Cr,null,e.slice()):"object"==typeof e?Yr(e):Vr(kr,null,String(e))}function Yr(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:zr(e)}function Kr(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(h(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),Kr(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||jr in t?3===o&&An&&(1===An.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=An}}else m(t)?(t={default:t,_ctx:An},n=32):(t=String(t),64&o?(n=16,t=[qr(t)]):n=8);e.children=t,e.shapeFlag|=n}function Jr(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=de([t.class,o.class]));else if("style"===e)t.style=ue([t.style,o.style]);else if(a(e)){const n=t[e],r=o[e];!r||n===r||h(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function Gr(e,t,n,o=null){ln(e,t,7,[n,o])}const Qr=Zo();let Zr=0;let ei=null;const ti=()=>ei||An;let ni,oi;{const e=F(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};ni=t("__VUE_INSTANCE_SETTERS__",(e=>ei=e)),oi=t("__VUE_SSR_SETTERS__",(e=>ai=e))}const ri=e=>{const t=ei;return ni(e),e.scope.on(),()=>{e.scope.off(),ni(t)}},ii=()=>{ei&&ei.scope.off(),ni(null)};function si(e){return 4&e.vnode.shapeFlag}let ai=!1;function li(e,t,n){m(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=sn(t)),ci(e,n)}function ci(e,t,n){const o=e.type;e.render||(e.render=o.render||i);{const t=ri(e);Ve();try{Wo(e)}finally{ze(),t()}}}function ui(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(sn(Xt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Ro?Ro[n](e):void 0,has:(e,t)=>t in e||t in Ro}))}function di(e,t=!0){return m(e)?e.displayName||e.name:e.name||t&&e.__name}const fi=(e,t)=>{const n=function(e,t,n=!1){let o,r;const s=m(e);return s?(o=e,r=i):(o=e.get,r=e.set),new Jt(o,r,s||!r,n)}(e,0,ai);return n};const hi="3.4.21",pi="undefined"!=typeof document?document:null,gi=pi&&pi.createElement("template"),mi={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r="svg"===t?pi.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?pi.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?pi.createElement(e,{is:n}):pi.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>pi.createTextNode(e),createComment:e=>pi.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>pi.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{gi.innerHTML="svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e;const r=gi.content;if("svg"===o||"mathml"===o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},vi="transition",yi=Symbol("_vtc"),_i=(e,{slots:t})=>function(e,t,n){const o=arguments.length;return 2===o?_(t)&&!h(t)?Rr(t)?Vr(e,null,[t]):Vr(e,t):Vr(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Rr(n)&&(n=[n]),Vr(e,t,n))}(io,function(e){const t={};for(const c in e)c in bi||(t[c]=e[c]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:s=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:u=s,appearToClass:d=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:h=`${n}-leave-active`,leaveToClass:p=`${n}-leave-to`}=e,g=function(e){if(null==e)return null;if(_(e))return[Si(e.enter),Si(e.leave)];{const t=Si(e);return[t,t]}}(r),m=g&&g[0],v=g&&g[1],{onBeforeEnter:y,onEnter:b,onEnterCancelled:w,onLeave:x,onLeaveCancelled:S,onBeforeAppear:T=y,onAppear:C=b,onAppearCancelled:k=w}=t,E=(e,t,n)=>{Ci(e,t?d:a),Ci(e,t?u:s),n&&n()},L=(e,t)=>{e._isLeaving=!1,Ci(e,f),Ci(e,p),Ci(e,h),t&&t()},O=e=>(t,n)=>{const r=e?C:b,s=()=>E(t,e,n);wi(r,[t,s]),ki((()=>{Ci(t,e?l:i),Ti(t,e?d:a),xi(r)||Li(t,o,m,s)}))};return c(t,{onBeforeEnter(e){wi(y,[e]),Ti(e,i),Ti(e,s)},onBeforeAppear(e){wi(T,[e]),Ti(e,l),Ti(e,u)},onEnter:O(!1),onAppear:O(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>L(e,t);Ti(e,f),document.body.offsetHeight,Ti(e,h),ki((()=>{e._isLeaving&&(Ci(e,f),Ti(e,p),xi(x)||Li(e,o,v,n))})),wi(x,[e,n])},onEnterCancelled(e){E(e,!1),wi(w,[e])},onAppearCancelled(e){E(e,!0),wi(k,[e])},onLeaveCancelled(e){L(e),wi(S,[e])}})}(e),t);_i.displayName="Transition";const bi={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};_i.props=c({},ro,bi);const wi=(e,t=[])=>{h(e)?e.forEach((e=>e(...t))):e&&e(...t)},xi=e=>!!e&&(h(e)?e.some((e=>e.length>1)):e.length>1);function Si(e){const t=(e=>{const t=v(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function Ti(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[yi]||(e[yi]=new Set)).add(t)}function Ci(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const n=e[yi];n&&(n.delete(t),n.size||(e[yi]=void 0))}function ki(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let Ei=0;function Li(e,t,n,o){const r=e._endId=++Ei,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:s,timeout:a,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o("transitionDelay"),i=o("transitionDuration"),s=Oi(r,i),a=o("animationDelay"),l=o("animationDuration"),c=Oi(a,l);let u=null,d=0,f=0;t===vi?s>0&&(u=vi,d=s,f=i.length):"animation"===t?c>0&&(u="animation",d=c,f=l.length):(d=Math.max(s,c),u=d>0?s>c?vi:"animation":null,f=u?u===vi?i.length:l.length:0);const h=u===vi&&/\b(transform|all)(,|$)/.test(o("transitionProperty").toString());return{type:u,timeout:d,propCount:f,hasTransform:h}}(e,t);if(!s)return o();const c=s+"end";let u=0;const d=()=>{e.removeEventListener(c,f),i()},f=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),a+1),e.addEventListener(c,f)}function Oi(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Mi(t)+Mi(e[n]))))}function Mi(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}const $i=Symbol("_vod"),Ai=Symbol("_vsh"),Pi={beforeMount(e,{value:t},{transition:n}){e[$i]="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Ii(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Ii(e,!0),o.enter(e)):o.leave(e,(()=>{Ii(e,!1)})):Ii(e,t))},beforeUnmount(e,{value:t}){Ii(e,t)}};function Ii(e,t){e.style.display=t?e[$i]:"none",e[Ai]=!t}const Bi=Symbol(""),Ni=/(^|;)\s*display\s*:/;const Ri=/\s*!important$/;function Fi(e,t,n){if(h(n))n.forEach((n=>Fi(e,t,n)));else if(null==n&&(n=""),n=Yi(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=Hi[t];if(n)return n;let o=L(t);if("filter"!==o&&o in e)return Hi[t]=o;o=$(o);for(let r=0;r<ji.length;r++){const n=ji[r]+o;if(n in e)return Hi[t]=n}return t}(e,t);Ri.test(n)?e.setProperty(M(o),n.replace(Ri,""),"important"):e[o]=n}}const ji=["Webkit","Moz","ms"],Hi={};const{unit:Di,unitRatio:Wi,unitPrecision:Vi}={unit:"rem",unitRatio:10/320,unitPrecision:5},zi=(qi=Di,Ui=Wi,Xi=Vi,e=>e.replace(ge,((e,t)=>{if(!t)return e;if(1===Ui)return`${t}${qi}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*Ui,Xi);return 0===n?"0":`${n}${qi}`})));var qi,Ui,Xi;const Yi=e=>v(e)?zi(e):e,Ki="http://www.w3.org/1999/xlink";const Ji=Symbol("_vei");function Gi(e,t,n,o,r=null){const i=e[Ji]||(e[Ji]={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(Qi.test(e)){let n;for(t={};n=e.match(Qi);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):M(e.slice(2)),t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&h(i)){const n=ts(e,i);for(let o=0;o<n.length;o++){const i=n[o];ln(i,t,5,i.__wwe?[e]:r(e))}}else ln(ts(e,n.value),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>Zi||(es.then((()=>Zi=0)),Zi=Date.now()))(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const Qi=/(?:Once|Passive|Capture)$/;let Zi=0;const es=Promise.resolve();function ts(e,t){if(h(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const ns=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;const os=["ctrl","shift","alt","meta"],rs={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>os.some((n=>e[`${n}Key`]&&!t.includes(n)))},is=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(n,...o)=>{for(let e=0;e<t.length;e++){const o=rs[t[e]];if(o&&o(n,t))return}return e(n,...o)})},ss=c({patchProp:(e,t,n,o,r,i,s,c,u)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;bn((()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,s);const d="svg"===r;"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e[yi];i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,d):"style"===t?function(e,t,n){const o=e.style,r=v(n);let i=!1;if(n&&!r){if(t)if(v(t))for(const e of t.split(";")){const t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Fi(o,t,"")}else for(const e in t)null==n[e]&&Fi(o,e,"");for(const e in n)"display"===e&&(i=!0),Fi(o,e,n[e])}else if(r){if(t!==n){const e=o[Bi];e&&(n+=";"+e),o.cssText=n,i=Ni.test(n)}}else t&&e.removeAttribute("style");$i in e&&(e[$i]=i?o.display:"",e[Ai]&&(o.display="none"));const{__wxsStyle:s}=e;if(s)for(const a in s)Fi(o,a,s[a])}(e,n,o):a(t)?l(t)||Gi(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&ns(t)&&m(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){const t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}if(ns(t)&&v(n))return!1;return t in e}(e,t,o,d))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);const a=e.tagName;if("value"===t&&"PROGRESS"!==a&&!a.includes("-")){const o=null==n?"":n;return("OPTION"===a?e.getAttribute("value")||"":e.value)===o&&"_value"in e||(e.value=o),null==n&&e.removeAttribute(t),void(e._value=n)}let l=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=U(n):null==n&&"string"===o?(n="",l=!0):"number"===o&&(n=0,l=!0)}try{e[t]=n}catch(c){}l&&e.removeAttribute(t)}(e,t,o,i,s,c,u):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(Ki,t.slice(6,t.length)):e.setAttributeNS(Ki,t,n);else{const o=q(t);null==n||o&&!U(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,d))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},mi);let as;const ls=(...e)=>{const t=(as||(as=br(ss))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(v(e)){return document.querySelector(e)}return e}
/*!
  * vue-router v4.3.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */(e);if(!o)return;const r=t._component;m(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};var cs,us,ds,fs;(us=cs||(cs={})).pop="pop",us.push="push",(fs=ds||(ds={})).back="back",fs.forward="forward",fs.unknown="";const hs=Symbol("");var ps,gs;(gs=ps||(ps={}))[gs.aborted=4]="aborted",gs[gs.cancelled=8]="cancelled",gs[gs.duplicated=16]="duplicated";const ms=["{","}"];const vs=/^(?:\d)+/,ys=/^(?:\w)+/;const _s=Object.prototype.hasOwnProperty,bs=(e,t)=>_s.call(e,t),ws=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=ms){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=vs.test(t)?"list":a&&ys.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function xs(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class Ss{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||ws,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=xs(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{bs(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=xs(t,this.messages))&&(o=this.messages[t]):n=t,bs(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function Ts(e,t={},n,o){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&Tc?Tc():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new Ss({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=cf().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}const Cs=re((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let ks;function Es(){if(!ks){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,ks=Ts(e),Cs()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>ks.add(e,__uniConfig.locales[e]))),ks.setLocale(e)}}return ks}function Ls(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const Os=re((()=>{const e="uni.async.",t=["error"];Es().add("en",Ls(e,t,["The connection timed out, click the screen to try again."]),!1),Es().add("es",Ls(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),Es().add("fr",Ls(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),Es().add("zh-Hans",Ls(e,t,["连接服务器超时，点击屏幕重试"]),!1),Es().add("zh-Hant",Ls(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),Ms=re((()=>{const e="uni.showToast.",t=["unpaired"];Es().add("en",Ls(e,t,["Please note showToast must be paired with hideToast"]),!1),Es().add("es",Ls(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),Es().add("fr",Ls(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),Es().add("zh-Hans",Ls(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),Es().add("zh-Hant",Ls(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),$s=re((()=>{const e="uni.showLoading.",t=["unpaired"];Es().add("en",Ls(e,t,["Please note showLoading must be paired with hideLoading"]),!1),Es().add("es",Ls(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),Es().add("fr",Ls(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),Es().add("zh-Hans",Ls(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),Es().add("zh-Hant",Ls(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),As=re((()=>{const e="uni.showModal.",t=["cancel","confirm"];Es().add("en",Ls(e,t,["Cancel","OK"]),!1),Es().add("es",Ls(e,t,["Cancelar","OK"]),!1),Es().add("fr",Ls(e,t,["Annuler","OK"]),!1),Es().add("zh-Hans",Ls(e,t,["取消","确定"]),!1),Es().add("zh-Hant",Ls(e,t,["取消","確定"]),!1)})),Ps=re((()=>{const e="uni.chooseFile.",t=["notUserActivation"];Es().add("en",Ls(e,t,["File chooser dialog can only be shown with a user activation"]),!1),Es().add("es",Ls(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),Es().add("fr",Ls(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),Es().add("zh-Hans",Ls(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),Es().add("zh-Hant",Ls(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)}));function Is(e){const t=new Le;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let Bs=1;const Ns=Object.create(null);function Rs(e,t){return e+"."+t}function Fs({id:e,name:t,args:n},o){t=Rs(o,t);const r=t=>{e&&jh.publishHandler("invokeViewApi."+e,t)},i=Ns[t];i?i(n,r):r({})}const js=c(Is("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=jh,i=n?Bs++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),Hs=me(!0);let Ds;function Ws(){Ds&&(clearTimeout(Ds),Ds=null)}let Vs=0,zs=0;function qs(e){if(Ws(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];Vs=t,zs=n,Ds=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function Us(e){if(!Ds)return;if(1!==e.touches.length)return Ws();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Vs)>10||Math.abs(n-zs)>10?Ws():void 0}function Xs(e,t){const n=Number(e);return isNaN(n)?t:n}function Ys(){const e=__uniConfig.globalStyle||{},t=Xs(e.rpxCalcMaxDeviceWidth,960),n=Xs(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function Ks(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Js,Gs,Qs=["top","left","right","bottom"],Zs={};function ea(){return Gs="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function ta(){if(Gs="string"==typeof Gs?Gs:ea()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Qs.forEach((function(e){s(o,e)})),document.body.appendChild(o),i(),Js=!0}else Qs.forEach((function(e){Zs[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Gs+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i((function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){oa.length||setTimeout((function(){var e={};oa.forEach((function(t){e[t]=Zs[t]})),oa.length=0,ra.forEach((function(t){t(e)}))}),0);oa.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(Zs,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function na(e){return Js||ta(),Zs[e]}var oa=[];var ra=[];const ia=Ks({get support(){return 0!=("string"==typeof Gs?Gs:ea()).length},get top(){return na("top")},get left(){return na("left")},get right(){return na("right")},get bottom(){return na("bottom")},onChange:function(e){ea()&&(Js||ta(),"function"==typeof e&&ra.push(e))},offChange:function(e){var t=ra.indexOf(e);t>=0&&ra.splice(t,1)}}),sa=is((()=>{}),["prevent"]),aa=is((e=>{}),["stop"]);function la(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function ca(){const e=la(document.documentElement.style,"--window-top");return e?e+ia.top:0}function ua(){const e=document.documentElement.style,t=ca(),n=la(e,"--window-bottom"),o=la(e,"--window-left"),r=la(e,"--window-right"),i=la(e,"--top-window-height");return{top:t,bottom:n?n+ia.bottom:0,left:o?o+ia.left:0,right:r?r+ia.right:0,topWindowHeight:i||0}}function da(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function fa(e){return Symbol(e)}function ha(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function pa(e,t=!1){if(t)return function(e){if(!ha(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>lc(parseFloat(t))+"px"))}(e);if(v(e)){const t=parseInt(e)||0;return ha(e)?lc(t):t}return e}function ga(e){return e.$page}function ma(e){return 0===e.tagName.indexOf("UNI-")}const va="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",ya="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z";function _a(e,t="#000",n=27){return Vr("svg",{width:n,height:n,viewBox:"0 0 32 32"},[Vr("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function ba(){{const{$pageInstance:e}=ti();return e&&ka(e.proxy)}}function wa(){const e=vu(),t=e.length;if(t)return e[t-1]}function xa(){var e;const t=null==(e=wa())?void 0:e.$page;if(t)return t.meta}function Sa(){const e=wa();if(e)return e.$vm}const Ta=["navigationBar","pullToRefresh"];function Ca(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=c({id:t},n,e);Ta.forEach((t=>{o[t]=c({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function ka(e){var t,n;return(null==(t=e.$page)?void 0:t.id)||(null==(n=e.$basePage)?void 0:n.id)}function Ea(e,t,n){if(v(e))n=t,t=e,e=Sa();else if("number"==typeof e){const t=vu().find((t=>ga(t).id===e));e=t?t.$vm:Sa()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function La(e){e.preventDefault()}let Oa,Ma=0;function $a({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-Ma)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(Ma=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(Oa=setTimeout(s,300))),o=!1};return function(){clearTimeout(Oa),o||requestAnimationFrame(s),o=!0}}function Aa(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Aa(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),oe(i.concat(n).join("/"))}function Pa(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}function Ia(){Ys(),he(ma),window.addEventListener("touchstart",qs,Hs),window.addEventListener("touchmove",Us,Hs),window.addEventListener("touchend",Ws,Hs),window.addEventListener("touchcancel",Ws,Hs)}class Ba{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(ce(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&ce(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=ja(this.$el.querySelector(e));return t?Na(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=ja(n[o]);e&&t.push(Na(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||v(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:M(n);(v(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(v(e)&&(e=V(e)),S(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];m(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&jh.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Na(e,t=!0){if(t&&e&&(e=le(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Ba(e)),e.$el.__wxsComponentDescriptor}function Ra(e,t){return Na(e,t)}function Fa(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>Ra(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=le(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,Ra(r,!1)]}}function ja(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function Ha(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e;let s,a;s=ve(t?r:function(e){for(;!ma(e);)e=e.parentElement;return e}(r)),a=ve(i);const l={type:n,timeStamp:o,target:s,detail:{},currentTarget:a};return e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),function(e,t){c(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(l,e),l}function Da(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Wa(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const Va=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=!ma(o);if(r)return Fa(e,t,n,!1)||[e];const i=Ha(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=ca();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Da(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=ca();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Da(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=ca();i.touches=Wa(e.touches,t),i.changedTouches=Wa(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return Fa(i,t,n)||[i]},createNativeEvent:Ha},Symbol.toStringTag,{value:"Module"});function za(e){!function(e){const t=e.globalProperties;c(t,Va),t.$gcd=Ra}(e._context.config)}let qa=1;function Ua(e){return(e||function(){const e=xa();return e?e.id:-1}())+".invokeViewApi"}const Xa=c(Is("view"),{invokeOnCallback:(e,t)=>Hh.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Hh,s=o?qa++:0;o&&r("invokeViewApi."+s,o,!0),i(Ua(n),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=Hh,a=qa++,l="invokeViewApi."+a;return r(l,n),s(Ua(o),{id:a,name:e,args:t},o),()=>{i(l)}}});function Ya(e){Ea(wa(),"onResize",e),Hh.invokeOnCallback("onWindowResize",e)}function Ka(e){const t=wa();Ea(cf(),"onShow",e),Ea(t,"onShow")}function Ja(){Ea(cf(),"onHide"),Ea(wa(),"onHide")}const Ga=["onPageScroll","onReachBottom"];function Qa(){Ga.forEach((e=>Hh.subscribe(e,function(e){return(t,n)=>{Ea(parseInt(n),e,t)}}(e))))}function Za(){!function(){const{on:e}=Hh;e("onResize",Ya),e("onAppEnterForeground",Ka),e("onAppEnterBackground",Ja)}(),Qa()}function el(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new xe(this.$page.id)),e.eventChannel}}function tl(e){e._context.config.globalProperties.getOpenerEventChannel=el}function nl(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function ol(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${lc(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function rl(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach((e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(s=s.map(ol)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?ol(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach((t=>{e.$el.style[t]=a[t]})),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout((()=>{i()}),0)}const il={props:["animation"],watch:{animation:{deep:!0,handler(){rl(this)}}},mounted(){rl(this)}},sl=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(il),al(e)},al=e=>(e.__reserved=!0,e.compatConfig={MODE:3},ho(e));function ll(e){return e.__wwe=!0,e}function cl(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){let r;return r=ve(n),{type:o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const ul={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function dl(e){const t=en(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:ll((function(e){e.touches.length>1||s(e)})),onMousedown:ll((function(e){r||(s(e),window.addEventListener("mouseup",l))})),onTouchend:ll((function(){a()})),onMouseup:ll((function(){r&&l()})),onTouchcancel:ll((function(){r=!1,t.value=!1,clearTimeout(n)}))}}}function fl(e,t){return v(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}const hl=fa("uf"),pl=fa("ul");function gl(e,t,n){const o=ba();n&&!e||S(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&jh.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?jh.on(r,t[r]):e&&jh.on(`uni-${r}-${o}-${e}`,t[r])}))}function ml(e,t,n){const o=ba();n&&!e||S(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&jh.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?jh.off(r,t[r]):e&&jh.off(`uni-${r}-${o}-${e}`,t[r])}))}const vl=sl({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=en(null),o=rr(hl,!1),{hovering:r,binding:i}=dl(e),s=ll(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),a=rr(pl,!1);return a&&(a.addHandler(s),Lo((()=>{a.removeHandler(s)}))),function(e,t){gl(e.id,t),Yn((()=>e.id),((e,n)=>{ml(n,t,!0),gl(e,t,!0)})),Oo((()=>{ml(e.id,t)}))}(e,{"label-click":s}),()=>{const o=e.hoverClass,a=fl(e,"disabled"),l=fl(e,"loading"),c=fl(e,"plain"),u=o&&"none"!==o;return Vr("uni-button",Jr({ref:n,onClick:s,id:e.id,class:u&&r.value?o:""},u&&i,a,l,c),[t.default&&t.default()],16,["onClick","id"])}}}),yl=fa("upm");function _l(){return rr(yl)}function bl(e){const t=function(e){return jt(function(e){{const{navigationBar:t}=e,{titleSize:n,titleColor:o,backgroundColor:r}=t;t.titleText=t.titleText||"",t.type=t.type||"default",t.titleSize=n||"16px",t.titleColor=o||"#000000",t.backgroundColor=r||"#F8F8F8"}return e}(JSON.parse(JSON.stringify(Ca(__uniRoutes[0].meta,e)))))}(e);return or(yl,t),t}function wl(){const e=location.href,t=e.indexOf("?"),n=e.indexOf("#",t>-1?t:0);let o={};t>-1&&(o=we(e.slice(t+1,n>-1?n:e.length)));const{meta:r}=__uniRoutes[0],i=oe(r.route);return{meta:r,query:o,path:i,matched:[{path:i}]}}function xl(){return history.state&&history.state.__id__||1}const Sl=["original","compressed"],Tl=["album","camera"],Cl=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function kl(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function El(e,t){return!h(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function Ll(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let Ol=1;const Ml={};function $l(e,t,n){if("number"==typeof e){const o=Ml[e];if(o)return o.keepAlive||delete Ml[e],o.callback(t,n)}return t}const Al="success",Pl="fail",Il="complete";function Bl(e,t={},{beforeAll:n,beforeSuccess:o}={}){S(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];m(o)&&(t[n]=Ll(o),delete e[n])}return t}(t),a=m(r),l=m(i),c=m(s),u=Ol++;return function(e,t,n,o=!1){Ml[e]={name:t,keepAlive:o,callback:n}}(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),m(n)&&n(u),u.errMsg===e+":ok"?(m(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)})),u}const Nl="success",Rl="fail",Fl="complete",jl={},Hl={};function Dl(e,t){return function(n){return e(n,t)||n}}function Wl(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Dl(i,n));else{const e=i(t,n);if(b(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function Vl(e,t={}){return[Nl,Rl,Fl].forEach((n=>{const o=e[n];if(!h(o))return;const r=t[n];t[n]=function(e){Wl(o,e,t).then((e=>m(r)&&r(e)||e))}})),t}function zl(e,t){const n=[];h(jl.returnValue)&&n.push(...jl.returnValue);const o=Hl[e];return o&&h(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function ql(e){const t=Object.create(null);Object.keys(jl).forEach((e=>{"returnValue"!==e&&(t[e]=jl[e].slice())}));const n=Hl[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Ul(e,t,n,o){const r=ql(e);if(r&&Object.keys(r).length){if(h(r.invoke)){return Wl(r.invoke,n).then((n=>t(Vl(ql(e),n),...o)))}return t(Vl(r,n),...o)}return t(n,...o)}function Xl(e,t){return(n={},...o)=>function(e){return!(!S(e)||![Al,Pl,Il].find((t=>m(e[t]))))}(n)?zl(e,Ul(e,t,n,o)):zl(e,new Promise(((r,i)=>{Ul(e,t,c(n,{success:r,fail:i}),o)})))}function Yl(e,t,n,o={}){const r=t+":fail";let i="";return i=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,$l(e,c({errMsg:i},o))}function Kl(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(v(e))return e}const r=function(e,t){const n=e[0];if(!t||!t.formatArgs||!S(t.formatArgs)&&S(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(m(s)){const o=s(e[0][t],n);if(v(o))return o}else f(n,t)||(n[t]=s)}}(t,o);if(r)return r}function Jl(e,t,n,o){return n=>{const r=Bl(e,n,o),i=Kl(0,[n],0,o);return i?Yl(r,e,i):t(n,{resolve:t=>function(e,t,n){return $l(e,c(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Yl(r,e,function(e){return!e||v(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function Gl(e,t,n,o){return Xl(e,Jl(e,t,0,o))}function Ql(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=Kl(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Zl(e,t,n,o){return Xl(e,function(e,t,n,o){return Jl(e,t,0,o)}(e,t,0,o))}let ec=!1,tc=0,nc=0,oc=960,rc=375,ic=750;function sc(){const{windowWidth:e,pixelRatio:t,platform:n}=function(){const e=Du(),t=zu(Vu(e,Wu(e)));return{platform:Nu?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();tc=e,nc=t,ec="ios"===n}function ac(e,t){const n=Number(e);return isNaN(n)?t:n}const lc=Ql(0,((e,t)=>{if(0===tc&&(sc(),function(){const e=__uniConfig.globalStyle||{};oc=ac(e.rpxCalcMaxDeviceWidth,960),rc=ac(e.rpxCalcBaseDeviceWidth,375),ic=ac(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||tc;n=e===ic||n<=oc?n:rc;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==nc&&ec?.5:1),e<0?-o:o})),cc=[.5,.8,1,1.25,1.5,2];const uc=(e,t,n,o)=>{!function(e,t,n,o,r){Hh.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};const dc={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function fc(e){let t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(f(dc,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(dc[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return console.error("unsupported color:"+e),[0,0,0,255]}class hc{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,fc(t)])}}class pc{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class gc{constructor(e){this.width=e}}let mc=0,vc={};function yc(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(mc++);r.callbackId=e,vc[e]=o}Hh.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(!function(e,t){e=e||{},v(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?m(e.success)&&e.success(t):m(e.fail)&&e.fail(t),m(e.complete)&&e.complete(t)}(vc[e],t),delete vc[e])}))}const _c={canvas:class{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}setFillStyle(e){console.log("initCanvasContextProperty implemented.")}setStrokeStyle(e){console.log("initCanvasContextProperty implemented.")}setShadow(e,t,n,o){console.log("initCanvasContextProperty implemented.")}addColorStop(e,t){console.log("initCanvasContextProperty implemented.")}setLineWidth(e){console.log("initCanvasContextProperty implemented.")}setLineCap(e){console.log("initCanvasContextProperty implemented.")}setLineJoin(e){console.log("initCanvasContextProperty implemented.")}setLineDash(e,t){console.log("initCanvasContextProperty implemented.")}setMiterLimit(e){console.log("initCanvasContextProperty implemented.")}fillRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}strokeRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}clearRect(e,t,n,o){console.log("initCanvasContextProperty implemented.")}fill(){console.log("initCanvasContextProperty implemented.")}stroke(){console.log("initCanvasContextProperty implemented.")}scale(e,t){console.log("initCanvasContextProperty implemented.")}rotate(e){console.log("initCanvasContextProperty implemented.")}translate(e,t){console.log("initCanvasContextProperty implemented.")}setFontSize(e){console.log("initCanvasContextProperty implemented.")}fillText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTextAlign(e){console.log("initCanvasContextProperty implemented.")}setTextBaseline(e){console.log("initCanvasContextProperty implemented.")}drawImage(e,t,n,o,r,i,s,a,l){console.log("initCanvasContextProperty implemented.")}setGlobalAlpha(e){console.log("initCanvasContextProperty implemented.")}strokeText(e,t,n,o){console.log("initCanvasContextProperty implemented.")}setTransform(e,t,n,o,r,i){console.log("initCanvasContextProperty implemented.")}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],function(e,t,n,o,r){Hh.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new hc("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new hc("radial",[e,t,n])}createPattern(e,t){if(void 0===t)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new pc(e,t);console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+t+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}measureText(e,t){let n=0;return n=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new gc(n)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],s=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(s.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal"].indexOf(e)>-1?(s.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(s.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&a()})),1===o.length&&a(),o=s.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function a(){s.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}},map:class{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){uc(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){uc(this.id,this.pageId,"moveToLocation",e)}getScale(e){uc(this.id,this.pageId,"getScale",e)}getRegion(e){uc(this.id,this.pageId,"getRegion",e)}includePoints(e){uc(this.id,this.pageId,"includePoints",e)}translateMarker(e){uc(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){uc(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){uc(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){uc(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){uc(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){uc(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){uc(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){uc(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){uc(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){uc(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){uc(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){uc(this.id,this.pageId,"openMapApp",e)}on(e,t){uc(this.id,this.pageId,"on",{name:e,callback:t})}},video:class{constructor(e,t){this.id=e,this.pageId=t}play(){qu(this.id,this.pageId,"play")}pause(){qu(this.id,this.pageId,"pause")}stop(){qu(this.id,this.pageId,"stop")}seek(e){qu(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){qu(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~cc.indexOf(e)||(e=1),qu(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){qu(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){qu(this.id,this.pageId,"exitFullScreen")}showStatusBar(){qu(this.id,this.pageId,"showStatusBar")}hideStatusBar(){qu(this.id,this.pageId,"hideStatusBar")}},editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){yc(this.id,this.pageId,e,t)}}};function bc(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=_c[n];e.context=new r(t,o),delete e.contextInfo}}class wc{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery._push(this._selector,this._component,this._single,{node:!0},e),this._selectorQuery}}class xc{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return function(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};e.id&&(t.id="");e.dataset&&(t.dataset={});e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0);e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight);if(e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){if(!e)return t.$el;return e.$el}(t,e),s=i.parentElement;if(!s)return o?null:[];const{nodeType:a}=i,l=3===a||8===a;if(o){const e=l?s.querySelector(n):Xu(i,n)?i:i.querySelector(n);return e?Uu(e,r):null}{let e=[];const t=(l?s:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push(Uu(t,r))})),!l&&Xu(i,n)&&e.unshift(Uu(i,r)),e}}(e,t,n,r,i))})),n(o)}(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{h(e)?e.forEach(bc):bc(e);const o=n[t];m(o)&&o.call(this,e)})),m(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=ae(e),this}select(e){return this._nodesRef=new wc(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new wc(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new wc(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const Sc=Ql(0,(e=>((e=ae(e))&&!function(e){const t=ae(e);if(t.$page)return ka(t);if(!t.$)return;{const{$pageInstance:e}=t.$;if(e)return ka(e.proxy)}const n=t.$.root.proxy;return n&&n.$page?ka(n):void 0}(e)&&(e=null),new xc(e||Sa())))),Tc=Ql(0,(()=>{const e=cf();return e&&e.$vm?e.$vm.$locale:Es().getLocale()})),Cc={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};const kc={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=El(e,Sl)},sourceType(e,t){t.sourceType=El(e,Tl)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},Ec={formatArgs:{src(e,t){t.src=Pu(e)}}},Lc={formatArgs:{urls(e,t){t.urls=e.map((e=>v(e)&&e?Pu(e):""))},current(e,t){"number"==typeof e?t.current=e>0&&e<t.urls.length?e:0:v(e)&&e&&(t.current=Pu(e))}}},Oc="json",Mc=["text","arraybuffer"],$c=encodeURIComponent;ArrayBuffer,Boolean;const Ac={formatArgs:{method(e,t){t.method=kl((e||"").toUpperCase(),Cl)},data(e,t){t.data=e||""},url(e,t){t.method===Cl[0]&&S(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),s={};i.forEach((e=>{const t=e.split("=");s[t[0]]=t[1]}));for(const a in t)if(f(t,a)){let e=t[a];null==e?e="":S(e)&&(e=JSON.stringify(e)),s[$c(a)]=$c(e)}return r=Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==Cl[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||Oc).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===Mc.indexOf(t.responseType)&&(t.responseType="text")}}},Pc={formatArgs:{filePath(e,t){e&&(t.filePath=Pu(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}};const Ic={url:{type:String,required:!0}},Bc=(jc(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),jc(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),Wc("navigateTo")),Nc=Wc("redirectTo"),Rc=Wc("reLaunch"),Fc={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(vu().length-1,e)}}};function jc(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let Hc;function Dc(){Hc=""}function Wc(e){return{formatArgs:{url:Vc(e)},beforeAll:Dc}}function Vc(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/")||0===e.indexOf("uni:"))return e;let t="";const n=vu();return n.length&&(t=ga(n[n.length-1]).route),Aa(t,e)}(t)).split("?")[0],r=Pa(o,!0);if(!r)return"page `"+t+"` is not found";if("navigateTo"===e||"redirectTo"===e){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if("switchTab"===e&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if("switchTab"!==e&&"preloadPage"!==e||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!v(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if("preloadPage"!==e){if(Hc===t&&"appLaunch"!==n.openType)return`${Hc} locked`;__uniConfig.ready&&(Hc=t)}else if(r.meta.isTabBar){const e=vu(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const zc={formatArgs:{animation(e,t){e||(e={duration:0,timingFunc:"linear"}),t.animation={duration:e.duration||0,timingFunc:e.timingFunc||"linear"}}}},qc=(Boolean,{formatArgs:{title:"",mask:!1}}),Uc=(Boolean,{beforeInvoke(){As()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!f(t,"cancelText")){const{t:e}=Es();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!f(t,"confirmText")){const{t:e}=Es();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),Xc=["success","loading","none","error"],Yc=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=kl(e,Xc)},image(e,t){t.image=e?Pu(e):""},duration:1500,mask:!1}});function Kc(){const e=Sa();if(!e)return;const t=mu(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:_u(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,Ea(e,"onHide"))}function Jc(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}function Gc(e){const t=mu().values();for(const n of t){const t=uu(n);if(Jc(e,t))return n.$.__isActive=!0,t.id}}const Qc=Zl("switchTab",(({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(du.handledBeforeEntryPageRoutes)return Kc(),ou({type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},Gc(e)).then(o).catch(r);hu.push({args:{type:"switchTab",url:e,tabBarText:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,Wc("switchTab"));function Zc(){const e=wa();if(!e)return;const t=uu(e);_u(Su(t.path,t.id))}const eu=Zl("redirectTo",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(du.handledBeforeEntryPageRoutes)return Zc(),ou({type:"redirectTo",url:e,isAutomatedTesting:t}).then(n).catch(o);pu.push({args:{type:"redirectTo",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,Nc);function tu(){const e=mu().keys();for(const t of e)_u(t)}const nu=Zl("reLaunch",(({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(du.handledBeforeEntryPageRoutes)return tu(),ou({type:"reLaunch",url:e,isAutomatedTesting:t}).then(n).catch(o);gu.push({args:{type:"reLaunch",url:e,isAutomatedTesting:t},resolve:n,reject:o})}),0,Rc);function ou({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:r},i){const s=cf().$router,{path:a,query:l}=function(e){const[t,n]=e.split("?",2);return{path:t,query:we(n||"")}}(t);return new Promise(((t,c)=>{const u=function(e,t){return{__id__:t||++bu,__type__:e}}(e,i);s["navigateTo"===e?"push":"replace"]({path:a,query:l,state:u,force:!0}).then((i=>{if(function(e,t){return e instanceof Error&&hs in e&&(null==t||!!(e.type&t))}(i))return c(i.message);if("switchTab"===e&&(s.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=s.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new xe(u.__id__,o),t(r?{__id__:u.__id__}:{eventChannel:e.eventChannel})}return r?t({__id__:u.__id__}):t()}))}))}function ru(e){const t=window.CSS&&window.CSS.supports;return t&&(t(e)||t.apply(window.CSS,e.split(":")))}const iu=ru("top:env(a)"),su=ru("top:constant(a)"),au=(()=>iu?"env":su?"constant":"")();function lu(e){let t=0;var n,o;"custom"!==e.navigationBar.style&&["default","float"].indexOf(e.navigationBar.type)>-1&&(t=44),da({"--window-top":(o=t,au?`calc(${o}px + ${au}(safe-area-inset-top))`:`${o}px`),"--window-bottom":(n=0,au?`calc(${n}px + ${au}(safe-area-inset-bottom))`:`${n}px`)})}const cu=new Map;function uu(e){return e.$page}const du={handledBeforeEntryPageRoutes:!1},fu=[],hu=[],pu=[],gu=[];function mu(){return cu}function vu(){return yu()}function yu(){const e=[],t=cu.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function _u(e,t=!0){const n=cu.get(e);n.$.__isUnload=!0,Ea(n,"onUnload"),cu.delete(e),t&&function(e){const t=Tu.get(e);t&&(Tu.delete(e),Cu.pruneCacheEntry(t))}(e)}let bu=xl();function wu(e){const t=_l();return function(e,t,n,o,r,i){const{id:s,route:a}=o,l=$e(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:oe(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",__uniRoutes[0].path,{},t)}function xu(e){e.$route;const t=wu();!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),cu.set(Su(t.path,t.id),e),1===cu.size&&setTimeout((()=>{!function(){if(du.handledBeforeEntryPageRoutes)return;du.handledBeforeEntryPageRoutes=!0;const e=[...fu];fu.length=0,e.forEach((({args:e,resolve:t,reject:n})=>ou(e).then(t).catch(n)));const t=[...hu];hu.length=0,t.forEach((({args:e,resolve:t,reject:n})=>(Kc(),ou(e,Gc(e.url)).then(t).catch(n))));const n=[...pu];pu.length=0,n.forEach((({args:e,resolve:t,reject:n})=>(Zc(),ou(e).then(t).catch(n))));const o=[...gu];gu.length=0,o.forEach((({args:e,resolve:t,reject:n})=>(tu(),ou(e).then(t).catch(n))))}()}),0)}function Su(e,t){return e+"$$"+t}const Tu=new Map,Cu={get:e=>Tu.get(e),set(e,t){!function(e){const t=parseInt(e.split("$$")[1]);if(!t)return;Cu.forEach(((e,n)=>{const o=parseInt(n.split("$$")[1]);o&&o>t&&(Cu.delete(n),Cu.pruneCacheEntry(e),bn((()=>{cu.forEach(((e,t)=>{e.$.isUnmounted&&cu.delete(t)}))})))}))}(e),Tu.set(e,t)},delete(e){Tu.get(e)&&Tu.delete(e)},forEach(e){Tu.forEach(e)}};function ku(e,t){!function(e){const t=Lu(e),{body:n}=document;Ou&&n.removeAttribute(Ou),t&&n.setAttribute(t,""),Ou=t}(e),lu(t),function(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}(t),$u(e,t)}function Eu(e){const t=Lu(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function Lu(e){return e.type.__scopeId}let Ou,Mu;function $u(e,t){if(document.removeEventListener("touchmove",La),Mu&&document.removeEventListener("scroll",Mu),t.disableScroll)return document.addEventListener("touchmove",La);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!(null==n?void 0:n.length)&&!(null==o?void 0:o.length)&&!r)return;const i={},s=uu(e.proxy).id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&jh.publishHandler("onPageScroll",{scrollTop:o},e),n&&jh.emit(e+".onPageScroll",{scrollTop:o})}}(s,n,r)),(null==o?void 0:o.length)&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>jh.publishHandler("onReachBottom",{},s)),Mu=$a(i),requestAnimationFrame((()=>document.addEventListener("scroll",Mu)))}function Au(e){const{base:t}=__uniConfig.router;return 0===oe(e).indexOf(t)?oe(e):t+e}function Pu(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0!==e.indexOf("./")||!e.includes("/static/")&&0!==e.indexOf("./"+(n||"assets")+"/")||(e=e.slice(1))),0===e.indexOf("/")){if(0!==e.indexOf("//"))return Au(e.slice(1));e="https:"+e}if(ee.test(e)||te.test(e)||0===e.indexOf("blob:"))return e;const o=yu();return o.length?Au(Aa(uu(o[o.length-1]).route,e).slice(1)):e}const Iu=navigator.userAgent,Bu=/android/i.test(Iu),Nu=/iphone|ipad|ipod/i.test(Iu),Ru=Iu.match(/Windows NT ([\d|\d.\d]*)/i),Fu=/Macintosh|Mac/i.test(Iu),ju=/Linux|X11/i.test(Iu),Hu=Fu&&navigator.maxTouchPoints>0;function Du(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Wu(e){return e&&90===Math.abs(window.orientation)}function Vu(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function zu(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function qu(e,t,n,o){Hh.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function Uu(e,t){const n={},{top:o,topWindowHeight:r}=ua();if(t.node){const t=e.tagName.split("-")[1]||e.tagName;t&&(n.node=e.querySelector(t))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=pe(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(h(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(h(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function Xu(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}const Yu={};function Ku(e,t){const n=Yu[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const s=new Uint8Array(i);for(;i--;)s[i]=r.charCodeAt(i);return Ju(s,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function Ju(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function Gu(e){for(const n in Yu)if(f(Yu,n)){if(Yu[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return Yu[t]=e,t}const Qu=nl(),Zu=nl();const ed=sl({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=en(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=jt({width:-1,height:-1});return Yn((()=>c({},o)),(e=>t("resize",e))),()=>{const t=e.value;t&&(o.width=t.offsetWidth,o.height=t.offsetHeight,n())}}(n,t,o);return function(e,t,n,o){yo(o),Co((()=>{t.initial&&bn(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>Vr("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[Vr("div",{onScroll:r},[Vr("div",null,null)],40,["onScroll"]),Vr("div",{onScroll:r},[Vr("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});const td={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},nd={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},od={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},rd=sl({name:"Image",props:td,setup(e,{emit:t}){const n=en(null),o=function(e,t){const n=en(""),o=fi((()=>{let e="auto",o="";const r=od[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=jt({rootEl:e,src:fi((()=>t.src?Pu(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return Co((()=>{const t=e.value;r.origWidth=t.clientWidth||0,r.origHeight=t.clientHeight||0})),r}(n,e),r=cl(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=nd[o];if(!r)return;const{origWidth:i,origHeight:s}=n,a=i&&s?i/s:0;if(!a)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){id&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,a))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return Yn((()=>t.mode),((e,t)=>{nd[t]&&r(),nd[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,s;const a=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void a();i=i||new Image,i.onload=e=>{const{width:u,height:d}=i;a(u,d,l),bn((()=>{o()})),i.draggable=t.draggable,s&&s.remove(),s=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:d})},i.onerror=t=>{a(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};Yn((()=>e.src),(e=>l(e))),Yn((()=>e.imgSrc),(e=>{!e&&s&&(s.remove(),s=null)})),Co((()=>l(e.src))),Lo((()=>c()))}(o,e,n,i,r),()=>Vr("uni-image",{ref:n},[Vr("div",{style:o.modeStyle},null,4),nd[e.mode]?Vr(ed,{onResize:i},null,8,["onResize"]):Vr("span",null,null)],512)}});const id="Google Inc."===navigator.vendor;const sd=me(!0),ad=[];let ld=0,cd=!1;const ud=e=>ad.forEach((t=>t.userAction=e));const dd=["class","style"],fd=/^on[A-Z]+/,hd=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=ti(),r=tn({}),i=tn({}),s=tn({}),a=n.concat(dd);return o.attrs=jt(o.attrs),Un((()=>{const e=(n=o.attrs,Object.keys(n).map((e=>[e,n[e]]))).reduce(((e,[n,o])=>(a.includes(n)?e.exclude[n]=o:fd.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e)),{exclude:{},attrs:{},listeners:{}});var n;r.value=e.attrs,i.value=e.listeners,s.value=e.exclude})),{$attrs:r,$listeners:i,$excludeAttrs:s}};function pd(e){const t=[];return h(e)&&e.forEach((e=>{Rr(e)?e.type===Cr?t.push(...pd(e.children)):t.push(e):h(e)&&t.push(...pd(e))})),t}const gd=sl({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=en(null),o=en(!1);let{setContexts:r,events:i}=function(e,t){const n=en(0),o=en(0),r=jt({x:null,y:null}),i=en(null);let s=null,a=[];function l(t){t&&1!==t&&(e.scaleArea?a.forEach((function(e){e._setScale(t)})):s&&s._setScale(t))}function c(e,n=a){let o=t.value;function r(e){for(let t=0;t<n.length;t++){const o=n[t];if(e===o.rootRef.value)return o}return e===o||e===document.body||e===document?null:r(e.parentNode)}return r(e)}const u=ll((t=>{let n=t.touches;if(n&&n.length>1){let t={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(i.value=md(t),r.x=t.x,r.y=t.y,!e.scaleArea){let e=c(n[0].target),t=c(n[1].target);s=e&&e===t?e:null}}})),d=ll((e=>{let t=e.touches;if(t&&t.length>1){e.preventDefault();let n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==r.x&&i.value&&i.value>0){l(md(n)/i.value)}r.x=n.x,r.y=n.y}})),f=ll((t=>{let n=t.touches;n&&n.length||t.changedTouches&&(r.x=0,r.y=0,i.value=null,e.scaleArea?a.forEach((function(e){e._endScale()})):s&&s._endScale())}));function h(){p(),a.forEach((function(e,t){e.setParent()}))}function p(){let e=window.getComputedStyle(t.value),r=t.value.getBoundingClientRect();n.value=r.width-["Left","Right"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0),o.value=r.height-["Top","Bottom"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0)}return or("movableAreaWidth",n),or("movableAreaHeight",o),{setContexts(e){a=e},events:{_onTouchstart:u,_onTouchmove:d,_onTouchend:f,_resize:h}}}(e,n);const{$listeners:s,$attrs:a,$excludeAttrs:l}=hd(),c=s.value;["onTouchstart","onTouchmove","onTouchend"].forEach((e=>{let t=c[e],n=i[`_${e}`];c[e]=t?[].concat(t,n):n})),Co((()=>{i._resize(),o.value=!0}));let u=[];const d=[];function f(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n=n.el;const o=d.find((e=>n===e.rootRef.value));o&&e.push(Xt(o))}r(e)}return or("_isMounted",o),or("movableAreaRootRef",n),or("addMovableViewContext",(e=>{d.push(e),f()})),or("removeMovableViewContext",(e=>{const t=d.indexOf(e);t>=0&&(d.splice(t,1),f())})),()=>{const e=t.default&&t.default();return u=pd(e),Vr("uni-movable-area",Jr({ref:n},a.value,l.value,c),[Vr(ed,{onResize:i._resize},null,8,["onResize"]),u],16)}}});function md(e){return Math.sqrt(e.x*e.x+e.y*e.y)}const vd=function(e,t,n,o){e.addEventListener(t,(e=>{m(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let yd,_d;function bd(e,t,n){Lo((()=>{document.removeEventListener("mousemove",yd),document.removeEventListener("mouseup",_d)}));let o=0,r=0,i=0,s=0;const a=function(e,n,a,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:a,y:l,dx:a-o,dy:l-r,ddx:a-i,ddy:l-s,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;vd(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=s=e.touches[0].pageY,a(e,"start",o,r)})),vd(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=s=e.pageY,a(e,"start",o,r)})),vd(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=a(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,s=e.touches[0].pageY,t}}));const d=yd=function(e){if(!l&&c&&u){const t=a(e,"move",e.pageX,e.pageY);return i=e.pageX,s=e.pageY,t}};document.addEventListener("mousemove",d),vd(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,a(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const f=_d=function(e){if(c=!1,!l&&u)return u=null,a(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",f),vd(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,a(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}function wd(e,t,n){return e>t-n&&e<t+n}function xd(e,t){return wd(e,0,t)}function Sd(){}function Td(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function Cd(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function kd(e,t,n){this._springX=new Cd(e,t,n),this._springY=new Cd(e,t,n),this._springScale=new Cd(e,t,n),this._startTime=0}Sd.prototype.x=function(e){return Math.sqrt(e)},Td.prototype.setV=function(e,t){const n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},Td.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},Td.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);let t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},Td.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},Td.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},Td.prototype.dt=function(){return-this._x_v/this._x_a},Td.prototype.done=function(){const e=wd(this.s().x,this._endPositionX)||wd(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},Td.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},Td.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},Cd.prototype._solve=function(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,s=t/(r*e);return{x:function(e){return(i+s*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+s*e)*t+s*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),s=(-n+Math.sqrt(i))/(2*o),a=(t-r*e)/(s-r),l=e-a;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*t+a*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,s*e)),l*r*t+a*s*n}}}const s=Math.sqrt(4*o*r-n*n)/(2*o),a=-n/2*o,l=e,c=(t-a*e)/s;return{x:function(e){return Math.pow(Math.E,a*e)*(l*Math.cos(s*e)+c*Math.sin(s*e))},dx:function(e){const t=Math.pow(Math.E,a*e),n=Math.cos(s*e),o=Math.sin(s*e);return t*(c*s*n-l*s*o)+a*t*(c*o+l*n)}}},Cd.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},Cd.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},Cd.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!xd(t,.1)){t=t||0;let o=this._endPosition;this._solution&&(xd(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),xd(t,.1)&&(t=0),xd(o,.1)&&(o=0),o+=this._endPosition),this._solution&&xd(o-e,.1)&&xd(t,.1)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}},Cd.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},Cd.prototype.done=function(e){return e||(e=(new Date).getTime()),wd(this.x(),this._endPosition,.1)&&xd(this.dx(),.1)},Cd.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},Cd.prototype.springConstant=function(){return this._k},Cd.prototype.damping=function(){return this._c},Cd.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},kd.prototype.setEnd=function(e,t,n,o){const r=(new Date).getTime();this._springX.setEnd(e,o,r),this._springY.setEnd(t,o,r),this._springScale.setEnd(n,o,r),this._startTime=r},kd.prototype.x=function(){const e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},kd.prototype.done=function(){const e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},kd.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};function Ed(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}const Ld=sl({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.1},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,{slots:t,emit:n}){const o=en(null),r=cl(o,n),{setParent:i}=function(e,t,n){const o=rr("_isMounted",en(!1)),r=rr("addMovableViewContext",(()=>{})),i=rr("removeMovableViewContext",(()=>{}));let s,a,l=en(1),c=en(1),u=en(!1),d=en(0),f=en(0),h=null,p=null,g=!1,m=null,v=null;const y=new Sd,_=new Sd,b={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=fi((()=>{let t=Number(e.friction);return isNaN(t)||t<=0?2:t})),x=new Td(1,w.value);Yn((()=>e.disabled),(()=>{q()}));const{_updateOldScale:S,_endScale:T,_setScale:C,scaleValueSync:k,_updateBoundary:E,_updateOffset:L,_updateWH:O,_scaleOffset:M,minX:$,minY:A,maxX:P,maxY:I,FAandSFACancel:B,_getLimitXY:N,_setTransform:R,_revise:F,dampingNumber:j,xMove:H,yMove:D,xSync:W,ySync:V,_STD:z}=function(e,t,n,o,r,i,s,a,l,c){const u=fi((()=>{let t=Number(e.scaleMin);return isNaN(t)?.1:t})),d=fi((()=>{let t=Number(e.scaleMax);return isNaN(t)?10:t})),f=en(Number(e.scaleValue)||1);Yn(f,(e=>{R(e)})),Yn(u,(()=>{N()})),Yn(d,(()=>{N()})),Yn((()=>e.scaleValue),(e=>{f.value=Number(e)||0}));const{_updateBoundary:h,_updateOffset:p,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:_,maxY:b}=function(e,t,n){const o=rr("movableAreaWidth",en(0)),r=rr("movableAreaHeight",en(0)),i=rr("movableAreaRootRef"),s={x:0,y:0},a={x:0,y:0},l=en(0),c=en(0),u=en(0),d=en(0),f=en(0),h=en(0);function p(){let e=0-s.x+a.x,t=o.value-l.value-s.x-a.x;u.value=Math.min(e,t),f.value=Math.max(e,t);let n=0-s.y+a.y,i=r.value-c.value-s.y-a.y;d.value=Math.min(n,i),h.value=Math.max(n,i)}function g(){s.x=$d(e.value,i.value),s.y=Ad(e.value,i.value)}function m(o){o=o||t.value,o=n(o);let r=e.value.getBoundingClientRect();c.value=r.height/t.value,l.value=r.width/t.value;let i=c.value*o,s=l.value*o;a.x=(s-l.value)/2,a.y=(i-c.value)/2}return{_updateBoundary:p,_updateOffset:g,_updateWH:m,_scaleOffset:a,minX:u,minY:d,maxX:f,maxY:h}}(t,o,B),{FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:T,_revise:C,dampingNumber:k,xMove:E,yMove:L,xSync:O,ySync:M,_STD:$}=function(e,t,n,o,r,i,s,a,l,c,u,d,f,h){const p=fi((()=>{let e=Number(t.damping);return isNaN(e)?20:e})),g=fi((()=>"all"===t.direction||"horizontal"===t.direction)),m=fi((()=>"all"===t.direction||"vertical"===t.direction)),v=en(Id(t.x)),y=en(Id(t.y));Yn((()=>t.x),(e=>{v.value=Id(e)})),Yn((()=>t.y),(e=>{y.value=Id(e)})),Yn(v,(e=>{C(e)})),Yn(y,(e=>{k(e)}));const _=new kd(1,9*Math.pow(p.value,2)/40,p.value);function b(e,t){let n=!1;return e>r.value?(e=r.value,n=!0):e<s.value&&(e=s.value,n=!0),t>i.value?(t=i.value,n=!0):t<a.value&&(t=a.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){d&&d.cancel(),u&&u.cancel()}function x(e,n,r,i,s,a){w(),g.value||(e=l.value),m.value||(n=c.value),t.scale||(r=o.value);let d=b(e,n);e=d.x,n=d.y,t.animation?(_._springX._solution=null,_._springY._solution=null,_._springScale._solution=null,_._springX._endPosition=l.value,_._springY._endPosition=c.value,_._springScale._endPosition=o.value,_.setEnd(e,n,r,1),u=Pd(_,(function(){let e=_.x();S(e.x,e.y,e.scale,i,s,a)}),(function(){u.cancel()}))):S(e,n,r,i,s,a)}function S(r,i,s,a="",u,d){null!==r&&"NaN"!==r.toString()&&"number"==typeof r||(r=l.value||0),null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=c.value||0),r=Number(r.toFixed(1)),i=Number(i.toFixed(1)),s=Number(s.toFixed(1)),l.value===r&&c.value===i||u||h("change",{},{x:Ed(r,n.x),y:Ed(i,n.y),source:a}),t.scale||(s=o.value),s=+(s=f(s)).toFixed(3),d&&s!==o.value&&h("scale",{},{x:r,y:i,scale:s});let p="translateX("+r+"px) translateY("+i+"px) translateZ(0px) scale("+s+")";e.value&&(e.value.style.transform=p,e.value.style.webkitTransform=p,l.value=r,c.value=i,o.value=s)}function T(e){let t=b(l.value,c.value),n=t.x,r=t.y,i=t.outOfBounds;return i&&x(n,r,o.value,e),i}function C(e){if(g.value){if(e+n.x===l.value)return l;u&&u.cancel(),x(e+n.x,y.value+n.y,o.value)}return e}function k(e){if(m.value){if(e+n.y===c.value)return c;u&&u.cancel(),x(v.value+n.x,e+n.y,o.value)}return e}return{FAandSFACancel:w,_getLimitXY:b,_animationTo:x,_setTransform:S,_revise:T,dampingNumber:p,xMove:g,yMove:m,xSync:v,ySync:y,_STD:_}}(t,e,m,o,_,b,v,y,s,a,l,c,B,n);function A(t,n){if(e.scale){t=B(t),g(t),h();const e=x(s.value,a.value),o=e.x,r=e.y;n?S(o,r,t,"",!0,!0):Md((function(){T(o,r,t,"",!0,!0)}))}}function P(){i.value=!0}function I(e){r.value=e}function B(e){return e=Math.max(.1,u.value,e),e=Math.min(10,d.value,e)}function N(){if(!e.scale)return!1;A(o.value,!0),I(o.value)}function R(t){return!!e.scale&&(A(t=B(t),!0),I(t),t)}function F(){i.value=!1,I(o.value)}function j(e){e&&(e=r.value*e,P(),A(e))}return{_updateOldScale:I,_endScale:F,_setScale:j,scaleValueSync:f,_updateBoundary:h,_updateOffset:p,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:_,maxY:b,FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:T,_revise:C,dampingNumber:k,xMove:E,yMove:L,xSync:O,ySync:M,_STD:$}}(e,n,t,l,c,u,d,f,h,p);function q(){u.value||e.disabled||(B(),b.historyX=[0,0],b.historyY=[0,0],b.historyT=[0,0],H.value&&(s=d.value),D.value&&(a=f.value),n.value.style.willChange="transform",m=null,v=null,g=!0)}function U(t){if(!u.value&&!e.disabled&&g){let n=d.value,o=f.value;if(null===v&&(v=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),H.value&&(n=t.detail.dx+s,b.historyX.shift(),b.historyX.push(n),D.value||null!==m||(m=Math.abs(t.detail.dx/t.detail.dy)<1)),D.value&&(o=t.detail.dy+a,b.historyY.shift(),b.historyY.push(o),H.value||null!==m||(m=Math.abs(t.detail.dy/t.detail.dx)<1)),b.historyT.shift(),b.historyT.push(t.detail.timeStamp),!m){t.preventDefault();let r="touch";n<$.value?e.outOfBounds?(r="touch-out-of-bounds",n=$.value-y.x($.value-n)):n=$.value:n>P.value&&(e.outOfBounds?(r="touch-out-of-bounds",n=P.value+y.x(n-P.value)):n=P.value),o<A.value?e.outOfBounds?(r="touch-out-of-bounds",o=A.value-_.x(A.value-o)):o=A.value:o>I.value&&(e.outOfBounds?(r="touch-out-of-bounds",o=I.value+_.x(o-I.value)):o=I.value),Md((function(){R(n,o,l.value,r)}))}}}function X(){if(!u.value&&!e.disabled&&g&&(n.value.style.willChange="auto",g=!1,!m&&!F("out-of-bounds")&&e.inertia)){const e=1e3*(b.historyX[1]-b.historyX[0])/(b.historyT[1]-b.historyT[0]),t=1e3*(b.historyY[1]-b.historyY[0])/(b.historyT[1]-b.historyT[0]),n=d.value,o=f.value;x.setV(e,t),x.setS(n,o);const r=x.delta().x,i=x.delta().y;let s=r+n,a=i+o;s<$.value?(s=$.value,a=o+($.value-n)*i/r):s>P.value&&(s=P.value,a=o+(P.value-n)*i/r),a<A.value?(a=A.value,s=n+(A.value-o)*r/i):a>I.value&&(a=I.value,s=n+(I.value-o)*r/i),x.setEnd(s,a),p=Pd(x,(function(){let e=x.s(),t=e.x,n=e.y;R(t,n,l.value,"friction")}),(function(){p.cancel()}))}e.outOfBounds||e.inertia||B()}function Y(){if(!o.value)return;B();let t=e.scale?k.value:1;L(),O(t),E();let n=N(W.value+M.x,V.value+M.y),r=n.x,i=n.y;R(r,i,t,"",!0),S(t)}return Co((()=>{bd(n.value,(e=>{switch(e.detail.state){case"start":q();break;case"move":U(e);break;case"end":X()}})),Y(),x.reconfigure(1,w.value),z.reconfigure(1,9*Math.pow(j.value,2)/40,j.value),n.value.style.transformOrigin="center";const e={rootRef:n,setParent:Y,_endScale:T,_setScale:C};r(e),Oo((()=>{i(e)}))})),Oo((()=>{B()})),{setParent:Y}}(e,r,o);return()=>Vr("uni-movable-view",{ref:o},[Vr(ed,{onResize:i},null,8,["onResize"]),t.default&&t.default()],512)}});let Od=!1;function Md(e){Od||(Od=!0,requestAnimationFrame((function(){e(),Od=!1})))}function $d(e,t){if(e===t)return 0;let n=e.offsetLeft;return e.offsetParent?n+=$d(e.offsetParent,t):0}function Ad(e,t){if(e===t)return 0;let n=e.offsetTop;return e.offsetParent?n+=Ad(e.offsetParent,t):0}function Pd(e,t,n){let o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);let i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}function Id(e){return/\d+[ur]px$/i.test(e)?lc(parseFloat(e)):Number(e)||0}const Bd=sl({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,{slots:t}){const n=en(null),o=fi((()=>{const t={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":t.height=e.refresherHeight+"px";break;case"refreshing":t.height=e.refresherThreshold+"px",t.transition="height 0.3s";break;case"":case"refresherabort":case"restore":t.height="0px",t.transition="height 0.3s"}return t})),r=fi((()=>{const t=e.refresherHeight/e.refresherThreshold;return 360*(t>1?1:t)}));return()=>{const{refreshState:i,refresherDefaultStyle:s,refresherThreshold:a}=e;return Vr("div",{ref:n,style:o.value,class:"uni-scroll-view-refresher"},["none"!==s?Vr("div",{class:"uni-scroll-view-refresh"},[Vr("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==i?Vr("svg",{key:"refresh__icon",style:{transform:"rotate("+r.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[Vr("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),Vr("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==i?Vr("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[Vr("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"===s?Vr("div",{class:"uni-scroll-view-refresher-container",style:{height:`${a}px`}},[t.default&&t.default()]):null],4)}}}),Nd=me(!0),Rd=sl({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n,expose:o}){const r=en(null),i=en(null),s=en(null),a=en(null),l=cl(r,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=function(e){const t=fi((()=>Number(e.scrollTop)||0)),n=fi((()=>Number(e.scrollLeft)||0));return{state:jt({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:f,realScrollY:h,_scrollLeftChanged:p,_scrollTopChanged:g}=function(e,t,n,o,r,i,s,a,l){let c=!1,u=0,d=!1,f=()=>{};const h=fi((()=>e.scrollX)),p=fi((()=>e.scrollY)),g=fi((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),m=fi((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function v(e,t){const n=s.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=a.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",f),i.removeEventListener("webkitTransitionEnd",f),f=()=>x(e,t),i.addEventListener("transitionend",f),i.addEventListener("webkitTransitionEnd",f),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function y(e){const n=e.target;r("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),p.value&&(n.scrollTop<=g.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+m.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),h.value&&(n.scrollLeft<=g.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+m.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function _(t){p.value&&(e.scrollWithAnimation?v(t,"y"):s.value.scrollTop=t)}function b(t){h.value&&(e.scrollWithAnimation?v(t,"x"):s.value.scrollLeft=t)}function w(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=s.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(h.value){let n=o.left-t.left,r=s.value.scrollLeft+n;e.scrollWithAnimation?v(r,"x"):s.value.scrollLeft=r}if(p.value){let n=o.top-t.top,r=s.value.scrollTop+n;e.scrollWithAnimation?v(r,"y"):s.value.scrollTop=r}}}}function x(e,t){a.value.style.transition="",a.value.style.webkitTransition="",a.value.style.transform="",a.value.style.webkitTransform="";let n=s.value;"x"===t?(n.style.overflowX=h.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=p.value?"auto":"hidden",n.scrollTop=e),a.value.removeEventListener("transitionend",f),a.value.removeEventListener("webkitTransitionEnd",f)}function S(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherpulling",{},{deltaY:t.refresherHeight,dy:t.refresherHeight}),r("refresherrefresh",{},{dy:C.y-T.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(d=!1,r("refresherrestore",{},{dy:C.y-T.y})),"refresherabort"===n&&d&&(d=!1,r("refresherabort",{},{dy:C.y-T.y}))}t.refreshState=n}}let T={x:0,y:0},C={x:0,y:e.refresherThreshold};return Co((()=>{bn((()=>{_(n.value),b(o.value)})),w(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),y(e)},a=null,l=function(n){if(null===T)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,l=s.value;if(Math.abs(o-T.x)>Math.abs(i-T.y))if(h.value){if(0===l.scrollLeft&&o>T.x)return void(a=!1);if(l.scrollWidth===l.offsetWidth+l.scrollLeft&&o<T.x)return void(a=!1);a=!0}else a=!1;else if(p.value)if(0===l.scrollTop&&i>T.y)a=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(l.scrollHeight===l.offsetHeight+l.scrollTop&&i<T.y)return void(a=!1);a=!0}else a=!1;if(a&&n.stopPropagation(),0===l.scrollTop&&1===n.touches.length&&S("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-T.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,d=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(d=!0,r("refresherpulling",n,{deltaY:o,dy:o})))}},f=function(e){1===e.touches.length&&(T={x:e.touches[0].pageX,y:e.touches[0].pageY})},g=function(n){C={x:n.changedTouches[0].pageX,y:n.changedTouches[0].pageY},t.refresherHeight>=e.refresherThreshold?S("refreshing"):S("refresherabort"),T={x:0,y:0},C={x:0,y:e.refresherThreshold}};s.value.addEventListener("touchstart",f,Nd),s.value.addEventListener("touchmove",l,me(!1)),s.value.addEventListener("scroll",i,me(!1)),s.value.addEventListener("touchend",g,Nd),Lo((()=>{s.value.removeEventListener("touchstart",f),s.value.removeEventListener("touchmove",l),s.value.removeEventListener("scroll",i),s.value.removeEventListener("touchend",g)}))})),yo((()=>{p.value&&(s.value.scrollTop=t.lastScrollTop),h.value&&(s.value.scrollLeft=t.lastScrollLeft)})),Yn(n,(e=>{_(e)})),Yn(o,(e=>{b(e)})),Yn((()=>e.scrollIntoView),(e=>{w(e)})),Yn((()=>e.refresherTriggered),(e=>{!0===e?S("refreshing"):!1===e&&S("restore")})),{realScrollX:h,realScrollY:p,_scrollTopChanged:_,_scrollLeftChanged:b}}(e,c,u,d,l,r,i,a,t),m=fi((()=>{let e="";return f.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",h.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e})),v=fi((()=>{let t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t}));return o({$getMain:()=>i.value}),()=>{const{refresherEnabled:t,refresherBackground:o,refresherDefaultStyle:l,refresherThreshold:u}=e,{refresherHeight:d,refreshState:f}=c;return Vr("uni-scroll-view",{ref:r},[Vr("div",{ref:s,class:"uni-scroll-view"},[Vr("div",{ref:i,style:m.value,class:v.value},[t?Vr(Bd,{refreshState:f,refresherHeight:d,refresherThreshold:u,refresherDefaultStyle:l,refresherBackground:o},{default:()=>["none"==l?n.refresher&&n.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,Vr("div",{ref:a,class:"uni-scroll-view-content"},[n.default&&n.default()],512)],6)],512)],512)}}});function Fd(e,t,n,o,r,i){function s(){c&&(clearTimeout(c),c=null)}let a,l,c=null,u=!0,d=0,f=1,h=null,p=!1,g=0,m="";const v=fi((()=>n.value.length>t.displayMultipleItems)),y=fi((()=>e.circular&&v.value));function _(r){Math.floor(2*d)===Math.floor(2*r)&&Math.ceil(2*d)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,s=o+t.displayMultipleItems,a=0;a<i;a++){const t=r[a],n=Math.floor(o/i)*i+a,l=n+i,c=n-i,u=Math.max(o-(n+1),n-s,0),d=Math.max(o-(l+1),l-s,0),f=Math.max(o-(c+1),c-s,0),h=Math.min(u,d,f),p=[n,l,c][[u,d,f].indexOf(h)];t.updatePosition(p,e.vertical)}}(r);const s="translate("+(e.vertical?"0":100*-r*f+"%")+", "+(e.vertical?100*-r*f+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=s,l.style.transform=s),d=r,!a){if(r%1==0)return;a=r}r-=Math.floor(a);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=a%1>.5||a<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function b(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function w(){h=null}function x(){if(!h)return void(p=!1);const e=h,o=e.toPos,r=e.acc,s=e.endTime,c=e.source,u=s-Date.now();if(u<=0){_(o),h=null,p=!1,a=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}_(o+r*u*u/2),l=requestAnimationFrame(x)}function S(e,o,r){w();const i=t.duration,s=n.value.length;let a=d;if(y.value)if(r<0){for(;a<e;)a+=s;for(;a-s>e;)a-=s}else if(r>0){for(;a>e;)a-=s;for(;a+s<e;)a+=s;a+s-e<e-a&&(a+=s)}else{for(;a+s<e;)a+=s;for(;a-s>e;)a-=s;a+s-e<e-a&&(a+=s)}else"click"===o&&(e=e+t.displayMultipleItems-1<s?e:0);h={toPos:e,acc:2*(a-e)/(i*i),endTime:Date.now()+i,source:o},p||(p=!0,l=requestAnimationFrame(x))}function T(){s();const e=n.value,o=function(){c=null,m="autoplay",y.value?t.current=b(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,S(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function C(e){e?T():s()}return Yn([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(m="",t.current=o)})),Yn([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){s(),h&&(_(h.toPos),h=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);f=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();f=e.width/t.width,f>0&&f<1||(f=1)}const a=d;d=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(_(a+l-g),g=l):(_(l),e.autoplay&&T())):(u=!0,_(-t.displayMultipleItems-1))})),Yn((()=>t.interval),(()=>{c&&(s(),T())})),Yn((()=>t.current),((e,o)=>{!function(e,o){const r=m;m="";const s=n.value;if(!r){const t=s.length;S(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const a=s[e];if(a){const e=t.currentItemId=a.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)})),Yn((()=>t.currentItemId),(e=>{r("update:currentItemId",e)})),Yn((()=>e.autoplay&&!t.userTracking),C),C(e.autoplay&&!t.userTracking),Co((()=>{let r=!1,i=0,a=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=b(d+o);e?_(g):(m="touch",t.current=r,S(r,"touch",0!==o?o:0===r&&y.value&&d>=1?1:0))}bd(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,s(),g=d,i=0,a=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&T())}return function(r){const s=a;a=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),_(n)}const d=a-s||1,f=o.value;e.vertical?u(-r.dy/f.offsetHeight,-r.ddy/d):u(-r.dx/f.offsetWidth,-r.ddx/d)}(c.detail),!1}}}))})),Oo((()=>{s(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){S(t.current=e,m="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const jd=sl({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=en(null),r=cl(o,n),i=en(null),s=en(null),a=function(e){return jt({interval:fi((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:fi((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:fi((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=fi((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:pa(e.previousMargin,!0),bottom:pa(e.nextMargin,!0)}:{top:0,bottom:0,left:pa(e.previousMargin,!0),right:pa(e.nextMargin,!0)}),t})),c=fi((()=>{const t=Math.abs(100/a.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const d=[],f=en([]);function h(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=d.find((e=>n===e.rootRef.value));o&&e.push(Xt(o))}f.value=e}or("addSwiperContext",(function(e){d.push(e),h()}));or("removeSwiperContext",(function(e){const t=d.indexOf(e);t>=0&&(d.splice(t,1),h())}));const{onSwiperDotClick:p,circularEnabled:g,swiperEnabled:m}=Fd(e,a,f,s,n,r);let v=()=>null;return v=Hd(o,e,a,p,f,g,m),()=>{const n=t.default&&t.default();return u=pd(n),Vr("uni-swiper",{ref:o},[Vr("div",{ref:i,class:"uni-swiper-wrapper"},[Vr("div",{class:"uni-swiper-slides",style:l.value},[Vr("div",{ref:s,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&Vr("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[f.value.map(((t,n,o)=>Vr("div",{onClick:()=>p(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<a.current+a.displayMultipleItems&&n>=a.current||n<a.current+a.displayMultipleItems-o.length},style:{background:n===a.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),Hd=(e,t,n,o,r,i,s)=>{let a=!1,l=!1,u=!1,d=en(!1);function f(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}Un((()=>{a="auto"===t.navigation,d.value=!0!==t.navigation||a,_()})),Un((()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,u=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,s.value||(l=!0,u=!0,a&&(d.value=!0))}));const h={onMouseover:e=>f(e,"over"),onMouseout:e=>f(e,"out")};function p(e,t,s){if(e.stopPropagation(),s)return;const a=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=a-1);break;case"next":l++,l>=a&&i.value&&(l=0)}o(l)}const g=()=>_a("M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",t.navigationColor,26);let m;const v=n=>{clearTimeout(m);const{clientX:o,clientY:r}=n,{left:i,right:s,top:a,bottom:l,width:c,height:u}=e.value.getBoundingClientRect();let f=!1;if(f=t.vertical?!(r-a<u/3||l-r<u/3):!(o-i<c/3||s-o<c/3),f)return m=setTimeout((()=>{d.value=f}),300);d.value=f},y=()=>{d.value=!0};function _(){e.value&&(e.value.removeEventListener("mousemove",v),e.value.removeEventListener("mouseleave",y),a&&(e.value.addEventListener("mousemove",v),e.value.addEventListener("mouseleave",y)))}return Co(_),function(){const e={"uni-swiper-navigation-hide":d.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?Vr(Cr,null,[Vr("div",Jr({class:["uni-swiper-navigation uni-swiper-navigation-prev",c({"uni-swiper-navigation-disabled":l},e)],onClick:e=>p(e,"prev",l)},h),[g()],16,["onClick"]),Vr("div",Jr({class:["uni-swiper-navigation uni-swiper-navigation-next",c({"uni-swiper-navigation-disabled":u},e)],onClick:e=>p(e,"next",u)},h),[g()],16,["onClick"])]):null}},Dd=sl({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=en(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,s=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=s,i.style.transform=s)}};return Co((()=>{const e=rr("addSwiperContext");e&&e(o)})),Oo((()=>{const e=rr("removeSwiperContext");e&&e(o)})),()=>Vr("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),Wd={ensp:" ",emsp:" ",nbsp:" "};function Vd(e,t){return function(e,{space:t,decode:n}){let o="",r=!1;for(let i of e)t&&Wd[t]&&" "===i&&(i=Wd[t]),r?(o+="n"===i?"\n":"\\"===i?"\\":"\\"+i,r=!1):"\\"===i?r=!0:o+=i;return n?o.replace(/&nbsp;/g,Wd.nbsp).replace(/&ensp;/g,Wd.ensp).replace(/&emsp;/g,Wd.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}(e,t).split("\n")}const zd=sl({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=en(null);return()=>{const o=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==Er){const n=Vd(t.children,{space:e.space,decode:e.decode}),r=n.length-1;n.forEach(((e,t)=>{(0!==t||e)&&o.push(qr(e)),t!==r&&o.push(Vr("br"))}))}else o.push(t)})),Vr("uni-text",{ref:n,selectable:!!e.selectable||null},[Vr("span",null,o)],8,["selectable"])}}}),qd=sl({name:"View",props:c({},ul),setup(e,{slots:t}){const n=en(null),{hovering:o,binding:r}=dl(e);return()=>{const i=e.hoverClass;return i&&"none"!==i?Vr("uni-view",Jr({class:o.value?i:"",ref:n},r),[Io(t,"default")],16):Vr("uni-view",{ref:n},[Io(t,"default")],512)}}});function Ud(e,t,n,o){m(t)&&xo(e,t.bind(n),o)}function Xd(e,t,n){const o=e.mpType||n.$mpType;if(o&&"component"!==o&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!m(t))&&(Te.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];h(r)?r.forEach((e=>Ud(o,e,n,t))):Ud(o,r,n,t)}})),"page"===o)){t.__isVisible=!0;try{let e=t.attrs.__pageQuery;0,Ea(n,"onLoad",e),delete t.attrs.__pageQuery;const o=n.$page;"preloadPage"!==(null==o?void 0:o.openType)&&Ea(n,"onShow")}catch(r){console.error(r.message+"\n"+r.stack)}}}function Yd(e,t,n){Xd(e,t,n)}function Kd(e,t,n){return e[t]=n}function Jd(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function Gd(e){const t=e.config.errorHandler;return function(n,o,r){t&&t(n,o,r);const i=e._instance;if(!i||!i.proxy)throw n;i.onError?Ea(i.proxy,"onError",n):un(n,0,o&&o.$.vnode,!1)}}function Qd(e,t){return e?[...new Set([].concat(e,t))]:t}function Zd(e){const t=e.config;var n;t.errorHandler=ke(e,Gd),n=t.optionMergeStrategies,Te.forEach((e=>{n[e]=Qd}));const o=t.globalProperties;o.$set=Kd,o.$applyOptions=Yd,o.$callMethod=Jd,function(e){Ce.forEach((t=>t(e)))}(e)}const ef={install(e){Zd(e),za(e),tl(e),e.config.warnHandler||(e.config.warnHandler=tf)}};function tf(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const nf={class:"uni-async-loading"},of=Vr("i",{class:"uni-loading"},null,-1),rf=al({name:"AsyncLoading",render:()=>($r(),Nr("div",nf,[of]))});function sf(){window.location.reload()}const af=al({name:"AsyncError",setup(){Os();const{t:e}=Es();return()=>Vr("div",{class:"uni-async-error",onClick:sf},[e("uni.async.error")],8,["onClick"])}});let lf;function cf(){return lf}function uf(e){lf=e,Object.defineProperty(lf.$.ctx,"$children",{get:()=>yu().map((e=>e.$vm))});const t=lf.$.appContext.app;t.component(rf.name)||t.component(rf.name,rf),t.component(af.name)||t.component(af.name,af),function(e){e.$vm=e,e.$mpType="app";const t=en(Es().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(lf),function(e,t){const n=e.$options||{};n.globalData=c(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(lf),Za(),Ia()}function df(e,{clone:t,init:n,setup:o,before:r}){t&&(e=c({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=ti();if(n(r.proxy),o(r),i)return i(e,t)},e}function ff(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?df(e.default,t):df(e,t)}function hf(e){return ff(e,{clone:!0,init:xu,setup(e){e.$pageInstance=e;const t=wl(),n=_e(t.query);e.attrs.__pageQuery=n,uu(e.proxy).options=n,e.proxy.options=n;const o=_l();var r,i;return e.onReachBottom=jt([]),e.onPageScroll=jt([]),Yn([e.onReachBottom,e.onPageScroll],(()=>{const t=wa();e.proxy===t&&$u(e,o)}),{once:!0}),To((()=>{ku(e,o)})),Co((()=>{Eu(e);const{onReady:n}=e;n&&I(n),vf(t)})),bo((()=>{if(!e.__isVisible){ku(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&I(n),bn((()=>{vf(t)}))}}),"ba",r),function(e,t){bo(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;{const{onHide:t}=e;t&&I(t)}}})),i=o.id,jh.subscribe(Rs(i,"invokeViewApi"),Fs),Lo((()=>{!function(e){jh.unsubscribe(Rs(e,"invokeViewApi")),Object.keys(Ns).forEach((t=>{0===t.indexOf(e+".")&&delete Ns[t]}))}(o.id)})),n}})}function pf(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=Of(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Hh.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function gf(e){S(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&Hh.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function mf(){const{emit:e}=Hh;"visible"===document.visibilityState?e("onAppEnterForeground",c({},Zu)):e("onAppEnterBackground")}function vf(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&Ea("onTabItemTap",{index:n,text:t,pagePath:o})}const yf=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let _f;function bf(){if(_f=_f||yf.__DC_STAT_UUID,!_f){_f=Date.now()+""+Math.floor(1e7*Math.random());try{yf.__DC_STAT_UUID=_f}catch(e){}}return _f}function wf(){if(!0!==__uniConfig.darkmode)return v(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function xf(){let e,t="0",n="",o="phone";const r=navigator.language;if(Nu){e="iOS";const o=Iu.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=Iu.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(Bu){e="Android";const o=Iu.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=Iu.match(/\((.+?)\)/),i=r?r[1].split(";"):Iu.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(Hu){if(n="iPad",e="iOS",o="pad",t=m(window.BigInt)?"14.0":"13.0",14===parseInt(t)){const e=Iu.match(/Version\/(\S*)\b/);e&&(t=e[1])}}else if(Ru||Fu||ju){n="PC",e="PC",o="pc",t="0";let r=Iu.match(/\((.+?)\)/)[1];if(Ru){switch(e="Windows",Ru[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(Fu){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(ju){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLocaleLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(Iu)&&(a=t[n],l=Iu.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:Iu,osname:e,osversion:t,theme:wf()}}const Sf=Ql(0,(()=>{const e=window.devicePixelRatio,t=Du(),n=Wu(t),o=Vu(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=zu(o);let s=window.innerHeight;const a=ia.top,l={left:ia.left,right:i-ia.right,top:ia.top,bottom:s-ia.bottom,width:i-ia.left-ia.right,height:s-ia.top-ia.bottom},{top:c,bottom:u}=ua();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:ia.top,right:ia.right,bottom:ia.bottom,left:ia.left},screenTop:r-s}}));let Tf,Cf=!0;function kf(){Cf&&(Tf=xf())}const Ef=Ql(0,(()=>{kf();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a,osname:l,osversion:u}=Tf;return c({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:bf(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i,osName:l?l.toLocaleLowerCase():void 0,osVersion:u})})),Lf=Ql(0,(()=>{kf();const{theme:e,language:t,browserName:n,browserVersion:o}=Tf;return c({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Tc?Tc():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:"",uniPlatform:"web",isUniAppX:!1,uniCompileVersion:__uniConfig.compilerVersion,uniCompilerVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion},{})})),Of=Ql(0,(()=>{Cf=!0,kf(),Cf=!1;const e=Sf(),t=Ef(),n=Lf();Cf=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=Tf,l=c(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLocaleLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return S(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)}));const Mf=Ql(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)}));function $f(e){const t=localStorage&&localStorage.getItem(e);if(!v(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=v(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const Af=Ql(0,(e=>{try{return $f(e)}catch(t){return""}})),Pf=Ql(0,(e=>{localStorage&&localStorage.removeItem(e)})),If=Ql(0,(()=>{localStorage&&localStorage.clear()}));const Bf=Zl("getImageInfo",(({src:e},{resolve:t,reject:n})=>{const o=new Image;o.onload=function(){t({width:o.naturalWidth,height:o.naturalHeight,path:0===e.indexOf("/")?window.location.protocol+"//"+window.location.host+e:e})},o.onerror=function(){n()},o.src=e}),0,Ec),Nf={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function Rf({count:e,sourceType:t,type:n,extension:o}){!function(e={userAction:!1}){cd||(["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!ld&&ud(!0),ld++,setTimeout((()=>{!--ld&&ud(!1)}),0)}),sd)})),cd=!0);ad.push(e)}();const r=document.createElement("input");return r.type="file",function(e,t){for(const n in t)e.style[n]=t[n]}(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${Nf[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}let Ff=null;const jf=Zl("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{Ps();const{t:i}=Es();Ff&&(document.body.removeChild(Ff),Ff=null),Ff=Rf({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(Ff),Ff.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||Gu(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),Ff.click(),ld||console.warn(i("uni.chooseFile.notUserActivation"))}),0,kc),Hf={esc:["Esc","Escape"],enter:["Enter"]},Df=Object.keys(Hf);const Wf=Vr("div",{class:"uni-mask"},null,-1);function Vf(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),ls(ho({setup:()=>()=>($r(),Nr(e,t,null,16))}))}function zf(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function qf(e,{onEsc:t,onEnter:n}){const o=en(e.visible),{key:r,disable:i}=function(){const e=en(""),t=en(!1),n=n=>{if(t.value)return;const o=Df.find((e=>-1!==Hf[e].indexOf(n.key)));o&&(e.value=o),bn((()=>e.value=""))};return Co((()=>{document.addEventListener("keyup",n)})),Lo((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}();return Yn((()=>e.visible),(e=>o.value=e)),Yn((()=>o.value),(e=>i.value=!e)),Un((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}let Uf=0,Xf="";function Yf(e){let t=Uf;Uf+=e?1:-1,Uf=Math.max(0,Uf),Uf>0?0===t&&(Xf=document.body.style.overflow,document.body.style.overflow="hidden"):(document.body.style.overflow=Xf,Xf="")}const Kf=al({name:"ImageView",props:{src:{type:String,default:""}},setup(e){const t=jt({direction:"none"});let n=1,o=0,r=0,i=0,s=0;function a({detail:e}){n=e.scale}function l(e){const t=e.target.getBoundingClientRect();o=t.width,r=t.height}function c(e){const t=e.target.getBoundingClientRect();i=t.width,s=t.height,d(e)}function u(e){const a=n*o>i,l=n*r>s;t.direction=a&&l?"all":a?"horizontal":l?"vertical":"none",d(e)}function d(e){"all"!==t.direction&&"horizontal"!==t.direction||e.stopPropagation()}return()=>{const n={position:"absolute",left:"0",top:"0",width:"100%",height:"100%"};return Vr(gd,{style:n,onTouchstart:ll(c),onTouchmove:ll(d),onTouchend:ll(u)},{default:()=>[Vr(Ld,{style:n,direction:t.direction,inertia:!0,scale:!0,"scale-min":"1","scale-max":"4",onScale:a},{default:()=>[Vr("img",{src:e.src,style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",maxHeight:"100%",maxWidth:"100%"},onLoad:l},null,40,["src","onLoad"])]},8,["style","direction","inertia","scale","onScale"])]},8,["style","onTouchstart","onTouchmove","onTouchend"])}}});function Jf(e){let t="number"==typeof e.current?e.current:e.urls.indexOf(e.current);return t=t<0?0:t,t}const Gf=al({name:"ImagePreview",props:{urls:{type:Array,default:()=>[]},current:{type:[Number,String],default:0}},emits:["close"],setup(e,{emit:t}){Co((()=>Yf(!0))),Oo((()=>Yf(!1)));const n=en(null),o=en(Jf(e));let r;function i(){r||bn((()=>{t("close")}))}function s(e){o.value=e.detail.current}Yn((()=>e.current),(()=>o.value=Jf(e))),Co((()=>{const e=n.value;let t=0,o=0;e.addEventListener("mousedown",(e=>{r=!1,t=e.clientX,o=e.clientY})),e.addEventListener("mouseup",(e=>{(Math.abs(e.clientX-t)>20||Math.abs(e.clientY-o)>20)&&(r=!0)}))}));const a={position:"absolute","box-sizing":"border-box",top:"0",right:"0",width:"60px",height:"44px",padding:"6px","line-height":"32px","font-size":"26px",color:"white","text-align":"center",cursor:"pointer"};return()=>{let t;return Vr("div",{ref:n,style:{display:"block",position:"fixed",left:"0",top:"0",width:"100%",height:"100%",zIndex:999,background:"rgba(0,0,0,0.8)"},onClick:i},[Vr(jd,{navigation:"auto",current:o.value,onChange:s,"indicator-dots":!1,autoplay:!1,style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%"}},(r=t=e.urls.map((e=>Vr(Dd,null,{default:()=>[Vr(Kf,{src:e},null,8,["src"])]}))),"function"==typeof r||"[object Object]"===Object.prototype.toString.call(r)&&!Rr(r)?t:{default:()=>[t],_:1}),8,["current","onChange"]),Vr("div",{style:a},[_a("M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z","#ffffff",26)],4)],8,["onClick"]);var r}}});let Qf,Zf=null;const eh=()=>{Zf=null,bn((()=>{null==Qf||Qf.unmount(),Qf=null}))},th=Zl("previewImage",((e,{resolve:t})=>{Zf?c(Zf,e):(Zf=jt(e),bn((()=>{Qf=Vf(Gf,Zf,eh),Qf.mount(zf("u-a-p"))}))),t()}),0,Lc),nh=Gl("request",(({url:e,data:t,header:n={},method:o,dataType:r,responseType:i,withCredentials:s,timeout:a=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const d=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(v(t)||t instanceof ArrayBuffer)u=t;else if("json"===d)try{u=JSON.stringify(t)}catch(m){u=t.toString()}else if("urlencoded"===d){const e=[];for(const n in t)f(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const h=new XMLHttpRequest,p=new oh(h);h.open(o,e);for(const v in n)f(n,v)&&h.setRequestHeader(v,n[v]);const g=setTimeout((function(){h.onload=h.onabort=h.onerror=null,p.abort(),c("timeout",{errCode:5})}),a);return h.responseType=i,h.onload=function(){clearTimeout(g);const e=h.status;let t="text"===i?h.responseText:h.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(m){}l({data:t,statusCode:e,header:rh(h.getAllResponseHeaders()),cookies:[]})},h.onabort=function(){clearTimeout(g),c("abort",{errCode:600003})},h.onerror=function(){clearTimeout(g),c(void 0,{errCode:5})},h.withCredentials=s,h.send(u),p}),0,Ac);class oh{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function rh(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class ih{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){m(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const sh=Gl("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i={},formData:s={},timeout:a=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new ih;return h(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(Ju(e)):Ku(t)))).then((function(t){var n,o=new XMLHttpRequest,d=new FormData;Object.keys(s).forEach((e=>{d.append(e,s[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];d.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c("",{errCode:602001})},o.onabort=function(){clearTimeout(n),c("abort",{errCode:600003})},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort",{errCode:600003}):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout",{errCode:5})}),a),o.send(d),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,Pc),ah=Zl("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===Ea("onBackPress",{from:e.from||"navigateBack"})&&(o=!1),o?(cf().$router.go(-e.delta),t()):n("onBackPress")}),0,Fc),lh=Zl("navigateTo",(({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:r})=>{if(du.handledBeforeEntryPageRoutes)return ou({type:"navigateTo",url:e,events:t,isAutomatedTesting:n}).then(o).catch(r);fu.push({args:{type:"navigateTo",url:e,events:t,isAutomatedTesting:n},resolve:o,reject:r})}),0,Bc);function ch(e){__uniConfig.darkmode&&Hh.on("onThemeChange",e)}function uh(e){Hh.off("onThemeChange",e)}function dh(e){let t={};return __uniConfig.darkmode&&(t=$e(e,__uniConfig.themeConfig,wf())),__uniConfig.darkmode?t:e}const fh={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},hh=ho({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=en(""),o=()=>s.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),s=qf(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),a=function(e){const t=en(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=fh[e].cancelColor})(e,t)};return Un((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===wf()&&n({theme:"dark"}),ch(n))):uh(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:d,placeholderText:f}=e;return n.value=o,Vr(_i,{name:"uni-fade"},{default:()=>[Zn(Vr("uni-modal",{onTouchmove:sa},[Wf,Vr("div",{class:"uni-modal"},[t?Vr("div",{class:"uni-modal__hd"},[Vr("strong",{class:"uni-modal__title",textContent:t||""},null,8,["textContent"])]):null,d?Vr("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:f,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):Vr("div",{class:"uni-modal__bd",onTouchmovePassive:aa,textContent:o},null,40,["onTouchmovePassive","textContent"]),Vr("div",{class:"uni-modal__ft"},[l&&Vr("div",{style:{color:a.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),Vr("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[Pi,s.value]])]})}}});let ph;const gh=re((()=>{Hh.on("onHidePopup",(()=>ph.visible=!1))}));let mh;function vh(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&ph.editable&&(o.content=t),mh&&mh(o)}const yh=Zl("showModal",((e,{resolve:t})=>{gh(),mh=t,ph?(c(ph,e),ph.visible=!0):(ph=jt(e),bn((()=>(Vf(hh,ph,vh).mount(zf("u-a-m")),bn((()=>ph.visible=!0))))))}),0,Uc),_h={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==Xc.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},bh={light:"#fff",dark:"rgba(255,255,255,0.9)"},wh=e=>bh[e],xh=ho({name:"Toast",props:_h,setup(e){Ms(),$s();const{Icon:t}=function(e){const t=en(wh(wf())),n=({theme:e})=>t.value=wh(e);Un((()=>{e.visible?ch(n):uh(n)}));return{Icon:fi((()=>{switch(e.icon){case"success":return Vr(_a(va,t.value,38),{class:"uni-toast__icon"});case"error":return Vr(_a(ya,t.value,38),{class:"uni-toast__icon"});case"loading":return Vr("i",{class:["uni-toast__icon","uni-loading"]},null,2);default:return null}}))}}(e),n=qf(e,{});return()=>{const{mask:o,duration:r,title:i,image:s}=e;return Vr(_i,{name:"uni-fade"},{default:()=>[Zn(Vr("uni-toast",{"data-duration":r},[o?Vr("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:sa},null,40,["onTouchmove"]):"",s||t.value?Vr("div",{class:"uni-toast"},[s?Vr("img",{src:s,class:"uni-toast__icon"},null,10,["src"]):t.value,Vr("p",{class:"uni-toast__content"},[i])]):Vr("div",{class:"uni-sample-toast"},[Vr("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[Pi,n.value]])]})}}});let Sh,Th,Ch="";const kh=Be();function Eh(e){Sh?c(Sh,e):(Sh=jt(c(e,{visible:!1})),bn((()=>{kh.run((()=>{Yn([()=>Sh.visible,()=>Sh.duration],(([e,t])=>{if(e){if(Th&&clearTimeout(Th),"onShowLoading"===Ch)return;Th=setTimeout((()=>{$h("onHideToast")}),t)}else Th&&clearTimeout(Th)}))})),Hh.on("onHidePopup",(()=>$h("onHidePopup"))),Vf(xh,Sh,(()=>{})).mount(zf("u-a-t"))}))),setTimeout((()=>{Sh.visible=!0}),10)}const Lh=Zl("showToast",((e,{resolve:t,reject:n})=>{Eh(e),Ch="onShowToast",t()}),0,Yc),Oh={icon:"loading",duration:1e8,image:""},Mh=Zl("showLoading",((e,{resolve:t,reject:n})=>{c(e,Oh),Eh(e),Ch="onShowLoading",t()}),0,qc);function $h(e){const{t:t}=Es();if(!Ch)return;let n="";if("onHideToast"===e&&"onShowToast"!==Ch?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==Ch&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);Ch="",setTimeout((()=>{Sh.visible=!1}),10)}function Ah(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Hh.emit("onNavigationBarChange",{titleText:t})}Un(t),yo(t)}function Ph(e,t,n,o,r){if(!e)return r("page not found");const{navigationBar:i}=e;switch(t){case"setNavigationBarColor":const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:r,timingFunc:s}=o;e&&(i.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(i.backgroundColor=t),i.duration=r+"ms",i.timingFunc=s;break;case"showNavigationBarLoading":i.loading=!0;break;case"hideNavigationBarLoading":i.loading=!1;break;case"setNavigationBarTitle":const{title:a}=n;i.titleText=a}o()}const Ih=Zl("setNavigationBarColor",((e,{resolve:t,reject:n})=>{Ph(xa(),"setNavigationBarColor",e,t,n)}),0,zc),Bh=Zl("showNavigationBarLoading",((e,{resolve:t,reject:n})=>{Ph(xa(),"showNavigationBarLoading",e||{},t,n)})),Nh=Zl("hideNavigationBarLoading",((e,{resolve:t,reject:n})=>{Ph(xa(),"hideNavigationBarLoading",e||{},t,n)})),Rh=Zl("setNavigationBarTitle",((e,{resolve:t,reject:n})=>{Ph(xa(),"setNavigationBarTitle",e,t,n)})),Fh=al({name:"Layout",setup(e,{emit:t}){const n=en(null);da({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const{layoutState:o,windowState:r}=function(){wl();{const e=jt({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return Yn((()=>e.marginWidth),(e=>da({"--window-margin":e+"px"}))),Yn((()=>e.leftWindowWidth+e.marginWidth),(e=>{da({"--window-left":e+"px"})})),Yn((()=>e.rightWindowWidth+e.marginWidth),(e=>{da({"--window-right":e+"px"})})),{layoutState:e,windowState:fi((()=>({})))}}}();!function(e,t){const n=wl();function o(){const o=document.body.clientWidth,r=yu();let i={};if(r.length>0){i=uu(r[r.length-1]).meta}else{const e=Pa(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((f(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,bn((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")}))):(e.marginWidth=0,bn((()=>{const e=t.value;e&&e.removeAttribute("style")})))}Yn([()=>n.path],o),Co((()=>{o(),window.addEventListener("resize",o)}))}(o,n);const i=function(e){const t=en(!1);return fi((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(!1);return()=>{const e=Vr(__uniRoutes[0].component);return Vr("uni-app",{ref:n,class:i.value},[e,!1],2)}}});const jh=c(js,{publishHandler(e,t,n){Hh.subscribeHandler(e,t,n)}}),Hh=c(Xa,{publishHandler(e,t,n){jh.subscribeHandler(e,t,n)}}),Dh=al({name:"PageHead",setup(){const e=en(null),t=_l(),n=function(e,t){const n=Wt(e),o=n?jt(dh(e)):dh(e);return __uniConfig.darkmode&&n&&Yn(e,(e=>{const t=dh(e);for(const n in t)o[n]=t[n]})),t&&ch(t),o}(t.navigationBar,(()=>{const e=dh(t.navigationBar);n.backgroundColor=e.backgroundColor,n.titleColor=e.titleColor})),{clazz:o,style:r}=function(e){const t=fi((()=>{const{type:t,titlePenetrate:n,shadowColorType:o}=e,r={"uni-page-head":!0,"uni-page-head-transparent":"transparent"===t,"uni-page-head-titlePenetrate":"YES"===n,"uni-page-head-shadow":!!o};return o&&(r[`uni-page-head-shadow-${o}`]=!0),r})),n=fi((()=>({backgroundColor:e.backgroundColor,color:e.titleColor,transitionDuration:e.duration,transitionTimingFunction:e.timingFunc})));return{clazz:t,style:n}}(n);return()=>{const t=n.type||"default",i="transparent"!==t&&"float"!==t&&Vr("div",{class:{"uni-placeholder":!0,"uni-placeholder-titlePenetrate":n.titlePenetrate}},null,2);return Vr("uni-page-head",{"uni-page-head-type":t},[Vr("div",{ref:e,class:o.value,style:r.value},[Vr("div",{class:"uni-page-head-hd"},[null]),Wh(n),Vr("div",{class:"uni-page-head-ft"},[])],6),i],8,["uni-page-head-type"])}}});function Wh(e,t){return function({type:e,loading:t,titleSize:n,titleText:o,titleImage:r}){return Vr("div",{class:"uni-page-head-bd"},[Vr("div",{style:{fontSize:n,opacity:"transparent"===e?0:1},class:"uni-page-head__title"},[t?Vr("i",{class:"uni-loading"},null):r?Vr("img",{src:r,class:"uni-page-head__title_image"},null,8,["src"]):o],4)])}(e)}const Vh=al({name:"PageBody",setup(e,t){const n=en(null),o=en(null);return Yn((()=>false.enablePullDownRefresh),(()=>{o.value=null}),{immediate:!0}),()=>Vr(Cr,null,[!1,Vr("uni-page-wrapper",Jr({ref:n},o.value),[Vr("uni-page-body",null,[Io(t.slots,"default")]),null],16)])}}),zh=al({name:"Page",setup(e,t){let n=bl(xl());const o=n.navigationBar,r={};return Ah(n),()=>Vr("uni-page",{"data-page":n.route,style:r},"custom"!==o.style?[Vr(Dh),qh(t),null]:[qh(t),null])}});function qh(e){return $r(),Nr(Vh,{key:0},{default:Bn((()=>[Io(e.slots,"page")])),_:3})}const Uh={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=lc;const Xh=Object.assign({}),Yh=Object.assign;window.__uniConfig=Yh({globalStyle:{backgroundColor:"#F8F8F8",navigationBar:{backgroundColor:"#F8F8F8",type:"default",titleColor:"#000000"},isNVue:!1},uniIdRouter:{},easycom:{autoscan:!0,custom:{"gui-(.*)":"@/Grace6/components/gui-$1.vue"}},compilerVersion:"4.55"},{appId:"__UNI__FAB860B",appName:"smile",appVersion:"1.0.0",appVersionCode:"100",async:Uh,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,bMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(Xh).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return Yh(e[n]||(e[n]={}),Xh[t].default),e}),{}),router:{mode:"history",base:"./",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const Kh={delay:Uh.delay,timeout:Uh.timeout,suspensible:Uh.suspensible};Uh.loading&&(Kh.loadingComponent={name:"SystemAsyncLoading",render:()=>Vr(Hn(Uh.loading))}),Uh.error&&(Kh.errorComponent={name:"SystemAsyncError",render:()=>Vr(Hn(Uh.error))});const Jh=()=>t((()=>import("./pages-index-index.UeaAyDEp.js")),__vite__mapDeps([0,1]),import.meta.url).then((e=>hf(e.default||e))),Gh=go(Yh({loader:Jh},Kh));window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=cf(),t=e&&e.$route&&e.$route.query||{};return()=>{return e=Gh,n=t,$r(),Nr(zh,null,{page:Bn((()=>[Vr(e,Yh({},n,{ref:"page"}),null,512)])),_:1});var e,n}}},loader:Jh,meta:{isQuit:!0,isEntry:!0,navigationBar:{type:"default",titleColor:"拍照"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const Qh={onLaunch:function(){},onShow:function(){},onHide:function(){}};ff(Qh,{init:uf,setup(e){const t=wl();return To((()=>{var n;n=e,Object.keys(Cc).forEach((e=>{Cc[e].forEach((t=>{xo(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i}=e,s=function({path:e,query:t}){return c(Qu,{path:e,query:t}),c(Zu,Qu),c({},Qu)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:_e(t.query)});o&&I(o,s),r&&I(r,s)})),Co((()=>{window.addEventListener("resize",function(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r),r=o((()=>e.apply(this,arguments)),t)};return i.cancel=function(){n(r)},i}(pf,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",gf),document.addEventListener("visibilitychange",mf),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{Hh.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>($r(),Nr(Fh));e.setup=(e,o)=>{const r=t&&t(e,o);return m(r)?n:r},e.render=n}}),ls(Qh).use(ef).mount("#app");export{Af as A,Pf as B,If as C,jf as D,Bf as E,th as F,Mh as G,Rh as H,Ih as I,Bh as J,Nh as K,yh as L,sh as M,Br as N,Cr as O,vl as P,Rd as S,Vr as a,de as b,Nr as c,qr as d,Ur as e,zd as f,qd as g,is as h,rd as i,Of as j,Sc as k,Wn as l,nu as m,ue as n,$r as o,Qc as p,eu as q,Io as r,Lh as s,X as t,lc as u,lh as v,Bn as w,ah as x,nh as y,Mf as z};
