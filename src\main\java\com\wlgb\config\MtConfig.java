package com.wlgb.config;

import com.dianping.openapi.sdk.api.customerauth.session.CustomerKeyShopScopeQuery;
import com.dianping.openapi.sdk.api.customerauth.session.entity.CustomerKeyShopScopeRequest;
import com.dianping.openapi.sdk.api.customerauth.session.entity.CustomerKeyShopScopeResponse;
import com.dianping.openapi.sdk.api.oauth.CustomerRefreshToken;
import com.dianping.openapi.sdk.api.oauth.DynamicToken;
import com.dianping.openapi.sdk.api.oauth.entity.CustomerRefreshTokenResponse;
import com.dianping.openapi.sdk.api.oauth.entity.DynamicTokenRequest;
import com.dianping.openapi.sdk.api.oauth.entity.DynamicTokenResponse;
import com.dianping.openapi.sdk.api.oauth.entity.RefreshTokenRequest;
import com.dianping.openapi.sdk.api.oauth.enums.GrantType;
import com.dianping.openapi.sdk.api.tuangou.TuangouReceiptConsume;
import com.dianping.openapi.sdk.api.tuangou.TuangouReceiptPrepare;
import com.dianping.openapi.sdk.api.tuangou.entity.*;
import com.dianping.openapi.sdk.httpclient.DefaultOpenAPIClient;
import com.wlgb.entity.WlgbMtLog;
import com.wlgb.entity.WlgbMtMd;

import java.util.Date;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月17日 14:33
 */
public class MtConfig {

    /**
     * 刷新token  只有12次机会，每刷新一次有效2591999秒（一个月）
     *
     * @param app_key
     * @param app_secret
     * @param refresh_token
     */
    public static CustomerRefreshTokenResponse sxToken(String app_key, String app_secret, String refresh_token) {
        DefaultOpenAPIClient openAPIClient = new DefaultOpenAPIClient();
        RefreshTokenRequest request = new RefreshTokenRequest(app_key, app_secret, refresh_token);
        CustomerRefreshToken refreshToken = new CustomerRefreshToken(request);
        CustomerRefreshTokenResponse response = openAPIClient.invoke(refreshToken);
        return response;
    }

    /***
     * 通过美团北极星的UI授权后，需要换取session。本方法就是换取session的方法
     * @param app_key
     * @param app_secret
     * @param auth_code
     * @return
     */
    public static DynamicTokenResponse HqToken(String app_key, String app_secret, String auth_code, String redirect_url) {
        DefaultOpenAPIClient openAPIClient = new DefaultOpenAPIClient();
        DynamicTokenRequest request = new DynamicTokenRequest(app_key, app_secret, GrantType.AUTHORIZATION_CODE.getValue(), auth_code, redirect_url);
        DynamicToken dynamicToken = new DynamicToken(request);
        DynamicTokenResponse response = openAPIClient.invoke(dynamicToken);
        return response;
    }


    /**
     * 验券接口
     *
     * @param app_key
     * @param app_secret
     * @param session
     * @param requestid
     * @param receipt_code
     * @param count
     * @param open_shop_uuid
     * @param app_shop_account
     * @param app_shop_accountname
     */
    public static TuangouReceiptConsumeResponse getYQ(String app_key, String app_secret, String session, String requestid, String receipt_code, Integer count, String open_shop_uuid, String app_shop_account, String app_shop_accountname) {
        DefaultOpenAPIClient openAPIClient = new DefaultOpenAPIClient();
        TuangouReceiptConsumeRequest request = new TuangouReceiptConsumeRequest(app_key, app_secret, session, requestid, receipt_code, count, "", app_shop_account, app_shop_accountname, open_shop_uuid);
        TuangouReceiptConsume tuangouReceiptConsume = new TuangouReceiptConsume(request);
        TuangouReceiptConsumeResponse response = new TuangouReceiptConsumeResponse();
        try {
            response = openAPIClient.invoke(tuangouReceiptConsume);
        } catch (Exception e) {
            return null;
        }
        return response;
    }


    /**
     * 查验券码个数
     *
     * @param app_key
     * @param app_secret
     * @param session
     * @param receipt_code
     * @param open_shop_uuid
     */
    public static TuangouReceiptPrepareResponse jyQm(String app_key, String app_secret, String session, String receipt_code, String open_shop_uuid, String app_shop_id) {
        DefaultOpenAPIClient openAPIClient = new DefaultOpenAPIClient();
        TuangouReceiptPrepareRequest request = new TuangouReceiptPrepareRequest(app_key, app_secret, session, receipt_code, app_shop_id, open_shop_uuid);
        TuangouReceiptPrepare tuangouReceiptPrepare = new TuangouReceiptPrepare(request);
        TuangouReceiptPrepareResponse response = new TuangouReceiptPrepareResponse();
        try {
            response = openAPIClient.invoke(tuangouReceiptPrepare);
        } catch (Exception e) {
            return null;
        }
        return response;
    }

    /**
     * session适用店铺查询接口
     *
     * @param app_key
     * @param app_secret
     * @param session
     * @param bid
     */
    public static CustomerKeyShopScopeResponse getSYDP(String app_key, String app_secret, String session, String bid) {
        DefaultOpenAPIClient openAPIClient = new DefaultOpenAPIClient();
        CustomerKeyShopScopeRequest request = new CustomerKeyShopScopeRequest(app_key, app_secret, session, bid);
        CustomerKeyShopScopeQuery shopScopeQuery = new CustomerKeyShopScopeQuery(request);
        CustomerKeyShopScopeResponse response;
        try {
            response = openAPIClient.invoke(shopScopeQuery);
        } catch (Exception e) {
            return null;
        }
        return response;
    }

    /**
     * 保存美团记录表
     *
     * @param ddbh
     * @param ddid
     * @param s
     * @return
     */
    public static WlgbMtLog saveMtLog(String ddbh, String ddid, DingdingEmployee dingdingEmployee, String s,
                                      TuangouReceiptConsumeResponseEntity tt, WlgbMtMd wlgbMtMd, String crmbh, String khdh) {
        // 保存美团验券数据给渠道
        WlgbMtLog wlgbMtLog = new WlgbMtLog();
        wlgbMtLog.setDdbh(ddbh);
//        wlgbMtLog.setDdid(ddid);
        wlgbMtLog.setYqr(dingdingEmployee != null ? dingdingEmployee.getName() : null);
        wlgbMtLog.setYqrid(dingdingEmployee != null ? dingdingEmployee.getUserid() : null);
        wlgbMtLog.setQh(s);
        wlgbMtLog.setSpmc(tt.getDeal_title());
        wlgbMtLog.setSpsmj(tt.getDeal_price());
        wlgbMtLog.setSpscj(tt.getDeal_marketprice());
        wlgbMtLog.setYqmd(wlgbMtMd.getShopname());
        wlgbMtLog.setYqsj(new Date());
        wlgbMtLog.setBz("已验券");
        wlgbMtLog.setYqcs(wlgbMtMd.getCityname());
        wlgbMtLog.setYqdm(wlgbMtMd.getBranchname());
        wlgbMtLog.setYqmddz(wlgbMtMd.getShopaddress());
        wlgbMtLog.setCrmbh(crmbh);
        wlgbMtLog.setKhdh(khdh);

        return wlgbMtLog;
    }
}
