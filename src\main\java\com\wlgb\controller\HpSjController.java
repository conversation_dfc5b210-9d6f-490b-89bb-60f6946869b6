package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.FwqBbb2;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/23 10:34
 */
@RestController
@RequestMapping(value = "/wlgb/hpsj")
@Slf4j
public class HpSjController {

    @Autowired
    private HpslService hpslService;
    @Autowired
    private FwqThbD9cService fwqThbD9cService;
    @Autowired
    private QydzService qydzService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private BjjlService bjjlService;
    @Autowired
    private BjjlZbdService bjjlZbdService;

    /**
     * 批量添加好评数量
     *
     * @param req
     * @return
     */
    @RequestMapping("/addHpsls")
    public Result addHpsls(HttpServletRequest req) {
        String dq = req.getParameter("dq");
        String qy = req.getParameter("qy");
        String dz = req.getParameter("dz");
        String city = req.getParameter("city");
        String bm = req.getParameter("bm");
        String rq = req.getParameter("rq");
        String hpqd = req.getParameter("hpqd");
        String sl = req.getParameter("sl");
        String bz = req.getParameter("bz");
        String spr = req.getParameter("spr");
        String dzuserid = req.getParameter("dzuserid");
        String bmid = req.getParameter("bmid");
        String sprxm = req.getParameter("sprxm");
        String wybs = req.getParameter("wybs");
        String slid = req.getParameter("slid");
        String djrid = req.getParameter("djrid");
        String cjtime = req.getParameter("cjtime");
        Hpsl hpsl = new Hpsl();
        hpsl.setDq(dq);
        hpsl.setQy(qy);
        hpsl.setDz(dz);
        hpsl.setCity(city);
        hpsl.setBm(bm);
        try {
            Calendar c = Calendar.getInstance();
            c.setTimeInMillis(Long.parseLong(rq));
            hpsl.setRq(c.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        hpsl.setHpqd(hpqd);
        try {
            hpsl.setSl(Integer.parseInt(sl));
        } catch (Exception e) {
            e.printStackTrace();
        }
        hpsl.setBz(bz);
        hpsl.setJdsl(0);
        hpsl.setSwcs(0);
        hpsl.setLcsl(0);
        hpsl.setTjry(sprxm);
        hpsl.setBmid(bmid);
        try {
            Calendar c = Calendar.getInstance();
            c.setTimeInMillis(Long.parseLong(cjtime));
            hpsl.setCjtime(c.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        hpsl.setWybs(wybs);
        hpsl.setSlid(slid);
        hpsl.setDjrid(djrid);

        hpslService.save(hpsl);
        return Result.OK();
    }

    /**
     * 修改好评数量（留存数量）
     */
    @RequestMapping(value = "/editHpsls")
    public Result editHpsls(HttpServletRequest request) {
        String wybs = request.getParameter("wybs");
        String lcsl = request.getParameter("lcsl");
        if (wybs == null || "".equals(wybs)) {
            return Result.error("唯一标识空的");
        }
        Hpsl hpsl = new Hpsl();
        hpsl.setWybs(wybs);
        Hpsl hpsl1 = hpslService.queryHpSlByHpSl(hpsl);
        if (hpsl1 == null) {
            return Result.error("数据库找不到数据");
        }
        Hpsl hpsl2 = new Hpsl();
        hpsl2.setId(hpsl1.getId());
        hpsl2.setSfwc("1");
        try {
            hpsl2.setLcsl(Integer.parseInt(lcsl));
        } catch (Exception e) {
            e.printStackTrace();
        }
        hpslService.updateById(hpsl2);


        return Result.OK();
    }

    /**
     * 新增抖音条数和小红书条数
     *
     * @param req
     * @return
     */
    @RequestMapping("/dytsAndxhsts")
    public Result dytsAndxhsts(HttpServletRequest req) {
        String rq = req.getParameter("rq");
        String dyfbsl = req.getParameter("dyfbsl");
        String xhsfbsl = req.getParameter("xhsfbsl");
        String dtts = req.getParameter("dtts");
        String mrtjhys = req.getParameter("mrtjhys");
        String vid = req.getParameter("vid");
        String vname = req.getParameter("vname");
        String dzids = req.getParameter("dz");
        String wybs = req.getParameter("wybs");
        String sprid = req.getParameter("sprid");
        String slid = req.getParameter("slid");
        String sfwc = req.getParameter("sfwc");
        String hptype = req.getParameter("hptype");
        Pattern pattern = Pattern.compile("[0-9-_]+");
        Matcher matcher = pattern.matcher(dzids);
        if (matcher.find()) {
            String extractedValue = matcher.group();
            System.out.println(extractedValue);
            DingdingEmployee dd = weiLianService.queryDingDingById(extractedValue);
            FwqThbD9c d9c = new FwqThbD9c();
            d9c.setXm(dd.getName());
            //获取店长所属部门
            FwqBbb2 fwqBbb2 = weiLianService.queryBbb2ByXm(dd.getName());
            log.info("------dd------" + dd);
            if (fwqBbb2 == null) {
                d9c.setQy("未知");
            } else {
                d9c.setQy(fwqBbb2.getBmmc());
            }
            try {
                Calendar c = Calendar.getInstance();
                c.setTimeInMillis(Long.parseLong(rq));
                d9c.setSstime(c.getTime());
            } catch (Exception e) {
                e.printStackTrace();
            }
            d9c.setSlid(slid);
            d9c.setSprid(sprid);
            d9c.setWybs(wybs);
            d9c.setSfwc(sfwc);
            d9c.setHptype(hptype);
            d9c.setDyts(Double.parseDouble(dyfbsl));
            d9c.setXhsts(Double.parseDouble(xhsfbsl));
            d9c.setDtts(Double.parseDouble(dtts));
            d9c.setMrtjhys(Double.parseDouble(mrtjhys));
            d9c.setVid(vid);
            d9c.setVname(vname);
            fwqThbD9cService.save(d9c);
        }
        return Result.OK();
    }

    /**
     * 新增抖音条数和小红书完成状态
     */
    @RequestMapping(value = "editDytsAndxhsts")
    public Result editDytsAndxhsts(HttpServletRequest request) {
        String wybs = request.getParameter("wybs");
        String lcnum = request.getParameter("lcnum");
        String sfwc = request.getParameter("sfwc");
        FwqThbD9c fwqThbD9c = new FwqThbD9c();
        fwqThbD9c.setWybs(wybs);
        FwqThbD9c fwqThbD9c1 = fwqThbD9cService.selectOne(fwqThbD9c);
        if(fwqThbD9c1 == null){
            return Result.error("找不到对应的数据");
        }
        FwqThbD9c fwqThbD9c2 = new FwqThbD9c();
        fwqThbD9c2.setLcnum(lcnum);
        fwqThbD9c2.setSfwc(sfwc);
        fwqThbD9c2.setId(fwqThbD9c1.getId());
        fwqThbD9cService.updateById(fwqThbD9c2);

        return Result.OK();
    }


    /**
     * 区域店长新增
     */
    @PostMapping("/saveDz")
    public Result<Object> saveDz(@RequestParam String datas) {
        JSONObject jsonObject = JSONObject.parseObject(datas);
        Qydz qydz = new Qydz();
        qydz.setUserid(jsonObject.getString("userid"));
        qydz.setDq(jsonObject.getString("dq"));
        qydz.setXm(jsonObject.getString("dz"));
        qydz.setQy(jsonObject.getString("qy"));
        qydzService.save(qydz);
        return Result.OK();
    }

    /**
     * 区域店长删除
     */
    @PostMapping("/delDz")
    public Result<Object> delDz(@RequestParam String userid) {
        Qydz qydz = new Qydz();
        qydz.setUserid(userid);
        Qydz qydz1 = qydzService.queryByQydz(qydz);
        if (qydz1 == null) {
            return Result.error("找不到该user信息");
        }
        qydzService.deleteById(qydz1.getId());
        return Result.OK();
    }

    /**
     * 区域店长修改
     */
    @PostMapping(value = "/updateDz")
    public Result<Object> updateDz(@RequestParam String datas) {
        JSONObject jsonObject = JSONObject.parseObject(datas);
        Qydz qydz = new Qydz();
        qydz.setUserid(jsonObject.getString("userid"));
        qydz.setDq(jsonObject.getString("dq"));
        qydz.setXm(jsonObject.getString("dz"));
        qydz.setQy(jsonObject.getString("qy"));

        Qydz qydz2 = new Qydz();
        qydz2.setUserid(qydz.getUserid());
        Qydz qydz1 = qydzService.queryByQydz(qydz2);
        if (qydz1 == null) {
            return Result.error("找不到该user信息");
        }
        qydz.setId(qydz1.getId());
        qydzService.updateById(qydz);

        return Result.OK();
    }

    /**
     * 保存笔记记录
     */
    @RequestMapping(value = "saveBjJl")
    public Result saveBjJl(HttpServletRequest request) {
        String gsrxm = request.getParameter("gsrxm");
        String gsrid = request.getParameter("gsrid");
        String sprxm = request.getParameter("sprxm");
        String sprid = request.getParameter("sprid");
        String zllnum = request.getParameter("zllnum");
        String city = request.getParameter("city");
        String qdbm = request.getParameter("qdbm");
        String gsrbm = request.getParameter("gsrbm");
        String cjtime = request.getParameter("cjtime");
        String wybs = request.getParameter("wybs");
        String slid = request.getParameter("slid");

        Bjjl bjjl = new Bjjl();
        bjjl.setGsrxm(gsrxm);
        bjjl.setGsrid(gsrid);
        bjjl.setSprxm(sprxm);
        bjjl.setSprid(sprid);
        try {
            bjjl.setZllnum(Integer.parseInt(zllnum));
        } catch (Exception e) {
            e.printStackTrace();
        }
        bjjl.setCity(city);
        bjjl.setQdbm(qdbm);
        bjjl.setGsrbm(gsrbm);
        try {
            Calendar c = Calendar.getInstance();
            c.setTimeInMillis(Long.parseLong(cjtime));
            bjjl.setCjtime(c.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        bjjl.setWybs(wybs);
        bjjl.setSlid(slid);

        bjjlService.save(bjjl);

        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_IHDDOQT44HHKKST9CMK5");
        ydAppkey.setToken("RA766M91LDWRE24I5EVJM81EL0MJ3X9R5L8RKQ7");
        String lcSl = YdConfig.hqLcSl(ydAppkey.getAppkey(), ydAppkey.getToken(), slid);
        JSONObject jsonObject = JSONObject.parseObject(lcSl);
        if (jsonObject.getBoolean("success") != null && jsonObject.getBoolean("success")) {
            JSONObject result = jsonObject.getJSONObject("result");
            if (result != null && result.size() > 0) {
                JSONObject data = result.getJSONObject("data");
                if (data != null && data.size() > 0) {
                    JSONArray array = data.getJSONArray("tableField_lpby5aw5");
                    if (array != null && array.size() > 0) {
                        for (Object j : array) {
                            JSONObject l = (JSONObject) j;
                            String zbdWybs = l.getString("textField_lpewvvdm");
                            Integer llNum = l.getInteger("numberField_lpby5aw7");

                            BjjlZbd bjjlZbd = new BjjlZbd();
                            bjjlZbd.setBjjlwybs(wybs);
                            bjjlZbd.setLlnum(llNum);
                            bjjlZbd.setZbdwybs(zbdWybs);
                            bjjlZbd.setSlid(slid);
                            bjjlZbdService.save(bjjlZbd);
                        }
                    }
                }

            }

        }

        return Result.OK();
    }

}
