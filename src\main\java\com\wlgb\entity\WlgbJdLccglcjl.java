package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 乐诚采购流程记录
 * @Author: jeecg-boot
 * @Date:   2022-07-25
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_lccglcjl")
public class WlgbJdLccglcjl {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**审批编号*/
    private java.lang.String spbh;
	/**审批标题*/
    private java.lang.String spbt;
	/**申请人*/
    private java.lang.String sqr;
	/**申请人id*/
    private java.lang.String sqrid;
	/**申请人部门*/
    private java.lang.String sqrbm;
	/**申请人部门id*/
    private java.lang.String sqrbmid;
	/**门店类型*/
    private java.lang.String mdlx;
	/**费用类别*/
    private java.lang.String fylb;
	/**是否代付*/
    private java.lang.String sfdf;
	/**付款类别*/
    private java.lang.String fklb;
	/**费用明细*/
    private java.lang.String fymx;
	/**说明原因*/
    private java.lang.String smyy;
	/**预计金额*/
    private java.lang.Double yjje;
	/**别墅名称*/
    private java.lang.String bsmc;
	/**出纳转账时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date cnzzsj;
	/**出纳转账金额*/
    private java.lang.Double cnzzje;
	/**是否录入金蝶*/
    private java.lang.Integer sflrjd;
}
