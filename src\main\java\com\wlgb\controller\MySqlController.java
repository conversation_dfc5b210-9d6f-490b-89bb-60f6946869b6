package com.wlgb.controller;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import com.wlgb.config.Result;
import com.wlgb.service2.FwqThbD3cService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/wlgb/mysql")
public class MySqlController {
    @Autowired
    private FwqThbD3cService fwqThbD3cService;

    @RequestMapping(value = "queryCCGC")
    public Result queryCCGC() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 减去一个月，得到上个月的日期，会自动处理跨年的问题
        LocalDate lastMonthDate = currentDate.minusMonths(1);
        // 得到本月的日期，会自动处理跨年的问题
        LocalDate lastMonthDate2 = currentDate.minusMonths(0);
        // 设置为上个月的1号
        LocalDate firstDayOfLastMonth = lastMonthDate.withDayOfMonth(1);
        //设置本月的1号
        LocalDate firstDayOfLastMonth2 = lastMonthDate2.withDayOfMonth(1);
        // 定义日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 格式化日期为字符串
        String formattedDate = firstDayOfLastMonth.format(formatter);
        String formattedDate2 = firstDayOfLastMonth2.format(formatter);
        //上个月 1号 14:00:00
        String sy = formattedDate + " 14:00:00";
        //本月 1号 14:00:00
        String by = formattedDate2 + " 14:00:00";
        //执行存储过程,同步执行
        fwqThbD3cService.queryCCGCscthbd1c(sy, by);
        fwqThbD3cService.queryCCGCscthbd2c(sy, by);
        return Result.OK();
    }


}
