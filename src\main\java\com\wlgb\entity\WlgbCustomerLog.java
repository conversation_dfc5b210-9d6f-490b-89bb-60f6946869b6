package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 带看操作日志
 * @Author: jeecg-boot
 * @Date:   2020-10-16
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_customer_log")
public class WlgbCustomerLog {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String clid;
	/**操作用户id*/
    private java.lang.String cluserid;
	/**操作人姓名*/
    private java.lang.String clname;
	/**操作详情*/
    private java.lang.String cltext;
	/**操作时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date cltime;
	/**带看id*/
    private java.lang.String clcid;
}
