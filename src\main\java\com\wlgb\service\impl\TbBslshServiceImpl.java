package com.wlgb.service.impl;

import com.wlgb.entity.TbBslsh;
import com.wlgb.mapper.TbBslshMapper;
import com.wlgb.service.TbBslshService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27 13:15
 */
@Service
public class TbBslshServiceImpl implements TbBslshService {
    @Resource
    private TbBslshMapper tbBslshMapper;

    @Override
    public void saveBatch(List<TbBslsh> list) {
        list.forEach(l->{
            tbBslshMapper.insertSelective(l);
        });
    }
}
