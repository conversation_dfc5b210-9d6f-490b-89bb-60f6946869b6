package com.wlgb.config;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.taobao.api.ApiException;

/**
 * <AUTHOR>
 * @Date 2022/11/27 14:27
 * @Version 1.0
 */
public class DingToken {
    /**
     * 获取钉钉企业token
     *
     * @param dingkey 钉钉key
     * @return token
     */
    public static String token(Dingkey dingkey) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest req = new OapiGettokenRequest();
        req.setAppkey(dingkey.getAppkey());
        req.setAppsecret(dingkey.getAppsecret());
        req.setHttpMethod("GET");
        OapiGettokenResponse rsp = client.execute(req);
        return rsp.getAccessToken();
    }
}
