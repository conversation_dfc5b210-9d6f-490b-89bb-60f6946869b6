package com.wlgb.config;

import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponse;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.aliyun.tea.TeaException;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiProcessinstanceGetRequest;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class DingSpXqConfig {

    public static OapiProcessinstanceGetResponse.ProcessInstanceTopVo SpXq(Dingkey dingkey, String processInstanceId) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/get");
        OapiProcessinstanceGetRequest req = new OapiProcessinstanceGetRequest();
        req.setProcessInstanceId(processInstanceId);
        OapiProcessinstanceGetResponse rsp = client.execute(req, DingToken.token(dingkey));
        OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = rsp.getProcessInstance();
        log.info("*******DingSpXqConfig.SpX的返回值name*********{}", JSON.toJSONString(processInstance));
        return processInstance;
    }

    /************以下是通过最新api获取审批详细内容的接口**************/
    public static com.aliyun.dingtalkworkflow_1_0.Client createClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkworkflow_1_0.Client(config);
    }

    public static GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult getSpXq(Dingkey dingkey, String processInstanceId) throws Exception {
        String token = DingToken.token(dingkey);
        com.aliyun.dingtalkworkflow_1_0.Client client = DingSpXqConfig.createClient();
        com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders getProcessInstanceHeaders = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders();
        getProcessInstanceHeaders.xAcsDingtalkAccessToken = token;
        com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest getProcessInstanceRequest = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest()
                .setProcessInstanceId(processInstanceId);
        GetProcessInstanceResponse getxp = new GetProcessInstanceResponse();
        try {
            getxp = client.getProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new com.aliyun.teautil.models.RuntimeOptions());
            log.info("*******DingSpXqConfig.getSpXq的返回值name*********{}", JSON.toJSONString(getxp));
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                System.out.println("获取审批详情报错");
                System.out.println(err.message);
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // err 中含有 code 和 message 属性，可帮助开发定位问题
                System.out.println("获取审批详情报错11111111111111111");
                System.out.println(err.message);
            }
        }
        return getxp.getBody().getResult();
    }

}
