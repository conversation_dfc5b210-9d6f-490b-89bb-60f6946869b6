package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdSstyjsqjl;
import com.wlgb.mapper.WlgbJdSstyjsqjlMapper;
import com.wlgb.service.WlgbJdSstyjsqjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 23:04
 */
@Service
public class WlgbJdSstyjsqjlServiceImpl implements WlgbJdSstyjsqjlService {
    @Resource
    private WlgbJdSstyjsqjlMapper wlgbJdSstyjsqjlMapper;

    @Override
    public void save(WlgbJdSstyjsqjl wlgbJdSstyjsqjl) {
        wlgbJdSstyjsqjl.setCreateTime(new Date());
        wlgbJdSstyjsqjl.setId(IdConfig.uuId());
        wlgbJdSstyjsqjlMapper.insertSelective(wlgbJdSstyjsqjl);
    }

    @Override
    public void updateById(WlgbJdSstyjsqjl wlgbJdSstyjsqjl) {
        wlgbJdSstyjsqjl.setUpdateTime(new Date());
        wlgbJdSstyjsqjlMapper.updateByPrimaryKeySelective(wlgbJdSstyjsqjl);
    }

    @Override
    public WlgbJdSstyjsqjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd) {
        Example example = new Example(WlgbJdSstyjsqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdSstyjsqjlMapper.selectOneByExample(example);
    }
}
