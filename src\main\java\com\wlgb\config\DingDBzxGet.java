package com.wlgb.config;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiProcessWorkrecordTaskQueryRequest;
import com.dingtalk.api.request.OapiProcessinstanceGetRequest;
import com.dingtalk.api.response.OapiProcessWorkrecordTaskQueryResponse;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.taobao.api.ApiException;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/24 14:02
 */
public class DingDBzxGet {
    /**
     * 获取待办列表
     */
    public static String hqWwcDb(Dingkey dingkey, String userid, Long page) {
        String body = "";
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/process/workrecord/task/query");
            OapiProcessWorkrecordTaskQueryRequest req = new OapiProcessWorkrecordTaskQueryRequest();
            req.setUserid(userid);
            req.setOffset(page);
            req.setCount(50L);
            req.setStatus(0L);
            OapiProcessWorkrecordTaskQueryResponse rsp = client.execute(req, DingToken.token(dingkey));
            body = rsp.getBody();
            return body;
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return body;
    }

    /**
     * 根据id获取待办详情
     */
    public static String hqWwcDbXq(Dingkey dingkey, String slid) {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/get");
            OapiProcessinstanceGetRequest req = new OapiProcessinstanceGetRequest();
            req.setProcessInstanceId(slid);
            OapiProcessinstanceGetResponse rsp = client.execute(req, DingToken.token(dingkey));
            String body = rsp.getBody();
            return body;
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return "";
    }
}
