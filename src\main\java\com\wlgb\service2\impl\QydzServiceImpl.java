package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.Qydz;
import com.wlgb.mapper.QydzMapper;
import com.wlgb.service2.QydzService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/3 9:47
 */
@Service
@DS(value = "second")
public class QydzServiceImpl implements QydzService {
    @Resource
    private QydzMapper qydzMapper;

    @Override
    public void save(Qydz qydz) {
        qydzMapper.insertSelective(qydz);
    }

    @Override
    public void updateById(Qydz qydz) {
        qydzMapper.updateByPrimaryKeySelective(qydz);
    }

    @Override
    public Qydz queryByQydz(Qydz qydz) {
        return qydzMapper.selectOne(qydz);
    }

    @Override
    public void deleteById(Integer id) {
        qydzMapper.deleteByPrimaryKey(id);
    }
}
