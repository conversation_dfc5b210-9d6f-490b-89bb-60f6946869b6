package com.wlgb.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/12 8:50
 */
@Data
@Component
@ConfigurationProperties(prefix = "jqrtz")
public class JqrConfig {
    private String chwebhook;
    private String chkey;
    private String hpswebhook;
    private String hpskey;
    private String xdwebhook;
    private String xdkey;
    private String dhrjxydwebhook;
    private String dhrjxydkey;
}
