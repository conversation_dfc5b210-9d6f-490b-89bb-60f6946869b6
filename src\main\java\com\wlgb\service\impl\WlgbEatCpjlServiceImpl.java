package com.wlgb.service.impl;

import com.wlgb.entity.WlgbEatCpjl;
import com.wlgb.mapper.WlgbEatCpjlMapper;
import com.wlgb.service.WlgbEatCpjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/13 18:47
 */
@Service
public class WlgbEatCpjlServiceImpl implements WlgbEatCpjlService {
    @Resource
    private WlgbEatCpjlMapper wlgbEatCpjlMapper;

    @Override
    public void save(WlgbEatCpjl wlgbEatCpjl) {
        wlgbEatCpjl.setCreateTime(new Date());
        wlgbEatCpjlMapper.insertSelective(wlgbEatCpjl);
    }

    @Override
    public void clearData(String ddbh) {
        WlgbEatCpjl wlgbEatCpjl = new WlgbEatCpjl();
        wlgbEatCpjl.setSfsc(0);
        wlgbEatCpjl.setDdbh(ddbh);
        List<WlgbEatCpjl> list = wlgbEatCpjlMapper.select(wlgbEatCpjl);
        list.forEach(l->{
            WlgbEatCpjl wlgbEatCpjl1 = new WlgbEatCpjl();
            wlgbEatCpjl1.setSfsc(1);
            wlgbEatCpjl1.setUpdateTime(new Date());
            wlgbEatCpjl1.setId(l.getId());
            wlgbEatCpjlMapper.updateByPrimaryKeySelective(wlgbEatCpjl1);
        });
    }
}
