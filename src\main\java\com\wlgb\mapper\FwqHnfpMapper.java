package com.wlgb.mapper;

import com.wlgb.entity.FwqHnfp;
import com.wlgb.entity.vo.FwqHnfpVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface FwqHnfpMapper extends Mapper<FwqHnfp> {

    @Select("<script>" +
            "SELECT xiaoquname, COUNT(*) as count, DATE_FORMAT(MAX(time), '%Y-%m-%d' )  as time2 " +
            "FROM fwq_hnfp " +
            "WHERE sfsc = '0' " +
            "AND time = (" +
            "    SELECT MAX(time) " +
            "    FROM fwq_hnfp f2 " +
            "    WHERE f2.xiaoquname = fwq_hnfp.xiaoquname " +
            "    AND f2.sfsc = '0' " +
            "    <if test='sousuo != null and sousuo != \"\"'>" +
            "    AND (f2.xiaoquname LIKE CONCAT('%', #{sousuo}, '%') " +
            "    OR f2.scrname LIKE CONCAT('%', #{sousuo}, '%') " +
            "    OR f2.biaodiname LIKE CONCAT('%', #{sousuo}, '%')) " +
            "    </if>" +
            ") " +
            "<if test='sousuo != null and sousuo != \"\"'>" +
            "AND (xiaoquname LIKE CONCAT('%', #{sousuo}, '%') " +
            "OR scrname LIKE CONCAT('%', #{sousuo}, '%') " +
            "OR biaodiname LIKE CONCAT('%', #{sousuo}, '%')) " +
            "</if>" +
            "GROUP BY xiaoquname " +
            "ORDER BY MAX(time) DESC " +
            "LIMIT #{offset}, #{pageSize}" +
            "</script>")
    List<FwqHnfpVo> selectGroupByXiaoquName(@Param("sousuo") String sousuo, @Param("offset") int offset, @Param("pageSize") int pageSize);
}
