package com.wlgb.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kingdee.bos.webapi.sdk.K3CloudApi;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/03/12 14:11
 */
@Slf4j
public class KingDeeConfig {
    public static void main(String[] args) {
        K3CloudApi k3CloudApi = new K3CloudApi();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FormId", "AR_RECEIVEBILL");
        jsonObject.put("FieldKeys", "FBillTypeID_FEntryId");
//
//        String data = "[" +
//                "{\"Left\":\"(\",\"FieldName\":\"FBillTypeID\",\"Compare\":\"=\",\"Value\":\"SKDLX06_SYS\",\"Right\":\")\",\"Logic\":\"AND\"}" +
//                ",{\"Left\":\"(\",\"FieldName\":\"FDATE\",\"Compare\":\"=\",\"Value\":\"2022-09-01\",\"Right\":\")\",\"Logic\":\"\"}" +
//                "]";

        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("Left", "(");
        jsonObject1.put("FieldName", "FBillTypeID_FEntryId");
        jsonObject1.put("Compare", "=");
        jsonObject1.put("Value", "SKDLX06_SYS");
        jsonObject1.put("Right", ")");
        jsonObject1.put("Logic", "");
        list.add(jsonObject1);
//        JSONObject jsonObject2 = new JSONObject();
//        jsonObject2.put("Left", "(");
//        jsonObject2.put("FieldName", "FDATE");
//        jsonObject2.put("Compare", ">=");
//        jsonObject2.put("Value", "2022-09-01");
//        jsonObject2.put("Right", ")");
//        jsonObject2.put("Logic", "");
//        list.add(jsonObject2);

        jsonObject.put("FilterString", list);
        jsonObject.put("TopRowCount", 2000);
        jsonObject.put("StartRow", 0);
        jsonObject.put("Limit", 0);
        try {
            List<List<Object>> view = k3CloudApi.executeBillQuery(jsonObject.toJSONString());
            if (view.size() > 0) {
                System.out.println("存在");
            } else {
                System.out.println("不存在");
            }
            System.out.println(view);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 场地费尾款保存收款单
     *
     * @param res  场地费表数据
     * @param bsbm 金蝶门店编码
     * @param mdlx 账套编码
     */
    public static void cdfSaveSkd(JSONObject res, String bsbm, String mdlx) {
        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "AR_RECEIVEBILL";

        //请求数据对象
        JSONObject data = new JSONObject();
        //需要更新的字段，数组类型，格式：[key1,key2,...] （非必录）注（更新单据体字段得加上单据体key）
        data.put("NeedUpDateFields", new ArrayList<>());
        //需返回结果的字段集合，数组类型，格式：[key,entitykey.key,...]（非必录） 注（返回单据体字段格式：entitykey.key）
        data.put("NeedReturnFields", new ArrayList<>());
        //是否删除已存在的分录，布尔类型，默认true（非必录）
        data.put("IsDeleteEntry", true);
        //表单所在的子系统内码，字符串类型（非必录）
        data.put("SubSystemId", "");
        //是否验证所有的基础资料有效性，布尔类，默认false（非必录）
        data.put("IsVerifyBaseDataField", false);
        //是否批量填充分录，默认true（非必录）
        data.put("IsEntryBatchFill", true);
        //是否验证标志，布尔类型，默认true（非必录）
        data.put("ValidateFlag", true);
        //是否用编码搜索基础资料，布尔类型，默认true（非必录）
        data.put("NumberSearch", true);
        //交互标志集合，字符串类型，分号分隔，格式："flag1;flag2;..."（非必录） 例如（允许负库存标识：STK_InvCheckResult）
        data.put("InterationFlags", "");

        //请求子数据
        JSONObject data_1 = new JSONObject();
        data_1.put("FID", 0);

        //单据类型
        data_1.put("FBillTypeID", zhdx("SKDLX01_SYS"));
        //业务日期
        data_1.put("FDATE", res.getString("dzrq"));
        //往来单位类型
        data_1.put("FCONTACTUNITTYPE", "BD_Customer");

        //往来单位
        data_1.put("FCONTACTUNIT", zhdx("CUST0006"));
        //付款单位类型
        data_1.put("FPAYUNITTYPE", "BD_Customer");

        //付款单位
        data_1.put("FPAYUNIT", zhdx("CUST0006"));

        //币别
        data_1.put("FCURRENCYID", zhdx("PRE001"));
        //收款组织
        data_1.put("FPAYORGID", zhdx(mdlx));
        //结算汇率
        data_1.put("FSETTLERATE", 1.0);
        //协议单编号
        data_1.put("F_ABC_TEXT3", res.getString("ddbh"));
        //结算组织
        data_1.put("FSETTLEORGID", zhdx(mdlx));
        //单据状态
        data_1.put("FDOCUMENTSTATUS", "Z");
        //是否期初单据
        data_1.put("FISINIT", false);
        //汇率
        data_1.put("FEXCHANGERATE", 1.0);
        //作废状态
        data_1.put("FCancelStatus", "A");

        //B2C业务
        data_1.put("FISB2C", false);
        //是否转销
        data_1.put("FIsWriteOff", false);
        //核销方式
        data_1.put("FMatchMethodID", 0);
        //是否下推携带汇率到结算汇率
        data_1.put("FISCARRYRATE", false);

        //银盛编号
//        data_1.put("F_YS_Text", "无");
        //订单编号
//        data_1.put("F_DD_Text", res.getString("ddbh"));
        //别墅
//        data_1.put("F_abc_Base", zhdx(bsbm));
        //券码
        data_1.put("F_abc_Text", "");

        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FEntryID", 0);

        //银盛编号
//        jsonObject.put("F_YSLSH_Text", "无");
        //订单编号
//        jsonObject.put("F_XDDBH_Text1", res.getString("ddbh"));
        //协议单编号
        jsonObject.put("F_ABC_Text2", res.getString("ddbh"));
        //结算方式
        jsonObject.put("FSETTLETYPEID", zhdx("JSFS01_SYS"));
        //收款用途
        jsonObject.put("FPURPOSEID", zhdx("SFKYT41_SYS"));
        //预收销售订单号内码
        jsonObject.put("FSaleOrderID", 0);
        //表体-应收金额
        jsonObject.put("FRECTOTALAMOUNTFOR", res.getDouble("gsys"));
        //收款金额
        jsonObject.put("FRECAMOUNTFOR_E", res.getDouble("gsys"));
        //门店
        jsonObject.put("F_ABC_BASE1", zhdx(bsbm));
        //费用项目
        jsonObject.put("FCOSTID", zhdx("1122.03"));

        list.add(jsonObject);

        data_1.put("FRECEIVEBILLENTRY", list);

        //表单数据包，JSON类型（必录）
        data.put("Model", data_1);
        String save = null;
        try {
            save = k3CloudApi.save(formId, data.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        String number = "";
        JSONObject jsonObject2 = JSONObject.parseObject(save);
        if (jsonObject2 != null) {
            JSONObject result = jsonObject2.getJSONObject("Result");
            if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
                number = result.getString("Number");
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("CreateOrgId", 0);
                List<String> list2 = new ArrayList<>();
                list2.add(number);
                jsonObject1.put("Numbers", list2);
                try {
                    String submit = k3CloudApi.submit(formId, jsonObject1.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    String audit = k3CloudApi.audit(formId, jsonObject1.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 应收单提交
     *
     * @param list 应收单提交
     */
    public static void ysdTj(List<String> list) {
        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "AR_receivable";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("CreateOrgId", 0);
        jsonObject.put("Numbers", list);
        jsonObject.put("Ids", "");
        jsonObject.put("SelectedPostId", 0);
        jsonObject.put("NetworkCtrl", "false");

        String submit = null;
        try {
            submit = k3CloudApi.submit(formId, jsonObject.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(submit);

        String audit = null;
        try {
            audit = k3CloudApi.audit(formId, jsonObject.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(audit);

//        skdXt(list);
    }


    /**
     * 收款退款单提交
     *
     * @param list 收款退款单提交
     */
    public static void skTkdTj(List<String> list) {
        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "AR_REFUNDBILL";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("CreateOrgId", 0);
        jsonObject.put("Numbers", list);
        jsonObject.put("Ids", "");
        jsonObject.put("SelectedPostId", 0);
        jsonObject.put("NetworkCtrl", "false");

        String submit = null;
        try {
            submit = k3CloudApi.submit(formId, jsonObject.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(submit);

        String audit = null;
        try {
            audit = k3CloudApi.audit(formId, jsonObject.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(audit);
    }

    /**
     * 应收单下推收款单
     *
     * @param list 编号
     */
    @SneakyThrows
    public static void skdXt(List<String> list) {
        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "AR_receivable";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Numbers", list);
        jsonObject.put("RuleId", "AR_recableToRecBill");

        String push = k3CloudApi.push(formId, jsonObject.toJSONString());
        System.out.println(push);
    }

    /**
     * 转换对象
     */
    public static JSONObject zhdx(String text) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FNumber", text);

        return jsonObject;
    }


    /**
     * 场地费表提交至应收单
     *
     * @param res  场地费表数据
     * @param mdbh 门店编码
     */
    public static Boolean cdf(JSONObject res, String mdbh, String mdlx) {
        Boolean t = false;

        double sum = res.getDouble("df") + res.getDouble("cy") + res.getDouble("zhgssp") + res.getDouble("ch") + res.getDouble("hps") + res.getDouble("jbs") + res.getDouble("bx") + res.getDouble("cs") + res.getDouble("cfsy") + res.getDouble("ws") + res.getDouble("pc") + res.getDouble("rt") + res.getDouble("ys") + res.getDouble("dg") + res.getDouble("qtf") + res.getDouble("dzjjc");
        if (sum >= 0) {
            String obj = "{\n" +
                    "    \"NeedUpDateFields\": [],\n" +
                    "    \"NeedReturnFields\": [],\n" +
                    "    \"IsDeleteEntry\": \"true\",\n" +
                    "    \"SubSystemId\": \"\",\n" +
                    "    \"IsVerifyBaseDataField\": \"false\",\n" +
                    "    \"IsEntryBatchFill\": \"true\",\n" +
                    "    \"ValidateFlag\": \"true\",\n" +
                    "    \"NumberSearch\": \"true\",\n" +
                    "    \"IsAutoAdjustField\": \"false\",\n" +
                    "    \"InterationFlags\": \"\",\n" +
                    "    \"IgnoreInterationFlag\": \"\",\n";
            JSONObject model = new JSONObject();
            model.put("FID", 0);
            //单据类型
            model.put("FBillTypeID", zhdx("YSD02_SYS"));
            //业务日期
            model.put("FDATE", res.getString("dzrq"));
            //单据生成时间
            model.put("F_PAEA_Datetime", res.getString("dzrq"));
            //是否期初单据
            model.put("FISINIT", false);
            //
            model.put("FENDDATE_H", res.getString("dzrq"));
            //客户
            model.put("FCUSTOMERID", zhdx("CUST0006"));
            //币别
            model.put("FCURRENCYID", zhdx("PRE001"));
            //
            //价外税
            model.put("FISPRICEEXCLUDETAX", true);
            //结算组织
            model.put("FSETTLEORGID", zhdx(mdlx));
            //收款组织
            model.put("FPAYORGID", zhdx(mdlx));
            //销售组织
            model.put("FSALEORGID", zhdx(mdlx));
            //按含税单价录入
            model.put("FISTAX", true);
            //作废状态
            model.put("FCancelStatus", "A");
            //业务类型
            model.put("FBUSINESSTYPE", "FY");
            //立账类型
            model.put("FSetAccountType", "1");
            //参与暂估应收核销
            model.put("FISHookMatch", false);
            //先到票后出库
            model.put("FISINVOICEARLIER", false);
            //反写开票数量
            model.put("FWBOPENQTY", false);
            model.put("FISGENERATEPLANBYCOSTITEM", false);
            //别墅
            model.put("F_abc_Base", zhdx(mdbh));
            //总金额
            model.put("F_abc_Text", sum + "");
            //银盛编号
            model.put("F_abc_Text1", "无");
            //订单编号
            model.put("F_abc_Text2", res.getString("ddbh"));
            obj += "\"Model\":" + (model.toJSONString().substring(0, model.toJSONString().length() - 1)) + ",\"FSALEDEPTID\":{\"FNumber\":\"" + 1 + "\"},\n";

            //客户
            JSONObject jsonObject3 = new JSONObject();
            //订货方
            jsonObject3.put("FORDERID", zhdx("CUST0006"));
            //收货方
            jsonObject3.put("FTRANSFERID", zhdx("CUST0006"));
            //付款方
            jsonObject3.put("FChargeId", zhdx("CUST0006"));
            obj += "\"FsubHeadSuppiler\":" + jsonObject3.toJSONString() + ",\n";

            //财务
            JSONObject jsonObject1 = new JSONObject();
            //到期日计算日期
            jsonObject1.put("FACCNTTIMEJUDGETIME", res.getString("dzrq"));
            //结算方式
            jsonObject1.put("FSettleTypeID", zhdx("JSFS01_SYS"));
            //本位币
            jsonObject1.put("FMAINBOOKSTDCURRID", zhdx("PRE001"));
            //汇率类型
            jsonObject1.put("FEXCHANGETYPE", zhdx("HLTX01_SYS"));
            //汇率
            jsonObject1.put("FExchangeRate", 1.0);
            //不含税金额
            jsonObject1.put("FNoTaxAmountFor", sum);
            obj += "\"FsubHeadFinc\": " + jsonObject1.toJSONString() + ",\n";

            //明细
            obj += "\"FEntityDetail\": [";
            String str = "";
            JSONObject jsonObject = new JSONObject();
            //订单编号
            jsonObject.put("F_abc_Text3", res.getString("ddbh"));
            //计价单位
            jsonObject.put("FPRICEUNITID", zhdx("CNY"));
            //计价数量
            jsonObject.put("FPriceQty", 1.0);
            //控制发货数量
            jsonObject.put("FDeliveryControl", false);
            //是否赠品
            jsonObject.put("FIsFree", false);
            //计价基本分母
            jsonObject.put("FPriceBaseDen", 1.0);
            //销售基本分子
            jsonObject.put("FSalBaseNum", 1.0);
            //库存基本分子
            jsonObject.put("FStockBaseNum", 1.0);
            //场地费
            if (res.getDouble("yhhcdf") > 0) {
                jsonObject.put("FCOSTID", zhdx("6001.01"));
                jsonObject.put("FTaxPrice", res.getDouble("yhhcdf"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("yhhcdf"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("yhhcdf"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("yhhcdf"));
                str += jsonObject.toJSONString();
            }
            //电费
            if (res.getDouble("df") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.02"));
                jsonObject.put("FTaxPrice", res.getDouble("df"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("df"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("df"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("df"));
                str += jsonObject.toJSONString();
            }
            //餐饮
            if (res.getDouble("cy") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.03"));
                jsonObject.put("FTaxPrice", res.getDouble("cy"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("cy"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("cy"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("cy"));
                str += jsonObject.toJSONString();
            }
            //商品
            if (res.getDouble("zhgssp") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.04"));
                jsonObject.put("FTaxPrice", res.getDouble("zhgssp"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("zhgssp"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("zhgssp"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("zhgssp"));
                str += jsonObject.toJSONString();
            }
            //策划
            if (res.getDouble("ch") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.05"));
                jsonObject.put("FPRICEUNITID", zhdx("CNY"));
                jsonObject.put("FTaxPrice", res.getDouble("ch"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("ch"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("ch"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("ch"));
                str += jsonObject.toJSONString();
            }
            //厨房使用费
            if (res.getDouble("cfsy") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.06"));
                jsonObject.put("FTaxPrice", res.getDouble("cfsy"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("cfsy"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("cfsy"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("cfsy"));
                str += jsonObject.toJSONString();
            }
            //卫生费
            if (res.getDouble("ws") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.07"));
                jsonObject.put("FTaxPrice", res.getDouble("ws"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("ws"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("ws"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("ws"));
                str += jsonObject.toJSONString();
            }
            //赔偿费
            if (res.getDouble("pc") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.08"));
                jsonObject.put("FTaxPrice", res.getDouble("pc"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("pc"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("pc"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("pc"));
                str += jsonObject.toJSONString();
            }
            //人头费
            if (res.getDouble("rt") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.09"));
                jsonObject.put("FTaxPrice", res.getDouble("rt"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("rt"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("rt"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("rt"));
                str += jsonObject.toJSONString();
            }
            //轰趴师
            if (res.getDouble("hps") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.10"));
                jsonObject.put("FTaxPrice", res.getDouble("hps"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("hps"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("hps"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("hps"));
                str += jsonObject.toJSONString();
            }
            //剧本杀
            if (res.getDouble("jbs") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.11"));
                jsonObject.put("FTaxPrice", res.getDouble("jbs"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("jbs"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("jbs"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("jbs"));
                str += jsonObject.toJSONString();
            }
            //保险
            if (res.getDouble("bx") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.12"));
                jsonObject.put("FTaxPrice", res.getDouble("bx"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("bx"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("bx"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("bx"));
                str += jsonObject.toJSONString();
            }
            //cs
            if (res.getDouble("cs") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.13"));
                jsonObject.put("FTaxPrice", res.getDouble("cs"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("cs"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("cs"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("cs"));
                str += jsonObject.toJSONString();
            }
            //延时
            if (res.getDouble("ys") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.14"));
                jsonObject.put("FTaxPrice", res.getDouble("ys"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("ys"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("ys"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("ys"));
                str += jsonObject.toJSONString();
            }
            //代购
            if (res.getDouble("dg") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.15"));
                jsonObject.put("FTaxPrice", res.getDouble("dg"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("dg"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("dg"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("dg"));
                str += jsonObject.toJSONString();
            }
            //其他
            if (res.getDouble("qtf") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.99"));
                jsonObject.put("FTaxPrice", res.getDouble("qtf"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("qtf"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("qtf"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("qtf"));
                str += jsonObject.toJSONString();
            }
            //店长奖金池
            if (res.getDouble("dzjjc") > 0) {
                if (!"".equals(str)) {
                    str += ",";
                }
                jsonObject.put("FCOSTID", zhdx("6001.45"));
                jsonObject.put("FTaxPrice", res.getDouble("dzjjc"));
                jsonObject.put("FALLAMOUNTFOR_D", res.getDouble("dzjjc"));
                jsonObject.put("FNORECEIVEAMOUNT", res.getDouble("dzjjc"));
                jsonObject.put("FTAILDIFFFLAG", res.getDouble("dzjjc"));
                str += jsonObject.toJSONString();
            }
            obj += str + "],\n}\n}";

            String formId = "AR_receivable";
            K3CloudApi k3CloudApi = new K3CloudApi();

            String save = null;
            try {
                save = k3CloudApi.save(formId, obj);
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println(save);
            JSONObject jsonObject2 = JSONObject.parseObject(save);
            JSONObject result = jsonObject2.getJSONObject("Result");
            if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
                String number = result.getString("Number");
                List<String> list6 = new ArrayList<>();
                list6.add(number);
                ysdTj(list6);
                t = true;
            }

            if (t) {
                Double gsys = res.getDouble("gsys");
                if (gsys > 0) {
                    cdfSaveSkd(res, mdbh, mdlx);
                } else if (gsys < 0) {
                    //改成正数，金蝶不能录入负数
                    gsys = gsys * -1;
                    res.put("gsys", gsys);
                    cdfSkTkdSave(res, mdlx, mdbh);
                }
            }

            if (res.getDouble("yhhcdf") < 0) {
                scQtYsd(res, mdlx, mdbh);
            }
        }
        return t;

    }

    /**
     * 收款退款单
     */
    public static void cdfSkTkdSave(JSONObject res, String mdlx, String mdbh) {

        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("FSETTLETYPEID", zhdx("JSFS01_SYS"));
        jsonObject1.put("FPURPOSEID", zhdx("SFKYT01_SYS"));
        jsonObject1.put("FREFUNDAMOUNTFOR", res.getDouble("gsys"));
        jsonObject1.put("FREFUNDAMOUNTFOR_E", res.getDouble("gsys"));
        jsonObject1.put("FNOTE", "公司应收负数");
        jsonObject1.put("FREFUNDAMOUNT_E", res.getDouble("gsys"));
        jsonObject1.put("FPOSTDATE", res.getString("dzrq"));
        jsonObject1.put("FISPOST", true);
        jsonObject1.put("FRuZhangType", "1");
        jsonObject1.put("FPayType", "A");
        jsonObject1.put("FNOTVERIFICATEAMOUNT", res.getDouble("gsys"));
        jsonObject1.put("FByAgentBank", false);
        jsonObject1.put("FOverseaPay", false);
        //科目
        jsonObject1.put("FCOSTID", zhdx("1122.03"));
        list.add(jsonObject1);

        String obj = "{\n" +
                "    \"NeedUpDateFields\": [],\n" +
                "    \"NeedReturnFields\": [],\n" +
                "    \"IsDeleteEntry\": \"true\",\n" +
                "    \"SubSystemId\": \"\",\n" +
                "    \"IsVerifyBaseDataField\": \"false\",\n" +
                "    \"IsEntryBatchFill\": \"true\",\n" +
                "    \"ValidateFlag\": \"true\",\n" +
                "    \"NumberSearch\": \"true\",\n" +
                "    \"IsAutoAdjustField\": \"false\",\n" +
                "    \"InterationFlags\": \"\",\n" +
                "    \"IgnoreInterationFlag\": \"\",\n" +
                "    \"Model\": {\n" +
                "        \"FID\": 0,\n" +
                "        \"FBillTypeID\": {\n" +
                "            \"FNUMBER\": \"SKTKDLX01_SYS\"\n" +
                "        },\n" +
                "        \"FDATE\": \"" + res.getString("dzrq") + "\",\n" +
                "        \"FCONTACTUNITTYPE\": \"BD_Customer\",\n" +
                "        \"FISINIT\": false,\n" +
                "        \"FCONTACTUNIT\": {\n" +
                "            \"FNumber\": \"CUST0006\"\n" +
                "        },\n" +
                "        \"F_ABC_BASE\": {\n" +
                "            \"FNumber\": \"" + mdbh + "\"\n" +
                "        },\n" +
                "        \"FSETTLERATE\": 1.0,\n" +
                "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                "        \"FRECTUNITTYPE\": \"BD_Customer\",\n" +
                "        \"FRECTUNIT\": {\n" +
                "            \"FNumber\": \"CUST0006\"\n" +
                "        },\n" +
                "        \"FCURRENCYID\": {\n" +
                "            \"FNumber\": \"PRE001\"\n" +
                "        },\n" +
                "        \"FSETTLEORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FSALEORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FBUSINESSTYPE\": \"1\",\n" +
                "        \"FEXCHANGERATE\": 1.0,\n" +
                "        \"FCancelStatus\": \"A\",\n" +
                "        \"FPAYORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FISSAMEORG\": true,\n" +
                "        \"FSETTLECUR\": {\n" +
                "            \"FNUMBER\": \"PRE001\"\n" +
                "        },\n" +
                "        \"FISB2C\": false,\n" +
                "        \"FIsWriteOff\": false,\n" +
                "        \"FREMARK\": \"公司应收负数\",\n" +
                "        \"F_ABC_TEXT12\": \"" + res.getString("ddbh") + "\",\n" +
                "        \"FISCARRYRATE\": false,\n" +
                "        \"FSETTLEMAINBOOKID\": {\n" +
                "            \"FNUMBER\": \"PRE001\"\n" +
                "        },\n" +
                "        \"FVirIsSameAcctOrg\": false,\n" +
                "        \"FREFUNDBILLENTRY\":" + list.toString() + "\n" +
                "    }\n" +
                "}";

        String formId = "AR_REFUNDBILL";
        K3CloudApi k3CloudApi = new K3CloudApi();

        String save = null;
        try {
            save = k3CloudApi.save(formId, obj);
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject2 = JSONObject.parseObject(save);
        JSONObject result = jsonObject2.getJSONObject("Result");
        if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
            String number = result.getString("Number");
            List<String> list6 = new ArrayList<>();
            list6.add(number);
            skTkdTj(list6);
        }
    }

    /**
     * 退定金收款退款单
     */
    public static Boolean tdjSkTkdSave(String json) {
        String formId = "AR_REFUNDBILL";
        K3CloudApi k3CloudApi = new K3CloudApi();

        String save = null;
        try {
            save = k3CloudApi.save(formId, json);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(save);
        JSONObject jsonObject2 = JSONObject.parseObject(save);
        JSONObject result = jsonObject2.getJSONObject("Result");
        if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
            String number = result.getString("Number");
            List<String> list6 = new ArrayList<>();
            list6.add(number);
            skTkdTj(list6);
            return true;
        }
        return false;
    }


    /**
     * 返现其他应付单
     */
    public static Boolean fxQtYfd(JSONObject res) {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FCOSTID", zhdx(res.getString("km")));
        jsonObject.put("FCOSTDEPARTMENTID", zhdx("BM000009"));
        jsonObject.put("F_abc_Base1", zhdx(res.getString("bsbh")));
        jsonObject.put("FNOTAXAMOUNTFOR", res.getDouble("je"));
        jsonObject.put("FTOTALAMOUNTFOR", res.getDouble("je"));
        jsonObject.put("FNOTSETTLEAMOUNTFOR_D", res.getDouble("je"));
        jsonObject.put("FNOTAXAMOUNT_D", res.getDouble("je"));
        jsonObject.put("FCREATEINVOICE", false);
        list.add(jsonObject);

        JSONObject model = new JSONObject();
        model.put("FID", 0);
        model.put("FBillTypeID", zhdx("QTYFD01_SYS"));
        model.put("FDATE", df2.format(res.getDate("dkrq")));
        model.put("FENDDATE_H", df2.format(res.getDate("dkrq")));
        model.put("FISINIT", false);
        model.put("FCONTACTUNITTYPE", "BD_Customer");
        model.put("FCONTACTUNIT", zhdx("CUST0007"));
        model.put("FCURRENCYID", zhdx("PRE001"));
        model.put("FTOTALAMOUNTFOR_H", res.getDouble("je"));
        model.put("FNOTSETTLEAMOUNTFOR", res.getDouble("je"));
        model.put("FSETTLEORGID", zhdx(res.getString("zt")));
        model.put("FPURCHASEORGID", zhdx(res.getString("zt")));
        model.put("FPAYORGID", zhdx(res.getString("zt")));
        model.put("FSettleTypeID", zhdx("JSFS01_SYS"));
        model.put("FMAINBOOKSTDCURRID", zhdx("PRE001"));
        model.put("FEXCHANGETYPE", zhdx("HLTX01_SYS"));
        model.put("FExchangeRate", 1.0);
        model.put("FNOTAXAMOUNT", res.getDouble("je"));
        model.put("FACCNTTIMEJUDGETIME", df2.format(res.getDate("dkrq")));
        model.put("FCancelStatus", "A");
        model.put("FBUSINESSTYPE", "T");
        model.put("F_abc_Base", zhdx(res.getString("bsbh")));
        model.put("F_abc_Text12", res.getString("ddbh"));
        model.put("F_abc_Text1", res.getString("spbt"));
        model.put("F_abc_Text", res.getString("lsh"));
        model.put("FEntity", list);

        JSONObject data = new JSONObject();
        data.put("NeedUpDateFields", new ArrayList<>());
        data.put("NeedReturnFields", new ArrayList<>());
        data.put("IsDeleteEntry", true);
        data.put("SubSystemId", "");
        data.put("IsVerifyBaseDataField", false);
        data.put("IsEntryBatchFill", true);
        data.put("ValidateFlag", true);
        data.put("NumberSearch", true);
        data.put("IsAutoAdjustField", false);
        data.put("InterationFlags", "");
        data.put("IgnoreInterationFlag", "");
        data.put("Model", model);

        String formId = "AP_OtherPayable";
        K3CloudApi k3CloudApi = new K3CloudApi();

        String save = null;
        try {
            save = k3CloudApi.save(formId, data.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(save);
        JSONObject jsonObject2 = JSONObject.parseObject(save);
        JSONObject result = jsonObject2.getJSONObject("Result");
        if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
            return true;
        }
        return false;

    }

    /**
     * 审批提交单据
     *
     * @param json 提交的数据
     * @param lx   审批类型
     */
//    public static Boolean spTjJdDj(String json, String lx) {
//        String formId = "";
//        if ("预支".equals(lx) || "费用付款".equals(lx) || "资金申请".equals(lx) || "威廉币提现".equals(lx) || "退宿舍押金".equals(lx) || "借支审批".equals(lx) || "付款单".equals(lx)) {
//            formId = "AP_PAYBILL";
//        } else if ("付款退款单".equals(lx)) {
//            formId = "AP_REFUNDBILL";
//        } else if ("报销".equals(lx) || "退定金-客户返现".equals(lx) || "其他应付单".equals(lx)) {
//            formId = "AP_OtherPayable";
//        }
//        if ("".equals(formId)) {
//            return false;
//        }
//        K3CloudApi k3CloudApi = new K3CloudApi();
//
//        String save = null;
//        try {
//            save = k3CloudApi.save(formId, json);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        log.info("spTjJdDj====*********{}", JSON.toJSONString(save));
//        JSONObject jsonObject2 = JSONObject.parseObject(save);
//        JSONObject result = jsonObject2.getJSONObject("Result");
//        if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
//            return true;
//        }
//        return false;
//    }

    /**
     * 审批提交单据
     *
     * @param json 提交的数据
     * @param lx   审批类型
     */
    public static Map spTjJdDj2(String json, String lx) {
        log.info("spTjJdDj==lxlxlxlx==*********{}", lx);
        Map<String, String> map = new HashMap<>();
        String formId = "";
        if ("预支".equals(lx) || "费用付款".equals(lx) || "资金申请".equals(lx) || "威廉币提现".equals(lx) || "退宿舍押金".equals(lx) || "借支审批".equals(lx) || "付款单".equals(lx)) {
            formId = "AP_PAYBILL";
        } else if ("付款退款单".equals(lx)) {
            formId = "AP_REFUNDBILL";
        } else if ("报销".equals(lx) || "退定金-客户返现".equals(lx) || "其他应付单".equals(lx)) {
            formId = "AP_OtherPayable";
        }
        if ("".equals(formId)) {
            map.put("jg", "false");
            map.put("msg", "不存在的单据类型");
            return map;
        }
        K3CloudApi k3CloudApi = new K3CloudApi();

        String save = null;
        try {
            save = k3CloudApi.save(formId, json);
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("spTjJdDj====*********{}", JSON.toJSONString(save));
        JSONObject jsonObject2 = JSONObject.parseObject(save);
        JSONObject result = jsonObject2.getJSONObject("Result");
        if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
            map.put("jg", "true");
            map.put("msg", "上传成功");
            return map;
        }
        JSONArray arr = result.getJSONObject("ResponseStatus").getJSONArray("Errors");
        JSONObject jj = (JSONObject) arr.get(0);
        map.put("jg", "false");
        map.put("msg", jj.getString("Message"));
        return map;
    }

    /**
     * 保存收款单
     *
     * @param ysbh 银盛编号
     * @param je   金额
     * @param ddbh 订单编号
     * @param qm   券码
     * @param ly   来源
     */
    public static Boolean saveSkd3(String ysbh, Double je, String ddbh, String qm, String ly, Date dzsj, String yhk) {
        Boolean b = false;

        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "AR_RECEIVEBILL";

        //请求数据对象
        JSONObject data = new JSONObject();
        //需要更新的字段，数组类型，格式：[key1,key2,...] （非必录）注（更新单据体字段得加上单据体key）
        data.put("NeedUpDateFields", new ArrayList<>());
        //需返回结果的字段集合，数组类型，格式：[key,entitykey.key,...]（非必录） 注（返回单据体字段格式：entitykey.key）
        data.put("NeedReturnFields", new ArrayList<>());
        //是否删除已存在的分录，布尔类型，默认true（非必录）
        data.put("IsDeleteEntry", true);
        //表单所在的子系统内码，字符串类型（非必录）
        data.put("SubSystemId", "");
        //是否验证所有的基础资料有效性，布尔类，默认false（非必录）
        data.put("IsVerifyBaseDataField", false);
        //是否批量填充分录，默认true（非必录）
        data.put("IsEntryBatchFill", true);
        //是否验证标志，布尔类型，默认true（非必录）
        data.put("ValidateFlag", true);
        //是否用编码搜索基础资料，布尔类型，默认true（非必录）
        data.put("NumberSearch", true);
        //交互标志集合，字符串类型，分号分隔，格式："flag1;flag2;..."（非必录） 例如（允许负库存标识：STK_InvCheckResult）
        data.put("InterationFlags", "");

        //请求子数据
        JSONObject data_1 = new JSONObject();
        data_1.put("FID", 0);

        //单据类型子数据
        JSONObject data_1_1 = new JSONObject();
        data_1_1.put("FNUMBER", "SKDLX06_SYS");

        //单据类型
        data_1.put("FBillTypeID", data_1_1);
        //业务日期
        data_1.put("FDATE", df2.format(dzsj));
        //往来单位类型
        data_1.put("FCONTACTUNITTYPE", "BD_Customer");

        //往来单位
        data_1.put("FCONTACTUNIT", zhdx("CUST0004"));
        //付款单位类型
        data_1.put("FPAYUNITTYPE", "BD_Customer");

        //付款单位
        data_1.put("FPAYUNIT", zhdx("CUST0004"));

        //币别
        data_1.put("FCURRENCYID", zhdx("PRE001"));
        //收款组织
        data_1.put("FPAYORGID", zhdx("102"));
        //结算汇率
        data_1.put("FSETTLERATE", 1.0);
        //结算组织
        data_1.put("FSETTLEORGID", zhdx("102"));
        //单据状态
        data_1.put("FDOCUMENTSTATUS", "Z");
        //是否期初单据
        data_1.put("FISINIT", false);
        //汇率
        data_1.put("FEXCHANGERATE", 1.0);
        //作废状态
        data_1.put("FCancelStatus", "A");

        //B2C业务
        data_1.put("FISB2C", false);
        //是否转销
        data_1.put("FIsWriteOff", false);
        //核销方式
        data_1.put("FMatchMethodID", 0);
        //是否下推携带汇率到结算汇率
        data_1.put("FISCARRYRATE", false);

        //银盛编号
        data_1.put("F_YS_Text", ysbh);
        //订单编号
        data_1.put("F_DD_Text", ddbh);
        //券码
        data_1.put("F_abc_Remarks", qm);
        //平台类型
        data_1.put("F_abc_Text1", ly);

        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FEntryID", 0);

        //银盛编号
        jsonObject.put("F_YSLSH_Text", ysbh);
        //订单编号
        jsonObject.put("F_XDDBH_Text1", ddbh);
        //结算方式
        //现金
//        jsonObject.put("FSETTLETYPEID", zhdx("JSFS01_SYS"));
        //电汇
        jsonObject.put("FSETTLETYPEID", zhdx("JSFS04_SYS"));
        //银行卡
        jsonObject.put("FACCOUNTID", zhdx(yhk));
        //收款用途
        jsonObject.put("FPURPOSEID", zhdx("SFKYT41_SYS"));
        //预收销售订单号内码
        jsonObject.put("FSaleOrderID", 0);
        //表体-应收金额
        jsonObject.put("FRECTOTALAMOUNTFOR", je);
        //收款金额
        jsonObject.put("FRECAMOUNTFOR_E", je);
        //现金折扣
        jsonObject.put("FSETTLEDISTAMOUNTFOR", 0);
        //手续费
        jsonObject.put("FHANDLINGCHARGEFOR", 0);
        //长短款
        jsonObject.put("FOVERUNDERAMOUNTFOR", 0);
        //费用项目
        if (!"美团".equals(ly) && !"短租".equals(ly)) {
            jsonObject.put("FCOSTID", zhdx("1122.01.01"));
        } else {
            jsonObject.put("FCOSTID", zhdx("1122.01.02"));
        }


        list.add(jsonObject);

        data_1.put("FRECEIVEBILLENTRY", list);

        //表单数据包，JSON类型（必录）
        data.put("Model", data_1);
        String save = "";
        try {
            save = k3CloudApi.save(formId, data.toJSONString());
            System.out.println(save);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String number = "";
        JSONObject jsonObject2 = JSONObject.parseObject(save);
        if (jsonObject2 != null) {
            JSONObject result = jsonObject2.getJSONObject("Result");
            if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
                b = result.getJSONObject("ResponseStatus").getBoolean("IsSuccess");
                number = result.getString("Number");
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("CreateOrgId", 0);
                List<String> list2 = new ArrayList<>();
                list2.add(number);
                jsonObject1.put("Numbers", list2);
                try {
                    String submit = k3CloudApi.submit(formId, jsonObject1.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    String audit = k3CloudApi.audit(formId, jsonObject1.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return b;
    }

    /**
     * 保存收款单
     *
     * @param ysbh 银盛编号
     * @param je   金额
     * @param ddbh 订单编号
     * @param qm   券码
     * @param ly   来源
     */
    public static Boolean saveSkd4(String ysbh, Double je, String ddbh, String qm, String ly, Date dzsj, String bm, String mdlx) {
        Boolean b = false;

        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "AR_RECEIVEBILL";

        //请求数据对象
        JSONObject data = new JSONObject();
        //需要更新的字段，数组类型，格式：[key1,key2,...] （非必录）注（更新单据体字段得加上单据体key）
        data.put("NeedUpDateFields", new ArrayList<>());
        //需返回结果的字段集合，数组类型，格式：[key,entitykey.key,...]（非必录） 注（返回单据体字段格式：entitykey.key）
        data.put("NeedReturnFields", new ArrayList<>());
        //是否删除已存在的分录，布尔类型，默认true（非必录）
        data.put("IsDeleteEntry", true);
        //表单所在的子系统内码，字符串类型（非必录）
        data.put("SubSystemId", "");
        //是否验证所有的基础资料有效性，布尔类，默认false（非必录）
        data.put("IsVerifyBaseDataField", false);
        //是否批量填充分录，默认true（非必录）
        data.put("IsEntryBatchFill", true);
        //是否验证标志，布尔类型，默认true（非必录）
        data.put("ValidateFlag", true);
        //是否用编码搜索基础资料，布尔类型，默认true（非必录）
        data.put("NumberSearch", true);
        //交互标志集合，字符串类型，分号分隔，格式："flag1;flag2;..."（非必录） 例如（允许负库存标识：STK_InvCheckResult）
        data.put("InterationFlags", "");

        //请求子数据
        JSONObject data_1 = new JSONObject();
        data_1.put("FID", 0);

        //单据类型子数据
        JSONObject data_1_1 = new JSONObject();
        data_1_1.put("FNUMBER", "SKDLX06_SYS");

        //单据类型
        data_1.put("FBillTypeID", data_1_1);
        //业务日期
        data_1.put("FDATE", df2.format(dzsj));
        //往来单位类型
        data_1.put("FCONTACTUNITTYPE", "BD_Customer");

        //往来单位
        data_1.put("FCONTACTUNIT", zhdx("CUST0004"));
        //付款单位类型
        data_1.put("FPAYUNITTYPE", "BD_Customer");

        //付款单位
        data_1.put("FPAYUNIT", zhdx("CUST0004"));

        //币别
        data_1.put("FCURRENCYID", zhdx("PRE001"));
        //收款组织
        data_1.put("FPAYORGID", zhdx(mdlx));
        //结算汇率
        data_1.put("FSETTLERATE", 1.0);
        //结算组织
        data_1.put("FSETTLEORGID", zhdx(mdlx));
        //单据状态
        data_1.put("FDOCUMENTSTATUS", "Z");
        //是否期初单据
        data_1.put("FISINIT", false);
        //汇率
        data_1.put("FEXCHANGERATE", 1.0);
        //作废状态
        data_1.put("FCancelStatus", "A");

        //B2C业务
        data_1.put("FISB2C", false);
        //是否转销
        data_1.put("FIsWriteOff", false);
        //核销方式
        data_1.put("FMatchMethodID", 0);
        //是否下推携带汇率到结算汇率
        data_1.put("FISCARRYRATE", false);

        //银盛编号
        data_1.put("F_YS_Text", ysbh);
        //订单编号
//        data_1.put("F_DD_Text", ddbh);
        data_1.put("F_ABC_TEXT3", ddbh);
        //券码
        data_1.put("F_abc_Remarks", qm);
        //平台类型
        data_1.put("F_abc_Text1", ly);

        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FEntryID", 0);

        //银盛编号
        jsonObject.put("F_YSLSH_Text", ysbh);
        //订单编号
//        jsonObject.put("F_XDDBH_Text1", ddbh);
        jsonObject.put("F_abc_Text2", ddbh);
        //结算方式
        jsonObject.put("FSETTLETYPEID", zhdx("JSFS01_SYS"));
        //收款用途
        jsonObject.put("FPURPOSEID", zhdx("SFKYT07_SYS"));
        if (bm != null && !"".equals(bm)) {
            //门店
            jsonObject.put("F_abc_Base1", zhdx(bm));
        }
        //预收销售订单号内码
        jsonObject.put("FSaleOrderID", 0);
        //表体-应收金额
        jsonObject.put("FRECTOTALAMOUNTFOR", je);
        //收款金额
        jsonObject.put("FRECAMOUNTFOR_E", je);
        //现金折扣
        jsonObject.put("FSETTLEDISTAMOUNTFOR", 0);
        //手续费
        jsonObject.put("FHANDLINGCHARGEFOR", 0);
        //长短款
        jsonObject.put("FOVERUNDERAMOUNTFOR", 0);
        //费用项目
        jsonObject.put("FCOSTID", zhdx("1122.01.02"));


        list.add(jsonObject);

        data_1.put("FRECEIVEBILLENTRY", list);

        //表单数据包，JSON类型（必录）
        data.put("Model", data_1);
        String save = "";
        try {
            save = k3CloudApi.save(formId, data.toJSONString());
            System.out.println(save);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String number = "";
        JSONObject jsonObject2 = JSONObject.parseObject(save);
        if (jsonObject2 != null) {
            JSONObject result = jsonObject2.getJSONObject("Result");
            if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
                b = result.getJSONObject("ResponseStatus").getBoolean("IsSuccess");
                number = result.getString("Number");
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("CreateOrgId", 0);
                List<String> list2 = new ArrayList<>();
                list2.add(number);
                jsonObject1.put("Numbers", list2);
                try {
                    String submit = k3CloudApi.submit(formId, jsonObject1.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    String audit = k3CloudApi.audit(formId, jsonObject1.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return b;
    }

    /**
     * 保存收款单
     *
     * @param je   金额
     * @param ddbh 订单编号
     * @param qm   券码
     * @param ly   来源
     */
    public static Boolean saveSkd5(Double je, String ddbh, String qm, String ly, Date dzsj, String bm, String mdlx, String yhk) {
        Boolean b = false;

        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "AR_RECEIVEBILL";

        String json = "{\n" +
                "    \"NeedUpDateFields\": [],\n" +
                "    \"NeedReturnFields\": [],\n" +
                "    \"IsDeleteEntry\": \"true\",\n" +
                "    \"SubSystemId\": \"\",\n" +
                "    \"IsVerifyBaseDataField\": \"false\",\n" +
                "    \"IsEntryBatchFill\": \"true\",\n" +
                "    \"ValidateFlag\": \"true\",\n" +
                "    \"NumberSearch\": \"true\",\n" +
                "    \"IsAutoAdjustField\": \"false\",\n" +
                "    \"InterationFlags\": \"\",\n" +
                "    \"IgnoreInterationFlag\": \"\",\n" +
                "    \"Model\": {\n" +
                "        \"FID\": 0,\n" +
                "        \"FBillTypeID\": {\n" +
                "            \"FNUMBER\": \"SKDLX06_SYS\"\n" +
                "        },\n" +
                "        \"FDATE\": \"" + df2.format(dzsj) + "\",\n" +
                "        \"FCONTACTUNITTYPE\": \"BD_Customer\",\n" +
                "        \"FCONTACTUNIT\": {\n" +
                "            \"FNumber\": \"CUST0004\"\n" +
                "        },\n" +
                "        \"FPAYUNITTYPE\": \"BD_Customer\",\n" +
                "        \"FPAYUNIT\": {\n" +
                "            \"FNumber\": \"CUST0004\"\n" +
                "        },\n" +
                "        \"FCURRENCYID\": {\n" +
                "            \"FNumber\": \"PRE001\"\n" +
                "        },\n" +
                "        \"FPAYORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FSETTLERATE\": 1.0,\n" +
                "        \"FSETTLEORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FSALEORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                "        \"FBUSINESSTYPE\": \"3\",\n" +
                "        \"FISINIT\": false,\n" +
                "        \"FEXCHANGERATE\": 1.0,\n" +
                "        \"FCancelStatus\": \"A\",\n" +
                "        \"FSETTLECUR\": {\n" +
                "            \"FNUMBER\": \"PRE001\"\n" +
                "        },\n" +
                "        \"FISB2C\": false,\n" +
                "        \"FIsWriteOff\": false,\n" +
                "        \"FSETTLEMAINBOOKID\": {\n" +
                "            \"FNUMBER\": \"PRE001\"\n" +
                "        },\n" +
                "        \"FISCARRYRATE\": false,\n" +
                "        \"FVirIsSameAcctOrg\": false,\n" +
                "        \"F_abc_Text1\": \"" + ly + "\",\n" +
                "        \"F_abc_Remarks\": \"" + qm + "\",\n" +
                "        \"F_ABC_TEXT3\": \"" + ddbh + "\",\n" +
                "        \"FRECEIVEBILLENTRY\": [\n" +
                "            {\n" +
                "                \"F_abc_Text2\": \"" + ddbh + "\",\n" +
                "                \"F_abc_Base1\": {\n" +
                "                    \"FNUMBER\": \"" + bm + "\"\n" +
                "                },\n" +
                "                \"FSETTLETYPEID\": {\n" +
                "                    \"FNumber\": \"JSFS04_SYS\"\n" +
                "                },\n" +
                "                \"FPURPOSEID\": {\n" +
                "                    \"FNumber\": \"SFKYT001_SYS\"\n" +
                "                },\n" +
                "                \"FACCOUNTID\": {\n" +
                "                    \"FNumber\": \"" + yhk + "\"\n" +
                "                },\n" +
                "                \"FRECTOTALAMOUNTFOR\": " + je + ",\n" +
                "                \"FRECAMOUNTFOR_E\": " + je + ",\n" +
                "                \"FRECAMOUNT_E\": " + je + ",\n" +
                "                \"FPOSTDATE\": \"" + df2.format(dzsj) + "\",\n" +
                "                \"FNOTVERIFICATEAMOUNT\": " + je + ",\n" +
                "                \"FCOSTID\": {\n" +
                "                    \"FNUMBER\": \"1122.02\"\n" +
                "                }\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        String save = "";
        try {
            save = k3CloudApi.save(formId, json);
            System.out.println(save);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String number = "";
        JSONObject jsonObject2 = JSONObject.parseObject(save);
        if (jsonObject2 != null) {
            JSONObject result = jsonObject2.getJSONObject("Result");
            if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
                b = result.getJSONObject("ResponseStatus").getBoolean("IsSuccess");
                number = result.getString("Number");
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("CreateOrgId", 0);
                List<String> list2 = new ArrayList<>();
                list2.add(number);
                jsonObject1.put("Numbers", list2);
                try {
                    String submit = k3CloudApi.submit(formId, jsonObject1.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    String audit = k3CloudApi.audit(formId, jsonObject1.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return b;
    }

    /**
     * 保存收款单
     *
     * @param ysbh 银盛编号
     * @param je   金额
     * @param ddbh 订单编号
     * @param qm   券码
     * @param ly   来源
     */
    public static Boolean saveSkd6(String ysbh, Double je, String ddbh, String qm, String ly, Date dzsj, String bm, String mdlx, String yhk) {
        Boolean b = false;

        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "AR_RECEIVEBILL";

        String json = "{\n" +
                "    \"NeedUpDateFields\": [],\n" +
                "    \"NeedReturnFields\": [],\n" +
                "    \"IsDeleteEntry\": \"true\",\n" +
                "    \"SubSystemId\": \"\",\n" +
                "    \"IsVerifyBaseDataField\": \"false\",\n" +
                "    \"IsEntryBatchFill\": \"true\",\n" +
                "    \"ValidateFlag\": \"true\",\n" +
                "    \"NumberSearch\": \"true\",\n" +
                "    \"IsAutoAdjustField\": \"false\",\n" +
                "    \"InterationFlags\": \"\",\n" +
                "    \"IgnoreInterationFlag\": \"\",\n" +
                "    \"Model\": {\n" +
                "        \"FID\": 0,\n" +
                "        \"FBillTypeID\": {\n" +
                "            \"FNUMBER\": \"SKDLX06_SYS\"\n" +
                "        },\n" +
                "        \"FDATE\": \"" + df2.format(dzsj) + "\",\n" +
                "        \"FCONTACTUNITTYPE\": \"BD_Customer\",\n" +
                "        \"FCONTACTUNIT\": {\n" +
                "            \"FNumber\": \"CUST0004\"\n" +
                "        },\n" +
                "        \"FPAYUNITTYPE\": \"BD_Customer\",\n" +
                "        \"FPAYUNIT\": {\n" +
                "            \"FNumber\": \"CUST0004\"\n" +
                "        },\n" +
                "        \"FCURRENCYID\": {\n" +
                "            \"FNumber\": \"PRE001\"\n" +
                "        },\n" +
                "        \"FPAYORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FSETTLERATE\": 1.0,\n" +
                "        \"FSETTLEORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FSALEORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                "        \"FBUSINESSTYPE\": \"3\",\n" +
                "        \"FISINIT\": false,\n" +
                "        \"FEXCHANGERATE\": 1.0,\n" +
                "        \"FCancelStatus\": \"A\",\n" +
                "        \"FSETTLECUR\": {\n" +
                "            \"FNUMBER\": \"PRE001\"\n" +
                "        },\n" +
                "        \"FISB2C\": false,\n" +
                "        \"FIsWriteOff\": false,\n" +
                "        \"FSETTLEMAINBOOKID\": {\n" +
                "            \"FNUMBER\": \"PRE001\"\n" +
                "        },\n" +
                "        \"FISCARRYRATE\": false,\n" +
                "        \"FVirIsSameAcctOrg\": false,\n" +
                "        \"F_abc_Text1\": \"" + ly + "\",\n" +
                "        \"F_abc_Remarks\": \"" + qm + "\",\n" +
                "        \"F_YS_Text\": \"" + ysbh + "\",\n" +
                "        \"F_DD_Text\": \"" + ddbh + "\",\n" +
                "        \"FRECEIVEBILLENTRY\": [\n" +
                "            {\n" +
                "                \"F_YSLSH_Text\": \"" + ysbh + "\",\n" +
                "                \"F_XDDBH_Text1\": \"" + ddbh + "\",\n" +
                "                \"F_abc_Base1\": {\n" +
                "                    \"FNUMBER\": \"" + bm + "\"\n" +
                "                },\n" +
                "                \"FSETTLETYPEID\": {\n" +
                "                    \"FNumber\": \"JSFS04_SYS\"\n" +
                "                },\n" +
                "                \"FPURPOSEID\": {\n" +
                "                    \"FNumber\": \"SFKYT001_SYS\"\n" +
                "                },\n" +
                "                \"FACCOUNTID\": {\n" +
                "                    \"FNumber\": \"" + yhk + "\"\n" +
                "                },\n" +
                "                \"FRECTOTALAMOUNTFOR\": " + je + ",\n" +
                "                \"FRECAMOUNTFOR_E\": " + je + ",\n" +
                "                \"FRECAMOUNT_E\": " + je + ",\n" +
                "                \"FPOSTDATE\": \"" + df2.format(dzsj) + "\",\n" +
                "                \"FNOTVERIFICATEAMOUNT\": " + je + ",\n" +
                "                \"FCOSTID\": {\n" +
                "                    \"FNUMBER\": \"1122.02\"\n" +
                "                }\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        String save = "";
        try {
            save = k3CloudApi.save(formId, json);
            System.out.println(save);
        } catch (Exception e) {
            e.printStackTrace();
        }
        String number = "";
        JSONObject jsonObject2 = JSONObject.parseObject(save);
        if (jsonObject2 != null) {
            JSONObject result = jsonObject2.getJSONObject("Result");
            if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
                b = result.getJSONObject("ResponseStatus").getBoolean("IsSuccess");
                number = result.getString("Number");
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("CreateOrgId", 0);
                List<String> list2 = new ArrayList<>();
                list2.add(number);
                jsonObject1.put("Numbers", list2);
                try {
                    String submit = k3CloudApi.submit(formId, jsonObject1.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    String audit = k3CloudApi.audit(formId, jsonObject1.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return b;
    }

    /**
     * 部门处理
     *
     * @param number 部门id
     * @param bmmc   部门名称
     */
    public static Boolean bmCl(String number, String bmmc) {
        Boolean b = false;
        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "BD_Department";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("CreateOrgId", "0");
        jsonObject.put("Number", number);
        jsonObject.put("Id", "");
        try {
            //查询number部门在金蝶是否存在？
            String view = k3CloudApi.view(formId, jsonObject.toJSONString());
            log.info("*******往金蝶新增部门数据===******{}", view);
            JSONObject jsonObject1 = JSONObject.parseObject(view);
            JSONObject result = jsonObject1.getJSONObject("Result");
            if (!result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
                //进去这里说明部门不存在，接下来就准备新增部门了
                SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
                String json = "{\n" +
                        "    \"NeedUpDateFields\": [],\n" +
                        "    \"NeedReturnFields\": [],\n" +
                        "    \"IsDeleteEntry\": \"true\",\n" +
                        "    \"SubSystemId\": \"\",\n" +
                        "    \"IsVerifyBaseDataField\": \"false\",\n" +
                        "    \"IsEntryBatchFill\": \"true\",\n" +
                        "    \"ValidateFlag\": \"true\",\n" +
                        "    \"NumberSearch\": \"true\",\n" +
                        "    \"IsAutoAdjustField\": \"false\",\n" +
                        "    \"InterationFlags\": \"\",\n" +
                        "    \"IgnoreInterationFlag\": \"\",\n" +
                        "    \"Model\": {\n" +
                        "        \"FDEPTID\": 0,\n" +
                        "        \"FNumber\": \"" + number + "\",\n" +
                        "        \"FCreateOrgId\": {\n" +
                        "            \"FNumber\": \"100\"\n" +
                        "        },\n" +
                        "        \"FUseOrgId\": {\n" +
                        "            \"FNumber\": \"100\"\n" +
                        "        },\n" +
                        "        \"FName\": \"" + bmmc + "_" + number + "\",\n" +
                        "        \"FEffectDate\": \"" + df2.format(new Date()) + "\",\n" +
                        "        \"FLapseDate\": \"9999-12-31 00:00:00\",\n" +
                        "        \"FDeptProperty\": {\n" +
                        "            \"FNumber\": \"DP02_SYS\"\n" +
                        "        },\n" +
                        "        \"FIsCopyFlush\": false,\n" +
                        "        \"FSHRMapEntity\": {}\n" +
                        "    }\n" +
                        "}";
                //保存
                String save = k3CloudApi.save(formId, json);
                JSONObject jsonObject2 = JSONObject.parseObject(save);
                JSONObject result1 = jsonObject2.getJSONObject("Result");
                if (result1.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
                    b = true;
                    JSONObject jsonObject3 = new JSONObject();
                    jsonObject3.put("CreateOrgId", 0);
                    List<String> list2 = new ArrayList<>();
                    list2.add(number);
                    jsonObject3.put("Numbers", list2);
                    try {
                        //提交
                        String submit = k3CloudApi.submit(formId, jsonObject3.toJSONString());
                        System.out.println(submit);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        //审核
                        String audit = k3CloudApi.audit(formId, jsonObject3.toJSONString());
                        System.out.println(audit);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } else {
                b = true;
                System.out.println("已存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return b;
    }

    /**
     * 人员处理
     *
     * @param number 部门id
     * @param name   部门名称
     */
    public static Boolean ryCl(String number, String name) {
        Boolean b = false;
        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "BD_Empinfo";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("CreateOrgId", "0");
        jsonObject.put("Number", number);
        jsonObject.put("Id", "");
        try {
            String view = k3CloudApi.view(formId, jsonObject.toJSONString());
            JSONObject jsonObject1 = JSONObject.parseObject(view);
            JSONObject result = jsonObject1.getJSONObject("Result");
            if (!result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
                SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
                String json = "{\n" +
                        "    \"NeedUpDateFields\": [],\n" +
                        "    \"NeedReturnFields\": [],\n" +
                        "    \"IsDeleteEntry\": \"true\",\n" +
                        "    \"SubSystemId\": \"\",\n" +
                        "    \"IsVerifyBaseDataField\": \"false\",\n" +
                        "    \"IsEntryBatchFill\": \"true\",\n" +
                        "    \"ValidateFlag\": \"true\",\n" +
                        "    \"NumberSearch\": \"true\",\n" +
                        "    \"IsAutoAdjustField\": \"false\",\n" +
                        "    \"InterationFlags\": \"\",\n" +
                        "    \"IgnoreInterationFlag\": \"\",\n" +
                        "    \"Model\": {\n" +
                        "        \"FID\": 0,\n" +
                        "        \"FName\": \"" + name + "\",\n" +
                        "        \"FStaffNumber\": \"" + number + "\",\n" +
                        "        \"FUseOrgId\": {\n" +
                        "            \"FNumber\": \"100\"\n" +
                        "        },\n" +
                        "        \"FCreateOrgId\": {\n" +
                        "            \"FNumber\": \"100\"\n" +
                        "        },\n" +
                        "        \"FCreateSaler\": false,\n" +
                        "        \"FCreateUser\": false,\n" +
                        "        \"FCreateCashier\": false,\n" +
                        "        \"FJoinDate\": \"" + df2.format(new Date()) + "\",\n" +
                        "        \"FSHRMapEntity\": {}\n" +
                        "    }\n" +
                        "}";
                String save = k3CloudApi.save(formId, json);
                JSONObject jsonObject2 = JSONObject.parseObject(save);
                JSONObject result1 = jsonObject2.getJSONObject("Result");
                if (result1.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
                    b = true;
                    JSONObject jsonObject3 = new JSONObject();
                    jsonObject3.put("CreateOrgId", 0);
                    List<String> list2 = new ArrayList<>();
                    list2.add(number);
                    jsonObject3.put("Numbers", list2);
                    try {
                        String submit = k3CloudApi.submit(formId, jsonObject3.toJSONString());
                        System.out.println(submit);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        String audit = k3CloudApi.audit(formId, jsonObject3.toJSONString());
                        System.out.println(audit);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } else {
                b = true;
                System.out.println("已存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return b;
    }

    /**
     * 查询别墅是否存在
     *
     * @param bsmc 别墅名称
     */
    public static JSONObject queryBsSfCz(String bsmc) {
        K3CloudApi k3CloudApi = new K3CloudApi();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FormId", "BAS_PreBaseDataOne");
        jsonObject.put("FieldKeys", "FNumber");
        jsonObject.put("FilterString", "FName like '%" + bsmc + "%'");
        jsonObject.put("TopRowCount", 1);
        jsonObject.put("StartRow", 0);
        jsonObject.put("Limit", 0);

        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("success", false);
        try {
            List<List<Object>> view = k3CloudApi.executeBillQuery(jsonObject.toJSONString());
            if (view.size() > 0) {
                jsonObject1.put("success", true);
                jsonObject1.put("bm", view.get(0).get(0));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return jsonObject1;
    }

    /**
     * 上传场地费负数
     */
    public static boolean scQtYsd(JSONObject res, String mdlx, String mdbh) {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String json = "{\n" +
                "    \"NeedUpDateFields\": [],\n" +
                "    \"NeedReturnFields\": [],\n" +
                "    \"IsDeleteEntry\": \"true\",\n" +
                "    \"SubSystemId\": \"\",\n" +
                "    \"IsVerifyBaseDataField\": \"false\",\n" +
                "    \"IsEntryBatchFill\": \"true\",\n" +
                "    \"ValidateFlag\": \"true\",\n" +
                "    \"NumberSearch\": \"true\",\n" +
                "    \"IsAutoAdjustField\": \"false\",\n" +
                "    \"InterationFlags\": \"\",\n" +
                "    \"IgnoreInterationFlag\": \"\",\n" +
                "    \"Model\": {\n" +
                "        \"FID\": 0,\n" +
                "        \"FBillTypeID\": {\n" +
                "            \"FNUMBER\": \"QTYSD01_SYS\"\n" +
                "        },\n" +
                "        \"FDATE\": \"" + df2.format(res.getDate("dzrq")) + "\",\n" +
                "        \"FENDDATE_H\": \"" + df2.format(res.getDate("dzrq")) + "\",\n" +
                "        \"FISINIT\": false,\n" +
                "        \"FCONTACTUNITTYPE\": \"BD_Customer\",\n" +
                "        \"FCONTACTUNIT\": {\n" +
                "            \"FNumber\": \"CUST0006\"\n" +
                "        },\n" +
                "        \"FCURRENCYID\": {\n" +
                "            \"FNumber\": \"PRE001\"\n" +
                "        },\n" +
                "        \"FSETTLEORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FPAYORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FSALEORGID\": {\n" +
                "            \"FNumber\": \"" + mdlx + "\"\n" +
                "        },\n" +
                "        \"FACCNTTIMEJUDGETIME\": \"" + df2.format(res.getDate("dzrq")) + "\",\n" +
                "        \"FDDBH1\": \"" + res.getString("ddbh") + "\",\n" +
                "        \"FSettleTypeID\": {\n" +
                "            \"FNumber\": \"JSFS01_SYS\"\n" +
                "        },\n" +
                "        \"FMAINBOOKSTDCURRID\": {\n" +
                "            \"FNumber\": \"PRE001\"\n" +
                "        },\n" +
                "        \"FEXCHANGETYPE\": {\n" +
                "            \"FNumber\": \"HLTX01_SYS\"\n" +
                "        },\n" +
                "        \"FExchangeRate\": 1.0,\n" +
                "        \"FCancelStatus\": \"A\",\n" +
                "        \"F_PAEA_Datetime\": \"" + df2.format(res.getDate("dzrq")) + "\",\n";
        List<JSONObject> list2 = new ArrayList<>();
        JSONObject jsonObject4 = new JSONObject();
        jsonObject4.put("FCOSTID", KingDeeConfig.zhdx("6001.01"));
        jsonObject4.put("FDDBH", res.getString("ddbh"));
        jsonObject4.put("FMD", KingDeeConfig.zhdx(mdbh));
        jsonObject4.put("FNOTAXAMOUNTFOR", res.getDouble("yhhcdf"));
        jsonObject4.put("FAMOUNTFOR_D", res.getDouble("yhhcdf"));
        jsonObject4.put("FNOTAXAMOUNT_D", res.getDouble("yhhcdf"));
        jsonObject4.put("FCREATEINVOICE", false);

        list2.add(jsonObject4);

        json += "        \"FEntity\": " + list2.toString() +
                "    }\n" +
                "}";

        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "AR_OtherRecAble";
        String save = null;
        try {
            save = k3CloudApi.save(formId, json);
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject2 = JSONObject.parseObject(save);
        JSONObject result = jsonObject2.getJSONObject("Result");
        if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
            String number = result.getString("Number");
            JSONObject jsonObject3 = new JSONObject();
            jsonObject3.put("CreateOrgId", 0);
            List<String> list3 = new ArrayList<>();
            list3.add(number);
            jsonObject3.put("Numbers", list3);
            try {
                String submit = k3CloudApi.submit(formId, jsonObject3.toJSONString());
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                String audit = k3CloudApi.audit(formId, jsonObject3.toJSONString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return true;
    }

    /**
     * 保存门店信息（添加与修改，金蝶未提供修改接口，共用保存）
     */
    public static void saveMd(String bsmc, String bm, String xz, String fid) {
        String json = "{\n" +
                "    \"NeedUpDateFields\": [],\n" +
                "    \"NeedReturnFields\": [],\n" +
                "    \"IsDeleteEntry\": \"true\",\n" +
                "    \"SubSystemId\": \"\",\n" +
                "    \"IsVerifyBaseDataField\": \"false\",\n" +
                "    \"IsEntryBatchFill\": \"true\",\n" +
                "    \"ValidateFlag\": \"true\",\n" +
                "    \"NumberSearch\": \"true\",\n" +
                "    \"IsAutoAdjustField\": \"false\",\n" +
                "    \"InterationFlags\": \"\",\n" +
                "    \"IgnoreInterationFlag\": \"\",\n" +
                "    \"Model\": {\n" +
                "        \"FID\": " + fid + ",\n" +
                "        \"FNumber\": \"" + bm + "\",\n" +
                "        \"FCreateOrgId\": {\n" +
                "            \"FNumber\": \"100\"\n" +
                "        },\n" +
                "        \"FUseOrgId\": {\n" +
                "            \"FNumber\": \"100\"\n" +
                "        },\n" +
                "        \"FName\": \"" + bsmc + "\",\n" +
                "        \"FDescription\": \"" + xz + "\"\n" +
                "    }\n" +
                "}";

        K3CloudApi k3CloudApi = new K3CloudApi();
        String formId = "BAS_PreBaseDataOne";
        String save = null;
        try {
            save = k3CloudApi.save(formId, json);
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject2 = JSONObject.parseObject(save);
        JSONObject result = jsonObject2.getJSONObject("Result");
        if (result.getJSONObject("ResponseStatus").getBoolean("IsSuccess")) {
            String number = result.getString("Number");
            JSONObject jsonObject3 = new JSONObject();
            jsonObject3.put("CreateOrgId", 0);
            List<String> list3 = new ArrayList<>();
            list3.add(number);
            jsonObject3.put("Numbers", list3);
            try {
                String submit = k3CloudApi.submit(formId, jsonObject3.toJSONString());
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                String audit = k3CloudApi.audit(formId, jsonObject3.toJSONString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 获取生成别墅编码
     */
    public static String bsScBm(String cxBh) {
        K3CloudApi k3CloudApi = new K3CloudApi();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FormId", "BAS_PreBaseDataOne");
        jsonObject.put("FieldKeys", "FNumber");
        jsonObject.put("FilterString", "FNumber like '" + cxBh + "%'");
        jsonObject.put("StartRow", 0);
        jsonObject.put("Limit", 5000);
        int i = 1;
        String bm = cxBh + ("000" + i);
        try {
            List<List<Object>> view = k3CloudApi.executeBillQuery(jsonObject.toJSONString());
            if (view.size() > 0) {
                List<Object> l1 = view.get(view.size() - 1);
                String s = l1.get(0).toString();
                String substring = s.substring(2);
                i = Integer.parseInt(substring) + 1;
                bm = cxBh + (i < 10 ? "000" + i : i < 100 ? "00" + i : i < 1000 ? "0" + i : i + "");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return bm;
    }

    /**
     * 门店反审核
     */
    public static void mdFsh(String number) {
        K3CloudApi k3CloudApi = new K3CloudApi();
        JSONObject jsonObject3 = new JSONObject();
        jsonObject3.put("CreateOrgId", 0);
        List<String> list3 = new ArrayList<>();
        list3.add(number);
        jsonObject3.put("Numbers", list3);
        try {
            String submit = k3CloudApi.unAudit("BAS_PreBaseDataOne", jsonObject3.toJSONString());
            System.out.println(submit);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 修改门店信息
     */
    public static JSONObject queryKingDeeBsXx(String bsmc) {
        K3CloudApi k3CloudApi = new K3CloudApi();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FormId", "BAS_PreBaseDataOne");
        jsonObject.put("FieldKeys", "FNumber,FID");
        jsonObject.put("FilterString", "FName = '" + bsmc + "'");
        jsonObject.put("TopRowCount", 1);
        jsonObject.put("StartRow", 0);
        jsonObject.put("Limit", 0);

        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("success", false);
        try {
            List<List<Object>> view = k3CloudApi.executeBillQuery(jsonObject.toJSONString());
            if (view.size() > 0) {
                jsonObject1.put("success", true);
                jsonObject1.put("bm", view.get(0).get(0));
                jsonObject1.put("fid", view.get(0).get(1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonObject1;
    }


}
