package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "fwq_xdjc_dzmrlc_fsjl")
public class FwqXdjcDzmrlcFsjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    private String fsjg;
    private java.util.Date time;

    private String userid;
    private String username;
}
