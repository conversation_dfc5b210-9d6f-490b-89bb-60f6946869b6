package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.*;
import com.wlgb.service.*;
import com.wlgb.service2.*;
import com.wlgb.service3.DzxydService;
import com.wlgb.service3.WeiLianDaiBanService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.wlgb.config.Tools.isEmpty;

@RestController
@RequestMapping(value = "/wlgb/ydd/user")
public class NBUserController {
    @Autowired
    private TbYddNBUserService tbYddNBUserService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;

    /**
     * 将宜搭的内部员工表单保存到数据库中并回调给小程序
     */
    @RequestMapping(value = "/saveuser")
    public Result saveuser(HttpServletRequest request) {
        String name = request.getParameter("name");
        if (isEmpty(name)) {
            return Result.error("name空的");
        }
        String userid = request.getParameter("userid");
        if (isEmpty(userid)) {
            return Result.error("userid空的");
        }
        String sjh = request.getParameter("sjh");
        if (isEmpty(userid)) {
            return Result.error("sjh空的");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("name", name);
        paramMap.put("userid", userid);
        paramMap.put("sjh", sjh);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/ydd/user/saveuserTaSk", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    @RequestMapping(value = "saveuserTaSk")
    public Result saveuserTaSk(HttpServletRequest request) throws ApiException {
        String name = request.getParameter("name");
        String userid = request.getParameter("userid");
        String sjh = request.getParameter("sjh");
        TbYddNBUser tbYddNBUser = new TbYddNBUser();
        tbYddNBUser.setName(name);
        tbYddNBUser.setUserid(userid);
        tbYddNBUser.setSjh(sjh);
        TbYddNBUser tu2 = tbYddNBUserService.queryTbYddNBUserBySjh(userid);
        if (tu2 != null && tu2.getId() != null) {
            tu2.setName(name);
            tu2.setUserid(userid);
            tu2.setSjh(sjh);
            tbYddNBUserService.updateById(tu2);
        } else {
            tbYddNBUserService.save(tbYddNBUser);
        }
        //将内部员工信息发送给小程序
        String post = null;
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        try {
            Map<String, String> map = new HashMap<>();
            map.put("name", name);
            map.put("userid", userid);
            map.put("sjh", sjh);
            post = HttpClientUtil.post("https://zion-app.functorz.com/zero/PO76RBe4ZJK/callback/39528544-4a08-46f7-931f-128030d3f982", map);
            System.out.println("post====" + post);
        } catch (Exception e) {
            e.printStackTrace();
            try {
                String context1 = "将内部员工更新至小程序出错了，userid：" + userid + "，错误原因：" + e;
                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
            } catch (ApiException e1) {
                e1.printStackTrace();
            }
        }
        try {
            if (post != null && !"".equals(post)) {
                JSONObject jsonObject1 = JSONObject.parseObject(post);
                String jg = jsonObject1.getString("jg");
                if (!"ok".equalsIgnoreCase(jg)) {
                    String context1 = "将内部员工更新至小程序出错了，userid：" + userid + "，请求结果：" + post;
                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                }
            }
        } catch (Exception e) {
            String context1 = "定金到账将状态更新至小程序出错了，userid：" + userid + "，请求结果：" + post + "，错误原因：" + e;
            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
        }
        return Result.OK();
    }
}
