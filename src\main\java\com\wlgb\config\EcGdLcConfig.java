package com.wlgb.config;

import com.wlgb.entity.TbVilla;
import com.wlgb.entity.TbXyd;
import com.wlgb.entity.WlgbDksljl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/01/10 10:12
 */
public class EcGdLcConfig extends LcConfig{

    public static WlgbDksljl ecGdLc(TbXyd tbXyd1,
                                    TbVilla villa,
                                    Dingkey dingkey,
                                    String xydUrl,
                                    DingdingEmployee employee,
                                    Yd<PERSON><PERSON><PERSON> ydAppkey,
                                    YdBd ydBd,
                                    WlgbDksljl wlgbDksljl1){
        if(wlgbDksljl1 == null){
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Map<String, Object> formDatamap = new HashMap<>();
            //需要和数据库保持一致
            formDatamap.put("aid", tbXyd1.getXddbh());
            //性质（运营查数据）
            formDatamap.put("vxz", villa.getVxz());
            //当前流程的名字
            formDatamap.put("aname", tbXyd1.getXfd() + "发起了'二次跟单申请',进场:" + df2.format(tbXyd1.getXjctime()) + ",退场:" + df2.format(tbXyd1.getXtctime()) + ",别墅:" + villa.getVname() + ",请您立即处理");
            formDatamap.put("xzkdh", tbXyd1.getXzkdh());
            formDatamap.put("xzk", tbXyd1.getXzk());
            //进场时间
            formDatamap.put("jcsj", tbXyd1.getXjctime());
            //退场时间
            formDatamap.put("tcsj", tbXyd1.getXtctime());
            try {
                formDatamap.put("aimg", YdConfig.setTpList(xydUrl, "协议单图片"));
            } catch (Exception e) {
                e.printStackTrace();
            }
            formDatamap.put("xid", tbXyd1.getXid());
            formDatamap.put("bsmc", villa.getVname());
            formDatamap.put("bsid", villa.getVid());
            formDatamap.put("zbdz", villa.getPid());
            //城市
            String city = villa.getCszq() != null ? !"".equals(villa.getCszq()) ? villa.getCszq() : villa.getCity() : villa.getCity();
            formDatamap.put("city", city);
            //运营部门
            formDatamap.put("DzSsBm", villa.getVbsssbm() != null ? villa.getVbsssbm().replace("(", "").replace(")", "") : "");
            //流程执行人的ID
            formDatamap.put("ecgdzxr", villa.getPid());

            String ecgdzxr = employee != null ? employee.getUserid() : null;
            //流程执行人的ID
            formDatamap.put("ecgdzxr", ecgdzxr != null && !"".equals(ecgdzxr) ? ecgdzxr : villa.getPid());
            //发起人改成房东

            GatewayResult gatewayResult = queryYdLcXzRc(tbXyd1, "发起二次跟单申请", formDatamap, "012412221639786136545", dingkey, ydAppkey, ydBd);

            WlgbDksljl wlgbDksljl = new WlgbDksljl();
            wlgbDksljl.setId(IdConfig.uuId());
            wlgbDksljl.setTjsj(new Date());
            if (gatewayResult != null) {
                wlgbDksljl.setSlid(gatewayResult.getResult());
            }
            wlgbDksljl.setBz("二次跟单");
            wlgbDksljl.setJsr(villa.getPid());
            wlgbDksljl.setDdid(tbXyd1.getXid());
            wlgbDksljl.setBsid(tbXyd1.getXbsmc());

            return wlgbDksljl;
        }
        return null;
    }
}
