package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmCsfq;
import com.wlgb.mapper.CrmCsfqMapper;
import com.wlgb.service4.DhRjCrmCsfqService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 18:20
 */
@Service
@DS("fourth")
public class DhRjCrmCsfqServiceImpl implements DhRjCrmCsfqService {
    @Resource
    private CrmCsfqMapper crmCsfqMapper;

    @Override
    public void save(CrmCsfq crmCsfq) {
        crmCsfqMapper.insertSelective(crmCsfq);
    }

    @Override
    public void updateById(CrmCsfq crmCsfq) {
        crmCsfqMapper.updateByPrimaryKeySelective(crmCsfq);
    }

    @Override
    public CrmCsfq queryCrmCsFqByCrmCsFq(CrmCsfq crmCsfq) {
        return crmCsfqMapper.selectOne(crmCsfq);
    }
}
