package com.wlgb.service;

import com.wlgb.entity.WlgbJdWlbtxjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 22:50
 */
public interface WlgbJdWlbtxjlService {
    void save(WlgbJdWlbtxjl wlgbJdWlbtxjl);

    void updateById(WlgbJdWlbtxjl wlgbJdWlbtxjl);

    WlgbJdWlbtxjl queryBySpBhAndSfLrJd(String spbh, Integer sfLrJd);

    List<WlgbJdWlbtxjl> queryListByWlgbJdWlbtxjl(WlgbJdWlbtxjl wlgbJdWlbtxjl);
}
