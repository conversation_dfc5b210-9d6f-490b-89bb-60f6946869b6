package com.wlgb.service.impl;

import com.wlgb.entity.WlgbJdbTwo;
import com.wlgb.mapper.WlgbJdbTwoMapper;
import com.wlgb.service.WlgbJdbTwoService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

@Service
public class WlgbJdbTwoServiceImpl implements WlgbJdbTwoService {
    @Resource
    private WlgbJdbTwoMapper wlgbJdbTwoMapper;

    @Override
    public void save(WlgbJdbTwo wlgbJdbTwo) {
        wlgbJdbTwoMapper.insertSelective(wlgbJdbTwo);
    }

    @Override
    public void updateById(WlgbJdbTwo wlgbJdbTwo) {
        wlgbJdbTwoMapper.updateByPrimaryKeySelective(wlgbJdbTwo);
    }

    @Override
    public WlgbJdbTwo queryByXid(String xid) {
        Example example = new Example(WlgbJdbTwo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xid", xid);
        return wlgbJdbTwoMapper.selectOneByExample(example);
    }
}
