package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;

/**
 * @Description: 别墅表
 * @Author: jeecg-boot
 * @Date:   2020-10-15
 * @Version: V1.0
 */
@Data
public class TbVilla {

	/**别墅ID*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String vid;
	/**别墅名称*/
    private java.lang.String vname;
	/**备注*/
    private java.lang.String vremark;
	/**店长ID*/
    private java.lang.String pid;
	/**分区ID*/
    private java.lang.String fid;
	/**代号*/
    private java.lang.Integer vno;
	/**是否删除 1是 0否*/
    private java.lang.Integer vsfsc;
	/**集群id*/
    private java.lang.Integer jid;
	/**别墅所在城市*/
    private java.lang.String city;
	/**周六白场限价*/
    private java.lang.Integer zlbxj;
	/**周六晚场限价*/
    private java.lang.Integer zlwxj;
	/**周末全场限价*/
    private java.lang.Integer zmqcxj;
	/**周日白场限价*/
    private java.lang.Integer zrbxj;
	/**周五晚场限价*/
    private java.lang.Integer zwwxj;
	/**非周末限价*/
    private java.lang.Integer fzmxj;
	/**性质：如加盟或直营*/
    private java.lang.String vxz;
	/**城市总群*/
    private java.lang.String cszq;
	/**播报城市*/
    private java.lang.String bbcity;
	/**别墅档次*/
    private java.lang.String bsdc;
	/**开业时间*/
    private java.util.Date kytime;
	/**关店时间*/
    private java.util.Date gdtime;
	/**门店所属套账： 直营 或 加盟*/
    private java.lang.String vmdsstz;
	/**门店所属套账： 直营 或 加盟*/
    private java.lang.String vsfft;
	/**别墅所属部门:运营一部或运营二部*/
    private java.lang.String vbsssbm;
	/**备注*/
    private java.lang.String vbz;
	/**别墅区域*/
    private java.lang.String vbsqy;
	/**别墅餐饮区域*/
    private java.lang.String vcyqy;
	/**别墅餐饮区域编号*/
    private java.lang.String vcyqybh;
	/**城市总经理姓名*/
    private java.lang.String cszjlxm;
	/**城市总经理id*/
    private java.lang.String cszjlid;

}
