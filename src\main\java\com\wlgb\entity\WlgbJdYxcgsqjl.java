package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 一线采购申请记录
 * @Author: jeecg-boot
 * @Date:   2022-06-16
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_yxcgsqjl")
public class WlgbJdYxcgsqjl {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**审批编号*/
    private java.lang.String spbh;
	/**审批标题*/
    private java.lang.String spbt;
	/**申请人*/
    private java.lang.String sqr;
	/**申请人id*/
    private java.lang.String sqrid;
	/**申请人部门*/
    private java.lang.String sqrbm;
	/**申请人部门id*/
    private java.lang.String sqrbmid;
	/**付款方式*/
    private java.lang.String fkfs;
	/**别墅名称*/
    private java.lang.String bsmc;
	/**别墅编号*/
    private java.lang.String bsbh;
	/**备注*/
    private java.lang.String bz;
	/**出纳转账时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date cnzzsj;
	/**出纳转账金额*/
    private java.lang.Double cnzzje;
	/**是否录入金蝶*/
    private java.lang.Integer sflrjd;
	/**采购类型*/
    private java.lang.String cglx;
	/**餐饮区域编号*/
    private java.lang.String cyqybh;
}
