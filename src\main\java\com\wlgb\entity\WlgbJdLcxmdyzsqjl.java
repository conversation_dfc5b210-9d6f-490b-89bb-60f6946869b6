package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 乐诚项目抵预支申请记录
 * @Author: jeecg-boot
 * @Date:   2022-08-01
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_lcxmdyzsqjl")
public class WlgbJdLcxmdyzsqjl {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**审批编号*/
    private java.lang.String spbh;
	/**审批标题*/
    private java.lang.String spbt;
	/**申请时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date sqsj;
	/**审批结束时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date spjssj;
	/**申请人*/
    private java.lang.String sqr;
	/**申请人id*/
    private java.lang.String sqrid;
	/**申请人部门*/
    private java.lang.String sqrbm;
	/**申请人部门id*/
    private java.lang.String sqrbmid;
	/**门店类型*/
    private java.lang.String mdlx;
	/**费用发生项目名称*/
    private java.lang.String fyfsxmmc;
	/**预计回票时间*/
    private java.lang.String yjhfsj;
	/**抵预支金额*/
    private java.lang.Double dyzje;
	/**是否录入金蝶*/
    private java.lang.Integer sflrjd;
	/**审批实例id*/
    private java.lang.String slid;
}
