package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: crm_ryxx
 * @Author: jeecg-boot
 * @Date:   2021-09-25
 * @Version: V1.0
 */
@Data
@Table(name = "crm_ryxx")
public class CrmRyxx {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
	/**钉钉id*/
    private java.lang.String userid;
	/**姓名*/
    private java.lang.String name;
	/**电话*/
    private java.lang.String mobile;
	/**工号*/
    private java.lang.String jobnumber;
	/**岗位*/
    private java.lang.String gw;
	/**入职时间*/
    private java.lang.String rzsj;
	/**部门*/
    private java.lang.String bm;
	/**部门id*/
    private java.lang.String bmid;
	/**办公地点*/
    private java.lang.String bgdd;
}
