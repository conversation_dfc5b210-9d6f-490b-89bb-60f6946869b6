package com.wlgb.service.impl;

import com.wlgb.entity.WlgbDksljl;
import com.wlgb.mapper.WlgbDksljlMapper;
import com.wlgb.service.WlgbDksljlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月17日 15:49
 */
@Service
@Slf4j
public class WlgbDksljlServiceImpl implements WlgbDksljlService {
    @Resource
    private WlgbDksljlMapper wlgbDksljlMapper;

    @Override
    public Integer queryCountByBzAndXid(String bz, String xid) {
        Example example = new Example(WlgbDksljl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bz", bz);
        criteria.andEqualTo("ddid", xid);
        return wlgbDksljlMapper.selectCountByExample(example);
    }
    @Override
    public WlgbDksljl queryByBzAndXid(String bz, String xid) {
        Example example = new Example(WlgbDksljl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bz", bz);
        criteria.andEqualTo("ddid", xid);
        return wlgbDksljlMapper.selectOneByExample(example);
    }

    @Override
    public void save(WlgbDksljl wlgbDksljl) {
        wlgbDksljl.setCreateTime(new Date());
        boolean cfTest = false;
        try {
            wlgbDksljlMapper.insertSelective(wlgbDksljl);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbDksljlMapper.insertSelective(wlgbDksljl);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，insert：参数" + wlgbDksljl.toString());
                    }
                }
            }
        }
    }

    @Override
    public List<WlgbDksljl> queryByXid(String xid) {
        Example example = new Example(WlgbDksljl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddid", xid);
        return wlgbDksljlMapper.selectByExample(example);
    }

    @Override
    public void removeById(String id) {
        boolean cfTest = false;
        try {
            wlgbDksljlMapper.deleteByPrimaryKey(id);
        } catch (Exception e) {
            e.printStackTrace();
            cfTest = true;
        }
        if (cfTest) {
            for (int i = 0; i < 5; i++) {
                boolean cfJs = true;
                try {
                    wlgbDksljlMapper.deleteByPrimaryKey(id);
                } catch (Exception e) {
                    e.printStackTrace();
                    cfJs = false;
                }
                if (cfJs) {
                    break;
                } else {
                    if (i == 4) {
                        log.info("**************重复四次还是失败**************，delete：参数表wlgb_dksljl,id:" + id);
                    }
                }
            }
        }
    }
}
