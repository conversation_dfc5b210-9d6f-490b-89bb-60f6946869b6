package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "crm_bm")
public class CrmBm {

    /**cid*/
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer bid;
    /**部门名称*/
    private String bmname;
    /**部门id*/
    private String bmid;
    /**父部门id*/
    private String parentid;
}
