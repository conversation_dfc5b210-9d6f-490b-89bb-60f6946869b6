package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.DhrjVilla;
import com.wlgb.mapper.DhrjVillaMapper;
import com.wlgb.service4.DhrjVillaService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 15:34
 */
@Service
@DS("fourth")
public class DhrjVillaServiceImpl implements DhrjVillaService {
    @Resource
    private DhrjVillaMapper dhrjVillaMapper;

    @Override
    public void save(DhrjVilla dhrjVilla) {
        dhrjVillaMapper.insertSelective(dhrjVilla);
    }

    @Override
    public void updateById(DhrjVilla dhrjVilla) {
        dhrjVillaMapper.updateByPrimaryKeySelective(dhrjVilla);
    }

    @Override
    public DhrjVilla queryDhrjVillaByDhrjVilla(DhrjVilla dhrjVilla) {
        return dhrjVillaMapper.selectOne(dhrjVilla);
    }
}
