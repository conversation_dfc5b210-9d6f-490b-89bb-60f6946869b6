package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbKdjZdjCzjl;
import com.wlgb.mapper.TbKdjZdjCzjlMapper;
import com.wlgb.service2.TbKdjZdjCzjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27 16:07
 */
@Service
@DS(value = "second")
public class TbKdjZdjCzjlServiceImpl implements TbKdjZdjCzjlService {
    @Resource
    private TbKdjZdjCzjlMapper tbKdjZdjCzjlMapper;

    @Override
    public void save(TbKdjZdjCzjl tbKdjZdjCzjl) {
        tbKdjZdjCzjlMapper.insertSelective(tbKdjZdjCzjl);
    }
}
