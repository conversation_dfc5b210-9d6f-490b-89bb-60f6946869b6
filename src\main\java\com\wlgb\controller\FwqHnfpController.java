package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.config.oss.OssBootUtil;
import com.wlgb.entity.FwqHnfp;
import com.wlgb.entity.FwqSmile;
import com.wlgb.entity.vo.FwqHnfpVo;
import com.wlgb.service.OssFileService;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.FwqHnfpService;
import com.wlgb.service2.FwqSmileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static com.wlgb.config.Tools.isEmpty;

/**
 * <AUTHOR>
 * @Date 2025/02/21 18:30
 * @Version 1.0
 */
@RestController
@Slf4j
@RequestMapping(value = "/hnfp")
public class FwqHnfpController {
    @Autowired
    private FwqHnfpService fwqHnfpService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private OssFileService ossFileService;


    /**
     * 上传照片到oss
     */
    @RequestMapping(value = "upFiles")
    public String upFiles(@RequestParam("jsonData") String jsonData) {
        if (isEmpty(jsonData)) {
            return "参数不能为空";
        }
        log.info("***************{}", jsonData);
        // 解析 JSON 数据
        JSONObject json = JSONObject.parseObject(jsonData);
        String url = json.getString("url");
        String zytype = json.getString("zytype");
        String xiaoquname = json.getString("xiaoquname");
        String miaoshu = json.getString("miaoshu");
        String biaodiname = json.getString("biaodiname");
        String biaodiurl = json.getString("biaodiurl");
        long size = json.getLong("size");
        //查询文件是否已经存在了.不存在就保存
        FwqHnfp fwqHnfp = fwqHnfpService.selectOneByZyurl(url);
        if (fwqHnfp == null) {
            String httpurl = "http://hunanfafu.oss-cn-shenzhen.aliyuncs.com";
            fwqHnfp = new FwqHnfp();
            fwqHnfp.setZytype(zytype);
            fwqHnfp.setZyurl(httpurl + url);
            fwqHnfp.setZyurlfengmian(httpurl + url + "?x-oss-process=video/snapshot,t_1000,f_jpg,w_330,h_200,m_fast");
            if ((size / 1024 / 1024 / 1024) > 1) {
                fwqHnfp.setZysize(String.format("%.3fG", size / 1024.0 / 1024.0 / 1024.0));
            } else if ((size / 1024 / 1024) > 1) {
                fwqHnfp.setZysize(String.format("%.3fM", size / 1024.0 / 1024.0));
            } else if ((size / 1024) > 1) {
                fwqHnfp.setZysize(String.format("%.3fK", size / 1024.0));
            } else {
                fwqHnfp.setZysize(String.format("%.3f字节", size));
            }
            fwqHnfp.setTime(new Date());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = sdf.format(new Date());
            fwqHnfp.setTime2(formattedDate);
            fwqHnfp.setBiaodiname(biaodiname);
            fwqHnfp.setXiaoquname(xiaoquname);
            fwqHnfp.setBiaodiurl(biaodiurl);
            fwqHnfp.setSfsc("0");
            fwqHnfp.setMiaoshu(miaoshu);
            fwqHnfpService.save(fwqHnfp);
        }
        return "ok";
    }

    /**
     * 查询数据库的数据
     */
    @RequestMapping(value = "selectFiles")
    public Result selectFiles(@RequestParam(value = "sousuo", defaultValue = "") String sousuo,
                              @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                              @RequestParam(value = "pageSize", defaultValue = "1000000") int pageSize) {
        log.info("***************sousuosousuo{}", sousuo);
        List<FwqHnfpVo> list = fwqHnfpService.selectAllBySousuo(sousuo, pageNum, pageSize);
        return Result.OK(list);
    }


    /**
     * 查询数据库的数据
     */
    @RequestMapping(value = "selectAllByXiaoquname")
    public Result selectAllByXiaoquname(@RequestParam(value = "xiaoquname", defaultValue = "") String xiaoquname) {
        List<FwqHnfp> list = fwqHnfpService.selectAllByXiaoquname(xiaoquname);
        return Result.OK(list);
    }

    /**
     * 删除数据库的数据
     */
    @RequestMapping(value = "deFiles")
    public String deFiles(@RequestParam("jsonData") String jsonData) throws IOException {
        if (isEmpty(jsonData)) {
            return "参数不能为空";
        }
        // 解析 JSON 数据
        JSONObject json = JSONObject.parseObject(jsonData);
        String url = json.getString("url");
        //查询文件是否已经存在了.不存在就保存
        FwqHnfp fwqHnfp = fwqHnfpService.selectOneByZyurl(url);
        if (fwqHnfp != null) {
            fwqHnfp.setSfsc("1");
            fwqHnfpService.update(fwqHnfp);
        }
        return "ok";
    }

    @RequestMapping(value = "savebiaodan")
    public Result savebiaodan(HttpServletRequest request) throws Exception {
        String xiaoquname = request.getParameter("xiaoquname");
        String slid = request.getParameter("slid");
        String biaodiname = request.getParameter("biaodiname");
        String zhoubianurl = request.getParameter("zhoubianurl");
        String neijingurl = request.getParameter("neijingurl");
        String kongjingtouurl = request.getParameter("kongjingtouurl");
        if (xiaoquname.isEmpty() || slid.isEmpty()) {
            return Result.error("参数不能为空");
        }
        log.info("*************{}", slid);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("测试用");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        JSONArray kjtArray = JSONArray.parseArray(kongjingtouurl);
        // 循环获取每个对象的 downloadUrl
        for (int i = 0; i < kjtArray.size(); i++) {
            JSONObject jsonObject = kjtArray.getJSONObject(i);
            String url = "https://www.aliwork.com" + jsonObject.getString("url");
            GatewayResult jg = YdConfig.getFileUrl(token, ydAppkey, url);
            if (jg.getSuccess() != null && jg.getSuccess()) {
                String url1 = jg.getResult();


            }
        }
        //删除表单实例
        DingBdLcConfig.scBdSl(token, ydAppkey, "012412221639786136545", slid);
        return Result.OK();
    }
}
