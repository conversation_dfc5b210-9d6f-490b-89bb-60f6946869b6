package com.wlgb.config;

import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.entity.TbXyd;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/01/10 14:55
 */
@Slf4j
public class BdConfig {

    /**
     * 修改宜搭表单容错机制
     *
     * @param tbXyd       协议单对象
     * @param name        操作名称
     * @param formDatamap 数据
     * @param formInstId  实例id
     * @param userid      操作人id
     * @param dingkey     钉钉key
     */
    protected static void queryYdRc(TbXyd tbXyd, String name, Map<String, Object> formDatamap, String formInstId, String userid, Dingkey dingkey, DingdingEmployee ding, YdAppkey ydAppkey) {
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        JSONObject json = new JSONObject(formDatamap);
        for (int i = 0; i < 5; i++) {
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, formInstId, json.toJSONString());
                log.info("*****修改表单实例fanhuizhi*********{}",gatewayResult);
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                e.printStackTrace();
            }
            if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                if (i == 4) {
                    try {
                        DingQunSend.send((ding != null ? ding.getName() : tbXyd.getXfd()) + "正在" + name + "，更新表单出错了，订单编号：" + tbXyd.getXddbh() + "，错误原因：" + gatewayResult.toString() + "，接口值：" + (tbXyd.getSfdyjk()), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                break;
            }
        }
    }


    /**
     * 新增宜搭表单容错机制
     *
     * @param tbXyd       协议单对象
     * @param name        操作名称
     * @param formDatamap 数据
     * @param userid      操作人id
     * @param dingkey     钉钉key
     */
    protected static GatewayResult queryYdXzRc(TbXyd tbXyd,
                                               String name,
                                               Map<String, Object> formDatamap,
                                               String userid,
                                               Dingkey dingkey,
                                               YdAppkey ydAppkey,
                                               YdBd ydBd,
                                               DingdingEmployee ding) {
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        JSONObject json = new JSONObject(formDatamap);
        GatewayResult gatewayResult = null;
        for (int i = 0; i < 5; i++) {
            try {
                //更新表单协议单和店长ID
                gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, json.toJSONString(), userid, ydBd.getFormid());
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                e.printStackTrace();
            }
            if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                if (i == 4) {
                    try {
                        DingQunSend.send((ding != null ? ding.getName() : tbXyd.getXfd()) + "正在" + name + "，新增表单出错了，订单编号：" + tbXyd.getXddbh() + "，错误原因：" + gatewayResult.toString(), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                break;
            }
        }
        return gatewayResult;
    }

    protected static GatewayResult xgBdSl(YdAppkey ydAppkey, Dingkey dingkey, String userId, String formInstId, String json) {
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userId, formInstId, json);
        } catch (Exception e) {
            gatewayResult = new GatewayResult();
            e.printStackTrace();
        }
        return gatewayResult;
    }

}
