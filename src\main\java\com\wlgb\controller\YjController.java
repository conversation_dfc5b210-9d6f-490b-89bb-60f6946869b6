package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlgb.config.*;
import com.wlgb.entity.Fwqbbb;
import com.wlgb.entity.Fwqthbd1cjtcopy1;
import com.wlgb.service.WeiLianDdXcxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/05/12 11:08
 * @Version 1.0
 */
@RestController
@RequestMapping(value = "/wlgb/yj")
@Slf4j
public class YjController {

    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;

    /**
     * 查询城市和部门查某个时间段的总业绩
     *
     * @param csjson
     * @return 业绩
     * @throws IOException 1
     */
    @GetMapping(value = "getyj")
    public Result getyj(@RequestParam(value = "csjson", defaultValue = "") String csjson) throws IOException, ParseException {
        System.out.println(csjson);
        List<Fwqbbb> listo = new ArrayList<>();
        JSONObject jsonObj = JSON.parseObject(csjson);
        //去除所有空格，以防万一
        String str = jsonObj.getString("citylist").replaceAll("\\s", "");
        if (str.isEmpty()) {
            return Result.error("城市不能为空");
        }
        String[] strlist = str.split(",");
        for (String s : strlist) {
            Fwqbbb fwqbbb = new Fwqbbb();
            fwqbbb.setBm(jsonObj.getString("bm"));
            fwqbbb.setBmid(jsonObj.getString("bmid"));
            fwqbbb.setCity(s);
            fwqbbb.setBsxz(jsonObj.getString("bsxz"));
            fwqbbb.setKssj(jsonObj.getString("kssj"));
            fwqbbb.setJssj(jsonObj.getString("jssj"));
            Map<String, Object> map = new HashMap<>();
            map.put("city", fwqbbb.getCity());
            map.put("bmmc", fwqbbb.getBm());
            map.put("kssj", fwqbbb.getKssj());
            map.put("jssj", fwqbbb.getJssj());
            //查询某部门、直营、某个时间段的下单业绩
            List<Fwqthbd1cjtcopy1> fwqthbd1cjtcopy1List = weiLianDdXcxService.queryYj(map);
            Double d = 0.0;
            if (fwqthbd1cjtcopy1List.size() > 0 && fwqthbd1cjtcopy1List.get(0) != null) {
                d = fwqthbd1cjtcopy1List.get(0).getXDYJ();
            }
            fwqbbb.setXdyj(d);
            listo.add(fwqbbb);
        }
        log.info("************{}", listo);
        return Result.OK(listo);
    }

}
