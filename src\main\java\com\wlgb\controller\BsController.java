package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.TbCsfq;
import com.wlgb.entity.TbCsjq;
import com.wlgb.entity.TbVilla;
import com.wlgb.entity.WlgbJdBsmcXgjl;
import com.wlgb.entity.vo.QyUtil;
import com.wlgb.entity.vo.TbVillaVo;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service.WlgbJdBsmcXgjlService;
import com.wlgb.service2.TbCsfqService;
import com.wlgb.service2.TbCsjqService;
import com.wlgb.service2.TbVillaService;
import com.wlgb.service2.<PERSON><PERSON>ianService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/08/07 09:47
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/bs")
public class BsController {
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WlgbJdBsmcXgjlService wlgbJdBsmcXgjlService;
    @Autowired
    private TbVillaService tbVillaService;
    @Autowired
    private TbCsfqService tbCsfqService;
    @Autowired
    private TbCsjqService tbCsjqService;

    /**
     * 别墅分区下拉
     */
    @RequestMapping(value = "queryCsFqSelect")
    public Result queryCsFqSelect() {
        List<Select> list = weiLianService.queryCsFqSelect();

        return Result.OK(list);
    }

    /**
     * 别墅集群下拉
     */
    @RequestMapping(value = "queryCsJqSelect")
    public Result queryCsJqSelect() {
        List<Select> list = weiLianService.queryCsJqSelect();

        return Result.OK(list);
    }

    /**
     * 别墅播报城市下拉
     */
    @RequestMapping(value = "queryBbCsSelect")
    public Result queryBbCsSelect() {
        List<Select> list = weiLianService.queryBbCsSelect();

        return Result.OK(list);
    }

    /**
     * 别墅分公司下拉
     */
    @RequestMapping(value = "queryFgsSelect")
    public Result queryFgsSelect() {
        List<Select> list = weiLianService.queryFgsSelect();

        return Result.OK(list);
    }

    /**
     * 别墅数据列表
     */
    @RequestMapping(value = "queryBsWhList")
    public Result queryBsWhList(HttpServletRequest request) {
        String pageNo = request.getParameter("pageNo");
        String pageSize = request.getParameter("pageSize");
        String vname = request.getParameter("vname");
        String vxz = request.getParameter("vxz");
        String vmdsstz = request.getParameter("vmdsstz");
        String vsfft = request.getParameter("vsfft");
        String vcyqy = request.getParameter("vcyqy");
        PageHelp pageHelp = new PageHelp(pageNo != null ? Integer.parseInt(pageNo) : 1, pageSize != null ? Integer.parseInt(pageSize) : 10);
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        if (vname != null && !"".equals(vname)) {
            map.put("vname", vname);
        }
        if (vxz != null && !"".equals(vxz)) {
            map.put("vxz", vxz);
        }
        if (vmdsstz != null && !"".equals(vmdsstz)) {
            map.put("vmdsstz", vmdsstz);
        }
        if (vsfft != null && !"".equals(vsfft)) {
            map.put("vsfft", vsfft);
        }
        if (vcyqy != null && !"".equals(vcyqy)) {
            map.put("vcyqy", vcyqy);
        }
        PageHelpUtil pageHelpUtil1 = weiLianService.queryBsWhList(map);
        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);

        return Result.OK(pageHelpUtil1);
    }

    /**
     * 别墅数据下载
     */
    @RequestMapping(value = "queryBsListXz")
    public Result queryBsListXz(HttpServletRequest request) {
        String vname = request.getParameter("vname");
        String vxz = request.getParameter("vxz");
        Map<String, Object> map = new HashMap<>();
        if (vname != null && !"".equals(vname)) {
            map.put("vname", vname);
        }
        if (vxz != null && !"".equals(vxz)) {
            map.put("vxz", vxz);
        }
        List<TbVillaVo> list = weiLianService.queryBsListXz(map);

        return Result.OK(list);
    }

    /**
     * 新增别墅
     */
    @RequestMapping(value = "tjBsXz")
    public Result tjBsXz(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas没有数据！");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject.isEmpty()) {
            return Result.error("数据不完整！");
        }
        TbVilla villa = JSON.toJavaObject(jsonObject, TbVilla.class);
        villa.setVid(IdConfig.uuId());
        tbVillaService.save(villa);
        //保存到金蝶
        tjJdBs(villa);

        String context = "门店管理系统新增了门店";
        context += "\n\n门店名称：" + villa.getVname();
        context += "\n\n城市：" + villa.getCity();
        context += "\n\n性质：" + villa.getVxz();
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        context += "\n\n送达时间：" + df2.format(new Date());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");

        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_EDOLU85IIM2FODPX0UE1");
        ydAppkey.setToken("K7666JC1J3XKM1F1BW7HW4U7851U2ZWY05XVLZ4");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        log.info("*****************jsonObject******{}", jsonObject);
        GatewayResult gatewayResult = new GatewayResult();
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("textField_lx43i9mm", IdConfig.uuId());
        jsonObject1.put("selectField_lvx7n6n6", villa.getVname());
        jsonObject1.put("selectField_lvx7n6n6_id", villa.getVid());
        //集群
        Map<String, Object> map1 = new HashMap<>();
        map1.put("jid", jsonObject.getString("jid"));
        QyUtil qyUtil = weiLianService.queryQyUtil(map1);
        jsonObject1.put("selectField_lvx7n6n7", qyUtil.getJname());
        jsonObject1.put("selectField_lvx7n6n7_id", qyUtil.getJid());
        //所属排单
        jsonObject1.put("selectField_lvx7n6nb", jsonObject.getString("selectField_lxb8rm3y"));
        jsonObject1.put("selectField_lvx7n6nb_id", jsonObject.getString("selectField_lxb8rm3y"));
        Integer vremark = jsonObject.getInteger("vremark");
        jsonObject1.put("numberField_lx41sc79", (vremark == null ? 1 : vremark));
        try {
            gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObject1.toString(), "012412221639786136545", "FORM-9B606CE14934413BB26C5A2288F6492FYG1S");
            log.info("*****************gatewayResult******{}", gatewayResult);
        } catch (Exception e) {
            log.info("***********e******eeeee******{}", e);
            gatewayResult = new GatewayResult();
        }
        Boolean success = gatewayResult.getSuccess();
        if (success != null && !success) {
            try {
                DingDBConfig.sendGztzText(dingkey, "15349026426046931,159909317438346", context);
            } catch (ApiException e) {
                log.info("***********e***e***eeeee******{}", e);
            }
        }
        return Result.OK();
    }

    /**
     * 别墅修改
     */
    @RequestMapping(value = "tjBsXg")
    public Result tjBsXg(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas没有数据！");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null || jsonObject.size() < 1) {
            return Result.error("数据不完整！");
        }
        TbVilla villa = JSON.toJavaObject(jsonObject, TbVilla.class);
        TbVilla villa1 = tbVillaService.getById(villa.getVid());
        if (villa1 == null) {
            return Result.error("找不到别墅");
        }
        tbVillaService.updateById(villa);
        //使用原名称查询金蝶数据
        JSONObject jsonObject1 = KingDeeConfig.queryKingDeeBsXx(villa1.getVname());
        log.info("金蝶返回的门店数据**********{}", jsonObject1);
        if (jsonObject1.getBoolean("success")) {
            String bm = jsonObject1.getString("bm");
            if (bm != null && !"".equals(bm)) {
                String fid = jsonObject1.getString("fid");
                KingDeeConfig.mdFsh(bm);
                KingDeeConfig.saveMd(villa.getVname(), bm, villa.getVxz(), fid);
            } else {
                tjJdBs(villa);
            }
        } else {
            tjJdBs(villa);
        }
        if (!villa1.getVname().equals(villa.getVname())) {
            WlgbJdBsmcXgjl wlgbJdBsmcXgjl1 = wlgbJdBsmcXgjlService.queryByVidAndSfSc(villa1.getVid(), 0);
            if (wlgbJdBsmcXgjl1 != null) {
                WlgbJdBsmcXgjl wlgbJdBsmcXgjl = new WlgbJdBsmcXgjl();
                wlgbJdBsmcXgjl.setId(wlgbJdBsmcXgjl1.getId());
                wlgbJdBsmcXgjl.setSfsc(1);
                wlgbJdBsmcXgjlService.updateById(wlgbJdBsmcXgjl);
            }
            WlgbJdBsmcXgjl wlgbJdBsmcXgjl = new WlgbJdBsmcXgjl();
            wlgbJdBsmcXgjl.setVid(villa1.getVid());
            wlgbJdBsmcXgjl.setVname(villa.getVname());
            wlgbJdBsmcXgjl.setYname(villa1.getVname());
            wlgbJdBsmcXgjl.setXgtime(new Date());
            wlgbJdBsmcXgjlService.save(wlgbJdBsmcXgjl);
            String context = "门店管理系统修改了门店名称";
            context += "\n\n原门店名称：" + villa1.getVname();
            context += "\n\n新门店名称：" + villa.getVname();
            context += "\n\n城市：" + villa.getCity();
            context += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            try {
                DingDBConfig.sendGztzText(dingkey, "15349026426046931,159909317438346", context);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
        if (!villa1.getVxz().equals(villa.getVxz())) {
            String context = "门店管理系统修改了门店性质";
            context += "\n\n原性质：" + villa1.getVxz();
            context += "\n\n新性质：" + villa.getVxz();
            context += "\n\n门店名称：" + villa.getVname();
            context += "\n\n城市：" + villa.getCity();
            context += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            try {
                DingDBConfig.sendGztzText(dingkey, "15349026426046931,159909317438346", context);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
        return Result.OK();
    }

    /**
     * 添加金蝶别墅
     */
    public void tjJdBs(TbVilla villa) {
        String bm = "01";
        if ("托管加盟".equals(villa.getVxz())) {
            bm = "02";
        } else if ("半托管加盟".equals(villa.getVxz())) {
            bm = "03";
        } else if ("合作".equals(villa.getVxz())) {
            bm = "04";
        }
        String scBm = KingDeeConfig.bsScBm(bm);
        KingDeeConfig.saveMd(villa.getVname(), scBm, villa.getVxz(), "0");
    }

    /**
     * 别墅删除
     */
    @RequestMapping(value = "tjBsSc")
    public Result tjBsSc(HttpServletRequest request) {
        String vid = request.getParameter("vid");
        long gtrq = Long.parseLong(request.getParameter("dateField_lxwis48x"));
        TbVilla tbVilla = tbVillaService.getById(vid);
        if (tbVilla == null) {
            return Result.error("找不到别墅");
        }
        TbVilla villa = new TbVilla();
        villa.setVid(tbVilla.getVid());
        villa.setVsfsc(1);
        villa.setVsfft("0");
        Date date = new Date(gtrq);
        villa.setGdtime(date);
        tbVillaService.updateById(villa);
        String context = "门店管理系统删除了门店";
        context += "\n\n门店名称：" + tbVilla.getVname();
        context += "\n\n城市：" + tbVilla.getCity();
        context += "\n\n送达时间：" + DateFormatConfig.df1(new Date());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        try {
            DingDBConfig.sendGztzText(dingkey, "15349026426046931", context);
        } catch (ApiException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 别墅分区数据列表
     */
    @RequestMapping(value = "queryFqWhList")
    public Result queryFqWhList(HttpServletRequest request) {
        String pageNo = request.getParameter("pageNo");
        String pageSize = request.getParameter("pageSize");
        String fname = request.getParameter("fname");
        String cid = request.getParameter("cid");
        PageHelp pageHelp = new PageHelp(pageNo != null ? Integer.parseInt(pageNo) : 1, pageSize != null ? Integer.parseInt(pageSize) : 10);
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        if (fname != null && !"".equals(fname)) {
            map.put("fname", fname);
        }
        if (cid != null && !"".equals(cid)) {
            map.put("cid", cid);
        }
        PageHelpUtil pageHelpUtil1 = weiLianService.queryFqWhList(map);
        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);

        return Result.OK(pageHelpUtil1);
    }

    /**
     * 别墅分区新增
     */
    @RequestMapping(value = "tjFqXz")
    public Result tjFqXz(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas没有数据！");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null || jsonObject.size() < 1) {
            return Result.error("数据不完整！");
        }
        TbCsfq csfq = JSON.toJavaObject(jsonObject, TbCsfq.class);
        csfq.setFid(IdConfig.uuId());
        tbCsfqService.save(csfq);


        return Result.OK();
    }

    /**
     * 别墅分区修改
     */
    @RequestMapping(value = "tjFqXg")
    public Result tjFqXg(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas没有数据！");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null || jsonObject.size() < 1) {
            return Result.error("数据不完整！");
        }
        TbCsfq csfq = JSON.toJavaObject(jsonObject, TbCsfq.class);
        TbCsfq csfq1 = tbCsfqService.getById(csfq.getFid());
        if (csfq1 == null) {
            return Result.error("找不到分区");
        }
        tbCsfqService.updateById(csfq);


        return Result.OK();
    }

    /**
     * 别墅集群数据列表
     */
    @RequestMapping(value = "queryJqWhList")
    public Result queryJqWhList(HttpServletRequest request) {
        String pageNo = request.getParameter("pageNo");
        String pageSize = request.getParameter("pageSize");
        String jname = request.getParameter("jname");
        String cid = request.getParameter("cid");
        PageHelp pageHelp = new PageHelp(pageNo != null ? Integer.parseInt(pageNo) : 1, pageSize != null ? Integer.parseInt(pageSize) : 10);
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("help", pageHelpUtil);
        if (jname != null && !"".equals(jname)) {
            map.put("jname", jname);
        }
        if (cid != null && !"".equals(cid)) {
            map.put("cid", cid);
        }
        PageHelpUtil pageHelpUtil1 = weiLianService.queryJqWhList(map);
        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);

        return Result.OK(pageHelpUtil1);
    }

    /**
     * 别墅集群新增
     */
    @RequestMapping(value = "tjJqXz")
    public Result tjJqXz(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas没有数据！");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null || jsonObject.size() < 1) {
            return Result.error("数据不完整！");
        }
        TbCsjq csjq = JSON.toJavaObject(jsonObject, TbCsjq.class);
        csjq.setJcolor("#000000");
        tbCsjqService.save(csjq);


        return Result.OK();
    }

    /**
     * 别墅集群修改
     */
    @RequestMapping(value = "tjJqXg")
    public Result tjJqXg(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas没有数据！");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null || jsonObject.size() < 1) {
            return Result.error("数据不完整！");
        }
        TbCsjq csjq = JSON.toJavaObject(jsonObject, TbCsjq.class);
        TbCsjq csjq1 = tbCsjqService.getById(csjq.getJid());
        if (csjq1 == null) {
            return Result.error("找不到集群");
        }
        tbCsjqService.updateById(csjq);


        return Result.OK();
    }
}
