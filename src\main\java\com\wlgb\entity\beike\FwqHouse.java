package com.wlgb.entity.beike;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Table;

@Data
@Table(name = "fwq_house")
@Accessors(chain = true)
public class FwqHouse {
    @ExcelProperty("楼盘名称")
    private String title;

    @ExcelProperty("访问网页")
    private String detailpageurl;

    @ExcelProperty("楼盘图片")
    private String imageurl;

    @ExcelProperty("所在地址")
    private String address;

    @ExcelProperty("户型")
    private String housetype;

    @ExcelProperty("房产类型")
    private String propertytype;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("建筑面积")
    private String buildingarea;

    @ExcelProperty("总价")
    private String totalprice;

    @ExcelProperty("单价（元/㎡(均价)）")
    private String singleprice;

    @ExcelProperty("标签")
    private String tag;
}

