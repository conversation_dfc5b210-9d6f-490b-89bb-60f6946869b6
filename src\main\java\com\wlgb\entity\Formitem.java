package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: formitem
 * @Author: jeecg-boot
 * @Date:   2020-10-29
 * @Version: V1.0
 */
@Data
@Table(name = "formitem")
public class Formitem{

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String cid;
	/**标题*/
    private java.lang.String title;
	/**内容*/
    private java.lang.String content;
	/**主题id*/
    private java.lang.String tid;
	/**排序使用*/
    private java.lang.Integer count;
}
