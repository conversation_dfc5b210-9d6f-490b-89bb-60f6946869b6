package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/12 16:13
 */
@Data
@Table(name = "wlgb_eat_bsjl")
public class WlgbEatBsjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**创建时间*/
    private Date createTime;
    /**修改时间*/
    private Date updateTime;
    /**流水号*/
    private String lsh;
    /**提交人*/
    private String tjr;
    /**提交人id*/
    private String tjrid;
    /**供应商*/
    private String gys;
    /**供应商编号*/
    private String gysbh;
    /**食材*/
    private String sc;
    /**单位*/
    private String dw;
    /**最小单价*/
    private Double minPrice;
    /**报损数量*/
    private Integer bsNum;
    /**报损金额*/
    private Double bsJe;
    /**报损总金额*/
    private Double bsZje;
    /**备注*/
    private String bz;
    /**是否删除(0:否，1:是)*/
    private Integer sfsc;
}
