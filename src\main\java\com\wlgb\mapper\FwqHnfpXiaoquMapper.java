package com.wlgb.mapper;

import com.wlgb.entity.FwqHnfpXiaoqu;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;


public interface FwqHnfpXiaoquMapper extends Mapper<FwqHnfpXiaoqu> {
    @Select("<script>" +
            "SELECT xiaoquname, COUNT(*) as count " +
            "FROM fwq_hnfp " +
            "WHERE sfsc = '0' " +
            "GROUP BY xiaoquname " +
            "ORDER BY time DESC " +
            "</script>")
    List<FwqHnfpXiaoqu> selectGroupByXiaoquName(@Param("sousuo") String sousuo, @Param("offset") int offset, @Param("pageSize") int pageSize);
}
