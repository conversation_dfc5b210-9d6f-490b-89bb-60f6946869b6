package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdYzsqjl;
import com.wlgb.mapper.WlgbJdYzsqjlMapper;
import com.wlgb.service.WlgbJdYzsqjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 22:25
 */
@Service
public class WlgbJdYzsqjlServiceImpl implements WlgbJdYzsqjlService {
    @Resource
    private WlgbJdYzsqjlMapper wlgbJdYzsqjlMapper;

    @Override
    public void save(WlgbJdYzsqjl wlgbJdYzsqjl) {
        wlgbJdYzsqjl.setCreateTime(new Date());
        wlgbJdYzsqjl.setId(IdConfig.uuId());
        wlgbJdYzsqjlMapper.insertSelective(wlgbJdYzsqjl);
    }

    @Override
    public void updateById(WlgbJdYzsqjl wlgbJdYzsqjl) {
        wlgbJdYzsqjl.setUpdateTime(new Date());
        wlgbJdYzsqjlMapper.updateByPrimaryKeySelective(wlgbJdYzsqjl);
    }

    @Override
    public WlgbJdYzsqjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd) {
        Example example = new Example(WlgbJdYzsqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdYzsqjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdYzsqjl> queryListWlgbJdYzsqjl(WlgbJdYzsqjl wlgbJdYzsqjl) {
        return wlgbJdYzsqjlMapper.select(wlgbJdYzsqjl);
    }
}
