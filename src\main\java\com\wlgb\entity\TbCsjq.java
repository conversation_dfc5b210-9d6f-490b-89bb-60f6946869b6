package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: tb_csjq
 * @Author: jeecg-boot
 * @Date:   2021-01-03
 * @Version: V1.0
 */
@Data
@Table(name = "tb_csjq")
public class TbCsjq {

	/**集群编号*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer jid;
	/**集群名称*/
    private java.lang.String jname;
	/**备注*/
    private java.lang.String jbz;
	/**公司ID*/
    private java.lang.String cid;
	/**集群颜色*/
    private java.lang.String jcolor;
}
