package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "fwq_smile")
public class FwqSmile {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    private java.util.Date sctime;
    private String userid;
    private String username;
    private String url;
    private String emotion;
    private String emotionconfidence;
    private String swxw;
    private String sfyx;
    private String pzzt;
}
