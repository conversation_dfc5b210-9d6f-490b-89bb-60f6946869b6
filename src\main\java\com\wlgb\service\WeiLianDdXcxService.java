package com.wlgb.service;

import com.alibaba.fastjson.JSONObject;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.WlgbJmsbb;
import com.wlgb.entity.vo.WlgbNotVilla;
import com.wlgb.entity.vo.WlgbQrdDzjl;
import com.wlgb.entity.vo.YjDzVo;

import java.util.List;
import java.util.Map;

public interface WeiLianDdXcxService {
    List<TbXyd> queryXyEcGd();

    Integer queryBsCount(String vid);

    Csry queryCsRyByUserId(String userId);

    Csry queryCsRyJmByUserId(String userId);

    Integer queryJdbOneCountByXid(String xid);

    Integer queryJdbTwoCountByXid(String xid);

    TbVilla queryTbVillaById(String vid);

    TbVilla queryTbVillaByIdOrVname(String vidorvname);

    Dingkey queryDingKeyById(String id);

    DingdingEmployee queryDingdingEmployeeByUserId(String userId);

    DingdingEmployee queryDingdingEmployeeByUserName(String name);

    DingdingEmployee queryDingdingEmployeeBySJH(String sjh);

    Dzxyd queryDzXydByXid(String xid);

    YdAppkey queryYdAppKeyByBz(String bz);

    YdBd queryYdBdByBz(String bz);

    List<TbXyd> queryNotFsJcLc(Map<String, Object> map);

    List<TbXyd> queryNotFsDzBd(Map<String, Object> map);

    Integer queryNotEcGdCountByXid(String xid);

    Double queryKdjJzj(Map<String, Object> map);

    List<TbVilla> queryEhDbBsList();

    List<WlgbHrQun> queryHrQunList();

    List<HrLsZy> queryHrLsZy(Map<String, Object> map);

    List<HrLsZy> queryHrLsJl(Map<String, Object> map);

    PageHelpUtil QueryMtXsList(Map<String, Object> map);

    List<WlgbJdDjbbd> queryDjbDataByFormIdList(Map<String, Object> map);

    PageHelpUtil queryDzWhkList(Map<String, Object> map);

    PageHelpUtil queryXsDj(Map<String, Object> map);

    List<Fwqthbd1cjtcopy1> queryYj(Map<String, Object> map);

    WlgbJmsbb queryJmsBbByVid(String vid);

    Integer queryNotQx(String userId);

    Integer querySfSqKdj(Map<String, Object> map);

    void zxKdjHfGc(Map<String, Object> map);

    Integer queryDdCz(String xid);

    Integer queryByXidDzJlCount(String xid);

    void insertDzHfJl(WlgbQrdDzjl wlgbQrdDzjl);

    List<WlgbQrdDzjl> queryFsHf();

    void updateXgZt(WlgbQrdDzjl wlgbQrdDzjl);

    List<WlgbNotVilla> queryGqGl();

    void deleteSc(String vid);

    List<DkBbJqr> queryLcBbJqr();

    List<String> queryJqrBbAt(String city);

    void zxCdf();

    void zxCdf2(String xddbh);

    List<JSONObject> queryCdfList();

    PageHelpUtil queryProductByTypeList(Map<String, Object> map);

    Integer querySpLbCount(String processCode);

    List<WlgbHxyhDzjl> queryBdj();

    JSONObject queryCnQyByBmId(String bmid);

    void zxPyqBbSj();

    List<Map<String, Object>> queryBbSjRy();

    List<Map<String, Object>> queryBbSjBm();

    String queryBsKySjByBsMc(String bsmc);

    List<WlgbJdDjbbd> queryDgZzWscJdList();

    List<JSONObject> queryYhxQm();

    void qkJdFyKmTable();

    List<Map<String, Object>> queryHrGzRzFsr();

    void zxKdjSj(String start, String end);

    void zxKdjSjMs(String start, String end);

    List<Map<String, Object>> queryKdjZsZdj();

    List<Map<String, Object>> queryBsHzbZdj();

    List<Map<String, Object>> queryRqZzbZdj();

    WlgbJdDjbbd queryDjJlByLsh(String lsh);

    List<Select> queryFyKmSelect();

    void insertCpJl(Map<String, Object> map);

    PageHelpUtil queryYwJlHfJl(Map<String, Object> map);

    Integer queryYxbTzLb(String lbid);

    List<String> queryYxBmHrTz();

    String queryZpbRy();

    List<Map<String, Object>> queryDtSj();

    List<Map<String, Object>> queryDySj();

    List<Map<String, Object>> queryDzSj();

    List<Map<String, Object>> querySzSj();

    List<Map<String, Object>> queryDyySj();

    List<Map<String, Object>> queryOldyySj();

    List<Map<String, Object>> queryDnnSj();

    List<Map<String, Object>> querySnnSj();

    List<Map<String, Object>> queryYgwyx();

    List<Map<String, Object>> queryNgwyx();

    List<Map<String, Object>> queryBmmb();

    List<Map<String, Object>> queryGrmb();

    List<Map<String, Object>> queryYbmmb();

    List<Map<String, Object>> querySgrmb();

    void insertSc(WlgbNotVilla wlgbNotVilla);

    Integer queryByVidCount(String vid);

    void updateSc(WlgbNotVilla wlgbNotVilla);

    void zxXCdf(String bsxz, String xzkssj, String xzjssj);

    List<Map<String, Object>> queryDzyXCdf();

    List<WlgbOrderCyjl> queryCyJlByz();

    List<String> queryCyJlDzByz();

    String queryProcessTypeByProcessCode(String processCode);

    List<YjDzVo> queryYjWkZfJlByDdBh(String ddbh);

    PageHelpUtil querySjCdfNewJl(Map<String, Object> map);

    void insertSjCdfNewJl(SjCdfNewJl sjCdfNewJl);

    PageHelpUtil querySpList(Map<String, Object> map);

}
