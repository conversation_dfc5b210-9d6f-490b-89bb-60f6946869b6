package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbCsfq;
import com.wlgb.mapper.TbCsfqMapper;
import com.wlgb.service2.TbCsfqService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/7 16:43
 */
@Service
@DS(value = "second")
public class TbCsfqServiceImpl implements TbCsfqService {
    @Resource
    private TbCsfqMapper tbCsfqMapper;

    @Override
    public void save(TbCsfq tbCsfq) {
        tbCsfqMapper.insertSelective(tbCsfq);
    }

    @Override
    public void updateById(TbCsfq tbCsfq) {
        tbCsfqMapper.updateByPrimaryKeySelective(tbCsfq);
    }

    @Override
    public TbCsfq getById(String fid) {
        return tbCsfqMapper.selectByPrimaryKey(fid);
    }
}
