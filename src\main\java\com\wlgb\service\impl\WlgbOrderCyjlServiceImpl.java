package com.wlgb.service.impl;

import com.wlgb.entity.WlgbOrderCyjl;
import com.wlgb.mapper.WlgbOrderCyjlMapper;
import com.wlgb.service.WlgbOrderCyjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/5 15:38
 */
@Service
public class WlgbOrderCyjlServiceImpl implements WlgbOrderCyjlService {
    @Resource
    private WlgbOrderCyjlMapper wlgbOrderCyjlMapper;

    @Override
    public void save(WlgbOrderCyjl wlgbOrderCyjl) {
        wlgbOrderCyjl.setCreateTime(new Date());
        wlgbOrderCyjlMapper.insertSelective(wlgbOrderCyjl);
    }

    @Override
    public void updateById(WlgbOrderCyjl wlgbOrderCyjl) {
        wlgbOrderCyjl.setUpdateTime(new Date());
        wlgbOrderCyjlMapper.updateByPrimaryKeySelective(wlgbOrderCyjl);
    }

    @Override
    public List<WlgbOrderCyjl> queryListByOrderCyJl(WlgbOrderCyjl wlgbOrderCyjl) {
        return wlgbOrderCyjlMapper.select(wlgbOrderCyjl);
    }

    @Override
    public void clearCyJl(String ddbh) {
        WlgbOrderCyjl wlgbOrderCyjl = new WlgbOrderCyjl();
        wlgbOrderCyjl.setDdbh(ddbh);
        wlgbOrderCyjl.setCsfsc(0);
        List<WlgbOrderCyjl> list = wlgbOrderCyjlMapper.select(wlgbOrderCyjl);

        list.forEach(l->{
            WlgbOrderCyjl wlgbOrderCyjl1 = new WlgbOrderCyjl();
            wlgbOrderCyjl1.setId(l.getId());
            wlgbOrderCyjl1.setCsfsc(1);
            wlgbOrderCyjl1.setUpdateTime(new Date());
            wlgbOrderCyjlMapper.updateByPrimaryKeySelective(wlgbOrderCyjl1);
        });
    }
}
