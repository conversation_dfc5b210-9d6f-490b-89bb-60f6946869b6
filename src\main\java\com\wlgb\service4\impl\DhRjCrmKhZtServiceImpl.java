package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmKhzt;
import com.wlgb.mapper.CrmKhztMapper;
import com.wlgb.service4.DhRjCrmKhZtService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 18:23
 */
@Service
@DS("fourth")
public class DhRjCrmKhZtServiceImpl implements DhRjCrmKhZtService {
    @Resource
    private CrmKhztMapper crmKhztMapper;

    @Override
    public void save(CrmKhzt crmKhzt) {
        crmKhztMapper.insertSelective(crmKhzt);
    }

    @Override
    public void updateById(CrmKhzt crmKhzt) {
        crmKhztMapper.updateByPrimaryKeySelective(crmKhzt);
    }

    @Override
    public CrmKhzt queryCrmKhZtByCrmKhZt(CrmKhzt crmKhzt) {
        return crmKhztMapper.selectOne(crmKhzt);
    }
}
