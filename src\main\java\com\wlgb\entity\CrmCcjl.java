package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "crm_ccjl")
public class CrmCcjl {

    /**cid*/
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer cid;
    /**查重时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date ccsj;
    /**查重数据*/
    private String ccxx;
    /**汇总查出来的所有数据编号*/
    private String hz;
    /**操作人*/
    private String czr;
    /**操作人id*/
    private String czrid;
}
