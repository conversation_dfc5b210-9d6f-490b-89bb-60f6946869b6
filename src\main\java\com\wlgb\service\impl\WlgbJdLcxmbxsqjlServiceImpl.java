package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdLcxmbxsqjl;
import com.wlgb.mapper.WlgbJdLcxmbxsqjlMapper;
import com.wlgb.service.WlgbJdLcxmbxsqjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 23:49
 */
@Service
public class WlgbJdLcxmbxsqjlServiceImpl implements WlgbJdLcxmbxsqjlService {
    @Resource
    private WlgbJdLcxmbxsqjlMapper wlgbJdLcxmbxsqjlMapper;

    @Override
    public void save(WlgbJdLcxmbxsqjl wlgbJdLcxmbxsqjl) {
        wlgbJdLcxmbxsqjl.setCreateTime(new Date());
        wlgbJdLcxmbxsqjl.setId(IdConfig.uuId());
        wlgbJdLcxmbxsqjlMapper.insertSelective(wlgbJdLcxmbxsqjl);
    }

    @Override
    public void updateById(WlgbJdLcxmbxsqjl wlgbJdLcxmbxsqjl) {
        wlgbJdLcxmbxsqjl.setUpdateTime(new Date());
        wlgbJdLcxmbxsqjlMapper.updateByPrimaryKeySelective(wlgbJdLcxmbxsqjl);
    }

    @Override
    public WlgbJdLcxmbxsqjl queryBySpBhAndSfLrJd(String spbh, Integer sfLrJd) {
        Example example = new Example(WlgbJdLcxmbxsqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spbh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdLcxmbxsqjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdLcxmbxsqjl> queryListByWlgbJdLcxmbxsqjl(WlgbJdLcxmbxsqjl wlgbJdLcxmbxsqjl) {
        return wlgbJdLcxmbxsqjlMapper.select(wlgbJdLcxmbxsqjl);
    }
}
