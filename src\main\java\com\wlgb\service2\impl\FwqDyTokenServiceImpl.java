package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqDyToken;
import com.wlgb.entity.vo.FwqBbb;
import com.wlgb.mapper.FwqBbbMapper;
import com.wlgb.mapper.FwqDyTokenMapper;
import com.wlgb.service2.FwqBbbService;
import com.wlgb.service2.FwqDyTokenService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@DS(value = "second")
public class FwqDyTokenServiceImpl implements FwqDyTokenService {
    @Resource
    private FwqDyTokenMapper fwqDyTokenMapper;

    @Override
    public void save(FwqDyToken fwqDyToken) {
        fwqDyTokenMapper.insertSelective(fwqDyToken);
    }

    @Override
    public List<FwqDyToken> selectByAccountid(String accountid) {
        FwqDyToken fwqDyToken = new FwqDyToken();
        fwqDyToken.setAccountid(accountid);
        return fwqDyTokenMapper.select(fwqDyToken);
    }

    @Override
    public List<FwqDyToken> selectAll() {
        return fwqDyTokenMapper.selectAll();
    }

    @Override
    public void deleteByPrimaryKey(FwqDyToken fwqDyToken) {
        fwqDyTokenMapper.deleteByPrimaryKey(fwqDyToken);
    }
}
