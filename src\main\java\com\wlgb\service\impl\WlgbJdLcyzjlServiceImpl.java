package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdLcyzjl;
import com.wlgb.mapper.WlgbJdLcyzjlMapper;
import com.wlgb.service.WlgbJdLcyzjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/28 12:12
 */
@Service
public class WlgbJdLcyzjlServiceImpl implements WlgbJdLcyzjlService {
    @Resource
    private WlgbJdLcyzjlMapper wlgbJdLcyzjlMapper;

    @Override
    public void save(WlgbJdLcyzjl wlgbJdLcyzjl) {
        wlgbJdLcyzjl.setId(IdConfig.uuId());
        wlgbJdLcyzjl.setCreateTime(new Date());
        wlgbJdLcyzjlMapper.insertSelective(wlgbJdLcyzjl);
    }

    @Override
    public void updateById(WlgbJdLcyzjl wlgbJdLcyzjl) {
        wlgbJdLcyzjl.setUpdateTime(new Date());
        wlgbJdLcyzjlMapper.updateByPrimaryKeySelective(wlgbJdLcyzjl);
    }

    @Override
    public WlgbJdLcyzjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd) {
        Example example = new Example(WlgbJdLcyzjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdLcyzjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdLcyzjl> queryListBySfLrJd(Integer sfLrJd) {
        Example example = new Example(WlgbJdLcyzjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdLcyzjlMapper.selectByExample(example);
    }

    @Override
    public Integer queryCountBySpBh(String spBh) {
        Example example = new Example(WlgbJdLcyzjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        return wlgbJdLcyzjlMapper.selectCountByExample(example);
    }
}
