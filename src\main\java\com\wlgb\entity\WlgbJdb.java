package com.wlgb.entity;

import java.io.Serializable;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 接单表
 * @Author: jeecg-boot
 * @Date:   2020-09-18
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jdb")
public class WlgbJdb implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
    /**是否添加客户微信(是:微信截图,否无法填写下一项)*/
    private java.lang.String sftjkhwx;
    /**微信截图(是)*/
    private java.lang.String wxtgjt;
	/** 是否规范备注*/
    private java.lang.String sfgfbzwx;
    /** 是否确认订单信息*/
    private java.lang.String sfqrddxx;
    /** 是否确认订单信息*/
    private java.lang.String sfsmzysx;
    /** 是否推销增值*/
    private java.lang.String sftxzz;
    /**订单是否需要修改*/
    private java.lang.String ddsfxyxg;
    /**附上事宜与截图(是)*/
    private java.lang.String fssyyjt;
	/**具体入场时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    private java.util.Date jtrcsj;
	/**卫生是否打扫完毕*/
    private java.lang.String wsds;
	/**附上大厅图片*/
    private java.lang.String dttp;
	/**商品是否补充完毕*/
    private java.lang.String spbc;
	/**附上至少两个货架图片*/
    private java.lang.String hjtp;
	/**货架图片2*/
    private java.lang.String hjtp2;
	/**设备是否检查正常*/
    private java.lang.String sbjc;
	/**附上按摩椅娃娃机开启图*/
    private java.lang.String kqt;
	/**增值是否对接完毕*/
    private java.lang.String zzdj;
	/**特殊需求是否完毕*/
    private java.lang.String tsxq;
	/**是否收取全款*/
    private java.lang.String sqqk;
	/**是否转回公司*/
    private java.lang.String zhgs;
	/**付转账凭证*/
    private java.lang.String zzpz;
	/**是否签署短租合同*/
    private java.lang.String qsdcht;
	/**附上纸质合同照片*/
    private java.lang.String zzhtzp;
	/**是否拍电表*/
    private java.lang.String rcPdb;
	/**进场电表1*/
    private java.lang.String rcSjxjdbt;
	/**进场电表2*/
    private java.lang.String rcSjxjdbt2;
	/**是否教学设备*/
    private java.lang.String jxsb;
	/**是否清点商品*/
    private java.lang.String qdsp;
	/**是否发现异常情况*/
    private java.lang.String fxycqk;
	/**具体情况及如何解决的*/
    private java.lang.String jtqkjj;
	/**是否与客户互动*/
    private java.lang.String khhd;
	/**具体互动内容*/
    private java.lang.String jtnr;
	/**备注未互动原因*/
    private java.lang.String whdyy;
	/**是否刷到平台好评*/
    private java.lang.String pthp;
	/**具体条数*/
    private java.lang.String jtjs;
	/**备注未刷到好评原因*/
    private java.lang.String whpyy;
	/**是否完成防扰民打卡*/
    private java.lang.String frmdk;
	/**打卡时间相机图片*/
    private java.lang.String dksjxjzp;
	/**备注未打卡原因*/
    private java.lang.String wdkyy;
	/**设备卫生是否异常*/
    private java.lang.String sbws;
	/**具体信息及赔偿情况(是)*/
    private java.lang.String jtxx;
	/**是否拍电表*/
    private java.lang.String tcSfpdb;
	/**退场电表1*/
    private java.lang.String tcSjdbt;
	/**退场电表2*/
    private java.lang.String tcSjdbt2;
	/**备注原因(否)*/
    private java.lang.String tcJtyy;
	/**是否有退场扯皮*/
    private java.lang.String tczp;
	/**备注详情(是)*/
    private java.lang.String bzxq;
	/**是否微笑送客户离开*/
    private java.lang.String wxskh;
	/**备注原因(否)*/
    private java.lang.String bzyyF;
	/**客户是否有建议和意见*/
    private java.lang.String khjy;
	/**备注具体内容(是)*/
    private java.lang.String bzjtnr;
	/**是否询问(否)*/
    private java.lang.String sfxw;
	/**租金*/
    private java.math.BigDecimal zj;
	/**押金*/
    private java.math.BigDecimal yj;
	/**转回定金*/
    private java.math.BigDecimal zhdj;
	/**电费*/
    private java.math.BigDecimal df;
	/**进场电度数*/
    private java.lang.Double jcdds;
	/**退场电度数*/
    private java.lang.Double tcdds;
	/**商品消费金额*/
    private java.math.BigDecimal spxf;
	/**商品附件表*/
    private java.lang.String spfjb;
	/**点餐消费金额*/
    private java.math.BigDecimal dcxf;
	/**餐饮成本*/
    private java.math.BigDecimal cycb;
	/**是否使用厨房*/
    private java.lang.String sfsycf;
	/**厨房使用费*/
    private java.math.BigDecimal cfsyf;
	/**超出人数*/
    private java.lang.Integer ccrs;
	/**人头费*/
    private java.math.BigDecimal rtf;
	/**转发优惠金额*/
    private java.math.BigDecimal zfyhje;
	/**转发证据截图*/
    private java.lang.String zfzjjt;
	/**卫生费*/
    private java.math.BigDecimal wsf;
	/**卫生赔偿截图*/
    private java.lang.String wspcjt;
	/**赔偿费*/
    private java.math.BigDecimal pcf;
	/**赔偿点截图*/
    private java.lang.String pcdjt;
	/**投保人数*/
    private java.lang.Integer tbrs;
	/**保险费*/
    private java.math.BigDecimal bxf;
	/**轰趴师收费*/
    private java.math.BigDecimal hpssf;
	/**策划费用*/
    private java.math.BigDecimal cfhy;
	/**其他费用*/
    private java.math.BigDecimal qtfy;
	/**其他费用详情*/
    private java.lang.String qtfyxq;
	/**费用总计*/
    private java.math.BigDecimal fyzj;
	/**应退押金*/
    private java.math.BigDecimal ytyj;
	/**转回公司*/
    private java.math.BigDecimal zfgs;
	/**是否接通电话*/
    private java.lang.String sfjtdh;
	/**未接通原因*/
    private java.lang.String wjtyy;
	/**消费金额是否一致*/
    private java.lang.String xfjesfyz;
	/**询问详细记录*/
    private java.lang.String xwxqjl;
	/**店长是否有误*/
    private java.lang.String dzsfyw;
	/**有误备注详情*/
    private java.lang.String ywbzxq;
	/**店长服务评价(1-5星)*/
    private java.lang.Integer fwpj;
	/**是否有建议和意见*/
    private java.lang.String sfyjyhyj;
	/**投诉*/
    private java.lang.String tsxqyy;
	/**建议*/
    private java.lang.String jyxq;
	/**是否为严重投诉*/
    private java.lang.String sfwyzts;
    /**进场提交时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date time;
    /**进场提交时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date time2;
    /**巡场服务提交时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date time3;
    /**退场提交时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date time4;
	/**订单状态(1:待跟单2:入场准备3:接待入场4:现场服务5:退场6:对账7:反馈8:完成)*/
    private java.lang.Integer status;
	/**订单id*/
    private java.lang.String xid;
	/**接单用户id*/
    private java.lang.String userId;
    /**是否收取场地费尾款*/
	private java.lang.String sfsqcdfwk;
    /**场地费尾款图片1*/
	private java.lang.String cdfwktp1;
    /**场地费尾款图片2*/
	private java.lang.String cdfwktp2;
    /**是否收取增值尾款*/
    private java.lang.String sfsqzzwk;
    /**增值尾款图片1*/
	private java.lang.String zzwktp1;
    /**增值尾款图片2*/
	private java.lang.String zzwktp2;
    /**转回公司图片*/
	private java.lang.String zhgstp;

    /**别墅门口道路停车图*/
    private java.lang.String tcpztp;

    /**是否避免厨房用火安全隐患(0:否,1:是)*/
    private java.lang.String bmcfyhaq;

    /**厨房照片1*/
    private java.lang.String cfzp1;

    /**厨房照片2*/
    private java.lang.String cfzp2;

    /**门窗图片*/
    private java.lang.String mctp;

    /**户外街道停车图*/
    private java.lang.String hwjdtct;

    /**大厅图片*/
    private java.lang.String dttp2;

    /**设备异常图片*/
    private java.lang.String sbyctp;

    /**设备异常赔偿金额*/
    private java.math.BigDecimal sbycpcje;

    /**设备异常赔偿金额*/
    private java.lang.Double cdfwkje;
    private java.lang.Double zzwkje;
    private java.lang.Double qyj;

}
