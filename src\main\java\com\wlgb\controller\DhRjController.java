package com.wlgb.controller;

import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service4.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 12:07
 * 大户人家
 */
@RestController
@RequestMapping(value = "/dhrj/api")
public class DhRjController {
    @Autowired
    private DhRjService dhRjService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private DhrjXydService dhrjXydService;
    @Autowired
    private DhrjVillaService dhrjVillaService;
    @Autowired
    private DhrjKhqdService dhrjKhqdService;
    @Autowired
    private DhrjKhtypeService dhrjKhtypeService;
    @Autowired
    private DhRjXydLogService dhRjXydLogService;
    @Autowired
    private DhRjOssFileService dhRjOssFileService;
    @Autowired
    private DingDingTokenService dingDingTokenService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;


    /**
     * 撞单判断
     */
    @RequestMapping(value = "pdDdSfCz")
    public Result pdDdSfCz(HttpServletRequest request) {
        String xbsmc = request.getParameter("xbsmc");
        if (xbsmc == null || "".equals(xbsmc)) {
            return Result.error("别墅空的");
        }
        String xddbh = request.getParameter("xddbh");
        if (xddbh == null || "".equals(xddbh)) {
            return Result.error("订单编号空的");
        }
        String xjctime = request.getParameter("xjctime");
        String xtctime = request.getParameter("xtctime");

        DhrjXyd xyd = new DhrjXyd();
        xyd.setXbsmc(xbsmc);
        if (xjctime != null && !"".equals(xjctime)) {
            Calendar c = Calendar.getInstance();
            c.setTimeInMillis(Long.parseLong(xjctime));
            xyd.setXjctime(c.getTime());
        } else {
            return Result.error("进场时间不能为空！");
        }
        if (xtctime != null && !"".equals(xtctime)) {
            Calendar c = Calendar.getInstance();
            c.setTimeInMillis(Long.parseLong(xtctime));
            xyd.setXtctime(c.getTime());
        } else {
            return Result.error("退场时间不能为空！");
        }

        DhrjXyd dhrjXyd = new DhrjXyd();
        dhrjXyd.setXddbh(xddbh);
        DhrjXyd dhRjXyd = dhrjXydService.queryDhRjXydByDhRjXyd(dhrjXyd);
        if (dhRjXyd != null) {
            xyd.setId(dhRjXyd.getId());
            if (!xyd.getXbsmc().equals(dhRjXyd.getXbsmc())) {
                xyd.setId(null);
            }
            xyd.setXsfsc(dhRjXyd.getXsfsc());
        } else {
            xyd.setXsfsc(0);
        }

        if (xyd.getXjctime().getTime() >= xyd.getXtctime().getTime()) {
            return Result.error("退场时间不能早于进场时间！");
        }

        if (xyd.getXsfsc() == 0) {
            Integer countXydSfZd = dhRjService.queryCountDhRjXydSfCz(xyd);
            System.out.println(countXydSfZd);
            if (countXydSfZd > 0) {
                return Result.error("该门店此时间段撞单了！");
            }
        }


        return Result.OK();
    }

    /**
     * 下单提交
     */
    @RequestMapping(value = "saveXyd")
    public Result saveXyd(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        String userId = request.getParameter("userId");
        String formInstId = request.getParameter("formInstId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        DhrjXyd dhrjXyd = JSONObject.toJavaObject(jsonObject, DhrjXyd.class);
        if (dhrjXyd.getXjctime().getTime() >= dhrjXyd.getXtctime().getTime()) {
            return Result.error("退场时间不能早于进场时间！");
        }
        Integer sfCz = dhRjService.queryCountDhRjXydSfCz(dhrjXyd);
        if (sfCz > 0) {
            return Result.error("该门店此时间段撞单了！");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userId", userId);
        paramMap.put("formInstId", formInstId);
        paramMap.put("uuid", IdConfig.uuId());
        paramMap.put("sfxyd", "1");
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/dhrj/task/saveXydTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 改单
     */
    @RequestMapping(value = "xydEdit")
    public Result xydEdit(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return Result.error("datas空的");
        }
        String userId = request.getParameter("userId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        DhrjXyd dhrjXyd = JSONObject.toJavaObject(jsonObject, DhrjXyd.class);

        DhrjXyd dhrjXyd1 = new DhrjXyd();
        dhrjXyd1.setXddbh(dhrjXyd.getXddbh());
        dhrjXyd1.setXsfsc(0);
        DhrjXyd dhrjXyd2 = dhrjXydService.queryDhRjXydByDhRjXyd(dhrjXyd1);
        if (dhrjXyd2 == null) {
            return Result.error("订单不存在");
        }
        if (dhrjXyd.getXjctime().getTime() >= dhrjXyd.getXtctime().getTime()) {
            return Result.error("退场时间不能早于进场时间！");
        }
        Integer sfCz = dhRjService.queryCountDhRjXydSfCz(dhrjXyd);
        if (sfCz > 0) {
            return Result.error("该门店此时间段撞单了！");
        }
        if (dhrjXyd2.getXapi() == 1) {
            DhrjXyd dhrjXyd3 = new DhrjXyd();
            dhrjXyd3.setId(dhrjXyd2.getId());
            dhrjXyd3.setXapi(0);
            dhrjXydService.updateById(dhrjXyd3);

            return Result.OK();
        }
        String formInstId = request.getParameter("formInstId");

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userId", userId);
        paramMap.put("formInstId", formInstId);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/dhrj/task/xydEditTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }


        return Result.OK();
    }

    /**
     * 删单
     */
    @RequestMapping(value = "xydDel")
    public Result xydDel(HttpServletRequest request) {
        String userId = request.getParameter("userId");
        String xddbh = request.getParameter("xddbh");
        if (xddbh == null || "".equals(xddbh)) {
            return Result.error("订单编号空的！");
        }
        DhrjXyd dhrjXyd = new DhrjXyd();
        dhrjXyd.setXsfsc(0);
        dhrjXyd.setXddbh(xddbh);
        DhrjXyd dhRjXyd = dhrjXydService.queryDhRjXydByDhRjXyd(dhrjXyd);
        if (dhRjXyd == null) {
            return Result.error("订单不存在");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("userId", userId);
        paramMap.put("xddbh", xddbh);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/dhrj/task/xydDelTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 删单校验
     */
    @RequestMapping(value = "scReg")
    public Result scReg(HttpServletRequest request) {
        String xddbh = request.getParameter("xddbh");
        if (xddbh == null || "".equals(xddbh)) {
            return Result.error("订单编号空的！");
        }
        DhrjXyd dhrjXyd = new DhrjXyd();
        dhrjXyd.setXsfsc(0);
        dhrjXyd.setXddbh(xddbh);
        DhrjXyd dhRjXyd = dhrjXydService.queryDhRjXydByDhRjXyd(dhrjXyd);
        if (dhRjXyd == null) {
            return Result.error("订单不存在");
        }

        return Result.OK();
    }

    /**
     * 门店提交
     */
    @RequestMapping(value = "mdSave")
    public Result mdSave(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        DhrjVilla dhrjVilla = new DhrjVilla();
        dhrjVilla.setMdbh(jsonObject.getString("textField_lnwu6odz"));
        dhrjVilla.setCity(jsonObject.getString("selectField_lnwu6oe1"));
        dhrjVilla.setVname(jsonObject.getString("textField_liwru1b6"));
        dhrjVilla.setBz(jsonObject.getString("textareaField_liwru1b8"));
        dhrjVilla.setVxz(jsonObject.getString("radioField_lomdc4qe"));
        //值班管家
        try {
            String mdgj = xzJq(jsonObject.getString("employeeField_lnya9va5"));
            dhrjVilla.setZbdzid(mdgj);
            if (mdgj != null && !"".equals(mdgj)) {
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(mdgj);
                if (dingdingEmployee != null) {
                    dhrjVilla.setZbdz(dingdingEmployee.getName());
                    dhrjVilla.setZbdzid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(mdgj);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            dhrjVilla.setZbdz(dingdingEmployee2.getName());
                            dhrjVilla.setZbdzid(dingdingEmployee2.getUserid());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        dhrjVillaService.save(dhrjVilla);

        return Result.OK();
    }

    /**
     * 门店修改
     */
    @RequestMapping(value = "mdEdit")
    public Result mdEdit(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);

        String mdbh = jsonObject.getString("textField_lnwu6odz");
        DhrjVilla dhrjVilla1 = new DhrjVilla();
        dhrjVilla1.setMdbh(mdbh);
        dhrjVilla1.setVsfsc(0);
        DhrjVilla dhrjVilla2 = dhrjVillaService.queryDhrjVillaByDhrjVilla(dhrjVilla1);
        if (dhrjVilla2 == null) {
            return Result.error("门店不存在");
        }

        DhrjVilla dhrjVilla = new DhrjVilla();
        dhrjVilla.setId(dhrjVilla2.getId());
        dhrjVilla.setCity(jsonObject.getString("selectField_lnwu6oe1"));
        dhrjVilla.setVname(jsonObject.getString("textField_liwru1b6"));
        dhrjVilla.setBz(jsonObject.getString("textareaField_liwru1b8"));
        dhrjVilla.setVxz(jsonObject.getString("radioField_lomdc4qe"));
        //值班管家
        try {
            String mdgj = jsonObject.getString("employeeField_lnya9va5");
            if (mdgj != null && !"".equals(mdgj)) {
                mdgj = xzJq(mdgj);
                dhrjVilla.setZbdzid(mdgj);
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(mdgj);
                if (dingdingEmployee != null) {
                    dhrjVilla.setZbdz(dingdingEmployee.getName());
                    dhrjVilla.setZbdzid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(mdgj);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            dhrjVilla.setZbdz(dingdingEmployee2.getName());
                            dhrjVilla.setZbdzid(dingdingEmployee2.getUserid());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        dhrjVillaService.updateById(dhrjVilla);

        return Result.OK();
    }

    /**
     * 门店删除
     */
    @RequestMapping(value = "mdDel")
    public Result mdDel(HttpServletRequest request) {
        String mdbh = request.getParameter("mdbh");
        if (mdbh == null || "".equals(mdbh)) {
            return Result.error("datas空的");
        }
        DhrjVilla dhrjVilla1 = new DhrjVilla();
        dhrjVilla1.setMdbh(mdbh);
        dhrjVilla1.setVsfsc(0);
        DhrjVilla dhrjVilla2 = dhrjVillaService.queryDhrjVillaByDhrjVilla(dhrjVilla1);
        if (dhrjVilla2 == null) {
            return Result.error("门店不存在");
        }

        DhrjVilla dhrjVilla = new DhrjVilla();
        dhrjVilla.setId(dhrjVilla2.getId());
        dhrjVilla.setVsfsc(1);

        dhrjVillaService.updateById(dhrjVilla);

        return Result.OK();
    }

    /**
     * 渠道提交
     */
    @RequestMapping(value = "qdSave")
    public Result qdSave(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        DhrjKhqd dhrjKhqd = new DhrjKhqd();
        dhrjKhqd.setQdbh(jsonObject.getString("textField_lnwu6odz"));
        dhrjKhqd.setQdname(jsonObject.getString("textField_liwrqanc"));
        dhrjKhqd.setBz(jsonObject.getString("textareaField_liwrqane"));

        dhrjKhqdService.save(dhrjKhqd);
        return Result.OK();
    }

    /**
     * 渠道修改
     */
    @RequestMapping(value = "qdEdit")
    public Result qdEdit(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);

        String qdbh = jsonObject.getString("textField_lnwu6odz");
        DhrjKhqd dhrjKhqd1 = new DhrjKhqd();
        dhrjKhqd1.setQdbh(qdbh);
        dhrjKhqd1.setSfsc(0);

        DhrjKhqd dhrjKhqd2 = dhrjKhqdService.queryDhrjKhqdByDhrjKhqd(dhrjKhqd1);
        if (dhrjKhqd2 == null) {
            return Result.error("渠道不存在");
        }
        DhrjKhqd dhrjKhqd = new DhrjKhqd();
        dhrjKhqd.setId(dhrjKhqd2.getId());
        dhrjKhqd.setQdname(jsonObject.getString("textField_liwrqanc"));
        dhrjKhqd.setBz(jsonObject.getString("textareaField_liwrqane"));

        dhrjKhqdService.updateById(dhrjKhqd);

        return Result.OK();
    }

    /**
     * 渠道删除
     */
    @RequestMapping(value = "qdDel")
    public Result qdDel(HttpServletRequest request) {
        String qdbh = request.getParameter("qdbh");
        if (qdbh == null || "".equals(qdbh)) {
            return Result.error("datas空的");
        }
        DhrjKhqd dhrjKhqd1 = new DhrjKhqd();
        dhrjKhqd1.setQdbh(qdbh);
        dhrjKhqd1.setSfsc(0);

        DhrjKhqd dhrjKhqd2 = dhrjKhqdService.queryDhrjKhqdByDhrjKhqd(dhrjKhqd1);
        if (dhrjKhqd2 == null) {
            return Result.error("渠道不存在");
        }

        DhrjKhqd dhrjKhqd = new DhrjKhqd();
        dhrjKhqd.setId(dhrjKhqd2.getId());
        dhrjKhqd.setSfsc(1);
        dhrjKhqdService.updateById(dhrjKhqd);

        return Result.OK();
    }

    /**
     * 聚会类型提交
     */
    @RequestMapping(value = "lxSave")
    public Result lxSave(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        DhrjKhtype dhrjKhtype = new DhrjKhtype();
        dhrjKhtype.setLxbh(jsonObject.getString("textField_lnwu6odz"));
        dhrjKhtype.setLxname(jsonObject.getString("textField_liws59fm"));
        dhrjKhtype.setBz(jsonObject.getString("textareaField_liws59fo"));

        dhrjKhtypeService.save(dhrjKhtype);
        return Result.OK();
    }

    /**
     * 聚会类型修改
     */
    @RequestMapping(value = "lxEdit")
    public Result lxEdit(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);

        String lxbh = jsonObject.getString("textField_lnwu6odz");
        DhrjKhtype dhrjKhtype1 = new DhrjKhtype();
        dhrjKhtype1.setLxbh(lxbh);
        dhrjKhtype1.setSfsc(0);

        DhrjKhtype dhrjKhtype2 = dhrjKhtypeService.queryDhrjKhtypeByDhrjKhtype(dhrjKhtype1);
        if (dhrjKhtype2 == null) {
            return Result.error("聚会类型不存在");
        }

        DhrjKhtype dhrjKhtype = new DhrjKhtype();
        dhrjKhtype.setId(dhrjKhtype2.getId());
        dhrjKhtype.setLxname(jsonObject.getString("textField_liws59fm"));
        dhrjKhtype.setBz(jsonObject.getString("textareaField_liws59fo"));

        dhrjKhtypeService.updateById(dhrjKhtype);

        return Result.OK();
    }

    /**
     * 聚会类型删除
     */
    @RequestMapping(value = "lxDel")
    public Result lxDel(HttpServletRequest request) {
        String lxbh = request.getParameter("lxbh");
        if (lxbh == null || "".equals(lxbh)) {
            return Result.error("datas空的");
        }
        DhrjKhtype dhrjKhtype1 = new DhrjKhtype();
        dhrjKhtype1.setLxbh(lxbh);
        dhrjKhtype1.setSfsc(0);

        DhrjKhtype dhrjKhtype2 = dhrjKhtypeService.queryDhrjKhtypeByDhrjKhtype(dhrjKhtype1);
        if (dhrjKhtype2 == null) {
            return Result.error("聚会类型不存在");
        }

        DhrjKhtype dhrjKhtype = new DhrjKhtype();
        dhrjKhtype.setId(dhrjKhtype2.getId());
        dhrjKhtype.setSfsc(1);
        dhrjKhtypeService.updateById(dhrjKhtype);

        return Result.OK();
    }

    /**
     * 触发协议单修改
     */
    @RequestMapping(value = "cfEditXyd")
    public Result cfEditXyd(HttpServletRequest request) {
        String userId = request.getParameter("userId");
        String formInstId = request.getParameter("formInstId");
        if (formInstId == null || "".equals(formInstId)) {
            return Result.error("实例id空的");
        }
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String xfd = jsonObject.getString("xfd");
        if (xfd != null && !"".equals(xfd)) {
            //针对手机用户提交
            if (!xfd.contains("$")) {
                xfd = "ding968d75cfe0d9045c4ac5d6980864d335$" + xfd;
            }
        }
        jsonObject.put("xfd", xfd);
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_WGE8YPM32BUH1BPZD6N4");
        ydAppkey.setToken("E8866MB1GLNB3ME6AG8TX8LNXWV62CUEXQWIL22");
        DingDingToken dingDingToken = dhRjService.queryDingDingTokenByName("大户人家crm");
        if (dingDingToken != null) {
            if (new Date().getTime() > dingDingToken.getSxtime().getTime()) {
                String token = gxDingDingToken();
                dingDingToken.setToken(token);
            }
            //发送添加成功通知
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = YdConfig.xgBdSl(dingDingToken.getToken(), ydAppkey, "yida_pub_account", formInstId, jsonObject.toJSONString());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (gatewayResult == null || !gatewayResult.getSuccess()) {
                return Result.error("宜搭接口返回了失败");
            }
        }

        DingdingEmployee ding = null;
        if (userId != null && !"".equals(userId)) {
            String userId1 = userId;
            //针对手机用户提交
            if (!userId.contains("$")) {
                userId = "ding968d75cfe0d9045c4ac5d6980864d335$" + userId;
            }
            DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(userId);
            if (dingdingEmployee != null) {
                ding = dingdingEmployee;
            } else {
                DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                if (dingdingEmployee1 != null) {
                    DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                    if (dingdingEmployee2 != null) {
                        ding = dingdingEmployee2;
                    }
                }
            }
        }

        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXltext("改单");
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXlxid(jsonObject.getString("xddbh"));

        dhRjXydLogService.save(wlgbXydLog);

        return Result.OK();
    }

    /**
     * 触发协议单删除
     */
    @RequestMapping(value = "cfDeleteXyd")
    public Result cfDeleteXyd(HttpServletRequest request) {
        String userId = request.getParameter("userId");
        String formInstId = request.getParameter("formInstId");
        String xddbh = request.getParameter("xddbh");
        if (formInstId == null || "".equals(formInstId)) {
            return Result.error("实例id空的");
        }
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_WGE8YPM32BUH1BPZD6N4");
        ydAppkey.setToken("E8866MB1GLNB3ME6AG8TX8LNXWV62CUEXQWIL22");
        String scBdSl = YdConfig.scBdSl(ydAppkey.getAppkey(), ydAppkey.getToken(), formInstId);
        JSONObject jsonObject = JSONObject.parseObject(scBdSl);
        if (jsonObject != null && jsonObject.size() > 0) {
            if (!jsonObject.getBoolean("success")) {
                return Result.error("宜搭接口返回了失败");
            }
        }
        System.out.println(scBdSl);

        DingdingEmployee ding = null;
        if (userId != null && !"".equals(userId)) {
            String userId1 = userId;
            //针对手机用户提交
            if (!userId.contains("$")) {
                userId = "ding968d75cfe0d9045c4ac5d6980864d335$" + userId;
            }
            DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(userId);
            if (dingdingEmployee != null) {
                ding = dingdingEmployee;
            } else {
                DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                if (dingdingEmployee1 != null) {
                    DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                    if (dingdingEmployee2 != null) {
                        ding = dingdingEmployee2;
                    }
                }
            }
        }

        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXltext("删单");
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXlxid(xddbh);

        dhRjXydLogService.save(wlgbXydLog);
        return Result.OK();
    }

    /**
     * 选人截取
     *
     * @param userid 用户id
     * @return 用户id
     */
    private String xzJq(String userid) {
        String s = userid.substring(0, 1);
        if ("[".equals(s)) {
            userid = userid.substring(2, userid.length() - 2);
        }
        return userid;
    }

    /**
     * 获取星期几
     */
    public static String getWeek(Date date) {
        String[] weeks = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int week_index = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (week_index < 0) {
            week_index = 0;
        }

        return weeks[week_index];
    }

    @RequestMapping(value = "scDzXyd")
    public Result scDzXyd(String ddbh) {
        DhrjXyd dhrjXyd = new DhrjXyd();
        dhrjXyd.setXddbh(ddbh);
        DhrjXyd dhrjXyd1 = dhrjXydService.queryDhRjXydByDhRjXyd(dhrjXyd);
        try {
            Map<String, Object> xydMap = dhRjDzXyd(dhrjXyd1);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    @RequestMapping(value = "scDzXyd1")
    public Result scDzXyd1(String ddbh) {
        DhrjXyd dhrjXyd = new DhrjXyd();
        dhrjXyd.setXddbh(ddbh);
        DhrjXyd dhrjXyd1 = dhrjXydService.queryDhRjXydByDhRjXyd(dhrjXyd);
        DhrjVilla dhrjVilla = new DhrjVilla();
        dhrjVilla.setMdbh(dhrjXyd1.getXbsmc());
        DhrjVilla dhrjVilla1 = dhrjVillaService.queryDhrjVillaByDhrjVilla(dhrjVilla);
        try {
            Map<String, Object> xydMap = dhRjYhDzXyd(dhrjXyd1, dhrjVilla1);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    /**
     * 生成电子协议单
     *
     * @param xyd 协议单数据
     */
    private Map<String, Object> dhRjYhDzXyd(DhrjXyd xyd, DhrjVilla dhrjVilla1) throws Exception {
        DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(dhrjVilla1 != null ? dhrjVilla1.getZbdzid() : "不存在");
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy年MM月dd日");
        String zpTime = formatter1.format(xyd.getXjctime()) + ("1".equals(xyd.getCc()) ? " 午餐" : "2".equals(xyd.getCc()) ? "晚餐" : "3".equals(xyd.getCc()) ? "晚晚场" : "其他");
        //除场地费之外的其他费用
        Map<String, Object> map = new HashMap<>();
        //订单编号
        map.put("ddbh", xyd.getXddbh());
        //别墅名字
        map.put("bsName", xyd.getXbsmc2());
        //房东姓名
        map.put("fdName", xyd.getXfd());
        //房东电话
        map.put("fdTelphone", xyd.getXfddh());
        //租客姓名
        map.put("zkName", xyd.getXzk());
        //租客电话
        map.put("zkTelphone", xyd.getXzkdh());
        //租客身份证号码
        map.put("zksfz", xyd.getXzksfz());
        //公司名称（性质）
        if (xyd.getXdwmc() != null && !"".equals(xyd.getXdwmc())) {
            map.put("dwmc", xyd.getXdwmc() + (xyd.getXtdxz() != null ? "(" + xyd.getXtdxz() + ")" : ""));
        } else {
            map.put("dwmc", (xyd.getXtdxz() != null ? xyd.getXtdxz() : ""));
        }

        //客户来源
        map.put("khly", xyd.getXkhly());
        //租赁时间（进场时间与退场时间）
        map.put("zpTime", zpTime);
        //进场时间
        map.put("jxtime", "进场:" + DateFormatConfig.df1(xyd.getXjctime()));
        //退场时间
        map.put("tctime", "退场:" + DateFormatConfig.df1(xyd.getXtctime()));
        //人数
        map.put("rs", (xyd.getXrs() != null ? xyd.getXrs() : 0) + "桌");
        //超出人数加收
        map.put("ccMoney", xyd.getXcudrfy());
        //支付宝订单号后六位
        String hlw = "";
        if (xyd.getXzzhlw() != null && !"".equals(xyd.getXzzhlw()) && xyd.getXzzhlw().length() > 0) {
            if (xyd.getXzzhlw().length() > 6) {
                hlw = xyd.getXzzhlw().substring(xyd.getXzzhlw().length() - 6);
            } else {
                hlw = xyd.getXzzhlw();
            }
        }
        //支付宝订单号后两位
        map.put("zfbddh", hlw);
        //支付宝转账定金时间
        map.put("zfbzzTime", xyd.getXzzsj() != null ? DateFormatConfig.df1(xyd.getXzzsj()) : "");
        //场地租赁费原价
        map.put("qkje", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0));
        //场地优惠后的价
        map.put("hfyj", (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0));
        //定金
        map.put("dj", xyd.getXysdj() != null ? xyd.getXysdj() : 0);
        //单点餐
        map.put("dcze", (xyd.getXrs() != null ? xyd.getXrs() : 0) * (xyd.getXcudrfy() != null ? xyd.getXcudrfy() : 0));
        //招待经理服务
        map.put("zdjlfw", xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0);
        //策划布景
        map.put("chbj", xyd.getXchbjje() != null ? xyd.getXchbjje() : 0);
        //轰趴师主持人
        map.put("hps", xyd.getXhpsje() != null ? xyd.getXhpsje() : 0);
        //定做团建横幅
        map.put("tjhf", xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0);
        //酒水商品套餐
        map.put("jssp", xyd.getXjsspje() != null ? xyd.getXjsspje() : 0);
        //延时费
        map.put("ysf", xyd.getXysf() != null ? xyd.getXysf() : 0);
        //总金额(场地费+（人数*人头单价）+招待经理服务+策划布景+轰趴师主持人+定做团建横幅+酒水商品套餐)---去除单点餐
//        double sum = (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) + (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0) + (xyd.getXchbjje() != null ? xyd.getXchbjje() : 0) + (xyd.getXhpsje() != null ? xyd.getXhpsje() : 0) + (xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0) + (xyd.getXjsspje() != null ? xyd.getXjsspje() : 0)+(xyd.getXysf() != null ? xyd.getXysf() : 0);
        double sum = (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) + (xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0) + (xyd.getXchbjje() != null ? xyd.getXchbjje() : 0) + (xyd.getXhpsje() != null ? xyd.getXhpsje() : 0) + (xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0) + (xyd.getXjsspje() != null ? xyd.getXjsspje() : 0) + (xyd.getXysf() != null ? xyd.getXysf() : 0) + (xyd.getXrs() != null ? xyd.getXrs() : 0) * (xyd.getXcudrfy() != null ? xyd.getXcudrfy() : 0);
        map.put("zje", sum);
        //进场前台支付金额
        map.put("qtzfje", xyd.getXkhjczfje() != null ? xyd.getXkhjczfje() : 0);
        //备注
        map.put("bzxx", xyd.getXsxbz() != null && !"".equals(xyd.getXsxbz()) ? xyd.getXsxbz() : "无");
        //代码
        String dm = "";
        if (xyd.getXdm() != null && !"".equals(xyd.getXdm()) && xyd.getXdm().length() > 0) {
            if (xyd.getXdm().length() > 6) {
                dm = xyd.getXdm().substring(0, 6);
            } else {
                dm = xyd.getXdm();
            }
        }
        map.put("dm", dm);
        //付款码
        String fkm = "";
        if (xyd.getXfkm() != null && !"".equals(xyd.getXfkm()) && xyd.getXfkm().length() > 0) {
            if (xyd.getXfkm().length() > 4) {
                fkm = xyd.getXfkm().substring(0, 4);
            } else {
                fkm = xyd.getXfkm();
            }
        }
        map.put("fkm", fkm);
        //值班店长
        map.put("zbdzxm", dingdingEmployee != null ? dingdingEmployee.getName() : "");
        //值班店长电话
        map.put("zbdzdh", dingdingEmployee != null ? dingdingEmployee.getMobile() : "");
        //是否现场成交
        map.put("sfxccj", xyd.getXisxzcj() != null && !"".equals(xyd.getXisxzcj()) ? xyd.getXisxzcj() : "否");
        //是否泳池单
        map.put("sfycd", xyd.getXsfycd() != null && !"".equals(xyd.getXsfycd()) ? xyd.getXsfycd() : "否");
        //甲方姓名
        map.put("jfxm", xyd.getXfd());
        //乙方姓名
        map.put("yfxm", xyd.getXzk());
        //日期
        map.put("rq", formatter1.format(xyd.getXsendtime()));

        //模板
        String tempName = FileConfig.getFileAbsolutePath2("static" + File.separator + "dhRjYhXyd.html");
        String context = PDFUtil.freeMarkerRender(map, tempName);
        String id = "DHRJXYD" + DateFormatConfig.df2(new Date()) + IdConfig.uuId();
        String pdf = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
        String png = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".png");
        File newPdf = new File(pdf);
        //生成pdf
        PDFUtil.createPdfDhRJXyd(context, newPdf.getPath());
        //生成图片
        PDFUtil.pdfToImg(newPdf.getPath(), 1, png);
        Map<String, Object> map1 = new HashMap<>();
        map1.put("png", id + ".png");
        //删除pdf临时文件
        newPdf.delete();
        return map1;
    }


    /**
     * 生成电子协议单
     *
     * @param xyd 协议单数据
     */
    private Map<String, Object> dhRjDzXyd(DhrjXyd xyd) throws Exception {
        DhrjVilla dhrjVilla = new DhrjVilla();
        dhrjVilla.setMdbh(xyd.getXbsmc());
        DhrjVilla dhrjVilla1 = dhrjVillaService.queryDhrjVillaByDhrjVilla(dhrjVilla);
        DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(dhrjVilla1 != null ? dhrjVilla1.getZbdzid() : "不存在");
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy年MM月dd日");
        String zpTime = "进场：" + DateFormatConfig.df1(xyd.getXjctime()) + "<br />退场：" + DateFormatConfig.df1(xyd.getXtctime());
        //除场地费之外的其他费用
        Map<String, Object> map = new HashMap<>();
        //订单编号
        map.put("ddbh", xyd.getXddbh());
        //进场时间
        map.put("jxtime", DateFormatConfig.df1(xyd.getXjctime()));
        //退场时间
        map.put("tctime", DateFormatConfig.df1(xyd.getXtctime()));
        //别墅名字
        map.put("bsName", xyd.getXbsmc2());
        //房东姓名
        map.put("fdName", xyd.getXfd());
        //房东电话
        map.put("fdTelphone", xyd.getXfddh());
        //租客姓名
        map.put("zkName", xyd.getXzk());
        //租客电话
        map.put("zkTelphone", xyd.getXzkdh());
        //租客身份证号码
        map.put("zksfz", xyd.getXzksfz());
        //公司名称（性质）
        if (xyd.getXdwmc() != null && !"".equals(xyd.getXdwmc())) {
            map.put("dwmc", xyd.getXdwmc() + (xyd.getXtdxz() != null ? "(" + xyd.getXtdxz() + ")" : ""));
        } else {
            map.put("dwmc", (xyd.getXtdxz() != null ? xyd.getXtdxz() : ""));
        }

        //客户来源
        map.put("khly", xyd.getXkhly());
        //租赁时间（进场时间与退场时间）
        map.put("zpTime", zpTime);
        //人数
        map.put("rs", (xyd.getXrs() != null ? xyd.getXrs() : 0) + "人");
        //超出人数加收
        map.put("ccMoney", xyd.getXcudrfy());
        //支付宝订单号后六位
        String hlw = "";
        if (xyd.getXzzhlw() != null && !"".equals(xyd.getXzzhlw()) && xyd.getXzzhlw().length() > 0) {
            if (xyd.getXzzhlw().length() > 6) {
                hlw = xyd.getXzzhlw().substring(xyd.getXzzhlw().length() - 6);
            } else {
                hlw = xyd.getXzzhlw();
            }
        }
        //支付宝订单号后两位
        map.put("zfbddh", hlw);
        //支付宝转账定金时间
        map.put("zfbzzTime", xyd.getXzzsj() != null ? DateFormatConfig.df1(xyd.getXzzsj()) : "");
        //场地租赁费原价
        map.put("qkje", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0));
        //场地优惠后的价
        map.put("hfyj", (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0));
        //定金
        map.put("dj", xyd.getXysdj() != null ? xyd.getXysdj() : 0);
        //单点餐
        map.put("dcze", (xyd.getXrs() != null ? xyd.getXrs() : 0) * (xyd.getXcudrfy() != null ? xyd.getXcudrfy() : 0));
        //招待经理服务
        map.put("zdjlfw", xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0);
        //策划布景
        map.put("chbj", xyd.getXchbjje() != null ? xyd.getXchbjje() : 0);
        //轰趴师主持人
        map.put("hps", xyd.getXhpsje() != null ? xyd.getXhpsje() : 0);
        //定做团建横幅
        map.put("tjhf", xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0);
        //酒水商品套餐
        map.put("jssp", xyd.getXjsspje() != null ? xyd.getXjsspje() : 0);
        //延时费
        map.put("ysf", xyd.getXysf() != null ? xyd.getXysf() : 0);
        //总金额(场地费+单点餐+招待经理服务+策划布景+轰趴师主持人+定做团建横幅+酒水商品套餐)---去除单点餐
//        double sum = (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) + (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0) + (xyd.getXchbjje() != null ? xyd.getXchbjje() : 0) + (xyd.getXhpsje() != null ? xyd.getXhpsje() : 0) + (xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0) + (xyd.getXjsspje() != null ? xyd.getXjsspje() : 0)+(xyd.getXysf() != null ? xyd.getXysf() : 0);
        double sum = (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) + (xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0) + (xyd.getXchbjje() != null ? xyd.getXchbjje() : 0) + (xyd.getXhpsje() != null ? xyd.getXhpsje() : 0) + (xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0) + (xyd.getXjsspje() != null ? xyd.getXjsspje() : 0) + (xyd.getXysf() != null ? xyd.getXysf() : 0) + (xyd.getXrs() != null ? xyd.getXrs() : 0) * (xyd.getXcudrfy() != null ? xyd.getXcudrfy() : 0);
        map.put("zje", sum);
        //进场前台支付金额
        map.put("qtzfje", xyd.getXkhjczfje() != null ? xyd.getXkhjczfje() : 0);
        //备注
        map.put("bzxx", xyd.getXsxbz() != null && !"".equals(xyd.getXsxbz()) ? xyd.getXsxbz() : "无");
        //代码
        String dm = "";
        if (xyd.getXdm() != null && !"".equals(xyd.getXdm()) && xyd.getXdm().length() > 0) {
            if (xyd.getXdm().length() > 6) {
                dm = xyd.getXdm().substring(0, 6);
            } else {
                dm = xyd.getXdm();
            }
        }
        map.put("dm", dm);
        //付款码
        String fkm = "";
        if (xyd.getXfkm() != null && !"".equals(xyd.getXfkm()) && xyd.getXfkm().length() > 0) {
            if (xyd.getXfkm().length() > 4) {
                fkm = xyd.getXfkm().substring(0, 4);
            } else {
                fkm = xyd.getXfkm();
            }
        }
        map.put("fkm", fkm);
        //值班店长
        map.put("zbdzxm", dingdingEmployee != null ? dingdingEmployee.getName() : "");
        //值班店长电话
        map.put("zbdzdh", dingdingEmployee != null ? dingdingEmployee.getMobile() : "");
        //是否现场成交
        map.put("sfxccj", xyd.getXisxzcj() != null && !"".equals(xyd.getXisxzcj()) ? xyd.getXisxzcj() : "否");
        //是否泳池单
        map.put("sfycd", xyd.getXsfycd() != null && !"".equals(xyd.getXsfycd()) ? xyd.getXsfycd() : "否");
        //甲方姓名
        map.put("jfxm", xyd.getXfd());
        //乙方姓名
        map.put("yfxm", xyd.getXzk());
        //日期
        map.put("rq", formatter1.format(xyd.getXsendtime()));

        //模板
        String tempName = FileConfig.getFileAbsolutePath2("static" + File.separator + "dhRjXyd.html");
        String context = PDFUtil.freeMarkerRender(map, tempName);
        String id = "DHRJXYD" + DateFormatConfig.df2(new Date()) + IdConfig.uuId();
        String pdf = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
        String png = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".png");
        File newPdf = new File(pdf);
        //生成pdf
        PDFUtil.createPdfDhRJXyd(context, newPdf.getPath());
        //生成图片
        PDFUtil.pdfToImg(newPdf.getPath(), 1, png);
        Map<String, Object> map1 = new HashMap<>();
        map1.put("png", id + ".png");
        //删除pdf临时文件
        newPdf.delete();
        return map1;
    }

    public String gxDingDingToken() {
        String token = null;
        Dingkey dingkey = dhRjService.queryDingKeyById("dgrhcrm");
        if (dingkey != null) {
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            System.out.println(token);
            DingDingToken dingDingToken = dhRjService.queryDingDingTokenByName(dingkey.getName());
            if (dingDingToken != null) {
                DingDingToken dingDingToken1 = new DingDingToken();
                dingDingToken1.setToken(token);
                dingDingToken1.setId(dingDingToken.getId());
                Date date = new Date();
                date.setMinutes(date.getMinutes() + 20);
                dingDingToken1.setSxtime(date);
                dingDingTokenService.updateById(dingDingToken1);
            } else {
                DingDingToken dingDingToken1 = new DingDingToken();
                BeanUtils.copyProperties(dingkey, dingDingToken1);
                dingDingToken1.setToken(token);
                dingDingTokenService.save(dingDingToken1);
            }
        }

        return token;
    }
}
