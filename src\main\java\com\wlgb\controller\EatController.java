package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wlgb.config.*;
import com.wlgb.entity.WlgbEatBsjl;
import com.wlgb.entity.WlgbEatDbjl;
import com.wlgb.entity.WlgbEatRkjl;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service.WlgbEatBsjlService;
import com.wlgb.service.WlgbEatDbjlService;
import com.wlgb.service.WlgbEatRkjlService;
import com.wlgb.service2.WeiLianService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/12 16:19
 */
@RestController
@RequestMapping(value = "/wlgb/eat")
@Slf4j
public class EatController {
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private WlgbEatRkjlService wlgbEatRkjlService;
    @Autowired
    private WlgbEatDbjlService wlgbEatDbjlService;
    @Autowired
    private WlgbEatBsjlService wlgbEatBsjlService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;

    /**
     * 提交报损记录
     */
    @RequestMapping(value = "tjBsJl")
    public Result tjBsJl(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String lsh = jsonObject.getString("serialNumberField_lnmw29pj");
        String tjr = jsonObject.getString("employeeField_lnl4lchs");
        if (tjr != null && !"".equals(tjr)) {
            tjr = xzjq(tjr);
        }
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(tjr);
        Double bsZje = jsonObject.getDouble("numberField_lnl4lchr");
        String bz = jsonObject.getString("textareaField_lnl4lchw");
        JSONArray jsonArray = jsonObject.getJSONArray("tableField_lnl4lchl");
        for (Object json : jsonArray) {
            JSONObject l = (JSONObject) json;
            WlgbEatBsjl wlgbEatBsjl = new WlgbEatBsjl();
            wlgbEatBsjl.setLsh(lsh);
            if (dingdingEmployee != null) {
                wlgbEatBsjl.setTjr(dingdingEmployee.getName());
                wlgbEatBsjl.setTjrid(dingdingEmployee.getUserid());
            }
            wlgbEatBsjl.setBsZje(bsZje);
            wlgbEatBsjl.setBz(bz);


            wlgbEatBsjl.setGys(l.getString("textField_lnl4lchi"));
            wlgbEatBsjl.setGysbh(l.getString("textField_lnl4lchj"));
            wlgbEatBsjl.setSc(l.getString("selectField_lnl4lchm"));
            wlgbEatBsjl.setDw(l.getString("textField_lnl4lchp"));
            wlgbEatBsjl.setMinPrice(l.getDouble("numberField_lnl4lchn"));
            wlgbEatBsjl.setBsNum(l.getInteger("numberField_lnl4lcho"));
            wlgbEatBsjl.setBsJe(l.getDouble("numberField_lnl4lchq"));

            wlgbEatBsjlService.save(wlgbEatBsjl);
        }

        return Result.OK();
    }

    /**
     * 提交入库记录
     */
    @RequestMapping(value = "tjRkJl")
    public Result tjRkJl(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String user = request.getParameter("user");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(user);
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String lsh = jsonObject.getString("serialNumberField_lnmw29pj");
        String gys = jsonObject.getString("textField_lnl4dvkh");
        String gysBh = jsonObject.getString("textField_lnl4dvki");
        Date cgTime = jsonObject.getDate("dateField_lnii3nue");
        Double sumJe = jsonObject.getDouble("numberField_lnii3nud");
        JSONArray jsonArray = jsonObject.getJSONArray("tableField_lnii3nu4");
        for (Object json : jsonArray) {
            JSONObject l = (JSONObject) json;
            WlgbEatRkjl wlgbEatRkjl = new WlgbEatRkjl();
            wlgbEatRkjl.setLsh(lsh);
            wlgbEatRkjl.setGys(gys);
            wlgbEatRkjl.setGysbh(gysBh);
            wlgbEatRkjl.setCgtime(cgTime);
            wlgbEatRkjl.setSumJe(sumJe);

            wlgbEatRkjl.setScmc(l.getString("selectField_lnii3nu3"));
            wlgbEatRkjl.setFhdw(l.getString("selectField_lnii3nu5"));
            wlgbEatRkjl.setDgNum(l.getInteger("numberField_lnii3nu6"));
            wlgbEatRkjl.setFhNum(l.getInteger("numberField_lnii3nu8"));
            wlgbEatRkjl.setFhPrice(l.getDouble("numberField_lnii3nua"));
            wlgbEatRkjl.setFhSumPrice(l.getDouble("numberField_lnii3nuc"));
            if (dingdingEmployee != null) {
                wlgbEatRkjl.setTjr(dingdingEmployee.getName());
                wlgbEatRkjl.setTjrid(dingdingEmployee.getUserid());
            }

            wlgbEatRkjlService.save(wlgbEatRkjl);
        }


        return Result.OK();
    }

    /**
     * 修改入库记录
     */
    @RequestMapping(value = "editRkJl")
    public Result editRkJl(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String user = request.getParameter("user");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(user);
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String lsh = jsonObject.getString("serialNumberField_lnmw29pj");
        String gys = jsonObject.getString("textField_lnl4dvkh");
        String gysBh = jsonObject.getString("textField_lnl4dvki");
        Date cgTime = jsonObject.getDate("dateField_lnii3nue");
        Double sumJe = jsonObject.getDouble("numberField_lnii3nud");
        JSONArray jsonArray = jsonObject.getJSONArray("tableField_lnii3nu4");
        if (lsh != null && !"".equals(lsh)) {
            wlgbEatRkjlService.clearData(lsh);
        }
        for (Object json : jsonArray) {
            JSONObject l = (JSONObject) json;
            WlgbEatRkjl wlgbEatRkjl = new WlgbEatRkjl();
            wlgbEatRkjl.setLsh(lsh);
            wlgbEatRkjl.setGys(gys);
            wlgbEatRkjl.setGysbh(gysBh);
            wlgbEatRkjl.setCgtime(cgTime);
            wlgbEatRkjl.setSumJe(sumJe);

            wlgbEatRkjl.setScmc(l.getString("selectField_lnii3nu3"));
            wlgbEatRkjl.setFhdw(l.getString("selectField_lnii3nu5"));
            wlgbEatRkjl.setDgNum(l.getInteger("numberField_lnii3nu6"));
            wlgbEatRkjl.setFhNum(l.getInteger("numberField_lnii3nu8"));
            wlgbEatRkjl.setFhPrice(l.getDouble("numberField_lnii3nua"));
            wlgbEatRkjl.setFhSumPrice(l.getDouble("numberField_lnii3nuc"));
            if (dingdingEmployee != null) {
                wlgbEatRkjl.setTjr(dingdingEmployee.getName());
                wlgbEatRkjl.setTjrid(dingdingEmployee.getUserid());
            }

            wlgbEatRkjlService.save(wlgbEatRkjl);
        }


        return Result.OK();
    }

    /**
     * 删除入库记录
     */
    @RequestMapping(value = "deleteRkJl")
    public Result deleteRkJl(HttpServletRequest request) {
        String lsh = request.getParameter("lsh");
        if (lsh == null || "".equals(lsh)) {
            return Result.error("lsh空的");
        }
        wlgbEatRkjlService.deleteRkJl(lsh);
        return Result.OK();
    }

    /**
     * 修改菜品信息
     */
    @RequestMapping(value = "editCpXx")
    public Result editCpXx(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        System.out.println("菜品修改");
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/editCpxxTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 修改食材信息
     */
    @RequestMapping(value = "editScXx")
    public Result editScXx(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        System.out.println("食材修改");
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/editScXxTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 提交调拨记录
     */
    @RequestMapping(value = "tjDbJl")
    public Result tjDbJl(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String user = request.getParameter("user");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        return addDbjl(datas, user,null);
    }

    /**
     * 修改调拨记录
     */
    @RequestMapping(value = "editDbJl")
    public Result editDbJl(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String user = request.getParameter("user");
        if (datas == null || "".equals(datas)) {
            return Result.error("datas空的");
        }
        String lsh = request.getParameter("lsh");
        log.info("lsh====" + lsh);
        if (lsh != null && !"".equals(lsh)) {
            wlgbEatDbjlService.clearData(lsh);
        }
        return addDbjl(datas, user, lsh);
    }

    /**
     * 删除调拨记录
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "deleteDbJl")
    public Result deleteDbJl(HttpServletRequest request) {
        String lsh = request.getParameter("lsh");
        if (lsh == null || "".equals(lsh)) {
            return Result.error("lsh空的");
        }
        wlgbEatDbjlService.clearData(lsh);
        return Result.OK();
    }

    public Result addDbjl(String datas, String user, String lshs) {
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(user);
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String lsh = "";
        if (lshs == null) {
            lsh = jsonObject.getString("serialNumberField_lnwygzbr");
        } else {
            lsh = lshs;
        }
        //出库集群名称
        String ckjqname = jsonObject.getString("textField_lnwygzbb");
        //出库集群编号
        String ckjqbh = jsonObject.getString("textField_lnwygzbc");
        //入库集群名称
        String rkjqname = jsonObject.getString("textField_lnwygzbl");
        //入库集群编号
        String rkjqbh = jsonObject.getString("textField_lnwygzbk");
        //子表单
        JSONArray jsonArray = jsonObject.getJSONArray("tableField_lnwygzbd");
        for (Object json : jsonArray) {
            JSONObject l = (JSONObject) json;
            //出库供应商名称
            String ckgysname = l.getString("textField_lnwygzbo");
            //出库供应商编号
            String ckgysbh = l.getString("textField_lnwygzbp");
            //出库食材数量
            Integer ckscnum = l.getInteger("numberField_lnwygzbf");
            //出库食材成本
            Double cksccb = l.getDouble("numberField_lnwygzbj");
            //出库食材名称
            String ckscmc = l.getString("textField_lnwygzbq");
            //出库食材编号
            String ckscbh = l.getString("textField_lnwygzbh");
            WlgbEatDbjl dbjl = new WlgbEatDbjl();
            dbjl.setLsh(lsh);
            dbjl.setCkjqname(ckjqname);
            dbjl.setCkjqbh(ckjqbh);
            dbjl.setRkjqbh(rkjqbh);
            dbjl.setRkjqname(rkjqname);
            dbjl.setCkgysname(ckgysname);
            dbjl.setCkgysbh(ckgysbh);
            dbjl.setCkscnum(ckscnum);
            dbjl.setCksccb(cksccb);
            dbjl.setCkscmc(ckscmc);
            dbjl.setCkscbh(ckscbh);
            dbjl.setSfsc(0);
            if (dingdingEmployee != null) {
                dbjl.setTjr(dingdingEmployee.getName());
                dbjl.setTjrid(dingdingEmployee.getUserid());
            }
            wlgbEatDbjlService.save(dbjl);
        }
        return Result.OK();
    }

    /**
     * 选人截取
     *
     * @param userid 用户id
     * @return 用户id
     */
    private String xzjq(String userid) {
        String s = userid.substring(0, 1);
        if ("[".equals(s)) {
            userid = userid.substring(2, userid.length() - 2);
        } else {
            userid = userid;
        }
        return userid;
    }


}
