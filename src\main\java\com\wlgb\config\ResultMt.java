package com.wlgb.config;

import lombok.Data;

@Data
public class ResultMt<T> {
    private String status;
    private Boolean success;
    private String message;
    private T data;
    private String errorInfo;
    private Integer code;
    private long timestamp = System.currentTimeMillis();

    public ResultMt<T> success(String message) {
        this.errorInfo = message;
        this.status = "OK";
        this.success = true;
        return this;
    }

    @Deprecated
    public static ResultMt<Object> ok() {
        ResultMt<Object> r = new ResultMt<Object>();
        r.setSuccess(true);
        r.setStatus("OK");
        r.setErrorInfo("成功");
        return r;
    }

    @Deprecated
    public static ResultMt<Object> ok(String msg) {
        ResultMt<Object> r = new ResultMt<Object>();
        r.setSuccess(true);
        r.setStatus("OK");
        r.setErrorInfo(msg);
        return r;
    }

    @Deprecated
    public static ResultMt<Object> ok(Object data) {
        ResultMt<Object> r = new ResultMt<Object>();
        r.setSuccess(true);
        r.setStatus("OK");
        r.setData(data);
        return r;
    }

    public static <T> ResultMt<T> OKMT() {
        ResultMt<T> r = new ResultMt<T>();
        r.setSuccess(true);
        r.setMessage("success");
        r.setStatus("OK");
        r.setCode(0);
        r.setErrorInfo("成功");
        return r;
    }

    public static <T> ResultMt<T> OK(T data) {
        ResultMt<T> r = new ResultMt<T>();
        r.setSuccess(true);
        r.setStatus("OK");
        r.setData(data);
        return r;
    }

    public static <T> ResultMt<T> OK(String msg, T data) {
        ResultMt<T> r = new ResultMt<T>();
        r.setSuccess(true);
        r.setStatus("OK");
        r.setErrorInfo(msg);
        r.setData(data);
        return r;
    }

    public static <T> ResultMt<T> ERRORMT(String msg, T data) {
        ResultMt<T> r = new ResultMt<T>();
        r.setSuccess(false);
        r.setMessage(msg);
        r.setStatus("500");
        r.setCode(500);
        r.setErrorInfo("成功");
        return r;
    }

    public static <T> ResultMt<T> error(String msg, T data) {
        ResultMt<T> r = new ResultMt<T>();
        r.setSuccess(false);
        r.setStatus("500");
        r.setErrorInfo(msg);
        r.setData(data);
        return r;
    }

    public static ResultMt<Object> error(String msg) {
        return error("500", msg);
    }

    public static ResultMt<Object> error(String code, String msg) {
        ResultMt<Object> r = new ResultMt<Object>();
        r.setStatus(code);
        r.setErrorInfo(msg);
        r.setSuccess(false);
        return r;
    }

    public ResultMt<T> error500(String message) {
        this.errorInfo = message;
        this.status = "500";
        this.success = false;
        return this;
    }
}
