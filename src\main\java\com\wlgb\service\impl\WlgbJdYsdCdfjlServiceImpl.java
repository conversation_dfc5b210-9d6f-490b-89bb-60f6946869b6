package com.wlgb.service.impl;

import com.wlgb.entity.WlgbJdYsdCdfjl;
import com.wlgb.mapper.WlgbJdYsdCdfjlMapper;
import com.wlgb.service.WlgbJdYsdCdfjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.wlgb.config.Tools.isEmpty;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年06月13日 17:44
 */
@Service
public class WlgbJdYsdCdfjlServiceImpl implements WlgbJdYsdCdfjlService {
    @Resource
    private WlgbJdYsdCdfjlMapper wlgbJdYsdCdfjlMapper;

    @Override
    public void save(WlgbJdYsdCdfjl wlgbJdYsdCdfjl) {
        wlgbJdYsdCdfjl.setCreateTime(new Date());
        wlgbJdYsdCdfjlMapper.insertSelective(wlgbJdYsdCdfjl);
    }

    @Override
    public void updateById(WlgbJdYsdCdfjl wlgbJdYsdCdfjl) {
        wlgbJdYsdCdfjl.setUpdateTime(new Date());
        wlgbJdYsdCdfjlMapper.updateByPrimaryKeySelective(wlgbJdYsdCdfjl);
    }

    @Override
    public WlgbJdYsdCdfjl queryByDdBhAndSfSc(String ddbh) {
        Example example = new Example(WlgbJdYsdCdfjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddbh", ddbh);
        criteria.andEqualTo("sfsc", 0);
        return wlgbJdYsdCdfjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdYsdCdfjl> queryListByWlgbJdYsdCdfjl(String ddbh) {
        Example example = new Example(WlgbJdYsdCdfjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sfsc", 0);
        criteria.andEqualTo("sflrjd", 1);
        if (isEmpty(ddbh)) {
            criteria.andGreaterThanOrEqualTo("createTime", java.time.LocalDate.now());
        } else {
            criteria.andEqualTo("ddbh", ddbh);
        }
        return wlgbJdYsdCdfjlMapper.selectByExample(example);
    }


}
