package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meituan.sdk.DefaultMeituanClient;
import com.meituan.sdk.MeituanClient;
import com.meituan.sdk.MeituanResponse;
import com.meituan.sdk.auth.MeituanTokenData;
import com.meituan.sdk.auth.MeituanTokenResponse;
import com.meituan.sdk.internal.exceptions.MtSdkException;
import com.meituan.sdk.model.ddzh.common.grayConfigBaseSession.GrayConfigBaseSessionRequest;
import com.meituan.sdk.model.ddzh.common.migrateSession.MigrateSessionRequest;
import com.meituan.sdk.model.ddzh.common.migrateSession.MigrateSessionResponse;
import com.meituan.sdk.model.ddzh.common.migrateTaskDetail.MigrateTaskDetailRequest;
import com.meituan.sdk.model.ddzh.common.migrateTaskDetail.MigrateTaskDetailResponse;
import com.meituan.sdk.model.ddzh.common.migrateTaskDetail.QuerySessionTokenTaskDetailTO;
import com.meituan.sdk.model.ddzh.common.pageQuerySession.PageQuerySessionRequest;
import com.meituan.sdk.model.ddzh.common.pageQuerySession.PageQuerySessionResponse;
import com.meituan.sdk.model.ddzh.common.pageQuerySessionTokenMapping.Mapping;
import com.meituan.sdk.model.ddzh.common.pageQuerySessionTokenMapping.PageQuerySessionTokenMappingRequest;
import com.meituan.sdk.model.ddzh.common.pageQuerySessionTokenMapping.PageQuerySessionTokenMappingResponse;
import com.meituan.sdk.model.ddzh.common.transferOpenShopUuidToOpPoiId.ShopToOpenShopMappingDTO;
import com.meituan.sdk.model.ddzh.common.transferOpenShopUuidToOpPoiId.TransferOpenShopUuidToOpPoiIdRequest;
import com.meituan.sdk.model.ddzh.common.transferOppoiidToOpenShopUuids.TransferOppoiidToOpenShopUuidsRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.ReceiptConsumeResult;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.RpaymentDetail;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.TuangouReceiptConsumeRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.TuangouReceiptConsumeResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptGetconsumed.TuangouReceiptGetconsumedRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptGetconsumed.TuangouReceiptGetconsumedResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptPrepare.PaymentDetailSub;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptPrepare.TuangouReceiptPrepareRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptPrepare.TuangouReceiptPrepareResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptQuerylistbydate.TuangouReceiptQuerylistbydateRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptQuerylistbydate.TuangouReceiptQuerylistbydateResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptReverseconsume.TuangouReceiptReverseconsumeRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptReverseconsume.TuangouReceiptReverseconsumeResponse;
import com.meituan.sdk.model.ddzh.ugc.ugcQueryShopReview.UgcQueryShopReviewRequest;
import com.meituan.sdk.model.ddzh.ugc.ugcQueryShopReview.UgcQueryShopReviewResponse;
import com.meituan.sdk.model.ddzh.ugc.ugcQuerystar.UgcQuerystarRequest;
import com.meituan.sdk.model.ddzh.ugc.ugcQuerystar.UgcQuerystarResponse;
import com.meituan.sdk.model.ddzhkh.auth.pageQueryTokenPoiList.CustomerPoiInfoTO;
import com.meituan.sdk.model.ddzhkh.auth.pageQueryTokenPoiList.PageQueryTokenPoiListRequest;
import com.meituan.sdk.model.ddzhkh.auth.pageQueryTokenPoiList.PageQueryTokenPoiListResponse;
import com.meituan.sdk.model.ddzhkh.auth.queryPoiMapping.QueryPoiMappingRequest;
import com.meituan.sdk.model.ddzhkh.auth.queryPoiMapping.QueryPoiMappingResponse;
import com.meituan.sdk.model.tuangouNg.coupon.queryCouponById.QueryCouponByIdRequest;
import com.meituan.sdk.model.tuangouNg.coupon.queryCouponById.QueryCouponByIdResponse;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.config.meituan.MeiTuanConfig;
import com.wlgb.entity.*;
import com.wlgb.service.*;
import com.wlgb.service2.TbXydService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.batch.BatchAutoConfiguration;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.wlgb.config.oss.oConvertUtils.isEmpty;

/**
 * @Description: Class  美团验券的接口
 * @author: fwq
 * @date: 2024年08月8日 19:56
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/mtyq")
public class MtYqController {
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;
    @Autowired
    private WlgbMtMdService wlgbMtMdService;
    @Autowired
    private WlgbMtTokenService wlgbMtTokenService;
    @Autowired
    private WlgbMtJlService wlgbMtJlService;
    @Autowired
    private WlgbMtLogService wlgbMtLogService;
    @Autowired
    private WlgbJdDjbbdService wlgbJdDjbbdService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private TbXydService tbXydService;
    @Value("${mtpeizhi.developerId}")
    private Long developerId;
    @Value("${mtpeizhi.SignKey}")
    private String SignKey;
    @Value("${mtpeizhi.AppKey}")
    private String AppKey;
    @Value("${mtpeizhi.developerIdJM}")
    private Long developerIdJM;
    @Value("${mtpeizhi.AppKeyJM}")
    private String AppKeyJM;
    @Value("${mtpeizhi.SignKeyJM}")
    private String SignKeyJM;
    @Value("${mtpeizhi.mdtokenurl}")
    private String mdtokenurl;

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////美团和北极星的最大区别是：美团是每个账号都有一个token并且每一个门店也都有对应的token，而北极星是一个账号对应一个token。////////////////////////
    //////////////理论上美团只要一个账号就可以了，一个账号授权七百多门店，然后就可以获取所有门店的token//////////////////////////////
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * 门店授权地址
     */
    @RequestMapping(value = "/mdsqgetcode")
    public void mdsqgetcode(HttpServletRequest request) {
        Map<String, String> param = new HashMap();
        param.put("developerId", String.valueOf(developerIdJM));
        param.put("businessId", "58");
        param.put("timestamp", (System.currentTimeMillis() / 1000) + "");
        param.put("charset", "UTF-8");
        String sign = MeiTuanConfig.getSign(SignKeyJM, param);
        param.put("sign", sign);
        System.out.println(sign);
        String shouquanurl = "https://open-erp.meituan.com/general/auth?developerId=" + param.get("developerId") + "&timestamp=" + param.get("timestamp") + "&charset=UTF-8&businessId=" + param.get("businessId") + "&sign=" + param.get("sign");

        System.out.println(shouquanurl);
    }

    /**
     * 门店授权后的回调地址,根据code去获取门店的token（每一个门店都有唯一的token）  //TODO  这里有个问题是  目前是根据门店名字opBizName来匹配的，以后要换成根据门店id匹配token
     */
    @RequestMapping(value = "/sqmcallback")
    public ResultMt sqmcallback(HttpServletRequest request) throws IOException {
        String code = request.getParameter("code");
        String developerId = request.getParameter("developerId");
        String businessId = request.getParameter("businessId");
        Map<String, String> param = new HashMap();
        param.put("developerId", developerId);
        param.put("businessId", businessId);
        param.put("timestamp", (System.currentTimeMillis() / 1000) + "");
        param.put("charset", "UTF-8");
        param.put("code", code);
        param.put("grantType", "authorization_code");
        String singkey = "", zh = "";
        if ("112687".equals(developerId)) {
            singkey = SignKey;
            zh = "wlgb6688";
        } else {
            singkey = SignKeyJM;
            zh = "wlgb7777";
        }
        String sign = MeiTuanConfig.getSign(singkey, param);
        param.put("sign", sign);
        String jg = HttpClientUtil.postAsync(mdtokenurl, param);
        log.info("*******美团请求******{}", jg);
        JSONObject jo = JSONObject.parseObject(jg);
        Integer jgcode = jo.getInteger("code");
        if (jgcode == 0) {
            JSONObject datajson = jo.getJSONObject("data");
            //本次授权的token
            String accessToken = datajson.getString("accessToken");
            //更新token需要用到
            String refreshToken = datajson.getString("refreshToken");
            //有效期
            Integer expireIn = datajson.getInteger("expireIn");
            //门店ID
            String opBizCode = datajson.getString("opBizCode");
            //门店名称
            String opBizName = datajson.getString("opBizName");
            WlgbMtMd md = new WlgbMtMd();

            // 使用字符串操作分割括号及其内容
            int startIndex = opBizName.indexOf('（');
            if (startIndex == -1) {
                startIndex = opBizName.indexOf('(');
            }
            if (startIndex != -1) {
                int endIndex = opBizName.indexOf('）', startIndex);
                if (endIndex == -1) {
                    endIndex = opBizName.indexOf(')', startIndex);
                }
                if (endIndex != -1) {
                    String namePart = opBizName.substring(0, startIndex);
                    String locationPart = opBizName.substring(startIndex + 1, endIndex);
                    md.setShopname(namePart);
                    md.setBranchname(locationPart);
                } else {
                    System.out.println("No matching closing parenthesis found.");
                }
            } else {
                md.setShopname(opBizName);
            }

            System.out.println(opBizName);
            Calendar nowTime = Calendar.getInstance();
            nowTime.setTime(new Date());
            nowTime.add(Calendar.SECOND, expireIn);
            md.setMtrefreshtoken(refreshToken);
            md.setMtaccesstoken(accessToken);
            md.setEndtime(nowTime.getTime());
            md.setOpbizcode(opBizCode);
            md.setOpbizname(opBizName);
            md.setMtdeveloperid(developerId);
            md.setMtsingkey(singkey);
            Map<String, Object> map = new HashMap<>();
            map.put("zh", zh);
            map.put("opbizcode", opBizCode);
            WlgbMtMd md1 = wlgbMtMdService.queryByopBizCode(map);
            if (!isEmpty(md1)) {
                md.setId(md1.getId());
                wlgbMtMdService.updateById(md);
            } else {
                md.setId(opBizCode);
                md.setOpenShopUuid(opBizCode);
                md.setZh(zh);
                md.setSfsc(0);
                md.setMtdeveloperid(developerId);
                md.setMtsingkey(singkey);
                wlgbMtMdService.save(md);
            }
        }
        return ResultMt.OKMT();
    }

    /**
     * 刷新美团账号token和门店token，注意如果3天内获取的token再调用刷新接口时token是不会有变化的。 TODO  需要改成定时任务，每天执行
     * 更新账号：只更新mttoken、 mtrefreshtoken、 mttokenendtime三个字段
     * 更新门店：只更新mtrefreshtoken、mtaccesstoken、endtime三个字段
     */
    @RequestMapping(value = "/refreshToken")
    public ResultMt refreshToken(HttpServletRequest request) throws MtSdkException {
        //数据库中所有的token
        Map<String, Object> map1 = new HashMap<>();
        map1.put("sfsc", "0");
        List<WlgbMtToken> tokenlist = wlgbMtTokenService.queryAllListById();
        for (WlgbMtToken token : tokenlist) {
            //刷新美团token，而不是门店token,先获取美团的token再循环获取美团门店的token
            Date date1 = new Date();
            Date date2 = new Date(token.getMttokenendtime().getTime());
            int result = date1.compareTo(date2);
            if (result > 0) {
                MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(token.getMtdeveloperid()), token.getMtsingkey()).build();
                //35天过期，建议您在访问令牌快过期前，使用刷新令牌进行刷新；若刷新令牌过期了，还未进行刷新，那您只有进行重新授权
                //授权地址：https://developer.meituan.com/ka/brand/authlink-tool
                //业务类型：到店综合（客户）
                MeituanTokenResponse mtresponse = meituanClient.refreshToken(59, token.getMtrefreshtoken());
                log.info("**************刷新门店token返回的值*******{}", mtresponse);
                MeituanTokenData mtdata = mtresponse.getData();
                if (!isEmpty(mtdata)) {
                    Calendar nowTime = Calendar.getInstance();
                    nowTime.setTime(new Date());
                    //有效期
                    int expireIn = mtdata.getExpireIn();
                    nowTime.add(Calendar.SECOND, expireIn);
                    //下次刷新需要用到的
                    token.setMtrefreshtoken(mtdata.getRefreshToken());
                    //刷新后的token
                    token.setMttoken(mtdata.getAccessToken());
                    token.setMttokenendtime(nowTime.getTime());
                    wlgbMtTokenService.updateById(token);
                }
            }
            Map<String, Object> map = new HashMap<>();
            map.put("endtime", new Date());
            map.put("mtdeveloperid", token.getMtdeveloperid());
            map.put("mtsingkey", token.getMtsingkey());
            List<WlgbMtMd> mttokenList = wlgbMtMdService.queryByToken(map);
            for (WlgbMtMd word : mttokenList) {
                MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(token.getMtdeveloperid()), token.getMtsingkey()).build();
                MeituanTokenResponse response = meituanClient.refreshToken(58, word.getMtrefreshtoken());
                log.info("**************刷新token返回的值*******{}", response);
                MeituanTokenData data = response.getData();
                if (!isEmpty(data)) {
                    Calendar nowTime = Calendar.getInstance();
                    nowTime.setTime(new Date());
                    //有效期
                    int expireIn = data.getExpireIn();
                    nowTime.add(Calendar.SECOND, expireIn);
                    //下次刷新需要用到的
                    word.setMtrefreshtoken(data.getRefreshToken());
                    //刷新后的token
                    word.setMtaccesstoken(data.getAccessToken());
                    word.setEndtime(nowTime.getTime());
                    wlgbMtMdService.updateById(word);
                }
            }
        }
        return ResultMt.OKMT();
    }

    /**
     * 门店映射回调地址
     */
    @RequestMapping(value = "/mdyscallback")
    public ResultMt mdyscallback(HttpServletRequest request) {
        Map<String, String> paramsAndHeaders = new HashMap<>();

        // 获取并打印请求参数
        Enumeration paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = (String) paramNames.nextElement();
            String[] paramValues = request.getParameterValues(paramName);

            if (paramValues.length == 1 && paramValues[0].length() != 0) {
                paramsAndHeaders.put(paramName, paramValues[0]);
            } else {
                // 如果参数有多个值，也可以将它们以列表形式存储
                // paramsAndHeaders.put(paramName, Arrays.asList(paramValues));
            }
        }

        // 获取并打印请求头
        Enumeration headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = (String) headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            paramsAndHeaders.put(headerName, headerValue);
        }

        // 打印所有参数和请求头
        Set<Map.Entry<String, String>> entries = paramsAndHeaders.entrySet();
        System.out.println("===========================");
        for (Map.Entry<String, String> entry : entries) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
        System.out.println("===========================");
        return ResultMt.OKMT();

    }

    /**
     * 批量授权美团门店后，自动填写门店的token和更新token
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/yewucallback")
    public ResultMt yewucallback(HttpServletRequest request) {
        String message = request.getParameter("message");
        log.info("***************message*********{}", message);
        // 解析 message 参数
        JSONObject jsonResponse = JSONObject.parseObject(message);
        if (!isEmpty(message)) {
            int successfulCount = jsonResponse.getIntValue("successfulCount");
            // 处理成功的授权
            if (successfulCount > 0) {
                //解析successfulAuthorizations{}
                JSONArray successfulAuthorizations = jsonResponse.getJSONArray("successfulAuthorizations");
                for (int i = 0; i < successfulAuthorizations.size(); i++) {
                    JSONObject auth = successfulAuthorizations.getJSONObject(i);
                    String accessToken = auth.getString("accessToken");
                    String refreshToken = auth.getString("refreshToken");
                    int expireIn = auth.getIntValue("expireIn");
                    String shopId = auth.getString("shopId");
                    String opBizCode = auth.getString("opBizCode");
                    String opBizName = auth.getString("opBizName");

                    WlgbMtMd md = new WlgbMtMd();
                    md.setMtaccesstoken(accessToken);
                    md.setMtrefreshtoken(refreshToken);
                    md.setOpbizcode(opBizCode);
                    md.setOpbizname(opBizName);
                    md.setMtmdzzid(shopId);
                    Calendar nowTime = Calendar.getInstance();
                    nowTime.setTime(new Date());
                    nowTime.add(Calendar.SECOND, expireIn);
                    md.setEndtime(nowTime.getTime());
                    Map<String, Object> map = new HashMap<>();
                    map.put("opbizcode", opBizCode);
                    WlgbMtMd md1 = wlgbMtMdService.queryByopBizCode(map);
                    if (!isEmpty(md1)) {
                        md.setId(md1.getId());
                        wlgbMtMdService.updateById(md);
                    }
                }
            }
        }
        return ResultMt.OKMT();
    }

    /**
     * 查询门店星级和单项分
     */
    @RequestMapping(value = "/querystar")
    public ResultMt querystar(HttpServletRequest request) throws MtSdkException {
        String opBizCode = request.getParameter("opBizCode");
        WlgbMtMd md = getToken(opBizCode);
        MeituanClient meituanClient = DefaultMeituanClient.builder(developerId, SignKey).build();
        UgcQuerystarRequest ugcQuerystarRequest = new UgcQuerystarRequest();
        ugcQuerystarRequest.setPlatform(1);
        MeituanResponse<UgcQuerystarResponse> response = meituanClient.invokeApi(ugcQuerystarRequest, md.getMtaccesstoken());
        if (response.isSuccess()) {
            UgcQuerystarResponse resp = response.getData();
            System.out.println("---------------------------------");
            System.out.println(resp);
            return ResultMt.OK(resp);
        } else {
            System.out.println("调用失败");
            return ResultMt.error("调用失败");
        }
    }

    /**
     * 单一门店查询评论数据
     */
    @RequestMapping(value = "/queryshopreview")
    public ResultMt queryshopreview(HttpServletRequest request) throws IOException, MtSdkException {
        String opBizCode = request.getParameter("opBizCode");
        WlgbMtMd mtmd = getToken(opBizCode);
        String appAuthToken = mtmd.getMtaccesstoken();
        MeituanClient meituanClient = DefaultMeituanClient.builder(developerId, SignKey).build();
        UgcQueryShopReviewRequest ugcQueryShopReviewRequest = new UgcQueryShopReviewRequest();
        ugcQueryShopReviewRequest.setStar(1);
        ugcQueryShopReviewRequest.setOffset(1);
        ugcQueryShopReviewRequest.setLimit(50);
        ugcQueryShopReviewRequest.setBeginTime("2024-01-10 10:10:10");
        ugcQueryShopReviewRequest.setEndTime("2024-10-16 10:10:10");
        ugcQueryShopReviewRequest.setPlatform(1);
        MeituanResponse<UgcQueryShopReviewResponse> response = meituanClient.invokeApi(ugcQueryShopReviewRequest, appAuthToken);
        if (response.isSuccess()) {
            UgcQueryShopReviewResponse resp = response.getData();
            System.out.println(resp);
            return ResultMt.OK(resp);
        } else {
            System.out.println("调用失败");
            return ResultMt.error("调用失败");
        }
    }

    /**
     * 适用门店查询 ,可以在这里更新所有门店id和名称
     */
    @RequestMapping(value = "/pageQueryPoiList")
    public ResultMt pageQueryPoiList(HttpServletRequest request) throws MtSdkException {
        //数据库中所有的token
        List<WlgbMtToken> tokenlist = wlgbMtTokenService.queryAllListById();
        for (WlgbMtToken token : tokenlist) {
            MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(token.getMtdeveloperid()), token.getMtsingkey()).build();
            PageQueryTokenPoiListRequest pageQueryTokenPoiListRequest = new PageQueryTokenPoiListRequest();
            //很难超过1000个门店
            pageQueryTokenPoiListRequest.setLimit(1000);
            pageQueryTokenPoiListRequest.setOffset(0);
            MeituanResponse<PageQueryTokenPoiListResponse> response = meituanClient.invokeApi(pageQueryTokenPoiListRequest, token.getMttoken());
            if (response.isSuccess()) {
                PageQueryTokenPoiListResponse resp = response.getData();
                for (int i = 0; i < resp.getTotal(); i++) {
                    CustomerPoiInfoTO cus = resp.getPoiInfoList().get(i);
                    Map<String, Object> map = new HashMap<>();
                    map.put("opbizcode", cus.getOpPoiId());
                    WlgbMtMd wlmtmd = wlgbMtMdService.queryByopBizCode(map);
                    if (!isEmpty(wlmtmd)) {
                        wlmtmd.setOpbizname(cus.getName());
                        wlmtmd.setShopaddress(cus.getAddress());
                        wlmtmd.setCityname(cus.getCityName());
                        wlgbMtMdService.updateById(wlmtmd);
                    } else {
                        wlmtmd = new WlgbMtMd();
                        wlmtmd.setId(IdConfig.uuId());
                        wlmtmd.setOpenShopUuid(cus.getOpPoiId());
                        wlmtmd.setOpbizname(cus.getName());
                        wlmtmd.setShopaddress(cus.getAddress());
                        wlmtmd.setCityname(cus.getCityName());
                        wlmtmd.setOpbizcode(cus.getOpPoiId());
                        String opBizName = cus.getName();
                        // 使用字符串操作分割括号及其内容
                        int startIndex = opBizName.indexOf('（');
                        if (startIndex == -1) {
                            startIndex = opBizName.indexOf('(');
                        }
                        if (startIndex != -1) {
                            int endIndex = opBizName.indexOf('）', startIndex);
                            if (endIndex == -1) {
                                endIndex = opBizName.indexOf(')', startIndex);
                            }
                            if (endIndex != -1) {
                                String namePart = opBizName.substring(0, startIndex);
                                String locationPart = opBizName.substring(startIndex + 1, endIndex);
                                wlmtmd.setShopname(namePart);
                                wlmtmd.setBranchname(locationPart);
                            } else {
                                System.out.println("No matching closing parenthesis found.");
                            }
                        } else {
                            wlmtmd.setShopname(opBizName);
                        }
                        wlmtmd.setShopaddress(cus.getAddress());
                        wlmtmd.setCityname(cus.getCityName());
                        wlmtmd.setZh(token.getZh());
                        wlmtmd.setSfsc(0);
                        wlmtmd.setMtdeveloperid(token.getMtdeveloperid());
                        wlmtmd.setMtsingkey(token.getMtsingkey());
                        wlgbMtMdService.save(wlmtmd);
                    }
                }
            } else {
                System.out.println("调用失败");
            }
        }
        return ResultMt.OKMT();
    }

    /**
     * 对应客户门店ID映射关系
     */
    @RequestMapping(value = "/queryPoiMapping")
    public ResultMt queryPoiMapping(HttpServletRequest request) throws MtSdkException {
        String isjm = request.getParameter("isjm");
        Long did = 0L;
        String dsk = "", token = "";
        Map<String, Object> map1 = new HashMap<>();
        if (("0".equals(isjm))) {
            did = developerIdJM;
            dsk = SignKeyJM;
            map1.put("mtdeveloperid", "113438");
        } else {
            did = developerId;
            dsk = SignKey;
            map1.put("mtdeveloperid", "112687");
        }
        List<WlgbMtToken> tokenlist = wlgbMtTokenService.queryAllListById(map1);
        token = tokenlist.get(0).getMttoken();
        MeituanClient meituanClient = DefaultMeituanClient.builder(did, dsk).build();
        QueryPoiMappingRequest queryPoiMappingRequest = new QueryPoiMappingRequest();
        queryPoiMappingRequest.setPoiIds(new ArrayList<>());
        MeituanResponse<QueryPoiMappingResponse> response = meituanClient.invokeApi(queryPoiMappingRequest, token);
        log.info("----对应客户门店ID映射关系-------{}", response);
        if (response.isSuccess()) {
            QueryPoiMappingResponse resp = response.getData();
            System.out.println(resp);
        } else {
            System.out.println("调用失败");
        }
        return ResultMt.OKMT();
    }

    /**
     * 输码验券校验  定金+补交定金
     * 验券的步骤是：先输入券码进行校验，校验完之后再调用验券接口
     */
    @RequestMapping(value = "/prepareDj")
    public Result prepareDj(HttpServletRequest request) throws Exception {
        //验券人id
        String userId = request.getParameter("userId");
        //券码长度
        String qmcd = request.getParameter("qmcd");
        //关键信息都在这里
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String opBizCode = jsonObject.getString("opBizCode");
        String qm = jsonObject.getString("smqm");
        if (qm.length() > 13) {
            return Result.OK("券码长度不能大于13位数字");
        }
        String crmbh = jsonObject.getString("crmbh");
        String khdh = jsonObject.getString("khdh");
        String lsh = jsonObject.getString("lsh");
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        lsh = lsh != null && !"".equals(lsh) ? lsh : df1.format(new Date());
        Integer sfbjdjwk = jsonObject.getInteger("sfbjdjwk");
        Integer sfbjdj = jsonObject.getInteger("sfbjdj");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);
        if (isEmpty(ding)) {
            return Result.OK("当前验券人在系统内不存在");
        }
        if (isEmpty(ding.getUserid()) || isEmpty(ding.getName())) {
            return Result.OK("当前验券人的id和姓名为空");
        }
        //获取当前门店的token
        WlgbMtMd mtmd = getToken(opBizCode);
        if (isEmpty(mtmd)) {
            return Result.OK("该美团门店不存在，无发验券");
        }
        //存放的是店长、轰趴顾问手动输入的券码,前端做了限制只能是10、13位数字
        Set<String> sset = new HashSet<>();
        sset.add(qm);

        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        //验券步骤：先校验  后验券 prepare：校验接口
        Set<Map<String, Object>> ssmap = prepare(sset, mtmd);
        if (ssmap.isEmpty()) {
            //已经验过券了，本接口是查询验券记录的信息
            TuangouReceiptGetconsumedResponse topicResponse = cxyyqxx(mtmd, qm);
            if (!isEmpty(topicResponse)) {
                log.info("**************已经验过券的结果**********{}", topicResponse);
                String text = "您提交的新美大验券，已经批量验过了！";
                text += "\n\n券码：" + qm;
                text += "\n\n送达时间：" + df2.format(new Date());
                DingDBConfig.sendGztzText(dingkey, ding.getUserid(), text);
            } else {
                String text = "您提交的新美大验券，异常！";
                text += "\n\n券码：" + qm;
                text += "\n\n送达时间：" + df2.format(new Date());
                DingDBConfig.sendGztzText(dingkey, ding.getUserid(), text);
            }
        } else {
            YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
            String token = DingToken.token(dingkey);
            //多个小数组（每个数组都是100个券码），这里存放的是所有券码，如果是100个以内的券码那就是一个数组，如果是200个券码那就是两个数组，以此类推
            List<List<Map<String, Object>>> subLists = getCodeSz(ssmap);
            //循环保存+验券
            for (List<Map<String, Object>> listmap : subLists) {
                Map<String, Object> map = listmap.get(0);
                //用来验券的券码（如果券码总数量<=100个券码，只需要验一次就可以了，如果<=200，就需要验券两次，，，以此类推）
                String receiptCode1 = (String) map.get("receiptCode");
                //用于验券的数量
                int codenum = (int) map.get("count");
                //验券
                TuangouReceiptConsumeResponse resp = consume(mtmd, receiptCode1, codenum, ding, lsh, crmbh, khdh, sfbjdj, sfbjdjwk, ydAppkey, dingkey, token, null, null);
                if (isEmpty(resp)) {
                    return Result.OK("验券失败");
                } else {
                    log.info("************验券结果***********{}", resp);
                }
            }
        }
        return Result.OK("验券成功");
    }

    /**
     * 输码验券校验  尾款
     * 验券的步骤是：先输入券码进行校验，校验完之后再调用验券接口
     */
    @RequestMapping(value = "/prepareWk")
    public Result prepareWk(HttpServletRequest request) throws Exception {
        //验券人id
        String userId = request.getParameter("userId");
        //券码长度
        String qmcd = request.getParameter("qmcd");
        //关键信息都在这里
        String datas = request.getParameter("datas");
        if (isEmpty(userId) || isEmpty(datas)) {
            return Result.OK("缺少参数");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        log.info("***********前端传过来的参数*************{}", jsonObject);
        String opBizCode = jsonObject.getString("opBizCode");
        String qm = jsonObject.getString("smqm");
        if (qm.length() > 13) {
            return Result.OK("券码长度不能大于13位数字");
        }
        String crmbh = jsonObject.getString("crmbh");
        String khdh = jsonObject.getString("khdh");
        String lsh = jsonObject.getString("xddbh");
        SimpleDateFormat df1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        lsh = lsh != null && !"".equals(lsh) ? lsh : df1.format(new Date());
        //这个slid是协议单的id
        String slid = jsonObject.getString("slid");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);
        if (isEmpty(ding)) {
            return Result.OK("当前验券人在系统内不存在");
        }
        if (isEmpty(ding.getUserid()) || isEmpty(ding.getName())) {
            return Result.OK("当前验券人的id和姓名为空");
        }
        //获取当前门店的token
        WlgbMtMd mtmd = getToken(opBizCode);
        if (isEmpty(mtmd)) {
            return Result.OK("该美团门店不存在，无发验券");
        }
        //存放的是店长、轰趴顾问手动输入的券码,前端做了限制只能是10、13位数字
        Set<String> sset = new HashSet<>();
        sset.add(qm);

        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");

        //验券步骤：先校验  后验券 prepare：校验接口
        Set<Map<String, Object>> ssmap = prepare(sset, mtmd);
        if (isEmpty(ssmap) || ssmap.isEmpty()) {
            //已经验过券了，本接口是查询验券记录的信息
            TuangouReceiptGetconsumedResponse topicResponse = cxyyqxx(mtmd, qm);
            log.info("已经验过券了******************{}", topicResponse);
            if (!isEmpty(topicResponse)) {
                log.info("**************已经验过券的结果**********{}", topicResponse);
                String text = "您提交的尾款 新美大验券，已经批量验过了！";
                text += "\n\n券码：" + qm;
                text += "\n\n送达时间：" + df2.format(new Date());
                DingDBConfig.sendGztzText(dingkey, ding.getUserid(), text);
            } else {
                String text = "您提交的新美大验券，券码异常！";
                text += "\n\n券码：" + qm;
                text += "\n\n送达时间：" + df2.format(new Date());
                DingDBConfig.sendGztzText(dingkey, ding.getUserid(), text);
            }
        } else {
            YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
            String token = DingToken.token(dingkey);
            //多个小数组（每个数组都是100个券码），这里存放的是所有券码，如果是100个以内的券码那就是一个数组，如果是200个券码那就是两个数组，以此类推
            List<List<Map<String, Object>>> subLists = getCodeSz(ssmap);
            //循环保存+验券,每次最多验100张
            for (List<Map<String, Object>> listmap : subLists) {
                Map<String, Object> map = listmap.get(0);
                //用来验券的券码（如果券码总数量<=100个券码，只需要验一次就可以了，如果<=200，就需要验券两次，，，以此类推）
                String receiptCode1 = (String) map.get("receiptCode");
                //用于验券的数量
                int codenum = (int) map.get("count");
                //验券
                TuangouReceiptConsumeResponse resp = consume(mtmd, receiptCode1, codenum, ding, lsh, crmbh, khdh, null, null, ydAppkey, dingkey, token, "yes", slid);
                if (isEmpty(resp)) {
                    return Result.OK("验券失败");
                } else {
                    log.info("************验券结果***********{}", resp);
                }
            }
        }
        return Result.OK("验券成功");
    }

    /**
     * 验券接口
     *
     * @param mtmd        门店详细（包括授权后的token）
     * @param receiptCode 团购券码，必须未验证
     * @param count       券码数量，必须小于100
     * @param ding        钉钉表数据
     * @param lsh         流水号
     * @param crmbh       crm的编号
     * @param khdh        客户电话
     * @param sfwk        yes:尾款
     * @return
     * @throws IOException
     * @throws MtSdkException
     */
    public TuangouReceiptConsumeResponse consume(WlgbMtMd mtmd, String receiptCode, Integer count, DingdingEmployee ding, String lsh, String crmbh, String khdh, Integer sfbjdj, Integer sfbjdjwk, YdAppkey ydAppkey, Dingkey dingkey, String token, String sfwk, String slid) throws Exception {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(mtmd.getMtdeveloperid()), mtmd.getMtsingkey()).build();
        TuangouReceiptConsumeRequest tuangouReceiptConsumeRequest = new TuangouReceiptConsumeRequest();
        //团购券码，必须未验证
        tuangouReceiptConsumeRequest.setReceiptCode(receiptCode);
        //券码数量，必须小于100
        tuangouReceiptConsumeRequest.setCount(count);
        //幂等性
        tuangouReceiptConsumeRequest.setRequestId(System.currentTimeMillis() + "");
        tuangouReceiptConsumeRequest.setAppShopAccountName("fwq");
        tuangouReceiptConsumeRequest.setAppShopAccount("fwq");
        MeituanResponse<TuangouReceiptConsumeResponse> response = meituanClient.invokeApi(tuangouReceiptConsumeRequest, mtmd.getMtaccesstoken());
        if (response.isSuccess()) {
            //包含了美团返回的所有数据
            TuangouReceiptConsumeResponse resp = response.getData();
            //所有明细
            List<ReceiptConsumeResult> relist = resp.getResult();
            List<JSONObject> wkyqlist = new ArrayList<>();
            double qmzje = 0.0;
            for (ReceiptConsumeResult res : relist) {
                Map<String, Object> map = new HashMap<>();
                //券码
                map.put("receiptCode", res.getReceiptCode());
                //商品名称
                map.put("dealTitle", res.getDealTitle());
                //商品市场价
                map.put("dealMarketPrice", res.getDealMarketPrice());
                //该券码所在的订单的支付明细，如果一笔订单包含两个券码a、b，在核销a、b券码时返回信息一致，都是该订单的支付明细 (如果订单有多张券可以通过订单券码分摊金额查询接口查询分摊信息)
                List<RpaymentDetail> paymentDetail = res.getPaymentDetail();
                Double dealPrice = 0.0;
                for (RpaymentDetail detail : paymentDetail) {
                    Integer type = detail.getAmountType();
                    //2：抵用券 5：积分 6：立减 8：商户抵用券 17：商家立减 18：美团商家立减  22：打折卡
                    if (type != 2 && type != 5 && type != 6 && type != 8 && type != 17 && type != 18 && type != 22) {
                        dealPrice += Double.parseDouble(detail.getAmount());
                    }
                }
                //商品售卖价格
                map.put("dealPrice", dealPrice);
                //所有券码总额
                qmzje += dealPrice;
                //只保存验券成功的记录，验券失败的记录不保存
                saveLog(receiptCode, map, mtmd, ding, lsh, crmbh, khdh, sfbjdj, sfbjdjwk, ydAppkey, dingkey, token, sfwk);
                if (!isEmpty(sfwk)) {
                    //尾款
                    JSONObject jsonObject2 = new JSONObject();
                    jsonObject2.put("xsbjje", dealPrice);
                    jsonObject2.put("yqrq", new Date());
                    jsonObject2.put("dm", (mtmd.getZh()).substring((mtmd.getZh().length()) - 3));
                    jsonObject2.put("qm", res.getReceiptCode());
                    jsonObject2.put("yqr", ding.getName());
                    wkyqlist.add(jsonObject2);
                }
            }
            //尾款
            if (!isEmpty(sfwk)) {
                saveXydByWk(receiptCode, lsh, qmzje, wkyqlist, slid, ding, ydAppkey, dingkey, token);
            }
            log.info("*************新的验券接口返回的结果**************{}", resp);
            return resp;
        } else {
            String text = "您提交的新美大验券，验券失败了！";
            text += "\n\n券码：" + receiptCode;
            text += "\n\n送达时间：" + df2.format(new Date());
            text += "\n\n失败原因:" + response.getMsg();
            DingDBConfig.sendGztzText(dingkey, ding.getUserid(), text);

            String context1 = "验券失败了，券码：" + receiptCode + "，美团返回的报错原因：" + response.getMsg();
            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
            return null;
        }
    }

    /**
     * 输入券码校验并将返回的结果保存到set中
     *
     * @param sset
     * @param mtmd 门店信息
     * @return
     * @throws IOException
     * @throws MtSdkException
     */
    public Set<Map<String, Object>> prepare(Set<String> sset, WlgbMtMd mtmd) throws MtSdkException {
        MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(mtmd.getMtdeveloperid()), mtmd.getMtsingkey()).build();
        //保存所有券码
        Set<Map<String, Object>> ssmap = new HashSet<>();
        for (String code : sset) {
            TuangouReceiptPrepareRequest tuangouReceiptPrepareRequest = new TuangouReceiptPrepareRequest();
            //验券券码
            tuangouReceiptPrepareRequest.setReceiptCode(code);
            MeituanResponse<TuangouReceiptPrepareResponse> response = meituanClient.invokeApi(tuangouReceiptPrepareRequest, mtmd.getMtaccesstoken());
            String responseCode = response.getCode();
            Set<Map<String, Object>> smap = new HashSet<>();
            if ("OP_SUCCESS".equals(responseCode)) {
                //获取本次校验后的券码
                smap = getcode(response);
                //合并到大的set里面
                ssmap.addAll(smap);
            } else if ("1008".equals(responseCode)) {
                System.out.println("已经验证过了，可以直接调用 已验券信息 接口");
                ssmap = null;
            }
        }
        return ssmap;
    }

    /**
     * 获取券码校验结果
     *
     * @param response 校验结果
     * @return
     */
    public Set<Map<String, Object>> getcode(MeituanResponse<TuangouReceiptPrepareResponse> response) {
        if (response.isSuccess()) {
            TuangouReceiptPrepareResponse resp = response.getData();
            //将所有券码保存到map中(用set保存，防止重复)
            Set<Map<String, Object>> mlist = new HashSet<>();
            Map<String, Object> map = new HashMap<>();
            //验证券码
            map.put("receiptCode", resp.getReceiptCode());
            //商品市场价
            map.put("dealMarketPrice", resp.getDealMarketPrice());
            //商品名称
            map.put("dealTitle", resp.getDealTitle());
            //可用券数量
            map.put("count", resp.getCount());

            //该券码所在的订单的支付明细，如果一笔订单包含两个券码a、b，在核销a、b券码时返回信息一致，都是该订单的支付明细 (如果订单有多张券可以通过订单券码分摊金额查询接口查询分摊信息)
            List<PaymentDetailSub> paymentDetail = resp.getPaymentDetail();
            Double dealPrice = 0.0;
            for (PaymentDetailSub detail : paymentDetail) {
                Integer type = detail.getAmountType();
                //2：抵用券 5：积分 6：立减 8：商户抵用券 17：商家立减 18：美团商家立减  22：打折卡
                if (type != 2 && type != 5 && type != 6 && type != 8 && type != 17 && type != 18 && type != 22) {
                    dealPrice += Double.parseDouble(detail.getAmount());
                }
            }
            //商品售卖价格
            map.put("dealPrice", dealPrice);
            mlist.add(map);
            return mlist;
        } else {
            return null;
        }
    }

    /**
     * 将所有券码分割成多个100个券码的小数组
     *
     * @param ssmap 保存所有券码
     * @return
     */
    public List<List<Map<String, Object>>> getCodeSz(Set<Map<String, Object>> ssmap) {
        //将set转换成list
        List<Map<String, Object>> mlistAsList = new ArrayList<>(ssmap);

        List<List<Map<String, Object>>> subLists = new ArrayList<>();
        // 第一个子集合：0-99
        subLists.add(mlistAsList.subList(0, Math.min(99, ssmap.size())));
        // 最多支持400个券码
        if (mlistAsList.size() > 100) {
            if (mlistAsList.size() <= 200) {
                // 第二个子集合：100-199
                subLists.add(mlistAsList.subList(100, Math.min(199, ssmap.size())));
            } else if (mlistAsList.size() <= 300) {
                subLists.add(mlistAsList.subList(100, 199));
                // 第三个子集合：200-299
                subLists.add(mlistAsList.subList(200, Math.min(299, ssmap.size())));
            } else if (mlistAsList.size() <= 400) {
                subLists.add(mlistAsList.subList(100, 199));
                subLists.add(mlistAsList.subList(200, 299));
                // 第四个子集合：300-399
                subLists.add(mlistAsList.subList(300, Math.min(399, ssmap.size())));
            }
        }
        return subLists;

    }

    /**
     * 查询已验券信息
     *
     * @param mtmd 美团门店信息
     * @param code 团购券码
     * @return
     * @throws IOException
     * @throws MtSdkException
     */
    public TuangouReceiptGetconsumedResponse cxyyqxx(WlgbMtMd mtmd, String code) throws MtSdkException {
        MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(mtmd.getMtdeveloperid()), mtmd.getMtsingkey()).build();
        TuangouReceiptGetconsumedRequest tuangouReceiptGetconsumedRequest = new TuangouReceiptGetconsumedRequest();
        tuangouReceiptGetconsumedRequest.setReceiptCode(code);
        MeituanResponse<TuangouReceiptGetconsumedResponse> response = meituanClient.invokeApi(tuangouReceiptGetconsumedRequest, mtmd.getMtaccesstoken());
        if (response.isSuccess()) {
            TuangouReceiptGetconsumedResponse resp = response.getData();
            System.out.println(resp);
            return resp;
        } else {
            System.out.println("调用失败");
            return null;
        }
    }

    /**
     * 撤销验券
     *
     * @param mtmd   美团门店
     * @param code   验券券码
     * @param dealid 套餐id
     * @return
     * @throws MtSdkException
     */
    public TuangouReceiptReverseconsumeResponse cxyq(WlgbMtMd mtmd, String code, String dealid) throws MtSdkException {
        MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(mtmd.getMtdeveloperid()), mtmd.getMtsingkey()).build();
        TuangouReceiptReverseconsumeRequest tuangouReceiptReverseconsumeRequest = new TuangouReceiptReverseconsumeRequest();

        tuangouReceiptReverseconsumeRequest.setAppShopAccount("string");
        tuangouReceiptReverseconsumeRequest.setAppShopAccountName("string");
        tuangouReceiptReverseconsumeRequest.setReceiptCode(code);
        tuangouReceiptReverseconsumeRequest.setDealId(dealid);

        MeituanResponse<TuangouReceiptReverseconsumeResponse> response = meituanClient.invokeApi(tuangouReceiptReverseconsumeRequest, mtmd.getMtaccesstoken());

        if (response.isSuccess()) {
            TuangouReceiptReverseconsumeResponse resp = response.getData();
            System.out.println(resp);
            return resp;
        } else {
            System.out.println("调用失败");
            return null;
        }
    }

    /**
     * 根据门店id 来获取token
     *
     * @param opBizCode 门店id
     * @return
     */
    public WlgbMtMd getToken(String opBizCode) {
        WlgbMtMd md = wlgbMtMdService.queryByShopUuId(opBizCode);
        if (isEmpty(md)) {
            Map<String, Object> map = new HashMap<>();
            map.put("opbizcode", opBizCode);
            md = wlgbMtMdService.queryByopBizCode(map);
            if (isEmpty(md)) {
                return null;
            }
        }
        Date date1 = new Date();
        if (!isEmpty(md) && !isEmpty(md.getEndtime())) {
            Date date2 = new Date(md.getEndtime().getTime());
            int result = date1.compareTo(date2);
            if (result < 0) {
                return md;
            }
        }
        return null;
    }

    /**
     * 将验完券的数据保存到数据库、上传宜搭、修改协议单等
     *
     * @param receiptCode 轰趴顾问输入的券码
     * @param map         验券后的信息
     * @param mtmd        门店信息
     * @param ding        钉钉表数据
     * @param lsh         流水号
     * @param crmbh       crm的编号
     * @param khdh        客户电话
     * @param sfbjdj      是否补交定金
     * @param sfbjdjwk    是否补交定金尾款
     */
    public void saveLog(String receiptCode, Map<String, Object> map, WlgbMtMd mtmd, DingdingEmployee ding, String lsh,
                        String crmbh, String khdh, Integer sfbjdj, Integer sfbjdjwk, YdAppkey ydAppkey, Dingkey dingkey, String token, String sfwk) throws Exception {
        Date date = new Date();
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //保存美团验券记录
        WlgbMtJl wlgbMtJl1 = new WlgbMtJl();
        wlgbMtJl1.setId(IdConfig.uuId());
        wlgbMtJl1.setYqr(ding.getUserid());
        wlgbMtJl1.setYqrxm(ding.getName());
        wlgbMtJl1.setMtqh(map.get("receiptCode").toString());
        wlgbMtJl1.setYqsj(date);
        wlgbMtJl1.setYqjg("验券成功");
        wlgbMtJl1.setDkje((Double) map.get("dealPrice"));
        wlgbMtJl1.setMtyqjg("成功");
        wlgbMtJl1.setDdbh(lsh);
        wlgbMtJlService.save(wlgbMtJl1);

        //保存券码的详细信息
        WlgbMtLog wlgbMtLog = new WlgbMtLog();
        wlgbMtLog.setId(IdConfig.uuId());
        wlgbMtLog.setDdbh(lsh);
        wlgbMtLog.setYqr(ding.getUserid());
        wlgbMtLog.setYqrid(ding.getName());
        wlgbMtLog.setQh(map.get("receiptCode").toString());
        wlgbMtLog.setSpmc(map.get("dealTitle").toString());
        wlgbMtLog.setSpscj(Double.parseDouble(map.get("dealMarketPrice").toString()));
        wlgbMtLog.setSpsmj(Double.parseDouble(map.get("dealPrice").toString()));
        wlgbMtLog.setYqmd(mtmd.getOpbizname());
        wlgbMtLog.setYqsj(date);
        wlgbMtLog.setBz("已验券");
        wlgbMtLog.setYqcs(mtmd.getCityname());
        wlgbMtLog.setYqdm(mtmd.getBranchname());
        wlgbMtLog.setYqmddz(mtmd.getShopaddress());
        wlgbMtLog.setCrmbh(crmbh);
        wlgbMtLog.setKhdh(khdh);
        wlgbMtLogService.save(wlgbMtLog);

        //上传宜搭
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("lsh", lsh);
        jsonObject1.put("je", map.get("dealPrice"));
        jsonObject1.put("fqr", ding.getUserid());
        jsonObject1.put("fqrId", ding.getUserid());
        jsonObject1.put("fqrName", ding.getName());
        jsonObject1.put("fqsj", date);
        jsonObject1.put("djlx", 2);
        jsonObject1.put("sspt", 1);
        //TODO  以后要换成美团的门店id   mtmd.getOpbizcode()
        jsonObject1.put("mtmd", mtmd.getOpenShopUuid());
        jsonObject1.put("hqqm", receiptCode);
        jsonObject1.put("yqsfwc", 1);
        jsonObject1.put("yqjg", 1);
        jsonObject1.put("yqqm", map.get("receiptCode").toString());
        jsonObject1.put("yqsj", date);
        jsonObject1.put("dm", (mtmd.getZh()).substring((mtmd.getZh().length()) - 3));
        jsonObject1.put("qmsj", 1);
        jsonObject1.put("yqr", ding.getUserid());
        jsonObject1.put("yqrId", ding.getUserid());
        jsonObject1.put("yqrName", ding.getName());
        jsonObject1.put("sfsc", 0);
        jsonObject1.put("sftk", 0);
        jsonObject1.put("sfsddj", 0);
        jsonObject1.put("sfscdj", 0);
        jsonObject1.put("khdh", khdh);
        jsonObject1.put("crmbh", crmbh);
        jsonObject1.put("ytje", 0);
        //定金表唯一标识
        jsonObject1.put("djwybs", IdConfig.uuId());
        if (!isEmpty(sfwk)) {
            //尾款
            jsonObject1.put("sfbjdj", 1);
            jsonObject1.put("sfxd", 1);
            jsonObject1.put("ddbh", lsh);
            jsonObject1.put("xdsj", date);
            jsonObject1.put("sfbjdjwk", 0);
            jsonObject1.put("sfxyld", 0);
        } else {
            //定金
            jsonObject1.put("sfbjdj", sfbjdj != null ? sfbjdj : 0);
            jsonObject1.put("sfxd", 0);
            if (sfbjdjwk != null) {
                //定金的尾款
                jsonObject1.put("sfbjdjwk", 1);
                jsonObject1.put("sfxyld", 1);
            } else {
                //定金
                jsonObject1.put("sfbjdjwk", 0);
                jsonObject1.put("sfxyld", 0);
            }
        }
        boolean sfscydcg = false;
        //新增宜搭实例  不管是不是定金、定金尾款、尾款 都需要上传
        GatewayResult gatewayResult = new GatewayResult();
        for (int j = 0; j < 10; j++) {
            gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObject1.toJSONString(), ding.getUserid(), "FORM-VJ86608110SZP416XSK260PKXR3M1J478HF2LZ1");
            if (!gatewayResult.getSuccess()) {
                if (j == 9) {
                    String context1 = "验券成功同步表单出错了，券码：" + map.get("receiptCode").toString() + "，错误原因：" + gatewayResult.toString();
                    DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                    String text = "您提交的新美大验券，验券失败了！";
                    text += "\n\n券码：" + map.get("receiptCode").toString();
                    text += "\n\n送达时间：" + df2.format(new Date());
                    text += "\n\n失败原因:" + gatewayResult.getErrorMsg();
                    DingDBConfig.sendGztzText(dingkey, ding.getUserid(), text);
                }
            } else {
                sfscydcg = true;
                String text = "您提交的新美大验券，验券完成了！";
                text += "\n\n券码：" + map.get("receiptCode").toString();
                text += "\n\n抵扣金额：" + map.get("dealPrice").toString();
                text += "\n\n送达时间：" + df2.format(new Date());
                DingDBConfig.sendGztzText(dingkey, ding.getUserid(), text);
                break;
            }
        }
        //如果是补交的，需要去修改协议单，下面就是通过接口修改宜搭协议单实例，修改后宜搭会自动调用接口再去修改数据库
        if ((sfbjdjwk != null) && (isEmpty(sfwk)) && (sfscydcg)) {
            List<WlgbJdDjbbd> list2 = wlgbJdDjbbdService.queryByLshAndSfSc(lsh);
            String finalToken = token;
            list2.forEach(l -> {
                JSONObject jsonObject2 = new JSONObject();
                jsonObject2.put("sfbjdjwk", 1);
                jsonObject2.put("sfxyld", 1);
                for (int j = 0; j < 10; j++) {
                    GatewayResult gatewayResult2 = new GatewayResult();
                    try {
                        gatewayResult2 = DingBdLcConfig.xgBdSl(finalToken, ydAppkey, l.getFqrId(), l.getFormInstId(), jsonObject1.toJSONString());
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    if (!gatewayResult2.getSuccess()) {
                        if (j == 9) {
                            String context1 = "验券成功，是补交同步出错了，券码：" + l.getLsh() + "，错误原因：" + gatewayResult2.getErrorMsg();
                            try {
                                DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                            } catch (ApiException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    } else {
                        break;
                    }
                }
            });
        }
    }

    /**
     * 尾款验券 处理
     *
     * @param receiptCode 轰趴顾问手动输入的券码
     * @param lsh         流水号
     * @param je          所有券码总金额
     * @param wkyqlist    所有券码关键数据
     * @param slid        协议单实例id
     * @param ding        钉钉表
     * @param ydAppkey
     * @param dingkey
     * @param token
     * @throws Exception
     */
    public void saveXydByWk(String receiptCode, String lsh, double je, List<JSONObject> wkyqlist, String slid, DingdingEmployee ding, YdAppkey ydAppkey, Dingkey dingkey, String token) throws Exception {
        TbXyd tbXyd = tbXydService.queryByDdBh(lsh);
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (tbXyd != null) {
            JSONObject jsonObject3 = new JSONObject();
            double xbjdj = tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0;
            //数据库中如果补交金额是0，那就直接将宜搭改成线上补交
            if (xbjdj == 0) {
                jsonObject3.put("sfbj", "是");
                jsonObject3.put("xcdzjbj", je);
                jsonObject3.put("xbjdj", je);
                jsonObject3.put("xzzfybj", 0.0);
                jsonObject3.put("xbjdjlx", "2");
                jsonObject3.put("xxsbj", je);
                jsonObject3.put("xsbjzbd", wkyqlist);
                GatewayResult gatewayResult = new GatewayResult();
                for (int j = 0; j < 10; j++) {
                    //修改下单表单的实例
                    gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, ding.getUserid(), slid, jsonObject3.toJSONString());
                    if (!gatewayResult.getSuccess()) {
                        if (j == 9) {
                            String text = "店长提交的补交定金美团验券，验券完成但修改协议单失败了！";
                            text += "\n\n券码：" + receiptCode;
                            text += "\n系统稍后将自动修改协议单，请注意工作通知";
                            text += "\n\n送达时间：" + df2.format(new Date());
                            text += "\n\n" + gatewayResult.getErrorMsg();
                            text += "\n\n订单编号：" + lsh;
                            DingDBConfig.sendGztzText(dingkey, "15349026426046931", text);
                            DingDBConfig.sendGztzText(dingkey, ding.getUserid(), text);
                        }
                    } else {
                        break;
                    }
                }
            } else {
                JSONObject data = null;
                for (int i = 0; i < 5; i++) {
                    //获取宜搭协议单表单数据
                    String s = YdConfig.hqBdSj(ydAppkey.getAppkey(), ydAppkey.getToken(), slid);
                    if (!"error".equals(s)) {
                        JSONObject jsonObject5 = JSONObject.parseObject(s);
                        if (jsonObject5.getBoolean("success")) {
                            JSONObject result = jsonObject5.getJSONObject("result");
                            if (result != null && result.size() > 0) {
                                data = result.getJSONObject("formData");
                                break;
                            }
                        }
                    }
                    if (i == 4) {
                        try {
                            String context1 = "补交定金验券查询订单失败，原因：" + s;
                            DingQunSend.send(context1, dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                        } catch (ApiException e) {
                            e.printStackTrace();
                        }
                    }
                }
                if (data != null) {
                    if ("2".equals(tbXyd.getXbjdjlx())) {
                        //线上补交定金子表单
                        JSONArray xsbjzbd = data.getJSONArray("xsbjzbd");
                        jsonObject3.put("sfbj", "是");
                        jsonObject3.put("xbjdjlx", "2");
                        double je1 = je;
                        if (xsbjzbd != null && xsbjzbd.size() > 0) {
                            for (Object o : xsbjzbd) {
                                JSONObject j = (JSONObject) o;
                                Double xsbjje = j.getDouble("xsbjje") != null ? j.getDouble("xsbjje") : 0;
                                je += xsbjje;
                                if (xsbjje > 0) {
                                    wkyqlist.add(j);
                                }
                            }
                        }
                        jsonObject3.put("xcdzjbj", je1 + (tbXyd.getXcdzjbj() != null ? tbXyd.getXcdzjbj() : 0.0));
                        jsonObject3.put("xbjdj", je1 + (tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0));
                        jsonObject3.put("xxsbj", je);
                        jsonObject3.put("xsbjzbd", wkyqlist);
                    } else if ("1".equals(tbXyd.getXbjdjlx())) {

                        jsonObject3.put("sfbj", "是");
                        jsonObject3.put("xbjdjlx", "e9d99d68afc54a25bdf08c8cb9dd767e");

                        jsonObject3.put("xcdzjbj", je + (tbXyd.getXcdzjbj() != null ? tbXyd.getXcdzjbj() : 0.0));
                        jsonObject3.put("xbjdj", je + (tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0));
                        jsonObject3.put("xxsbj", je);
                        jsonObject3.put("xsbjzbd", wkyqlist);
                    } else if ("e9d99d68afc54a25bdf08c8cb9dd767e".equals(tbXyd.getXbjdjlx())) {
                        jsonObject3.put("sfbj", "是");
                        jsonObject3.put("xbjdjlx", "e9d99d68afc54a25bdf08c8cb9dd767e");

                        double je1 = je;
                        JSONArray xsbjzbd = data.getJSONArray("xsbjzbd");
                        if (xsbjzbd != null && xsbjzbd.size() > 0) {
                            for (Object o : xsbjzbd) {
                                JSONObject j = (JSONObject) o;
                                double xsbjje = j.getDouble("xsbjje") != null ? j.getDouble("xsbjje") : 0;
                                je += xsbjje;
                                if (xsbjje > 0) {
                                    wkyqlist.add(j);
                                }
                            }
                        }
                        jsonObject3.put("xcdzjbj", je1 + (tbXyd.getXcdzjbj() != null ? tbXyd.getXcdzjbj() : 0.0));
                        jsonObject3.put("xbjdj", je1 + (tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0.0));
                        jsonObject3.put("xxsbj", je);
                        jsonObject3.put("xsbjzbd", wkyqlist);
                    }
                    for (int i = 0; i < 5; i++) {
                        System.out.println("这是第" + i + "次修改了");
                        GatewayResult gatewayResult = null;
                        try {
                            //修改表单实例的参数
                            gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, ding.getUserid(), slid, jsonObject3.toJSONString());
                        } catch (Exception e) {
                            gatewayResult = new GatewayResult();
                            e.printStackTrace();
                        }
                        if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                            String context1 = "补交定金同步协议单宜搭错误,第" + i;
                            context1 += "\n\n错误信息:" + gatewayResult.getErrorMsg();
                            context1 += "\n送达时间：" + df2.format(new Date());
                            try {
                                DingDBConfig.sendGztzText(dingkey, "15349026426046931", context1);
                            } catch (ApiException e) {
                                e.printStackTrace();
                            }
                        } else {
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 查询原北极星有效session，就是根据美团的token查询北极星的session
     *
     * @param request
     * @return
     * @throws IOException
     * @throws MtSdkException
     */
    @RequestMapping(value = "/sessionquery")
    public ResultMt sessionquery(HttpServletRequest request) throws IOException, MtSdkException {
        MeituanClient meituanClient = DefaultMeituanClient.builder(developerId, SignKey).build();
        PageQuerySessionRequest pageQuerySessionRequest = new PageQuerySessionRequest();
        pageQuerySessionRequest.setAppKey(AppKey);
        pageQuerySessionRequest.setLastIndexId(1);
        pageQuerySessionRequest.setPageSize(100);
        MeituanResponse<PageQuerySessionResponse> response = meituanClient.invokeApi(pageQuerySessionRequest);
        if (response.isSuccess()) {
            PageQuerySessionResponse resp = response.getData();
            System.out.println(resp);
        } else {
            System.out.println("调用失败");
        }
        return ResultMt.OKMT();
    }

    /**
     * 迁移原北极星session，只能迁移一次，如果下次还需要使用请直接调用 pageQuerySessionTokenMapping方法
     *
     * @param request
     * @return
     * @throws IOException
     * @throws MtSdkException
     */
    @RequestMapping(value = "/migrateSession")
    public ResultMt migrateSession(HttpServletRequest request) throws MtSdkException, IOException {
        List<Map> list = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("session", "28f11be1d7f69122dd083903ab39cd161b3547ed");
        map.put("appkey", "c23e31427e8db956");
        map.put("did", developerIdJM);
        map.put("dsk", SignKeyJM);
        map.put("isjm", "0");
        list.add(map);
        map.put("session", "5efa3bc0daebe317c82b8f15ed748558095d3708");
        map.put("appkey", "a771979820d7bd32");
        map.put("did", developerId);
        map.put("dsk", SignKey);
        map.put("isjm", "1");
        list.add(map);
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> mmm = list.get(i);
            MeituanClient meituanClient = DefaultMeituanClient.builder(Long.parseLong(mmm.get("did").toString()), mmm.get("dsk").toString()).build();
            MigrateSessionRequest migrateSessionRequest = new MigrateSessionRequest();
            migrateSessionRequest.setSession(mmm.get("session").toString());
            migrateSessionRequest.setAppKey(mmm.get("appkey").toString());
            MeituanResponse<MigrateSessionResponse> response = meituanClient.invokeApi(migrateSessionRequest);
            if (response.isSuccess()) {
                MigrateSessionResponse resp = response.getData();
                pageQuerySessionTokenMapping(mmm.get("session").toString(), Long.parseLong(mmm.get("did").toString()), mmm.get("dsk").toString());
                System.out.println(resp);
            } else {
                //以后每次批量更新都可以用这个，前提是门店的授权Token过期了，如果不用这个方法就直接在网页上手动一个个的授权也行
                pageQuerySessionTokenMapping(mmm.get("session").toString(), Long.parseLong(mmm.get("did").toString()), mmm.get("dsk").toString());
                System.out.println("批量更新完");
            }
            //调用阿里云函数计算的异步函数
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("isjm", mmm.get("isjm").toString());
            paramMap.put("uuid", IdConfig.uuId());
            //在这里进行异步调用函数计算里面的函数即可
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/mtyq/migrationTaskDetail", paramMap);
        }
        return ResultMt.OKMT();
    }

    /**
     * 查询已迁移原北极星session和合作中心token的映射关系,可以获取所有门店的token，不再需要通过url一个个的授权了
     *
     * @param session     北极星账号对应的session
     * @param developerId 美团开发者id
     * @param SignKey     美团singkey
     * @return
     * @throws IOException
     * @throws MtSdkException
     */
    public ResultMt pageQuerySessionTokenMapping(String session, Long developerId, String SignKey) throws IOException, MtSdkException {
        MeituanClient meituanClient = DefaultMeituanClient.builder(developerId, SignKey).build();
        List<Mapping> allItems = new ArrayList<>();
        fetchAllData(meituanClient, session, 0, 100, allItems);
        //此时，allItems里面存放了所有的北极星门店id和美团门店id、美团token、刷新token、过期时间等信息，但是没有北极星门店名称（也没有美团门店名称）
        for (Mapping m : allItems) {
            WlgbMtMd md = new WlgbMtMd();
            md.setMtaccesstoken(m.getAccessToken());
            md.setMtrefreshtoken(m.getRefreshToken());
            md.setOpbizcode(m.getOpBizCode());
//            md.setOpbizname("门店名称是什么");
            Calendar nowTime = Calendar.getInstance();
            nowTime.setTime(new Date());
            nowTime.add(Calendar.SECOND, m.getExpireIn());
            md.setEndtime(nowTime.getTime());
            md.setMtdeveloperid(developerId + "");
            md.setMtsingkey(SignKey);
            //北极星门店id  openShopUuid
            md.setOpenShopUuid(m.getOpenShopUuid());

            Map<String, Object> map = new HashMap<>();
            map.put("openShopUuid", m.getOpenShopUuid());
            WlgbMtMd md1 = wlgbMtMdService.queryByopBizCode(map);
            if (!isEmpty(md1)) {
                md.setId(md1.getId());
                wlgbMtMdService.updateById(md);
            } else {
                System.out.println("暂不新增");
            }
        }
        return ResultMt.OK(allItems);
    }

    /**
     * 递归方法，用于分页获取所有映射关系数据
     *
     * @param meituanClient 美团客户端实例
     * @param session       会话标识
     * @param offset        当前请求的偏移量
     * @param limit         每次请求的数据条数
     * @param allItems      存储所有数据的列表
     * @throws IOException    网络请求异常
     * @throws MtSdkException 美团 SDK 异常
     */
    private void fetchAllData(MeituanClient meituanClient, String session, int offset, int limit, List<Mapping> allItems) throws IOException, MtSdkException {
        // 构建请求对象
        PageQuerySessionTokenMappingRequest request = new PageQuerySessionTokenMappingRequest();
        request.setOffset(offset);
        request.setLimit(limit);
        request.setSession(session);
        // 调用 API 获取响应
        MeituanResponse<PageQuerySessionTokenMappingResponse> response = meituanClient.invokeApi(request);
        // 处理响应
        if (response.isSuccess()) {
            // 解析响应数据
            PageQuerySessionTokenMappingResponse resp = response.getData();
            // 将当前页的数据添加到 allItems 列表中
            allItems.addAll(resp.getMappingList());
            // 获取总数据条数
            int total = resp.getTotal();
            // 如果当前偏移量加上限制数量小于总数据条数，继续递归调用
            if (offset + limit < total) {
                fetchAllData(meituanClient, session, offset + limit, limit, allItems);
            }
        } else {
            // 输出调用失败的信息
            System.out.println("调用失败");
        }
    }

    /**
     * 查询已迁移原北极星session明细
     *
     * @param request
     * @return
     * @throws MtSdkException
     */
    @RequestMapping(value = "/migrationTaskDetail")
    public ResultMt migrationTaskDetail(HttpServletRequest request) throws MtSdkException, IOException {
        String isjm = request.getParameter("isjm");
        Long did = 0L;
        String session = "", dsk = "";
        if (("0".equals(isjm))) {
            session = "28f11be1d7f69122dd083903ab39cd161b3547ed";
            did = developerIdJM;
            dsk = SignKeyJM;
        } else {
            session = "5efa3bc0daebe317c82b8f15ed748558095d3708";
            did = developerId;
            dsk = SignKey;
        }
        MeituanClient meituanClient = DefaultMeituanClient.builder(did, dsk).build();
        List<QuerySessionTokenTaskDetailTO> allItems = new ArrayList<>();
        fetchAllDataBymigrationTaskDetail(meituanClient, session, 0, 100, allItems);
        for (QuerySessionTokenTaskDetailTO qt : allItems) {
            WlgbMtMd md = new WlgbMtMd();
            md.setOpbizname(qt.getOpBizName());
            md.setOpbizcode(qt.getOpBizCode());
            Map<String, Object> map = new HashMap<>();
            map.put("opbizcode", qt.getOpBizCode());
            WlgbMtMd md1 = wlgbMtMdService.queryByopBizCode(map);
            if (!isEmpty(md1)) {
                md.setId(md1.getId());
                wlgbMtMdService.updateById(md);
            } else {
                System.out.println("暂不新增");
            }
        }
        return ResultMt.OKMT();
    }

    /**
     * 递归方法，用于分页获取所有映射关系数据
     *
     * @param meituanClient 美团客户端实例
     * @param session       会话标识
     * @param offset        当前请求的偏移量
     * @param limit         每次请求的数据条数
     * @param allItems      存储所有数据的列表
     * @throws IOException    网络请求异常
     * @throws MtSdkException 美团 SDK 异常
     */
    private void fetchAllDataBymigrationTaskDetail(MeituanClient meituanClient, String session, int offset, int limit, List<QuerySessionTokenTaskDetailTO> allItems) throws IOException, MtSdkException {
        // 构建请求对象
        MigrateTaskDetailRequest migrateTaskDetailRequest = new MigrateTaskDetailRequest();
        migrateTaskDetailRequest.setOffset(offset);
        migrateTaskDetailRequest.setLimit(limit);
        migrateTaskDetailRequest.setSession(session);
        // 调用 API 获取响应
        MeituanResponse<MigrateTaskDetailResponse> response = meituanClient.invokeApi(migrateTaskDetailRequest);
        // 处理响应
        if (response.isSuccess()) {
            // 解析响应数据
            MigrateTaskDetailResponse resp = response.getData();
            // 将当前页的数据添加到 allItems 列表中
            allItems.addAll(resp.getTaskDetailList());
            // 获取总数据条数
            int total = resp.getTotal();
            // 如果当前偏移量加上限制数量小于总数据条数，继续递归调用
            if (offset + limit < total) {
                fetchAllDataBymigrationTaskDetail(meituanClient, session, offset + limit, limit, allItems);
            }
        } else {
            // 输出调用失败的信息
            System.out.println("调用失败");
        }
    }

    /**
     * 混淆后门店id转换(原北极星转合作中心)
     * 原北极星混淆后点评门店id转换为合作中心混淆后美团门店id
     * 本接口的作用就是把北极星更新后的门店数据补全，只补充opBizCode，然后手动授权就没问题了
     *
     * @param httprequest
     * @return
     */
    @RequestMapping(value = "/openShopUuidToOpPoiId")
    private ResultMt openShopUuidToOpPoiId(HttpServletRequest httprequest) throws MtSdkException {
        Map<String, Object> map1 = new HashMap<>();
        map1.put("sfsc", "0");
        List<WlgbMtToken> tokenlist = wlgbMtTokenService.queryAllListById();
        for (WlgbMtToken token : tokenlist) {
            Map<String, Object> map = new HashMap<>();
            map.put("zh", token.getZh());
            List<WlgbMtMd> ll = wlgbMtMdService.queryByopBizCodeIsNull(map);
            if (ll.isEmpty()) {
                continue;
            }
            ArrayList<String> appKeyList = new ArrayList<>();
            for (WlgbMtMd mm : ll) {
                appKeyList.add(mm.getOpenShopUuid());
            }
            MeituanClient meituanClient = DefaultMeituanClient.builder(Long.valueOf(token.getMtdeveloperid()), token.getMtsingkey()).build();
            TransferOpenShopUuidToOpPoiIdRequest transferOpenShopUuidToOpPoiIdRequest = new TransferOpenShopUuidToOpPoiIdRequest();
            transferOpenShopUuidToOpPoiIdRequest.setAppKey(token.getAppkey());
            transferOpenShopUuidToOpPoiIdRequest.setOpenShopUuids(appKeyList);
            MeituanResponse<List<ShopToOpenShopMappingDTO>> response = meituanClient.invokeApi(transferOpenShopUuidToOpPoiIdRequest);
            if (response.isSuccess()) {
                List<ShopToOpenShopMappingDTO> resp = response.getData();
                for (ShopToOpenShopMappingDTO dto : resp) {
                    Map<String, Object> mmp = new HashMap<>();
                    mmp.put("openShopUuid", dto.getOpenShopUuid());
                    WlgbMtMd md1 = wlgbMtMdService.queryByopBizCode(mmp);
                    if (!isEmpty(md1)) {
                        md1.setId(md1.getId());
                        md1.setOpbizcode(dto.getOpPoiId());
                        wlgbMtMdService.updateById(md1);
                    }
                }
                System.out.println(resp);
            } else {
                System.out.println("调用失败");
            }
        }
        return ResultMt.OKMT();
    }

    /**
     * 流量灰度配置（指定开发者和session）
     *
     * @param request
     * @return
     * @throws MtSdkException
     * @throws IOException
     */
    @RequestMapping(value = "/configBySession")
    public ResultMt configBySession(HttpServletRequest request) throws MtSdkException, IOException {
        String isjm = request.getParameter("isjm");
        Long did = 0L;
        String session = "", dsk = "", appkey = "";
        if (("1".equals(isjm))) {
            session = "28f11be1d7f69122dd083903ab39cd161b3547ed";
            did = developerIdJM;
            appkey = "c23e31427e8db956";
            dsk = SignKeyJM;
        } else {
            session = "5efa3bc0daebe317c82b8f15ed748558095d3708";
            did = developerId;
            appkey = "a771979820d7bd32";
            dsk = SignKey;
        }
        MeituanClient meituanClient = DefaultMeituanClient.builder(did, dsk).build();
        GrayConfigBaseSessionRequest grayConfigBaseSessionRequest = new GrayConfigBaseSessionRequest();
        grayConfigBaseSessionRequest.setAppKey(appkey);
        grayConfigBaseSessionRequest.setSession(session);
        grayConfigBaseSessionRequest.setRatio(1000);
        MeituanResponse<String> response = meituanClient.invokeApi(grayConfigBaseSessionRequest);
        if (response.isSuccess()) {
            String resp = response.getData();
            System.out.println(resp);
        } else {
            System.out.println("调用失败");
        }
        return ResultMt.OKMT();
    }

    /**
     * 到店综合品牌批量授权门店
     *
     * @return
     */
    @RequestMapping(value = "/batchAuthorize")
    public ResultMt batchAuthorize() throws IOException {
        String url = "https://api-open-cater.meituan.com/ddzh/approval/batchAuthorize";
        // 数据库中所有的token
        Map<String, Object> map1 = new HashMap<>();
        map1.put("sfsc", "0");
        List<WlgbMtToken> tokenlist = wlgbMtTokenService.queryAllListById();
        for (WlgbMtToken token : tokenlist) {
            Map<String, Object> map = new HashMap<>();
            map.put("mtdeveloperid", token.getMtdeveloperid());
            map.put("mtsingkey", token.getMtsingkey());
            List<WlgbMtMd> mttokenList = wlgbMtMdService.queryByToken(map);
            List<Long> ll = new ArrayList<>();
            for (WlgbMtMd mtmd : mttokenList) {
                if (isEmpty(mtmd.getEndtime())) {
                    ll.add(Long.valueOf(mtmd.getMtmdzzid()));
                } else {
                    Date date1 = new Date();
                    Date date2 = new Date(mtmd.getEndtime().getTime());
                    int result = date1.compareTo(date2);
                    if ((!isEmpty(mtmd.getMtmdzzid())) && (result > 0)) {
                        ll.add(Long.valueOf(mtmd.getMtmdzzid()));
                    }
                }
            }
            // 分批处理门店ID
            int batchSize = 500;
            for (int i = 0; i < ll.size(); i += batchSize) {
                int end = Math.min(i + batchSize, ll.size());
                List<Long> batch = ll.subList(i, end);
                String sjc = (System.currentTimeMillis() / 1000) + "";
                Map<String, String> param = new HashMap();
                param.put("charset", "UTF-8");
                param.put("developerId", token.getMtdeveloperid());
                param.put("timestamp", sjc);
                param.put("version", "2");
                param.put("businessId", "58");
                JSONObject json = new JSONObject();
                json.put("shopIds", batch);
                json.put("scope", "tuangou,ugc,merchantdata");
                json.put("shopType", 1);
                json.put("bizAccLogin", token.getZh());
                param.put("biz", json.toJSONString());
                String sign = MeiTuanConfig.getSign(token.getMtsingkey(), param);
                param.put("sign", sign);
                log.info("****请求参数********{}", param);
                String jg = HttpClientUtil.postAsync(url, param);
                log.info("------返回值-----------{}", jg);
            }
        }
        return ResultMt.OKMT();
    }

    /**
     * 验券记录查询
     *
     * @param request
     * @return
     * @throws MtSdkException
     * @throws IOException
     */
    @RequestMapping(value = "/querylistbydate")
    public Result querylistbydate(HttpServletRequest request) throws MtSdkException, IOException {
        String opbizcode = request.getParameter("opbizcode");
        if (isEmpty(opbizcode)) {
            return Result.OK("缺少参数");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("opbizcode", opbizcode);
        List<WlgbMtMd> ll = wlgbMtMdService.queryByToken(map);
        if (isEmpty(ll)) {
            return Result.OK("不存在的门店ID");
        }
        String dsk = ll.get(0).getMtsingkey();
        Long did = Long.valueOf(ll.get(0).getMtdeveloperid());
        MeituanClient meituanClient = DefaultMeituanClient.builder(did, dsk).build();
        TuangouReceiptQuerylistbydateRequest tuangouReceiptQuerylistbydateRequest = new TuangouReceiptQuerylistbydateRequest();
        tuangouReceiptQuerylistbydateRequest.setDate("2025-05-20");
        tuangouReceiptQuerylistbydateRequest.setBizType(0);
        tuangouReceiptQuerylistbydateRequest.setOffset(0);
        tuangouReceiptQuerylistbydateRequest.setType(0);
        tuangouReceiptQuerylistbydateRequest.setLimit(300);
        String appAuthToken = ll.get(0).getMtaccesstoken();
        MeituanResponse<TuangouReceiptQuerylistbydateResponse> response = meituanClient.invokeApi(tuangouReceiptQuerylistbydateRequest, appAuthToken);
        if (response.isSuccess()) {
            TuangouReceiptQuerylistbydateResponse resp = response.getData();
            System.out.println(resp);
        } else {
            System.out.println("调用失败");
        }
        return Result.OK();
    }


    /////////////////////////下面接口是为了同步宜搭表单而写的，主要是为了维护以前的北极星接口///////////////////////////

    /**
     * 删除门店
     *
     * @param request
     * @return
     * @throws MtSdkException
     * @throws IOException
     */
    @RequestMapping(value = "/mdSc")
    public Result mdSc(HttpServletRequest request) throws MtSdkException, IOException {
        String openshopuuid = request.getParameter("open_shop_uuid");
        if (isEmpty(openshopuuid)) {
            return Result.OK("缺少参数");
        } else {
            Map<String, Object> map = new HashMap<>();
            map.put("openShopUuid", openshopuuid);
            WlgbMtMd md1 = wlgbMtMdService.queryByopBizCode(map);
            if (md1 != null) {
                wlgbMtMdService.delete(md1);
            }
        }
        return Result.OK();
    }

    /**
     * 新增门店
     *
     * @param request
     * @return
     * @throws MtSdkException
     * @throws IOException
     */
    @RequestMapping(value = "/mdadd")
    public Result mdadd(HttpServletRequest request) throws MtSdkException, IOException {
        String openshopuuid = request.getParameter("open_shop_uuid");
        String cityname = request.getParameter("cityname");
        String shopaddress = request.getParameter("shopaddress");
        String branchname = request.getParameter("branchname");
        String shopname = request.getParameter("shopname");
        String slid = request.getParameter("slid");
        String zh = request.getParameter("textField_m2k5xifd");
        if (isEmpty(openshopuuid) || isEmpty(cityname) || isEmpty(shopaddress) || isEmpty(branchname) || isEmpty(shopname) || isEmpty(zh)) {
            return Result.OK("缺少参数");
        } else {
            WlgbMtMd md = new WlgbMtMd();
            md.setSlid(slid);
            md.setOpenShopUuid(openshopuuid);
            md.setCityname(cityname);
            md.setShopaddress(shopaddress);
            md.setBranchname(branchname);
            md.setShopname(shopname);
            md.setZh(zh);
            md.setSfsc(0);

            Map<String, Object> map = new HashMap<>();
            map.put("openShopUuid", openshopuuid);
            WlgbMtMd md1 = wlgbMtMdService.queryByopBizCode(map);
            if (!isEmpty(md1)) {
                md.setId(md1.getId());
                wlgbMtMdService.updateById(md);
            } else {
                md.setId(IdConfig.uuId());
                wlgbMtMdService.save(md);
            }
        }
        return Result.OK();
    }


}
