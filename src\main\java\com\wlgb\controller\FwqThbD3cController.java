package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wlgb.config.Result;
import com.wlgb.entity.FwqThbD3c;
import com.wlgb.service2.FwqThbD3cService;
import com.wlgb.service2.TbXydService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.wlgb.config.Tools.isEmpty;

@RestController
@RequestMapping("/wlgbxs/fwqThbD3c")
@Slf4j
public class FwqThbD3cController {

    @Autowired
    private FwqThbD3cService fwqThbD3Service;

    /**
     * 扣补业绩 新增
     *
     * @param request
     * @return
     */
    @RequestMapping("/add")
    public Result add(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("数据不能为空！");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        FwqThbD3c fwqThbD3c = JSON.toJavaObject(jsonObject, FwqThbD3c.class);
        log.info("****datasdatasdatasdatasdatasdatas********{}", datas);
        log.info("****fwqThbD3cfwqThbD3cfwqThbD3cfwqThbD3cfwqThbD3c********{}", fwqThbD3c);
        String lsh = fwqThbD3c.getWybs();
        FwqThbD3c fwqThbD3c1 = new FwqThbD3c();
        if (!isEmpty(lsh)) {
            fwqThbD3c1 = fwqThbD3Service.queryFwqThbD3cByLsh(lsh);
            if (fwqThbD3c1 != null) {
                Date date = new Date(fwqThbD3c.getDateField_km7687p8());
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String dateString = formatter.format(date);
                Map<String, Object> map = new HashMap<>();
                map.put("lsh", lsh);
                map.put("kyj", fwqThbD3c.getNumberField_km7687p4() == null ? "0" : fwqThbD3c.getNumberField_km7687p4());
                map.put("jyj", fwqThbD3c.getNumberField_km7687p5() == null ? "0" : fwqThbD3c.getNumberField_km7687p5());
                //暂时先不修改姓名
//                map.put("xm", fwqThbD3c.getEmployeeField_km7687p6());
                map.put("yjy", dateString);
                fwqThbD3Service.update(map);
            } else {
                //有两种情况1：从审批同步过来 2：直接手动添加
                fwqThbD3c1 = new FwqThbD3c();
                if (jsonObject.getString("ss") != null) {
                    fwqThbD3c1.setKyj(jsonObject.getString("ss"));
                } else {
                    fwqThbD3c1.setKyj(fwqThbD3c.getNumberField_km7687p4() == null ? "0" : fwqThbD3c.getNumberField_km7687p4());
                }
                fwqThbD3c1.setJyj(fwqThbD3c.getNumberField_km7687p5() == null ? "0" : fwqThbD3c.getNumberField_km7687p5());
                if (jsonObject.getString("fdxm") != null) {
                    fwqThbD3c1.setXm(jsonObject.getString("fdxm"));
                } else {
                    fwqThbD3c1.setXm(fwqThbD3c.getFdxm());
                }
                if (jsonObject.getString("wybs") != null) {
                    fwqThbD3c1.setLsh(jsonObject.getString("wybs"));
                } else {
                    fwqThbD3c1.setLsh(fwqThbD3c.getWybs());
                }
                Date date = new Date();
                if (fwqThbD3c.getDateField_km7687p8() != null) {
                    date = new Date(fwqThbD3c.getDateField_km7687p8());
                }
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String dateString = formatter.format(date);
                fwqThbD3c1.setYjy(dateString);
                fwqThbD3Service.save(fwqThbD3c1);
            }
        }
        return Result.OK();
    }

    /**
     * 扣补业绩 修改
     *
     * @param request
     * @return
     */
    @RequestMapping("/update")
    public Result update(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        log.info("****datasdatasdatasdatas********{}", datas);
        if (datas == null || "".equals(datas)) {
            return Result.error("数据不能为空！");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        FwqThbD3c fwqThbD3c = JSON.toJavaObject(jsonObject, FwqThbD3c.class);
        String lsh = fwqThbD3c.getWybs();
        FwqThbD3c fwqThbD3c1 = new FwqThbD3c();
        if (!isEmpty(lsh)) {
            fwqThbD3c1 = fwqThbD3Service.queryFwqThbD3cByLsh(lsh);
            if (fwqThbD3c1 != null) {
                Date date = new Date(fwqThbD3c.getDateField_km7687p8());
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String dateString = formatter.format(date);
                Map<String, Object> map = new HashMap<>();
                map.put("lsh", lsh);
                map.put("kyj", fwqThbD3c.getNumberField_km7687p4() == null ? "0" : fwqThbD3c.getNumberField_km7687p4());
                map.put("jyj", fwqThbD3c.getNumberField_km7687p5() == null ? "0" : fwqThbD3c.getNumberField_km7687p5());
                //暂时先不修改姓名
//                map.put("xm", fwqThbD3c.getFdxm());
                map.put("yjy", dateString);
                log.info("*****fwqThbD3c1fwqThbD3c1*******{}", map);
                fwqThbD3Service.update(map);
            }
        }
        return Result.OK();
    }

    /**
     * 扣补业绩 删除
     *
     * @param request
     * @return
     */
    @RequestMapping("/delete")
    public Result delete(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return Result.error("数据不能为空！");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        FwqThbD3c fwqThbD3c = JSON.toJavaObject(jsonObject, FwqThbD3c.class);
        String lsh = fwqThbD3c.getWybs();
        FwqThbD3c fwqThbD3c1 = new FwqThbD3c();
        if (!isEmpty(lsh)) {
            fwqThbD3c1 = fwqThbD3Service.queryFwqThbD3cByLsh(lsh);
            if (fwqThbD3c1 != null) {
                Date date = new Date(fwqThbD3c.getDateField_km7687p8());
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String dateString = formatter.format(date);
                Map<String, Object> map = new HashMap<>();
                map.put("lsh", lsh);
                fwqThbD3Service.delete(map);
            }
        }
        return Result.OK();
    }

}
