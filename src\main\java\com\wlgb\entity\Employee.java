package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 部门信息 钉钉同步
 *
 * <AUTHOR>
 */
@Data
@Table(name = "dingding_employee")
public class Employee {
    @Id
    @KeySql(useGeneratedKeys = true)
    public String userid;
    public String name;
    public String active;
    public String avatar;
    public String departid;
    public String position;
    public String mobile;
    public String tel;
    public String workplace;
    public String remark;
    public String email;
    public String jobnumber;
    public String dingid;
}
