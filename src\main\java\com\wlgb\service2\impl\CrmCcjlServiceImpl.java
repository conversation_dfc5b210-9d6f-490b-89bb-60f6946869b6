package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmCcjl;
import com.wlgb.mapper.CrmCcjlMapper;
import com.wlgb.service2.CrmCcjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/28 16:45
 */
@Service
@DS(value = "second")
public class CrmCcjlServiceImpl implements CrmCcjlService {
    @Resource
    private CrmCcjlMapper crmCcjlMapper;

    @Override
    public void save(CrmCcjl crmCcjl) {
        crmCcjlMapper.insertSelective(crmCcjl);
    }
}
