package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqHnfp;
import com.wlgb.entity.FwqHnfpUser;
import com.wlgb.entity.vo.FwqHnfpVo;
import com.wlgb.mapper.FwqHnfpMapper;
import com.wlgb.service2.FwqHnfpService;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

import static com.wlgb.config.Tools.isEmpty;

@Service
@DS(value = "second")
public class FwqHnfpServiceImpl implements FwqHnfpService {
    @Resource
    private FwqHnfpMapper fwqHnfpMapper;

    @Override
    public void save(FwqHnfp fwqHnfp) {
        fwqHnfpMapper.insertSelective(fwqHnfp);
    }

    @Override
    public void deleteByPrimaryKey(FwqHnfp fwqHnfp) {
        fwqHnfpMapper.deleteByPrimaryKey(fwqHnfp);
    }

    @Override
    public FwqHnfp selectOneByZyurl(String url) {
        Example example = new Example(FwqHnfp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("zyurl", url);
        //是否有效
        criteria.andEqualTo("sfsc", "0");
        return fwqHnfpMapper.selectOneByExample(example);
    }

    @Override
    public void update(FwqHnfp fwqHnfp) {
        fwqHnfpMapper.updateByPrimaryKey(fwqHnfp);
    }

    @Override
    public List<FwqHnfp> selectAllByXiaoquname(String xiaoquname) {
        FwqHnfp fwqHnfp = new FwqHnfp();
        fwqHnfp.setXiaoquname(xiaoquname);
        fwqHnfp.setSfsc("0");
        return fwqHnfpMapper.select(fwqHnfp);
    }

    @Override
    public List<FwqHnfpVo> selectAllBySousuo(String sousuo, int pageNum, int pageSize) {
        int offset = (pageNum - 1) * pageSize;
        return fwqHnfpMapper.selectGroupByXiaoquName(sousuo, offset, pageSize);
    }
}
