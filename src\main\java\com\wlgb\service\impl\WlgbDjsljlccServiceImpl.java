package com.wlgb.service.impl;

import com.wlgb.entity.WlgbDjsljlcc;
import com.wlgb.mapper.WlgbDjsljlccMapper;
import com.wlgb.service.WlgbDjsljlccService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月22日 17:43
 */
@Service
public class WlgbDjsljlccServiceImpl implements WlgbDjsljlccService {
    @Resource
    private WlgbDjsljlccMapper wlgbDjsljlccMapper;

    @Override
    public void save(WlgbDjsljlcc wlgbDjsljlcc) {
        wlgbDjsljlcc.setCreateTime(new Date());
        wlgbDjsljlccMapper.insertSelective(wlgbDjsljlcc);
    }

    @Override
    public void updateById(WlgbDjsljlcc wlgbDjsljlcc) {
        wlgbDjsljlcc.setUpdateTime(new Date());
        wlgbDjsljlccMapper.updateByPrimaryKeySelective(wlgbDjsljlcc);
    }

    @Override
    public WlgbDjsljlcc queryByTypeAndDdBh(Integer type, String ddbh) {
        Example example = new Example(WlgbDjsljlcc.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("type", type);
        criteria.andEqualTo("ddbh", ddbh);
        return wlgbDjsljlccMapper.selectOneByExample(example);
    }

    @Override
    public WlgbDjsljlcc queryByTypeAndSkBh(Integer type, String skbh) {
        Example example = new Example(WlgbDjsljlcc.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("type", type);
        criteria.andEqualTo("skbh", skbh);
        return wlgbDjsljlccMapper.selectOneByExample(example);
    }

    @Override
    public WlgbDjsljlcc queryBySkBh(String skbh) {
        Example example = new Example(WlgbDjsljlcc.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("skbh", skbh);
        return wlgbDjsljlccMapper.selectOneByExample(example);
    }
}
