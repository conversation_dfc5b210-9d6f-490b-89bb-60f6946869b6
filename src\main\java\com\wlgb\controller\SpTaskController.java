package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.service.*;
import com.wlgb.service2.TbVillaService;
import com.wlgb.service2.TbXydService;
import com.wlgb.service2.WeiLianService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static cn.hutool.core.util.ObjectUtil.isEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 20:42
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/spTask")
public class SpTaskController {
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private WlgbJdBxspjlService wlgbJdBxspjlService;
    @Autowired
    private WlgbJdSpCwlrjdsjService wlgbJdSpCwlrjdsjService;
    @Autowired
    private WlgbJdSpBsftService wlgbJdSpBsftService;
    @Autowired
    private WlgbJdSpCsftService wlgbJdSpCsftService;
    @Autowired
    private WlgbJdYhkService wlgbJdYhkService;
    @Autowired
    private WlgbJdBsmcXgjlService wlgbJdBsmcXgjlService;
    @Autowired
    private WlgbJdYzsqjlService wlgbJdYzsqjlService;
    @Autowired
    private WlgbJdFyfksqjlService wlgbJdFyfksqjlService;
    @Autowired
    private WlgbJdDyzsqjlService wlgbJdDyzsqjlService;
    @Autowired
    private WlgbJdWlbtxjlService wlgbJdWlbtxjlService;
    @Autowired
    private WlgbJdSstyjsqjlService wlgbJdSstyjsqjlService;
    @Autowired
    private WlgbJdJzspjlService wlgbJdJzspjlService;
    @Autowired
    private WlgbJdPtsdkbxsqjlService wlgbJdPtsdkbxsqjlService;
    @Autowired
    private WlgbJdLcxmbxsqjlService wlgbJdLcxmbxsqjlService;
    @Autowired
    private WlgbJdLcxmcgdfksqjlService wlgbJdLcxmcgdfksqjlService;
    @Autowired
    private WlgbJdLcxmyzsqjlService wlgbJdLcxmyzsqjlService;
    @Autowired
    private WlgbJdLcxmdyzsqjlService wlgbJdLcxmdyzsqjlService;
    @Autowired
    private WlgbJdYxcgsqjlService wlgbJdYxcgsqjlService;
    @Autowired
    private WlgbWzycgdzcService wlgbWzycgdzcService;
    @Autowired
    private WlgbJdLcyzjlService wlgbJdLcyzjlService;
    @Autowired
    private WlgbJdLccglcjlService wlgbJdLccglcjlService;
    @Autowired
    private WlgbJdTdjsqjlService wlgbJdTdjsqjlService;
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private WlgbJdDjbbdService wlgbJdDjbbdService;
    @Autowired
    private TbVillaService tbVillaService;
    @Autowired
    private WlgbSpAllService wlgbSpAllService;
    @Autowired
    private WlgbHxyhDzjlService wlgbHxyhDzjlService;

    /**
     * 审批回调
     *
     * @param request
     */
    @RequestMapping(value = "oaSpClTask")
    public void oaSpClTask(HttpServletRequest request) {
        String data = request.getParameter("data");
        JSONObject callBackContent = JSONObject.parseObject(data);
        log.info("***********审批内容************{}", callBackContent);
        String eventType = callBackContent.getString("EventType");
        String processCode = callBackContent.getString("processCode");
        //审批任务开始、结束、取消的回调
        if ("bpms_task_change".equals(eventType)) {
            String result = callBackContent.getString("result");
            //type：start：审批任务开始， finish：审批任务正常结束（完成或转交）
            String type = callBackContent.getString("type");
            if ("agree".equals(result)) {
                if ("PROC-E58A237F-B632-4CE6-BD05-3380F80CFC89".equals(processCode)) {
                    //报销审批
                    bxSpYb(callBackContent);
                    try {
                        fyFkSq2(callBackContent);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                } else if ("PROC-AC51A0D7-B743-4AC3-868B-8FB154B528FE".equals(processCode)) {
                    //预支审批
                    yzSq(callBackContent);
                    try {
                        fyFkSq2(callBackContent);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                } else if ("PROC-7FC13399-5218-4D5A-B09D-07A9C87527AF".equals(processCode)) {
                    //费用付款申请
                    fyFkSq(callBackContent);
                    try {
                        fyFkSq2(callBackContent);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                } else if ("PROC-0C6C8DC6-A30C-4501-90E6-5E5E10A1CEBC".equals(processCode)) {
                    //抵预支
                    dyzSq(callBackContent);
                    try {
                        fyFkSq2(callBackContent);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                } else if ("PROC-F24D04F1-EBF4-4EBD-8151-A89202CC56B1".equals(processCode)) {
//                                zjSq(callBackContent);
                } else if ("PROC-643F4887-014A-40DD-ABEE-959939FC715A".equals(processCode)) {
                    //威廉币提现
                    wlbTxSq(callBackContent);
                    try {
                        fyFkSq2(callBackContent);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                } else if ("PROC-232B690D-7A2F-4AF7-A62F-D9459C916E00".equals(processCode)) {
                    //宿舍退押金申请
                    ssTyjSq(callBackContent);
                } else if ("PROC-65BF6118-991E-4DEC-8271-413763307BD5".equals(processCode)) {
                    //借支审批
                    jzSp(callBackContent);
                } else if ("PROC-E8BA0CDF-A666-4CD7-80B1-D64EC9397DDA".equals(processCode)) {
                    //平台刷单款报销
                    ptSdkBxSq(callBackContent);
                } else if ("PROC-B5B4EE34-C4AD-45FD-AE7F-E1531AD13DE2".equals(processCode)) {
//                                dzyZySq(callBackContent);
                } else if ("PROC-F955B267-CC8D-40DD-93AD-5B40AE17CE65".equals(processCode)) {
                    //乐诚项目报销
                    lcXmBxSq(callBackContent);
                } else if ("PROC-4F470E14-6A9E-4781-AAAB-F84C4753587F".equals(processCode)) {
                    //乐诚项目采购代付款
                    lcXmCgDfkSq(callBackContent);
                } else if ("PROC-C228DBC7-8E8C-4669-8483-22116A934630".equals(processCode)) {
                    //乐诚项目预支申请
                    lcXmYzSq(callBackContent);
                } else if ("PROC-3ABC2EA9-8D38-4564-9E79-EE5630F1F25E".equals(processCode)) {
                    //乐诚项目抵预支申请
                    lcXmDyzSq(callBackContent);
                }
            }
        }
    }


    /**
     * 采购审批同步金蝶
     */
    @RequestMapping(value = "cgSqTbJdTask")
    public void cgSqTbJdTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);


        WlgbJdYxcgsqjl wlgbJdYxcgsqjl = new WlgbJdYxcgsqjl();
        String spbh = jsonObject.getString("textField_kpt1l40v");
        String spbh1 = jsonObject.getString("textField_kpt1mxko");
        spbh = spbh != null && !"".equals(spbh) ? spbh : spbh1;
        wlgbJdYxcgsqjl.setSpbh(spbh);
        WlgbJdYxcgsqjl wlgbJdYxcgsqjl1 = wlgbJdYxcgsqjlService.queryBySpBh(spbh);
        if (wlgbJdYxcgsqjl1 != null) {
            return;
        }
        String bmmc = jsonObject.getString("textField_l4gdvmbc");
        wlgbJdYxcgsqjl.setSqrbm(bmmc);
        String bmid = jsonObject.getString("textField_l4gdvmbd");
        wlgbJdYxcgsqjl.setSqrbmid(bmid);
        String spbt = jsonObject.getString("textField_l4gdvmbe");
        wlgbJdYxcgsqjl.setSpbt(spbt);
        String sqr = jsonObject.getString("textField_l4gdvmbg");
        wlgbJdYxcgsqjl.setSqr(sqr);
        String sqrid = jsonObject.getString("textField_l4gdvmbf");
        wlgbJdYxcgsqjl.setSqrid(sqrid);
        String bsmc = jsonObject.getString("textField_ktksooyq");
        bsmc = bsmc != null ? bsmc : jsonObject.getString("bs");
        bsmc = bsmc != null ? bsmc : jsonObject.getString("selectField_kqlmq57j");
        wlgbJdYxcgsqjl.setBsmc(bsmc);
        String skfs = jsonObject.getString("radioField_kp6hvuh6");
        String skfs1 = jsonObject.getString("fkfs2");
        String skfs2 = jsonObject.getString("radioField_kpgm2a52");
        wlgbJdYxcgsqjl.setFkfs(skfs != null && !"".equals(skfs) ? skfs : skfs1 != null && !"".equals(skfs1) ? skfs1 : skfs2);
        Date cnzzsj = jsonObject.getDate("dateField_l4fczgvl");
        wlgbJdYxcgsqjl.setCnzzsj(cnzzsj);
        Double cnzzje = jsonObject.getDouble("numberField_l4fczgvo");
        wlgbJdYxcgsqjl.setCnzzje(cnzzje);
        String bz = jsonObject.getString("textareaField_kp6hvuh3");
        String bz1 = jsonObject.getString("textareaField_kpgm2a50");
        wlgbJdYxcgsqjl.setBz(bz != null && !"".equals(bz) ? bz : bz1);
        //采购类型
        String cgType = jsonObject.getString("radioField_lmuatoux");
        wlgbJdYxcgsqjl.setCglx(cgType);
        //餐饮集群
        String cyjq = jsonObject.getString("selectField_lmub004b");
        if (cyjq != null && !"".equals(cyjq)) {
            cyjq = cyjq.substring(cyjq.indexOf("-") + 1);
        }
        wlgbJdYxcgsqjl.setCyqybh(cyjq);

        JSONArray list = jsonObject.getJSONArray("tableField_l4fczgvc");

        for (int i = 0; i < list.size(); i++) {
            JSONObject l = list.getJSONObject(i);
            double fyje = l.getDouble("numberField_l4fczgvk");
            if (fyje != 0) {
                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                wlgbJdSpCwlrjdsj.setSpbh(spbh != null && !"".equals(spbh) ? spbh : spbh1);
                wlgbJdSpCwlrjdsj.setSpbt(spbt);
                wlgbJdSpCwlrjdsj.setZt(l.getString("textField_l4fczgvf"));
                wlgbJdSpCwlrjdsj.setZtbm(l.getString("textField_l4fczgvg"));
                wlgbJdSpCwlrjdsj.setFykm(l.getString("textField_l4fczgvi"));
                wlgbJdSpCwlrjdsj.setFykmbm(l.getString("textField_l4fczgvj"));
                wlgbJdSpCwlrjdsj.setFyje(l.getDouble("numberField_l4fczgvk"));
                wlgbJdSpCwlrjdsj.setType(10);

                wlgbJdSpCwlrjdsjService.save(wlgbJdSpCwlrjdsj);
            }
        }
        boolean save = yxCgBxCl(wlgbJdYxcgsqjl.getSpbh(), wlgbJdYxcgsqjl, 0);
        if (save) {
            wlgbJdYxcgsqjl.setSflrjd(1);
        }
        wlgbJdYxcgsqjlService.save(wlgbJdYxcgsqjl);

    }

    @RequestMapping(value = "tjLcYzLcSpDataTask")
    public void tjLcYzLcSpDataTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String spbh = jsonObject.getString("textField_kpt1j37n");

        Integer count = wlgbJdLcyzjlService.queryCountBySpBh(spbh);
        if (count > 0) {
            return;
        }

        WlgbJdLcyzjl wlgbJdLcyzjl = new WlgbJdLcyzjl();
        String spbt = jsonObject.getString("textField_l4gdvmbe");
        wlgbJdLcyzjl.setSpbh(spbh);
        wlgbJdLcyzjl.setBsmc(jsonObject.getString("textField_ku0obdv5"));
        wlgbJdLcyzjl.setSpbt(spbt);
        wlgbJdLcyzjl.setSqrbm(jsonObject.getString("textField_l4gdvmbc"));
        wlgbJdLcyzjl.setSqrbmid(jsonObject.getString("textField_l4gdvmbd"));
        wlgbJdLcyzjl.setSqr(jsonObject.getString("textField_l4gdvmbg"));
        wlgbJdLcyzjl.setSqrid(jsonObject.getString("textField_l4gdvmbf"));
        wlgbJdLcyzjl.setMdlx(jsonObject.getString("radioField_krfqwz2e"));
        wlgbJdLcyzjl.setYzfylx(jsonObject.getString("selectField_krebs1g4"));
        try {
            wlgbJdLcyzjl.setYzje(jsonObject.getDouble("textField_krec06oe"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        wlgbJdLcyzjl.setBz(jsonObject.getString("textareaField_kp6hvuh3"));
        wlgbJdLcyzjl.setCnzzsj(jsonObject.getDate("dateField_l4fczgvl"));
        wlgbJdLcyzjl.setCnzzje(jsonObject.getDouble("numberField_l4fczgvo"));

        JSONArray list = jsonObject.getJSONArray("tableField_l4fczgvc");

        for (int i = 0; i < list.size(); i++) {
            JSONObject l = list.getJSONObject(i);
            double fyje = l.getDouble("numberField_l4fczgvk");
            if (fyje != 0) {
                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                wlgbJdSpCwlrjdsj.setSpbh(spbh);
                wlgbJdSpCwlrjdsj.setSpbt(spbt);
                wlgbJdSpCwlrjdsj.setZt(l.getString("textField_l4fczgvf"));
                wlgbJdSpCwlrjdsj.setZtbm(l.getString("textField_l4fczgvg"));
                wlgbJdSpCwlrjdsj.setFykm(l.getString("textField_l4fczgvi"));
                wlgbJdSpCwlrjdsj.setFykmbm(l.getString("textField_l4fczgvj"));
                wlgbJdSpCwlrjdsj.setFyje(l.getDouble("numberField_l4fczgvk"));
                wlgbJdSpCwlrjdsj.setType(11);

                wlgbJdSpCwlrjdsjService.save(wlgbJdSpCwlrjdsj);
            }
        }
        boolean save = lcYzLcCl(wlgbJdLcyzjl.getSpbh(), wlgbJdLcyzjl, 0);
        if (save) {
            wlgbJdLcyzjl.setSflrjd(1);
        }

        wlgbJdLcyzjlService.save(wlgbJdLcyzjl);
    }


    /**
     * 提交乐诚流程采购审批异步
     */
    @RequestMapping(value = "tjLcCgLcSpDataTask")
    public void tjLcCgLcSpDataTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String spbh = jsonObject.getString("textField_kpt1j37n");

        Integer count = wlgbJdLccglcjlService.queryCountBySpBh(spbh);
        if (count > 0) {
            return;
        }

        WlgbJdLccglcjl wlgbJdLccglcjl = new WlgbJdLccglcjl();
        String spbt = jsonObject.getString("textField_l4gdvmbe");
        wlgbJdLccglcjl.setSpbh(spbh);
        wlgbJdLccglcjl.setBsmc(jsonObject.getString("bsmcq"));
        wlgbJdLccglcjl.setSpbt(spbt);
        wlgbJdLccglcjl.setSqrbm(jsonObject.getString("textField_l4gdvmbc"));
        wlgbJdLccglcjl.setSqrbmid(jsonObject.getString("textField_l4gdvmbd"));
        wlgbJdLccglcjl.setSqr(jsonObject.getString("textField_l4gdvmbg"));
        wlgbJdLccglcjl.setSqrid(jsonObject.getString("textField_l4gdvmbf"));
        wlgbJdLccglcjl.setMdlx(jsonObject.getString("mdlx"));
//        wlgbJdLccglcjl.setFylb(jsonObject.getString("fylb"));
//        wlgbJdLccglcjl.setFklb(jsonObject.getString("selectField_kp5301ky"));
        wlgbJdLccglcjl.setSfdf(jsonObject.getString("radioField_kp49sver"));
//        wlgbJdLccglcjl.setFymx(jsonObject.getString("textareaField_kr09kvwx"));
        wlgbJdLccglcjl.setSmyy(jsonObject.getString("textareaField_kp4qot0r"));
        wlgbJdLccglcjl.setYjje(jsonObject.getDouble("zjez"));
        wlgbJdLccglcjl.setCnzzsj(jsonObject.getDate("dateField_l4fczgvl"));
        wlgbJdLccglcjl.setCnzzje(jsonObject.getDouble("numberField_l4fczgvo"));

        JSONArray list = jsonObject.getJSONArray("tableField_l4fczgvc");

        for (int i = 0; i < list.size(); i++) {
            JSONObject l = list.getJSONObject(i);
            double fyje = l.getDouble("numberField_l4fczgvk");
            if (fyje != 0) {
                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                wlgbJdSpCwlrjdsj.setSpbh(spbh);
                wlgbJdSpCwlrjdsj.setSpbt(spbt);
                wlgbJdSpCwlrjdsj.setZt(l.getString("textField_l4fczgvf"));
                wlgbJdSpCwlrjdsj.setZtbm(l.getString("textField_l4fczgvg"));
                wlgbJdSpCwlrjdsj.setFykm(l.getString("textField_l4fczgvi"));
                wlgbJdSpCwlrjdsj.setFykmbm(l.getString("textField_l4fczgvj"));
                wlgbJdSpCwlrjdsj.setFyje(l.getDouble("numberField_l4fczgvk"));
                wlgbJdSpCwlrjdsj.setType(12);

                wlgbJdSpCwlrjdsjService.save(wlgbJdSpCwlrjdsj);
            }
        }
        boolean save = lcCgLcCl(wlgbJdLccglcjl.getSpbh(), wlgbJdLccglcjl, 0);
        if (save) {
            wlgbJdLccglcjl.setSflrjd(1);
        }

        wlgbJdLccglcjlService.save(wlgbJdLccglcjl);

    }

    /**
     * 退定金申请异步
     */
    @RequestMapping(value = "tjTdjTask")
    public void tjTdjTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String formInstId = request.getParameter("formInstId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null || jsonObject.size() == 0) {
            return;
        }
        WlgbJdTdjsqjl wlgbJdTdjsqjl = JSONObject.toJavaObject(jsonObject, WlgbJdTdjsqjl.class);
        JSONArray sqr = jsonObject.getJSONArray("sqr");
        if (sqr != null && sqr.size() > 0) {
            String sqrid = sqr.getString(0);
            if (sqrid != null && !"".equals(sqrid)) {
                DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserId(sqrid);
                if (employee != null) {
                    wlgbJdTdjsqjl.setSqr(employee.getName());
                    wlgbJdTdjsqjl.setSqrid(employee.getUserid());
                }
            }
        }
        wlgbJdTdjsqjl.setId(IdConfig.uuId());
        wlgbJdTdjsqjl.setSlid(formInstId);
        String fylb = jsonObject.getString("radioField_l2gwshv0");
        wlgbJdTdjsqjl.setFylb(fylb);
        wlgbJdTdjsqjl.setSqrbmid(jsonObject.getString("textField_l3jir9r5"));
        wlgbJdTdjsqjl.setSqrbm(jsonObject.getString("textField_l3jir9r6"));
        wlgbJdTdjsqjl.setSjslid(jsonObject.getString("textField_l3jir9r3"));
        wlgbJdTdjsqjl.setZfjydh(jsonObject.getString("textField_l3gn44he"));
        wlgbJdTdjsqjl.setBz(jsonObject.getString("textareaField_l3kaz94x"));
        wlgbJdTdjsqjl.setZfjydh(jsonObject.getString("textField_l3l6yumr"));
        boolean save = tdjCl(wlgbJdTdjsqjl.getSpbh(), wlgbJdTdjsqjl, 0);
        if (save) {
            wlgbJdTdjsqjl.setSflrjd(1);
        }
        wlgbJdTdjsqjlService.save(wlgbJdTdjsqjl);
    }


    /**
     * 一线采购录入产品库异步
     */
    @RequestMapping(value = "cglrcpTask")
    public void cglrcpTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        System.out.println(jsonObject);
        String bsid = jsonObject.getString("xbsmc2");
        String sqr = jsonObject.getString("sqr");
        String cgbxr = jsonObject.getString("cgbxr");
        String spbh = jsonObject.getString("textField_kpt1l40v");

        //实际申请人
        String sjsqr = jsonObject.getString("employeeField_lg4ep9uq");
        //城市总经理
        String cszjl = jsonObject.getString("employeeField_ln1d41fw");
        //采购类型
        String cgType = jsonObject.getString("radioField_lmuatoux");
        //是否同步至餐饮管理库
        String sftbcygl = jsonObject.getString("sftbcygl");

        JSONArray a = jsonObject.getJSONArray("tableField_kpasvn8r");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        if ("其他采购".equals(cgType) || ("集群餐饮采购".equals(cgType) && "否".equals(sftbcygl))) {
            try {
                sjsqr = xzjq(sjsqr);
                cszjl = xzjq(cszjl);
                //宜搭服务回调还是有时间要求
                for (Object l : a) {
                    JSONObject j = (JSONObject) l;
                    WlgbWzycgdzc wlgbWzycgdzc = new WlgbWzycgdzc();
                    if ("集群餐饮采购".equals(cgType)) {
                        //区域
                        String cyqy = jsonObject.getString("selectField_ln00sodb");
                        JSONObject jsonObject1 = weiLianService.queryCyQyOneByQyName(cyqy);
                        if (jsonObject1 != null && jsonObject1.size() > 0) {
                            wlgbWzycgdzc.setCity(jsonObject1.getString("city"));
                            wlgbWzycgdzc.setBsmc(jsonObject1.getString("qyname"));
                            wlgbWzycgdzc.setBsid(jsonObject1.getString("qyid"));
                        }
                    } else {
                        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(bsid);
                        if (tbVilla == null) {
                            return;
                        }
                        sjsqr = tbVilla.getPid();
                        wlgbWzycgdzc.setCity(tbVilla.getCity());
                        wlgbWzycgdzc.setBsmc(tbVilla.getVname());
                        wlgbWzycgdzc.setBsid(tbVilla.getVid());
                    }

                    DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(sjsqr);
                    if (dingdingEmployee == null) {
                        return;
                    }
                    wlgbWzycgdzc.setFzrid(sjsqr);
                    wlgbWzycgdzc.setFzr(dingdingEmployee.getName());
                    if (cszjl != null && !"".equals(cszjl)) {
                        DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(cszjl);
                        if (dingdingEmployee1 != null) {
                            wlgbWzycgdzc.setCszjl(dingdingEmployee1.getName());
                            wlgbWzycgdzc.setCszjlid(dingdingEmployee1.getUserid());
                        }

                    } else {
                        JSONObject cnQy = weiLianDdXcxService.queryCnQyByBmId(dingdingEmployee.getDepartid());
                        if (cnQy == null || cnQy.size() == 0) {
                            return;
                        }
                        wlgbWzycgdzc.setCszjl(cnQy.getString("cszjl"));
                        wlgbWzycgdzc.setCszjlid(cnQy.getString("cszjlid"));
                    }


                    JSONArray jsonArray = j.getJSONArray("wpmc");
                    System.out.println(jsonArray);
                    jsonArray.forEach(f -> {
                        JSONObject q = (JSONObject) f;
                        String wpmc = q.getString("title");
                        wlgbWzycgdzc.setWpmc(wpmc);
                    });
                    //赔偿金
                    Double pcj = null;
                    if ("".equals(j.getString("numberField_kvc66qyy")) || j.getString("numberField_kvc66qyy") == null) {
                        pcj = 0.0;
                    } else {
                        pcj = Double.valueOf(j.getString("numberField_kvc66qyy"));
                    }
                    //利润
                    Double wxlr = null;
                    if ("".equals(j.getString("wxlr")) || j.getString("wxlr") == null) {
                        wxlr = 0.0;
                    } else {
                        wxlr = Double.valueOf(j.getString("wxlr"));
                    }

                    //原总价
                    Double wxyzj = null;
                    if ("".equals(j.getString("wxyzj")) || j.getString("wxyzj") == null) {
                        wxyzj = 0.0;
                    } else {
                        wxyzj = Double.valueOf(j.getString("wxyzj"));
                    }

                    //节约金额
                    Double jyje = null;
                    if ("".equals(j.getString("numberField_kvc66qyx")) || j.getString("numberField_kvc66qyx") == null) {
                        jyje = 0.0;
                    } else {
                        jyje = Double.valueOf(j.getString("numberField_kvc66qyx"));
                    }

                    //运费
                    Double yunf = null;
                    if ("".equals(j.getString("yunf")) || j.getString("yunf") == null) {
                        yunf = 0.0;
                    } else {
                        yunf = Double.valueOf(j.getString("yunf"));
                    }

                    //获取品牌
                    wlgbWzycgdzc.setBrand(j.getString("textField_kp6hvugy"));

                    wlgbWzycgdzc.setZcqy(j.getString("syqy"));
                    //店铺名称
                    String dpmc = j.getString("dpmc");
                    //采购渠道
                    String cgqd = j.getString("cgqd");
                    //获取类别selectField_kv8v3u0y
                    String wplb = j.getString("wplb");
                    wlgbWzycgdzc.setFl(wplb);
                    //获取单位
                    wlgbWzycgdzc.setDw(j.getString("textField_kphyt148"));
                    //获取单价
                    wlgbWzycgdzc.setDj(j.getDouble("numberField_kphyt147"));
                    //获取数量
                    wlgbWzycgdzc.setCgnum(j.getDouble("numberField_kpasvn8t"));
                    //获取备用数量
                    wlgbWzycgdzc.setBysl(j.getInteger("bysl"));
                    wlgbWzycgdzc.setSfsc(0);
                    //获取损失数量
                    wlgbWzycgdzc.setWzsl(j.getInteger("wzsl"));
                    //总金额
                    Double dgzje = j.getDouble("dgzje");
                    //采购赔偿内容
                    String cgzbnx = j.getString("cgzbnx");
                    //获取备注
                    wlgbWzycgdzc.setBz(j.getString("textareaField_kphziztm"));
                    wlgbWzycgdzc.setId(IdConfig.uuId());
                    System.out.println(wlgbWzycgdzc.getId());
                    wlgbWzycgdzc.setLsh(DateFormatConfig.dfSjc());
                    wlgbWzycgdzc.setWzgsqy("一线");
                    if (jsonObject.getLong("fksj") != null) {
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy.MM.dd");
                        String sd = formatter.format(jsonObject.getLong("fksj"));
                        wlgbWzycgdzc.setCgtime(sd);
                    }

                    WlgbWzycgdzc wlgbWzycgdzc1 = wlgbWzycgdzcService.queryByBsMcAndSfScAndWpMc(wlgbWzycgdzc.getBsmc(), 0, wlgbWzycgdzc.getWpmc());

                    //同步到宜搭
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("bsmc", wlgbWzycgdzc.getBsmc());
                    map.put("wpmc", wlgbWzycgdzc.getWpmc());
                    map.put("lsh", wlgbWzycgdzc.getLsh());
                    map.put("city", wlgbWzycgdzc.getCity());
                    map.put("sl", wlgbWzycgdzc.getCgnum());
                    map.put("cgtime", jsonObject.getLong("fksj"));
                    map.put("dj", wlgbWzycgdzc.getDj());
                    map.put("dw", wlgbWzycgdzc.getDw());
                    map.put("zj", dgzje);
                    map.put("cgqd", cgqd);
                    map.put("dpmc", dpmc);
                    map.put("brand", wlgbWzycgdzc.getBrand());
                    map.put("wzgsqy", wlgbWzycgdzc.getWzgsqy());
                    map.put("lb", wlgbWzycgdzc.getFl());
                    map.put("sqr", sqr);
                    map.put("bxr", cgbxr);
                    map.put("wxlr", wxlr);
                    map.put("wxyzj", wxyzj);
                    map.put("pcje", pcj);
                    map.put("jyje", jyje);
                    map.put("cgyf", yunf);
                    map.put("cgzbnx", cgzbnx);
                    xzBd("012412221639786136545", map, "物资采购同步表单");
                    if (!"日耗品".equals(wplb) && !"后勤类".equals(wplb)) {
                        if (wlgbWzycgdzc.getCgnum() > 0) {
                            if (wlgbWzycgdzc1 != null) {
                                wlgbWzycgdzc1.setCgnum(wlgbWzycgdzc.getCgnum() + wlgbWzycgdzc1.getCgnum());
                                wlgbWzycgdzc1.setBysl(wlgbWzycgdzc.getBysl());
                                wlgbWzycgdzcService.updateById(wlgbWzycgdzc1);
                                JSONObject jsonObject1 = new JSONObject();
                                jsonObject1.put("lsh", wlgbWzycgdzc1.getLsh());

                                String slid = YdConfig.hqId("012412221639786136545", ydAppkey.getAppkey(), ydAppkey.getToken(), "FORM-6J8668A14XLUZZ4VX7VA8GL41IWQ1RAUEYYUK73", jsonObject1.toJSONString(), "1", "1");
                                JSONObject object1 = JSONObject.parseObject(slid);
                                JSONObject jsonObject2 = object1.getJSONObject("result");
                                if (jsonObject2 != null) {
                                    JSONArray jsonArray1 = jsonObject2.getJSONArray("data");
                                    if (jsonArray1.size() > 0) {
                                        String formInstId1 = (String) jsonArray1.get(0);
                                        Map<String, Object> map1 = new HashMap<>();
                                        map1.put("cgnum", wlgbWzycgdzc1.getCgnum());
                                        map1.put("fzruserid", wlgbWzycgdzc.getFzrid());
                                        //修改宜搭表单
                                        xgBdSl(map1, formInstId1, "012412221639786136545");
                                    }
                                }
                                //同步修改转移表单
                                String slid1 = YdConfig.hqId("012412221639786136545", ydAppkey.getAppkey(), ydAppkey.getToken(), "FORM-78766VC1IMZV9OLYZYXILAXFEZJT1PUW9VVWK0", jsonObject1.toJSONString(), "1", "1");
                                JSONObject object2 = JSONObject.parseObject(slid1);
                                JSONObject jsonObject3 = object2.getJSONObject("result");
                                if (jsonObject3 != null) {
                                    JSONArray jsonArray1 = jsonObject3.getJSONArray("data");
                                    if (jsonArray1.size() > 0) {
                                        String formInstId1 = (String) jsonArray1.get(0);
                                        Map<String, Object> map8 = new HashMap<>();
                                        map8.put("ppxh", wlgbWzycgdzc1.getBrand());
                                        map8.put("zybs", wlgbWzycgdzc1.getBsmc());
                                        map8.put("zjr", wlgbWzycgdzc1.getFzr());
                                        map8.put("sysl", wlgbWzycgdzc1.getCgnum());
                                        map8.put("dw", wlgbWzycgdzc1.getDw());
                                        map8.put("zypm", wlgbWzycgdzc1.getWpmc());
                                        map8.put("syqy", wlgbWzycgdzc1.getZcqy());
                                        map.put("dj", wlgbWzycgdzc1.getDj());
                                        //修改宜搭表单
                                        xgBdSl(map8, formInstId1, "012412221639786136545");
                                    }
                                }
                            } else {
                                HashMap<String, Object> map2 = new HashMap<>();
                                //同步到宜搭
                                map2.put("bsmc", wlgbWzycgdzc.getBsmc() != null && !"".equals(wlgbWzycgdzc.getBsmc()) ? wlgbWzycgdzc.getBsmc() : null);
                                map2.put("bsid", wlgbWzycgdzc.getBsmc() != null && !"".equals(wlgbWzycgdzc.getBsmc()) ? wlgbWzycgdzc.getBsmc() : null);
                                map2.put("wpmc", wlgbWzycgdzc.getWpmc() != null && !"".equals(wlgbWzycgdzc.getWpmc()) ? wlgbWzycgdzc.getWpmc() : null);
                                map2.put("wpmcs", wlgbWzycgdzc.getWpmc() != null && !"".equals(wlgbWzycgdzc.getWpmc()) ? wlgbWzycgdzc.getWpmc() : null);
                                map2.put("lsh", wlgbWzycgdzc.getLsh());
                                map2.put("fzr", wlgbWzycgdzc.getFzr());
                                map2.put("city", wlgbWzycgdzc.getCity() != null && !"".equals(wlgbWzycgdzc.getCity()) ? wlgbWzycgdzc.getCity() : null);
                                map2.put("csmc", wlgbWzycgdzc.getCity() != null && !"".equals(wlgbWzycgdzc.getCity()) ? wlgbWzycgdzc.getCity() : null);
                                map2.put("cgnum", (wlgbWzycgdzc.getCgnum() != null ? wlgbWzycgdzc.getCgnum() : 0) - (wlgbWzycgdzc.getWzsl() != null ? wlgbWzycgdzc.getWzsl() : 0));
                                map2.put("cgtime", wlgbWzycgdzc.getCgtime());
                                map2.put("dj", wlgbWzycgdzc.getDj());
                                map2.put("dw", wlgbWzycgdzc.getDw());
                                map2.put("brand", wlgbWzycgdzc.getBrand());
                                map2.put("bysl", wlgbWzycgdzc.getBysl());
                                map2.put("wzgsqy", wlgbWzycgdzc.getWzgsqy());
                                map2.put("fzruserid", wlgbWzycgdzc.getFzrid());
                                map2.put("bp", wlgbWzycgdzc.getBp());
                                map2.put("fl", wlgbWzycgdzc.getFl());
                                map2.put("zcqy", wlgbWzycgdzc.getZcqy());
                                map2.put("cszjl", wlgbWzycgdzc.getCszjl());
                                map2.put("bz", (wlgbWzycgdzc.getBz() != null && !"".equals(wlgbWzycgdzc.getBz()) ? wlgbWzycgdzc.getBz() : "") + "采购流水号：" + spbh);
//                                if ((wlgbWzycgdzc.getCgnum() != null ? wlgbWzycgdzc.getCgnum() : 0) - (wlgbWzycgdzc.getWzsl() != null ? wlgbWzycgdzc.getWzsl() : 0) > 0) {
                                xzBd("012412221639786136545", map2, "物资采购提交表单");
//                                }
                                //同步到宜搭
                                HashMap<String, Object> map9 = new HashMap<>();
                                map9.put("zybs", wlgbWzycgdzc.getBsmc() != null && !"".equals(wlgbWzycgdzc.getBsmc()) ? wlgbWzycgdzc.getBsmc() : null);
                                map9.put("zypm", wlgbWzycgdzc.getWpmc() != null && !"".equals(wlgbWzycgdzc.getWpmc()) ? wlgbWzycgdzc.getWpmc() : null);
                                map9.put("lsh", wlgbWzycgdzc.getLsh());
                                map9.put("syqy", wlgbWzycgdzc.getZcqy());
                                map9.put("ppxh", wlgbWzycgdzc.getBrand());
                                map9.put("sysl", (wlgbWzycgdzc.getCgnum() != null ? wlgbWzycgdzc.getCgnum() : 0) - (wlgbWzycgdzc.getWzsl() != null ? wlgbWzycgdzc.getWzsl() : 0));
                                map9.put("dw", wlgbWzycgdzc.getDw());
                                map9.put("zjr", wlgbWzycgdzc.getFzr());
                                map9.put("cgtime", wlgbWzycgdzc.getCgtime());
                                map9.put("dj", wlgbWzycgdzc.getDj());
                                if ((wlgbWzycgdzc.getCgnum() != null ? wlgbWzycgdzc.getCgnum() : 0) - (wlgbWzycgdzc.getWzsl() != null ? wlgbWzycgdzc.getWzsl() : 0) > 0) {
                                    xzBd("012412221639786136545", map9, "物资移交表单同步");
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 报销审批
     */
    public void bxSpYb(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        String businessId = processInstanceTopVo.getBusinessId();
                        String title = processInstanceTopVo.getTitle();
                        WlgbJdBxspjl wlgbJdBxspjl = new WlgbJdBxspjl();
                        wlgbJdBxspjl.setSlid(processInstanceId);
                        wlgbJdBxspjl.setSpjssj(processInstanceTopVo.getFinishTime());
                        wlgbJdBxspjl.setSqsj(processInstanceTopVo.getCreateTime());
                        wlgbJdBxspjl.setSpbh(businessId);
                        wlgbJdBxspjl.setSpbt(title);
                        wlgbJdBxspjl.setSfxft(0);
                        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                        if (!formComponentValues.isEmpty()) {
                            for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                String name = l.getName();
                                if ("报销申请人（收款人姓名）".equals(name)) {
                                    String extValue = l.getExtValue();
                                    JSONArray jsonArray = JSONArray.parseArray(extValue);
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdBxspjl.setSqrid(jsonObject.getString("emplId"));
                                            wlgbJdBxspjl.setSqr(jsonObject.getString("name"));
                                        }
                                    }
                                }
                                if ("部门".equals(name) || "申请人部门".equals(name)) {
                                    JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            String name1 = jsonObject.getString("name");
                                            String id = jsonObject.getString("id");
                                            wlgbJdBxspjl.setSqrbm(name1);
                                            wlgbJdBxspjl.setSqrbmid("-1".equals(id) ? "1" : id);
                                        }
                                    }
                                }
                                if ("收款账号".equals(name)) {
                                    wlgbJdBxspjl.setSkzh(l.getValue());
                                }
                                if ("收款开户行".equals(name)) {
                                    wlgbJdBxspjl.setSkkhh(l.getValue());
                                }
                                if ("费用使用方".equals(name)) {
                                    wlgbJdBxspjl.setFysyf(l.getValue());
                                }
                                if ("支付费用类别".equals(name)) {
                                    wlgbJdBxspjl.setZffylb(l.getValue());
                                }
                                if ("报销总金额（元）".equals(name) || "本次报销总金额".equals(name)) {
                                    wlgbJdBxspjl.setBxzje(Double.parseDouble(l.getValue()));
                                }
                                if ("费用摘要明细".equals(name)) {
                                    wlgbJdBxspjl.setFyzymx(l.getValue().substring(0, Math.min(399, l.getValue().length())));
                                }
                                if ("费用所属分摊".equals(name)) {
                                    wlgbJdBxspjl.setFyssft(l.getValue());
                                }
                                if ("是否所有门店都平摊".equals(name)) {
                                    if (!"null".equalsIgnoreCase(l.getValue())) {
                                        wlgbJdBxspjl.setSfsymdpt(l.getValue());
                                    }
                                }
                                if ("出纳转账时间".equals(name)) {
                                    wlgbJdBxspjl.setCnzzsj(l.getValue());
                                }
                                if ("出纳转账金额".equals(name) || "出纳实际转账金额".equals(name)) {
                                    wlgbJdBxspjl.setCnzzje(Double.parseDouble(l.getValue()));
                                }
                                if ("分摊设置".equals(name)) {
                                    Boolean b = true;
                                    try {
                                        b = ftSzCl1(l.getValue(), businessId, title, 0);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                    if (!b) {
                                        wlgbJdBxspjl.setSfxft(1);
                                    }
                                }
                                if ("不平摊的门店和金额".equals(name)) {
                                    bptMdCl(l.getValue(), businessId, 0);
                                }
                                if ("公司门店城市".equals(name)) {
                                    mdCsCl(l.getValue(), businessId, 0);
                                }
                                if ("财务入金蝶数据".equals(name)) {
                                    if (wlgbJdBxspjl.getSfxft() == 0) {
                                        try {
                                            cwRjdSj(l.getValue(), businessId, title, 0);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }
                            }
                        }

                        boolean save = bxCl(wlgbJdBxspjl.getSpbh(), wlgbJdBxspjl, 0);
                        if (save) {
                            wlgbJdBxspjl.setSflrjd(1);
                        }
                        WlgbJdBxspjl wbx1 = wlgbJdBxspjlService.queryBySpBhAndSfLrJd(wlgbJdBxspjl.getSpbh(), 0);
                        if (!isEmpty(wbx1)) {
                            wlgbJdBxspjl.setId(wbx1.getId());
                            wlgbJdBxspjlService.updateById(wlgbJdBxspjl);
                        } else {
                            wlgbJdBxspjlService.save(wlgbJdBxspjl);
                        }
                    }
                }
            }
        }
    }


    /**
     * 预支申请
     */
    public void yzSq(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        String businessId = processInstanceTopVo.getBusinessId();
                        String title = processInstanceTopVo.getTitle();
                        WlgbJdYzsqjl wlgbJdYzsqjl = new WlgbJdYzsqjl();
                        wlgbJdYzsqjl.setSpbh(businessId);
                        wlgbJdYzsqjl.setSpbt(title);
                        wlgbJdYzsqjl.setSlid(processInstanceId);
                        wlgbJdYzsqjl.setSpjssj(processInstanceTopVo.getFinishTime());
                        wlgbJdYzsqjl.setSqsj(processInstanceTopVo.getCreateTime());
                        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                        if (formComponentValues != null && !formComponentValues.isEmpty()) {
                            for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                String name = l.getName();
                                if ("预支类型".equals(name)) {
                                    wlgbJdYzsqjl.setYzlx(l.getValue());
                                }
                                if ("预支款使用方".equals(name)) {
                                    wlgbJdYzsqjl.setYzksyf(l.getValue());
                                }
                                if ("支付费用类别".equals(name)) {
                                    wlgbJdYzsqjl.setZffylb(l.getValue());
                                }
                                if ("预支款用途（原因）".equals(name)) {
                                    wlgbJdYzsqjl.setYzkyt(l.getValue());
                                }
                                if ("预计抵预支时间（不得超过一个月）".equals(name)) {
                                    wlgbJdYzsqjl.setYjdyzsj(new Date(l.getValue().replace("-", "/")));
                                }
                                if ("本人累积所欠预支款".equals(name)) {
                                    wlgbJdYzsqjl.setBrljqk(Double.parseDouble(l.getValue()));
                                }
                                if ("本次预支金额（元）".equals(name)) {
                                    wlgbJdYzsqjl.setBcyzje(Double.parseDouble(l.getValue()));
                                }
                                if ("预支申请人（收款人）".equals(name)) {
                                    String extValue = l.getExtValue();
                                    JSONArray jsonArray = JSONArray.parseArray(extValue);
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdYzsqjl.setSqrid(jsonObject.getString("emplId"));
                                            wlgbJdYzsqjl.setSqr(jsonObject.getString("name"));
                                        }
                                    }
                                }
                                if ("部门".equals(name) || "申请人部门".equals(name)) {
                                    JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            String name1 = jsonObject.getString("name");
                                            String id = jsonObject.getString("id");
                                            wlgbJdYzsqjl.setSqrbm(name1);
                                            wlgbJdYzsqjl.setSqrbmid("-1".equals(id) ? "1" : id);
                                        }
                                    }
                                }
                                if ("收款账号".equals(name)) {
                                    wlgbJdYzsqjl.setSkzh(l.getValue());
                                }
                                if ("开户行".equals(name)) {
                                    wlgbJdYzsqjl.setKhh(l.getValue());
                                }
                                if ("财务入金蝶数据".equals(name)) {
                                    cwRjdSj(l.getValue(), businessId, title, 1);
                                }
                                if ("出纳转账时间".equals(name)) {
                                    wlgbJdYzsqjl.setCnzzsj(new Date(l.getValue().replace("-", "/")));
                                }
                                if ("出纳实际转账金额".equals(name)) {
                                    wlgbJdYzsqjl.setCnsjzzje(Double.parseDouble(l.getValue()));
                                }
                            }
                        }
                        //
                        boolean save = yzCl(wlgbJdYzsqjl.getSpbh(), wlgbJdYzsqjl, 0);
                        if (save) {
                            wlgbJdYzsqjl.setSflrjd(1);
                        }
                        //不允许数据出现重复的
                        WlgbJdYzsqjl wyz1 = wlgbJdYzsqjlService.queryBySpBhAndSfLrJd(wlgbJdYzsqjl.getSpbh(), 0);
                        if (!isEmpty(wyz1)) {
                            wlgbJdYzsqjl.setId(wyz1.getId());
                            wlgbJdYzsqjlService.updateById(wlgbJdYzsqjl);
                        } else {
                            wlgbJdYzsqjlService.save(wlgbJdYzsqjl);
                        }
                    }
                }
            }
        }
    }


    /**
     * 费用付款申请
     */
    public void fyFkSq(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        String businessId = processInstanceTopVo.getBusinessId();
                        String title = processInstanceTopVo.getTitle();
                        WlgbJdFyfksqjl wlgbJdFyfksqjl = new WlgbJdFyfksqjl();
                        wlgbJdFyfksqjl.setSpbh(businessId);
                        wlgbJdFyfksqjl.setSpbt(title);
                        wlgbJdFyfksqjl.setSlid(processInstanceId);
                        wlgbJdFyfksqjl.setSpjssj(processInstanceTopVo.getFinishTime());
                        wlgbJdFyfksqjl.setSqsj(processInstanceTopVo.getCreateTime());
                        wlgbJdFyfksqjl.setSfxft(0);
                        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                        if (formComponentValues != null && formComponentValues.size() > 0) {
                            for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                String name = l.getName();
                                if ("收缴机构（商家名字）".equals(name)) {
                                    wlgbJdFyfksqjl.setSkjg(l.getValue());
                                }
                                if ("户名（第三方收款账户名称）".equals(name)) {
                                    wlgbJdFyfksqjl.setHm(l.getValue());
                                }
                                if ("收款账号".equals(name)) {
                                    wlgbJdFyfksqjl.setSkzh(l.getValue());
                                }
                                if ("收款开户行".equals(name)) {
                                    wlgbJdFyfksqjl.setSkkhh(l.getValue());
                                }
                                if ("费用使用方".equals(name)) {
                                    wlgbJdFyfksqjl.setFysyf(l.getValue());
                                }
                                if ("支付费用类别".equals(name)) {
                                    wlgbJdFyfksqjl.setZffylx(l.getValue());
                                }
                                if ("费用摘要明细".equals(name)) {
                                    wlgbJdFyfksqjl.setFyzymx(l.getValue().substring(0, Math.min(399, l.getValue().length())));
                                }
                                if ("费用所属分摊".equals(name)) {
                                    wlgbJdFyfksqjl.setFyssft(l.getValue());
                                }
                                if ("是否所有门店都平摊".equals(name)) {
                                    if (!"null".equalsIgnoreCase(l.getValue())) {
                                        wlgbJdFyfksqjl.setSfsymdft(l.getValue());
                                    }
                                }
                                if ("付款类别".equals(name)) {
                                    wlgbJdFyfksqjl.setFklb(l.getValue());
                                }
                                if ("支付截止日期".equals(name)) {
                                    wlgbJdFyfksqjl.setZfjzrq(new Date(l.getValue().replace("-", "/")));
                                }
                                if ("合计金额（元）".equals(name)) {
                                    wlgbJdFyfksqjl.setHjje(Double.parseDouble(l.getValue()));
                                }
                                if ("备注".equals(name)) {
                                    wlgbJdFyfksqjl.setBz(l.getValue());
                                }
                                if ("申请人".equals(name)) {
                                    String extValue = l.getExtValue();
                                    JSONArray jsonArray = JSONArray.parseArray(extValue);
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdFyfksqjl.setSqrid(jsonObject.getString("emplId"));
                                            wlgbJdFyfksqjl.setSqr(jsonObject.getString("name"));
                                        }
                                    }
                                }
                                if ("部门".equals(name) || "申请人部门".equals(name)) {
                                    JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            String name1 = jsonObject.getString("name");
                                            String id = jsonObject.getString("id");
                                            wlgbJdFyfksqjl.setSqrbm(name1);
                                            wlgbJdFyfksqjl.setSqrbmid("-1".equals(id) ? "1" : id);
                                        }
                                    }
                                }
                                if ("分摊设置".equals(l.getName())) {
                                    Boolean b = true;
                                    try {
                                        b = ftSzCl1(l.getValue(), businessId, title, 2);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                    if (!b) {
                                        wlgbJdFyfksqjl.setSfxft(1);
                                    }
                                }
                                if ("不平摊的门店和金额".equals(l.getName())) {
                                    bptMdCl(l.getValue(), businessId, 2);
                                }
                                if ("公司门店城市".equals(l.getName())) {
                                    mdCsCl(l.getValue(), businessId, 2);
                                }
                                if ("财务入金蝶数据".equals(name)) {
                                    if (wlgbJdFyfksqjl.getSfxft() == 0) {
                                        try {
                                            cwRjdSj(l.getValue(), businessId, title, 2);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }
                                if ("出纳转账时间".equals(name)) {
                                    wlgbJdFyfksqjl.setCnzzsj(l.getValue());
                                }
                                if ("出纳实际转账金额".equals(name)) {
                                    wlgbJdFyfksqjl.setCnzzje(Double.parseDouble(l.getValue()));
                                }
                            }
                        }
                        //保存到数据库 并上传到金蝶
                        boolean save = fyFkCl(wlgbJdFyfksqjl.getSpbh(), wlgbJdFyfksqjl, 0);
                        if (save) {
                            wlgbJdFyfksqjl.setSflrjd(1);
                        }
                        WlgbJdFyfksqjl wfyks1 = wlgbJdFyfksqjlService.queryBySpBhAndSfLrJd(wlgbJdFyfksqjl.getSpbh(), 0);
                        if (!isEmpty(wfyks1)) {
                            wlgbJdFyfksqjl.setId(wfyks1.getId());
                            wlgbJdFyfksqjlService.updateById(wlgbJdFyfksqjl);
                        } else {
                            wlgbJdFyfksqjlService.save(wlgbJdFyfksqjl);
                        }
                    }
                }
            }
        }
    }

    public void fyFkSq2(JSONObject callBackContent) throws Exception {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (!processInstanceId.isEmpty()) {
            GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult getxp = DingSpXqConfig.getSpXq(dingkey, processInstanceId);
        }
    }


    /**
     * 抵预支申请
     */
    public void dyzSq(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (!processInstanceId.isEmpty()) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        String businessId = processInstanceTopVo.getBusinessId();
                        String title = processInstanceTopVo.getTitle();
                        WlgbJdDyzsqjl wlgbJdDyzsqjl = new WlgbJdDyzsqjl();
                        wlgbJdDyzsqjl.setSpbh(businessId);
                        wlgbJdDyzsqjl.setSpbt(title);
                        wlgbJdDyzsqjl.setSlid(processInstanceId);
                        wlgbJdDyzsqjl.setSpjssj(processInstanceTopVo.getFinishTime());
                        wlgbJdDyzsqjl.setSqsj(processInstanceTopVo.getCreateTime());
                        wlgbJdDyzsqjl.setSfxft(0);
                        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                        if (formComponentValues != null && formComponentValues.size() > 0) {
                            for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                String name = l.getName();
                                if ("费用使用方".equals(name)) {
                                    wlgbJdDyzsqjl.setFysyf(l.getValue());
                                }
                                if ("支付费用类别".equals(name)) {
                                    wlgbJdDyzsqjl.setZffylb(l.getValue());
                                }
                                if ("费用摘要明细".equals(name)) {
                                    wlgbJdDyzsqjl.setFyzymx(l.getValue().substring(0, Math.min(399, l.getValue().length())));
                                }
                                if ("费用所属分摊".equals(name)) {
                                    wlgbJdDyzsqjl.setFyssft(l.getValue());
                                }
                                if ("申请人".equals(name)) {
                                    String extValue = l.getExtValue();
                                    JSONArray jsonArray = JSONArray.parseArray(extValue);
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdDyzsqjl.setSqrid(jsonObject.getString("emplId"));
                                            wlgbJdDyzsqjl.setSqr(jsonObject.getString("name"));
                                        }
                                    }
                                }
                                if ("部门".equals(name) || "申请人部门".equals(name)) {
                                    JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            String name1 = jsonObject.getString("name");
                                            String id = jsonObject.getString("id");
                                            wlgbJdDyzsqjl.setSqrbm(name1);
                                            wlgbJdDyzsqjl.setSqrbmid("-1".equals(id) ? "1" : id);
                                        }
                                    }
                                }
                                if ("是否所有门店都平摊".equals(name)) {
                                    if (!"null".equalsIgnoreCase(l.getValue())) {
                                        wlgbJdDyzsqjl.setSfsymdft(l.getValue());
                                    }
                                }
                                if ("是否是平台刷单".equals(name)) {
                                    if (!"null".equalsIgnoreCase(l.getValue())) {
                                        wlgbJdDyzsqjl.setSfsptsd(l.getValue());
                                    }
                                }
                                if ("抵预支合计金额（元）".equals(name)) {
                                    wlgbJdDyzsqjl.setDyzje(Double.parseDouble(l.getValue()));
                                }
                                if ("备注".equals(name)) {
                                    wlgbJdDyzsqjl.setBz(l.getValue());
                                }
                                if ("预计回票时间（下款后的一个月内）".equals(name)) {
                                    wlgbJdDyzsqjl.setYjhpsj(new Date(l.getValue().replace("-", "/")));
                                }
                                if ("出纳转账时间".equals(name)) {
                                    if (!"null".equalsIgnoreCase(l.getValue())) {
                                        wlgbJdDyzsqjl.setCnzzsj(l.getValue());
                                    }
                                }
                                if ("出纳实际转账金额".equals(name)) {
                                    if (!"null".equalsIgnoreCase(l.getValue())) {
                                        wlgbJdDyzsqjl.setCnzzje(Double.parseDouble(l.getValue()));
                                    }
                                }
                                if ("分摊设置".equals(l.getName())) {
                                    Boolean b = true;
                                    try {
                                        b = ftSzCl1(l.getValue(), businessId, title, 3);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                    if (!b) {
                                        wlgbJdDyzsqjl.setSfxft(1);
                                    }
                                }
                                if ("不平摊的门店和金额".equals(l.getName())) {
                                    bptMdCl(l.getValue(), businessId, 3);
                                }
                                if ("公司门店城市".equals(l.getName())) {
                                    mdCsCl(l.getValue(), businessId, 3);
                                }
                                if ("财务入金蝶数据".equals(name)) {
                                    if (wlgbJdDyzsqjl.getSfxft() == 0) {
                                        try {
                                            cwRjdSj(l.getValue(), businessId, title, 3);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }
                            }
                        }
                        boolean save = dyzCl(wlgbJdDyzsqjl.getSpbh(), wlgbJdDyzsqjl, 0);
                        if (save) {
                            wlgbJdDyzsqjl.setSflrjd(1);
                        }
                        WlgbJdDyzsqjl wdyz1 = wlgbJdDyzsqjlService.queryBySpBhAndSfLrJd(wlgbJdDyzsqjl.getSpbh(), 0);
                        if (!isEmpty(wdyz1)) {
                            wlgbJdDyzsqjl.setId(wdyz1.getId());
                            wlgbJdDyzsqjlService.updateById(wlgbJdDyzsqjl);
                        } else {
                            wlgbJdDyzsqjlService.save(wlgbJdDyzsqjl);
                        }
                    }
                }
            }
        }
    }


    /**
     * 威廉币提现
     */
    public void wlbTxSq(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        String businessId = processInstanceTopVo.getBusinessId();
                        String title = processInstanceTopVo.getTitle();
                        WlgbJdWlbtxjl wlgbJdWlbtxjl = new WlgbJdWlbtxjl();
                        wlgbJdWlbtxjl.setSpjssj(processInstanceTopVo.getFinishTime());
                        wlgbJdWlbtxjl.setSqsj(processInstanceTopVo.getCreateTime());
                        wlgbJdWlbtxjl.setSlid(processInstanceId);
                        wlgbJdWlbtxjl.setSpbh(businessId);
                        wlgbJdWlbtxjl.setSpbt(title);
                        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                        if (formComponentValues != null && formComponentValues.size() > 0) {
                            for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                String name = l.getName();
                                if ("提现项目".equals(name)) {
                                    wlgbJdWlbtxjl.setTxxm(l.getValue());
                                }
                                if ("申请提现人".equals(name)) {
                                    String extValue = l.getExtValue();
                                    JSONArray jsonArray = JSONArray.parseArray(extValue);
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdWlbtxjl.setSqr(jsonObject.getString("name"));
                                            wlgbJdWlbtxjl.setSqrid(jsonObject.getString("emplId"));
                                        }
                                    }
                                }
                                if ("所在部门".equals(name)) {
                                    JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdWlbtxjl.setSqrbm(jsonObject.getString("name"));
                                            wlgbJdWlbtxjl.setSqrbmid(jsonObject.getString("id"));
                                        }
                                    }
                                }
                                if ("提现金额".equals(name)) {
                                    wlgbJdWlbtxjl.setTxje(Double.parseDouble(l.getValue()));
                                }
                                if ("户名".equals(name)) {
                                    wlgbJdWlbtxjl.setHm(l.getValue());
                                }
                                if ("收款账号".equals(name)) {
                                    wlgbJdWlbtxjl.setSkzh(l.getValue());
                                }
                                if ("收款开户行".equals(name)) {
                                    wlgbJdWlbtxjl.setSkkhh(l.getValue());
                                }
                                if ("出纳转账时间".equals(name)) {
                                    wlgbJdWlbtxjl.setCnzzsj(l.getValue());
                                }
                                if ("出纳实际转账金额".equals(name)) {
                                    wlgbJdWlbtxjl.setCnsjzzje(Double.parseDouble(l.getValue()));
                                }
                                if ("财务入金蝶数据".equals(name)) {
                                    cwRjdSj(l.getValue(), businessId, title, 6);
                                }
                            }
                        }
                        boolean save = wlbTxCl(wlgbJdWlbtxjl.getSpbh(), wlgbJdWlbtxjl, 0);
                        if (save) {
                            wlgbJdWlbtxjl.setSflrjd(1);
                        }
                        WlgbJdWlbtxjl wwlbtx1 = wlgbJdWlbtxjlService.queryBySpBhAndSfLrJd(wlgbJdWlbtxjl.getSpbh(), 0);
                        if (!isEmpty(wwlbtx1)) {
                            wlgbJdWlbtxjl.setId(wwlbtx1.getId());
                            wlgbJdWlbtxjlService.updateById(wlgbJdWlbtxjl);
                        } else {
                            wlgbJdWlbtxjlService.save(wlgbJdWlbtxjl);
                        }
                    }
                }
            }
        }
    }


    /**
     * 宿舍退押金申请
     */
    public void ssTyjSq(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        String businessId = processInstanceTopVo.getBusinessId();
                        String title = processInstanceTopVo.getTitle();
                        WlgbJdSstyjsqjl wlgbJdSstyjsqjl = new WlgbJdSstyjsqjl();
                        wlgbJdSstyjsqjl.setSpjssj(processInstanceTopVo.getFinishTime());
                        wlgbJdSstyjsqjl.setSqsj(processInstanceTopVo.getCreateTime());
                        wlgbJdSstyjsqjl.setSlid(processInstanceId);
                        wlgbJdSstyjsqjl.setSpbh(businessId);
                        wlgbJdSstyjsqjl.setSpbt(title);
                        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                        if (formComponentValues != null && formComponentValues.size() > 0) {
                            for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                String name = l.getName();
                                if ("申请人".equals(name)) {
                                    String extValue = l.getExtValue();
                                    JSONArray jsonArray = JSONArray.parseArray(extValue);
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdSstyjsqjl.setSqr(jsonObject.getString("name"));
                                            wlgbJdSstyjsqjl.setSqrid(jsonObject.getString("emplId"));
                                        }
                                    }
                                }
                                if ("申请部门".equals(name)) {
                                    JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdSstyjsqjl.setSqrbm(jsonObject.getString("name"));
                                            wlgbJdSstyjsqjl.setSqrbmid(jsonObject.getString("id"));
                                        }
                                    }
                                }
                                if ("收缴机构（商家名字）".equals(name)) {
                                    wlgbJdSstyjsqjl.setSkjg(l.getValue());
                                }
                                if ("户名（第三方收款账户名称）".equals(name)) {
                                    wlgbJdSstyjsqjl.setHm(l.getValue());
                                }
                                if ("收款账号".equals(name)) {
                                    wlgbJdSstyjsqjl.setSkzh(l.getValue());
                                }
                                if ("收款开户行".equals(name)) {
                                    wlgbJdSstyjsqjl.setSkkhm(l.getValue());
                                }
                                if ("费用使用方".equals(name)) {
                                    wlgbJdSstyjsqjl.setFysyf(l.getValue());
                                }
                                if ("支付费用类别".equals(name)) {
                                    wlgbJdSstyjsqjl.setZffylb(l.getValue());
                                }
                                if ("费用摘要明细".equals(name)) {
                                    wlgbJdSstyjsqjl.setFyzymx(l.getValue().substring(0, Math.min(399, l.getValue().length())));
                                }
                                if ("费用所属分摊".equals(name)) {
                                    wlgbJdSstyjsqjl.setFyssft(l.getValue());
                                }
                                if ("付款类别".equals(name)) {
                                    wlgbJdSstyjsqjl.setFklb(l.getValue());
                                }
                                if ("支付截止日期".equals(name)) {
                                    wlgbJdSstyjsqjl.setZfjzrq(l.getValue());
                                }
                                if ("合计金额（元）".equals(name)) {
                                    wlgbJdSstyjsqjl.setHjje(Double.parseDouble(l.getValue()));
                                }
                                if ("备注".equals(name)) {
                                    wlgbJdSstyjsqjl.setBz(l.getValue());
                                }
                                if ("出纳转账时间".equals(name)) {
                                    wlgbJdSstyjsqjl.setCnzzsj(l.getValue());
                                }
                                if ("出纳实际转账金额".equals(name)) {
                                    wlgbJdSstyjsqjl.setCnsjzzje(Double.parseDouble(l.getValue()));
                                }
                                if ("财务入金蝶数据".equals(name)) {
                                    cwRjdSj(l.getValue(), businessId, title, 7);
                                }
                            }
                        }
                        boolean save = ssTyjCl(wlgbJdSstyjsqjl.getSpbh(), wlgbJdSstyjsqjl, 0);
                        if (save) {
                            wlgbJdSstyjsqjl.setSflrjd(1);
                        }
                        WlgbJdSstyjsqjl wsstyj1 = wlgbJdSstyjsqjlService.queryBySpBhAndSfLrJd(wlgbJdSstyjsqjl.getSpbh(), 0);
                        if (!isEmpty(wsstyj1)) {
                            wlgbJdSstyjsqjl.setId(wsstyj1.getId());
                            wlgbJdSstyjsqjlService.updateById(wlgbJdSstyjsqjl);
                        } else {
                            wlgbJdSstyjsqjlService.save(wlgbJdSstyjsqjl);
                        }

                    }
                }
            }
        }
    }


    /**
     * 借支审批
     */
    public void jzSp(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        String businessId = processInstanceTopVo.getBusinessId();
                        String title = processInstanceTopVo.getTitle();
                        WlgbJdJzspjl wlgbJdJzspjl = new WlgbJdJzspjl();
                        wlgbJdJzspjl.setSpjssj(processInstanceTopVo.getFinishTime());
                        wlgbJdJzspjl.setSqsj(processInstanceTopVo.getCreateTime());
                        wlgbJdJzspjl.setSlid(processInstanceId);
                        wlgbJdJzspjl.setSpbh(businessId);
                        wlgbJdJzspjl.setSpbt(title);
                        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                        if (formComponentValues != null && formComponentValues.size() > 0) {
                            for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                String name = l.getName();
                                if ("借支申请人（收款人）".equals(name)) {
                                    String extValue = l.getExtValue();
                                    JSONArray jsonArray = JSONArray.parseArray(extValue);
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdJzspjl.setSqr(jsonObject.getString("name"));
                                            wlgbJdJzspjl.setSqrid(jsonObject.getString("emplId"));
                                        }
                                    }
                                }
                                if ("所在部门".equals(name)) {
                                    JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdJzspjl.setSqrbm(jsonObject.getString("name"));
                                            wlgbJdJzspjl.setSqrbmid(jsonObject.getString("id"));
                                        }
                                    }
                                }
                                if ("借支使用方".equals(name)) {
                                    wlgbJdJzspjl.setJzsyf(l.getValue());
                                }
                                if ("费用类别".equals(name)) {
                                    wlgbJdJzspjl.setFylb(l.getValue());
                                }
                                if ("借支用途（原因）".equals(name)) {
                                    wlgbJdJzspjl.setJzyt(l.getValue());
                                }
                                if ("预计还款时间".equals(name)) {
                                    wlgbJdJzspjl.setYjhkrq(l.getValue());
                                }
                                if ("本人累计借支".equals(name)) {
                                    wlgbJdJzspjl.setBrljjz(Double.parseDouble(l.getValue()));
                                }
                                if ("本次借支金额".equals(name)) {
                                    wlgbJdJzspjl.setBcjzje(Double.parseDouble(l.getValue()));
                                }
                                if ("收款帐号".equals(name)) {
                                    wlgbJdJzspjl.setSkzh(l.getValue());
                                }
                                if ("开户行".equals(name)) {
                                    wlgbJdJzspjl.setKhh(l.getValue());
                                }
                                if ("出纳转账时间".equals(name)) {
                                    wlgbJdJzspjl.setCnzzsj(l.getValue());
                                }
                                if ("出纳实际转账金额".equals(name)) {
                                    wlgbJdJzspjl.setCnsjzzje(Double.parseDouble(l.getValue()));
                                }
                                if ("财务入金蝶数据".equals(name)) {
                                    cwRjdSj(l.getValue(), businessId, title, 5);
                                }
                            }
                        }
                        boolean save = jzCl(wlgbJdJzspjl.getSpbh(), wlgbJdJzspjl, 0);
                        if (save) {
                            wlgbJdJzspjl.setSflrjd(1);
                        }
                        WlgbJdJzspjl wjzsp1 = wlgbJdJzspjlService.queryBySpBhAndSfLrJd(wlgbJdJzspjl.getSpbh(), 0);
                        if (!isEmpty(wjzsp1)) {
                            wlgbJdJzspjl.setId(wjzsp1.getId());
                            wlgbJdJzspjlService.updateById(wlgbJdJzspjl);
                        } else {
                            wlgbJdJzspjlService.save(wlgbJdJzspjl);
                        }

                    }
                }
            }
        }
    }


    /**
     * 平台刷单款报销
     */
    public void ptSdkBxSq(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        String businessId = processInstanceTopVo.getBusinessId();
                        String title = processInstanceTopVo.getTitle();
                        WlgbJdPtsdkbxsqjl wlgbJdPtsdkbxsqjl = new WlgbJdPtsdkbxsqjl();
                        wlgbJdPtsdkbxsqjl.setSpjssj(processInstanceTopVo.getFinishTime());
                        wlgbJdPtsdkbxsqjl.setSqsj(processInstanceTopVo.getCreateTime());
                        wlgbJdPtsdkbxsqjl.setSlid(processInstanceId);
                        wlgbJdPtsdkbxsqjl.setSpbh(businessId);
                        wlgbJdPtsdkbxsqjl.setSpbt(title);
                        wlgbJdPtsdkbxsqjl.setSfxft(0);
                        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                        if (formComponentValues != null && formComponentValues.size() > 0) {
                            for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                String name = l.getName();
                                if ("报销申请人（收款人姓名）".equals(name)) {
                                    String extValue = l.getExtValue();
                                    JSONArray jsonArray = JSONArray.parseArray(extValue);
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdPtsdkbxsqjl.setSqr(jsonObject.getString("name"));
                                            wlgbJdPtsdkbxsqjl.setSqrid(jsonObject.getString("emplId"));
                                        }
                                    }
                                }
                                if ("部门".equals(name)) {
                                    JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                    if (jsonArray != null && jsonArray.size() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        if (jsonObject != null && jsonObject.size() > 0) {
                                            wlgbJdPtsdkbxsqjl.setSqrbm(jsonObject.getString("name"));
                                            wlgbJdPtsdkbxsqjl.setSqrbmid(jsonObject.getString("id"));
                                        }
                                    }
                                }
                                if ("收款账号".equals(name)) {
                                    wlgbJdPtsdkbxsqjl.setSkzh(l.getValue());
                                }
                                if ("收款开户行".equals(name)) {
                                    wlgbJdPtsdkbxsqjl.setSkkhh(l.getValue());
                                }
                                if ("费用使用方".equals(name)) {
                                    wlgbJdPtsdkbxsqjl.setFysyf(l.getValue());
                                }
                                if ("支付费用类别".equals(name)) {
                                    wlgbJdPtsdkbxsqjl.setZffylb(l.getValue());
                                }
                                if ("支付具体分类".equals(name)) {
                                    wlgbJdPtsdkbxsqjl.setZfjtfl(l.getValue());
                                }
                                if ("费用摘要明细".equals(name)) {
                                    wlgbJdPtsdkbxsqjl.setFyzymx(l.getValue().substring(0, Math.min(399, l.getValue().length())));
                                }
                                if ("费用所属分摊".equals(name)) {
                                    wlgbJdPtsdkbxsqjl.setFyssft(l.getValue());
                                }
                                if ("是否所有门店都平摊".equals(name)) {
                                    if (!"null".equalsIgnoreCase(l.getValue())) {
                                        wlgbJdPtsdkbxsqjl.setSfsymdpt(l.getValue());
                                    }
                                }
                                if ("本次报销总金额".equals(name)) {
                                    wlgbJdPtsdkbxsqjl.setBcbxzje(Double.parseDouble(l.getValue()));
                                }
                                if ("备注".equals(name)) {
                                    wlgbJdPtsdkbxsqjl.setBz(l.getValue());
                                }
                                if ("出纳转账时间".equals(name)) {
                                    wlgbJdPtsdkbxsqjl.setCnzzsj(l.getValue());
                                }
                                if ("出纳实际转账金额".equals(name)) {
                                    wlgbJdPtsdkbxsqjl.setCnzzje(Double.parseDouble(l.getValue()));
                                }
                                if ("分摊设置".equals(l.getName())) {
                                    Boolean b = true;
                                    try {
                                        b = ftSzCl1(l.getValue(), businessId, title, 9);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                    if (!b) {
                                        wlgbJdPtsdkbxsqjl.setSfxft(1);
                                    }
                                }
                                if ("不平摊的门店和金额".equals(l.getName())) {
                                    bptMdCl(l.getValue(), businessId, 9);
                                }
                                if ("公司门店城市".equals(l.getName())) {
                                    mdCsCl(l.getValue(), businessId, 9);
                                }
                                if ("财务入金蝶数据".equals(name)) {
                                    if (wlgbJdPtsdkbxsqjl.getSfxft() == 0) {
                                        try {
                                            cwRjdSj(l.getValue(), businessId, title, 9);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }
                            }
                        }
                        boolean save = ptSdkBxCl(wlgbJdPtsdkbxsqjl.getSpbh(), wlgbJdPtsdkbxsqjl, 0);
                        if (save) {
                            wlgbJdPtsdkbxsqjl.setSflrjd(1);
                        }
                        WlgbJdPtsdkbxsqjl wptsdk1 = wlgbJdPtsdkbxsqjlService.queryBySpBhAndSfLrJd(wlgbJdPtsdkbxsqjl.getSpbh(), 0);
                        if (!isEmpty(wptsdk1)) {
                            wlgbJdPtsdkbxsqjl.setId(wptsdk1.getId());
                            wlgbJdPtsdkbxsqjlService.updateById(wlgbJdPtsdkbxsqjl);
                        } else {
                            wlgbJdPtsdkbxsqjlService.save(wlgbJdPtsdkbxsqjl);
                        }
                    }
                }
            }
        }
    }


    /**
     * 乐诚项目报销
     */
    public void lcXmBxSq(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        Date createTime = processInstanceTopVo.getCreateTime();
                        if (createTime.getTime() >= new Date("2022/07/30").getTime()) {
                            String businessId = processInstanceTopVo.getBusinessId();
                            String title = processInstanceTopVo.getTitle();
                            WlgbJdLcxmbxsqjl wlgbJdLcxmbxsqjl = new WlgbJdLcxmbxsqjl();
                            wlgbJdLcxmbxsqjl.setSpbh(businessId);
                            wlgbJdLcxmbxsqjl.setSpbt(title);
                            wlgbJdLcxmbxsqjl.setSlid(processInstanceId);
                            wlgbJdLcxmbxsqjl.setSpjssj(processInstanceTopVo.getFinishTime());
                            wlgbJdLcxmbxsqjl.setSqsj(createTime);
                            List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                            if (formComponentValues != null && formComponentValues.size() > 0) {
                                for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                    String name = l.getName();
                                    if ("申请人".equals(name)) {
                                        String extValue = l.getExtValue();
                                        JSONArray jsonArray = JSONArray.parseArray(extValue);
                                        if (jsonArray != null && jsonArray.size() > 0) {
                                            JSONObject jsonObject = jsonArray.getJSONObject(0);
                                            if (jsonObject != null && jsonObject.size() > 0) {
                                                wlgbJdLcxmbxsqjl.setSqr(jsonObject.getString("name"));
                                                wlgbJdLcxmbxsqjl.setSqrid(jsonObject.getString("emplId"));
                                            }
                                        }
                                    }
                                    if ("部门".equals(name)) {
                                        JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                        if (jsonArray != null && jsonArray.size() > 0) {
                                            JSONObject jsonObject = jsonArray.getJSONObject(0);
                                            if (jsonObject != null && jsonObject.size() > 0) {
                                                wlgbJdLcxmbxsqjl.setSqrbm(jsonObject.getString("name"));
                                                wlgbJdLcxmbxsqjl.setSqrbmid(jsonObject.getString("id"));
                                            }
                                        }
                                    }
                                    if ("收款账户名".equals(name)) {
                                        wlgbJdLcxmbxsqjl.setSkzhh(l.getValue());
                                    }
                                    if ("收款账号".equals(name)) {
                                        wlgbJdLcxmbxsqjl.setSkzh(l.getValue());
                                    }
                                    if ("收款开户行".equals(name)) {
                                        wlgbJdLcxmbxsqjl.setSkkhh(l.getValue());
                                    }
                                    if ("费用发生项目名称".equals(name)) {
                                        wlgbJdLcxmbxsqjl.setFyfsxmmc(l.getValue());
                                    }
                                    if ("门店类型".equals(name)) {
                                        wlgbJdLcxmbxsqjl.setMdlx(l.getValue());
                                    }
                                    if ("费用明细".equals(name)) {
                                        try {
                                            JSONObject jsonObject = JSONObject.parseObject(l.getExtValue());
                                            JSONArray jsonArray = jsonObject.getJSONArray("statValue");
                                            if (jsonArray.size() > 0) {
                                                JSONObject jsonObject1 = jsonArray.getJSONObject(0);
                                                wlgbJdLcxmbxsqjl.setBxzje(jsonObject1.getDouble("num"));
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    if ("财务入金蝶数据".equals(name)) {
                                        cwRjdSj(l.getValue(), businessId, title, 13);
                                    }
                                    if ("出纳转账时间".equals(name)) {
                                        wlgbJdLcxmbxsqjl.setCnzzsj(l.getValue());
                                    }
                                    if ("出纳实际转账金额".equals(name)) {
                                        wlgbJdLcxmbxsqjl.setCnzzje(Double.parseDouble(l.getValue()));
                                    }
                                }
                            }
                            boolean save = lcXmBxCl(wlgbJdLcxmbxsqjl.getSpbh(), wlgbJdLcxmbxsqjl, 0);
                            if (save) {
                                wlgbJdLcxmbxsqjl.setSflrjd(1);
                            }
                            WlgbJdLcxmbxsqjl wlcxm1 = wlgbJdLcxmbxsqjlService.queryBySpBhAndSfLrJd(wlgbJdLcxmbxsqjl.getSpbh(), 0);
                            if (!isEmpty(wlcxm1)) {
                                wlgbJdLcxmbxsqjl.setId(wlcxm1.getId());
                                wlgbJdLcxmbxsqjlService.updateById(wlgbJdLcxmbxsqjl);
                            } else {
                                wlgbJdLcxmbxsqjlService.save(wlgbJdLcxmbxsqjl);
                            }
                        }
                    }
                }
            }
        }
    }


    /**
     * 乐诚项目采购代付款
     */
    public void lcXmCgDfkSq(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        Date createTime = processInstanceTopVo.getCreateTime();
                        if (createTime.getTime() >= new Date("2022/07/30").getTime()) {
                            String businessId = processInstanceTopVo.getBusinessId();
                            String title = processInstanceTopVo.getTitle();
                            WlgbJdLcxmcgdfksqjl wlgbJdLcxmcgdfksqjl = new WlgbJdLcxmcgdfksqjl();
                            wlgbJdLcxmcgdfksqjl.setSpbh(businessId);
                            wlgbJdLcxmcgdfksqjl.setSpbt(title);
                            wlgbJdLcxmcgdfksqjl.setSlid(processInstanceId);
                            wlgbJdLcxmcgdfksqjl.setSpjssj(processInstanceTopVo.getFinishTime());
                            wlgbJdLcxmcgdfksqjl.setSqsj(createTime);
                            List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                            if (formComponentValues != null && formComponentValues.size() > 0) {
                                for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                    String name = l.getName();
                                    if ("申请人".equals(name)) {
                                        String extValue = l.getExtValue();
                                        JSONArray jsonArray = JSONArray.parseArray(extValue);
                                        if (jsonArray != null && jsonArray.size() > 0) {
                                            JSONObject jsonObject = jsonArray.getJSONObject(0);
                                            if (jsonObject != null && jsonObject.size() > 0) {
                                                wlgbJdLcxmcgdfksqjl.setSqr(jsonObject.getString("name"));
                                                wlgbJdLcxmcgdfksqjl.setSqrid(jsonObject.getString("emplId"));
                                            }
                                        }
                                    }
                                    if ("部门".equals(name)) {
                                        JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                        if (jsonArray != null && jsonArray.size() > 0) {
                                            JSONObject jsonObject = jsonArray.getJSONObject(0);
                                            if (jsonObject != null && jsonObject.size() > 0) {
                                                wlgbJdLcxmcgdfksqjl.setSqrbm(jsonObject.getString("name"));
                                                wlgbJdLcxmcgdfksqjl.setSqrbmid(jsonObject.getString("id"));
                                            }
                                        }
                                    }
                                    if ("门店类型".equals(name)) {
                                        wlgbJdLcxmcgdfksqjl.setMdlx(l.getValue());
                                    }
                                    if ("明细".equals(name)) {
                                        try {
                                            JSONObject jsonObject = JSONObject.parseObject(l.getExtValue());
                                            JSONArray jsonArray = jsonObject.getJSONArray("statValue");
                                            if (jsonArray.size() > 0) {
                                                JSONObject jsonObject1 = jsonArray.getJSONObject(0);
                                                wlgbJdLcxmcgdfksqjl.setFkzje(jsonObject1.getDouble("num"));
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                        try {
                                            JSONArray jsonArray = JSONObject.parseArray(l.getValue());
                                            if (jsonArray != null && jsonArray.size() > 0) {
                                                JSONObject jsonObject = jsonArray.getJSONObject(0);
                                                if (jsonObject != null && jsonObject.size() > 0) {
                                                    JSONArray rowValue = jsonObject.getJSONArray("rowValue");
                                                    if (rowValue != null && rowValue.size() > 0) {
                                                        for (int i = 0; i < rowValue.size(); i++) {
                                                            JSONObject jsonObject1 = rowValue.getJSONObject(i);
                                                            if (jsonObject1 != null && jsonObject1.size() > 0) {
                                                                String label = jsonObject1.getString("label");
                                                                if ("公司门店表单".equals(label)) {
                                                                    JSONArray array = jsonObject1.getJSONArray("value");
                                                                    if (array != null && array.size() > 0) {
                                                                        String md = array.getString(0);
                                                                        if (md != null && !"".equals(md)) {
                                                                            wlgbJdLcxmcgdfksqjl.setMdmc(md);
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    if ("财务入金蝶数据".equals(name)) {
                                        cwRjdSj(l.getValue(), businessId, title, 14);
                                    }
                                    if ("出纳转账时间".equals(name)) {
                                        wlgbJdLcxmcgdfksqjl.setCnzzsj(l.getValue());
                                    }
                                    if ("出纳实际转账金额".equals(name)) {
                                        wlgbJdLcxmcgdfksqjl.setCnzzje(Double.parseDouble(l.getValue()));
                                    }
                                    if ("公司门店表单".equals(name)) {
                                        String value = l.getValue();
                                        if (value != null && !"".equals(value)) {
                                            try {
                                                JSONArray jsonArray = JSONObject.parseArray(value);
                                                if (jsonArray != null && jsonArray.size() > 0) {
                                                    wlgbJdLcxmcgdfksqjl.setMdmc(jsonArray.getString(0));
                                                }
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                            }
                                        }
                                    }
                                }
                            }
                            boolean save = lcXmCgFyFkCl(wlgbJdLcxmcgdfksqjl.getSpbh(), wlgbJdLcxmcgdfksqjl, 0);
                            if (save) {
                                wlgbJdLcxmcgdfksqjl.setSflrjd(1);
                            }
                            if (wlgbJdLcxmcgdfksqjl.getFkzje() == null) {
                                wlgbJdLcxmcgdfksqjl.setFkzje(wlgbJdLcxmcgdfksqjl.getCnzzje());
                            }
                            WlgbJdLcxmcgdfksqjl wlcxmcg1 = wlgbJdLcxmcgdfksqjlService.queryBySpBhAndSfLrJd(wlgbJdLcxmcgdfksqjl.getSpbh(), 0);
                            if (!isEmpty(wlcxmcg1)) {
                                wlgbJdLcxmcgdfksqjl.setId(wlcxmcg1.getId());
                                wlgbJdLcxmcgdfksqjlService.updateById(wlgbJdLcxmcgdfksqjl);
                            } else {
                                wlgbJdLcxmcgdfksqjlService.save(wlgbJdLcxmcgdfksqjl);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 乐诚项目预支申请
     */
    public void lcXmYzSq(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        Date createTime = processInstanceTopVo.getCreateTime();
                        if (createTime.getTime() >= new Date("2022/07/30").getTime()) {
                            String businessId = processInstanceTopVo.getBusinessId();
                            String title = processInstanceTopVo.getTitle();
                            WlgbJdLcxmyzsqjl wlgbJdLcxmyzsqjl = new WlgbJdLcxmyzsqjl();
                            wlgbJdLcxmyzsqjl.setSpbh(businessId);
                            wlgbJdLcxmyzsqjl.setSpbt(title);
                            wlgbJdLcxmyzsqjl.setSlid(processInstanceId);
                            wlgbJdLcxmyzsqjl.setSpjssj(processInstanceTopVo.getFinishTime());
                            wlgbJdLcxmyzsqjl.setSqsj(createTime);
                            List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                            if (formComponentValues != null && formComponentValues.size() > 0) {
                                for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                    String name = l.getName();
                                    if ("申请人".equals(name)) {
                                        String extValue = l.getExtValue();
                                        JSONArray jsonArray = JSONArray.parseArray(extValue);
                                        if (jsonArray != null && jsonArray.size() > 0) {
                                            JSONObject jsonObject = jsonArray.getJSONObject(0);
                                            if (jsonObject != null && jsonObject.size() > 0) {
                                                wlgbJdLcxmyzsqjl.setSqr(jsonObject.getString("name"));
                                                wlgbJdLcxmyzsqjl.setSqrid(jsonObject.getString("emplId"));
                                            }
                                        }
                                    }
                                    if ("部门".equals(name)) {
                                        JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                        if (jsonArray != null && jsonArray.size() > 0) {
                                            JSONObject jsonObject = jsonArray.getJSONObject(0);
                                            if (jsonObject != null && jsonObject.size() > 0) {
                                                wlgbJdLcxmyzsqjl.setSqrbm(jsonObject.getString("name"));
                                                wlgbJdLcxmyzsqjl.setSqrbmid(jsonObject.getString("id"));
                                            }
                                        }
                                    }
                                    if ("预支类型".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setYzlx(l.getValue());
                                    }
                                    if ("门店类型".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setMdlx(l.getValue());
                                    }
                                    if ("费用一级分类".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setFyyjfl(l.getValue());
                                    }
                                    if ("费用类别_品牌标配类".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setFylb(l.getValue());
                                    }
                                    if ("预支款用途（原因）".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setYzkyt(l.getValue());
                                    }
                                    if ("预计抵预支时间（不得超过一个月）".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setYjdyzsj(l.getValue());
                                    }
                                    if ("本人累积所欠预支款".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setLjqk(Double.parseDouble(l.getValue()));
                                    }
                                    if ("本次预支金额（元）".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setBcyzje(Double.parseDouble(l.getValue()));
                                    }
                                    if ("银行卡户主姓名".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setYhkhzxm(l.getValue());
                                    }
                                    if ("收款账号".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setSkzh(l.getValue());
                                    }
                                    if ("开户行".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setKhh(l.getValue());
                                    }
                                    if ("财务入金蝶数据".equals(name)) {
                                        cwRjdSj(l.getValue(), businessId, title, 15);
                                    }
                                    if ("出纳转账时间".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setCnzzsj(l.getValue());
                                    }
                                    if ("出纳实际转账金额".equals(name)) {
                                        wlgbJdLcxmyzsqjl.setCnzzje(Double.parseDouble(l.getValue()));
                                    }
                                }
                            }
                            boolean save = lcXmYzCl(wlgbJdLcxmyzsqjl.getSpbh(), wlgbJdLcxmyzsqjl, 0);
                            if (save) {
                                wlgbJdLcxmyzsqjl.setSflrjd(1);
                            }
                            WlgbJdLcxmyzsqjl wlcxmcg1 = wlgbJdLcxmyzsqjlService.queryBySpBhAndSfLrJd(wlgbJdLcxmyzsqjl.getSpbh(), 0);
                            if (!isEmpty(wlcxmcg1)) {
                                wlgbJdLcxmyzsqjl.setId(wlcxmcg1.getId());
                                wlgbJdLcxmyzsqjlService.updateById(wlgbJdLcxmyzsqjl);
                            } else {
                                wlgbJdLcxmyzsqjlService.save(wlgbJdLcxmyzsqjl);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 乐诚项目抵预支申请
     */
    public void lcXmDyzSq(JSONObject callBackContent) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            if (processInstanceTopVo != null) {
                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //只有审批完成才进来
                if ("COMPLETED".equals(status)) {
                    if ("agree".equalsIgnoreCase(result)) {
                        Date createTime = processInstanceTopVo.getCreateTime();
                        if (createTime.getTime() >= new Date("2022/07/30").getTime()) {
                            String businessId = processInstanceTopVo.getBusinessId();
                            String title = processInstanceTopVo.getTitle();
                            WlgbJdLcxmdyzsqjl wlgbJdLcxmdyzsqjl = new WlgbJdLcxmdyzsqjl();
                            wlgbJdLcxmdyzsqjl.setSpbh(businessId);
                            wlgbJdLcxmdyzsqjl.setSpbt(title);
                            wlgbJdLcxmdyzsqjl.setSlid(processInstanceId);
                            wlgbJdLcxmdyzsqjl.setSpjssj(processInstanceTopVo.getFinishTime());
                            wlgbJdLcxmdyzsqjl.setSqsj(createTime);
                            List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
                            if (formComponentValues != null && formComponentValues.size() > 0) {
                                for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                                    String name = l.getName();
                                    if ("申请人".equals(name)) {
                                        String extValue = l.getExtValue();
                                        JSONArray jsonArray = JSONArray.parseArray(extValue);
                                        if (jsonArray != null && jsonArray.size() > 0) {
                                            JSONObject jsonObject = jsonArray.getJSONObject(0);
                                            if (jsonObject != null && jsonObject.size() > 0) {
                                                wlgbJdLcxmdyzsqjl.setSqr(jsonObject.getString("name"));
                                                wlgbJdLcxmdyzsqjl.setSqrid(jsonObject.getString("emplId"));
                                            }
                                        }
                                    }
                                    if ("部门".equals(name)) {
                                        JSONArray jsonArray = JSONArray.parseArray(l.getExtValue());
                                        if (jsonArray != null && jsonArray.size() > 0) {
                                            JSONObject jsonObject = jsonArray.getJSONObject(0);
                                            if (jsonObject != null && jsonObject.size() > 0) {
                                                wlgbJdLcxmdyzsqjl.setSqrbm(jsonObject.getString("name"));
                                                wlgbJdLcxmdyzsqjl.setSqrbmid(jsonObject.getString("id"));
                                            }
                                        }
                                    }
                                    if ("门店类型".equals(name)) {
                                        wlgbJdLcxmdyzsqjl.setMdlx(l.getValue());
                                    }
                                    if ("费用发生项目名称".equals(name)) {
                                        wlgbJdLcxmdyzsqjl.setFyfsxmmc(l.getValue());
                                    }
                                    if ("预计回票时间（下款后的一个月内）".equals(name)) {
                                        wlgbJdLcxmdyzsqjl.setYjhfsj(l.getValue());
                                    }
                                    if ("费用明细".equals(name)) {
                                        try {
                                            JSONObject jsonObject = JSONObject.parseObject(l.getExtValue());
                                            JSONArray jsonArray = jsonObject.getJSONArray("statValue");
                                            if (jsonArray.size() > 0) {
                                                JSONObject jsonObject1 = jsonArray.getJSONObject(0);
                                                wlgbJdLcxmdyzsqjl.setDyzje(jsonObject1.getDouble("num"));
                                            }
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }
                                    }
                                    if ("财务入金蝶数据".equals(name)) {
                                        cwRjdSj(l.getValue(), businessId, title, 16);
                                    }
                                }
                            }
                            boolean save = lcXmDyzCl(wlgbJdLcxmdyzsqjl.getSpbh(), wlgbJdLcxmdyzsqjl, 0);
                            if (save) {
                                wlgbJdLcxmdyzsqjl.setSflrjd(1);
                            }
                            WlgbJdLcxmdyzsqjl wlcxmcg1 = wlgbJdLcxmdyzsqjlService.queryBySpBhAndSfLrJd(wlgbJdLcxmdyzsqjl.getSpbh(), 0);
                            if (!isEmpty(wlcxmcg1)) {
                                wlgbJdLcxmdyzsqjl.setId(wlcxmcg1.getId());
                                wlgbJdLcxmdyzsqjlService.updateById(wlgbJdLcxmdyzsqjl);
                            } else {
                                wlgbJdLcxmdyzsqjlService.save(wlgbJdLcxmdyzsqjl);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 报销审批上传到金蝶
     *
     * @param spbh         审批编号
     * @param wlgbJdBxspjl 报销审批记录
     * @param type
     * @return
     */
    public boolean bxCl(String spbh, WlgbJdBxspjl wlgbJdBxspjl, Integer type) {
        if (type == 1) {
            wlgbJdBxspjl = wlgbJdBxspjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdBxspjl != null) {
            Boolean bmCl = KingDeeConfig.bmCl(wlgbJdBxspjl.getSqrbmid(), wlgbJdBxspjl.getSqrbm());
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(wlgbJdBxspjl.getSqrid(), wlgbJdBxspjl.getSqr());
                if (ryCl) {
                    List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(0, wlgbJdBxspjl.getSpbh(), 0);
                    List<String> list2 = new ArrayList<>();
                    list.forEach(l -> {
                        if (!list2.contains(l.getZtbm())) {
                            list2.add(l.getZtbm());
                        }
                    });
                    Double je = 0.0;
                    boolean sfXyb = true;
                    for (int p = 0; p < list2.size(); p++) {
                        if (sfXyb) {
                            String bm = list2.get(p);
                            List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(0, wlgbJdBxspjl.getSpbh(), 0, bm);
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("spbh", wlgbJdBxspjl.getSpbh());
                            jsonObject.put("fyssft", wlgbJdBxspjl.getFyssft());
                            jsonObject.put("sfsymdft", wlgbJdBxspjl.getSfsymdpt());
                            jsonObject.put("cnzzsj", wlgbJdBxspjl.getCnzzsj());
                            jsonObject.put("djFyZje", wlgbJdBxspjl.getBxzje());
                            JSONObject jsonObject1 = ft1(list2, jsonObject, 0, p, je, list3, wlgbJdBxspjl.getSqsj(), wlgbJdBxspjl.getSqrbmid(), wlgbJdBxspjl.getSfxft() != null ? wlgbJdBxspjl.getSfxft() : 0);
                            sfXyb = jsonObject1.getBoolean("success");
                            if (sfXyb) {
                                je = jsonObject1.getDouble("je");
                                JSONArray jsonArray = jsonObject1.getJSONArray("list");
                                List<JSONObject> list1 = JSONObject.parseArray(jsonArray.toJSONString(), JSONObject.class);
                                String json = "{\n" +
                                        "    \"NeedUpDateFields\": [],\n" +
                                        "    \"NeedReturnFields\": [],\n" +
                                        "    \"IsDeleteEntry\": \"true\",\n" +
                                        "    \"SubSystemId\": \"\",\n" +
                                        "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                        "    \"IsEntryBatchFill\": \"true\",\n" +
                                        "    \"ValidateFlag\": \"true\",\n" +
                                        "    \"NumberSearch\": \"true\",\n" +
                                        "    \"IsAutoAdjustField\": \"false\",\n" +
                                        "    \"InterationFlags\": \"\",\n" +
                                        "    \"IgnoreInterationFlag\": \"\",\n" +
                                        "    \"Model\": {\n" +
                                        "        \"FID\": 0,\n" +
                                        "        \"FBillTypeID\": {\n" +
                                        "            \"FNUMBER\": \"QTYFD02_SYS\"\n" +
                                        "        },\n" +
                                        "        \"FDATE\": \"" + wlgbJdBxspjl.getCnzzsj() + "\",\n" +
                                        "        \"FENDDATE_H\": \"" + wlgbJdBxspjl.getCnzzsj() + "\",\n" +
                                        "        \"FISINIT\": false,\n" +
                                        "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                        "        \"FCONTACTUNIT\": {\n" +
                                        "            \"FNumber\": \"" + wlgbJdBxspjl.getSqrid() + "\"\n" +
                                        "        },\n" +
                                        "        \"FDEPARTMENTID\": {\n" +
                                        "            \"FNumber\": \"" + wlgbJdBxspjl.getSqrbmid() + "\"\n" +
                                        "        },\n" +
                                        "        \"FCURRENCYID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FSETTLEORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FPURCHASEORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FPAYORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FDepartment\": {\n" +
                                        "            \"FNumber\": \"" + wlgbJdBxspjl.getSqrbmid() + "\"\n" +
                                        "        },\n" +
                                        "        \"FMAINBOOKSTDCURRID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FEXCHANGETYPE\": {\n" +
                                        "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                        "        },\n" +
                                        "        \"FExchangeRate\": 1.0,\n" +
                                        "        \"FACCNTTIMEJUDGETIME\": \"" + wlgbJdBxspjl.getCnzzsj() + "\",\n" +
                                        "        \"FCancelStatus\": \"A\",\n" +
                                        "        \"FBUSINESSTYPE\": \"T\",\n" +
                                        "        \"FRemarks\": \"" + wlgbJdBxspjl.getFyzymx() + "\",\n" +
                                        "        \"FSPSQR\": \"" + wlgbJdBxspjl.getSqr() + "\",\n" +
                                        "        \"FFYSYF\": \"" + wlgbJdBxspjl.getFysyf() + "\",\n" +
                                        "        \"FSPSQRBM\": \"" + wlgbJdBxspjl.getSqrbm() + "\",\n" +
                                        "        \"FSPSLBT\": \"" + wlgbJdBxspjl.getSpbt() + "\",\n" +
                                        "        \"FSPBH\": \"" + wlgbJdBxspjl.getSpbh() + "\",\n" +
                                        "        \"FEntity\": " + list1.toString() +
                                        "    }\n" +
                                        "}";
                                //录入金蝶
                                log.info("上传金蝶其他应付单的报销数据*********{}", json);
                                Map<String, String> map = KingDeeConfig.spTjJdDj2(json, "其他应付单");
                                save = "true".equals(map.get("jg"));
                                if (save) {
                                    WlgbJdYhk wlgbJdYhk = yhkCl(bm);
                                    String json1 = "{\n" +
                                            "    \"NeedUpDateFields\": [],\n" +
                                            "    \"NeedReturnFields\": [],\n" +
                                            "    \"IsDeleteEntry\": \"true\",\n" +
                                            "    \"SubSystemId\": \"\",\n" +
                                            "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                            "    \"IsEntryBatchFill\": \"true\",\n" +
                                            "    \"ValidateFlag\": \"true\",\n" +
                                            "    \"NumberSearch\": \"true\",\n" +
                                            "    \"IsAutoAdjustField\": \"false\",\n" +
                                            "    \"InterationFlags\": \"\",\n" +
                                            "    \"IgnoreInterationFlag\": \"\",\n" +
                                            "    \"Model\": {\n" +
                                            "        \"FID\": 0,\n" +
                                            "        \"FBillTypeID\": {\n" +
                                            "            \"FNUMBER\": \"FKDLX04_SYS\"\n" +
                                            "        },\n" +
                                            "        \"FDATE\": \"" + wlgbJdBxspjl.getCnzzsj() + "\",\n" +
                                            "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FCONTACTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdBxspjl.getSqrid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FRECTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdBxspjl.getSqrid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FDepartment\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdBxspjl.getSqrbmid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FISINIT\": false,\n" +
                                            "        \"FCURRENCYID\": {\n" +
                                            "            \"FNumber\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FEXCHANGERATE\": 1.0,\n" +
                                            "        \"FSETTLERATE\": 1.0,\n" +
                                            "        \"FSETTLEORGID\": {\n" +
                                            "            \"FNumber\": \"" + bm + "\"\n" +
                                            "        },\n" +
                                            "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                            "        \"FBUSINESSTYPE\": \"3\",\n" +
                                            "        \"FCancelStatus\": \"A\",\n" +
                                            "        \"FPAYORGID\": {\n" +
                                            "            \"FNumber\": \"" + bm + "\"\n" +
                                            "        },\n" +
                                            "        \"FISSAMEORG\": true,\n" +
                                            "        \"FIsCredit\": false,\n" +
                                            "        \"FSETTLECUR\": {\n" +
                                            "            \"FNUMBER\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FIsWriteOff\": false,\n" +
                                            "        \"FREALPAY\": false,\n" +
                                            "        \"FISCARRYRATE\": false,\n" +
                                            "        \"FSETTLEMAINBOOKID\": {\n" +
                                            "            \"FNUMBER\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FMoreReceive\": false,\n" +
                                            "        \"FVirIsSameAcctOrg\": false,\n" +
                                            "        \"FREMARK\": \"" + wlgbJdBxspjl.getFyzymx() + "\",\n" +
                                            "        \"FSPBT\": \"" + wlgbJdBxspjl.getSpbt() + "\",\n" +
                                            "        \"FSPBH\": \"" + wlgbJdBxspjl.getSpbh() + "\",\n" +
                                            "        \"FSPSQR\": \"" + wlgbJdBxspjl.getSqr() + "\",\n" +
                                            "        \"FFYSYF\": \"" + wlgbJdBxspjl.getFysyf() + "\",\n" +
                                            "        \"FSPSQRBM\": \"" + wlgbJdBxspjl.getSqrbm() + "\",\n";
                                    List<JSONObject> list4 = new ArrayList<>();
                                    String cnzzsj = wlgbJdBxspjl.getCnzzsj();
                                    String sqrbmid = wlgbJdBxspjl.getSqrbmid();
                                    list1.forEach(l -> {
                                        String fcostid = l.getJSONObject("FCOSTID").getString("FNumber");
                                        String fssmd = l.getJSONObject("FSSMD").getString("FNumber");
                                        Double fnotaxamountfor = l.getDouble("FNOTAXAMOUNTFOR");
                                        JSONObject jsonObject2 = fkdMx1(fssmd, fnotaxamountfor, cnzzsj, fcostid, sqrbmid, "SFKYT10_SYS", wlgbJdYhk.getZh());

                                        list4.add(jsonObject2);
                                    });
                                    json1 += "        \"FPAYBILLENTRY\": " + list4.toString() +
                                            "    }\n" +
                                            "}";
                                    //录入金蝶
                                    log.info("上传金蝶付款单的报销数据*********{}", json1);
                                    Map<String, String> map2 = KingDeeConfig.spTjJdDj2(json1, "付款单");
                                    save = "true".equals(map2.get("jg"));
                                    if (!save) {
                                        sendGztz(0, wlgbJdBxspjl.getSpbh(), "录入金蝶失败,原因：" + map.get("msg"), "付款单");
                                    }
                                } else {
                                    sendGztz(0, wlgbJdBxspjl.getSpbh(), "录入金蝶失败,原因：" + map.get("msg"), "其他应付单");
                                }
                            }
                            if (save) {
                                //上传单据成功之后将里面数据改成上传完成
                                list3.forEach(l -> {
                                    WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                    wlgbJdSpCwlrjdsj.setId(l.getId());
                                    wlgbJdSpCwlrjdsj.setSfscjd(1);
                                    wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                                });
                            }
                        }
                    }
                    if (!sfXyb) {
                        sendGztz(0, wlgbJdBxspjl.getSpbh(), "别墅缺失", "报销");
                    }
                    if (save) {
                        if (type == 1) {
                            WlgbJdBxspjl wlgbJdBxspjl1 = new WlgbJdBxspjl();
                            wlgbJdBxspjl1.setId(wlgbJdBxspjl.getId());
                            wlgbJdBxspjl1.setSflrjd(1);
                            wlgbJdBxspjlService.updateById(wlgbJdBxspjl1);
                        }
                    }
                } else {
                    sendGztz(0, wlgbJdBxspjl.getSpbh(), "员工缺失，补充错误", "报销");
                }
            } else {
                sendGztz(0, wlgbJdBxspjl.getSpbh(), "部门缺失，补充错误", "报销");
            }
        }
        return save;
    }


    /**
     * 预支审批处理
     */
    public boolean yzCl(String spbh, WlgbJdYzsqjl wlgbJdYzsqjl, Integer type) {
        if (type == 1) {
            wlgbJdYzsqjl = wlgbJdYzsqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdYzsqjl != null) {
            Boolean bmCl = KingDeeConfig.bmCl(wlgbJdYzsqjl.getSqrbmid(), wlgbJdYzsqjl.getSqrbm());
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(wlgbJdYzsqjl.getSqrid(), wlgbJdYzsqjl.getSqr());
                if (ryCl) {
                    List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(1, wlgbJdYzsqjl.getSpbh(), 0);
                    SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
                    List<String> list2 = new ArrayList<>();
                    list.forEach(l -> {
                        if (!list2.contains(l.getZtbm())) {
                            list2.add(l.getZtbm());
                        }
                    });
                    String sqrbmid = wlgbJdYzsqjl.getSqrbmid();
                    String cnzzsj = df2.format(wlgbJdYzsqjl.getCnzzsj());
                    for (String bm : list2) {
                        List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(1, wlgbJdYzsqjl.getSpbh(), 0, bm);
                        String json = "{\n" +
                                "    \"NeedUpDateFields\": [],\n" +
                                "    \"NeedReturnFields\": [],\n" +
                                "    \"IsDeleteEntry\": \"true\",\n" +
                                "    \"SubSystemId\": \"\",\n" +
                                "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                "    \"IsEntryBatchFill\": \"true\",\n" +
                                "    \"ValidateFlag\": \"true\",\n" +
                                "    \"NumberSearch\": \"true\",\n" +
                                "    \"IsAutoAdjustField\": \"false\",\n" +
                                "    \"InterationFlags\": \"\",\n" +
                                "    \"IgnoreInterationFlag\": \"\",\n" +
                                "    \"Model\": {\n" +
                                "        \"FID\": 0,\n" +
                                "        \"FBillTypeID\": {\n" +
                                "            \"FNUMBER\": \"FKDLX02_SYS\"\n" +
                                "        },\n" +
                                "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                "        \"FCONTACTUNIT\": {\n" +
                                "            \"FNumber\": \"" + wlgbJdYzsqjl.getSqrid() + "\"\n" +
                                "        },\n" +
                                "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                "        \"FRECTUNIT\": {\n" +
                                "            \"FNumber\": \"" + wlgbJdYzsqjl.getSqrid() + "\"\n" +
                                "        },\n" +
                                "        \"FDepartment\": {\n" +
                                "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                "        },\n" +
                                "        \"FISINIT\": false,\n" +
                                "        \"FCURRENCYID\": {\n" +
                                "            \"FNumber\": \"PRE001\"\n" +
                                "        },\n" +
                                "        \"FEXCHANGERATE\": 1.0,\n" +
                                "        \"FSETTLERATE\": 1.0,\n" +
                                "        \"FSETTLEORGID\": {\n" +
                                "            \"FNumber\": \"" + bm + "\"\n" +
                                "        },\n" +
                                "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                "        \"FBUSINESSTYPE\": \"3\",\n" +
                                "        \"FCancelStatus\": \"A\",\n" +
                                "        \"FPAYORGID\": {\n" +
                                "            \"FNumber\": \"" + bm + "\"\n" +
                                "        },\n" +
                                "        \"FISSAMEORG\": true,\n" +
                                "        \"FIsCredit\": false,\n" +
                                "        \"FSETTLECUR\": {\n" +
                                "            \"FNUMBER\": \"PRE001\"\n" +
                                "        },\n" +
                                "        \"FIsWriteOff\": false,\n" +
                                "        \"FREALPAY\": false,\n" +
                                "        \"FISCARRYRATE\": false,\n" +
                                "        \"FSETTLEMAINBOOKID\": {\n" +
                                "            \"FNUMBER\": \"PRE001\"\n" +
                                "        },\n" +
                                "        \"FMoreReceive\": false,\n" +
                                "        \"FVirIsSameAcctOrg\": false,\n" +
                                "        \"FREMARK\": \"" + wlgbJdYzsqjl.getYzkyt() + "\",\n" +
                                "        \"FSPBT\": \"" + wlgbJdYzsqjl.getSpbt() + "\",\n" +
                                "        \"FSPBH\": \"" + wlgbJdYzsqjl.getSpbh() + "\",\n" +
                                "        \"FSPSQR\": \"" + wlgbJdYzsqjl.getSqr() + "\",\n" +
                                "        \"FSPSQRBM\": \"" + wlgbJdYzsqjl.getSqrbm() + "\",\n" +
                                "        \"FFYSYF\": \"" + wlgbJdYzsqjl.getYzksyf() + "\",\n";
                        WlgbJdYhk wlgbJdYhk = yhkCl(bm);
                        List<JSONObject> list1 = new ArrayList<>();
                        list3.forEach(l -> {
                            JSONObject jsonObject = new JSONObject();
                            //电汇
                            jsonObject.put("FSETTLETYPEID", KingDeeConfig.zhdx("JSFS04_SYS"));
                            //银行卡账号
                            jsonObject.put("FACCOUNTID", KingDeeConfig.zhdx(wlgbJdYhk.getZh()));
                            jsonObject.put("FPURPOSEID", KingDeeConfig.zhdx("SFKYT42_SYS"));
                            jsonObject.put("FPAYTOTALAMOUNTFOR", l.getFyje());
                            jsonObject.put("FPAYAMOUNTFOR_E", l.getFyje());
                            jsonObject.put("FSETTLEPAYAMOUNTFOR", l.getFyje());
                            jsonObject.put("FRecType", "1");
                            jsonObject.put("FCOSTID", KingDeeConfig.zhdx(l.getFykmbm()));
                            //部门
                            jsonObject.put("FEXPENSEDEPTID_E", KingDeeConfig.zhdx(sqrbmid));
                            jsonObject.put("FPAYAMOUNT_E", l.getFyje());
                            jsonObject.put("FPOSTDATE", cnzzsj);
                            jsonObject.put("FRuZhangType", "1");
                            jsonObject.put("FPayType", "A");
                            jsonObject.put("FNOTVERIFICATEAMOUNT", l.getFyje());
                            jsonObject.put("FBankInvoice", false);
                            jsonObject.put("FByAgentBank", false);
                            jsonObject.put("FOverseaPay", false);
                            list1.add(jsonObject);
                        });
                        json += "        \"FPAYBILLENTRY\": " + list1.toString() +
                                "    }\n" +
                                "}";
                        //录入金蝶
                        Map<String, String> map22 = KingDeeConfig.spTjJdDj2(json, "付款单");
                        save = "true".equals(map22.get("jg"));
                        if (!save) {
                            sendGztz(0, wlgbJdYzsqjl.getSpbh(), "录入金蝶失败,原因：" + map22.get("msg"), "付款单");
                        }
                        if (save) {
                            //上传单据成功之后将里面数据改成上传完成
                            list3.forEach(l -> {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                wlgbJdSpCwlrjdsj.setId(l.getId());
                                wlgbJdSpCwlrjdsj.setSfscjd(1);
                                wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                            });
                        }
                    }
                    if (save) {
                        if (type == 1) {
                            WlgbJdYzsqjl wlgbJdYzsqjl1 = new WlgbJdYzsqjl();
                            wlgbJdYzsqjl1.setId(wlgbJdYzsqjl.getId());
                            wlgbJdYzsqjl1.setSflrjd(1);
                            wlgbJdYzsqjlService.updateById(wlgbJdYzsqjl1);
                        }
                    }
                } else {
                    sendGztz(0, wlgbJdYzsqjl.getSpbh(), "员工缺失，补充错误", "预支");
                }
            } else {
                sendGztz(0, wlgbJdYzsqjl.getSpbh(), "部门缺失，补充错误", "预支");
            }
        }

        return save;
    }


    /**
     * 费用付款审批处理 并 上传金蝶
     */
    public boolean fyFkCl(String spbh, WlgbJdFyfksqjl wlgbJdFyfksqjl, Integer type) {
        if (type == 1) {
            wlgbJdFyfksqjl = wlgbJdFyfksqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdFyfksqjl != null) {
            Boolean bmCl = KingDeeConfig.bmCl(wlgbJdFyfksqjl.getSqrbmid(), wlgbJdFyfksqjl.getSqrbm());
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(wlgbJdFyfksqjl.getSqrid(), wlgbJdFyfksqjl.getSqr());
                if (ryCl) {
                    List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(2, wlgbJdFyfksqjl.getSpbh(), 0);
                    List<String> list2 = new ArrayList<>();
                    list.forEach(l -> {
                        if (!list2.contains(l.getZtbm())) {
                            list2.add(l.getZtbm());
                        }
                    });
                    Double je = 0.0;
                    boolean sfXyb = true;
                    String cnzzsj = wlgbJdFyfksqjl.getCnzzsj();
                    for (int p = 0; p < list2.size(); p++) {
                        if (sfXyb) {
                            String bm = list2.get(p);
                            List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(2, wlgbJdFyfksqjl.getSpbh(), 0, bm);
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("spbh", wlgbJdFyfksqjl.getSpbh());
                            jsonObject.put("fyssft", wlgbJdFyfksqjl.getFyssft());
                            jsonObject.put("sfsymdft", wlgbJdFyfksqjl.getSfsymdft());
                            jsonObject.put("cnzzsj", cnzzsj);
                            jsonObject.put("djFyZje", wlgbJdFyfksqjl.getHjje());
                            JSONObject jsonObject1 = ft1(list2, jsonObject, 2, p, je, list3, wlgbJdFyfksqjl.getSqsj(), wlgbJdFyfksqjl.getSqrbmid(), wlgbJdFyfksqjl.getSfxft() != null ? wlgbJdFyfksqjl.getSfxft() : 0);
                            sfXyb = jsonObject1.getBoolean("success");
                            if (sfXyb) {
                                je = jsonObject1.getDouble("je");
                                JSONArray jsonArray = jsonObject1.getJSONArray("list");
                                List<JSONObject> list1 = JSONObject.parseArray(jsonArray.toJSONString(), JSONObject.class);
                                String json = "{\n" +
                                        "    \"NeedUpDateFields\": [],\n" +
                                        "    \"NeedReturnFields\": [],\n" +
                                        "    \"IsDeleteEntry\": \"true\",\n" +
                                        "    \"SubSystemId\": \"\",\n" +
                                        "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                        "    \"IsEntryBatchFill\": \"true\",\n" +
                                        "    \"ValidateFlag\": \"true\",\n" +
                                        "    \"NumberSearch\": \"true\",\n" +
                                        "    \"IsAutoAdjustField\": \"false\",\n" +
                                        "    \"InterationFlags\": \"\",\n" +
                                        "    \"IgnoreInterationFlag\": \"\",\n" +
                                        "    \"Model\": {\n" +
                                        "        \"FID\": 0,\n" +
                                        "        \"FBillTypeID\": {\n" +
                                        "            \"FNUMBER\": \"QTYFD01_SYS\"\n" +
                                        "        },\n" +
                                        "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                        "        \"FENDDATE_H\": \"" + cnzzsj + "\",\n" +
                                        "        \"FISINIT\": false,\n" +
                                        "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                        "        \"FCONTACTUNIT\": {\n" +
                                        "            \"FNumber\": \"" + wlgbJdFyfksqjl.getSqrid() + "\"\n" +
                                        "        },\n" +
                                        "        \"FCURRENCYID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FDEPARTMENTID\": {\n" +
                                        "            \"FNumber\": \"" + wlgbJdFyfksqjl.getSqrbmid() + "\"\n" +
                                        "        },\n" +
                                        "        \"FSETTLEORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FPURCHASEORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FPAYORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FMAINBOOKSTDCURRID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FEXCHANGETYPE\": {\n" +
                                        "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                        "        },\n" +
                                        "        \"FExchangeRate\": 1.0,\n" +
                                        "        \"FACCNTTIMEJUDGETIME\": \"" + cnzzsj + "\",\n" +
                                        "        \"FCancelStatus\": \"A\",\n" +
                                        "        \"FBUSINESSTYPE\": \"T\",\n" +
                                        "        \"FRemarks\": \"" + wlgbJdFyfksqjl.getFyzymx() + "\",\n" +
                                        "        \"FSPSLBT\": \"" + wlgbJdFyfksqjl.getSpbt() + "\",\n" +
                                        "        \"FSPBH\": \"" + wlgbJdFyfksqjl.getSpbh() + "\",\n" +
                                        "        \"FSPSQR\": \"" + wlgbJdFyfksqjl.getSqr() + "\",\n" +
                                        "        \"FFYSYF\": \"" + wlgbJdFyfksqjl.getFysyf() + "\",\n" +
                                        "        \"FSPSQRBM\": \"" + wlgbJdFyfksqjl.getSqrbm() + "\",\n";
                                json += "        \"FEntity\": " + list1.toString() +
                                        "    }\n" +
                                        "}";
                                //录入金蝶
                                Map<String, String> map21 = KingDeeConfig.spTjJdDj2(json, "其他应付单");
                                save = "true".equals(map21.get("jg"));
                                if (!save) {
                                    sendGztz(0, wlgbJdFyfksqjl.getSpbh(), "录入金蝶失败,原因：" + map21.get("msg"), "其他应付单");
                                }
                                if (save) {
                                    String json1 = "{\n" +
                                            "    \"NeedUpDateFields\": [],\n" +
                                            "    \"NeedReturnFields\": [],\n" +
                                            "    \"IsDeleteEntry\": \"true\",\n" +
                                            "    \"SubSystemId\": \"\",\n" +
                                            "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                            "    \"IsEntryBatchFill\": \"true\",\n" +
                                            "    \"ValidateFlag\": \"true\",\n" +
                                            "    \"NumberSearch\": \"true\",\n" +
                                            "    \"IsAutoAdjustField\": \"false\",\n" +
                                            "    \"InterationFlags\": \"\",\n" +
                                            "    \"IgnoreInterationFlag\": \"\",\n" +
                                            "    \"Model\": {\n" +
                                            "        \"FID\": 0,\n" +
                                            "        \"FBillTypeID\": {\n" +
                                            "            \"FNUMBER\": \"FKDLX02_SYS\"\n" +
                                            "        },\n" +
                                            "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                            "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FCONTACTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdFyfksqjl.getSqrid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FRECTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdFyfksqjl.getSqrid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FDepartment\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdFyfksqjl.getSqrbmid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FISINIT\": false,\n" +
                                            "        \"FCURRENCYID\": {\n" +
                                            "            \"FNumber\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FEXCHANGERATE\": 1.0,\n" +
                                            "        \"FSETTLERATE\": 1.0,\n" +
                                            "        \"FSETTLEORGID\": {\n" +
                                            "            \"FNumber\": \"" + bm + "\"\n" +
                                            "        },\n" +
                                            "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                            "        \"FBUSINESSTYPE\": \"3\",\n" +
                                            "        \"FCancelStatus\": \"A\",\n" +
                                            "        \"FPAYORGID\": {\n" +
                                            "            \"FNumber\": \"" + bm + "\"\n" +
                                            "        },\n" +
                                            "        \"FISSAMEORG\": true,\n" +
                                            "        \"FIsCredit\": false,\n" +
                                            "        \"FSETTLECUR\": {\n" +
                                            "            \"FNUMBER\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FIsWriteOff\": false,\n" +
                                            "        \"FREALPAY\": false,\n" +
                                            "        \"FISCARRYRATE\": false,\n" +
                                            "        \"FSETTLEMAINBOOKID\": {\n" +
                                            "            \"FNUMBER\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FMoreReceive\": false,\n" +
                                            "        \"FVirIsSameAcctOrg\": false,\n" +
                                            "        \"FREMARK\": \"" + wlgbJdFyfksqjl.getFyzymx() + "\",\n" +
                                            "        \"FSPBT\": \"" + wlgbJdFyfksqjl.getSpbt() + "\",\n" +
                                            "        \"FSPBH\": \"" + wlgbJdFyfksqjl.getSpbh() + "\",\n" +
                                            "        \"FSPSQR\": \"" + wlgbJdFyfksqjl.getSqr() + "\",\n" +
                                            "        \"FSPSQRBM\": \"" + wlgbJdFyfksqjl.getSqrbm() + "\",\n" +
                                            "        \"FFYSYF\": \"" + wlgbJdFyfksqjl.getFysyf() + "\",\n";
                                    WlgbJdYhk wlgbJdYhk = yhkCl(bm);
                                    List<JSONObject> list4 = new ArrayList<>();
                                    String sqrbmid = wlgbJdFyfksqjl.getSqrbmid();
                                    list1.forEach(l -> {
                                        String fcostid = l.getJSONObject("FCOSTID").getString("FNumber");
                                        String fssmd = l.getJSONObject("FSSMD").getString("FNumber");
                                        Double fnotaxamountfor = l.getDouble("FNOTAXAMOUNTFOR");
                                        JSONObject jsonObject2 = fkdMx1(fssmd, fnotaxamountfor, cnzzsj, fcostid, sqrbmid, "SFKYT20_SYS", wlgbJdYhk.getZh());

                                        list4.add(jsonObject2);
                                    });
                                    json1 += "        \"FPAYBILLENTRY\": " + list4.toString() +
                                            "    }\n" +
                                            "}";
                                    //录入金蝶
                                    Map<String, String> map20 = KingDeeConfig.spTjJdDj2(json1, "付款单");
                                    save = "true".equals(map20.get("jg"));
                                    if (!save) {
                                        sendGztz(0, wlgbJdFyfksqjl.getSpbh(), "录入金蝶失败,原因：" + map20.get("msg"), "付款单");
                                    }
                                }
                            }
                            if (save) {
                                //上传单据成功之后将里面数据改成上传完成
                                list3.forEach(l -> {
                                    WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                    wlgbJdSpCwlrjdsj.setId(l.getId());
                                    wlgbJdSpCwlrjdsj.setSfscjd(1);
                                    wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                                });
                            }
                        }
                    }
                    if (!sfXyb) {
                        sendGztz(0, wlgbJdFyfksqjl.getSpbh(), "别墅缺失", "费用付款");
                    }
                    if (save) {
                        if (type == 1) {
                            WlgbJdFyfksqjl wlgbJdFyfksqjl1 = new WlgbJdFyfksqjl();
                            wlgbJdFyfksqjl1.setId(wlgbJdFyfksqjl.getId());
                            wlgbJdFyfksqjl1.setSflrjd(1);
                            wlgbJdFyfksqjlService.updateById(wlgbJdFyfksqjl1);
                        }
                    }
                } else {
                    sendGztz(0, wlgbJdFyfksqjl.getSpbh(), "员工缺失，补充错误", "费用付款");
                }
            } else {
                sendGztz(0, wlgbJdFyfksqjl.getSpbh(), "部门缺失，补充错误", "费用付款");
            }

        }

        return save;
    }


    /**
     * 抵预支审批处理
     */
    public boolean dyzCl(String spbh, WlgbJdDyzsqjl wlgbJdDyzsqjl, Integer type) {
        if (type == 1) {
            wlgbJdDyzsqjl = wlgbJdDyzsqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdDyzsqjl != null) {
            try {
                Boolean bmCl = KingDeeConfig.bmCl(wlgbJdDyzsqjl.getSqrbmid(), wlgbJdDyzsqjl.getSqrbm());
                if (bmCl) {
                    Boolean ryCl = KingDeeConfig.ryCl(wlgbJdDyzsqjl.getSqrid(), wlgbJdDyzsqjl.getSqr());
                    if (ryCl) {
                        List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(3, wlgbJdDyzsqjl.getSpbh(), 0);
                        List<String> list2 = new ArrayList<>();
                        list.forEach(l -> {
                            if (!list2.contains(l.getZtbm())) {
                                list2.add(l.getZtbm());
                            }
                        });
                        Double je = 0.0;
                        boolean sfXyb = true;
                        String sqrbmid = wlgbJdDyzsqjl.getSqrbmid();
                        for (int p = 0; p < list2.size(); p++) {
                            if (sfXyb) {
                                String bm = list2.get(p);
                                List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(3, wlgbJdDyzsqjl.getSpbh(), 0, bm);
                                SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("spbh", wlgbJdDyzsqjl.getSpbh());
                                jsonObject.put("fyssft", wlgbJdDyzsqjl.getFyssft());
                                jsonObject.put("sfsymdft", wlgbJdDyzsqjl.getSfsymdft());
                                jsonObject.put("cnzzsj", df2.format(wlgbJdDyzsqjl.getSpjssj()));
                                jsonObject.put("djFyZje", wlgbJdDyzsqjl.getDyzje());
                                JSONObject jsonObject1 = ft1(list2, jsonObject, 3, p, je, list3, wlgbJdDyzsqjl.getSqsj(), sqrbmid, wlgbJdDyzsqjl.getSfxft() != null ? wlgbJdDyzsqjl.getSfxft() : 0);
                                sfXyb = jsonObject1.getBoolean("success");
                                if (sfXyb) {
                                    je = jsonObject1.getDouble("je");
                                    JSONArray jsonArray = jsonObject1.getJSONArray("list");
                                    List<JSONObject> list1 = JSONObject.parseArray(jsonArray.toJSONString(), JSONObject.class);
                                    String json = "{\n" +
                                            "    \"NeedUpDateFields\": [],\n" +
                                            "    \"NeedReturnFields\": [],\n" +
                                            "    \"IsDeleteEntry\": \"true\",\n" +
                                            "    \"SubSystemId\": \"\",\n" +
                                            "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                            "    \"IsEntryBatchFill\": \"true\",\n" +
                                            "    \"ValidateFlag\": \"true\",\n" +
                                            "    \"NumberSearch\": \"true\",\n" +
                                            "    \"IsAutoAdjustField\": \"false\",\n" +
                                            "    \"InterationFlags\": \"\",\n" +
                                            "    \"IgnoreInterationFlag\": \"\",\n" +
                                            "    \"Model\": {\n" +
                                            "        \"FID\": 0,\n" +
                                            "        \"FBillTypeID\": {\n" +
                                            "            \"FNUMBER\": \"" + ("是".equals(wlgbJdDyzsqjl.getSfsptsd()) ? "QTYFD01_SYS" : "QTYFD02_SYS") + "\"\n" +
                                            "        },\n" +
                                            "        \"FDATE\": \"" + df2.format(wlgbJdDyzsqjl.getSpjssj()) + "\",\n" +
                                            "        \"FENDDATE_H\": \"" + df2.format(wlgbJdDyzsqjl.getSpjssj()) + "\",\n" +
                                            "        \"FISINIT\": false,\n" +
                                            "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FCONTACTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdDyzsqjl.getSqrid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FCURRENCYID\": {\n" +
                                            "            \"FNumber\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FDEPARTMENTID\": {\n" +
                                            "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                            "        },\n" +
                                            "        \"FSETTLEORGID\": {\n" +
                                            "            \"FNumber\": \"" + bm + "\"\n" +
                                            "        },\n" +
                                            "        \"FPURCHASEORGID\": {\n" +
                                            "            \"FNumber\": \"" + bm + "\"\n" +
                                            "        },\n" +
                                            "        \"FPAYORGID\": {\n" +
                                            "            \"FNumber\": \"" + bm + "\"\n" +
                                            "        },\n" +
                                            "        \"FMAINBOOKSTDCURRID\": {\n" +
                                            "            \"FNumber\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FEXCHANGETYPE\": {\n" +
                                            "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                            "        },\n" +
                                            "        \"FExchangeRate\": 1.0,\n" +
                                            "        \"FACCNTTIMEJUDGETIME\": \"" + df2.format(wlgbJdDyzsqjl.getSpjssj()) + "\",\n" +
                                            "        \"FCancelStatus\": \"A\",\n" +
                                            "        \"FBUSINESSTYPE\": \"T\",\n" +
                                            "        \"FRemarks\": \"" + wlgbJdDyzsqjl.getFyzymx() + "\",\n" +
                                            "        \"FSPSLBT\": \"" + wlgbJdDyzsqjl.getSpbt() + "\",\n" +
                                            "        \"FSPBH\": \"" + wlgbJdDyzsqjl.getSpbh() + "\",\n" +
                                            "        \"FSPSQR\": \"" + wlgbJdDyzsqjl.getSqr() + "\",\n" +
                                            "        \"FFYSYF\": \"" + wlgbJdDyzsqjl.getFysyf() + "\",\n" +
                                            "        \"FSPSQRBM\": \"" + wlgbJdDyzsqjl.getSqrbm() + "\",\n";
                                    List<JSONObject> list4 = new ArrayList<>();
                                    list1.forEach(l -> {
                                        String fmd = l.getJSONObject("FMD").getString("FNumber");
                                        String fcostid = l.getJSONObject("FCOSTID").getString("FNumber");
                                        Double frefundamountfor = l.getDouble("FREFUNDAMOUNTFOR");
                                        JSONObject jsonObject2 = scQtYfdMx(fmd, fcostid, frefundamountfor, sqrbmid);
                                        list4.add(jsonObject2);
                                    });
                                    json += "        \"FEntity\": " + list4.toString() +
                                            "    }\n" +
                                            "}";
                                    //录入金蝶
                                    log.info("抵预支录入金蝶 其他应付单**************{}", json);
                                    Map<String, String> map19 = KingDeeConfig.spTjJdDj2(json, "其他应付单");
                                    save = "true".equals(map19.get("jg"));
                                    if (!save) {
                                        sendGztz(0, wlgbJdDyzsqjl.getSpbh(), "录入金蝶失败,原因：" + map19.get("msg"), "其他应付单");
                                    }
                                    if (save) {
                                        //上传单据成功之后将里面数据改成上传完成
                                        list3.forEach(l -> {
                                            WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                            wlgbJdSpCwlrjdsj.setId(l.getId());
                                            wlgbJdSpCwlrjdsj.setSfscjd(1);
                                            wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                                        });
                                    }
                                }
                            }
                        }
                        if (!sfXyb) {
                            sendGztz(0, wlgbJdDyzsqjl.getSpbh(), "别墅缺失", "抵预支");
                        }
                        if (save) {
                            if (type == 1) {
                                WlgbJdDyzsqjl wlgbJdDyzsqjl1 = new WlgbJdDyzsqjl();
                                wlgbJdDyzsqjl1.setId(wlgbJdDyzsqjl.getId());
                                wlgbJdDyzsqjl1.setSflrjd(1);
                                wlgbJdDyzsqjlService.updateById(wlgbJdDyzsqjl1);
                            }
                        }
                    } else {
                        sendGztz(0, wlgbJdDyzsqjl.getSpbh(), "员工缺失，补充错误", "抵预支");
                    }
                } else {
                    sendGztz(0, wlgbJdDyzsqjl.getSpbh(), "员工缺失，补充错误", "抵预支");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return save;
    }


    /**
     * 威廉币提现审批处理
     */
    public boolean wlbTxCl(String spbh, WlgbJdWlbtxjl wlgbJdWlbtxjl, Integer type) {
        if (type == 1) {
            wlgbJdWlbtxjl = wlgbJdWlbtxjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdWlbtxjl != null) {
            String sqrid = wlgbJdWlbtxjl.getSqrid();
            String sqrbmid = wlgbJdWlbtxjl.getSqrbmid();
            Boolean bmCl = KingDeeConfig.bmCl(sqrbmid, wlgbJdWlbtxjl.getSqrbm());
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(sqrid, wlgbJdWlbtxjl.getSqr());
                if (ryCl) {
                    List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(6, wlgbJdWlbtxjl.getSpbh(), 0);
                    List<String> list2 = new ArrayList<>();
                    list.forEach(l -> {
                        if (!list2.contains(l.getZtbm())) {
                            list2.add(l.getZtbm());
                        }
                    });
                    String cnzzsj = wlgbJdWlbtxjl.getCnzzsj();
                    for (String bm : list2) {
                        List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(6, wlgbJdWlbtxjl.getSpbh(), 0, bm);
                        String json = "{\n" +
                                "    \"NeedUpDateFields\": [],\n" +
                                "    \"NeedReturnFields\": [],\n" +
                                "    \"IsDeleteEntry\": \"true\",\n" +
                                "    \"SubSystemId\": \"\",\n" +
                                "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                "    \"IsEntryBatchFill\": \"true\",\n" +
                                "    \"ValidateFlag\": \"true\",\n" +
                                "    \"NumberSearch\": \"true\",\n" +
                                "    \"IsAutoAdjustField\": \"false\",\n" +
                                "    \"InterationFlags\": \"\",\n" +
                                "    \"IgnoreInterationFlag\": \"\",\n" +
                                "    \"Model\": {\n" +
                                "        \"FID\": 0,\n" +
                                "        \"FBillTypeID\": {\n" +
                                "            \"FNUMBER\": \"FKDLX02_SYS\"\n" +
                                "        },\n" +
                                "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                "        \"FCONTACTUNIT\": {\n" +
                                "            \"FNumber\": \"" + sqrid + "\"\n" +
                                "        },\n" +
                                "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                "        \"FRECTUNIT\": {\n" +
                                "            \"FNumber\": \"" + sqrid + "\"\n" +
                                "        },\n" +
                                "        \"FDepartment\": {\n" +
                                "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                "        },\n" +
                                "        \"FISINIT\": false,\n" +
                                "        \"FCURRENCYID\": {\n" +
                                "            \"FNumber\": \"PRE001\"\n" +
                                "        },\n" +
                                "        \"FEXCHANGERATE\": 1.0,\n" +
                                "        \"FSETTLERATE\": 1.0,\n" +
                                "        \"FSETTLEORGID\": {\n" +
                                "            \"FNumber\": \"" + bm + "\"\n" +
                                "        },\n" +
                                "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                "        \"FBUSINESSTYPE\": \"3\",\n" +
                                "        \"FCancelStatus\": \"A\",\n" +
                                "        \"FPAYORGID\": {\n" +
                                "            \"FNumber\": \"" + bm + "\"\n" +
                                "        },\n" +
                                "        \"FISSAMEORG\": true,\n" +
                                "        \"FIsCredit\": false,\n" +
                                "        \"FSETTLECUR\": {\n" +
                                "            \"FNUMBER\": \"PRE001\"\n" +
                                "        },\n" +
                                "        \"FIsWriteOff\": false,\n" +
                                "        \"FREALPAY\": false,\n" +
                                "        \"FISCARRYRATE\": false,\n" +
                                "        \"FSETTLEMAINBOOKID\": {\n" +
                                "            \"FNUMBER\": \"PRE001\"\n" +
                                "        },\n" +
                                "        \"FMoreReceive\": false,\n" +
                                "        \"FVirIsSameAcctOrg\": false,\n" +
                                "        \"FSPBT\": \"" + wlgbJdWlbtxjl.getSpbt() + "\",\n" +
                                "        \"FSPBH\": \"" + wlgbJdWlbtxjl.getSpbh() + "\",\n" +
                                "        \"FSPSQR\": \"" + wlgbJdWlbtxjl.getSqr() + "\",\n" +
                                "        \"FFYSYF\": \"" + wlgbJdWlbtxjl.getTxxm() + "\",\n" +
                                "        \"FSPSQRBM\": \"" + wlgbJdWlbtxjl.getSqrbm() + "\",\n";
                        List<JSONObject> list1 = new ArrayList<>();
                        WlgbJdYhk wlgbJdYhk = yhkCl(bm);
                        list3.forEach(l -> {
                            JSONObject jsonObject = fkdMx1("", l.getFyje(), cnzzsj, l.getFykmbm(), sqrbmid, "SFKYT12_SYS", wlgbJdYhk.getZh());

                            list1.add(jsonObject);
                        });
                        json += "        \"FPAYBILLENTRY\": " + list1.toString() +
                                "    }\n" +
                                "}";
                        //录入金蝶
                        Map<String, String> map18 = KingDeeConfig.spTjJdDj2(json, "付款单");
                        save = "true".equals(map18.get("jg"));
                        if (!save) {
                            sendGztz(0, wlgbJdWlbtxjl.getSpbh(), "录入金蝶失败,原因：" + map18.get("msg"), "付款单");
                        }
                        if (save) {
                            String json1 = "{\n" +
                                    "    \"NeedUpDateFields\": [],\n" +
                                    "    \"NeedReturnFields\": [],\n" +
                                    "    \"IsDeleteEntry\": \"true\",\n" +
                                    "    \"SubSystemId\": \"\",\n" +
                                    "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                    "    \"IsEntryBatchFill\": \"true\",\n" +
                                    "    \"ValidateFlag\": \"true\",\n" +
                                    "    \"NumberSearch\": \"true\",\n" +
                                    "    \"IsAutoAdjustField\": \"false\",\n" +
                                    "    \"InterationFlags\": \"\",\n" +
                                    "    \"IgnoreInterationFlag\": \"\",\n" +
                                    "    \"Model\": {\n" +
                                    "        \"FID\": 0,\n" +
                                    "        \"FBillTypeID\": {\n" +
                                    "            \"FNUMBER\": \"QTYFD02_SYS\"\n" +
                                    "        },\n" +
                                    "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                    "        \"FENDDATE_H\": \"" + cnzzsj + "\",\n" +
                                    "        \"FISINIT\": false,\n" +
                                    "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                    "        \"FCONTACTUNIT\": {\n" +
                                    "            \"FNumber\": \"" + sqrid + "\"\n" +
                                    "        },\n" +
                                    "        \"FCURRENCYID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FDEPARTMENTID\": {\n" +
                                    "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                    "        },\n" +
                                    "        \"FSETTLEORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FPURCHASEORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FPAYORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FMAINBOOKSTDCURRID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FEXCHANGETYPE\": {\n" +
                                    "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                    "        },\n" +
                                    "        \"FExchangeRate\": 1.0,\n" +
                                    "        \"FACCNTTIMEJUDGETIME\": \"" + cnzzsj + "\",\n" +
                                    "        \"FCancelStatus\": \"A\",\n" +
                                    "        \"FBUSINESSTYPE\": \"T\",\n" +
                                    "        \"FSPSLBT\": \"" + wlgbJdWlbtxjl.getSpbt() + "\",\n" +
                                    "        \"FSPBH\": \"" + wlgbJdWlbtxjl.getSpbh() + "\",\n" +
                                    "        \"FSPSQR\": \"" + wlgbJdWlbtxjl.getSqr() + "\",\n" +
                                    "        \"FFYSYF\": \"" + wlgbJdWlbtxjl.getTxxm() + "\",\n" +
                                    "        \"FSPSQRBM\": \"" + wlgbJdWlbtxjl.getSqrbm() + "\",\n";
                            List<JSONObject> list4 = new ArrayList<>();
                            list3.forEach(l -> {
                                JSONObject jsonObject = scQtYfdMx(null, l.getFykmbm(), l.getFyje(), sqrbmid);

                                list4.add(jsonObject);
                            });
                            json1 += "        \"FEntity\": " + list4.toString() +
                                    "    }\n" +
                                    "}";
                            //录入金蝶
                            Map<String, String> map17 = KingDeeConfig.spTjJdDj2(json1, "其他应付单");
                            save = "true".equals(map17.get("jg"));
                            if (!save) {
                                sendGztz(0, wlgbJdWlbtxjl.getSpbh(), "录入金蝶失败,原因：" + map17.get("msg"), "其他应付单");
                            }
                            if (save) {
                                //上传单据成功之后将里面数据改成上传完成
                                list3.forEach(l -> {
                                    WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                    wlgbJdSpCwlrjdsj.setId(l.getId());
                                    wlgbJdSpCwlrjdsj.setSfscjd(1);
                                    wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                                });
                            }
                        }
                    }
                    if (save) {
                        if (type == 1) {
                            WlgbJdWlbtxjl wlgbJdWlbtxjl1 = new WlgbJdWlbtxjl();
                            wlgbJdWlbtxjl1.setId(wlgbJdWlbtxjl.getId());
                            wlgbJdWlbtxjl1.setSflrjd(1);
                            wlgbJdWlbtxjlService.updateById(wlgbJdWlbtxjl1);
                        }
                    }
                } else {
                    sendGztz(0, wlgbJdWlbtxjl.getSpbh(), "员工缺失，补充错误", "威廉币提现");
                }
            } else {
                sendGztz(0, wlgbJdWlbtxjl.getSpbh(), "部门缺失，补充错误", "威廉币提现");
            }
        }
        return save;
    }


    /**
     * 宿舍退押金审批处理
     */
    public boolean ssTyjCl(String spbh, WlgbJdSstyjsqjl wlgbJdSstyjsqjl, Integer type) {
        if (type == 1) {
            wlgbJdSstyjsqjl = wlgbJdSstyjsqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdSstyjsqjl != null) {
            try {
                Boolean bmCl = KingDeeConfig.bmCl(wlgbJdSstyjsqjl.getSqrbmid(), wlgbJdSstyjsqjl.getSqrbm());
                if (bmCl) {
                    Boolean ryCl = KingDeeConfig.ryCl(wlgbJdSstyjsqjl.getSqrid(), wlgbJdSstyjsqjl.getSqr());
                    if (ryCl) {
                        List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(7, wlgbJdSstyjsqjl.getSpbh(), 0);
                        List<String> list2 = new ArrayList<>();
                        list.forEach(l -> {
                            if (!list2.contains(l.getZtbm())) {
                                list2.add(l.getZtbm());
                            }
                        });
                        String sqrbmid = wlgbJdSstyjsqjl.getSqrbmid();
                        String cnzzsj = wlgbJdSstyjsqjl.getCnzzsj();
                        for (int p = 0; p < list2.size(); p++) {
                            String bm = list2.get(p);
                            List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(7, wlgbJdSstyjsqjl.getSpbh(), 0, bm);
                            String json = "{\n" +
                                    "    \"NeedUpDateFields\": [],\n" +
                                    "    \"NeedReturnFields\": [],\n" +
                                    "    \"IsDeleteEntry\": \"true\",\n" +
                                    "    \"SubSystemId\": \"\",\n" +
                                    "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                    "    \"IsEntryBatchFill\": \"true\",\n" +
                                    "    \"ValidateFlag\": \"true\",\n" +
                                    "    \"NumberSearch\": \"true\",\n" +
                                    "    \"IsAutoAdjustField\": \"false\",\n" +
                                    "    \"InterationFlags\": \"\",\n" +
                                    "    \"IgnoreInterationFlag\": \"\",\n" +
                                    "    \"Model\": {\n" +
                                    "        \"FID\": 0,\n" +
                                    "        \"FBillTypeID\": {\n" +
                                    "            \"FNUMBER\": \"FKDLX02_SYS\"\n" +
                                    "        },\n" +
                                    "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                    "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                    "        \"FCONTACTUNIT\": {\n" +
                                    "            \"FNumber\": \"" + wlgbJdSstyjsqjl.getSqrid() + "\"\n" +
                                    "        },\n" +
                                    "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                    "        \"FRECTUNIT\": {\n" +
                                    "            \"FNumber\": \"" + wlgbJdSstyjsqjl.getSqrid() + "\"\n" +
                                    "        },\n" +
                                    "        \"FDepartment\": {\n" +
                                    "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                    "        },\n" +
                                    "        \"FISINIT\": false,\n" +
                                    "        \"FCURRENCYID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FEXCHANGERATE\": 1.0,\n" +
                                    "        \"FSETTLERATE\": 1.0,\n" +
                                    "        \"FSETTLEORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                    "        \"FBUSINESSTYPE\": \"3\",\n" +
                                    "        \"FCancelStatus\": \"A\",\n" +
                                    "        \"FPAYORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FISSAMEORG\": true,\n" +
                                    "        \"FIsCredit\": false,\n" +
                                    "        \"FSETTLECUR\": {\n" +
                                    "            \"FNUMBER\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FIsWriteOff\": false,\n" +
                                    "        \"FREALPAY\": false,\n" +
                                    "        \"FISCARRYRATE\": false,\n" +
                                    "        \"FSETTLEMAINBOOKID\": {\n" +
                                    "            \"FNUMBER\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FMoreReceive\": false,\n" +
                                    "        \"FVirIsSameAcctOrg\": false,\n" +
                                    "        \"FREMARK\": \"" + wlgbJdSstyjsqjl.getFyzymx() + "\",\n" +
                                    "        \"FSPBT\": \"" + wlgbJdSstyjsqjl.getSpbt() + "\",\n" +
                                    "        \"FSPBH\": \"" + wlgbJdSstyjsqjl.getSpbh() + "\",\n" +
                                    "        \"FSPSQR\": \"" + wlgbJdSstyjsqjl.getSqr() + "\",\n" +
                                    "        \"FFYSYF\": \"" + wlgbJdSstyjsqjl.getFysyf() + "\",\n" +
                                    "        \"FSPSQRBM\": \"" + wlgbJdSstyjsqjl.getSqrbm() + "\",\n";
                            WlgbJdYhk wlgbJdYhk = yhkCl(bm);
                            List<JSONObject> list1 = new ArrayList<>();
                            list3.forEach(l -> {
                                JSONObject jsonObject = fkdMx1("", l.getFyje(), cnzzsj, l.getFykmbm(), sqrbmid, "SFKYT12_SYS", wlgbJdYhk.getZh());

                                list1.add(jsonObject);
                            });
                            json += "        \"FPAYBILLENTRY\": " + list1.toString() +
                                    "    }\n" +
                                    "}";
                            //录入金蝶
                            Map<String, String> map16 = KingDeeConfig.spTjJdDj2(json, "付款单");
                            save = "true".equals(map16.get("jg"));
                            if (!save) {
                                sendGztz(0, wlgbJdSstyjsqjl.getSpbh(), "录入金蝶失败,原因：" + map16.get("msg"), "付款单");
                            }
                            if (save) {
                                //上传单据成功之后将里面数据改成上传完成
                                list3.forEach(l -> {
                                    WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                    wlgbJdSpCwlrjdsj.setId(l.getId());
                                    wlgbJdSpCwlrjdsj.setSfscjd(1);
                                    wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                                });
                            }
                        }
                        if (save) {
                            if (type == 1) {
                                WlgbJdSstyjsqjl wlgbJdSstyjsqjl1 = new WlgbJdSstyjsqjl();
                                wlgbJdSstyjsqjl1.setId(wlgbJdSstyjsqjl.getId());
                                wlgbJdSstyjsqjl1.setSflrjd(1);
                                wlgbJdSstyjsqjlService.updateById(wlgbJdSstyjsqjl1);
                            }
                        }
                    } else {
                        sendGztz(0, wlgbJdSstyjsqjl.getSpbh(), "员工缺失，补充错误", "抵预支");
                    }
                } else {
                    sendGztz(0, wlgbJdSstyjsqjl.getSpbh(), "员工缺失，补充错误", "抵预支");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return save;
    }


    /**
     * 借支审批处理
     */
    public boolean jzCl(String spbh, WlgbJdJzspjl wlgbJdJzspjl, Integer type) {
        if (type == 1) {
            wlgbJdJzspjl = wlgbJdJzspjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        Boolean bmCl = KingDeeConfig.bmCl(wlgbJdJzspjl.getSqrbmid(), wlgbJdJzspjl.getSqrbm());
        if (bmCl) {
            Boolean ryCl = KingDeeConfig.ryCl(wlgbJdJzspjl.getSqrid(), wlgbJdJzspjl.getSqr());
            if (ryCl) {
                List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(5, wlgbJdJzspjl.getSpbh(), 0);
                SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                List<String> list2 = new ArrayList<>();
                list.forEach(l -> {
                    if (!list2.contains(l.getZtbm())) {
                        list2.add(l.getZtbm());
                    }
                });
                String sqrbmid = wlgbJdJzspjl.getSqrbmid();
                String cnzzsj = wlgbJdJzspjl.getCnzzsj();
                for (String bm : list2) {
                    List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(5, wlgbJdJzspjl.getSpbh(), 0, bm);
                    String json = "{\n" +
                            "    \"NeedUpDateFields\": [],\n" +
                            "    \"NeedReturnFields\": [],\n" +
                            "    \"IsDeleteEntry\": \"true\",\n" +
                            "    \"SubSystemId\": \"\",\n" +
                            "    \"IsVerifyBaseDataField\": \"false\",\n" +
                            "    \"IsEntryBatchFill\": \"true\",\n" +
                            "    \"ValidateFlag\": \"true\",\n" +
                            "    \"NumberSearch\": \"true\",\n" +
                            "    \"IsAutoAdjustField\": \"false\",\n" +
                            "    \"InterationFlags\": \"\",\n" +
                            "    \"IgnoreInterationFlag\": \"\",\n" +
                            "    \"Model\": {\n" +
                            "        \"FID\": 0,\n" +
                            "        \"FBillTypeID\": {\n" +
                            "            \"FNUMBER\": \"FKDLX02_SYS\"\n" +
                            "        },\n" +
                            "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                            "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                            "        \"FCONTACTUNIT\": {\n" +
                            "            \"FNumber\": \"" + wlgbJdJzspjl.getSqrid() + "\"\n" +
                            "        },\n" +
                            "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                            "        \"FRECTUNIT\": {\n" +
                            "            \"FNumber\": \"" + wlgbJdJzspjl.getSqrid() + "\"\n" +
                            "        },\n" +
                            "        \"FDepartment\": {\n" +
                            "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                            "        },\n" +
                            "        \"FISINIT\": false,\n" +
                            "        \"FCURRENCYID\": {\n" +
                            "            \"FNumber\": \"PRE001\"\n" +
                            "        },\n" +
                            "        \"FEXCHANGERATE\": 1.0,\n" +
                            "        \"FSETTLERATE\": 1.0,\n" +
                            "        \"FSETTLEORGID\": {\n" +
                            "            \"FNumber\": \"" + bm + "\"\n" +
                            "        },\n" +
                            "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                            "        \"FBUSINESSTYPE\": \"3\",\n" +
                            "        \"FCancelStatus\": \"A\",\n" +
                            "        \"FPAYORGID\": {\n" +
                            "            \"FNumber\": \"" + bm + "\"\n" +
                            "        },\n" +
                            "        \"FISSAMEORG\": true,\n" +
                            "        \"FIsCredit\": false,\n" +
                            "        \"FSETTLECUR\": {\n" +
                            "            \"FNUMBER\": \"PRE001\"\n" +
                            "        },\n" +
                            "        \"FIsWriteOff\": false,\n" +
                            "        \"FREALPAY\": false,\n" +
                            "        \"FISCARRYRATE\": false,\n" +
                            "        \"FSETTLEMAINBOOKID\": {\n" +
                            "            \"FNUMBER\": \"PRE001\"\n" +
                            "        },\n" +
                            "        \"FMoreReceive\": false,\n" +
                            "        \"FVirIsSameAcctOrg\": false,\n" +
                            "        \"FREMARK\": \"" + wlgbJdJzspjl.getJzyt() + "\",\n" +
                            "        \"FSPBT\": \"" + wlgbJdJzspjl.getSpbt() + "\",\n" +
                            "        \"FSPBH\": \"" + wlgbJdJzspjl.getSpbh() + "\",\n" +
                            "        \"FSPSQR\": \"" + wlgbJdJzspjl.getSqr() + "\",\n" +
                            "        \"FFYSYF\": \"" + wlgbJdJzspjl.getJzsyf() + "\",\n" +
                            "        \"FSPSQRBM\": \"" + wlgbJdJzspjl.getSqrbm() + "\",\n";
                    WlgbJdYhk wlgbJdYhk = yhkCl(bm);
                    List<JSONObject> list1 = new ArrayList<>();
                    list3.forEach(l -> {
                        JSONObject jsonObject = fkdMx1("", l.getFyje(), cnzzsj, l.getFykmbm(), sqrbmid, "SFKYT11_SYS", wlgbJdYhk.getZh());

                        list1.add(jsonObject);
                    });
                    json += "        \"FPAYBILLENTRY\": " + list1.toString() +
                            "    }\n" +
                            "}";
                    //录入金蝶
                    Map<String, String> map15 = KingDeeConfig.spTjJdDj2(json, "付款单");
                    save = "true".equals(map15.get("jg"));
                    if (!save) {
                        sendGztz(0, wlgbJdJzspjl.getSpbh(), "录入金蝶失败,原因：" + map15.get("msg"), "付款单");
                    }
                    if (save) {
                        //上传单据成功之后将里面数据改成上传完成
                        list3.forEach(l -> {
                            WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                            wlgbJdSpCwlrjdsj.setId(l.getId());
                            wlgbJdSpCwlrjdsj.setSfscjd(1);
                            wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                        });
                    }
                }
                if (save) {
                    if (type == 1) {
                        WlgbJdJzspjl wlgbJdJzspjl1 = new WlgbJdJzspjl();
                        wlgbJdJzspjl1.setId(wlgbJdJzspjl.getId());
                        wlgbJdJzspjl1.setSflrjd(1);
                        wlgbJdJzspjlService.updateById(wlgbJdJzspjl1);
                    }
                }
            } else {
                sendGztz(0, wlgbJdJzspjl.getSpbh(), "员工缺失，补充错误", "借支审批");
            }
        } else {
            sendGztz(0, wlgbJdJzspjl.getSpbh(), "部门缺失，补充错误", "借支审批");
        }

        return save;
    }


    /**
     * 平台刷单款报销审批处理
     */
    public boolean ptSdkBxCl(String spbh, WlgbJdPtsdkbxsqjl wlgbJdPtsdkbxsqjl, Integer type) {
        if (type == 1) {
            wlgbJdPtsdkbxsqjl = wlgbJdPtsdkbxsqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdPtsdkbxsqjl != null) {
            String sqrbmid = wlgbJdPtsdkbxsqjl.getSqrbmid();
            Boolean bmCl = KingDeeConfig.bmCl(sqrbmid, wlgbJdPtsdkbxsqjl.getSqrbm());
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(wlgbJdPtsdkbxsqjl.getSqrid(), wlgbJdPtsdkbxsqjl.getSqr());
                if (ryCl) {
                    List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(9, wlgbJdPtsdkbxsqjl.getSpbh(), 0);
                    List<String> list2 = new ArrayList<>();
                    list.forEach(l -> {
                        if (!list2.contains(l.getZtbm())) {
                            list2.add(l.getZtbm());
                        }
                    });
                    Double je = 0.0;
                    boolean sfXyb = true;
                    String cnzzsj = wlgbJdPtsdkbxsqjl.getCnzzsj();
                    for (int p = 0; p < list2.size(); p++) {
                        if (sfXyb) {
                            String bm = list2.get(p);
                            List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(9, wlgbJdPtsdkbxsqjl.getSpbh(), 0, bm);
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("spbh", wlgbJdPtsdkbxsqjl.getSpbh());
                            jsonObject.put("fyssft", wlgbJdPtsdkbxsqjl.getFyssft());
                            jsonObject.put("sfsymdft", wlgbJdPtsdkbxsqjl.getSfsymdpt());
                            jsonObject.put("cnzzsj", cnzzsj);
                            jsonObject.put("djFyZje", wlgbJdPtsdkbxsqjl.getBcbxzje());
                            JSONObject jsonObject1 = ft1(list2, jsonObject, 9, p, je, list3, wlgbJdPtsdkbxsqjl.getSqsj(), sqrbmid, wlgbJdPtsdkbxsqjl.getSfxft() != null ? wlgbJdPtsdkbxsqjl.getSfxft() : 0);
                            sfXyb = jsonObject1.getBoolean("success");
                            if (sfXyb) {
                                je = jsonObject1.getDouble("je");
                                JSONArray jsonArray = jsonObject1.getJSONArray("list");
                                List<JSONObject> list1 = JSONObject.parseArray(jsonArray.toJSONString(), JSONObject.class);
                                String json = "{\n" +
                                        "    \"NeedUpDateFields\": [],\n" +
                                        "    \"NeedReturnFields\": [],\n" +
                                        "    \"IsDeleteEntry\": \"true\",\n" +
                                        "    \"SubSystemId\": \"\",\n" +
                                        "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                        "    \"IsEntryBatchFill\": \"true\",\n" +
                                        "    \"ValidateFlag\": \"true\",\n" +
                                        "    \"NumberSearch\": \"true\",\n" +
                                        "    \"IsAutoAdjustField\": \"false\",\n" +
                                        "    \"InterationFlags\": \"\",\n" +
                                        "    \"IgnoreInterationFlag\": \"\",\n" +
                                        "    \"Model\": {\n" +
                                        "        \"FID\": 0,\n" +
                                        "        \"FBillTypeID\": {\n" +
                                        "            \"FNUMBER\": \"QTYFD01_SYS\"\n" +
                                        "        },\n" +
                                        "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                        "        \"FENDDATE_H\": \"" + cnzzsj + "\",\n" +
                                        "        \"FISINIT\": false,\n" +
                                        "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                        "        \"FCONTACTUNIT\": {\n" +
                                        "            \"FNumber\": \"" + wlgbJdPtsdkbxsqjl.getSqrid() + "\"\n" +
                                        "        },\n" +
                                        "        \"FCURRENCYID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FDEPARTMENTID\": {\n" +
                                        "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                        "        },\n" +
                                        "        \"FSETTLEORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FPURCHASEORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FPAYORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FMAINBOOKSTDCURRID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FEXCHANGETYPE\": {\n" +
                                        "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                        "        },\n" +
                                        "        \"FExchangeRate\": 1.0,\n" +
                                        "        \"FACCNTTIMEJUDGETIME\": \"" + cnzzsj + "\",\n" +
                                        "        \"FCancelStatus\": \"A\",\n" +
                                        "        \"FBUSINESSTYPE\": \"T\",\n" +
                                        "        \"FRemarks\": \"" + wlgbJdPtsdkbxsqjl.getFyzymx() + "\",\n" +
                                        "        \"FSPSLBT\": \"" + wlgbJdPtsdkbxsqjl.getSpbt() + "\",\n" +
                                        "        \"FSPBH\": \"" + wlgbJdPtsdkbxsqjl.getSpbh() + "\",\n" +
                                        "        \"FSPSQR\": \"" + wlgbJdPtsdkbxsqjl.getSqr() + "\",\n" +
                                        "        \"FFYSYF\": \"" + wlgbJdPtsdkbxsqjl.getFysyf() + "\",\n" +
                                        "        \"FSPSQRBM\": \"" + wlgbJdPtsdkbxsqjl.getSqrbm() + "\",\n";

                                json += "        \"FEntity\": " + list1.toString() +
                                        "    }\n" +
                                        "}";
                                Map<String, String> map14 = KingDeeConfig.spTjJdDj2(json, "其他应付单");
                                save = "true".equals(map14.get("jg"));
                                if (!save) {
                                    sendGztz(0, wlgbJdPtsdkbxsqjl.getSpbh(), "录入金蝶失败,原因：" + map14.get("msg"), "其他应付单");
                                }
                                if (save) {
                                    String json1 = "{\n" +
                                            "    \"NeedUpDateFields\": [],\n" +
                                            "    \"NeedReturnFields\": [],\n" +
                                            "    \"IsDeleteEntry\": \"true\",\n" +
                                            "    \"SubSystemId\": \"\",\n" +
                                            "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                            "    \"IsEntryBatchFill\": \"true\",\n" +
                                            "    \"ValidateFlag\": \"true\",\n" +
                                            "    \"NumberSearch\": \"true\",\n" +
                                            "    \"IsAutoAdjustField\": \"false\",\n" +
                                            "    \"InterationFlags\": \"\",\n" +
                                            "    \"IgnoreInterationFlag\": \"\",\n" +
                                            "    \"Model\": {\n" +
                                            "        \"FID\": 0,\n" +
                                            "        \"FBillTypeID\": {\n" +
                                            "            \"FNUMBER\": \"FKDLX02_SYS\"\n" +
                                            "        },\n" +
                                            "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                            "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FCONTACTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdPtsdkbxsqjl.getSqrid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FRECTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdPtsdkbxsqjl.getSqrid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FDepartment\": {\n" +
                                            "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                            "        },\n" +
                                            "        \"FISINIT\": false,\n" +
                                            "        \"FCURRENCYID\": {\n" +
                                            "            \"FNumber\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FEXCHANGERATE\": 1.0,\n" +
                                            "        \"FSETTLERATE\": 1.0,\n" +
                                            "        \"FSETTLEORGID\": {\n" +
                                            "            \"FNumber\": \"" + bm + "\"\n" +
                                            "        },\n" +
                                            "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                            "        \"FBUSINESSTYPE\": \"3\",\n" +
                                            "        \"FCancelStatus\": \"A\",\n" +
                                            "        \"FPAYORGID\": {\n" +
                                            "            \"FNumber\": \"" + bm + "\"\n" +
                                            "        },\n" +
                                            "        \"FISSAMEORG\": true,\n" +
                                            "        \"FIsCredit\": false,\n" +
                                            "        \"FSETTLECUR\": {\n" +
                                            "            \"FNUMBER\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FIsWriteOff\": false,\n" +
                                            "        \"FREALPAY\": false,\n" +
                                            "        \"FISCARRYRATE\": false,\n" +
                                            "        \"FSETTLEMAINBOOKID\": {\n" +
                                            "            \"FNUMBER\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FMoreReceive\": false,\n" +
                                            "        \"FVirIsSameAcctOrg\": false,\n" +
                                            "        \"FREMARK\": \"" + wlgbJdPtsdkbxsqjl.getFyzymx() + "\",\n" +
                                            "        \"FSPBT\": \"" + wlgbJdPtsdkbxsqjl.getSpbt() + "\",\n" +
                                            "        \"FSPBH\": \"" + wlgbJdPtsdkbxsqjl.getSpbh() + "\",\n" +
                                            "        \"FSPSQR\": \"" + wlgbJdPtsdkbxsqjl.getSqr() + "\",\n" +
                                            "        \"FFYSYF\": \"" + wlgbJdPtsdkbxsqjl.getFysyf() + "\",\n" +
                                            "        \"FSPSQRBM\": \"" + wlgbJdPtsdkbxsqjl.getSqrbm() + "\",\n";
                                    WlgbJdYhk wlgbJdYhk = yhkCl(bm);
                                    List<JSONObject> list4 = new ArrayList<>();
                                    list1.forEach(l -> {
                                        String fcostid = l.getJSONObject("FCOSTID").getString("FNumber");
                                        String fssmd = l.getJSONObject("FSSMD").getString("FNumber");
                                        Double fnotaxamountfor = l.getDouble("FNOTAXAMOUNTFOR");
                                        JSONObject jsonObject2 = fkdMx1(fssmd, fnotaxamountfor, cnzzsj, fcostid, sqrbmid, "SFKYT002_SYS", wlgbJdYhk.getZh());

                                        list4.add(jsonObject2);
                                    });
                                    json1 += "        \"FPAYBILLENTRY\": " + list4.toString() +
                                            "    }\n" +
                                            "}";
                                    //录入金蝶
                                    Map<String, String> map13 = KingDeeConfig.spTjJdDj2(json1, "付款单");
                                    save = "true".equals(map13.get("jg"));
                                    if (!save) {
                                        sendGztz(0, wlgbJdPtsdkbxsqjl.getSpbh(), "录入金蝶失败,原因：" + map13.get("msg"), "付款单");
                                    }
                                }
                                if (save) {
                                    //上传单据成功之后将里面数据改成上传完成
                                    list3.forEach(l -> {
                                        WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                        wlgbJdSpCwlrjdsj.setId(l.getId());
                                        wlgbJdSpCwlrjdsj.setSfscjd(1);
                                        wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                                    });
                                }
                            }
                            if (!sfXyb) {
                                sendGztz(0, wlgbJdPtsdkbxsqjl.getSpbh(), "别墅缺失", "平台刷单款报销");
                            }
                            if (save) {
                                if (type == 1) {
                                    WlgbJdPtsdkbxsqjl wlgbJdPtsdkbxsqjl1 = new WlgbJdPtsdkbxsqjl();
                                    wlgbJdPtsdkbxsqjl1.setId(wlgbJdPtsdkbxsqjl.getId());
                                    wlgbJdPtsdkbxsqjl1.setSflrjd(1);
                                    wlgbJdPtsdkbxsqjlService.updateById(wlgbJdPtsdkbxsqjl1);
                                }
                            }
                        }
                    }
                } else {
                    sendGztz(0, wlgbJdPtsdkbxsqjl.getSpbh(), "员工缺失，补充错误", "平台刷单款报销审批");
                }
            } else {
                sendGztz(0, wlgbJdPtsdkbxsqjl.getSpbh(), "部门缺失，补充错误", "平台刷单款报销审批");
            }
        }
        return save;
    }


    /**
     * 乐诚项目报销审批处理
     *
     * @param spbh             审批编号
     * @param wlgbJdLcxmbxsqjl 乐诚项目报销审批数据
     * @param type             0未在数据库，1数据库有
     */
    public boolean lcXmBxCl(String spbh, WlgbJdLcxmbxsqjl wlgbJdLcxmbxsqjl, Integer type) {
        if (type == 1) {
            wlgbJdLcxmbxsqjl = wlgbJdLcxmbxsqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdLcxmbxsqjl != null) {
            String sqrbmid = wlgbJdLcxmbxsqjl.getSqrbmid();
            String sqrid = wlgbJdLcxmbxsqjl.getSqrid();
            String cnzzsj = wlgbJdLcxmbxsqjl.getCnzzsj();
            Boolean bmCl = KingDeeConfig.bmCl(sqrbmid, wlgbJdLcxmbxsqjl.getSqrbm());
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(sqrid, wlgbJdLcxmbxsqjl.getSqr());
                if (ryCl) {
                    List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(13, wlgbJdLcxmbxsqjl.getSpbh(), 0);
                    List<String> list2 = new ArrayList<>();
                    list.forEach(l -> {
                        if (!list2.contains(l.getZtbm())) {
                            list2.add(l.getZtbm());
                        }
                    });
                    for (String bm : list2) {
                        List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(13, wlgbJdLcxmbxsqjl.getSpbh(), 0, bm);
                        List<JSONObject> list1 = new ArrayList<>();
                        list3.forEach(l -> {
                            JSONObject jsonObject = scQtYfdMx(null, l.getFykmbm(), l.getFyje(), sqrbmid);
                            list1.add(jsonObject);
                        });
                        String json = "{\n" +
                                "    \"NeedUpDateFields\": [],\n" +
                                "    \"NeedReturnFields\": [],\n" +
                                "    \"IsDeleteEntry\": \"true\",\n" +
                                "    \"SubSystemId\": \"\",\n" +
                                "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                "    \"IsEntryBatchFill\": \"true\",\n" +
                                "    \"ValidateFlag\": \"true\",\n" +
                                "    \"NumberSearch\": \"true\",\n" +
                                "    \"IsAutoAdjustField\": \"false\",\n" +
                                "    \"InterationFlags\": \"\",\n" +
                                "    \"IgnoreInterationFlag\": \"\",\n" +
                                "    \"Model\": {\n" +
                                "        \"FID\": 0,\n" +
                                "        \"FBillTypeID\": {\n" +
                                "            \"FNUMBER\": \"QTYFD02_SYS\"\n" +
                                "        },\n" +
                                "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                "        \"FENDDATE_H\": \"" + cnzzsj + "\",\n" +
                                "        \"FISINIT\": false,\n" +
                                "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                "        \"FCONTACTUNIT\": {\n" +
                                "            \"FNumber\": \"" + sqrid + "\"\n" +
                                "        },\n" +
                                "        \"FDEPARTMENTID\": {\n" +
                                "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                "        },\n" +
                                "        \"FCURRENCYID\": {\n" +
                                "            \"FNumber\": \"PRE001\"\n" +
                                "        },\n" +
                                "        \"FSETTLEORGID\": {\n" +
                                "            \"FNumber\": \"" + bm + "\"\n" +
                                "        },\n" +
                                "        \"FPURCHASEORGID\": {\n" +
                                "            \"FNumber\": \"" + bm + "\"\n" +
                                "        },\n" +
                                "        \"FPAYORGID\": {\n" +
                                "            \"FNumber\": \"" + bm + "\"\n" +
                                "        },\n" +
                                "        \"FDepartment\": {\n" +
                                "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                "        },\n" +
                                "        \"FMAINBOOKSTDCURRID\": {\n" +
                                "            \"FNumber\": \"PRE001\"\n" +
                                "        },\n" +
                                "        \"FEXCHANGETYPE\": {\n" +
                                "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                "        },\n" +
                                "        \"FExchangeRate\": 1.0,\n" +
                                "        \"FACCNTTIMEJUDGETIME\": \"" + cnzzsj + "\",\n" +
                                "        \"FCancelStatus\": \"A\",\n" +
                                "        \"FBUSINESSTYPE\": \"T\",\n" +
                                "        \"FRemarks\": \"" + wlgbJdLcxmbxsqjl.getFyfsxmmc() + "\",\n" +
                                "        \"FSPSQR\": \"" + wlgbJdLcxmbxsqjl.getSqr() + "\",\n" +
                                "        \"FFYSYF\": \"" + wlgbJdLcxmbxsqjl.getMdlx() + "\",\n" +
                                "        \"FSPSQRBM\": \"" + wlgbJdLcxmbxsqjl.getSqrbm() + "\",\n" +
                                "        \"FSPSLBT\": \"" + wlgbJdLcxmbxsqjl.getSpbt() + "\",\n" +
                                "        \"FSPBH\": \"" + wlgbJdLcxmbxsqjl.getSpbh() + "\",\n" +
                                "        \"FEntity\": " + list1.toString() +
                                "    }\n" +
                                "}";
                        //录入金蝶
                        Map<String, String> map12 = KingDeeConfig.spTjJdDj2(json, "其他应付单");
                        save = "true".equals(map12.get("jg"));
                        if (!save) {
                            sendGztz(0, wlgbJdLcxmbxsqjl.getSpbh(), "录入金蝶失败,原因：" + map12.get("msg"), "其他应付单");
                        }
                        if (save) {
                            WlgbJdYhk wlgbJdYhk = yhkCl(bm);
                            String json1 = "{\n" +
                                    "    \"NeedUpDateFields\": [],\n" +
                                    "    \"NeedReturnFields\": [],\n" +
                                    "    \"IsDeleteEntry\": \"true\",\n" +
                                    "    \"SubSystemId\": \"\",\n" +
                                    "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                    "    \"IsEntryBatchFill\": \"true\",\n" +
                                    "    \"ValidateFlag\": \"true\",\n" +
                                    "    \"NumberSearch\": \"true\",\n" +
                                    "    \"IsAutoAdjustField\": \"false\",\n" +
                                    "    \"InterationFlags\": \"\",\n" +
                                    "    \"IgnoreInterationFlag\": \"\",\n" +
                                    "    \"Model\": {\n" +
                                    "        \"FID\": 0,\n" +
                                    "        \"FBillTypeID\": {\n" +
                                    "            \"FNUMBER\": \"FKDLX04_SYS\"\n" +
                                    "        },\n" +
                                    "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                    "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                    "        \"FCONTACTUNIT\": {\n" +
                                    "            \"FNumber\": \"" + sqrid + "\"\n" +
                                    "        },\n" +
                                    "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                    "        \"FRECTUNIT\": {\n" +
                                    "            \"FNumber\": \"" + sqrid + "\"\n" +
                                    "        },\n" +
                                    "        \"FDepartment\": {\n" +
                                    "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                    "        },\n" +
                                    "        \"FISINIT\": false,\n" +
                                    "        \"FCURRENCYID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FEXCHANGERATE\": 1.0,\n" +
                                    "        \"FSETTLERATE\": 1.0,\n" +
                                    "        \"FSETTLEORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                    "        \"FBUSINESSTYPE\": \"3\",\n" +
                                    "        \"FCancelStatus\": \"A\",\n" +
                                    "        \"FPAYORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FISSAMEORG\": true,\n" +
                                    "        \"FIsCredit\": false,\n" +
                                    "        \"FSETTLECUR\": {\n" +
                                    "            \"FNUMBER\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FIsWriteOff\": false,\n" +
                                    "        \"FREALPAY\": false,\n" +
                                    "        \"FISCARRYRATE\": false,\n" +
                                    "        \"FSETTLEMAINBOOKID\": {\n" +
                                    "            \"FNUMBER\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FMoreReceive\": false,\n" +
                                    "        \"FVirIsSameAcctOrg\": false,\n" +
                                    "        \"FREMARK\": \"" + wlgbJdLcxmbxsqjl.getFyfsxmmc() + "\",\n" +
                                    "        \"FSPBT\": \"" + wlgbJdLcxmbxsqjl.getSpbt() + "\",\n" +
                                    "        \"FSPBH\": \"" + wlgbJdLcxmbxsqjl.getSpbh() + "\",\n" +
                                    "        \"FSPSQR\": \"" + wlgbJdLcxmbxsqjl.getSqr() + "\",\n" +
                                    "        \"FFYSYF\": \"" + wlgbJdLcxmbxsqjl.getMdlx() + "\",\n" +
                                    "        \"FSPSQRBM\": \"" + wlgbJdLcxmbxsqjl.getSqrbm() + "\",\n";
                            List<JSONObject> list4 = new ArrayList<>();
                            list1.forEach(l -> {
                                String fcostid = l.getJSONObject("FCOSTID").getString("FNumber");
                                String fssmd = l.getJSONObject("FSSMD").getString("FNumber");
                                Double fnotaxamountfor = l.getDouble("FNOTAXAMOUNTFOR");
                                JSONObject jsonObject2 = fkdMx1(fssmd, fnotaxamountfor, cnzzsj, fcostid, sqrbmid, "SFKYT10_SYS", wlgbJdYhk.getZh());

                                list4.add(jsonObject2);
                            });
                            json1 += "        \"FPAYBILLENTRY\": " + list4.toString() +
                                    "    }\n" +
                                    "}";
                            //录入金蝶
                            Map<String, String> map11 = KingDeeConfig.spTjJdDj2(json1, "付款单");
                            save = "true".equals(map11.get("jg"));
                            if (!save) {
                                sendGztz(0, wlgbJdLcxmbxsqjl.getSpbh(), "录入金蝶失败,原因：" + map11.get("msg"), "付款单");
                            }
                        }
                        if (save) {
                            //上传单据成功之后将里面数据改成上传完成
                            list3.forEach(l -> {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                wlgbJdSpCwlrjdsj.setId(l.getId());
                                wlgbJdSpCwlrjdsj.setSfscjd(1);
                                wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                            });
                        }
                    }
                    if (save) {
                        if (type == 1) {
                            WlgbJdLcxmbxsqjl wlgbJdLcxmbxsqjl1 = new WlgbJdLcxmbxsqjl();
                            wlgbJdLcxmbxsqjl1.setId(wlgbJdLcxmbxsqjl.getId());
                            wlgbJdLcxmbxsqjl1.setSflrjd(1);
                            wlgbJdLcxmbxsqjlService.updateById(wlgbJdLcxmbxsqjl1);
                        }
                    }
                }
            }
        }


        return save;
    }


    /**
     * 乐诚项目采购费用付款审批处理
     */
    public boolean lcXmCgFyFkCl(String spbh, WlgbJdLcxmcgdfksqjl wlgbJdLcxmcgdfksqjl, Integer type) {
        if (type == 1) {
            wlgbJdLcxmcgdfksqjl = wlgbJdLcxmcgdfksqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdLcxmcgdfksqjl != null) {
            String sqrbmid = wlgbJdLcxmcgdfksqjl.getSqrbmid();
            String sqrid = wlgbJdLcxmcgdfksqjl.getSqrid();
            Boolean bmCl = KingDeeConfig.bmCl(sqrbmid, wlgbJdLcxmcgdfksqjl.getSqrbm());
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(sqrid, wlgbJdLcxmcgdfksqjl.getSqr());
                if (ryCl) {
                    List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(14, wlgbJdLcxmcgdfksqjl.getSpbh(), 0);
                    List<String> list2 = new ArrayList<>();
                    list.forEach(l -> {
                        if (!list2.contains(l.getZtbm())) {
                            list2.add(l.getZtbm());
                        }
                    });
                    String cnzzsj = wlgbJdLcxmcgdfksqjl.getCnzzsj();
                    boolean b2 = true;
                    String md = null;
                    String bsmc = wlgbJdLcxmcgdfksqjl.getMdmc();
                    if (bsmc != null && !"".equals(bsmc)) {
                        //查询别墅表存不存在
                        TbVilla tbVilla = weiLianService.queryVillaByVname(bsmc);
                        //如果不存在
                        if (tbVilla == null) {
                            //去别墅修改记录表查询
                            WlgbJdBsmcXgjl wlgbJdBsmcXgjl = wlgbJdBsmcXgjlService.queryByYnameAndSfSc(bsmc, 0);
                            //别墅修改记录表存在
                            if (wlgbJdBsmcXgjl != null) {
                                //把别墅名字给上传使用
                                bsmc = wlgbJdBsmcXgjl.getVname();
                            }
                        }
                        JSONObject jsonObject = KingDeeConfig.queryBsSfCz(bsmc);
                        b2 = jsonObject.getBoolean("success");
                        md = jsonObject.getString("bm");
                    }
                    if (b2) {
                        for (String bm : list2) {
                            List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(14, wlgbJdLcxmcgdfksqjl.getSpbh(), 0, bm);
                            List<JSONObject> list1 = new ArrayList<>();
                            String finalMd = md;
                            list3.forEach(l -> {
                                JSONObject jsonObject = scQtYfdMx(finalMd, l.getFykmbm(), l.getFyje(), sqrbmid);
                                list1.add(jsonObject);
                            });
                            String json = "{\n" +
                                    "    \"NeedUpDateFields\": [],\n" +
                                    "    \"NeedReturnFields\": [],\n" +
                                    "    \"IsDeleteEntry\": \"true\",\n" +
                                    "    \"SubSystemId\": \"\",\n" +
                                    "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                    "    \"IsEntryBatchFill\": \"true\",\n" +
                                    "    \"ValidateFlag\": \"true\",\n" +
                                    "    \"NumberSearch\": \"true\",\n" +
                                    "    \"IsAutoAdjustField\": \"false\",\n" +
                                    "    \"InterationFlags\": \"\",\n" +
                                    "    \"IgnoreInterationFlag\": \"\",\n" +
                                    "    \"Model\": {\n" +
                                    "        \"FID\": 0,\n" +
                                    "        \"FBillTypeID\": {\n" +
                                    "            \"FNUMBER\": \"QTYFD01_SYS\"\n" +
                                    "        },\n" +
                                    "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                    "        \"FENDDATE_H\": \"" + cnzzsj + "\",\n" +
                                    "        \"FISINIT\": false,\n" +
                                    "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                    "        \"FCONTACTUNIT\": {\n" +
                                    "            \"FNumber\": \"" + sqrid + "\"\n" +
                                    "        },\n" +
                                    "        \"FCURRENCYID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FDEPARTMENTID\": {\n" +
                                    "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                    "        },\n" +
                                    "        \"FSETTLEORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FPURCHASEORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FPAYORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FMAINBOOKSTDCURRID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FEXCHANGETYPE\": {\n" +
                                    "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                    "        },\n" +
                                    "        \"FExchangeRate\": 1.0,\n" +
                                    "        \"FACCNTTIMEJUDGETIME\": \"" + cnzzsj + "\",\n" +
                                    "        \"FCancelStatus\": \"A\",\n" +
                                    "        \"FBUSINESSTYPE\": \"T\",\n" +
                                    "        \"FSPSLBT\": \"" + wlgbJdLcxmcgdfksqjl.getSpbt() + "\",\n" +
                                    "        \"FSPBH\": \"" + wlgbJdLcxmcgdfksqjl.getSpbh() + "\",\n" +
                                    "        \"FSPSQR\": \"" + wlgbJdLcxmcgdfksqjl.getSqr() + "\",\n" +
                                    "        \"FFYSYF\": \"" + wlgbJdLcxmcgdfksqjl.getMdlx() + "\",\n" +
                                    "        \"FSPSQRBM\": \"" + wlgbJdLcxmcgdfksqjl.getSqrbm() + "\",\n";
                            json += "        \"FEntity\": " + list1.toString() +
                                    "    }\n" +
                                    "}";
                            //录入金蝶
                            Map<String, String> map10 = KingDeeConfig.spTjJdDj2(json, "其他应付单");
                            save = "true".equals(map10.get("jg"));
                            if (!save) {
                                sendGztz(0, wlgbJdLcxmcgdfksqjl.getSpbh(), "录入金蝶失败,原因：" + map10.get("msg"), "其他应付单");
                            }
                            if (save) {
                                String json1 = "{\n" +
                                        "    \"NeedUpDateFields\": [],\n" +
                                        "    \"NeedReturnFields\": [],\n" +
                                        "    \"IsDeleteEntry\": \"true\",\n" +
                                        "    \"SubSystemId\": \"\",\n" +
                                        "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                        "    \"IsEntryBatchFill\": \"true\",\n" +
                                        "    \"ValidateFlag\": \"true\",\n" +
                                        "    \"NumberSearch\": \"true\",\n" +
                                        "    \"IsAutoAdjustField\": \"false\",\n" +
                                        "    \"InterationFlags\": \"\",\n" +
                                        "    \"IgnoreInterationFlag\": \"\",\n" +
                                        "    \"Model\": {\n" +
                                        "        \"FID\": 0,\n" +
                                        "        \"FBillTypeID\": {\n" +
                                        "            \"FNUMBER\": \"FKDLX02_SYS\"\n" +
                                        "        },\n" +
                                        "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                        "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                        "        \"FCONTACTUNIT\": {\n" +
                                        "            \"FNumber\": \"" + sqrid + "\"\n" +
                                        "        },\n" +
                                        "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                        "        \"FRECTUNIT\": {\n" +
                                        "            \"FNumber\": \"" + sqrid + "\"\n" +
                                        "        },\n" +
                                        "        \"FDepartment\": {\n" +
                                        "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                        "        },\n" +
                                        "        \"FISINIT\": false,\n" +
                                        "        \"FCURRENCYID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FEXCHANGERATE\": 1.0,\n" +
                                        "        \"FSETTLERATE\": 1.0,\n" +
                                        "        \"FSETTLEORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                        "        \"FBUSINESSTYPE\": \"3\",\n" +
                                        "        \"FCancelStatus\": \"A\",\n" +
                                        "        \"FPAYORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FISSAMEORG\": true,\n" +
                                        "        \"FIsCredit\": false,\n" +
                                        "        \"FSETTLECUR\": {\n" +
                                        "            \"FNUMBER\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FIsWriteOff\": false,\n" +
                                        "        \"FREALPAY\": false,\n" +
                                        "        \"FISCARRYRATE\": false,\n" +
                                        "        \"FSETTLEMAINBOOKID\": {\n" +
                                        "            \"FNUMBER\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FMoreReceive\": false,\n" +
                                        "        \"FVirIsSameAcctOrg\": false,\n" +
                                        "        \"FSPBT\": \"" + wlgbJdLcxmcgdfksqjl.getSpbt() + "\",\n" +
                                        "        \"FSPBH\": \"" + wlgbJdLcxmcgdfksqjl.getSpbh() + "\",\n" +
                                        "        \"FSPSQR\": \"" + wlgbJdLcxmcgdfksqjl.getSqr() + "\",\n" +
                                        "        \"FSPSQRBM\": \"" + wlgbJdLcxmcgdfksqjl.getSqrbm() + "\",\n" +
                                        "        \"FFYSYF\": \"" + wlgbJdLcxmcgdfksqjl.getMdlx() + "\",\n";
                                WlgbJdYhk wlgbJdYhk = yhkCl(bm);
                                List<JSONObject> list4 = new ArrayList<>();
                                list1.forEach(l -> {
                                    String fcostid = l.getJSONObject("FCOSTID").getString("FNumber");
                                    String fssmd = l.getJSONObject("FSSMD").getString("FNumber");
                                    Double fnotaxamountfor = l.getDouble("FNOTAXAMOUNTFOR");
                                    JSONObject jsonObject2 = fkdMx1(fssmd, fnotaxamountfor, cnzzsj, fcostid, sqrbmid, "SFKYT20_SYS", wlgbJdYhk.getZh());

                                    list4.add(jsonObject2);
                                });
                                json1 += "        \"FPAYBILLENTRY\": " + list4.toString() +
                                        "    }\n" +
                                        "}";
                                //录入金蝶
                                Map<String, String> map9 = KingDeeConfig.spTjJdDj2(json1, "付款单");
                                save = "true".equals(map9.get("jg"));
                                if (!save) {
                                    sendGztz(0, wlgbJdLcxmcgdfksqjl.getSpbh(), "录入金蝶失败,原因：" + map9.get("msg"), "付款单");
                                }
                            }
                            if (save) {
                                //上传单据成功之后将里面数据改成上传完成
                                list3.forEach(l -> {
                                    WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                    wlgbJdSpCwlrjdsj.setId(l.getId());
                                    wlgbJdSpCwlrjdsj.setSfscjd(1);
                                    wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                                });
                            }
                        }
                        if (save) {
                            if (type == 1) {
                                WlgbJdLcxmcgdfksqjl wlgbJdLcxmcgdfksqjl1 = new WlgbJdLcxmcgdfksqjl();
                                wlgbJdLcxmcgdfksqjl1.setId(wlgbJdLcxmcgdfksqjl.getId());
                                wlgbJdLcxmcgdfksqjl1.setSflrjd(1);
                                wlgbJdLcxmcgdfksqjlService.updateById(wlgbJdLcxmcgdfksqjl1);
                            }
                        }
                    } else {
                        sendGztz(0, wlgbJdLcxmcgdfksqjl.getSpbh(), "门店不存在", "乐诚项目采购费用付款");
                    }
                } else {
                    sendGztz(0, wlgbJdLcxmcgdfksqjl.getSpbh(), "员工缺失，补充错误", "乐诚项目采购费用付款");
                }
            } else {
                sendGztz(0, wlgbJdLcxmcgdfksqjl.getSpbh(), "部门缺失，补充错误", "乐诚项目采购费用付款");
            }
        }


        return save;
    }


    /**
     * 乐诚项目预支审批处理
     */
    public boolean lcXmYzCl(String spbh, WlgbJdLcxmyzsqjl wlgbJdLcxmyzsqjl, Integer type) {
        if (type == 1) {
            wlgbJdLcxmyzsqjl = wlgbJdLcxmyzsqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdLcxmyzsqjl != null) {
            String sqrbmid = wlgbJdLcxmyzsqjl.getSqrbmid();
            String sqrid = wlgbJdLcxmyzsqjl.getSqrid();
            Boolean bmCl = KingDeeConfig.bmCl(sqrbmid, wlgbJdLcxmyzsqjl.getSqrbm());
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(sqrid, wlgbJdLcxmyzsqjl.getSqr());
                if (ryCl) {
                    List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(15, wlgbJdLcxmyzsqjl.getSpbh(), 0);
                    List<String> list2 = new ArrayList<>();
                    list.forEach(l -> {
                        if (!list2.contains(l.getZtbm())) {
                            list2.add(l.getZtbm());
                        }
                    });
                    String cnzzsj = wlgbJdLcxmyzsqjl.getCnzzsj();
                    for (String bm : list2) {
                        List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(15, wlgbJdLcxmyzsqjl.getSpbh(), 0, bm);
                        String json = "{\n" +
                                "    \"NeedUpDateFields\": [],\n" +
                                "    \"NeedReturnFields\": [],\n" +
                                "    \"IsDeleteEntry\": \"true\",\n" +
                                "    \"SubSystemId\": \"\",\n" +
                                "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                "    \"IsEntryBatchFill\": \"true\",\n" +
                                "    \"ValidateFlag\": \"true\",\n" +
                                "    \"NumberSearch\": \"true\",\n" +
                                "    \"IsAutoAdjustField\": \"false\",\n" +
                                "    \"InterationFlags\": \"\",\n" +
                                "    \"IgnoreInterationFlag\": \"\",\n" +
                                "    \"Model\": {\n" +
                                "        \"FID\": 0,\n" +
                                "        \"FBillTypeID\": {\n" +
                                "            \"FNUMBER\": \"FKDLX02_SYS\"\n" +
                                "        },\n" +
                                "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                "        \"FCONTACTUNIT\": {\n" +
                                "            \"FNumber\": \"" + sqrid + "\"\n" +
                                "        },\n" +
                                "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                "        \"FRECTUNIT\": {\n" +
                                "            \"FNumber\": \"" + sqrid + "\"\n" +
                                "        },\n" +
                                "        \"FDepartment\": {\n" +
                                "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                "        },\n" +
                                "        \"FISINIT\": false,\n" +
                                "        \"FCURRENCYID\": {\n" +
                                "            \"FNumber\": \"PRE001\"\n" +
                                "        },\n" +
                                "        \"FEXCHANGERATE\": 1.0,\n" +
                                "        \"FSETTLERATE\": 1.0,\n" +
                                "        \"FSETTLEORGID\": {\n" +
                                "            \"FNumber\": \"" + bm + "\"\n" +
                                "        },\n" +
                                "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                "        \"FBUSINESSTYPE\": \"3\",\n" +
                                "        \"FCancelStatus\": \"A\",\n" +
                                "        \"FPAYORGID\": {\n" +
                                "            \"FNumber\": \"" + bm + "\"\n" +
                                "        },\n" +
                                "        \"FISSAMEORG\": true,\n" +
                                "        \"FIsCredit\": false,\n" +
                                "        \"FSETTLECUR\": {\n" +
                                "            \"FNUMBER\": \"PRE001\"\n" +
                                "        },\n" +
                                "        \"FIsWriteOff\": false,\n" +
                                "        \"FREALPAY\": false,\n" +
                                "        \"FISCARRYRATE\": false,\n" +
                                "        \"FSETTLEMAINBOOKID\": {\n" +
                                "            \"FNUMBER\": \"PRE001\"\n" +
                                "        },\n" +
                                "        \"FMoreReceive\": false,\n" +
                                "        \"FVirIsSameAcctOrg\": false,\n" +
                                "        \"FREMARK\": \"" + wlgbJdLcxmyzsqjl.getYzkyt() + "\",\n" +
                                "        \"FSPBT\": \"" + wlgbJdLcxmyzsqjl.getSpbt() + "\",\n" +
                                "        \"FSPBH\": \"" + wlgbJdLcxmyzsqjl.getSpbh() + "\",\n" +
                                "        \"FSPSQR\": \"" + wlgbJdLcxmyzsqjl.getSqr() + "\",\n" +
                                "        \"FSPSQRBM\": \"" + wlgbJdLcxmyzsqjl.getSqrbm() + "\",\n" +
                                "        \"FFYSYF\": \"" + wlgbJdLcxmyzsqjl.getMdlx() + "\",\n";
                        WlgbJdYhk wlgbJdYhk = yhkCl(bm);
                        List<JSONObject> list1 = new ArrayList<>();
                        list3.forEach(l -> {
                            JSONObject jsonObject = new JSONObject();
                            //电汇
                            jsonObject.put("FSETTLETYPEID", KingDeeConfig.zhdx("JSFS04_SYS"));
                            //银行卡账号
                            jsonObject.put("FACCOUNTID", KingDeeConfig.zhdx(wlgbJdYhk.getZh()));
                            jsonObject.put("FPURPOSEID", KingDeeConfig.zhdx("SFKYT42_SYS"));
                            jsonObject.put("FPAYTOTALAMOUNTFOR", l.getFyje());
                            jsonObject.put("FPAYAMOUNTFOR_E", l.getFyje());
                            jsonObject.put("FSETTLEPAYAMOUNTFOR", l.getFyje());
                            jsonObject.put("FRecType", "1");
                            jsonObject.put("FCOSTID", KingDeeConfig.zhdx(l.getFykmbm()));
                            //部门
                            jsonObject.put("FEXPENSEDEPTID_E", KingDeeConfig.zhdx(sqrbmid));
                            jsonObject.put("FPAYAMOUNT_E", l.getFyje());
                            jsonObject.put("FPOSTDATE", cnzzsj);
                            jsonObject.put("FRuZhangType", "1");
                            jsonObject.put("FPayType", "A");
                            jsonObject.put("FNOTVERIFICATEAMOUNT", l.getFyje());
                            jsonObject.put("FBankInvoice", false);
                            jsonObject.put("FByAgentBank", false);
                            jsonObject.put("FOverseaPay", false);
                            list1.add(jsonObject);
                        });
                        json += "        \"FPAYBILLENTRY\": " + list1.toString() +
                                "    }\n" +
                                "}";
                        //录入金蝶
                        Map<String, String> map8 = KingDeeConfig.spTjJdDj2(json, "付款单");
                        save = "true".equals(map8.get("jg"));
                        if (!save) {
                            sendGztz(0, wlgbJdLcxmyzsqjl.getSpbh(), "录入金蝶失败,原因：" + map8.get("msg"), "付款单");
                        }
                        if (save) {
                            //上传单据成功之后将里面数据改成上传完成
                            list3.forEach(l -> {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                wlgbJdSpCwlrjdsj.setId(l.getId());
                                wlgbJdSpCwlrjdsj.setSfscjd(1);
                                wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                            });
                        }
                    }
                    if (save) {
                        if (type == 1) {
                            WlgbJdLcxmyzsqjl wlgbJdLcxmyzsqjl1 = new WlgbJdLcxmyzsqjl();
                            wlgbJdLcxmyzsqjl1.setId(wlgbJdLcxmyzsqjl.getId());
                            wlgbJdLcxmyzsqjl1.setSflrjd(1);
                            wlgbJdLcxmyzsqjlService.updateById(wlgbJdLcxmyzsqjl1);
                        }
                    }
                } else {
                    sendGztz(0, wlgbJdLcxmyzsqjl.getSpbh(), "员工缺失，补充错误", "乐诚项目采购费用付款");
                }
            } else {
                sendGztz(0, wlgbJdLcxmyzsqjl.getSpbh(), "部门缺失，补充错误", "乐诚项目采购费用付款");
            }
        }


        return save;
    }


    /**
     * 乐诚项目抵预支处理
     */
    public boolean lcXmDyzCl(String spbh, WlgbJdLcxmdyzsqjl wlgbJdLcxmdyzsqjl, Integer type) {
        if (type == 1) {
            wlgbJdLcxmdyzsqjl = wlgbJdLcxmdyzsqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdLcxmdyzsqjl != null) {
            try {
                String sqrbmid = wlgbJdLcxmdyzsqjl.getSqrbmid();
                String sqrid = wlgbJdLcxmdyzsqjl.getSqrid();
                SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String spjssj = df2.format(wlgbJdLcxmdyzsqjl.getSpjssj());
                Boolean bmCl = KingDeeConfig.bmCl(sqrbmid, wlgbJdLcxmdyzsqjl.getSqrbm());
                if (bmCl) {
                    Boolean ryCl = KingDeeConfig.ryCl(sqrid, wlgbJdLcxmdyzsqjl.getSqr());
                    if (ryCl) {
                        List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(16, wlgbJdLcxmdyzsqjl.getSpbh(), 0);
                        List<String> list2 = new ArrayList<>();
                        list.forEach(l -> {
                            if (!list2.contains(l.getZtbm())) {
                                list2.add(l.getZtbm());
                            }
                        });
                        for (String bm : list2) {
                            List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(16, wlgbJdLcxmdyzsqjl.getSpbh(), 0, bm);
                            String json = "{\n" +
                                    "    \"NeedUpDateFields\": [],\n" +
                                    "    \"NeedReturnFields\": [],\n" +
                                    "    \"IsDeleteEntry\": \"true\",\n" +
                                    "    \"SubSystemId\": \"\",\n" +
                                    "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                    "    \"IsEntryBatchFill\": \"true\",\n" +
                                    "    \"ValidateFlag\": \"true\",\n" +
                                    "    \"NumberSearch\": \"true\",\n" +
                                    "    \"IsAutoAdjustField\": \"false\",\n" +
                                    "    \"InterationFlags\": \"\",\n" +
                                    "    \"IgnoreInterationFlag\": \"\",\n" +
                                    "    \"Model\": {\n" +
                                    "        \"FID\": 0,\n" +
                                    "        \"FBillTypeID\": {\n" +
                                    "            \"FNUMBER\": \"QTYFD02_SYS\"\n" +
                                    "        },\n" +
                                    "        \"FDATE\": \"" + spjssj + "\",\n" +
                                    "        \"FENDDATE_H\": \"" + spjssj + "\",\n" +
                                    "        \"FISINIT\": false,\n" +
                                    "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                    "        \"FCONTACTUNIT\": {\n" +
                                    "            \"FNumber\": \"" + sqrid + "\"\n" +
                                    "        },\n" +
                                    "        \"FCURRENCYID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FDEPARTMENTID\": {\n" +
                                    "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                    "        },\n" +
                                    "        \"FSETTLEORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FPURCHASEORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FPAYORGID\": {\n" +
                                    "            \"FNumber\": \"" + bm + "\"\n" +
                                    "        },\n" +
                                    "        \"FMAINBOOKSTDCURRID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FEXCHANGETYPE\": {\n" +
                                    "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                    "        },\n" +
                                    "        \"FExchangeRate\": 1.0,\n" +
                                    "        \"FACCNTTIMEJUDGETIME\": \"" + spjssj + "\",\n" +
                                    "        \"FCancelStatus\": \"A\",\n" +
                                    "        \"FBUSINESSTYPE\": \"T\",\n" +
                                    "        \"FRemarks\": \"" + wlgbJdLcxmdyzsqjl.getFyfsxmmc() + "\",\n" +
                                    "        \"FSPSLBT\": \"" + wlgbJdLcxmdyzsqjl.getSpbt() + "\",\n" +
                                    "        \"FSPBH\": \"" + wlgbJdLcxmdyzsqjl.getSpbh() + "\",\n" +
                                    "        \"FSPSQR\": \"" + wlgbJdLcxmdyzsqjl.getSqr() + "\",\n" +
                                    "        \"FFYSYF\": \"" + wlgbJdLcxmdyzsqjl.getMdlx() + "\",\n" +
                                    "        \"FSPSQRBM\": \"" + wlgbJdLcxmdyzsqjl.getSqrbm() + "\",\n";
                            List<JSONObject> list4 = new ArrayList<>();
                            list3.forEach(l -> {
                                JSONObject jsonObject = scQtYfdMx(null, l.getFykmbm(), l.getFyje(), sqrbmid);

                                list4.add(jsonObject);
                            });
                            json += "        \"FEntity\": " + list4.toString() +
                                    "    }\n" +
                                    "}";
                            //录入金蝶
                            Map<String, String> map7 = KingDeeConfig.spTjJdDj2(json, "其他应付单");
                            save = "true".equals(map7.get("jg"));
                            if (!save) {
                                sendGztz(0, wlgbJdLcxmdyzsqjl.getSpbh(), "录入金蝶失败,原因：" + map7.get("msg"), "其他应付单");
                            }
                            if (save) {
                                //上传单据成功之后将里面数据改成上传完成
                                list3.forEach(l -> {
                                    WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                    wlgbJdSpCwlrjdsj.setId(l.getId());
                                    wlgbJdSpCwlrjdsj.setSfscjd(1);
                                    wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                                });
                            }
                        }
                        if (save) {
                            if (type == 1) {
                                WlgbJdLcxmdyzsqjl wlgbJdLcxmdyzsqjl1 = new WlgbJdLcxmdyzsqjl();
                                wlgbJdLcxmdyzsqjl1.setId(wlgbJdLcxmdyzsqjl.getId());
                                wlgbJdLcxmdyzsqjl1.setSflrjd(1);
                                wlgbJdLcxmdyzsqjlService.updateById(wlgbJdLcxmdyzsqjl1);
                            }
                        }
                    } else {
                        sendGztz(0, wlgbJdLcxmdyzsqjl.getSpbh(), "员工缺失，补充错误", "抵预支");
                    }
                } else {
                    sendGztz(0, wlgbJdLcxmdyzsqjl.getSpbh(), "员工缺失，补充错误", "抵预支");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return save;
    }


    /**
     * 分摊设置处理
     *
     * @param value      值
     * @param businessId 审批编号
     * @param type       类型
     */
    public Boolean ftSzCl1(String value, String businessId, String title, Integer type) {
        JSONArray jsonArray = JSONArray.parseArray(value);
        boolean b = true;
        if (!jsonArray.isEmpty()) {
            JSONArray rowValue1 = jsonArray.getJSONObject(0).getJSONArray("rowValue");
            if (!rowValue1.isEmpty()) {
                for (int i = 0; i < rowValue1.size(); i++) {
                    JSONObject jsonObject = rowValue1.getJSONObject(i);
                    String label = jsonObject.getString("label");
                    if ("公司费用科目".equals(label)) {
                        JSONObject extendValue = jsonObject.getJSONObject("extendValue");
                        JSONObject jsonObject1 = extendValue.getJSONArray("list").getJSONObject(0);
                        JSONArray rowValue2 = jsonObject1.getJSONArray("rowValue");
                        for (int k = 0; k < rowValue2.size(); k++) {
                            JSONObject jsonObject2 = rowValue2.getJSONObject(k);
                            String label1 = jsonObject2.getString("label");
                            if ("编码".equals(label1)) {
                                String value1 = jsonObject2.getString("value");
                                if (!value1.isEmpty() && !"null".equalsIgnoreCase(value1)) {
                                    b = false;
                                }
                            }
                        }
                    }
                }
            }
            if (b) {
                for (int j = 0; j < jsonArray.size(); j++) {
                    JSONArray rowValue = jsonArray.getJSONObject(j).getJSONArray("rowValue");
                    if (!rowValue.isEmpty()) {
                        WlgbJdSpBsft wlgbJdSpBsft = new WlgbJdSpBsft();
                        wlgbJdSpBsft.setSpbh(businessId);
                        for (int i = 0; i < rowValue.size(); i++) {
                            JSONObject jsonObject = rowValue.getJSONObject(i);
                            String label = jsonObject.getString("label");
                            if ("公司门店表单".equals(label)) {
                                wlgbJdSpBsft.setBsmc(jsonObject.getJSONArray("value").getString(0));
                                TbVilla tbVilla = weiLianService.queryVillaByVname(wlgbJdSpBsft.getBsmc());
                                if (tbVilla != null) {
                                    wlgbJdSpBsft.setBsxz(tbVilla.getVxz());
                                }
                            }
                            if ("该门店分摊金额".equals(label)) {
                                wlgbJdSpBsft.setJe(jsonObject.getDouble("value"));
                            }
                        }
                        wlgbJdSpBsft.setType(1);
                        wlgbJdSpBsft.setStatu(type);
                        wlgbJdSpBsftService.save(wlgbJdSpBsft);
                    }
                }
            } else {
                String zt = "";
                String ztbh = "";
                String km = "";
                String kmbm = "";
                for (int j = 0; j < jsonArray.size(); j++) {
                    JSONArray rowValue = jsonArray.getJSONObject(j).getJSONArray("rowValue");
                    if (!rowValue.isEmpty()) {
                        WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                        wlgbJdSpCwlrjdsj.setSpbh(businessId);
                        wlgbJdSpCwlrjdsj.setSpbt(title);
                        for (int i = 0; i < rowValue.size(); i++) {
                            JSONObject jsonObject = rowValue.getJSONObject(i);
                            String label = jsonObject.getString("label");
                            if ("公司门店表单".equals(label)) {
                                wlgbJdSpCwlrjdsj.setBsmc(jsonObject.getJSONArray("value").getString(0));
                                String bsmc = wlgbJdSpCwlrjdsj.getBsmc();
                                //查询别墅表存不存在
                                TbVilla tbVilla = weiLianService.queryVillaByVname(bsmc);
                                //如果不存在
                                if (tbVilla == null) {
                                    //去别墅修改记录表查询
                                    WlgbJdBsmcXgjl wlgbJdBsmcXgjl = wlgbJdBsmcXgjlService.queryByYnameAndSfSc(bsmc, 0);
                                    //别墅修改记录表存在
                                    if (wlgbJdBsmcXgjl != null) {
                                        //把别墅名字给上传使用
                                        bsmc = wlgbJdBsmcXgjl.getVname();
                                    }
                                }
                                JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                                Boolean success = jsonObject1.getBoolean("success");
                                if (success) {
                                    wlgbJdSpCwlrjdsj.setBsbh(jsonObject1.getString("bm"));
                                }
                            }
                            if ("该门店分摊金额".equals(label)) {
                                wlgbJdSpCwlrjdsj.setFyje(jsonObject.getDouble("value"));
                            }
                            if ("公司帐套".equals(label)) {
                                JSONObject extendValue = jsonObject.getJSONObject("extendValue");
                                if (extendValue != null && extendValue.size() > 0) {
                                    JSONObject jsonObject1 = extendValue.getJSONArray("list").getJSONObject(0);
                                    JSONArray rowValue2 = jsonObject1.getJSONArray("rowValue");
                                    for (int k = 0; k < rowValue2.size(); k++) {
                                        JSONObject jsonObject2 = rowValue2.getJSONObject(k);
                                        String label1 = jsonObject2.getString("label");
                                        if ("帐套编码".equals(label1)) {
                                            String value1 = jsonObject2.getString("value");
                                            if (value1 != null && !"".equals(value1) && !"null".equalsIgnoreCase(value1)) {
                                                ztbh = value1;
                                            }
                                        }
                                        if ("公司帐套".equals(label1)) {
                                            String value1 = jsonObject2.getString("value");
                                            if (value1 != null && !"".equals(value1) && !"null".equalsIgnoreCase(value1)) {
                                                zt = value1;
                                            }
                                        }
                                    }
                                }
                            }
                            if ("公司费用科目".equals(label)) {
                                JSONObject extendValue = jsonObject.getJSONObject("extendValue");
                                JSONObject jsonObject1 = extendValue.getJSONArray("list").getJSONObject(0);
                                JSONArray rowValue2 = jsonObject1.getJSONArray("rowValue");
                                for (int k = 0; k < rowValue2.size(); k++) {
                                    JSONObject jsonObject2 = rowValue2.getJSONObject(k);
                                    String label1 = jsonObject2.getString("label");
                                    if ("编码".equals(label1)) {
                                        String value1 = jsonObject2.getString("value");
                                        if (value1 != null && !"".equals(value1) && !"null".equalsIgnoreCase(value1)) {
                                            kmbm = value1;
                                        }
                                    }
                                    if ("全名".equals(label1)) {
                                        String value1 = jsonObject2.getString("value");
                                        if (value1 != null && !"".equals(value1) && !"null".equalsIgnoreCase(value1)) {
                                            km = value1;
                                        }
                                    }

                                }
                            }
                        }
                        if ("".equals(ztbh)) {
                            if ("直营".equals(zt)) {
                                ztbh = "102";
                            } else if ("加盟".equals(zt)) {
                                ztbh = "101";
                            } else if ("威廉集团".equals(zt)) {
                                ztbh = "100";
                            } else if ("乐诚".equals(zt)) {
                                ztbh = "103";
                            } else if ("九运".equals(zt)) {
                                ztbh = "105";
                            } else if ("奋马".equals(zt)) {
                                ztbh = "104";
                            }
                        }
                        wlgbJdSpCwlrjdsj.setFykm(km);
                        wlgbJdSpCwlrjdsj.setFykmbm(kmbm);
                        wlgbJdSpCwlrjdsj.setZt(zt);
                        wlgbJdSpCwlrjdsj.setZtbm(ztbh);
                        wlgbJdSpCwlrjdsj.setType(type);
                        double fyje = wlgbJdSpCwlrjdsj.getFyje();
                        if (fyje != 0) {
                            wlgbJdSpCwlrjdsjService.save(wlgbJdSpCwlrjdsj);
                        }
                    }
                }
            }
        }
        return b;
    }


    /**
     * 不平摊门店处理
     *
     * @param value      值
     * @param businessId 审批编号
     */
    public void bptMdCl(String value, String businessId, Integer type) {
        JSONArray jsonArray = JSONArray.parseArray(value);
        if (jsonArray != null && jsonArray.size() > 0) {
            for (int j = 0; j < jsonArray.size(); j++) {
                JSONArray rowValue = jsonArray.getJSONObject(j).getJSONArray("rowValue");
                if (rowValue != null && rowValue.size() > 0) {
                    WlgbJdSpBsft wlgbJdSpBsft = new WlgbJdSpBsft();
                    wlgbJdSpBsft.setSpbh(businessId);
                    for (int i = 0; i < rowValue.size(); i++) {
                        JSONObject jsonObject = rowValue.getJSONObject(i);
                        String label = jsonObject.getString("label");
                        if ("公司门店表单".equals(label)) {
                            wlgbJdSpBsft.setBsmc(jsonObject.getJSONArray("value").getString(0));
                            TbVilla tbVilla = weiLianService.queryVillaByVname(wlgbJdSpBsft.getBsmc());
                            if (tbVilla != null) {
                                wlgbJdSpBsft.setBsxz(tbVilla.getVxz());
                            }
                        }
                        if ("该门店分摊金额".equals(label)) {
                            wlgbJdSpBsft.setJe(jsonObject.getDouble("value"));
                        }
                    }
                    wlgbJdSpBsft.setType(2);
                    wlgbJdSpBsft.setStatu(type);
                    wlgbJdSpBsftService.save(wlgbJdSpBsft);
                }
            }
        }
    }

    /**
     * 门店城市处理
     *
     * @param value      值
     * @param businessId 审批编号
     */
    public void mdCsCl(String value, String businessId, Integer type) {
        JSONArray jsonArray = JSONArray.parseArray(value);
        if (jsonArray != null && jsonArray.size() > 0) {
            for (int j = 0; j < jsonArray.size(); j++) {
                boolean result = false;
                String arrayString = jsonArray.getString(j);
                try {
                    Object obj = JSONObject.parse(arrayString);
                    result = true;
                } catch (Exception e) {
                    result = false;
                }
                if (result) {
                    JSONArray rowValue = jsonArray.getJSONObject(j).getJSONArray("rowValue");
                    if (rowValue != null && rowValue.size() > 0) {
                        WlgbJdSpCsft wlgbJdSpCsft = new WlgbJdSpCsft();
                        wlgbJdSpCsft.setSpbh(businessId);
                        for (int i = 0; i < rowValue.size(); i++) {
                            JSONObject jsonObject = rowValue.getJSONObject(i);
                            String label = jsonObject.getString("label");
                            if ("公司门店城市".equals(label)) {
                                wlgbJdSpCsft.setCity(jsonObject.getJSONArray("value").getString(0));
                            }
                        }
                        wlgbJdSpCsft.setStatu(type);
                        wlgbJdSpCsftService.save(wlgbJdSpCsft);
                    }
                } else {
                    if (arrayString != null && !"".equals(arrayString)) {
                        WlgbJdSpCsft wlgbJdSpCsft = new WlgbJdSpCsft();
                        wlgbJdSpCsft.setSpbh(businessId);
                        wlgbJdSpCsft.setStatu(type);
                        wlgbJdSpCsft.setCity(arrayString);
                        wlgbJdSpCsftService.save(wlgbJdSpCsft);
                    }
                }
            }
        }
    }

    /**
     * 财务入金蝶数据存库处理
     *
     * @param value      值
     * @param businessId 审批编号
     * @param title      审批标题
     */
    public void cwRjdSj(String value, String businessId, String title, Integer type) {
        JSONArray jsonArray = JSONArray.parseArray(value);
        if (jsonArray != null && jsonArray.size() > 0) {
            for (int j = 0; j < jsonArray.size(); j++) {
                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                wlgbJdSpCwlrjdsj.setSpbh(businessId);
                wlgbJdSpCwlrjdsj.setSpbt(title);
                JSONArray rowValue = jsonArray.getJSONObject(j).getJSONArray("rowValue");
                if (rowValue != null && rowValue.size() > 0) {
                    for (int i = 0; i < rowValue.size(); i++) {
                        JSONObject jsonObject = rowValue.getJSONObject(i);
                        String label = jsonObject.getString("label");
                        if (type == 7) {
                            String value1 = jsonObject.getString("value");
                            if ("帐套编码".equals(label)) {
                                wlgbJdSpCwlrjdsj.setZtbm(value1);
                            }
                            if ("公司帐套".equals(label)) {
                                wlgbJdSpCwlrjdsj.setZt(value1);
                            }
                            if ("编码".equals(label)) {
                                wlgbJdSpCwlrjdsj.setFykmbm(value1);
                            }
                            if ("名称".equals(label)) {
                                wlgbJdSpCwlrjdsj.setFykm(value1);
                            }
                        } else {
                            if ("公司帐套".equals(label)) {
                                JSONObject extendValue = jsonObject.getJSONObject("extendValue");
                                if (extendValue != null && extendValue.size() > 0) {
                                    JSONObject jsonObject1 = extendValue.getJSONArray("list").getJSONObject(0);
                                    JSONArray rowValue1 = jsonObject1.getJSONArray("rowValue");
                                    for (int k = 0; k < rowValue1.size(); k++) {
                                        JSONObject jsonObject2 = rowValue1.getJSONObject(k);
                                        String label1 = jsonObject2.getString("label");
                                        String value1 = jsonObject2.getString("value");
                                        if ("帐套编码".equals(label1)) {
                                            wlgbJdSpCwlrjdsj.setZtbm(value1);
                                        }
                                        if ("公司帐套".equals(label1)) {
                                            wlgbJdSpCwlrjdsj.setZt(value1);
                                        }
                                    }
                                }
                            }
                            if ("公司费用科目".equals(label)) {
                                JSONObject extendValue = jsonObject.getJSONObject("extendValue");
                                JSONObject jsonObject1 = extendValue.getJSONArray("list").getJSONObject(0);
                                JSONArray rowValue1 = jsonObject1.getJSONArray("rowValue");
                                for (int k = 0; k < rowValue1.size(); k++) {
                                    JSONObject jsonObject2 = rowValue1.getJSONObject(k);
                                    String label1 = jsonObject2.getString("label");
                                    if ("编码".equals(label1)) {
                                        String value1 = jsonObject2.getString("value");
                                        wlgbJdSpCwlrjdsj.setFykmbm(value1);
                                    }
                                }
                                wlgbJdSpCwlrjdsj.setFykm(jsonObject.getJSONArray("value").getString(0));
                            }
                        }
                        if ("费用金额".equals(label) || "金额（元）".equals(label)) {
                            Double je = jsonObject.getDouble("value");
                            wlgbJdSpCwlrjdsj.setFyje(je);
                        }
                        if ("卡号".equals(label)) {
                            wlgbJdSpCwlrjdsj.setKh(jsonObject.getString("value"));
                        }
                        if ("付款时间".equals(label)) {
                            wlgbJdSpCwlrjdsj.setFksj(jsonObject.getString("value"));
                        }
                    }
                    wlgbJdSpCwlrjdsj.setType(type);
                    double fyje = wlgbJdSpCwlrjdsj.getFyje();
                    if (fyje != 0) {
                        wlgbJdSpCwlrjdsjService.save(wlgbJdSpCwlrjdsj);
                    }
                }
            }
        }
    }

    /**
     * 一线采购报修审批处理
     */
    public boolean yxCgBxCl(String spbh, WlgbJdYxcgsqjl wlgbJdYxcgsqjl, Integer type) {
        if (type == 1) {
            wlgbJdYxcgsqjl = wlgbJdYxcgsqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        boolean save = false;
        if (wlgbJdYxcgsqjl != null) {
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String zzsj = df2.format(wlgbJdYxcgsqjl.getCnzzsj());
            String sqrbmid = wlgbJdYxcgsqjl.getSqrbmid();
            Boolean bmCl = KingDeeConfig.bmCl(sqrbmid, wlgbJdYxcgsqjl.getSqrbm());
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(wlgbJdYxcgsqjl.getSqrid(), wlgbJdYxcgsqjl.getSqr());
                if (ryCl) {

                    if ("集群餐饮采购".equals(wlgbJdYxcgsqjl.getCglx())) {
                        TbVilla tbVilla = new TbVilla();
                        tbVilla.setVcyqybh(wlgbJdYxcgsqjl.getCyqybh());
                        tbVilla.setVsfsc(0);
                        tbVilla.setVsfft("1");
                        List<TbVilla> vList = tbVillaService.queryList(tbVilla);
                        List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(10, wlgbJdYxcgsqjl.getSpbh(), 0);
                        List<String> list2 = new ArrayList<>();
                        list.forEach(l -> {
                            if (!list2.contains(l.getZtbm())) {
                                list2.add(l.getZtbm());
                            }
                        });
                        for (String l : list2) {
                            List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(10, wlgbJdYxcgsqjl.getSpbh(), 0, l);
                            String json = "{\n" +
                                    "    \"NeedUpDateFields\": [],\n" +
                                    "    \"NeedReturnFields\": [],\n" +
                                    "    \"IsDeleteEntry\": \"true\",\n" +
                                    "    \"SubSystemId\": \"\",\n" +
                                    "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                    "    \"IsEntryBatchFill\": \"true\",\n" +
                                    "    \"ValidateFlag\": \"true\",\n" +
                                    "    \"NumberSearch\": \"true\",\n" +
                                    "    \"IsAutoAdjustField\": \"false\",\n" +
                                    "    \"InterationFlags\": \"\",\n" +
                                    "    \"IgnoreInterationFlag\": \"\",\n" +
                                    "    \"Model\": {\n" +
                                    "        \"FID\": 0,\n" +
                                    "        \"FBillTypeID\": {\n" +
                                    "            \"FNUMBER\": \"" + ("代付".equals(wlgbJdYxcgsqjl.getFkfs()) ? "QTYFD01_SYS" : "QTYFD02_SYS") + "\"\n" +
                                    "        },\n" +
                                    "        \"FDATE\": \"" + zzsj + "\",\n" +
                                    "        \"FENDDATE_H\": \"" + zzsj + "\",\n" +
                                    "        \"FISINIT\": false,\n" +
                                    "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                    "        \"FCONTACTUNIT\": {\n" +
                                    "            \"FNumber\": \"" + wlgbJdYxcgsqjl.getSqrid() + "\"\n" +
                                    "        },\n" +
                                    "        \"FCURRENCYID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FDEPARTMENTID\": {\n" +
                                    "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                    "        },\n" +
                                    "        \"FSETTLEORGID\": {\n" +
                                    "            \"FNumber\": \"" + l + "\"\n" +
                                    "        },\n" +
                                    "        \"FPURCHASEORGID\": {\n" +
                                    "            \"FNumber\": \"" + l + "\"\n" +
                                    "        },\n" +
                                    "        \"FPAYORGID\": {\n" +
                                    "            \"FNumber\": \"" + l + "\"\n" +
                                    "        },\n" +
                                    "        \"FMAINBOOKSTDCURRID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FEXCHANGETYPE\": {\n" +
                                    "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                    "        },\n" +
                                    "        \"FExchangeRate\": 1.0,\n" +
                                    "        \"FACCNTTIMEJUDGETIME\": \"" + zzsj + "\",\n" +
                                    "        \"FCancelStatus\": \"A\",\n" +
                                    "        \"FBUSINESSTYPE\": \"T\",\n" +
                                    "        \"FRemarks\": \"" + wlgbJdYxcgsqjl.getBz() + "\",\n" +
                                    "        \"FSPSLBT\": \"" + wlgbJdYxcgsqjl.getSpbt() + "\",\n" +
                                    "        \"FSPBH\": \"" + wlgbJdYxcgsqjl.getSpbh() + "\",\n" +
                                    "        \"FSPSQR\": \"" + wlgbJdYxcgsqjl.getSqr() + "\",\n" +
                                    "        \"FSPSQRBM\": \"" + wlgbJdYxcgsqjl.getSqrbm() + "\",\n";
                            List<JSONObject> list1 = new ArrayList<>();
                            WlgbJdYxcgsqjl finalWlgbJdYxcgsqjl = wlgbJdYxcgsqjl;
                            boolean b = false;
                            for (WlgbJdSpCwlrjdsj l1 : list3) {
                                double sum = 0.0;
                                for (int i = 0; i < vList.size(); i++) {
                                    TbVilla l2 = vList.get(i);
                                    double je = l1.getFyje() / vList.size();
                                    String je1 = je + "";
                                    String substring1 = je1.substring(je1.indexOf("."));
                                    if (substring1.length() <= 3) {
                                        je = Double.parseDouble(je1);
                                    } else {
                                        String substring = je1.substring(0, je1.indexOf(".") + 3);
                                        je = Double.parseDouble(substring);
                                    }
                                    if (i == vList.size() - 1) {
                                        je = l1.getFyje() - sum;
                                    }

                                    sum += je;
                                    JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(l2.getVname());
                                    Boolean success = jsonObject1.getBoolean("success");
                                    if (success) {
                                        JSONObject jsonObject2 = scQtYfdMx(jsonObject1.getString("bm"), l1.getFykmbm(), je, sqrbmid);
                                        list1.add(jsonObject2);
                                    } else {
                                        b = true;
                                    }
                                }
                            }
                            if (b) {
                                sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "别墅不存在", wlgbJdYxcgsqjl.getSpbt());
                                return false;
                            }
                            json += "        \"FEntity\": " + list1.toString() +
                                    "    }\n" +
                                    "}";
                            Map<String, String> map6 = KingDeeConfig.spTjJdDj2(json, "其他应付单");
                            save = "true".equals(map6.get("jg"));
                            if (!save) {
                                sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "录入金蝶失败,原因：" + map6.get("msg"), "其他应付单");
                            }
                            if (save) {
                                String json1 = "{\n" +
                                        "    \"NeedUpDateFields\": [],\n" +
                                        "    \"NeedReturnFields\": [],\n" +
                                        "    \"IsDeleteEntry\": \"true\",\n" +
                                        "    \"SubSystemId\": \"\",\n" +
                                        "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                        "    \"IsEntryBatchFill\": \"true\",\n" +
                                        "    \"ValidateFlag\": \"true\",\n" +
                                        "    \"NumberSearch\": \"true\",\n" +
                                        "    \"IsAutoAdjustField\": \"false\",\n" +
                                        "    \"InterationFlags\": \"\",\n" +
                                        "    \"IgnoreInterationFlag\": \"\",\n" +
                                        "    \"Model\": {\n" +
                                        "        \"FID\": 0,\n" +
                                        "        \"FBillTypeID\": {\n" +
                                        "            \"FNUMBER\": \"" + ("代付".equals(wlgbJdYxcgsqjl.getFkfs()) ? "FKDLX02_SYS" : "FKDLX04_SYS") + "\"\n" +
                                        "        },\n" +
                                        "        \"FDATE\": \"" + zzsj + "\",\n" +
                                        "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                        "        \"FCONTACTUNIT\": {\n" +
                                        "            \"FNumber\": \"" + wlgbJdYxcgsqjl.getSqrid() + "\"\n" +
                                        "        },\n" +
                                        "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                        "        \"FRECTUNIT\": {\n" +
                                        "            \"FNumber\": \"" + wlgbJdYxcgsqjl.getSqrid() + "\"\n" +
                                        "        },\n" +
                                        "        \"FDepartment\": {\n" +
                                        "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                        "        },\n" +
                                        "        \"FISINIT\": false,\n" +
                                        "        \"FCURRENCYID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FEXCHANGERATE\": 1.0,\n" +
                                        "        \"FSETTLERATE\": 1.0,\n" +
                                        "        \"FSETTLEORGID\": {\n" +
                                        "            \"FNumber\": \"" + l + "\"\n" +
                                        "        },\n" +
                                        "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                        "        \"FBUSINESSTYPE\": \"3\",\n" +
                                        "        \"FCancelStatus\": \"A\",\n" +
                                        "        \"FPAYORGID\": {\n" +
                                        "            \"FNumber\": \"" + l + "\"\n" +
                                        "        },\n" +
                                        "        \"FISSAMEORG\": true,\n" +
                                        "        \"FIsCredit\": false,\n" +
                                        "        \"FSETTLECUR\": {\n" +
                                        "            \"FNUMBER\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FIsWriteOff\": false,\n" +
                                        "        \"FREALPAY\": false,\n" +
                                        "        \"FISCARRYRATE\": false,\n" +
                                        "        \"FSETTLEMAINBOOKID\": {\n" +
                                        "            \"FNUMBER\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FMoreReceive\": false,\n" +
                                        "        \"FVirIsSameAcctOrg\": false,\n" +
                                        "        \"FREMARK\": \"" + wlgbJdYxcgsqjl.getBz() + "\",\n" +
                                        "        \"FSPBT\": \"" + wlgbJdYxcgsqjl.getSpbt() + "\",\n" +
                                        "        \"FSPBH\": \"" + wlgbJdYxcgsqjl.getSpbh() + "\",\n" +
                                        "        \"FSPSQR\": \"" + wlgbJdYxcgsqjl.getSqr() + "\",\n" +
                                        "        \"FSPSQRBM\": \"" + wlgbJdYxcgsqjl.getSqrbm() + "\",\n";
                                WlgbJdYhk wlgbJdYhk = yhkCl(l);
                                List<JSONObject> list4 = new ArrayList<>();
                                list1.forEach(l2 -> {
                                    String fcostid = l2.getJSONObject("FCOSTID").getString("FNumber");
                                    String fssmd = l2.getJSONObject("FSSMD").getString("FNumber");
                                    Double fnotaxamountfor = l2.getDouble("FNOTAXAMOUNTFOR");
                                    JSONObject jsonObject2 = fkdMx1(fssmd, fnotaxamountfor, zzsj, fcostid, sqrbmid, ("代付".equals(finalWlgbJdYxcgsqjl.getFkfs()) ? "SFKYT20_SYS" : "SFKYT10_SYS"), wlgbJdYhk.getZh());

                                    list4.add(jsonObject2);
                                });
                                json1 += "        \"FPAYBILLENTRY\": " + list4.toString() +
                                        "    }\n" +
                                        "}";
                                //录入金蝶
                                Map<String, String> map5 = KingDeeConfig.spTjJdDj2(json1, "付款单");
                                save = "true".equals(map5.get("jg"));
                                if (!save) {
                                    sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "录入金蝶失败,原因：" + map5.get("msg"), "付款单");
                                }
                                if (save) {
                                    list3.forEach(l1 -> {
                                        WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj1 = new WlgbJdSpCwlrjdsj();
                                        wlgbJdSpCwlrjdsj1.setId(l1.getId());
                                        wlgbJdSpCwlrjdsj1.setSfscjd(1);
                                        wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj1);
                                    });
                                }
                            }
                        }
                        if (save) {
                            if (type == 1) {
                                WlgbJdYxcgsqjl wlgbJdYxcgsqjl1 = new WlgbJdYxcgsqjl();
                                wlgbJdYxcgsqjl1.setId(wlgbJdYxcgsqjl.getId());
                                wlgbJdYxcgsqjl1.setSflrjd(1);
                                wlgbJdYxcgsqjlService.updateById(wlgbJdYxcgsqjl1);
                            }
                        }
                    } else {
                        String bsmc = wlgbJdYxcgsqjl.getBsmc();
                        if (bsmc != null && !"".equals(bsmc)) {
                            //查询别墅表存不存在
                            TbVilla tbVilla = weiLianService.queryVillaByVname(bsmc);
                            //如果不存在
                            if (tbVilla == null) {
                                //去别墅修改记录表查询
                                WlgbJdBsmcXgjl wlgbJdBsmcXgjl = wlgbJdBsmcXgjlService.queryByYnameAndSfSc(bsmc, 0);
                                //别墅修改记录表存在
                                if (wlgbJdBsmcXgjl != null) {
                                    //把别墅名字给上传使用
                                    bsmc = wlgbJdBsmcXgjl.getVname();
                                }
                            }
                            JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                            Boolean success = jsonObject1.getBoolean("success");
                            if ("区域平摊".equals(bsmc) || "区域办公室".equals(bsmc)) {
                                success = true;
                            }
                            if (success) {
                                String bm = jsonObject1.getString("bm");
                                wlgbJdYxcgsqjl.setBsbh(bm);
                                List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(10, wlgbJdYxcgsqjl.getSpbh(), 0);
                                List<String> list2 = new ArrayList<>();
                                list.forEach(l -> {
                                    if (!list2.contains(l.getZtbm())) {
                                        list2.add(l.getZtbm());
                                    }
                                });
                                for (String l : list2) {
                                    List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(10, wlgbJdYxcgsqjl.getSpbh(), 0, l);
                                    String json = "{\n" +
                                            "    \"NeedUpDateFields\": [],\n" +
                                            "    \"NeedReturnFields\": [],\n" +
                                            "    \"IsDeleteEntry\": \"true\",\n" +
                                            "    \"SubSystemId\": \"\",\n" +
                                            "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                            "    \"IsEntryBatchFill\": \"true\",\n" +
                                            "    \"ValidateFlag\": \"true\",\n" +
                                            "    \"NumberSearch\": \"true\",\n" +
                                            "    \"IsAutoAdjustField\": \"false\",\n" +
                                            "    \"InterationFlags\": \"\",\n" +
                                            "    \"IgnoreInterationFlag\": \"\",\n" +
                                            "    \"Model\": {\n" +
                                            "        \"FID\": 0,\n" +
                                            "        \"FBillTypeID\": {\n" +
                                            "            \"FNUMBER\": \"" + ("代付".equals(wlgbJdYxcgsqjl.getFkfs()) ? "QTYFD01_SYS" : "QTYFD02_SYS") + "\"\n" +
                                            "        },\n" +
                                            "        \"FDATE\": \"" + zzsj + "\",\n" +
                                            "        \"FENDDATE_H\": \"" + zzsj + "\",\n" +
                                            "        \"FISINIT\": false,\n" +
                                            "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FCONTACTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + wlgbJdYxcgsqjl.getSqrid() + "\"\n" +
                                            "        },\n" +
                                            "        \"FCURRENCYID\": {\n" +
                                            "            \"FNumber\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FDEPARTMENTID\": {\n" +
                                            "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                            "        },\n" +
                                            "        \"FSETTLEORGID\": {\n" +
                                            "            \"FNumber\": \"" + l + "\"\n" +
                                            "        },\n" +
                                            "        \"FPURCHASEORGID\": {\n" +
                                            "            \"FNumber\": \"" + l + "\"\n" +
                                            "        },\n" +
                                            "        \"FPAYORGID\": {\n" +
                                            "            \"FNumber\": \"" + l + "\"\n" +
                                            "        },\n" +
                                            "        \"FMAINBOOKSTDCURRID\": {\n" +
                                            "            \"FNumber\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FEXCHANGETYPE\": {\n" +
                                            "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                            "        },\n" +
                                            "        \"FExchangeRate\": 1.0,\n" +
                                            "        \"FACCNTTIMEJUDGETIME\": \"" + zzsj + "\",\n" +
                                            "        \"FCancelStatus\": \"A\",\n" +
                                            "        \"FBUSINESSTYPE\": \"T\",\n" +
                                            "        \"FRemarks\": \"" + wlgbJdYxcgsqjl.getBz() + "\",\n" +
                                            "        \"FSPSLBT\": \"" + wlgbJdYxcgsqjl.getSpbt() + "\",\n" +
                                            "        \"FSPBH\": \"" + wlgbJdYxcgsqjl.getSpbh() + "\",\n" +
                                            "        \"FSPSQR\": \"" + wlgbJdYxcgsqjl.getSqr() + "\",\n" +
                                            "        \"FSPSQRBM\": \"" + wlgbJdYxcgsqjl.getSqrbm() + "\",\n";
                                    List<JSONObject> list1 = new ArrayList<>();
                                    WlgbJdYxcgsqjl finalWlgbJdYxcgsqjl = wlgbJdYxcgsqjl;
                                    list3.forEach(l1 -> {
                                        JSONObject jsonObject2 = scQtYfdMx(finalWlgbJdYxcgsqjl.getBsbh(), l1.getFykmbm(), l1.getFyje(), sqrbmid);
                                        list1.add(jsonObject2);
                                    });
                                    json += "        \"FEntity\": " + list1.toString() +
                                            "    }\n" +
                                            "}";
                                    Map<String, String> map4 = KingDeeConfig.spTjJdDj2(json, "其他应付单");
                                    save = "true".equals(map4.get("jg"));
                                    if (!save) {
                                        sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "录入金蝶失败,原因：" + map4.get("msg"), "其他应付单");
                                    }
                                    if (save) {
                                        String json1 = "{\n" +
                                                "    \"NeedUpDateFields\": [],\n" +
                                                "    \"NeedReturnFields\": [],\n" +
                                                "    \"IsDeleteEntry\": \"true\",\n" +
                                                "    \"SubSystemId\": \"\",\n" +
                                                "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                                "    \"IsEntryBatchFill\": \"true\",\n" +
                                                "    \"ValidateFlag\": \"true\",\n" +
                                                "    \"NumberSearch\": \"true\",\n" +
                                                "    \"IsAutoAdjustField\": \"false\",\n" +
                                                "    \"InterationFlags\": \"\",\n" +
                                                "    \"IgnoreInterationFlag\": \"\",\n" +
                                                "    \"Model\": {\n" +
                                                "        \"FID\": 0,\n" +
                                                "        \"FBillTypeID\": {\n" +
                                                "            \"FNUMBER\": \"" + ("代付".equals(wlgbJdYxcgsqjl.getFkfs()) ? "FKDLX02_SYS" : "FKDLX04_SYS") + "\"\n" +
                                                "        },\n" +
                                                "        \"FDATE\": \"" + zzsj + "\",\n" +
                                                "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                                "        \"FCONTACTUNIT\": {\n" +
                                                "            \"FNumber\": \"" + wlgbJdYxcgsqjl.getSqrid() + "\"\n" +
                                                "        },\n" +
                                                "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                                "        \"FRECTUNIT\": {\n" +
                                                "            \"FNumber\": \"" + wlgbJdYxcgsqjl.getSqrid() + "\"\n" +
                                                "        },\n" +
                                                "        \"FDepartment\": {\n" +
                                                "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                                "        },\n" +
                                                "        \"FISINIT\": false,\n" +
                                                "        \"FCURRENCYID\": {\n" +
                                                "            \"FNumber\": \"PRE001\"\n" +
                                                "        },\n" +
                                                "        \"FEXCHANGERATE\": 1.0,\n" +
                                                "        \"FSETTLERATE\": 1.0,\n" +
                                                "        \"FSETTLEORGID\": {\n" +
                                                "            \"FNumber\": \"" + l + "\"\n" +
                                                "        },\n" +
                                                "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                                "        \"FBUSINESSTYPE\": \"3\",\n" +
                                                "        \"FCancelStatus\": \"A\",\n" +
                                                "        \"FPAYORGID\": {\n" +
                                                "            \"FNumber\": \"" + l + "\"\n" +
                                                "        },\n" +
                                                "        \"FISSAMEORG\": true,\n" +
                                                "        \"FIsCredit\": false,\n" +
                                                "        \"FSETTLECUR\": {\n" +
                                                "            \"FNUMBER\": \"PRE001\"\n" +
                                                "        },\n" +
                                                "        \"FIsWriteOff\": false,\n" +
                                                "        \"FREALPAY\": false,\n" +
                                                "        \"FISCARRYRATE\": false,\n" +
                                                "        \"FSETTLEMAINBOOKID\": {\n" +
                                                "            \"FNUMBER\": \"PRE001\"\n" +
                                                "        },\n" +
                                                "        \"FMoreReceive\": false,\n" +
                                                "        \"FVirIsSameAcctOrg\": false,\n" +
                                                "        \"FREMARK\": \"" + wlgbJdYxcgsqjl.getBz() + "\",\n" +
                                                "        \"FSPBT\": \"" + wlgbJdYxcgsqjl.getSpbt() + "\",\n" +
                                                "        \"FSPBH\": \"" + wlgbJdYxcgsqjl.getSpbh() + "\",\n" +
                                                "        \"FSPSQR\": \"" + wlgbJdYxcgsqjl.getSqr() + "\",\n" +
                                                "        \"FSPSQRBM\": \"" + wlgbJdYxcgsqjl.getSqrbm() + "\",\n";
                                        WlgbJdYhk wlgbJdYhk = yhkCl(l);
                                        List<JSONObject> list4 = new ArrayList<>();
                                        list1.forEach(l2 -> {
                                            String fcostid = l2.getJSONObject("FCOSTID").getString("FNumber");
                                            String fssmd = l2.getJSONObject("FSSMD").getString("FNumber");
                                            Double fnotaxamountfor = l2.getDouble("FNOTAXAMOUNTFOR");
                                            JSONObject jsonObject2 = fkdMx1(fssmd, fnotaxamountfor, zzsj, fcostid, sqrbmid, ("代付".equals(finalWlgbJdYxcgsqjl.getFkfs()) ? "SFKYT20_SYS" : "SFKYT10_SYS"), wlgbJdYhk.getZh());

                                            list4.add(jsonObject2);
                                        });
                                        json1 += "        \"FPAYBILLENTRY\": " + list4.toString() +
                                                "    }\n" +
                                                "}";
                                        //录入金蝶
                                        Map<String, String> map3 = KingDeeConfig.spTjJdDj2(json1, "付款单");
                                        save = "true".equals(map3.get("jg"));
                                        if (!save) {
                                            sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "录入金蝶失败,原因：" + map3.get("msg"), "付款单");
                                        }
                                        if (save) {
                                            list3.forEach(l1 -> {
                                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj1 = new WlgbJdSpCwlrjdsj();
                                                wlgbJdSpCwlrjdsj1.setId(l1.getId());
                                                wlgbJdSpCwlrjdsj1.setSfscjd(1);
                                                wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj1);
                                            });
                                        }
                                    }
                                }
                                if (save) {
                                    if (type == 1) {
                                        WlgbJdYxcgsqjl wlgbJdYxcgsqjl1 = new WlgbJdYxcgsqjl();
                                        wlgbJdYxcgsqjl1.setId(wlgbJdYxcgsqjl.getId());
                                        wlgbJdYxcgsqjl1.setSflrjd(1);
                                        wlgbJdYxcgsqjlService.updateById(wlgbJdYxcgsqjl1);
                                    }
                                }
                            } else {
                                sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "别墅不存在", wlgbJdYxcgsqjl.getSpbt());
                            }
                        } else {
                            sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "别墅缺失，补充错误", wlgbJdYxcgsqjl.getSpbt());
                        }
                    }
                } else {
                    sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "员工缺失，补充错误", wlgbJdYxcgsqjl.getSpbt());
                }
            } else {
                sendGztz(0, wlgbJdYxcgsqjl.getSpbh(), "部门缺失，补充错误", wlgbJdYxcgsqjl.getSpbt());
            }
        }

        return save;
    }


    /**
     * 乐诚预支流程处理
     *
     * @param spBh         审批编号
     * @param wlgbJdLcyzjl 乐诚预支数据
     * @param type         1数据库存在，0数据库未存在
     */
    public boolean lcYzLcCl(String spBh, WlgbJdLcyzjl wlgbJdLcyzjl, Integer type) {
        if (type == 1) {
            wlgbJdLcyzjl = wlgbJdLcyzjlService.queryBySpBhAndSfLrJd(spBh, 0);
        }
        boolean save = false;
        if (wlgbJdLcyzjl != null) {
            Boolean bmCl = KingDeeConfig.bmCl(wlgbJdLcyzjl.getSqrbmid(), wlgbJdLcyzjl.getSqrbm());
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(wlgbJdLcyzjl.getSqrid(), wlgbJdLcyzjl.getSqr());
                if (ryCl) {
                    String bsmc = wlgbJdLcyzjl.getBsmc();
                    if (bsmc != null && !"".equals(bsmc)) {
                        //查询别墅表存不存在
                        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(bsmc);
                        //如果不存在
                        if (tbVilla == null) {
                            //去别墅修改记录表查询
                            WlgbJdBsmcXgjl wlgbJdBsmcXgjl = wlgbJdBsmcXgjlService.queryByYnameAndSfSc(bsmc, 0);
                            //别墅修改记录表存在
                            if (wlgbJdBsmcXgjl != null) {
                                //把别墅名字给上传使用
                                bsmc = wlgbJdBsmcXgjl.getVname();
                            }
                        }
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        if (jsonObject1.getBoolean("success")) {
                            String bm1 = jsonObject1.getString("bm");
                            List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(11, wlgbJdLcyzjl.getSpbh(), 0);
                            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
                            List<String> list2 = new ArrayList<>();
                            list.forEach(l -> {
                                if (!list2.contains(l.getZtbm())) {
                                    list2.add(l.getZtbm());
                                }
                            });
                            String sqrbmid = wlgbJdLcyzjl.getSqrbmid();
                            String cnzzsj = df2.format(wlgbJdLcyzjl.getCnzzsj());
                            for (String bm : list2) {
                                List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(11, wlgbJdLcyzjl.getSpbh(), 0, bm);
                                String json = "{\n" +
                                        "    \"NeedUpDateFields\": [],\n" +
                                        "    \"NeedReturnFields\": [],\n" +
                                        "    \"IsDeleteEntry\": \"true\",\n" +
                                        "    \"SubSystemId\": \"\",\n" +
                                        "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                        "    \"IsEntryBatchFill\": \"true\",\n" +
                                        "    \"ValidateFlag\": \"true\",\n" +
                                        "    \"NumberSearch\": \"true\",\n" +
                                        "    \"IsAutoAdjustField\": \"false\",\n" +
                                        "    \"InterationFlags\": \"\",\n" +
                                        "    \"IgnoreInterationFlag\": \"\",\n" +
                                        "    \"Model\": {\n" +
                                        "        \"FID\": 0,\n" +
                                        "        \"FBillTypeID\": {\n" +
                                        "            \"FNUMBER\": \"FKDLX02_SYS\"\n" +
                                        "        },\n" +
                                        "        \"FDATE\": \"" + cnzzsj + "\",\n" +
                                        "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                        "        \"FCONTACTUNIT\": {\n" +
                                        "            \"FNumber\": \"" + wlgbJdLcyzjl.getSqrid() + "\"\n" +
                                        "        },\n" +
                                        "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                        "        \"FRECTUNIT\": {\n" +
                                        "            \"FNumber\": \"" + wlgbJdLcyzjl.getSqrid() + "\"\n" +
                                        "        },\n" +
                                        "        \"FDepartment\": {\n" +
                                        "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                        "        },\n" +
                                        "        \"FISINIT\": false,\n" +
                                        "        \"FCURRENCYID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FEXCHANGERATE\": 1.0,\n" +
                                        "        \"FSETTLERATE\": 1.0,\n" +
                                        "        \"FSETTLEORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                        "        \"FBUSINESSTYPE\": \"3\",\n" +
                                        "        \"FCancelStatus\": \"A\",\n" +
                                        "        \"FPAYORGID\": {\n" +
                                        "            \"FNumber\": \"" + bm + "\"\n" +
                                        "        },\n" +
                                        "        \"FISSAMEORG\": true,\n" +
                                        "        \"FIsCredit\": false,\n" +
                                        "        \"FSETTLECUR\": {\n" +
                                        "            \"FNUMBER\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FIsWriteOff\": false,\n" +
                                        "        \"FREALPAY\": false,\n" +
                                        "        \"FISCARRYRATE\": false,\n" +
                                        "        \"FSETTLEMAINBOOKID\": {\n" +
                                        "            \"FNUMBER\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FMoreReceive\": false,\n" +
                                        "        \"FVirIsSameAcctOrg\": false,\n" +
                                        "        \"FREMARK\": \"" + wlgbJdLcyzjl.getBz() + "\",\n" +
                                        "        \"FSPBT\": \"" + wlgbJdLcyzjl.getSpbt() + "\",\n" +
                                        "        \"FSPBH\": \"" + wlgbJdLcyzjl.getSpbh() + "\",\n" +
                                        "        \"FSPSQR\": \"" + wlgbJdLcyzjl.getSqr() + "\",\n" +
                                        "        \"FSPSQRBM\": \"" + wlgbJdLcyzjl.getSqrbm() + "\",\n" +
                                        "        \"FFYSYF\": \"" + wlgbJdLcyzjl.getMdlx() + "\",\n";
                                WlgbJdYhk wlgbJdYhk = yhkCl(bm);
                                List<JSONObject> list1 = new ArrayList<>();
                                list3.forEach(l -> {
                                    JSONObject jsonObject = new JSONObject();
                                    //电汇
                                    jsonObject.put("FSETTLETYPEID", KingDeeConfig.zhdx("JSFS04_SYS"));
                                    //银行卡账号
                                    jsonObject.put("FACCOUNTID", KingDeeConfig.zhdx(wlgbJdYhk.getZh()));
                                    jsonObject.put("FPURPOSEID", KingDeeConfig.zhdx("SFKYT42_SYS"));
                                    jsonObject.put("FPAYTOTALAMOUNTFOR", l.getFyje());
                                    jsonObject.put("FPAYAMOUNTFOR_E", l.getFyje());
                                    jsonObject.put("FSETTLEPAYAMOUNTFOR", l.getFyje());
                                    jsonObject.put("FRecType", "1");
                                    jsonObject.put("FCOSTID", KingDeeConfig.zhdx(l.getFykmbm()));
                                    jsonObject.put("FMD", KingDeeConfig.zhdx(bm1));
                                    //部门
                                    jsonObject.put("FEXPENSEDEPTID_E", KingDeeConfig.zhdx(sqrbmid));
                                    jsonObject.put("FPAYAMOUNT_E", l.getFyje());
                                    jsonObject.put("FPOSTDATE", cnzzsj);
                                    jsonObject.put("FRuZhangType", "1");
                                    jsonObject.put("FPayType", "A");
                                    jsonObject.put("FNOTVERIFICATEAMOUNT", l.getFyje());
                                    jsonObject.put("FBankInvoice", false);
                                    jsonObject.put("FByAgentBank", false);
                                    jsonObject.put("FOverseaPay", false);
                                    list1.add(jsonObject);
                                });
                                json += "        \"FPAYBILLENTRY\": " + list1.toString() +
                                        "    }\n" +
                                        "}";
                                //录入金蝶
                                Map<String, String> map2 = KingDeeConfig.spTjJdDj2(json, "付款单");
                                save = "true".equals(map2.get("jg"));
                                if (!save) {
                                    sendGztz(0, wlgbJdLcyzjl.getSpbh(), "录入金蝶失败,原因：" + map2.get("msg"), "付款单");
                                }
                                if (save) {
                                    //上传单据成功之后将里面数据改成上传完成
                                    list3.forEach(l -> {
                                        WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                                        wlgbJdSpCwlrjdsj.setId(l.getId());
                                        wlgbJdSpCwlrjdsj.setSfscjd(1);
                                        wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj);
                                    });
                                }
                            }
                            if (save) {
                                if (type == 1) {
                                    WlgbJdLcyzjl wlgbJdYzsqjl1 = new WlgbJdLcyzjl();
                                    wlgbJdYzsqjl1.setId(wlgbJdLcyzjl.getId());
                                    wlgbJdYzsqjl1.setSflrjd(1);
                                    wlgbJdLcyzjlService.updateById(wlgbJdYzsqjl1);
                                }
                            }
                        } else {
                            sendGztz(0, wlgbJdLcyzjl.getSpbh(), "别墅不存在", "乐诚预支流程");
                        }
                    } else {
                        sendGztz(0, wlgbJdLcyzjl.getSpbh(), "别墅缺失，补充错误", "乐诚预支流程");
                    }
                } else {
                    sendGztz(0, wlgbJdLcyzjl.getSpbh(), "员工缺失，补充错误", "乐诚预支流程");
                }
            } else {
                sendGztz(0, wlgbJdLcyzjl.getSpbh(), "部门缺失，补充错误", "乐诚预支流程");
            }
        }
        return save;
    }

    /**
     * 乐诚采购流程处理
     *
     * @param spBh           审批编号
     * @param wlgbJdLccglcjl 乐诚采购流程数据
     * @param type           1数据库存在，0数据库未存在
     */
    public boolean lcCgLcCl(String spBh, WlgbJdLccglcjl wlgbJdLccglcjl, Integer type) {
        if (type == 1) {
            wlgbJdLccglcjl = wlgbJdLccglcjlService.queryBySpBhAndSfLrJd(spBh, 0);
        }
        boolean save = false;
        if (wlgbJdLccglcjl != null) {
            String sqrbmid = wlgbJdLccglcjl.getSqrbmid();
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd");
            String zzsj = df2.format(wlgbJdLccglcjl.getCnzzsj());
            String sqrid = wlgbJdLccglcjl.getSqrid();
            Boolean bmCl = KingDeeConfig.bmCl(sqrbmid, wlgbJdLccglcjl.getSqrbm());
            String fylx = wlgbJdLccglcjl.getSfdf();
            if (bmCl) {
                Boolean ryCl = KingDeeConfig.ryCl(sqrid, wlgbJdLccglcjl.getSqr());
                String bsmc = wlgbJdLccglcjl.getBsmc();
                if (ryCl) {
                    if (bsmc != null && !"".equals(bsmc)) {
                        //查询别墅表存不存在
                        TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(bsmc);
                        //如果不存在
                        if (tbVilla == null) {
                            //去别墅修改记录表查询
                            WlgbJdBsmcXgjl wlgbJdBsmcXgjl = wlgbJdBsmcXgjlService.queryByYnameAndSfSc(bsmc, 0);
                            //别墅修改记录表存在
                            if (wlgbJdBsmcXgjl != null) {
                                //把别墅名字给上传使用
                                bsmc = wlgbJdBsmcXgjl.getVname();
                            }
                        }
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        if (jsonObject1.getBoolean("success")) {
                            String bm1 = jsonObject1.getString("bm");
                            List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJd(12, wlgbJdLccglcjl.getSpbh(), 0);
                            List<String> list2 = new ArrayList<>();
                            list.forEach(l -> {
                                if (!list2.contains(l.getZtbm())) {
                                    list2.add(l.getZtbm());
                                }
                            });
                            for (String l : list2) {
                                List<WlgbJdSpCwlrjdsj> list3 = wlgbJdSpCwlrjdsjService.queryByTypeAndSpBhAndSfScJdAndBm(12, wlgbJdLccglcjl.getSpbh(), 0, l);
                                String json = "{\n" +
                                        "    \"NeedUpDateFields\": [],\n" +
                                        "    \"NeedReturnFields\": [],\n" +
                                        "    \"IsDeleteEntry\": \"true\",\n" +
                                        "    \"SubSystemId\": \"\",\n" +
                                        "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                        "    \"IsEntryBatchFill\": \"true\",\n" +
                                        "    \"ValidateFlag\": \"true\",\n" +
                                        "    \"NumberSearch\": \"true\",\n" +
                                        "    \"IsAutoAdjustField\": \"false\",\n" +
                                        "    \"InterationFlags\": \"\",\n" +
                                        "    \"IgnoreInterationFlag\": \"\",\n" +
                                        "    \"Model\": {\n" +
                                        "        \"FID\": 0,\n" +
                                        "        \"FBillTypeID\": {\n" +
                                        "            \"FNUMBER\": \"" + ("1".equals(fylx) ? "QTYFD01_SYS" : "QTYFD02_SYS") + "\"\n" +
                                        "        },\n" +
                                        "        \"FDATE\": \"" + zzsj + "\",\n" +
                                        "        \"FENDDATE_H\": \"" + zzsj + "\",\n" +
                                        "        \"FISINIT\": false,\n" +
                                        "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                        "        \"FCONTACTUNIT\": {\n" +
                                        "            \"FNumber\": \"" + sqrid + "\"\n" +
                                        "        },\n" +
                                        "        \"FCURRENCYID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FDEPARTMENTID\": {\n" +
                                        "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                        "        },\n" +
                                        "        \"FSETTLEORGID\": {\n" +
                                        "            \"FNumber\": \"" + l + "\"\n" +
                                        "        },\n" +
                                        "        \"FPURCHASEORGID\": {\n" +
                                        "            \"FNumber\": \"" + l + "\"\n" +
                                        "        },\n" +
                                        "        \"FPAYORGID\": {\n" +
                                        "            \"FNumber\": \"" + l + "\"\n" +
                                        "        },\n" +
                                        "        \"FMAINBOOKSTDCURRID\": {\n" +
                                        "            \"FNumber\": \"PRE001\"\n" +
                                        "        },\n" +
                                        "        \"FEXCHANGETYPE\": {\n" +
                                        "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                        "        },\n" +
                                        "        \"FExchangeRate\": 1.0,\n" +
                                        "        \"FACCNTTIMEJUDGETIME\": \"" + zzsj + "\",\n" +
                                        "        \"FCancelStatus\": \"A\",\n" +
                                        "        \"FBUSINESSTYPE\": \"T\",\n" +
                                        "        \"FRemarks\": \"" + wlgbJdLccglcjl.getSmyy() + "\",\n" +
                                        "        \"FSPSLBT\": \"" + wlgbJdLccglcjl.getSpbt() + "\",\n" +
                                        "        \"FSPBH\": \"" + wlgbJdLccglcjl.getSpbh() + "\",\n" +
                                        "        \"FSPSQR\": \"" + wlgbJdLccglcjl.getSqr() + "\",\n" +
                                        "        \"FSPSQRBM\": \"" + wlgbJdLccglcjl.getSqrbm() + "\",\n";
                                List<JSONObject> list1 = new ArrayList<>();
                                list3.forEach(l1 -> {
                                    JSONObject jsonObject2 = scQtYfdMx(bm1, l1.getFykmbm(), l1.getFyje(), sqrbmid);
                                    list1.add(jsonObject2);
                                });
                                json += "        \"FEntity\": " + list1.toString() +
                                        "    }\n" +
                                        "}";
                                Map<String, String> map1 = KingDeeConfig.spTjJdDj2(json, "其他应付单");
                                save = "true".equals(map1.get("jg"));
                                if (!save) {
                                    sendGztz(0, wlgbJdLccglcjl.getSpbh(), "录入金蝶失败,原因：" + map1.get("msg"), "其他应付单");
                                }
                                if (save) {
                                    String json1 = "{\n" +
                                            "    \"NeedUpDateFields\": [],\n" +
                                            "    \"NeedReturnFields\": [],\n" +
                                            "    \"IsDeleteEntry\": \"true\",\n" +
                                            "    \"SubSystemId\": \"\",\n" +
                                            "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                            "    \"IsEntryBatchFill\": \"true\",\n" +
                                            "    \"ValidateFlag\": \"true\",\n" +
                                            "    \"NumberSearch\": \"true\",\n" +
                                            "    \"IsAutoAdjustField\": \"false\",\n" +
                                            "    \"InterationFlags\": \"\",\n" +
                                            "    \"IgnoreInterationFlag\": \"\",\n" +
                                            "    \"Model\": {\n" +
                                            "        \"FID\": 0,\n" +
                                            "        \"FBillTypeID\": {\n" +
                                            "            \"FNUMBER\": \"" + ("1".equals(fylx) ? "FKDLX02_SYS" : "FKDLX04_SYS") + "\"\n" +
                                            "        },\n" +
                                            "        \"FDATE\": \"" + zzsj + "\",\n" +
                                            "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FCONTACTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + sqrid + "\"\n" +
                                            "        },\n" +
                                            "        \"FRECTUNITTYPE\": \"BD_Empinfo\",\n" +
                                            "        \"FRECTUNIT\": {\n" +
                                            "            \"FNumber\": \"" + sqrid + "\"\n" +
                                            "        },\n" +
                                            "        \"FDepartment\": {\n" +
                                            "            \"FNumber\": \"" + sqrbmid + "\"\n" +
                                            "        },\n" +
                                            "        \"FISINIT\": false,\n" +
                                            "        \"FCURRENCYID\": {\n" +
                                            "            \"FNumber\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FEXCHANGERATE\": 1.0,\n" +
                                            "        \"FSETTLERATE\": 1.0,\n" +
                                            "        \"FSETTLEORGID\": {\n" +
                                            "            \"FNumber\": \"" + l + "\"\n" +
                                            "        },\n" +
                                            "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                                            "        \"FBUSINESSTYPE\": \"3\",\n" +
                                            "        \"FCancelStatus\": \"A\",\n" +
                                            "        \"FPAYORGID\": {\n" +
                                            "            \"FNumber\": \"" + l + "\"\n" +
                                            "        },\n" +
                                            "        \"FISSAMEORG\": true,\n" +
                                            "        \"FIsCredit\": false,\n" +
                                            "        \"FSETTLECUR\": {\n" +
                                            "            \"FNUMBER\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FIsWriteOff\": false,\n" +
                                            "        \"FREALPAY\": false,\n" +
                                            "        \"FISCARRYRATE\": false,\n" +
                                            "        \"FSETTLEMAINBOOKID\": {\n" +
                                            "            \"FNUMBER\": \"PRE001\"\n" +
                                            "        },\n" +
                                            "        \"FMoreReceive\": false,\n" +
                                            "        \"FVirIsSameAcctOrg\": false,\n" +
                                            "        \"FREMARK\": \"" + wlgbJdLccglcjl.getSmyy() + "\",\n" +
                                            "        \"FSPBT\": \"" + wlgbJdLccglcjl.getSpbt() + "\",\n" +
                                            "        \"FSPBH\": \"" + wlgbJdLccglcjl.getSpbh() + "\",\n" +
                                            "        \"FSPSQR\": \"" + wlgbJdLccglcjl.getSqr() + "\",\n" +
                                            "        \"FSPSQRBM\": \"" + wlgbJdLccglcjl.getSqrbm() + "\",\n";
                                    WlgbJdYhk wlgbJdYhk = yhkCl(l);
                                    List<JSONObject> list4 = new ArrayList<>();
                                    list1.forEach(l2 -> {
                                        String fcostid = l2.getJSONObject("FCOSTID").getString("FNumber");
                                        String fssmd = l2.getJSONObject("FSSMD").getString("FNumber");
                                        Double fnotaxamountfor = l2.getDouble("FNOTAXAMOUNTFOR");
                                        JSONObject jsonObject2 = fkdMx1(fssmd, fnotaxamountfor, zzsj, fcostid, sqrbmid, ("1".equals(fylx) ? "SFKYT20_SYS" : "SFKYT10_SYS"), wlgbJdYhk.getZh());

                                        list4.add(jsonObject2);
                                    });
                                    json1 += "        \"FPAYBILLENTRY\": " + list4.toString() +
                                            "    }\n" +
                                            "}";
                                    //录入金蝶
                                    Map<String, String> map2 = KingDeeConfig.spTjJdDj2(json1, "付款单");
                                    save = "true".equals(map2.get("jg"));
                                    if (!save) {
                                        sendGztz(0, wlgbJdLccglcjl.getSpbh(), "录入金蝶失败,原因：" + map2.get("msg"), "付款单");
                                    }
                                    if (save) {
                                        list3.forEach(l1 -> {
                                            WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj1 = new WlgbJdSpCwlrjdsj();
                                            wlgbJdSpCwlrjdsj1.setId(l1.getId());
                                            wlgbJdSpCwlrjdsj1.setSfscjd(1);
                                            wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj1);
                                        });
                                    }
                                }
                            }
                        } else {
                            sendGztz(0, wlgbJdLccglcjl.getSpbh(), "别墅不存在", "乐诚采购流程");
                        }
                        if (save) {
                            if (type == 1) {
                                WlgbJdLccglcjl wlgbJdLccglcjl1 = new WlgbJdLccglcjl();
                                wlgbJdLccglcjl1.setId(wlgbJdLccglcjl.getId());
                                wlgbJdLccglcjl1.setSflrjd(1);
                                wlgbJdLccglcjlService.updateById(wlgbJdLccglcjl1);
                            }
                        }
                    } else {
                        sendGztz(0, wlgbJdLccglcjl.getSpbh(), "别墅缺失，补充错误", "乐诚采购流程");
                    }
                } else {
                    sendGztz(0, wlgbJdLccglcjl.getSpbh(), "员工缺失，补充错误", "乐诚采购流程");
                }
            } else {
                sendGztz(0, wlgbJdLccglcjl.getSpbh(), "部门缺失，补充错误", "乐诚采购流程");
            }
        }
        return save;
    }

    /**
     * 退定金审批处理
     */
    public boolean tdjCl(String spbh, WlgbJdTdjsqjl l, Integer type) {
        System.out.println("进入tdjCl");
        boolean save = false;
        if (type == 1) {
            l = wlgbJdTdjsqjlService.queryBySpBhAndSfLrJd(spbh, 0);
        }
        if (l != null) {
            SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (!"新老客户返现".equals(l.getFylb())) {
                String json = "{\n" +
                        "    \"NeedUpDateFields\": [],\n" +
                        "    \"NeedReturnFields\": [],\n" +
                        "    \"IsDeleteEntry\": \"true\",\n" +
                        "    \"SubSystemId\": \"\",\n" +
                        "    \"IsVerifyBaseDataField\": \"false\",\n" +
                        "    \"IsEntryBatchFill\": \"true\",\n" +
                        "    \"ValidateFlag\": \"true\",\n" +
                        "    \"NumberSearch\": \"true\",\n" +
                        "    \"IsAutoAdjustField\": \"false\",\n" +
                        "    \"InterationFlags\": \"\",\n" +
                        "    \"IgnoreInterationFlag\": \"\",\n" +
                        "    \"Model\": {\n" +
                        "        \"FID\": 0,\n" +
                        "        \"FBillTypeID\": {\n" +
                        "            \"FNUMBER\": \"SKTKDLX03_SYS\"\n" +
                        "        },\n" +
                        "        \"FDATE\": \"" + df2.format(l.getDkrq()) + "\",\n" +
                        "        \"FCONTACTUNITTYPE\": \"BD_Customer\",\n" +
                        "        \"FISINIT\": false,\n" +
                        "        \"FCONTACTUNIT\": {\n" +
                        "            \"FNumber\": \"CUST0004\"\n" +
                        "        },\n" +
                        "        \"FSETTLERATE\": 1.0,\n" +
                        "        \"FDOCUMENTSTATUS\": \"Z\",\n" +
                        "        \"FRECTUNITTYPE\": \"BD_Customer\",\n" +
                        "        \"FRECTUNIT\": {\n" +
                        "            \"FNumber\": \"CUST0004\"\n" +
                        "        },\n" +
                        "        \"FCURRENCYID\": {\n" +
                        "            \"FNumber\": \"PRE001\"\n" +
                        "        },\n" +
                        "        \"FSETTLEORGID\": {\n" +
                        "            \"FNumber\": \"" + l.getZtbm() + "\"\n" +
                        "        },\n" +
                        "        \"FSALEORGID\": {\n" +
                        "            \"FNumber\": \"" + l.getZtbm() + "\"\n" +
                        "        },\n" +
                        "        \"FBUSINESSTYPE\": \"3\",\n" +
                        "        \"FEXCHANGERATE\": 1.0,\n" +
                        "        \"FCancelStatus\": \"A\",\n" +
                        "        \"FPAYORGID\": {\n" +
                        "            \"FNumber\": \"" + l.getZtbm() + "\"\n" +
                        "        },\n" +
                        "        \"FISSAMEORG\": true,\n" +
                        "        \"FSETTLECUR\": {\n" +
                        "            \"FNUMBER\": \"PRE001\"\n" +
                        "        },\n" +
                        "        \"FISB2C\": false,\n" +
                        "        \"FIsWriteOff\": false,\n" +
                        "        \"FISCARRYRATE\": false,\n" +
                        "        \"FSETTLEMAINBOOKID\": {\n" +
                        "            \"FNUMBER\": \"PRE001\"\n" +
                        "        },\n" +
                        "        \"FVirIsSameAcctOrg\": false,\n" +
                        "        \"FREMARK\": \"" + l.getBz() + "\",\n" +
                        "        \"F_abc_Text1\": \"" + l.getSlbt() + "\",\n" +
                        "        \"F_abc_Text\": \"" + l.getSpbh() + "\",\n";

                //无订单定金
                if ("已下单退线下定金".equals(l.getFylb()) || "已下单退线上定金".equals(l.getFylb())) {
                    //有客户定金
                    TbXyd tbXyd = tbXydService.queryByDdBh2(l.getDdbh());
                    TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
                    JSONObject jsonObject2 = KingDeeConfig.queryBsSfCz(villa.getVname());
                    if (jsonObject2.getBoolean("success")) {
                        json += "        \"F_abc_Base\": {\n" +
                                "            \"FNUMBER\": \"" + jsonObject2.getString("bm") + "\"\n" +
                                "        },\n";
                    }
                    json += "        \"F_abc_Text12\": \"" + tbXyd.getXddbh() + "\",\n";
                }
                json += "        \"FREFUNDBILLENTRY\": [\n" +
                        "            {\n" +
                        "                \"FSETTLETYPEID\": {\n" +
                        "                    \"FNumber\": \"JSFS01_SYS\"\n" +
                        "                },\n" +
                        "                \"FPURPOSEID\": {\n" +
                        "                    \"FNumber\": \"SFKYT41_SYS\"\n" +
                        "                },\n" +
                        "                \"FREFUNDAMOUNTFOR\": " + l.getDkje() + ",\n" +
                        "                \"FREFUNDAMOUNTFOR_E\": " + l.getDkje() + ",\n" +
                        "                \"FREFUNDAMOUNT_E\": " + l.getDkje() + ",\n" +
                        "                \"FPOSTDATE\": \"" + df2.format(l.getDkrq()) + "\",\n" +
                        "                \"FISPOST\": true,\n" +
                        "                \"FRuZhangType\": \"1\",\n" +
                        "                \"FPayType\": \"A\",\n" +
                        "                \"FNOTVERIFICATEAMOUNT\": " + l.getDkje() + ",\n" +
                        "                \"FByAgentBank\": false,\n" +
                        "                \"FOverseaPay\": false,\n" +
                        "                \"FCOSTID\": {\n" +
                        "                    \"FNUMBER\": \"" + l.getKmbm() + "\"\n" +
                        "                },\n" +
                        "                \"FCOSTDEPARTMENTID\": {\n" +
                        "                    \"FNUMBER\": \"" + l.getSqrbmid() + "\"\n" +
                        "                }\n" +
                        "            }\n" +
                        "        ]\n" +
                        "    }\n" +
                        "}";
                Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
                Boolean bmCl = KingDeeConfig.bmCl(l.getSqrbmid(), l.getSqrbm());//部门处理
                if (bmCl) {
                    Boolean ryCl = KingDeeConfig.ryCl(l.getSqrid(), l.getSqr());//人员处理
                    if (ryCl) {
                        save = KingDeeConfig.tdjSkTkdSave(json);
                        if (save) {
                            l.setSflrjd(1);
                            YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
                            String token = null;
                            try {
                                token = DingToken.token(dingkey);
                            } catch (ApiException e) {
                                e.printStackTrace();
                            }
                            if (l.getSjslid() != null && !"".equals(l.getSjslid())) {
                                JSONObject jsonObject1 = new JSONObject();
                                jsonObject1.put("ytje", l.getDkje());
                                jsonObject1.put("sfxd", 4);
                                for (int i = 0; i < 5; i++) {
                                    GatewayResult gatewayResult = null;
                                    try {
                                        gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, l.getSqrid(), l.getSjslid(), jsonObject1.toJSONString());
                                    } catch (Exception e) {
                                        gatewayResult = new GatewayResult();
                                        e.printStackTrace();
                                    }
                                    if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                                        if (i == 4) {
                                            try {
                                                DingQunSend.send("退定金修改定金表字段，审批流水号：" + l.getSpbh() + "，错误原因：" + gatewayResult.toString(), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                            } catch (ApiException e) {
                                                e.printStackTrace();
                                            }
                                        }
                                    } else {
                                        break;
                                    }
                                }
                            } else {
                                List<WlgbJdDjbbd> list = wlgbJdDjbbdService.queryByLshAndSfSc(l.getZfjydh());
                                double sum = 0.0;
                                for (int i = 0; i < list.size(); i++) {
                                    JSONObject jsonObject1 = new JSONObject();
                                    jsonObject1.put("sfxd", 4);
                                    double je = l.getDkje() / list.size();
                                    BigDecimal bigDecimal = new BigDecimal(je);
                                    jsonObject1.put("ytje", bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                                    sum += bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                                    if (i == list.size() - 1) {
                                        if (sum < l.getDkje()) {
                                            double ce = l.getDkje() - sum;
                                            double bce = je + ce;
                                            BigDecimal bigDecimal2 = new BigDecimal(bce);
                                            jsonObject1.put("ytje", bigDecimal2.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                                        }
                                    }
                                    for (int j = 0; j < 5; j++) {
                                        GatewayResult gatewayResult = null;
                                        try {
                                            gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, l.getSqrid(), list.get(i).getFormInstId(), jsonObject1.toJSONString());
                                        } catch (Exception e) {
                                            gatewayResult = new GatewayResult();
                                            e.printStackTrace();
                                        }
                                        if (gatewayResult.getSuccess() != null && !gatewayResult.getSuccess()) {
                                            if (j == 4) {
                                                try {
                                                    DingQunSend.send("退定金修改定金表字段，审批流水号：" + l.getSpbh() + "，错误原因：" + gatewayResult.toString(), dingkey, "chate5bd183f6a0ebdd18e057550c487e5cc");
                                                } catch (ApiException e) {
                                                    e.printStackTrace();
                                                }
                                            }
                                        } else {
                                            break;
                                        }
                                    }
                                }
                            }
                        } else {
                            String text = "退定金审批传入金蝶收款退款单出错了！";
                            text += "\n\n审批编号：" + l.getSpbh();
                            text += "\n审批标题：" + l.getSlbt();
                            text += "\n费用类别：" + l.getFylb();
                            text += "\n送达时间：" + df2.format(new Date());
                            try {
                                DingDBConfig.sendGztzText(dingkey, "15349026426046931", text);
                            } catch (ApiException e) {
                                e.printStackTrace();
                            }
                        }
                    } else {
                        sendGztz(0, l.getSpbh(), "员工缺失，补充错误", "退定金");
                    }
                } else {
                    sendGztz(0, l.getSpbh(), "部门缺失，补充错误", "退定金");
                }
            }
            else {
                TbXyd tbXyd = tbXydService.queryByDdBh2(l.getDdbh());
                TbVilla villa = weiLianDdXcxService.queryTbVillaById(tbXyd.getXbsmc());
                Boolean bmCl = KingDeeConfig.bmCl(l.getSqrbmid(), l.getSqrbm());
                if (bmCl) {
                    Boolean ryCl = KingDeeConfig.ryCl(l.getSqrid(), l.getSqr());
                    if (ryCl) {
                        JSONObject jsonObject2 = KingDeeConfig.queryBsSfCz(villa.getVname());
                        Boolean success = jsonObject2.getBoolean("success");
                        if (success) {
                            String bm = jsonObject2.getString("bm");
                            String json = "{\n" +
                                    "    \"NeedUpDateFields\": [],\n" +
                                    "    \"NeedReturnFields\": [],\n" +
                                    "    \"IsDeleteEntry\": \"true\",\n" +
                                    "    \"SubSystemId\": \"\",\n" +
                                    "    \"IsVerifyBaseDataField\": \"false\",\n" +
                                    "    \"IsEntryBatchFill\": \"true\",\n" +
                                    "    \"ValidateFlag\": \"true\",\n" +
                                    "    \"NumberSearch\": \"true\",\n" +
                                    "    \"IsAutoAdjustField\": \"false\",\n" +
                                    "    \"InterationFlags\": \"\",\n" +
                                    "    \"IgnoreInterationFlag\": \"\",\n" +
                                    "    \"Model\": {\n" +
                                    "        \"FID\": 0,\n" +
                                    "        \"FBillTypeID\": {\n" +
                                    "            \"FNUMBER\": \"QTYFD02_SYS\"\n" +
                                    "        },\n" +
                                    "        \"FDATE\": \"" + df2.format(l.getDkrq()) + "\",\n" +
                                    "        \"FENDDATE_H\": \"" + df2.format(l.getDkrq()) + "\",\n" +
                                    "        \"FISINIT\": false,\n" +
                                    "        \"FCONTACTUNITTYPE\": \"BD_Empinfo\",\n" +
                                    "        \"FCONTACTUNIT\": {\n" +
                                    "            \"FNumber\": \"" + l.getSqrid() + "\"\n" +
                                    "        },\n" +
                                    "        \"FCURRENCYID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FTOTALAMOUNTFOR_H\": " + l.getDkje() + ",\n" +
                                    "        \"FNOTSETTLEAMOUNTFOR\": " + l.getDkje() + ",\n" +
                                    "        \"FDEPARTMENTID\": {\n" +
                                    "            \"FNumber\": \"" + l.getSqrbmid() + "\"\n" +
                                    "        },\n" +
                                    "        \"FSETTLEORGID\": {\n" +
                                    "            \"FNumber\": \"" + l.getZtbm() + "\"\n" +
                                    "        },\n" +
                                    "        \"FPURCHASEORGID\": {\n" +
                                    "            \"FNumber\": \"" + l.getZtbm() + "\"\n" +
                                    "        },\n" +
                                    "        \"FPAYORGID\": {\n" +
                                    "            \"FNumber\": \"" + l.getZtbm() + "\"\n" +
                                    "        },\n" +
                                    "        \"FMAINBOOKSTDCURRID\": {\n" +
                                    "            \"FNumber\": \"PRE001\"\n" +
                                    "        },\n" +
                                    "        \"FEXCHANGETYPE\": {\n" +
                                    "            \"FNumber\": \"HLTX01_SYS\"\n" +
                                    "        },\n" +
                                    "        \"FExchangeRate\": 1.0,\n" +
                                    "        \"FNOTAXAMOUNT\": " + l.getDkje() + ",\n" +
                                    "        \"FACCNTTIMEJUDGETIME\": \"" + df2.format(l.getDkrq()) + "\",\n" +
                                    "        \"FCancelStatus\": \"A\",\n" +
                                    "        \"FBUSINESSTYPE\": \"T\",\n" +
                                    "        \"FXYDBH\": \"" + l.getDdbh() + "\",\n" +
                                    "        \"FSPSLBT\": \"" + l.getSlbt() + "\",\n" +
                                    "        \"FSPBH\": \"" + l.getSpbh() + "\",\n" +
                                    "        \"FSPSQR\": \"" + l.getSqr() + "\",\n" +
                                    "        \"FSPSQRBM\": \"" + l.getSqrbm() + "\",\n" +
                                    "        \"FEntity\": [\n" +
                                    "            {\n" +
                                    "                \"FCOSTID\": {\n" +
                                    "                    \"FNumber\": \"" + l.getKmbm() + "\"\n" +
                                    "                },\n" +
                                    "                \"FCOSTDEPARTMENTID\": {\n" +
                                    "                    \"FNumber\": \"" + l.getSqrbmid() + "\"\n" +
                                    "                },\n" +
                                    "                \"FSSMD\": {\n" +
                                    "                    \"FNUMBER\": \"" + bm + "\"\n" +
                                    "                },\n" +
                                    "                \"FNOTAXAMOUNTFOR\": " + l.getDkje() + ",\n" +
                                    "                \"FTOTALAMOUNTFOR\": " + l.getDkje() + ",\n" +
                                    "                \"FNOTSETTLEAMOUNTFOR_D\": " + l.getDkje() + ",\n" +
                                    "                \"FNOTAXAMOUNT_D\": " + l.getDkje() + ",\n" +
                                    "                \"FCREATEINVOICE\": false\n" +
                                    "            }\n" +
                                    "        ]\n" +
                                    "    }\n" +
                                    "}";
                            Map<String, String> map2 = KingDeeConfig.spTjJdDj2(json, "退定金-客户返现");
                            save = "true".equals(map2.get("jg"));
                            if (!save) {
                                sendGztz(0, l.getSpbh(), "录入金蝶失败,原因：" + map2.get("msg"), "退定金-客户返现");
                            }
                            if (save) {
                                l.setSflrjd(1);
                            }
                        } else {
                            sendGztz(0, l.getSpbh(), "门店缺失，异常", "退定金");
                        }
                    } else {
                        sendGztz(0, l.getSpbh(), "员工缺失，补充错误", "退定金");
                    }
                } else {
                    sendGztz(0, l.getSpbh(), "部门缺失，补充错误", "退定金");
                }
            }
        }
        if (save) {
            if (type == 1) {
                WlgbJdTdjsqjl wlgbJdTdjsqjl = new WlgbJdTdjsqjl();
                wlgbJdTdjsqjl.setId(l.getId());
                wlgbJdTdjsqjl.setSflrjd(1);
                wlgbJdTdjsqjlService.updateById(wlgbJdTdjsqjl);
            }
        }
        return save;
    }


    /**
     * 分摊
     *
     * @param list2       账套集合
     * @param type        单据类型
     * @param p           当前账套循环的序号
     * @param je          金额
     * @param list3       财务录入金蝶数据
     * @param jsonObject3 审批单部分内容
     * @param sqsj        申请时间
     * @return 金额与单据详情
     */
    public JSONObject ft1(List<String> list2, JSONObject jsonObject3, Integer type, Integer p, Double
            je, List<WlgbJdSpCwlrjdsj> list3, Date sqsj, String bmid, Integer sfxft) {
        String spbh = jsonObject3.getString("spbh");
        String fyssft = jsonObject3.getString("fyssft");
        String sfsymdft = jsonObject3.getString("sfsymdft");
        String cnzzsj = jsonObject3.getString("cnzzsj");
        Double djFyZje = jsonObject3.getDouble("djFyZje");
        List<JSONObject> list1 = new ArrayList<>();
        Boolean success = true;
        Boolean success1 = true;
        if ("总部分摊".equals(fyssft)) {
            list3.forEach(l -> {
                JSONObject jsonObject;
                if (type == 0 || type == 9 || type == 2) {
                    jsonObject = scQtYfdMx(null, l.getFykmbm(), l.getFyje(), bmid);
                } else {
                    jsonObject = scFkTkdMx(null, l.getFyje(), l.getFykmbm(), cnzzsj, bmid);
                }
                list1.add(jsonObject);
            });
        } else if ("别墅分摊".equals(fyssft) || "城市别墅分摊".equals(fyssft)) {
            if (sfxft == 1) {
                for (WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj : list3) {
                    String bsmc = wlgbJdSpCwlrjdsj.getBsmc();
                    String bsbh = wlgbJdSpCwlrjdsj.getBsbh();
                    if (bsbh == null || "".equals(bsbh)) {
                        //查询别墅表存不存在
                        TbVilla tbVilla = weiLianService.queryVillaByVname(bsmc);
                        //如果不存在
                        if (tbVilla == null) {
                            //去别墅修改记录表查询
                            WlgbJdBsmcXgjl wlgbJdBsmcXgjl = wlgbJdBsmcXgjlService.queryByYnameAndSfSc(bsmc, 0);
                            //别墅修改记录表存在
                            if (wlgbJdBsmcXgjl != null) {
                                //把别墅名字给上传使用
                                bsmc = wlgbJdBsmcXgjl.getVname();
                            }
                        }
                        if (bsmc != null && !"".equals(bsmc)) {
                            JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                            success = jsonObject1.getBoolean("success");
                            if (success) {
                                bsbh = jsonObject1.getString("bm");
                            }
                        }
                    }
                    JSONObject jsonObject;
                    if (type == 0 || type == 9 || type == 2) {
                        jsonObject = scQtYfdMx(bsbh, wlgbJdSpCwlrjdsj.getFykmbm(), wlgbJdSpCwlrjdsj.getFyje(), bmid);
                    } else {
                        jsonObject = scFkTkdMx(bsbh, wlgbJdSpCwlrjdsj.getFyje(), wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                    }
                    list1.add(jsonObject);

                    if (success1) {
                        success1 = success;
                    }
                }
            } else {
                List<WlgbJdSpBsft> list4 = wlgbJdSpBsftService.queryByStatueAndTypeAndSpBh(type, 1, spbh);
                for (WlgbJdSpBsft wlgbJdSpBsft : list4) {
                    je = 0.0;
                    String bsmc = wlgbJdSpBsft.getBsmc();
                    TbVilla tbVilla = weiLianService.queryVillaByVname(bsmc);
                    if (tbVilla != null) {
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        success = jsonObject1.getBoolean("success");
                        if (success) {
                            String bm = jsonObject1.getString("bm");
                            for (int i = 0; i < list3.size(); i++) {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(i);
                                double sum = wlgbJdSpBsft.getJe() / list3.size();
                                sum = ((int) (sum * 100)) / 100.0;
                                je += sum;
                                if (i == list3.size() - 1) {
                                    double ce = wlgbJdSpBsft.getJe() - je;
                                    sum += ce;
                                }
                                JSONObject jsonObject;
                                if (type == 0 || type == 9 || type == 2) {
                                    jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                } else {
                                    jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                }
                                list1.add(jsonObject);
                            }
                        }
                        if (success1) {
                            success1 = success;
                        }
                    }

                }
            }
        } else {
            boolean b = sfsymdft == null || "".equals(sfsymdft) || "是".equals(sfsymdft);
            if ("全国所有别墅分摊".equals(fyssft)) {
                List<String> bsXz = bsQbXzCl();
                List<String> list5 = bsJmZyXzCl(list3.get(0).getZt());
                if (b) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("time", sqsj);
                    map.put("list", list5);
                    List<TbVilla> list8 = weiLianService.queryKyQnMdList(map);
                    map.put("list", bsXz);
                    int count = weiLianService.queryKyQnMdCount(map);
                    for (int k = 0; k < list8.size(); k++) {
                        TbVilla villa = list8.get(k);
                        //如果门店不存在这些数据将先不录入，后期补录，发送工作通知
                        String bsmc = villa.getVname().replace("★", "");
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        success = jsonObject1.getBoolean("success");
                        if (success) {
                            String bm = jsonObject1.getString("bm");
                            for (int i = 0; i < list3.size(); i++) {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(i);
                                double sum = (djFyZje / count) / list3.size();
                                sum = ((int) (sum * 100)) / 100.0;
                                je += sum;
                                if (p == list2.size() - 1) {
                                    if (k == list8.size() - 1) {
                                        if (i == list3.size() - 1) {
                                            double ce = djFyZje - je;
                                            sum += ce;
                                        }
                                    }
                                }
                                JSONObject jsonObject;
                                if (type == 0 || type == 9 || type == 2) {
                                    jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                } else {
                                    jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                }
                                list1.add(jsonObject);
                            }
                        }
                        if (success1) {
                            success1 = success;
                        }
                    }
                } else {
                    List<WlgbJdSpBsft> list4 = wlgbJdSpBsftService.queryByStatueAndTypeAndSpBh(type, 2, spbh);
                    List<String> list6 = new ArrayList<>();
                    double zje = 0.0;
                    for (WlgbJdSpBsft wlgbJdSpBsft : list4) {
                        zje += wlgbJdSpBsft.getJe();
                        list6.add(wlgbJdSpBsft.getBsmc());
                        Map<String, Object> map = new HashMap<>();
                        map.put("time", sqsj);
                        map.put("list", list5);
                        map.put("bsmc", wlgbJdSpBsft.getBsmc());
                        int count = weiLianService.queryKyQnMdCount(map);
                        if (count > 0) {
                            JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(wlgbJdSpBsft.getBsmc());
                            success = jsonObject1.getBoolean("success");
                            if (success) {
                                String bm = jsonObject1.getString("bm");
                                for (int i = 0; i < list3.size(); i++) {
                                    WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(i);
                                    double sum = wlgbJdSpBsft.getJe() / list3.size();
                                    sum = ((int) (sum * 100)) / 100.0;
                                    je += sum;
                                    JSONObject jsonObject;
                                    if (type == 0 || type == 9 || type == 2) {
                                        jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                    } else {
                                        jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                    }
                                    list1.add(jsonObject);
                                }
                            }
                        }
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("time", sqsj);
                    map.put("list", list5);
                    map.put("list1", list6);
                    List<TbVilla> list8 = weiLianService.queryKyQnMdList(map);
                    map.put("list", bsXz);
                    int count = weiLianService.queryKyQnMdCount(map);

                    for (int i = 0; i < list8.size(); i++) {
                        TbVilla villa = list8.get(i);
                        //如果门店不存在这些数据将先不录入，后期补录，发送工作通知
                        String bsmc = villa.getVname().replace("★", "");
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        success = jsonObject1.getBoolean("success");
                        if (success) {
                            String bm = jsonObject1.getString("bm");
                            for (int j = 0; j < list3.size(); j++) {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(j);
                                double sum = ((djFyZje - zje) / count) / list3.size();
                                sum = ((int) (sum * 100)) / 100.0;
                                je += sum;
                                if (p == list2.size() - 1) {
                                    if (i == list8.size() - 1) {
                                        if (j == list3.size() - 1) {
                                            double ce = djFyZje - je;
                                            sum += ce;
                                        }
                                    }
                                }
                                JSONObject jsonObject;
                                if (type == 0 || type == 9 || type == 2) {
                                    jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                } else {
                                    jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                }
                                list1.add(jsonObject);
                            }
                        }
                        if (success1) {
                            success1 = success;
                        }
                    }
                }
            } else if ("全国加盟别墅分摊".equals(fyssft) || "全国直营别墅分摊".equals(fyssft)) {
                boolean jm = "全国加盟别墅分摊".equals(fyssft);
                List<String> list5 = bsJmZyXzCl(jm ? "加盟" : "直营");
                if (b) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("time", sqsj);
                    map.put("list", list5);
                    List<TbVilla> list8 = weiLianService.queryKyQnMdList(map);
                    for (int k = 0; k < list8.size(); k++) {
                        TbVilla villa = list8.get(k);
                        //如果门店不存在这些数据将先不录入，后期补录，发送工作通知
                        String bsmc = villa.getVname().replace("★", "");
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        success = jsonObject1.getBoolean("success");
                        if (success) {
                            String bm = jsonObject1.getString("bm");
                            for (int i = 0; i < list3.size(); i++) {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(i);
                                double sum = (djFyZje / list8.size()) / list3.size();
                                sum = ((int) (sum * 100)) / 100.0;
                                je += sum;
                                if (p == list2.size() - 1) {
                                    if (k == list8.size() - 1) {
                                        if (i == list3.size() - 1) {
                                            double ce = djFyZje - je;
                                            sum += ce;
                                        }
                                    }
                                }
                                JSONObject jsonObject;
                                if (type == 0 || type == 9 || type == 2) {
                                    jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                } else {
                                    jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                }
                                list1.add(jsonObject);
                            }
                        }
                        if (success1) {
                            success1 = success;
                        }
                    }
                } else {
                    List<WlgbJdSpBsft> list4 = wlgbJdSpBsftService.queryByStatueAndTypeAndSpBh(type, 2, spbh);
                    List<String> list6 = new ArrayList<>();
                    double zje = 0.0;
                    for (WlgbJdSpBsft wlgbJdSpBsft : list4) {
                        zje += wlgbJdSpBsft.getJe();
                        list6.add(wlgbJdSpBsft.getBsmc());
                        Map<String, Object> map = new HashMap<>();
                        map.put("time", sqsj);
                        map.put("list", list5);
                        map.put("bsmc", wlgbJdSpBsft.getBsmc());
                        int count = weiLianService.queryKyQnMdCount(map);
                        if (count > 0) {
                            //如果门店不存在这些数据将先不录入，后期补录，发送工作通知
                            String bsmc = wlgbJdSpBsft.getBsmc().replace("★", "");
                            JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                            success = jsonObject1.getBoolean("success");
                            if (success) {
                                String bm = jsonObject1.getString("bm");
                                for (int i = 0; i < list3.size(); i++) {
                                    WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(i);
                                    double sum = wlgbJdSpBsft.getJe() / list3.size();
                                    sum = ((int) (sum * 100)) / 100.0;
                                    je += sum;
                                    JSONObject jsonObject;
                                    if (type == 0 || type == 9 || type == 2) {
                                        jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                    } else {
                                        jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                    }
                                    list1.add(jsonObject);
                                }
                            }
                        }
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("time", sqsj);
                    map.put("list", list5);
                    map.put("list1", list6);
                    List<TbVilla> list8 = weiLianService.queryKyQnMdList(map);
                    for (int i = 0; i < list8.size(); i++) {
                        TbVilla villa = list8.get(i);
                        //如果门店不存在这些数据将先不录入，后期补录，发送工作通知
                        String bsmc = villa.getVname().replace("★", "");
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        success = jsonObject1.getBoolean("success");
                        if (success) {
                            String bm = jsonObject1.getString("bm");
                            for (int j = 0; j < list3.size(); j++) {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(j);
                                double sum = ((djFyZje - zje) / list8.size()) / list3.size();
                                sum = ((int) (sum * 100)) / 100.0;
                                je += sum;
                                if (p == list2.size() - 1) {
                                    if (i == list8.size() - 1) {
                                        if (j == list3.size() - 1) {
                                            double ce = djFyZje - je;
                                            sum += ce;
                                        }
                                    }
                                }
                                JSONObject jsonObject;
                                if (type == 0 || type == 9 || type == 2) {
                                    jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                } else {
                                    jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                }
                                list1.add(jsonObject);
                            }
                        }
                        if (success1) {
                            success1 = success;
                        }
                    }
                }
            } else if ("城市所有别墅分摊".equals(fyssft)) {
                List<WlgbJdSpCsft> list4 = wlgbJdSpCsftService.queryByStatueAndSpbh(type, spbh);
                String zt = list3.get(0).getZt();
                List<String> list5 = bsJmZyXzCl(zt);
                List<String> bsXz = bsQbXzCl();
                List<String> csList = new ArrayList<>();
                list4.forEach(l -> csList.add(l.getCity()));
                if (b) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("time", sqsj);
                    map.put("list", list5);
                    map.put("list2", csList);
                    List<TbVilla> list8 = weiLianService.queryKyQnMdList(map);
                    map.put("list", bsXz);
                    int count = weiLianService.queryKyQnMdCount(map);
                    for (int i = 0; i < list8.size(); i++) {
                        TbVilla villa = list8.get(i);
                        //如果门店不存在这些数据将先不录入，后期补录，发送工作通知
                        String bsmc = villa.getVname().replace("★", "");
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        success = jsonObject1.getBoolean("success");
                        if (success) {
                            String bm = jsonObject1.getString("bm");
                            for (int j = 0; j < list3.size(); j++) {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(j);
                                double sum = (djFyZje / count) / list3.size();
                                sum = ((int) (sum * 100)) / 100.0;
                                je += sum;
                                if (p == list2.size() - 1) {
                                    if (i == list8.size() - 1) {
                                        if (j == list3.size() - 1) {
                                            double ce = djFyZje - je;
                                            sum += ce;
                                        }
                                    }
                                }
                                JSONObject jsonObject;
                                if (type == 0 || type == 9 || type == 2) {
                                    jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                } else {
                                    jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                }
                                list1.add(jsonObject);
                            }
                        }
                        if (success1) {
                            success1 = success;
                        }
                    }
                } else {
                    List<WlgbJdSpBsft> list7 = wlgbJdSpBsftService.queryByStatueAndTypeAndSpBh(type, 2, spbh);
                    List<String> list6 = new ArrayList<>();
                    double zje = 0.0;
                    for (WlgbJdSpBsft wlgbJdSpBsft : list7) {
                        zje += wlgbJdSpBsft.getJe();
                        list6.add(wlgbJdSpBsft.getBsmc());
                        Map<String, Object> map = new HashMap<>();
                        map.put("time", sqsj);
                        map.put("list", list5);
                        map.put("list2", csList);
                        map.put("bsmc", wlgbJdSpBsft.getBsmc());
                        Integer count = weiLianService.queryKyQnMdCount(map);
                        if (count > 0) {
                            JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(wlgbJdSpBsft.getBsmc());
                            success = jsonObject1.getBoolean("success");
                            if (success) {
                                String bm = jsonObject1.getString("bm");
                                for (int i = 0; i < list3.size(); i++) {
                                    WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(i);
                                    double sum = wlgbJdSpBsft.getJe() / list3.size();
                                    sum = ((int) (sum * 100)) / 100.0;
                                    je += sum;
                                    JSONObject jsonObject;
                                    if (type == 0 || type == 9 || type == 2) {
                                        jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                    } else {
                                        jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                    }
                                    list1.add(jsonObject);
                                }
                            }
                            if (success1) {
                                success1 = success;
                            }
                        }
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("time", sqsj);
                    map.put("list", list5);
                    map.put("list1", list6);
                    map.put("list2", csList);
                    List<TbVilla> list8 = weiLianService.queryKyQnMdList(map);
                    map.put("list", bsXz);
                    int count = weiLianService.queryKyQnMdCount(map);

                    for (int i = 0; i < list8.size(); i++) {
                        TbVilla villa = list8.get(i);
                        //如果门店不存在这些数据将先不录入，后期补录，发送工作通知
                        String bsmc = villa.getVname().replace("★", "");
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        success = jsonObject1.getBoolean("success");
                        if (success) {
                            String bm = jsonObject1.getString("bm");
                            for (int j = 0; j < list3.size(); j++) {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(j);
                                double sum = ((djFyZje - zje) / count) / list3.size();
                                sum = ((int) (sum * 100)) / 100.0;
                                je += sum;
                                if (p == list2.size() - 1) {
                                    if (i == list8.size() - 1) {
                                        if (j == list3.size() - 1) {
                                            double ce = djFyZje - je;
                                            sum += ce;
                                        }
                                    }
                                }
                                JSONObject jsonObject;
                                if (type == 0 || type == 9 || type == 2) {
                                    jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                } else {
                                    jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                }
                                list1.add(jsonObject);
                            }
                        }
                        if (success1) {
                            success1 = success;
                        }
                    }
                }
            } else if ("城市加盟别墅分摊".equals(fyssft) || "城市直营别墅分摊".equals(fyssft)) {
                boolean jm = "城市加盟别墅分摊".equals(fyssft);
                List<WlgbJdSpCsft> list4 = wlgbJdSpCsftService.queryByStatueAndSpbh(type, spbh);
                List<String> csList = new ArrayList<>();
                list4.forEach(l -> csList.add(l.getCity()));
                List<String> list5 = bsJmZyXzCl(jm ? "加盟" : "直营");
                if (b) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("time", sqsj);
                    map.put("list", list5);
                    map.put("list2", csList);
                    List<TbVilla> list8 = weiLianService.queryKyQnMdList(map);
                    for (int i = 0; i < list8.size(); i++) {
                        TbVilla villa = list8.get(i);
                        //如果门店不存在这些数据将先不录入，后期补录，发送工作通知
                        String bsmc = villa.getVname().replace("★", "");
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        success = jsonObject1.getBoolean("success");
                        if (success) {
                            String bm = jsonObject1.getString("bm");
                            for (int j = 0; j < list3.size(); j++) {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(j);
                                double sum = (djFyZje / list8.size()) / list3.size();
                                sum = ((int) (sum * 100)) / 100.0;
                                je += sum;
                                if (p == list2.size() - 1) {
                                    if (i == list8.size() - 1) {
                                        if (j == list3.size() - 1) {
                                            double ce = djFyZje - je;
                                            sum += ce;
                                        }
                                    }
                                }
                                JSONObject jsonObject;
                                if (type == 0 || type == 9 || type == 2) {
                                    jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                } else {
                                    jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                }
                                list1.add(jsonObject);
                            }
                        }
                        if (success1) {
                            success1 = success;
                        }
                    }
                } else {
                    List<WlgbJdSpBsft> list7 = wlgbJdSpBsftService.queryByStatueAndTypeAndSpBh(type, 2, spbh);
                    List<String> list6 = new ArrayList<>();
                    double zje = 0.0;
                    for (WlgbJdSpBsft wlgbJdSpBsft : list7) {
                        zje += wlgbJdSpBsft.getJe();
                        list6.add(wlgbJdSpBsft.getBsmc());
                        Map<String, Object> map = new HashMap<>();
                        map.put("time", sqsj);
                        map.put("list", list5);
                        map.put("list2", csList);
                        map.put("bsmc", wlgbJdSpBsft.getBsmc());
                        int count = weiLianService.queryKyQnMdCount(map);
                        if (count > 0) {
                            JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(wlgbJdSpBsft.getBsmc());
                            success = jsonObject1.getBoolean("success");
                            if (success) {
                                String bm = jsonObject1.getString("bm");
                                for (int i = 0; i < list3.size(); i++) {
                                    WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(i);
                                    double sum = wlgbJdSpBsft.getJe() / list3.size();
                                    sum = ((int) (sum * 100)) / 100.0;
                                    je += sum;
                                    JSONObject jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                    list1.add(jsonObject);
                                }
                            }
                            if (success1) {
                                success1 = success;
                            }
                        }
                    }
                    Map<String, Object> map = new HashMap<>();
                    map.put("time", sqsj);
                    map.put("list", list5);
                    map.put("list1", list6);
                    map.put("list2", csList);
                    List<TbVilla> list8 = weiLianService.queryKyQnMdList(map);
                    for (int i = 0; i < list8.size(); i++) {
                        TbVilla villa = list8.get(i);
                        //如果门店不存在这些数据将先不录入，后期补录，发送工作通知
                        String bsmc = villa.getVname().replace("★", "");
                        JSONObject jsonObject1 = KingDeeConfig.queryBsSfCz(bsmc);
                        success = jsonObject1.getBoolean("success");
                        if (success) {
                            String bm = jsonObject1.getString("bm");
                            for (int j = 0; j < list3.size(); j++) {
                                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = list3.get(j);
                                double sum = ((djFyZje - zje) / list8.size()) / list3.size();
                                sum = ((int) (sum * 100)) / 100.0;
                                je += sum;
                                if (p == list2.size() - 1) {
                                    if (i == list8.size() - 1) {
                                        if (j == list3.size() - 1) {
                                            double ce = djFyZje - je;
                                            sum += ce;
                                        }
                                    }
                                }
                                JSONObject jsonObject;
                                if (type == 0 || type == 9 || type == 2) {
                                    jsonObject = scQtYfdMx(bm, wlgbJdSpCwlrjdsj.getFykmbm(), sum, bmid);
                                } else {
                                    jsonObject = scFkTkdMx(bm, sum, wlgbJdSpCwlrjdsj.getFykmbm(), cnzzsj, bmid);
                                }
                                list1.add(jsonObject);
                            }
                        }
                        if (success1) {
                            success1 = success;
                        }
                    }
                }
            }
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("list", list1);
        jsonObject.put("je", je);
        jsonObject.put("success", success1);

        return jsonObject;
    }

    /**
     * 付款单明细
     *
     * @param bm     金蝶门店
     * @param sum    金额
     * @param cnzzsj 出纳转账时间
     * @param fykmbm 费用科目编码
     * @param bmid   部门id
     */
    public JSONObject fkdMx1(String bm, Double sum, String cnzzsj, String fykmbm, String bmid, String fkyt, String yhk) {
        JSONObject jsonObject = new JSONObject();
        //现金
//        jsonObject.put("FSETTLETYPEID", KingDeeConfig.zhdx("JSFS01_SYS"));
        //电汇
        jsonObject.put("FSETTLETYPEID", KingDeeConfig.zhdx("JSFS04_SYS"));
        //银行卡账号
        jsonObject.put("FACCOUNTID", KingDeeConfig.zhdx(yhk));
        jsonObject.put("FPURPOSEID", KingDeeConfig.zhdx(fkyt));
        jsonObject.put("FPAYTOTALAMOUNTFOR", sum);
        jsonObject.put("FPAYAMOUNTFOR_E", sum);
        jsonObject.put("FSETTLEPAYAMOUNTFOR", sum);
        jsonObject.put("FRecType", "1");
        jsonObject.put("FCOSTID", KingDeeConfig.zhdx(fykmbm));
        jsonObject.put("FMD", KingDeeConfig.zhdx(bm));
        //部门
        jsonObject.put("FEXPENSEDEPTID_E", KingDeeConfig.zhdx(bmid));
        jsonObject.put("FPAYAMOUNT_E", sum);
        jsonObject.put("FPOSTDATE", cnzzsj);
        jsonObject.put("FRuZhangType", "1");
        jsonObject.put("FPayType", "A");
        jsonObject.put("FNOTVERIFICATEAMOUNT", sum);
        jsonObject.put("FBankInvoice", false);
        jsonObject.put("FByAgentBank", false);
        jsonObject.put("FOverseaPay", false);

        return jsonObject;
    }


    /**
     * 上传其他应付单明细
     *
     * @param bm   门店
     * @param kmbm 科目编码
     * @param sum  金额
     * @param bmid 部门id
     * @return 明细数据
     */
    public JSONObject scQtYfdMx(String bm, String kmbm, Double sum, String bmid) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FCOSTID", KingDeeConfig.zhdx(kmbm));
        jsonObject.put("FCOSTDEPARTMENTID", KingDeeConfig.zhdx(bmid));
        jsonObject.put("FSSMD", KingDeeConfig.zhdx(bm));
        jsonObject.put("FNOTAXAMOUNTFOR", sum);
        jsonObject.put("FTOTALAMOUNTFOR", sum);
        jsonObject.put("FNOTSETTLEAMOUNTFOR_D", sum);
        jsonObject.put("FNOTAXAMOUNT_D", sum);
        jsonObject.put("FCREATEINVOICE", false);

        return jsonObject;
    }


    /**
     * 上传付款退款单的明细赋值
     *
     * @param bm     门店
     * @param sum    金额
     * @param cnzzsj 出纳转账时间
     */
    public JSONObject scFkTkdMx(String bm, Double sum, String kmbm, String cnzzsj, String bmid) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("FSETTLETYPEID", KingDeeConfig.zhdx("JSFS01_SYS"));
        jsonObject.put("FPURPOSEID", KingDeeConfig.zhdx("SFKYT42_SYS"));
        jsonObject.put("FMD", KingDeeConfig.zhdx(bm));
        jsonObject.put("FEXPENSEDEPTID_E", KingDeeConfig.zhdx(bmid));
        jsonObject.put("FREFUNDAMOUNTFOR", sum);
        jsonObject.put("FREFUNDAMOUNTFOR_E", sum);
        jsonObject.put("FCOSTID", KingDeeConfig.zhdx(kmbm));
        jsonObject.put("FREFUNDAMOUNT_E", sum);
        jsonObject.put("FPOSTDATE", cnzzsj);

        return jsonObject;
    }

    /**
     * 别墅加盟直营性质处理
     *
     * @param zt 账套
     */
    public List<String> bsJmZyXzCl(String zt) {
        List<String> list5 = new ArrayList<>();
        boolean jm = "加盟".equals(zt);
        if (jm) {
            list5.add("加盟");
        } else {
            list5.add("直营");
        }
        return list5;
    }

    /**
     * 别墅全部性质处理
     */
    public List<String> bsQbXzCl() {
        List<String> bsXz = new ArrayList<>();
        bsXz.add("直营");
        bsXz.add("加盟");

        return bsXz;
    }


    /**
     * 发送工作通知
     *
     * @param type 审批类型
     * @param spbh 审批编号
     * @param yy   错误原因
     */
    public void sendGztz(Integer type, String spbh, String yy, String sp) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String context1 = "单据上传金蝶失败了";
        context1 += "\n\n原因：" + yy;
        context1 += "\n审批编号：" + spbh;
        if (type == 0) {
            context1 += "\n审批单据：" + sp + "审批";
        } else if (type == 4) {
            context1 += "\n表单上传：对公转账";
        } else if (type == 5) {
            context1 += "\n表单上传：场地费转应收单";
        } else if (type == 6) {
            context1 += "\n表单上传：新美大核销券码";
        } else if (type == 7) {
            context1 += "\n表单上传：对账完成多退少补";
        }
        context1 += "\n送达时间：" + df2.format(new Date());
        try {
            DingDBConfig.sendGztzText(dingkey, "15349026426046931", context1);
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }


    /**
     * 银行卡处理
     *
     * @param ztBm 账套编码
     * @return 银行卡对象
     */
    private WlgbJdYhk yhkCl(String ztBm) {
        String bz = "";
        if ("100".equals(ztBm)) {
            bz = "总部收入支出";
        } else if ("101".equals(ztBm)) {
            bz = "加盟收入支出";
        } else if ("102".equals(ztBm)) {
            bz = "直营费用";
        } else if ("103".equals(ztBm)) {
            bz = "乐诚收入支出";
        } else if ("104".equals(ztBm)) {
            bz = "奋马收入支出";
        } else if ("105".equals(ztBm)) {
            bz = "九运";
        } else if ("106".equals(ztBm)) {
            bz = "直营费用";
        }
        WlgbJdYhk wlgbJdYhk;
        if (!"".equals(bz)) {
            wlgbJdYhk = wlgbJdYhkService.querySfScAndLikeBz(0, bz);
        } else {
            wlgbJdYhk = new WlgbJdYhk();
        }
        return wlgbJdYhk;
    }

    /**
     * 修改表单实例
     *
     * @param id     要更新的表单数据ID
     * @param map    表单内容
     * @param userid 用户id
     */
    public GatewayResult xgBdSl(Map<String, Object> map, String id, String userid) {
        JSONObject json = new JSONObject(map);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.xgBdSl(token, ydAppkey, userid, id, json.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return gatewayResult;
    }


    /**
     * 发送新增表单
     *
     * @param userId 收取人id
     * @param map    表单内容
     * @param lx     表单类型
     */
    public GatewayResult xzBd(String userId, Map<String, Object> map, String lx) {
        JSONObject json = new JSONObject(map);
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz(lx);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, json.toJSONString(), userId, ydBd.getFormid());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return gatewayResult;
    }


    /**
     * 提交乐诚流程采购审批异步
     */
    @RequestMapping(value = "tjLcCgLcSpDataTask1")
    public void tjLcCgLcSpDataTask1(HttpServletRequest request) {
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        List<WlgbJdLcyzjl> list1 = wlgbJdLcyzjlService.queryListBySfLrJd(0);
        String finalToken = token;
        list1.forEach(li -> {
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("textField_kpt1j37n", li.getSpbh());
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = YdConfig.getLcSlData(finalToken, ydAppkey, "FORM-8K966U71JFFQJKNVVZSS8BFKJIKL1WKOJN3PK73", jsonObject1.toJSONString());
            } catch (Exception e) {
                e.printStackTrace();
            }
            String result = gatewayResult.getResult();
            JSONObject jsonObject = JSONObject.parseObject(result);
            String spbh = jsonObject.getString("textField_kpt1j37n");

            Integer count = wlgbJdLccglcjlService.queryCountBySpBh(spbh);
            if (count > 0) {
                return;
            }

            WlgbJdLccglcjl wlgbJdLccglcjl = new WlgbJdLccglcjl();
            String spbt = jsonObject.getString("textField_l4gdvmbe");
            wlgbJdLccglcjl.setSpbh(spbh);
            wlgbJdLccglcjl.setBsmc(jsonObject.getString("bsmcq"));
            wlgbJdLccglcjl.setSpbt(spbt);
            wlgbJdLccglcjl.setSqrbm(jsonObject.getString("textField_l4gdvmbc"));
            wlgbJdLccglcjl.setSqrbmid(jsonObject.getString("textField_l4gdvmbd"));
            wlgbJdLccglcjl.setSqr(jsonObject.getString("textField_l4gdvmbg"));
            wlgbJdLccglcjl.setSqrid(jsonObject.getString("textField_l4gdvmbf"));
            wlgbJdLccglcjl.setMdlx(jsonObject.getString("mdlx"));
//        wlgbJdLccglcjl.setFylb(jsonObject.getString("fylb"));
//        wlgbJdLccglcjl.setFklb(jsonObject.getString("selectField_kp5301ky"));
            wlgbJdLccglcjl.setSfdf(jsonObject.getString("radioField_kp49sver"));
//        wlgbJdLccglcjl.setFymx(jsonObject.getString("textareaField_kr09kvwx"));
            wlgbJdLccglcjl.setSmyy(jsonObject.getString("textareaField_kp4qot0r"));
            wlgbJdLccglcjl.setYjje(jsonObject.getDouble("zjez"));
            wlgbJdLccglcjl.setCnzzsj(jsonObject.getDate("dateField_l4fczgvl"));
            wlgbJdLccglcjl.setCnzzje(jsonObject.getDouble("numberField_l4fczgvo"));

            JSONArray list = jsonObject.getJSONArray("tableField_l4fczgvc");

            for (int i = 0; i < list.size(); i++) {
                JSONObject l = list.getJSONObject(i);
                double fyje = l.getDouble("numberField_l4fczgvk");
                if (fyje != 0) {
                    WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
                    wlgbJdSpCwlrjdsj.setSpbh(spbh);
                    wlgbJdSpCwlrjdsj.setSpbt(spbt);
                    wlgbJdSpCwlrjdsj.setZt(l.getString("textField_l4fczgvf"));
                    wlgbJdSpCwlrjdsj.setZtbm(l.getString("textField_l4fczgvg"));
                    wlgbJdSpCwlrjdsj.setFykm(l.getString("textField_l4fczgvi"));
                    wlgbJdSpCwlrjdsj.setFykmbm(l.getString("textField_l4fczgvj"));
                    wlgbJdSpCwlrjdsj.setFyje(l.getDouble("numberField_l4fczgvk"));
                    wlgbJdSpCwlrjdsj.setType(12);

                    wlgbJdSpCwlrjdsjService.save(wlgbJdSpCwlrjdsj);
                }
            }
            boolean save = lcCgLcCl(wlgbJdLccglcjl.getSpbh(), wlgbJdLccglcjl, 0);
            if (save) {
                wlgbJdLccglcjl.setSflrjd(1);
            }

            wlgbJdLccglcjlService.save(wlgbJdLccglcjl);
        });
    }

    /**
     * 选人截取
     *
     * @param userid 用户id
     * @return 用户id
     */
    private String xzjq(String userid) {
        String s = userid.substring(0, 1);
        if ("[".equals(s)) {
            userid = userid.substring(2, userid.length() - 2);
        } else {
            userid = userid;
        }
        return userid;
    }

    @RequestMapping(value = "yxCgBl")
    public Result yxCgBl(String spbh) {
        yxCgBxCl(spbh, null, 1);

        return Result.OK();
    }

    /**
     * 审批回调-获取所有审批事件以及考勤打卡__考勤记录
     */
    @RequestMapping(value = "tjSpAllTestKqJl")
    public void tjSpAllTestKqJl(HttpServletRequest request) {
        String data = request.getParameter("data");
        JSONObject callBackContent = JSONObject.parseObject(data);
        JSONArray jsonArray = JSONArray.parseArray(callBackContent.getString("DataList"));
        if (jsonArray.size() > 0) {
            jsonArray.forEach(l -> {
                WlgbSpAll wlgbSpAll = new WlgbSpAll();
                JSONObject jsonObject = JSONObject.parseObject(l.toString());
                //打卡地址
                String address = jsonObject.getString("address");
                if (address != null && !("".equals(address))) {
                    wlgbSpAll.setKqDkdd(address);
                }

                //打卡时间
                Date dksj = jsonObject.getDate("checkTime");
                if (null != dksj) {
                    wlgbSpAll.setKqDksj(new Date());
                }

                List<String> listIds;
                //打卡人id
                String userid = jsonObject.getString("userId");
                if (null != userid && !("".equals(userid))) {
                    wlgbSpAll.setKqDkrid(userid);

                    DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(userid);
                    if (Optional.ofNullable(dingdingEmployee).isPresent()) {
                        wlgbSpAll.setSjfqruserid(dingdingEmployee.getUserid());
                        wlgbSpAll.setSjfqrname(dingdingEmployee.getName());
                        wlgbSpAll.setKqDkr(dingdingEmployee.getName());
                        //打卡人
                        listIds = queryWgz(userid);
                        if (listIds.size() > 0) {
                            for (String listId : listIds) {
                                DingdingEmployee dingdingEmployeeCsr = weiLianDdXcxService.queryDingdingEmployeeByUserId(listId);
                                if (Optional.ofNullable(dingdingEmployeeCsr).isPresent()) {
                                    wlgbSpAll.setId(IdConfig.uuId());
                                    wlgbSpAll.setCsruserid(dingdingEmployeeCsr.getUserid());
                                    wlgbSpAll.setCsrname(dingdingEmployeeCsr.getName());
                                    wlgbSpAll.setSfsc(0);
                                    wlgbSpAllService.save(wlgbSpAll);

                                    //考勤记录新增
                                    kqXzBd(wlgbSpAll);
                                }
                            }
                        }
                    }

                }
            });
        }
    }

    //考勤记录新增表单
    private void kqXzBd(WlgbSpAll wlgbSpAll) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("kqdkr", wlgbSpAll.getKqDkrid());
        jsonObject.put("kqdkruserid", wlgbSpAll.getKqDkrid());
        jsonObject.put("kqdkrname", wlgbSpAll.getKqDkr());
        jsonObject.put("csgzr", wlgbSpAll.getCsruserid());
        jsonObject.put("csgzruserid", wlgbSpAll.getCsruserid());
        jsonObject.put("csgzrname", wlgbSpAll.getCsrname());
        jsonObject.put("address", wlgbSpAll.getKqDkdd());
        jsonObject.put("dksj", wlgbSpAll.getKqDksj());


        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("考勤记录");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("公开化系统");

        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        for (int i = 0; i < 6; i++) {
            GatewayResult gatewayResult;
            try {
                gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObject.toJSONString(), wlgbSpAll.getKqDkrid(), ydBd.getFormid());
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                e.printStackTrace();
            }

            if (gatewayResult.getSuccess()) {
                break;
            }
            if (i == 5) {
                if (!gatewayResult.getSuccess()) {
                    String txt = "考勤打卡同步公开化系统失败！！！\n\n" +
                            "失败原因：" + gatewayResult.toString() +
                            "\n\n代码：" + jsonObject;
                    try {
                        DingDBConfig.sendGztzText(dingkey, "15349026426046931", txt);
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 审批回调-获取所有审批事件以及考勤打卡__新增表单
     */
    @RequestMapping(value = "tjSpAllTestXzBd")
    public void tjSpAllTestXzBd(HttpServletRequest request) {
        String data = request.getParameter("data");
        JSONObject callBackContent = JSONObject.parseObject(data);
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String processInstanceId = callBackContent.getString("processInstanceId");
        String type = callBackContent.getString("type");
        //事件类型 bpms_task_change    bpms_instance_change
        String eventType = callBackContent.getString("EventType");
        if (processInstanceId != null && !"".equals(processInstanceId)) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = null;
            try {
                processInstanceTopVo = DingSpXqConfig.SpXq(dingkey, processInstanceId);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String splx = "";
            String processCode = callBackContent.getString("processCode");
            //从数据库里查询钉钉审批事件类型
            String type1 = weiLianDdXcxService.queryProcessTypeByProcessCode(processCode);
            if (type1 != null && !"".equals(type1)) {
                splx = type1;
            } else {
                return;
            }
            if (processInstanceTopVo != null) {

                String status = processInstanceTopVo.getStatus();
                String result = processInstanceTopVo.getResult();
                //钉钉审批发起
                if ("start".equals(type) && "bpms_instance_change".equals(eventType)) {
                    String sfcg = "error";
                    //查询我关注的人userid
                    List<String> listry = queryWgz(processInstanceTopVo.getOriginatorUserid());
                    if (listry.size() > 0 && !"".equals(splx)) {
                        String url = callBackContent.getString("url");
                        //添加审批数据
                        sfcg = TjSpsj(listry, processInstanceTopVo, splx, 1, url);
                    }
                }
                //钉钉审批结束
                if ("COMPLETED".equals(status) && "agree".equals(result) && "bpms_task_change".equals(eventType)) {
                    //是否成功
                    String sfcg = "error";
                    //查询我关注的人userid
                    List<String> listry = queryWgz(processInstanceTopVo.getOriginatorUserid());
                    if (listry.size() > 0 && !"".equals(splx)) {
                        sfcg = TjSpsj(listry, processInstanceTopVo, splx, 2, null);
                    }
                }
            }
        }
    }

    /**
     * 查询我关注的人
     *
     * @param fqrId 审批发起人userid
     * @return
     */
    public List<String> queryWgz(String fqrId) {

        List<String> list = new ArrayList<>();
        //发起人姓名
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(fqrId);
        if (!Optional.ofNullable(dingdingEmployee).isPresent()) {
            return list;
        }
        String name = dingdingEmployee.getName();
        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("我关注了谁");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("公开化系统");
        JSONObject jsonObject = new JSONObject();
//        被关注人姓名
        jsonObject.put("bgzrusername", name);
//        被关注人userid
        jsonObject.put("bgzruserid", fqrId);
        //查询表单 查找多少人关注了审批事件发起人
        String lc = YdConfig.zzBdSl(ydAppkey.getAppkey(), ydAppkey.getToken(), ydBd.getFormid(), jsonObject.toString());
        if ("error".equals(lc)) {
            return list;
        }

        JSONObject jsonObject1 = JSONObject.parseObject(lc);
        if ("true".equals(jsonObject1.getString("success"))) {
            JSONObject jsonres = jsonObject1.getJSONObject("result");
            if (jsonres != null) {
                JSONArray jsonArray = jsonres.getJSONArray("data");
//                System.out.println(jsonArray);
                jsonArray.forEach(l -> {
                    JSONObject formDataJson = JSONObject.parseObject(l.toString()).getJSONObject("formData");
                    String userid = formDataJson.getString("gzruserid");
                    list.add(userid);
                });
            }
        }
        return list;
    }

    //    添加审批数据
    public String TjSpsj(List<String> listry, OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo, String splx, Integer i, String url) {
        String zt = "error";
        //审批发起人
        DingdingEmployee dingdingEmployee = weiLianDdXcxService.queryDingdingEmployeeByUserId(processInstanceTopVo.getOriginatorUserid());
        if (dingdingEmployee == null) {
            return zt;
        }
        if (listry.size() > 0) {
            zt = "success";
            try {
                listry.forEach(l -> {
                    //关注人
                    DingdingEmployee dingdingEmployeeGzr = weiLianDdXcxService.queryDingdingEmployeeByUserId(l);
                    sjGs(dingdingEmployee, dingdingEmployeeGzr, processInstanceTopVo, splx, (i == 1 ? "发起" : "结束"), url);
                });
            } catch (Exception e) {
                zt = "error";
                e.printStackTrace();
            }
        }
        return zt;
    }

    /**
     * @param dingdingEmployee     发起人/被关注人信息
     * @param dingdingEmployeeGzr  关注人信息
     * @param processInstanceTopVo 审批数据
     * @param url                  OA审批发起的链接
     */
    //数据格式
    public void sjGs(DingdingEmployee dingdingEmployee, DingdingEmployee dingdingEmployeeGzr,
                     OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo, String splx,
                     String spzt, String url) {
        //新增所有审批
        WlgbSpAll wlgbSpAll = new WlgbSpAll();

        JSONObject jsonObject = new JSONObject();
        //实际发起人
        jsonObject.put("employeeField_l3sijaww", dingdingEmployee.getUserid());
        //实际发起人姓名
        jsonObject.put("textField_l3sijawx", dingdingEmployee.getName());
        //实际发起人ID
        jsonObject.put("textField_l3sijawy", dingdingEmployee.getUserid());
        //抄送给关注人
        jsonObject.put("employeeField_l3sj2k8q", dingdingEmployeeGzr.getUserid());
        //抄送给关注人姓名
        jsonObject.put("textField_l3the5u7", dingdingEmployeeGzr.getName());
        //抄送给关注人ID
        jsonObject.put("textField_l3the5u6", dingdingEmployeeGzr.getUserid());
        //审批类型
        jsonObject.put("radioField_l3sijawz", splx);
        //审批状态
        jsonObject.put("radioField_l3sjvcsb", spzt);

        if (url != null && !"".equals(url)) {
            jsonObject.put("sp_url", url);
            wlgbSpAll.setSpUrl(url);
        }

        //报销金额
        double d = 0.0;
        //费用使用方
        String fysyf = "";
        String fylb = "";

        List<OapiProcessinstanceGetResponse.FormComponentValueVo> formComponentValues = processInstanceTopVo.getFormComponentValues();
        if (formComponentValues != null && formComponentValues.size() > 0) {
            for (OapiProcessinstanceGetResponse.FormComponentValueVo l : formComponentValues) {
                String name = l.getName();
                if ("费用付款申请".equals(splx)) {
                    if ("合计金额（元）".equals(name)) {
                        d = Double.parseDouble(l.getValue());
                    }
                } else if ("报销申请".equals(splx)) {
                    if ("报销总金额（元）".equals(name) || "本次报销总金额".equals(name)) {
                        d = Double.parseDouble(l.getValue());
                    }
                } else if ("抵预支申请".equals(splx)) {
                    if ("抵预支合计金额（元）".equals(name)) {
                        d = Double.parseDouble(l.getValue());
                    }
                } else if ("预支申请".equals(splx)) {
                    if ("本次预支金额（元）".equals(name)) {
                        d = Double.parseDouble(l.getValue());
                    }
                }
                if ("费用使用方".equals(name)) {
                    fysyf = l.getValue();
                }
                if (null != l.getValue() && "支付费用类别".equals(name)) {
                    fylb = l.getValue();
                }
            }
        }
        //审批编号
        jsonObject.put("textField_l3sijax5", processInstanceTopVo.getBusinessId());
        //费用类别
        jsonObject.put("textField_l3sijax4", fylb);
        //费用使用方
        jsonObject.put("textField_l3sijax3", fysyf);
        //实际金额
        jsonObject.put("numberField_l3sijax1", d);
        jsonObject.put("dateField_l3sijax2", processInstanceTopVo.getCreateTime());


        wlgbSpAll.setId(IdConfig.uuId());
        wlgbSpAll.setFqsj(processInstanceTopVo.getCreateTime());
        wlgbSpAll.setCsrname(dingdingEmployeeGzr.getName());
        wlgbSpAll.setCsruserid(dingdingEmployeeGzr.getUserid());
        wlgbSpAll.setFylb(fylb);
        wlgbSpAll.setSjfqrname(dingdingEmployee.getName());
        wlgbSpAll.setSjfqruserid(dingdingEmployee.getUserid());
        wlgbSpAll.setFysyf(fysyf);
        wlgbSpAll.setSpbh(processInstanceTopVo.getBusinessId());
        wlgbSpAll.setSjje(d);
        wlgbSpAll.setSpzt(spzt);
        wlgbSpAll.setSplx(splx);
        wlgbSpAllService.save(wlgbSpAll);

        YdBd ydBd = weiLianDdXcxService.queryYdBdByBz("审批发起和结束记录");
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("公开化系统");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        for (int i = 0; i < 6; i++) {
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = DingBdLcConfig.xzBdSl(token, ydAppkey, jsonObject.toJSONString(), dingdingEmployee.getUserid(), ydBd.getFormid());
            } catch (Exception e) {
                gatewayResult = new GatewayResult();
                e.printStackTrace();
            }

            if (gatewayResult.getSuccess()) {
                break;
            }
            if (i == 5) {
                if (!gatewayResult.getSuccess()) {
                    String txt = "审批同步公开化系统失败！！！\n\n" +
                            "失败原因：" + gatewayResult.toString() +
                            "\n\n代码：" + jsonObject;
                    try {
                        DingDBConfig.sendGztzText(dingkey, "15349026426046931", txt);
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    @RequestMapping(value = "bxBl")
    public Result bxBl(String spbh) {
        bxCl(spbh, null, 1);
        return Result.OK();
    }

    /**
     * 将报销审批补录进金蝶
     *
     * @return
     */
    @RequestMapping(value = "bcBxList")
    public Result bcBxList() {
        WlgbJdBxspjl wlgbJdBxspjl = new WlgbJdBxspjl();
        wlgbJdBxspjl.setSflrjd(0);
        List<WlgbJdBxspjl> list = wlgbJdBxspjlService.queryListByWlgbJdBxspjl(wlgbJdBxspjl);
        log.info("*********bcBxList===************{}", JSON.toJSONString(list));
        list.forEach(l -> {
            bxCl(l.getSpbh(), null, 1);
        });
        return Result.OK();
    }

    @RequestMapping(value = "bcFyFkList")
    public Result bcFyFkList() {
        WlgbJdFyfksqjl wlgbJdFyfksqjl = new WlgbJdFyfksqjl();
        wlgbJdFyfksqjl.setSflrjd(0);
        List<WlgbJdFyfksqjl> list = wlgbJdFyfksqjlService.queryListWlgbJdFyfksqjl(wlgbJdFyfksqjl);
        list.forEach(l -> {
            fyFkCl(l.getSpbh(), null, 1);
        });

        return Result.OK();
    }

    @RequestMapping(value = "bcYzList")
    public Result bcYzList() {
        WlgbJdYzsqjl wlgbJdYzsqjl = new WlgbJdYzsqjl();
        wlgbJdYzsqjl.setSflrjd(0);
        List<WlgbJdYzsqjl> list = wlgbJdYzsqjlService.queryListWlgbJdYzsqjl(wlgbJdYzsqjl);
        list.forEach(l -> {
            yzCl(l.getSpbh(), null, 1);
        });

        return Result.OK();
    }

    @RequestMapping(value = "bcDyzList")
    public Result bcDyzList() {
        WlgbJdDyzsqjl wlgbJdDyzsqjl = new WlgbJdDyzsqjl();
        wlgbJdDyzsqjl.setSflrjd(0);
        List<WlgbJdDyzsqjl> list = wlgbJdDyzsqjlService.queryListWlgbJdDyzsqjl(wlgbJdDyzsqjl);
        list.forEach(l -> {
            dyzCl(l.getSpbh(), null, 1);
        });

        return Result.OK();
    }

    @RequestMapping(value = "bcLcXmCgList")
    public Result bcLcXmCgList() {
        WlgbJdLcxmcgdfksqjl wlgbJdLcxmcgdfksqjl = new WlgbJdLcxmcgdfksqjl();
        wlgbJdLcxmcgdfksqjl.setSflrjd(0);
        List<WlgbJdLcxmcgdfksqjl> list = wlgbJdLcxmcgdfksqjlService.queryListByWlgbJdLcxmcgdfksqjl(wlgbJdLcxmcgdfksqjl);
        list.forEach(l -> {
            lcXmCgFyFkCl(l.getSpbh(), null, 1);
        });

        return Result.OK();
    }

    @RequestMapping(value = "bcLcXmBxList")
    public Result bcLcXmBxList() {
        WlgbJdLcxmbxsqjl wlgbJdLcxmbxsqjl = new WlgbJdLcxmbxsqjl();
        wlgbJdLcxmbxsqjl.setSflrjd(0);
        List<WlgbJdLcxmbxsqjl> list = wlgbJdLcxmbxsqjlService.queryListByWlgbJdLcxmbxsqjl(wlgbJdLcxmbxsqjl);
        list.forEach(l -> {
            lcXmBxCl(l.getSpbh(), null, 1);
        });

        return Result.OK();
    }

    @RequestMapping(value = "bcLcXmDyzList")
    public Result bcLcXmDyzList() {
        WlgbJdLcxmdyzsqjl wlgbJdLcxmdyzsqjl = new WlgbJdLcxmdyzsqjl();
        wlgbJdLcxmdyzsqjl.setSflrjd(0);
        List<WlgbJdLcxmdyzsqjl> list = wlgbJdLcxmdyzsqjlService.queryListWlgbJdLcxmdyzsqjl(wlgbJdLcxmdyzsqjl);
        list.forEach(l -> {
            lcXmDyzCl(l.getSpbh(), null, 1);
        });

        return Result.OK();
    }

    @RequestMapping(value = "bcTdjList")
    public Result bcTdjList() {
        WlgbJdTdjsqjl wlgbJdTdjsqjl = new WlgbJdTdjsqjl();
        wlgbJdTdjsqjl.setSflrjd(0);
        List<WlgbJdTdjsqjl> list = wlgbJdTdjsqjlService.queryListByWlgbJdTdjsqjl(wlgbJdTdjsqjl);
        list.forEach(l -> {
            tdjCl(l.getSpbh(), null, 1);
        });

        return Result.OK();
    }

    @RequestMapping(value = "bcYxCgList")
    public Result bcYxCgList() {
        List<WlgbJdYxcgsqjl> list = wlgbJdYxcgsqjlService.queryBySfLrJd(0);
        list.forEach(l -> {
            yxCgBxCl(l.getSpbh(), null, 1);
        });

        return Result.OK();
    }

    @RequestMapping(value = "bcLcCgLcList")
    public Result bcLcCgLcList() {
        WlgbJdLccglcjl wlgbJdLccglcjl = new WlgbJdLccglcjl();
        wlgbJdLccglcjl.setSflrjd(0);
        List<WlgbJdLccglcjl> list = wlgbJdLccglcjlService.queryListByWlgbJdLccglcjl(wlgbJdLccglcjl);
        list.forEach(l -> {
            lcCgLcCl(l.getSpbh(), null, 1);
        });

        return Result.OK();
    }

    @RequestMapping(value = "bcLcYzLcList")
    public Result bcLcYzLcList() {
        List<WlgbJdLcyzjl> list = wlgbJdLcyzjlService.queryListBySfLrJd(0);
        list.forEach(l -> {
            lcYzLcCl(l.getSpbh(), null, 1);
        });

        return Result.OK();
    }

    @RequestMapping(value = "bcWlbTxList")
    public Result bcWlbTxList() {
        WlgbJdWlbtxjl wlgbJdWlbtxjl = new WlgbJdWlbtxjl();
        wlgbJdWlbtxjl.setSflrjd(0);
        List<WlgbJdWlbtxjl> list = wlgbJdWlbtxjlService.queryListByWlgbJdWlbtxjl(wlgbJdWlbtxjl);
        list.forEach(l -> {
            wlbTxCl(l.getSpbh(), null, 1);
        });

        return Result.OK();
    }

    @RequestMapping(value = "bcDjList")
    public Result bcDjList() {
        WlgbHxyhDzjl wlgbHxyhDzjl = new WlgbHxyhDzjl();
        wlgbHxyhDzjl.setSflrjd(0);
        List<WlgbHxyhDzjl> list = wlgbHxyhDzjlService.queryListByWlgbHxyhDzjl(wlgbHxyhDzjl);
        list.forEach(l -> {
            Boolean skd3 = KingDeeConfig.saveSkd3(l.getTradeNo(),
                    l.getTotalAmount(),
                    l.getOutTradeNo(),
                    "",
                    "",
                    l.getCreateTime(),
                    "6230200551557759"
            );
            if (skd3) {
                WlgbHxyhDzjl wlgbHxyhDzjl1 = new WlgbHxyhDzjl();
                wlgbHxyhDzjl1.setSflrjd(1);
                wlgbHxyhDzjl1.setId(l.getId());
                wlgbHxyhDzjlService.updateById(wlgbHxyhDzjl1);
            }
        });


        return Result.OK();
    }

    @RequestMapping(value = "dyzBl")
    public Result dyzBl(String spbh) {
        dyzCl(spbh, null, 1);
        return Result.OK();
    }

    @RequestMapping(value = "qcSpLrStatus")
    public Result qcSpLrStatus(HttpServletRequest request) {
        String spbh = request.getParameter("spbh");
        String type = request.getParameter("type");
        if ("报销".equals(type)) {
            WlgbJdBxspjl wlgbJdBxspjl = wlgbJdBxspjlService.queryBySpBhAndSfLrJd(spbh, 1);
            if (wlgbJdBxspjl == null) {
                return Result.OK("未找到数据");
            }
            WlgbJdBxspjl wlgbJdBxspjl1 = new WlgbJdBxspjl();
            wlgbJdBxspjl1.setId(wlgbJdBxspjl.getId());
            wlgbJdBxspjl1.setSflrjd(0);
            wlgbJdBxspjlService.updateById(wlgbJdBxspjl1);
        } else if ("费用付款".equals(type)) {
            WlgbJdFyfksqjl wlgbJdFyfksqjl = wlgbJdFyfksqjlService.queryBySpBhAndSfLrJd(spbh, 1);
            if (wlgbJdFyfksqjl == null) {
                return Result.OK("未找到数据");
            }
            WlgbJdFyfksqjl wlgbJdFyfksqjl1 = new WlgbJdFyfksqjl();
            wlgbJdFyfksqjl1.setId(wlgbJdFyfksqjl.getId());
            wlgbJdFyfksqjl1.setSflrjd(0);
            wlgbJdFyfksqjlService.updateById(wlgbJdFyfksqjl1);
        } else if ("预支".equals(type)) {
            WlgbJdYzsqjl wlgbJdYzsqjl = wlgbJdYzsqjlService.queryBySpBhAndSfLrJd(spbh, 1);
            if (wlgbJdYzsqjl == null) {
                return Result.OK("未找到数据");
            }
            WlgbJdYzsqjl wlgbJdYzsqjl1 = new WlgbJdYzsqjl();
            wlgbJdYzsqjl1.setId(wlgbJdYzsqjl.getId());
            wlgbJdYzsqjl1.setSflrjd(0);
            wlgbJdYzsqjlService.updateById(wlgbJdYzsqjl1);
        } else if ("抵预支".equals(type)) {
            WlgbJdDyzsqjl wlgbJdDyzsqjl = wlgbJdDyzsqjlService.queryBySpBhAndSfLrJd(spbh, 1);
            if (wlgbJdDyzsqjl == null) {
                return Result.OK("未找到数据");
            }
            WlgbJdDyzsqjl wlgbJdDyzsqjl1 = new WlgbJdDyzsqjl();
            wlgbJdDyzsqjl1.setId(wlgbJdDyzsqjl.getId());
            wlgbJdDyzsqjl1.setSflrjd(0);
            wlgbJdDyzsqjlService.updateById(wlgbJdDyzsqjl1);
        } else if ("威廉币提现".equals(type)) {
            WlgbJdWlbtxjl wlgbJdWlbtxjl = wlgbJdWlbtxjlService.queryBySpBhAndSfLrJd(spbh, 1);
            if (wlgbJdWlbtxjl == null) {
                return Result.OK("未找到数据");
            }
            WlgbJdWlbtxjl wlgbJdWlbtxjl1 = new WlgbJdWlbtxjl();
            wlgbJdWlbtxjl1.setId(wlgbJdWlbtxjl.getId());
            wlgbJdWlbtxjl1.setSflrjd(0);
            wlgbJdWlbtxjlService.updateById(wlgbJdWlbtxjl1);
        } else if ("宿舍退押金".equals(type)) {
            WlgbJdSstyjsqjl wlgbJdSstyjsqjl = wlgbJdSstyjsqjlService.queryBySpBhAndSfLrJd(spbh, 1);
            if (wlgbJdSstyjsqjl == null) {
                return Result.OK("未找到数据");
            }
            WlgbJdSstyjsqjl wlgbJdSstyjsqjl1 = new WlgbJdSstyjsqjl();
            wlgbJdSstyjsqjl1.setId(wlgbJdSstyjsqjl.getId());
            wlgbJdSstyjsqjl1.setSflrjd(0);
            wlgbJdSstyjsqjlService.updateById(wlgbJdSstyjsqjl1);
        } else if ("借支".equals(type)) {
            WlgbJdJzspjl wlgbJdJzspjl = wlgbJdJzspjlService.queryBySpBhAndSfLrJd(spbh, 1);
            if (wlgbJdJzspjl == null) {
                return Result.OK("未找到数据");
            }
            WlgbJdJzspjl wlgbJdJzspjl1 = new WlgbJdJzspjl();
            wlgbJdJzspjl1.setId(wlgbJdJzspjl.getId());
            wlgbJdJzspjl1.setSflrjd(0);
            wlgbJdJzspjlService.updateById(wlgbJdJzspjl1);
        } else if ("平台刷单款报销".equals(type)) {
            WlgbJdPtsdkbxsqjl wlgbJdPtsdkbxsqjl = wlgbJdPtsdkbxsqjlService.queryBySpBhAndSfLrJd(spbh, 1);
            if (wlgbJdPtsdkbxsqjl == null) {
                return Result.OK("未找到数据");
            }
            WlgbJdPtsdkbxsqjl wlgbJdPtsdkbxsqjl1 = new WlgbJdPtsdkbxsqjl();
            wlgbJdPtsdkbxsqjl1.setId(wlgbJdPtsdkbxsqjl.getId());
            wlgbJdPtsdkbxsqjl1.setSflrjd(0);
            wlgbJdPtsdkbxsqjlService.updateById(wlgbJdPtsdkbxsqjl1);
        }
        if (spbh != null && !"".equals(spbh)) {
            WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj = new WlgbJdSpCwlrjdsj();
            wlgbJdSpCwlrjdsj.setSfscjd(1);
            wlgbJdSpCwlrjdsj.setSpbh(spbh);
            List<WlgbJdSpCwlrjdsj> list = wlgbJdSpCwlrjdsjService.queryListByWlgbJdSpCwlrjdsj(wlgbJdSpCwlrjdsj);
            list.forEach(l -> {
                WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj1 = new WlgbJdSpCwlrjdsj();
                wlgbJdSpCwlrjdsj1.setId(l.getId());
                wlgbJdSpCwlrjdsj1.setSfscjd(0);
                wlgbJdSpCwlrjdsjService.updateById(wlgbJdSpCwlrjdsj1);

            });
        }
        return Result.OK();
    }


}
