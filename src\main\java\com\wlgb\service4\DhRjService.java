package com.wlgb.service4;

import com.wlgb.config.DingdingEmployee;
import com.wlgb.config.Dingkey;
import com.wlgb.config.PageHelpUtil;
import com.wlgb.entity.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 12:06
 */
public interface DhRjService {
    Integer queryCountDhRjXydSfCz(DhrjXyd dhrjXyd);

    DingdingEmployee queryDhRjDingDingEmployeeByUserId(String userId);

    DingdingEmployee queryDhRjDingDingEmployeeByName(String name);

    Integer queryDhRjXydMothCountByXfd(String xfd);

    Integer queryDhRjXydDateCountByXfd(String xfd);

    Integer queryDhRjXydYesCountByXfd(String xfd);

    Double queryDhRjXdYjByXfd(String xfd);

    List<CrmCs> queryCrmCsList();

    List<CrmKhzt> queryCrmKhZtList(Integer lxid);

    Integer queryCrmDhSfcz(Map<String, Object> map);

    Integer queryCrmWxhCount(String qkhwxh);

    Integer queryCrmHmCount(String dhhm);

    List<CrmCsfq> queryCrmCsSfqByCsName(String csName);

    String queryCrmBbh();

    List<CrmQbkh> queryCrmDataImport(Map<String, Object> map);

    PageHelpUtil queryCrmData(Map<String, Object> map);

    Integer queryCmrCsFqIdByFqName(String fqName);

    List<CrmBmxq> queryCrmBmCxByBmDa(String bmDa);

    List<CrmQbkh> queryCrmCcSjByCcData(String cc);

    List<CrmCsfq> queryCrmCsFqList();

    Dingkey queryDingKeyById(String id);

    DingDingToken queryDingDingTokenByName(String name);

    List<CrmUser> queryCrmUserList();

    void qkCrmBmXqTable();

    void qkCrmBmTable();

    void qkCrmUserBackupsTable();

    void bfCrmUserInBackups();

    void qkCrmUserCopyTable();

    void saveCrmUserCopy(CrmUser crmUser);

    void delCrmUserCopy();

    void qkCrmUserTable();

    void saveCrmUser();
}
