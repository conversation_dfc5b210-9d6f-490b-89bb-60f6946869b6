package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/26 18:19
 */
@Data
@Table(name = "ldt_bjjl")
public class Bjjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**归属人姓名*/
    private String gsrxm;
    /**归属人id*/
    private String gsrid;
    /**审批人姓名*/
    private String sprxm;
    /**审批人id*/
    private String sprid;
    /**总浏览量*/
    private Integer zllnum;
    /**城市*/
    private String city;
    /**渠道部门*/
    private String qdbm;
    /**归属人部门*/
    private String gsrbm;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date cjtime;
    /**唯一标识*/
    private String wybs;
    /**实例id*/
    private String slid;
}
