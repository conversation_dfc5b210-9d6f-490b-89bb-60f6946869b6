package com.wlgb.entity;

import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "fwq_wxfddtzjl")
public class FwqWxfddtzjl {
    @Id
    private Integer id;
    private String quyu;
    private String vname;
    private String xddbh;
    private String changci;
    private String zkname;
    private String zktel;
    private String qxname;
    private String qxuserid;
    private String jibie;
    private Date time;
    private String haoping;
    private String pyq;
    private String sjsy;
    private String xhfnr;
    private String xdpnr;
    private String renshu;
    private String khly;
    private String khxz;
}
