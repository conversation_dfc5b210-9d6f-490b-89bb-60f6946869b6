package com.wlgb.service3.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbDbwcw;
import com.wlgb.mapper.WlgbDbwcwMapper;
import com.wlgb.service3.WlgbDbwcwService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/16 17:08
 */
@Service
@DS("third")
public class WlgbDbwcwServiceImpl implements WlgbDbwcwService {
    @Resource
    private WlgbDbwcwMapper wlgbDbwcwMapper;

    @Override
    public void save(WlgbDbwcw wlgbDbwcw) {
        wlgbDbwcw.setId(IdConfig.uuId());
        wlgbDbwcw.setCreateTime(new Date());
        wlgbDbwcwMapper.insertSelective(wlgbDbwcw);
    }
}
