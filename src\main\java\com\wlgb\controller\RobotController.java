package com.wlgb.controller;

import com.alibaba.fastjson.JSONObject;
import com.wlgb.config.*;
import com.wlgb.entity.WlgbHxyhDzjl;
import com.wlgb.entity.WlgbJdDjLsjlb;
import com.wlgb.service.WlgbJdDjLsjlbService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Date 2023/02/09 14:14
 * @Version 1.0
 */
@RestController
@RequestMapping(value = "/robot")
public class RobotController {


    /**
     * 获取企业员工的user信息
     *
     * @param
     * @return
     * @throws FileNotFoundException
     */
    @GetMapping(value = "getuserxx")
    public Result queryYsDd(String ddbh) throws FileNotFoundException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("out_trade_no", ddbh);
        jsonObject.put("trade_no", ddbh);
        JSONObject jsonObject1 = YsPayConfig.queryDd(jsonObject);

        return Result.OK(jsonObject1);
    }

    @GetMapping(value = "demo")
    public Result demo() throws FileNotFoundException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("demo", "接口链接");

        return Result.OK(jsonObject);
    }

}
