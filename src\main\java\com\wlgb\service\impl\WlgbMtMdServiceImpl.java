package com.wlgb.service.impl;

import com.wlgb.entity.WlgbMtMd;
import com.wlgb.mapper.WlgbMtMdMapper;
import com.wlgb.service.WlgbMtMdService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月19日 16:50
 */
@Service
public class WlgbMtMdServiceImpl implements WlgbMtMdService {
    @Resource
    private WlgbMtMdMapper wlgbMtMdMapper;

    @Override
    public void saveBatch(List<WlgbMtMd> list) {
        list.forEach(l -> {
            l.setCreateTime(new Date());
            wlgbMtMdMapper.insertSelective(l);
        });
    }

    @Override
    public void save(WlgbMtMd wlgbMtMd) {
        wlgbMtMdMapper.insertSelective(wlgbMtMd);
    }

    @Override
    public Integer queryCountByOpenUuId(String openUuId) {
        Example example = new Example(WlgbMtMd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("openShopUuid", openUuId);
        return wlgbMtMdMapper.selectCountByExample(example);
    }

    @Override
    public WlgbMtMd queryCountByOpenUuId2(String openUuId) {
        Example example = new Example(WlgbMtMd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("openShopUuid", openUuId);
        return wlgbMtMdMapper.selectOneByExample(example);
    }

    @Override
    public WlgbMtMd queryByShopUuId(String shopUuId) {
        Example example = new Example(WlgbMtMd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("openShopUuid", shopUuId);
        return wlgbMtMdMapper.selectOneByExample(example);
    }

    @Override
    public WlgbMtMd queryByopBizCode(Map<String, Object>... map) {
        Example example = new Example(WlgbMtMd.class);
        Example.Criteria criteria = example.createCriteria();
        // 遍历所有传入的Map
        for (Map<String, Object> singleMap : map) {
            Set<Map.Entry<String, Object>> entries = singleMap.entrySet();
            // 遍历单个Map中的所有键值对
            for (Map.Entry<String, Object> entry : entries) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (!key.isEmpty()) {
                    criteria.andEqualTo(key, value);
                }
            }
        }
        return wlgbMtMdMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbMtMd> queryAllWlgbMtMd() {
        return wlgbMtMdMapper.selectAll();
    }

    @Override
    public void delete(WlgbMtMd wlgbMtMd) {
        wlgbMtMdMapper.delete(wlgbMtMd);
    }

    @Override
    public List<WlgbMtMd> queryByToken(Map<String, Object>... map) {
        Example example = new Example(WlgbMtMd.class);
        Example.Criteria criteria = example.createCriteria();
        // 遍历所有传入的Map
        for (Map<String, Object> singleMap : map) {
            Set<Map.Entry<String, Object>> entries = singleMap.entrySet();
            // 遍历单个Map中的所有键值对
            for (Map.Entry<String, Object> entry : entries) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if ((!key.isEmpty()) && ("endtime".equals(key))) {
                    //这是<=的意思
                    criteria.andLessThanOrEqualTo(key, value);
                } else {
                    //这是=的意思
                    criteria.andEqualTo(key, value);
                }
            }
        }
        return wlgbMtMdMapper.selectByExample(example);
    }

    @Override
    public List<WlgbMtMd> queryByopBizCodeIsNull(Map<String, Object>... map) {
        Example example = new Example(WlgbMtMd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIsNull("opbizcode");
        // 遍历所有传入的Map
        for (Map<String, Object> singleMap : map) {
            Set<Map.Entry<String, Object>> entries = singleMap.entrySet();
            // 遍历单个Map中的所有键值对
            for (Map.Entry<String, Object> entry : entries) {
                String key = entry.getKey();
                Object value = entry.getValue();
                //这是=的意思
                criteria.andEqualTo(key, value);
            }
        }
        return wlgbMtMdMapper.selectByExample(example);
    }


    @Override
    public void updateById(WlgbMtMd wlgbMtMd) {
        wlgbMtMdMapper.updateByPrimaryKeySelective(wlgbMtMd);
    }
}
