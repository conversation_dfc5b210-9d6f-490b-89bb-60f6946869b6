package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqDyYq;
import com.wlgb.mapper.FwqDyYqMapper;
import com.wlgb.service2.FwqDyYqService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Service
@DS(value = "second")
public class FwqDyYqServiceImpl implements FwqDyYqService {
    @Resource
    private FwqDyYqMapper fwqDyYqMapper;

    @Override
    public void save(FwqDyYq fwqDyYq) {
        fwqDyYqMapper.insertSelective(fwqDyYq);
    }

    @Override
    public List<FwqDyYq> selectAll() {
        return fwqDyYqMapper.selectAll();
    }

    @Override
    public List<FwqDyYq> selectByOrderid(String orderid) {
        FwqDyYq fwqDyYq = new FwqDyYq();
        fwqDyYq.setOrderid(orderid);
        fwqDyYq.setSfsc("0");
        return fwqDyYqMapper.select(fwqDyYq);
    }

    @Override
    public FwqDyYq selectByOrderidAndZt(String orderid, String verifytoken, String encryptedcode) {
        FwqDyYq fwqDyYq = new FwqDyYq();
        fwqDyYq.setOrderid(orderid);
        fwqDyYq.setSfsc("0");
        fwqDyYq.setZt("0");
        fwqDyYq.setVerifytoken(verifytoken);
        fwqDyYq.setEncryptedcode(encryptedcode);
        return fwqDyYqMapper.selectOne(fwqDyYq);
    }

    @Override
    public void deleteByPrimaryKey(FwqDyYq fwqDyPoi) {
        fwqDyYqMapper.deleteByPrimaryKey(fwqDyPoi);
    }

    @Override
    public void update(FwqDyYq fwqDyYq) {
        fwqDyYqMapper.updateByPrimaryKey(fwqDyYq);
    }
}
