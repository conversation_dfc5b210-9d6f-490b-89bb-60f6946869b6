package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: sys_user
 */
@Data
@Table(name = "sys_user")
public class SysUser {
    @Id
    @KeySql(useGeneratedKeys = true)
    private String user_id;
    private String username;
    private String password;
    private String name;
    private String ridhts;
    private String role_id;
    private String status;
    private String bz;
    private String skin;
    private String number;
    private String phone;
    private String department_id;
    private String corpid;
    private String quanxianzpd;
    private String mima;
}
