package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 订单回访
 * @Author: jeecg-boot
 * @Date:   2020-12-10
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_ddhf")
public class WlgbDdhf {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**提交人*/
    private java.lang.String tjr;
	/**提交时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date tjsj;
	/**回访上传截图*/
    private java.lang.String hfscjt;
	/**场地费*/
    private java.lang.Double cdf;
	/**电费*/
    private java.lang.Double df;
	/**用电度数*/
    private java.lang.Double ydds;
	/**餐饮费*/
    private java.lang.Double cyf;
	/**商品消费*/
    private java.lang.Double spxf;
	/**策划*/
    private java.lang.Double ztze;
	/**应退押金*/
    private java.lang.Double ytyj;
	/**押金*/
    private java.lang.Double yj;
	/**协议单id*/
    private java.lang.String xid;
	/**是否删除(0:否1:是)*/
    private java.lang.Integer sfsc;
	/**客户是否回访(0:否1:是)*/
    private java.lang.Integer khsfhf;
	/**消费是否一致(0:否1:是)*/
    private java.lang.Integer xfsfyz;
	/**门店卫生星级评价(1-5)*/
    private java.lang.Integer mdwsxjpj;
	/**门店设备星级评价(1-5)*/
    private java.lang.Integer mdsbxjpj;
	/**店长服务星级评价(1-5)*/
    private java.lang.Integer dzfwxjpj;
	/**客户回访时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date khhfsj;
    /**其他建议和诉求*/
    private java.lang.String qtjysq;
    /**二维码地址*/
    private java.lang.String ewm;
    /**获取二维码时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date hqewmtime;
    /**反馈内容*/
    private java.lang.String fknr;
    /**反馈图片*/
    private java.lang.String fktp;
    /**反馈人*/
    private java.lang.String fkr;
    /**反馈人id*/
    private java.lang.String fkrid;
    /**反馈时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date fksj;

    /**客户是否回访(0:否1:是)*/
    private java.lang.Integer sffk;
    /**卫生不满意原因*/
    private java.lang.String wsbmyy;
}
