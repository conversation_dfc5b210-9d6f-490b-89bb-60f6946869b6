package com.wlgb.service.impl;

import com.wlgb.entity.WlgbHxyhDzjl;
import com.wlgb.entity.WlgbHxyhYjWkDzjl;
import com.wlgb.mapper.WlgbHxyhDzjlMapper;
import com.wlgb.mapper.WlgbHxyhYjWkDzjlMapper;
import com.wlgb.service.WlgbHxyhDzjlService;
import com.wlgb.service.WlgbHxyhYjWkDzjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月03日 23:52
 */
@Service
public class WlgbHxyhYjWkDzjlServiceImpl implements WlgbHxyhYjWkDzjlService {
    @Resource
    private WlgbHxyhYjWkDzjlMapper wlgbHxyhYjWkDzjlMapper;

    @Override
    public void save(WlgbHxyhYjWkDzjl wlgbHxyhDzjl) {
        wlgbHxyhDzjl.setCreateTime(new Date());
        wlgbHxyhYjWkDzjlMapper.insertSelective(wlgbHxyhDzjl);
    }

    @Override
    public void updateById(WlgbHxyhYjWkDzjl wlgbHxyhDzjl) {
        wlgbHxyhDzjl.setUpdateTime(new Date());
        wlgbHxyhYjWkDzjlMapper.updateByPrimaryKeySelective(wlgbHxyhDzjl);
    }

    @Override
    public WlgbHxyhYjWkDzjl queryByYsBhAndLsBh(String ysBh, String lsBh) {
        Example example = new Example(WlgbHxyhYjWkDzjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tradeNo", ysBh);
        criteria.andEqualTo("outTradeNo", lsBh);
        return wlgbHxyhYjWkDzjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbHxyhYjWkDzjl> queryBdj() {
        Example example = new Example(WlgbHxyhYjWkDzjl.class);
        Example.Criteria criteria = example.createCriteria();
//        criteria.andCondition()
        criteria.andCondition("sflrjd = 0");
        return wlgbHxyhYjWkDzjlMapper.selectByExample(example);
    }

    @Override
    public WlgbHxyhYjWkDzjl queryWlgbHxyhYjWkDzjlByWlgbHxyhYjWkDzjl(WlgbHxyhYjWkDzjl wlgbHxyhYjWkDzjl) {
        return wlgbHxyhYjWkDzjlMapper.selectOne(wlgbHxyhYjWkDzjl);
    }
}
