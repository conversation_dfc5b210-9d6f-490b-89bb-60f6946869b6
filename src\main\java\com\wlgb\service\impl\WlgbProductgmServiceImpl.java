package com.wlgb.service.impl;

import com.wlgb.entity.WlgbProductgm;
import com.wlgb.mapper.WlgbProductgmMapper;
import com.wlgb.service.WlgbProductgmService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/18 19:09
 */
@Service
public class WlgbProductgmServiceImpl implements WlgbProductgmService {
    @Resource
    private WlgbProductgmMapper wlgbProductgmMapper;

    @Override
    public void save(WlgbProductgm wlgbProductgm) {
        wlgbProductgm.setCreateTime(new Date());
        wlgbProductgmMapper.insertSelective(wlgbProductgm);
    }

    @Override
    public void updateById(WlgbProductgm wlgbProductgm) {
        wlgbProductgm.setUpdateTime(new Date());
        wlgbProductgmMapper.updateByPrimaryKeySelective(wlgbProductgm);
    }

    @Override
    public List<WlgbProductgm> queryByDdbhList(String ddbh) {
        Example example = new Example(WlgbProductgm.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ddbh", ddbh);
        criteria.andEqualTo("sfsc", 0);
        return wlgbProductgmMapper.selectByExample(example);
    }
}
