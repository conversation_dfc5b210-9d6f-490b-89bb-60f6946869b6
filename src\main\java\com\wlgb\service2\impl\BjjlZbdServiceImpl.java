package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.BjjlZbd;
import com.wlgb.mapper.BjjlZbdMapper;
import com.wlgb.service2.BjjlZbdService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/26 19:01
 */
@Service
@DS(value = "second")
public class BjjlZbdServiceImpl implements BjjlZbdService {
    @Resource
    private BjjlZbdMapper bjjlZbdMapper;

    @Override
    public void save(BjjlZbd bjjlZbd) {
        bjjlZbdMapper.insertSelective(bjjlZbd);
    }
}
