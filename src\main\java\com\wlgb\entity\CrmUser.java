package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "crm_user")
public class CrmUser {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**钉钉id*/
    private String userid;
    /**姓名*/
    private String name;
    /**手机号*/
    private String mobile;
    /**头像链接*/
    private String avatar;
}
