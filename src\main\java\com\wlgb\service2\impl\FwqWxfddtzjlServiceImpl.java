package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqWxfddtzjl;
import com.wlgb.mapper.FwqWxfddtzjlMapper;
import com.wlgb.service2.FwqWxfddtzjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

@Service
@DS(value = "second")
public class FwqWxfddtzjlServiceImpl implements FwqWxfddtzjlService {
    @Resource
    private FwqWxfddtzjlMapper fwqWxfddtzjlMapper;

    @Override
    public void save(FwqWxfddtzjl fwqWxfddtzjl) {
        fwqWxfddtzjlMapper.insertSelective(fwqWxfddtzjl);
    }

    @Override
    public FwqWxfddtzjl selectByXddbh(String xddbh) {
        Example example = new Example(FwqWxfddtzjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xddbh", xddbh);
        return fwqWxfddtzjlMapper.selectOneByExample(example);
    }

}
