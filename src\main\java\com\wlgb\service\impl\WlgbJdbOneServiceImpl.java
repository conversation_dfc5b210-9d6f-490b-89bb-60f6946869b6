package com.wlgb.service.impl;

import com.wlgb.entity.WlgbJdb;
import com.wlgb.entity.WlgbJdbOne;
import com.wlgb.mapper.WlgbJdbOneMapper;
import com.wlgb.service.WlgbJdbOneService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

@Service
public class WlgbJdbOneServiceImpl implements WlgbJdbOneService {
    @Resource
    private WlgbJdbOneMapper wlgbJdbOneMapper;

    @Override
    public void save(WlgbJdbOne wlgbJdbOne) {
        wlgbJdbOneMapper.insertSelective(wlgbJdbOne);
    }

    @Override
    public void updateById(WlgbJdbOne wlgbJdbOne) {
        wlgbJdbOneMapper.updateByPrimaryKeySelective(wlgbJdbOne);
    }

    @Override
    public WlgbJdbOne queryByXid(String xid) {
        Example example = new Example(WlgbJdbOne.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xid", xid);
        return wlgbJdbOneMapper.selectOneByExample(example);
    }
}
