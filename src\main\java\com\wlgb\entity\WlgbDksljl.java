package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: Class
 * @author: xuzhen<PERSON>i
 * @date: 2023年04月17日 15:44
 */
@Data
@Table(name = "wlgb_dksljl")
public class WlgbDksljl {
    /**主键*/
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
    /**创建人*/
    private java.lang.String createBy;
    /**创建日期*/
    private java.util.Date createTime;
    /**更新人*/
    private java.lang.String updateBy;
    /**更新日期*/
    private java.util.Date updateTime;
    /**所属部门*/
    private java.lang.String sysOrgCode;
    /**实例id*/
    private java.lang.String slid;
    /**提交人id*/
    private java.lang.String tjrid;
    /**提交时间*/
    private java.util.Date tjsj;
    /**提交人*/
    private java.lang.String tjr;
    /**备注*/
    private java.lang.String bz;
    /**接收人*/
    private java.lang.String jsr;
    /**订单id*/
    private java.lang.String ddid;
    /**别墅id*/
    private java.lang.String bsid;
}
