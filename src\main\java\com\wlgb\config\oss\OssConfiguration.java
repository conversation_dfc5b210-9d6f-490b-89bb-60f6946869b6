package com.wlgb.config.oss;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Date 2022/11/27 13:23
 * @Version 1.0
 */
@Configuration
public class OssConfiguration {
    @Value("${oss.aliyun.endpoint}")
    private String endpoint;
    @Value("${oss.aliyun.accessKey}")
    private String accessKeyId;
    @Value("${oss.aliyun.secretKey}")
    private String accessKeySecret;
    @Value("${oss.aliyun.bucketName}")
    private String bucketName;
    @Value("${oss.aliyun.staticDomain}")
    private String staticDomain;

    @Bean
    public void initOssBootConfiguration() {
        OssBootUtil.setEndPoint(endpoint);
        OssBootUtil.setAccessKeyId(accessKeyId);
        OssBootUtil.setAccessKeySecret(accessKeySecret);
        OssBootUtil.setBucketName(bucketName);
        OssBootUtil.setStaticDomain(staticDomain);
    }
}
