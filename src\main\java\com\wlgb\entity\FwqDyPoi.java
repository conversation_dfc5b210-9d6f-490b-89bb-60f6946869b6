package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "fwq_dy_poi")
public class FwqDyPoi {
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
    private java.lang.String accountid;
    private java.lang.String bz;
    private java.lang.String pid;
    private java.lang.String poiname;
    private java.lang.String address;
    private java.lang.String sfsc;

}
