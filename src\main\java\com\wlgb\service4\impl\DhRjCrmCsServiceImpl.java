package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmCs;
import com.wlgb.mapper.CrmCsMapper;
import com.wlgb.service4.DhRjCrmCsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 18:14
 */
@Service
@DS("fourth")
public class DhRjCrmCsServiceImpl implements DhRjCrmCsService {
    @Resource
    private CrmCsMapper crmCsMapper;

    @Override
    public void save(CrmCs crmCs) {
        crmCsMapper.insertSelective(crmCs);
    }

    @Override
    public void updateById(CrmCs crmCs) {
        crmCsMapper.updateByPrimaryKeySelective(crmCs);
    }

    @Override
    public CrmCs queryCrmCsByCrmCs(CrmCs crmCs) {
        return crmCsMapper.selectOne(crmCs);
    }
}
