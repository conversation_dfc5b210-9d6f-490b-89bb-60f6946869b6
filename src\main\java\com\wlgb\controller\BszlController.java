package com.wlgb.controller;

import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.TbYddBszl;
import com.wlgb.entity.TbYddNBUser;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.TbYddBszlService;
import com.wlgb.service2.TbYddNBUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.wlgb.config.Tools.isEmpty;

@RestController
@RequestMapping(value = "/wlgb/ydd/bszl")
public class BszlController {
    @Autowired
    private TbYddBszlService tbYddBszlService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;

    /**
     * 返回别墅资料
     */
    @RequestMapping(value = "/getbszl")
    public Result getbszl(HttpServletRequest request) {
        String vname = request.getParameter("vname");
        if (isEmpty(vname)) {
            return Result.error("vname空的");
        }
        TbYddBszl tbYddBszl = tbYddBszlService.queryTbYddBszlByVname(vname);
        return Result.OK(tbYddBszl);
    }

    /**
     * 将宜搭的别墅一镜到底视频url表单保存到数据库中
     */
    @RequestMapping(value = "/savebszl")
    public Result savebszl(HttpServletRequest request) {
        String vname = request.getParameter("vname");
        if (isEmpty(vname)) {
            return Result.error("vname空的");
        }
        String vid = request.getParameter("vid");
        if (isEmpty(vid)) {
            return Result.error("vid空的");
        }
        String url = request.getParameter("url");
        if (isEmpty(url)) {
            return Result.error("url空的");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("vname", vname);
        paramMap.put("vid", vid);
        paramMap.put("url", url);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/ydd/bszl/savebszlTaSk", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.OK();
    }

    @RequestMapping(value = "savebszlTaSk")
    public Result savebszlTaSk(HttpServletRequest request) throws ApiException {
        String vname = request.getParameter("vname");
        String vid = request.getParameter("vid");
        String url = request.getParameter("url");
        TbYddBszl tbYddBszl = new TbYddBszl();
        tbYddBszl.setUrl(url);
        tbYddBszl.setVname(vname);
        tbYddBszl.setVid(vid);
        TbYddBszl tb = tbYddBszlService.queryTbYddBszlByVname(vname);
        if (tb != null && tb.getId() != null) {
            tb.setUrl(url);
            tb.setVname(vname);
            tb.setVid(vid);
            tbYddBszlService.updateById(tb);
        } else {
            tbYddBszlService.save(tbYddBszl);
        }
        return Result.OK();
    }

}
