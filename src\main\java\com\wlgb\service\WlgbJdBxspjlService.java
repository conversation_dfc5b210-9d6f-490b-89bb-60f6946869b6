package com.wlgb.service;

import com.wlgb.entity.WlgbJdBxspjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 20:50
 */
public interface WlgbJdBxspjlService {
    void save(WlgbJdBxspjl wlgbJdBxspjl);

    void updateById(WlgbJdBxspjl wlgbJdBxspjl);

    WlgbJdBxspjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd);

    List<WlgbJdBxspjl> queryListByWlgbJdBxspjl(WlgbJdBxspjl wlgbJdBxspjl);
}
