package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 20:22
 */
@Data
@Table(name = "dingding_token")
public class DingDingToken {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**唯一标识*/
    private String appkey;
    /**密钥*/
    private String appsecret;
    /**应用id*/
    private String agentId;
    /**公司名称*/
    private String name;
    /**token*/
    private String token;
    /**更新时间*/
    private Date updateTime;
    /*失效时间*/
    private Date sxtime;
}
