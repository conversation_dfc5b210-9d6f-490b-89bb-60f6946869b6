package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: tb_dk
 * @Author: jeecg-boot
 * @Date:   2021-04-12
 * @Version: V1.0
 */
@Data
@Table(name = "tb_dk")
public class TbDk {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
	/**带看状态：0：已到场，1：未到场*/
    private java.lang.String dkzt;
	/**订单编号*/
    private java.lang.String dkddbh;
	/**别墅名称(文字)*/
    private java.lang.String dkbsmc;
	/**城市*/
    private java.lang.String city;
	/**别墅id*/
    private java.lang.String dkbsid;
	/**带看人id*/
    private java.lang.String dkuser;
	/**带看人姓名*/
    private java.lang.String dkusername;
	/**发布人id*/
    private java.lang.String fbuser;
	/**发布人姓名*/
    private java.lang.String fbusername;
	/**发布时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date fbtime;
	/**踩点时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date dktime;
	/**客户姓名*/
    private java.lang.String khname;
	/**客户手机号*/
    private java.lang.String khtel;
	/**大概人数*/
    private java.lang.String dknum;
	/**最低价*/
    private java.lang.String zdj;
	/**最高价*/
    private java.lang.String zgj;
	/**cremark*/
    private java.lang.String bz;
	/**是否接单：0：是，否：1*/
    private java.lang.String sfjd;
	/**带看是否成功：0：是，1：否*/
    private java.lang.String dksfcg;
	/**客户基本信息*/
    private java.lang.String khjbxx;
	/**协议单图片*/
    private java.lang.String xydtp;
	/**带看失败原因：0：价格过高，1：店长服务，2：场地原因，3场次冲突，4：时间未定，5：对比同行，6：其他原因*/
    private java.lang.String dksbyy;
	/**失败原因-价格*/
    private java.lang.String sbyyjg;
	/**失败原因-店长*/
    private java.lang.String sbyydz;
	/**失败原因-场地*/
    private java.lang.String sbyycd;
	/**失败原因-场次*/
    private java.lang.String sbyycc;
	/**失败原因-时间*/
    private java.lang.String sbyysj;
	/**失败原因-同行*/
    private java.lang.String sbyyth;
	/**失败原因-其他*/
    private java.lang.String sbyyqt;
	/**是否删除：0：否，1：是*/
    private java.lang.String sfsc;
	/**删除人id*/
    private java.lang.String scuser;
	/**删除时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date sctime;
	/**修改人id*/
    private java.lang.String xguser;
	/**修改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date xgtime;
	/**完成带看时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date wcdktime;
	/**抢单人id*/
    private java.lang.String qduser;
	/**抢单人姓名*/
    private java.lang.String qdusername;
	/**抢单时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date qdtime;
    /**是否校代发起*/
    private java.lang.String dksfxdfq;
    /**校代电话*/
    private java.lang.String dkxdtel;
    /**crm的id*/
    private java.lang.String crmid;
}
