package com.wlgb.jobs;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiDepartmentListParentDeptsRequest;
import com.dingtalk.api.request.OapiDepartmentListRequest;
import com.dingtalk.api.request.OapiUserListbypageRequest;
import com.dingtalk.api.response.OapiDepartmentListParentDeptsResponse;
import com.dingtalk.api.response.OapiDepartmentListResponse;
import com.dingtalk.api.response.OapiUserListbypageResponse;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.CrmBm;
import com.wlgb.entity.CrmBmxq;
import com.wlgb.entity.CrmUser;
import com.wlgb.entity.DingDingToken;
import com.wlgb.service4.DhRjCrmBmService;
import com.wlgb.service4.DhRjCrmBmXqService;
import com.wlgb.service4.DhRjService;
import com.wlgb.service4.DingDingTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 18:43
 */
@Component
@RestController
@RequestMapping("/dhrj/job")
@Slf4j
public class DhRjJob {
    @Autowired
    private DhRjService dhRjService;
    @Autowired
    private DingDingTokenService dingDingTokenService;
    @Autowired
    private DhRjCrmBmService dhRjCrmBmService;
    @Autowired
    private DhRjCrmBmXqService dhRjCrmBmXqService;

    @RequestMapping(value = "gxDingDingToken")
    public String gxDingDingToken() {
        Dingkey dingkey = dhRjService.queryDingKeyById("dgrhcrm");
        String token = null;
        if (dingkey != null) {
            DingDingToken dingDingToken = dhRjService.queryDingDingTokenByName(dingkey.getName());
            if (dingDingToken != null) {
                if (new Date().getTime() > dingDingToken.getSxtime().getTime()) {
                    try {
                        token = DingToken.token(dingkey);
                    } catch (ApiException e) {
                        e.printStackTrace();
                    }
                    System.out.println(token);
                }
                DingDingToken dingDingToken1 = new DingDingToken();
                dingDingToken1.setToken(token);
                dingDingToken1.setId(dingDingToken.getId());
                Date date = new Date();
                date.setMinutes(date.getMinutes() + 20);
                dingDingToken1.setSxtime(date);
                dingDingTokenService.updateById(dingDingToken1);
            } else {
                DingDingToken dingDingToken1 = new DingDingToken();
                BeanUtils.copyProperties(dingkey, dingDingToken1);
                dingDingToken1.setToken(token);
                dingDingTokenService.save(dingDingToken1);
            }
        }
        return token;
    }

    /**
     * 更新crm用户表(①先更新)
     */
    @RequestMapping(value = "getUList")
    public void getUList() {
        DingDingToken dingDingToken = dhRjService.queryDingDingTokenByName("大户人家crm");
        if (dingDingToken != null) {
            if (new Date().getTime() > dingDingToken.getSxtime().getTime()) {
                String token = gxDingDingToken();
                dingDingToken.setToken(token);
            }
            try {
                List<CrmUser> list = getUser(dingDingToken);
                CrmUser userEntity = new CrmUser();
                //截断crm_user_backups 人员备份表
                dhRjService.qkCrmUserBackupsTable();
                //将crm_user人员表 赋值给 crm_user_backups 人员备份表
                dhRjService.bfCrmUserInBackups();
                dhRjService.qkCrmUserCopyTable();
                if (list.size() > 0) {
                    for (CrmUser crmUser : list) {
                        userEntity.setUserid(crmUser.getUserid());
                        userEntity.setName(crmUser.getName());
                        userEntity.setMobile(crmUser.getMobile());
                        userEntity.setAvatar(crmUser.getAvatar());
                        dhRjService.saveCrmUserCopy(userEntity);
                    }
                }
                //去除重复人员
                dhRjService.delCrmUserCopy();
                //截断crm_user表
                dhRjService.qkCrmUserTable();
                dhRjService.saveCrmUser();

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * (钉钉端)根据部门id遍历得到全体员工信息
     *
     * @param dingDingToken 钉钉Token对象
     * @return 员工信息列表
     */
    public List<CrmUser> getUser(DingDingToken dingDingToken) {
        String token = dingDingToken.getToken();
        List<CrmUser> userlist = new ArrayList<>();
        try {
            int num = 0;
            List<Long> bmlist = getBmListToDingDing(dingDingToken);
            for (Long id : bmlist) {
                // 创建钉钉客户端
                DingTalkClient client1 = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/listbypage");
                // 创建请求对象
                OapiUserListbypageRequest request1 = new OapiUserListbypageRequest();
                request1.setDepartmentId(id);
                request1.setOffset(0L);
                request1.setSize(100L);
                request1.setOrder("entry_desc");
                request1.setHttpMethod("GET");
                // 执行请求
                OapiUserListbypageResponse execute = client1.execute(request1, token);
                // 解析返回结果
                JSONObject objuser = JSONObject.parseObject(execute.getBody());
                JSONArray datauser = objuser.getJSONArray("userlist");
                for (Object u : datauser) {
                    CrmUser userEntity = new CrmUser();
                    JSONObject mapuser = (JSONObject) u;
                    userEntity.setUserid(mapuser.getString("userid") != null ? mapuser.getString("userid") : "");
                    userEntity.setName(mapuser.getString("name"));
                    userEntity.setAvatar(mapuser.getString("avatar"));
                    userEntity.setMobile(mapuser.getString("mobile"));
                    userlist.add(num, userEntity);
                    num = num + 1;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return userlist;
    }


    /**
     * 部门新增
     */
    public List<Long> getBmListToDingDing(DingDingToken dingDingToken) {
        String token = dingDingToken.getToken();
        List<Long> list1 = new ArrayList<>();
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/department/list");
            OapiDepartmentListRequest request = new OapiDepartmentListRequest();
            request.setId("1");
            request.setHttpMethod("GET");
            OapiDepartmentListResponse response = client.execute(request, token);
            //将获取的数据转化成json数据，并单独将部门id取出
            JSONObject obj = JSONObject.parseObject(response.getBody());
            JSONArray data = obj.getJSONArray("department");

            //截断部门表
            dhRjService.qkCrmBmTable();
            for (Object s : data) {
                CrmBm bmentity = new CrmBm();
                JSONObject mapbm = (JSONObject) s;
                long idd = mapbm.getLong("id");
                list1.add(idd);
                bmentity.setBmid(mapbm.getString("id"));
                bmentity.setBmname(mapbm.getString("name"));
                bmentity.setParentid(mapbm.getString("parentid"));
                dhRjCrmBmService.save(bmentity);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list1;
    }

    /**
     * 更新crm部门详情表（②后更新）
     */
    @RequestMapping(value = "getBmxq")
    public void getBmxq() {
        DingDingToken dingDingToken = dhRjService.queryDingDingTokenByName("大户人家crm");
        if (dingDingToken != null) {
            if (new Date().getTime() > dingDingToken.getSxtime().getTime()) {
                String token = gxDingDingToken();
                dingDingToken.setToken(token);
            }
            System.out.println("crm_bmxq 开始更新");
            try {
                List<CrmUser> useridlist = dhRjService.queryCrmUserList();
                List<CrmBmxq> Lbmxq = new ArrayList<>();

                for (int i = 0; i < useridlist.size(); i++) {
                    CrmBmxq bmxq = getBmXqToDingDing(useridlist.get(i).getUserid(), useridlist.get(i).getName(), dingDingToken);
                    Lbmxq.add(i, bmxq);
                }

                dhRjService.qkCrmBmXqTable();
                for (CrmBmxq crmBmxq : Lbmxq) {
                    dhRjCrmBmXqService.save(crmBmxq);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取部门详情
     */
    public CrmBmxq getBmXqToDingDing(String userid, String name, DingDingToken dingDingToken) throws ApiException {
        String token = dingDingToken.getToken();
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/department/list_parent_depts");
        OapiDepartmentListParentDeptsRequest request = new OapiDepartmentListParentDeptsRequest();
        request.setUserId(userid);
        request.setHttpMethod("GET");
        OapiDepartmentListParentDeptsResponse response = client.execute(request, token);
        CrmBmxq bmxq = new CrmBmxq();
        try {
            if (response.getDepartment() != null) {
                String bumen = response.getDepartment().replace("[", "");//去除[
                bumen = bumen.replace("]", "");//去除 ]
                bumen = bumen.replace(" ", "");//去除空格
                String[] bmid = bumen.trim().split(",");//将id分别存入String数组
                List<String> bmidl = Arrays.asList(bmid);
                HashSet<String> h = new HashSet<>(bmidl);
                List<String> bmidlist = new ArrayList<>();
                bmidlist.addAll(h);
                bmxq.setUserid(userid);
                bmxq.setName(name);
                if (bmidlist.size() > 0) {
                    bmxq.setBmid(bmidlist.get(0));
                }
                if (bmidlist.size() > 1) {
                    bmxq.setBmid2(bmidlist.get(1));
                }
                if (bmidlist.size() > 2) {
                    bmxq.setBmid3(bmidlist.get(2));
                }
                if (bmidlist.size() > 3) {
                    bmxq.setBmid4(bmidlist.get(3));
                }
                if (bmidlist.size() > 4) {
                    bmxq.setBmid5(bmidlist.get(4));
                }
                if (bmidlist.size() > 5) {
                    bmxq.setBmid6(bmidlist.get(5));
                }
                if (bmidlist.size() > 6) {
                    bmxq.setBmid7(bmidlist.get(6));
                }
                if (bmidlist.size() > 7) {
                    bmxq.setBmid8(bmidlist.get(7));
                }
                if (bmidlist.size() > 8) {
                    bmxq.setBmid9(bmidlist.get(8));
                }
                if (bmidlist.size() > 9) {
                    bmxq.setBmid10(bmidlist.get(9));
                }
                if (bmidlist.size() > 10) {
                    bmxq.setBmid11(bmidlist.get(10));
                }
                if (bmidlist.size() > 11) {
                    bmxq.setBmid12(bmidlist.get(11));
                }
                if (bmidlist.size() > 12) {
                    bmxq.setBmid13(bmidlist.get(12));
                }
                if (bmidlist.size() > 13) {
                    bmxq.setBmid14(bmidlist.get(13));
                }
                if (bmidlist.size() > 14) {
                    bmxq.setBmid15(bmidlist.get(14));
                }
                Thread.sleep(500);
                return bmxq;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bmxq;

    }
}
