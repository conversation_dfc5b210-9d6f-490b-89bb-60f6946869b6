package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/12 16:13
 */
@Data
@Table(name = "wlgb_eat_rkjl")
public class WlgbEatRkjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**创建时间*/
    private Date createTime;
    /**修改时间*/
    private Date updateTime;
    /**流水号*/
    private String lsh;
    /**提交人*/
    private String tjr;
    /**提交人id*/
    private String tjrid;
    /**供应商*/
    private String gys;
    /**供应商编号*/
    private String gysbh;
    /**采购时间*/
    private Date cgtime;
    /**食材名称*/
    private String scmc;
    /**发货单位*/
    private String fhdw;
    /**订购数量*/
    private Integer dgNum;
    /**发货数量*/
    private Integer fhNum;
    /**发货单价*/
    private Double fhPrice;
    /**发货金额*/
    private Double fhSumPrice;
    /**合计总金额*/
    private Double sumJe;
    /**是否删除(0:否，1:是)*/
    private Integer sfsc;

}
