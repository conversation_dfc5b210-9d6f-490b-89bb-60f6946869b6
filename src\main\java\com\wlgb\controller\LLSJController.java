package com.wlgb.controller;

import com.alibaba.fastjson.JSONObject;
import com.meituan.sdk.DefaultMeituanClient;
import com.meituan.sdk.MeituanClient;
import com.meituan.sdk.MeituanResponse;
import com.meituan.sdk.internal.exceptions.MtSdkException;
import com.meituan.sdk.model.ddzh.common.pageQuerySession.PageQuerySessionRequest;
import com.meituan.sdk.model.ddzh.common.pageQuerySession.PageQuerySessionResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.ReceiptConsumeResult;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.TuangouReceiptConsumeRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.TuangouReceiptConsumeResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptPrepare.TuangouReceiptPrepareRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptPrepare.TuangouReceiptPrepareResponse;
import com.meituan.sdk.model.ddzh.ugc.ugcQueryShopReview.UgcQueryShopReviewRequest;
import com.meituan.sdk.model.ddzh.ugc.ugcQueryShopReview.UgcQueryShopReviewResponse;
import com.meituan.sdk.model.ddzh.ugc.ugcQuerystar.UgcQuerystarRequest;
import com.meituan.sdk.model.ddzh.ugc.ugcQuerystar.UgcQuerystarResponse;
import com.meituan.sdk.model.ddzhkh.auth.pageQueryTokenPoiList.PageQueryTokenPoiListRequest;
import com.meituan.sdk.model.ddzhkh.auth.pageQueryTokenPoiList.PageQueryTokenPoiListResponse;
import com.wlgb.config.HttpClientUtil;
import com.wlgb.config.IdConfig;
import com.wlgb.config.Result;
import com.wlgb.config.ResultMt;
import com.wlgb.config.meituan.MeiTuanConfig;
import com.wlgb.entity.WlgbMtJl;
import com.wlgb.entity.WlgbMtLog;
import com.wlgb.entity.WlgbMtMd;
import com.wlgb.service.WlgbMtJlService;
import com.wlgb.service.WlgbMtMdService;
import com.wlgb.service2.WeiLianService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;

import static com.wlgb.config.oss.oConvertUtils.isEmpty;

/**
 * @Description: Class  调用存储过程的接口
 * @author: fwq
 * @date: 2024年08月28日 19:56
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/ccgc")
public class LLSJController {
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;
    @Autowired
    private WeiLianService weiLianService;

    /**
     * 查询数据(异步调用存储过程)
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/zxccgc")
    public Result zxccgc(HttpServletRequest request) {
        //存储过程的名字
        String ccgcnama = request.getParameter("ccgcnama");
        String kssj = request.getParameter("kssj");
        String jssj = request.getParameter("jssj");
        if (isEmpty(ccgcnama) || isEmpty(kssj) || isEmpty(jssj)) {
            return Result.error("缺少参数");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("ccgcnama", ccgcnama);
        paramMap.put("kssj", kssj);
        paramMap.put("jssj", jssj);
        //在这里进行异步调用函数计算里面的函数即可
        log.info("******打印参数********{}++++{}++++{}",ccgcnama,kssj,jssj);
        try {
            HttpClientUtil.postAsync("http://47.113.125.34:8080/wlkf/wlgb/ccgc/dyccgc", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }


        return Result.OK();
    }

}
