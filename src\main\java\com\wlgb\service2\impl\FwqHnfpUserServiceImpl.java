package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqHnfp;
import com.wlgb.entity.FwqHnfpUser;
import com.wlgb.mapper.FwqHnfpMapper;
import com.wlgb.mapper.FwqHnfpUserMapper;
import com.wlgb.service2.FwqHnfpService;
import com.wlgb.service2.FwqHnfpUserService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

import static com.wlgb.config.Tools.isEmpty;

@Service
@DS(value = "second")
public class FwqHnfpUserServiceImpl implements FwqHnfpUserService {
    @Resource
    private FwqHnfpUserMapper fwqHnfpUserMapper;

    @Override
    public void save(FwqHnfpUser fwqHnfp) {
        fwqHnfpUserMapper.insertSelective(fwqHnfp);
    }

    @Override
    public void deleteByPrimaryKey(FwqHnfpUser fwqHnfp) {
        fwqHnfpUserMapper.deleteByPrimaryKey(fwqHnfp);
    }

    @Override
    public FwqHnfpUser selectOneByTelName(String tel, String name) {
        Example example = new Example(FwqHnfpUser.class);
        Example.Criteria criteria = example.createCriteria();
        if (!isEmpty(tel)) {
            criteria.andEqualTo("tel", tel);
        }
        if (!isEmpty(name)) {
            criteria.andEqualTo("name", name);
        }
        return fwqHnfpUserMapper.selectOneByExample(example);
    }

    @Override
    public void update(FwqHnfpUser fwqHnfp) {
        fwqHnfpUserMapper.updateByPrimaryKey(fwqHnfp);
    }

    @Override
    public List<FwqHnfpUser> selectAllByUserid(String url) {
        Example example = new Example(FwqHnfpUser.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("zyurl", url);
        example.setOrderByClause("time DESC");
        return fwqHnfpUserMapper.selectByExample(example);
    }
}
