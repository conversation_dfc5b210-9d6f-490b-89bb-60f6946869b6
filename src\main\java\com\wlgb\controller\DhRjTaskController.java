package com.wlgb.controller;

import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service4.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 12:07
 */
@RestController
@RequestMapping(value = "/dhrj/task")
public class DhRjTaskController {
    @Autowired
    private DhRjService dhRjService;
    @Autowired
    private DhrjXydService dhrjXydService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private DhRjXydLogService dhRjXydLogService;
    @Autowired
    private DhRjOssFileService ossFileService;
    @Autowired
    private DhrjVillaService dhrjVillaService;
    @Autowired
    private DhRjCrmQbkhService dhRjCrmQbkhService;
    @Autowired
    private DhRjCrmXgjlbService dhRjCrmXgjlbService;
    @Autowired
    private JqrConfig jqrConfig;
    @Autowired
    private DingDingTokenService dingDingTokenService;


    /**
     * 下单提交异步
     */
    @RequestMapping(value = "saveXydTask")
    public void saveXydTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null || "".equals(datas)) {
            return;
        }
        String userId = request.getParameter("userId");
        String formInstId = request.getParameter("formInstId");

        DingdingEmployee ding = null;
        if (userId != null && !"".equals(userId)) {
            String userId1 = userId;
            //针对手机用户提交
            if (!userId.contains("$")) {
                userId = "ding968d75cfe0d9045c4ac5d6980864d335$" + userId;
            }
            DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(userId);
            if (dingdingEmployee != null) {
                ding = dingdingEmployee;
            } else {
                DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                if (dingdingEmployee1 != null) {
                    DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                    if (dingdingEmployee2 != null) {
                        ding = dingdingEmployee2;
                    }
                }
            }
        }

        JSONObject jsonObject = JSONObject.parseObject(datas);
        DhrjXyd dhrjXyd = JSONObject.toJavaObject(jsonObject, DhrjXyd.class);
        if (dhrjXyd.getXfd() != null && !"".equals(dhrjXyd.getXfd())) {
            dhrjXyd.setXfd(xzJq(dhrjXyd.getXfd()));
            String userId1 = dhrjXyd.getXfd();
            //针对手机用户提交
            if (!dhrjXyd.getXfd().contains("$")) {
                dhrjXyd.setXfd("ding968d75cfe0d9045c4ac5d6980864d335$" + dhrjXyd.getXfd());
            }
            DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(dhrjXyd.getXfd());
            if (dingdingEmployee != null) {
                dhrjXyd.setXfd(dingdingEmployee.getName());
                dhrjXyd.setXfdid(dingdingEmployee.getUserid());
            } else {
                DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                if (dingdingEmployee1 != null) {
                    DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                    if (dingdingEmployee2 != null) {
                        dhrjXyd.setXfd(dingdingEmployee2.getName());
                        dhrjXyd.setXfdid(dingdingEmployee2.getUserid());
                    }
                }
            }
        }
        dhrjXyd.setSlid(formInstId);
        dhrjXyd.setXsender(ding != null ? ding.getUserid() : userId);
        dhrjXyd.setXsendtime(new Date());
        dhrjXyd.setXbsmc(jsonObject.getString("textField_lnwxni3w"));
        dhrjXyd.setXtdxzbh(jsonObject.getString("textField_lnwxni3z"));
        dhrjXyd.setXkhlybh(jsonObject.getString("textField_lnwxni3y"));
        dhrjXyd.setCity(jsonObject.getString("textField_lnwxni3x"));
        //是否泳池单
        dhrjXyd.setXsfycd(jsonObject.getString("radioField_lnyap7us"));
        dhrjXyd.setXzdjlfwje(jsonObject.getDouble("numberField_lny9uixi"));
        dhrjXyd.setXchbjje(jsonObject.getDouble("numberField_lny9uixj"));
        dhrjXyd.setXhpsje(jsonObject.getDouble("numberField_lny9uixk"));
        dhrjXyd.setXtjhfje(jsonObject.getDouble("numberField_lny9uixl"));
        dhrjXyd.setXjsspje(jsonObject.getDouble("numberField_lny9uixm"));
        dhrjXyd.setXysf(jsonObject.getDouble("numberField_lo2as2b1"));
        dhrjXyd.setXzs(jsonObject.getInteger("numberField_lowkdq31"));
//        dhrjXyd.setXkhjczfje(jsonObject.getDouble("numberField_lny9uixn"));

        //需要改协议单图片，把触发api改成否
        dhrjXyd.setXapi(1);

        if (dhrjXyd.getXdjtype() != null) {
            if (dhrjXyd.getXdjtype() == 1) {
                dhrjXyd.setXxsdj(null);
                dhrjXyd.setXfkm(null);
                dhrjXyd.setXyqrq(null);
                dhrjXyd.setXyqr(null);
                dhrjXyd.setXdm(null);
            } else if (dhrjXyd.getXdjtype() == 2) {
                dhrjXyd.setXxxdj(null);
                dhrjXyd.setXzzsj(null);
                dhrjXyd.setXzzhlw(null);
            } else {
                //已收定金=线上+线下
                dhrjXyd.setXysdj((dhrjXyd.getXxsdj() != null ? dhrjXyd.getXxsdj() : 0.0) + (dhrjXyd.getXxxdj() != null ? dhrjXyd.getXxxdj() : 0.0));
            }
        }

        try {
            DhrjVilla dhrjVilla = new DhrjVilla();
            dhrjVilla.setMdbh(dhrjXyd.getXbsmc());
            DhrjVilla dhrjVilla1 = dhrjVillaService.queryDhrjVillaByDhrjVilla(dhrjVilla);
            Map<String, Object> xydMap = new HashMap<>();

            if (dhrjVilla1 != null) {
                if ("宴会".equals(dhrjVilla1.getVxz())) {
                    xydMap = dhRjYhDzXyd(dhrjXyd, dhrjVilla1);
                } else {
                    xydMap = dhRjDzXyd(dhrjXyd, dhrjVilla1);
                }
            }
            if (xydMap.size() > 0) {
                dhrjXyd.setXimagepath((String) xydMap.get("png"));
            }
            String filePath = FileConfig.getFileAbsolutePath2("static" + File.separator + "img" + File.separator +
                    dhrjXyd.getXimagepath());
            File file = new File(filePath);
            String upload = "";
            try {
                FileInputStream fileInputStream = new FileInputStream(file);
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
                fileInputStream.close();
                upload = ossFileService.upload(multipartFile);
                if (upload != null && !"".equals(upload)) {
                    upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            dhrjXyd.setXpdfpath(upload);
            dhrjXyd.setXimagepath(upload);
        } catch (Exception e) {
            e.printStackTrace();
        }

        dhrjXydService.save(dhrjXyd);

        String cc = "跨";
        if (dhrjXyd.getCc() != null && !"".equals(dhrjXyd.getCc())) {
            if ("1".equals(dhrjXyd.getCc())) {
                cc = "午餐场";
            } else if ("2".equals(dhrjXyd.getCc())) {
                cc = "晚餐场";
            } else if ("3".equals(dhrjXyd.getCc())) {
                cc = "晚晚通宵场";
            }
        }
        String xnc = "【恭喜" + dhrjXyd.getXfd();
        int fddsum = dhRjService.queryDhRjXydMothCountByXfd(dhrjXyd.getXfd());
        xnc += "[耶][鼓掌]本月第" + fddsum + "个单！[鼓掌][耶]】";
        xnc += "\n\uD83D\uDC4D 恭喜" + dhrjXyd.getXfd() + "刚刚下了1个" + dhrjXyd.getCity() + "的" + dhrjXyd.getXbsmc2() + "单";
        int ddnum = dhRjService.queryDhRjXydDateCountByXfd(dhrjXyd.getXfd());
        String body = xnc + "，今天第" + ddnum + "个单。\uD83D\uDE0D";

        if (ddnum == 0) {
            ddnum = dhRjService.queryDhRjXydYesCountByXfd(dhrjXyd.getXfd());
            body = xnc + "，昨天第" + ddnum + "个单。\uD83D\uDE0D";
        }
        body += "\n聚会类型：" + dhrjXyd.getXtdxz();
        body += "\n客户渠道：" + dhrjXyd.getXkhly();
        body += "\n进退场时间：" + DateFormatConfig.df2(dhrjXyd.getXjctime()) + "———" + DateFormatConfig.df2(dhrjXyd.getXtctime()) + "，" + getWeek(cc, dhrjXyd.getXjctime()) + cc + "单";
        Double xdYj = dhRjService.queryDhRjXdYjByXfd(dhrjXyd.getXfd());
        body += "\n" + dhrjXyd.getXfd() + "本月下单业绩[比心]" + xdYj + "[比心]";


        String wook = jqrConfig.getDhrjxydwebhook();
        String se = jqrConfig.getDhrjxydkey();

        String content = "![图片](" + dhrjXyd.getXimagepath() + ")";
        DingDingUtil.sendMark(wook, se, "电子协议单", content, null, false);
        DingDingUtil.sendMsg(wook, se, body, null, false);

        WlgbXydLog wlgbXydLog = new WlgbXydLog();
        wlgbXydLog.setXltext("下单");
        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
        wlgbXydLog.setXlxid(dhrjXyd.getXddbh());

        dhRjXydLogService.save(wlgbXydLog);

        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_WGE8YPM32BUH1BPZD6N4");
        ydAppkey.setToken("E8866MB1GLNB3ME6AG8TX8LNXWV62CUEXQWIL22");
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("aimg", YdConfig.setTpList(dhrjXyd.getXimagepath(), "协议单图片"));
        String gxbd = YdConfig.gxbd(jsonObject1.toJSONString(), ydAppkey.getAppkey(), ydAppkey.getToken(), formInstId);
        System.out.println(gxbd);
    }


    /**
     * 改单异步
     */
    @RequestMapping(value = "xydEditTask")
    public void xydEditTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return;
        }
        String userId = request.getParameter("userId");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        DhrjXyd dhrjXyd = JSONObject.toJavaObject(jsonObject, DhrjXyd.class);

        DhrjXyd dhrjXyd1 = new DhrjXyd();
        dhrjXyd1.setXddbh(dhrjXyd.getXddbh());
        dhrjXyd1.setXsfsc(0);
        DhrjXyd dhrjXyd2 = dhrjXydService.queryDhRjXydByDhRjXyd(dhrjXyd1);
        if (dhrjXyd2 == null) {
            return;
        }

        dhrjXyd.setId(dhrjXyd2.getId());
        String formInstId = request.getParameter("formInstId");

        DingdingEmployee ding = null;
        if (userId != null && !"".equals(userId)) {
            String userId1 = userId;
            //针对手机用户提交
            if (!userId.contains("$")) {
                userId = "ding968d75cfe0d9045c4ac5d6980864d335$" + userId;
            }
            DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(userId);
            if (dingdingEmployee != null) {
                ding = dingdingEmployee;
            } else {
                DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                if (dingdingEmployee1 != null) {
                    DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                    if (dingdingEmployee2 != null) {
                        ding = dingdingEmployee2;
                    }
                }
            }
        }

        if (dhrjXyd.getXfd() != null && !"".equals(dhrjXyd.getXfd())) {
            dhrjXyd.setXfd(xzJq(dhrjXyd.getXfd()));
            String userId1 = dhrjXyd.getXfd();
            //针对手机用户提交
            if (!dhrjXyd.getXfd().contains("$")) {
                dhrjXyd.setXfd("ding968d75cfe0d9045c4ac5d6980864d335$" + dhrjXyd.getXfd());
            }
            DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(dhrjXyd.getXfd());
            if (dingdingEmployee != null) {
                dhrjXyd.setXfd(dingdingEmployee.getName());
                dhrjXyd.setXfdid(dingdingEmployee.getUserid());
            } else {
                DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                if (dingdingEmployee1 != null) {
                    DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                    if (dingdingEmployee2 != null) {
                        dhrjXyd.setXfd(dingdingEmployee2.getName());
                        dhrjXyd.setXfdid(dingdingEmployee2.getUserid());
                    }
                }
            }
        }

        dhrjXyd.setXediter(ding != null ? ding.getUserid() : userId);
        dhrjXyd.setXedittime(new Date());
        dhrjXyd.setXbsmc(jsonObject.getString("textField_lnwxni3w"));
        dhrjXyd.setXtdxzbh(jsonObject.getString("textField_lnwxni3z"));
        dhrjXyd.setXkhlybh(jsonObject.getString("textField_lnwxni3y"));
        dhrjXyd.setCity(jsonObject.getString("textField_lnwxni3x"));
        //是否泳池单
        dhrjXyd.setXsfycd(jsonObject.getString("radioField_lnyap7us"));
        dhrjXyd.setXzdjlfwje(jsonObject.getDouble("numberField_lny9uixi"));
        dhrjXyd.setXchbjje(jsonObject.getDouble("numberField_lny9uixj"));
        dhrjXyd.setXhpsje(jsonObject.getDouble("numberField_lny9uixk"));
        dhrjXyd.setXtjhfje(jsonObject.getDouble("numberField_lny9uixl"));
        dhrjXyd.setXjsspje(jsonObject.getDouble("numberField_lny9uixm"));
        dhrjXyd.setXysf(jsonObject.getDouble("numberField_lo2as2b1"));
        dhrjXyd.setXzs(jsonObject.getInteger("numberField_lowkdq31"));
//        dhrjXyd.setXkhjczfje(jsonObject.getDouble("numberField_lny9uixn"));

        //需要改协议单图片，把触发api改成否
        dhrjXyd.setXapi(1);

        if (dhrjXyd.getXdjtype() != null) {
            if (dhrjXyd.getXdjtype() == 1) {
                dhrjXyd.setXxsdj(null);
                dhrjXyd.setXfkm(null);
                dhrjXyd.setXyqrq(null);
                dhrjXyd.setXyqr(null);
                dhrjXyd.setXdm(null);
            } else if (dhrjXyd.getXdjtype() == 2) {
                dhrjXyd.setXxxdj(null);
                dhrjXyd.setXzzsj(null);
                dhrjXyd.setXzzhlw(null);
            } else {
                //已收定金=线上+线下
                dhrjXyd.setXysdj((dhrjXyd.getXxsdj() != null ? dhrjXyd.getXxsdj() : 0.0) + (dhrjXyd.getXxxdj() != null ? dhrjXyd.getXxxdj() : 0.0));
            }
        }

        try {
            dhrjXyd.setXsendtime(dhrjXyd2.getXsendtime());
            DhrjVilla dhrjVilla = new DhrjVilla();
            dhrjVilla.setMdbh(dhrjXyd.getXbsmc());
            DhrjVilla dhrjVilla1 = dhrjVillaService.queryDhrjVillaByDhrjVilla(dhrjVilla);
            Map<String, Object> xydMap = new HashMap<>();

            if (dhrjVilla1 != null) {
                if ("宴会".equals(dhrjVilla1.getVxz())) {
                    xydMap = dhRjYhDzXyd(dhrjXyd, dhrjVilla1);
                } else {
                    xydMap = dhRjDzXyd(dhrjXyd, dhrjVilla1);
                }
            }
            if (xydMap.size() > 0) {
                dhrjXyd.setXimagepath((String) xydMap.get("png"));
            }
            String filePath = FileConfig.getFileAbsolutePath2("static" + File.separator + "img" + File.separator +
                    dhrjXyd.getXimagepath());
            File file = new File(filePath);
            String upload = "";
            try {
                FileInputStream fileInputStream = new FileInputStream(file);
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), fileInputStream);
                fileInputStream.close();
                upload = ossFileService.upload(multipartFile);
                if (upload != null && !"".equals(upload)) {
                    upload = upload.replace("https://jz-appjiuyundaojia.oss-cn-shenzhen.aliyuncs.com", "http://jiuyun2.qianquan888.com");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            dhrjXyd.setXpdfpath(upload);
            dhrjXyd.setXimagepath(upload);
        } catch (Exception e) {
            e.printStackTrace();
        }

        dhrjXydService.updateById(dhrjXyd);

        String cc = "跨";
        if (dhrjXyd.getCc() != null && !"".equals(dhrjXyd.getCc())) {
            if ("1".equals(dhrjXyd.getCc())) {
                cc = "午餐场";
            } else if ("2".equals(dhrjXyd.getCc())) {
                cc = "晚餐场";
            } else if ("3".equals(dhrjXyd.getCc())) {
                cc = "晚晚通宵场";
            }
        }
        String body = "修改了一个单。";
        body += "\n聚会类型：" + dhrjXyd.getXtdxz();
        body += "\n客户渠道：" + dhrjXyd.getXkhly();
        body += "\n进退场时间：" + DateFormatConfig.df2(dhrjXyd.getXjctime()) + "———" + DateFormatConfig.df2(dhrjXyd.getXtctime()) + "，" + getWeek(cc, dhrjXyd.getXjctime()) + cc + "单";
        Double xdYj = dhRjService.queryDhRjXdYjByXfd(dhrjXyd.getXfd());
        body += "\n" + dhrjXyd.getXfd() + "本月下单业绩[比心]" + xdYj + "[比心]";


        String wook = jqrConfig.getDhrjxydwebhook();
        String se = jqrConfig.getDhrjxydkey();


        String content = "![图片](" + dhrjXyd.getXimagepath() + ")";
        DingDingUtil.sendMark(wook, se, "电子协议单", content, null, false);
        DingDingUtil.sendMsg(wook, se, body, null, false);

//        WlgbXydLog wlgbXydLog = new WlgbXydLog();
//        wlgbXydLog.setXltext("改单");
//        wlgbXydLog.setXluserid(ding != null ? ding.getUserid() : null);
//        wlgbXydLog.setXlname(ding != null ? ding.getName() : null);
//        wlgbXydLog.setXlxid(dhrjXyd.getXddbh());
//
//        dhRjXydLogService.save(wlgbXydLog);

        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_WGE8YPM32BUH1BPZD6N4");
        ydAppkey.setToken("E8866MB1GLNB3ME6AG8TX8LNXWV62CUEXQWIL22");
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("aimg", YdConfig.setTpList(dhrjXyd.getXimagepath(), "协议单图片"));
        String gxbd = YdConfig.gxbd(jsonObject1.toJSONString(), ydAppkey.getAppkey(), ydAppkey.getToken(), formInstId);
        System.out.println(gxbd);
    }

    /**
     * 删单异步
     */
    @RequestMapping(value = "xydDelTask")
    public void xydDelTask(HttpServletRequest request) {
        String userId = request.getParameter("userId");
        String xddbh = request.getParameter("xddbh");
        if (xddbh == null || "".equals(xddbh)) {
            return;
        }
        DhrjXyd dhrjXyd = new DhrjXyd();
        dhrjXyd.setXsfsc(0);
        dhrjXyd.setXddbh(xddbh);
        DhrjXyd dhRjXyd = dhrjXydService.queryDhRjXydByDhRjXyd(dhrjXyd);
        if (dhRjXyd == null) {
            return;
        }
        DingdingEmployee ding = null;
        if (userId != null && !"".equals(userId)) {
            String userId1 = userId;
            //针对手机用户提交
            if (!userId.contains("$")) {
                userId = "ding968d75cfe0d9045c4ac5d6980864d335$" + userId;
            }
            DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(userId);
            if (dingdingEmployee != null) {
                ding = dingdingEmployee;
            } else {
                DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                if (dingdingEmployee1 != null) {
                    DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                    if (dingdingEmployee2 != null) {
                        ding = dingdingEmployee2;
                    }
                }
            }
        }

        DhrjXyd dhrjXyd1 = new DhrjXyd();
        dhrjXyd1.setId(dhRjXyd.getId());
        dhrjXyd1.setXsfsc(1);
        dhrjXyd1.setXdeltime(new Date());
        dhrjXyd1.setXdeler(ding != null ? ding.getUserid() : userId);
        dhrjXydService.updateById(dhrjXyd1);


        String cc = "跨";
        if (dhrjXyd.getCc() != null && !"".equals(dhrjXyd.getCc())) {
            if ("1".equals(dhrjXyd.getCc())) {
                cc = "午餐场";
            } else if ("2".equals(dhrjXyd.getCc())) {
                cc = "晚餐场";
            } else if ("3".equals(dhrjXyd.getCc())) {
                cc = "晚晚通宵场";
            }
        }

        String body = "删除了一个单。";
        body += "\n门店：" + dhRjXyd.getXbsmc2();
        body += "\n聚会类型：" + dhRjXyd.getXtdxz();
        body += "\n客户渠道：" + dhRjXyd.getXkhly();
        body += "\n进退场时间：" + DateFormatConfig.df2(dhRjXyd.getXjctime()) + "———" + DateFormatConfig.df2(dhRjXyd.getXtctime()) + "，" + getWeek(cc, dhRjXyd.getXjctime()) + cc + "单";


        String wook = jqrConfig.getDhrjxydwebhook();
        String se = jqrConfig.getDhrjxydkey();

        DingDingUtil.sendMsg(wook, se, body, null, false);
    }

    /**
     * crm新增
     */
    @RequestMapping(value = "saveCrmQbKhTask")
    public void saveCrmQbKhTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String slid = request.getParameter("slid");
        if (datas == null) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        CrmQbkh qbkh = JSONObject.toJavaObject(jsonObject, CrmQbkh.class);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //创建时间转换
        if (qbkh.getQcjsj() != null && !"".equals(qbkh.getQcjsj())) {
            Long cjsj = Long.parseLong(qbkh.getQcjsj());  //获取当前时间戳
            String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
            qbkh.setQcjsj(cjsjsd);

        }
        //修改时间转换
        if (qbkh.getQxgsj() != null && !"".equals(qbkh.getQxgsj())) {
            Long xgsj = Long.parseLong(qbkh.getQxgsj());  //获取当前时间戳
            String xgsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(xgsj))));      // 时间戳转换成时间
            qbkh.setQxgsj(xgsjsd);
        }
        //分配时间转换
        if (qbkh.getQfpsj() != null && !"".equals(qbkh.getQfpsj())) {
            Long fpsj = Long.parseLong(qbkh.getQfpsj());  //获取当前时间戳
            String fpsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(fpsj))));      // 时间戳转换成时间
            qbkh.setQfpsj(fpsjsd);
        }
        //捞取时间转换
        if (qbkh.getQlqsj() != null && !"".equals(qbkh.getQlqsj())) {
            Long lqsj = Long.parseLong(qbkh.getQlqsj());  //获取当前时间戳
            String lqsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(lqsj))));      // 时间戳转换成时间
            qbkh.setQlqsj(lqsjsd);
        }
        //移交时间转换
        if (qbkh.getQyjsj() != null && !"".equals(qbkh.getQyjsj())) {
            Long yjsj = Long.parseLong(qbkh.getQyjsj());  //获取当前时间戳
            String yjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(yjsj))));      // 时间戳转换成时间
            qbkh.setQyjsj(yjsjsd);
        }
        //删除时间转换
        if (qbkh.getQscsj() != null && !"".equals(qbkh.getQscsj())) {
            Long scsj = Long.parseLong(qbkh.getQscsj());  //获取当前时间戳
            String scsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(scsj))));      // 时间戳转换成时间
            qbkh.setQscsj(scsjsd);
        }
        //客户预订时间转换
        if (qbkh.getQkhydsj() != null && !"".equals(qbkh.getQkhydsj())) {
            Long khydsj = Long.parseLong(qbkh.getQkhydsj());  //获取当前时间戳
            String khydsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(khydsj))));      // 时间戳转换成时间
            qbkh.setQkhydsj(khydsjsd);
        }
        //负责人
        try {
            if (qbkh.getQfzrid() != null && !"".equals(qbkh.getQfzrid())) {
                String userId1 = qbkh.getQfzrid();
                //针对手机用户提交
                if (!qbkh.getQfzrid().contains("$")) {
                    qbkh.setQfzrid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQfzrid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQfzrid());
                if (dingdingEmployee != null) {
                    qbkh.setQfzrid(dingdingEmployee.getUserid());
                    qbkh.setQfzr(dingdingEmployee.getName());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQfzrid(dingdingEmployee2.getUserid());
                            qbkh.setQfzr(dingdingEmployee2.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //创建人
        try {
            if (qbkh.getQcjrid() != null && !"".equals(qbkh.getQcjrid())) {
                String userId1 = qbkh.getQcjrid();
                //针对手机用户提交
                if (!qbkh.getQcjrid().contains("$")) {
                    qbkh.setQcjrid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQcjrid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQcjrid());
                if (dingdingEmployee != null) {
                    qbkh.setQcjrid(dingdingEmployee.getUserid());
                    qbkh.setQcjr(dingdingEmployee.getName());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQcjrid(dingdingEmployee2.getUserid());
                            qbkh.setQcjr(dingdingEmployee2.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //修改人
        try {
            if (qbkh.getQxgrid() != null && !"".equals(qbkh.getQxgrid())) {
                String userId1 = qbkh.getQxgrid();
                //针对手机用户提交
                if (!qbkh.getQxgrid().contains("$")) {
                    qbkh.setQxgrid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQxgrid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQxgrid());
                if (dingdingEmployee != null) {
                    qbkh.setQxgr(dingdingEmployee.getName());
                    qbkh.setQxgrid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQxgrid(dingdingEmployee2.getUserid());
                            qbkh.setQxgr(dingdingEmployee2.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //删除人
        try {
            if (qbkh.getQscrid() != null && !"".equals(qbkh.getQscrid())) {
                String userId1 = qbkh.getQscrid();
                //针对手机用户提交
                if (!qbkh.getQscrid().contains("$")) {
                    qbkh.setQscrid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQscrid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQscrid());
                if (dingdingEmployee != null) {
                    qbkh.setQscr(dingdingEmployee.getName());
                    qbkh.setQscrid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQscrid(dingdingEmployee2.getUserid());
                            qbkh.setQscr(dingdingEmployee2.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //移交人id
        try {
            if (qbkh.getQyjuserid() != null && !"".equals(qbkh.getQyjuserid())) {
                String userId1 = qbkh.getQyjuserid();
                //针对手机用户提交
                if (!qbkh.getQyjuserid().contains("$")) {
                    qbkh.setQyjuserid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQyjuserid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQyjuserid());
                if (dingdingEmployee != null) {
                    qbkh.setQyjuserid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQyjuserid(dingdingEmployee2.getUserid());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //分配人id
        try {
            if (qbkh.getQfpuserid() != null && !"".equals(qbkh.getQfpuserid())) {
                String userId1 = qbkh.getQfpuserid();
                //针对手机用户提交
                if (!qbkh.getQfpuserid().contains("$")) {
                    qbkh.setQfpuserid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQfpuserid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQfpuserid());
                if (dingdingEmployee != null) {
                    qbkh.setQfpuserid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQfpuserid(dingdingEmployee2.getUserid());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        qbkh.setSlid(slid);
        dhRjCrmQbkhService.save(qbkh);
        DingDingToken dingDingToken = dhRjService.queryDingDingTokenByName("大户人家crm");
        if (dingDingToken != null) {
            if (new Date().getTime() > dingDingToken.getSxtime().getTime()) {
                String token = gxDingDingToken();
                dingDingToken.setToken(token);
            }
            //发送添加成功通知
            xzCrmGzTz(dingDingToken, qbkh.getQcjr(), qbkh.getQkhwxh(), qbkh.getQfzr(), qbkh.getQkhdh(), qbkh.getQcjrid(), qbkh.getQfzrid(), qbkh);
        }


    }

    /**
     * crm信息修改，添加修改记录表
     */
    @RequestMapping(value = "updateCrmQbKhTask")
    public void updateCrmQbKhTask(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        //查询没有修改前的数据
        CrmQbkh crmQbkh = JSONObject.toJavaObject(jsonObject, CrmQbkh.class);
        CrmQbkh crmQbkh1 = dhRjCrmQbkhService.queryByCrmBh(crmQbkh.getCrmbh());
        if (crmQbkh1 == null) {
            return;
        }

        CrmXgjlb qbkh = JSONObject.toJavaObject(jsonObject, CrmXgjlb.class);
        String xxtz = jsonObject.getString("xxtz");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //创建时间转换
        if (qbkh.getQcjsj() != null && !"".equals(qbkh.getQcjsj())) {
            Long cjsj = Long.parseLong(qbkh.getQcjsj());  //获取当前时间戳
            String cjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(cjsj))));      // 时间戳转换成时间
            qbkh.setQcjsj(cjsjsd);

        }
        //修改时间转换
        if (qbkh.getQxgsj() != null && !"".equals(qbkh.getQxgsj())) {
            Long xgsj = Long.parseLong(qbkh.getQxgsj());  //获取当前时间戳
            String xgsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(xgsj))));      // 时间戳转换成时间
            qbkh.setQxgsj(xgsjsd);
        }
        //分配时间转换
        if (qbkh.getQfpsj() != null && !"".equals(qbkh.getQfpsj())) {
            Long fpsj = Long.parseLong(qbkh.getQfpsj());  //获取当前时间戳
            String fpsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(fpsj))));      // 时间戳转换成时间
            qbkh.setQfpsj(fpsjsd);
        }
        //捞取时间转换
        if (qbkh.getQlqsj() != null && !"".equals(qbkh.getQlqsj())) {
            Long lqsj = Long.parseLong(qbkh.getQlqsj());  //获取当前时间戳
            String lqsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(lqsj))));      // 时间戳转换成时间
            qbkh.setQlqsj(lqsjsd);
        }
        //移交时间转换
        if (qbkh.getQyjsj() != null && !"".equals(qbkh.getQyjsj())) {
            Long yjsj = Long.parseLong(qbkh.getQyjsj());  //获取当前时间戳
            String yjsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(yjsj))));      // 时间戳转换成时间
            qbkh.setQyjsj(yjsjsd);
        }
        //删除时间转换
        if (qbkh.getQscsj() != null && !"".equals(qbkh.getQscsj())) {
            Long scsj = Long.parseLong(qbkh.getQscsj());  //获取当前时间戳
            String scsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(scsj))));      // 时间戳转换成时间
            qbkh.setQscsj(scsjsd);
        }
        //客户预订时间转换
        if (qbkh.getQkhydsj() != null && !"".equals(qbkh.getQkhydsj())) {
            Long khydsj = Long.parseLong(qbkh.getQkhydsj());  //获取当前时间戳
            String khydsjsd = sdf.format(new Date(Long.parseLong(String.valueOf(khydsj))));      // 时间戳转换成时间
            qbkh.setQkhydsj(khydsjsd);
        }
        //负责人
        try {
            if (qbkh.getQfzrid() != null && !"".equals(qbkh.getQfzrid())) {
                String userId1 = qbkh.getQfzrid();
                //针对手机用户提交
                if (!qbkh.getQfzrid().contains("$")) {
                    qbkh.setQfzrid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQfzrid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQfzrid());
                if (dingdingEmployee != null) {
                    qbkh.setQfzr(dingdingEmployee.getName());
                    qbkh.setQfzrid(dingdingEmployee.getUserid());
                    //设置负责人
                    crmQbkh.setQfzr(dingdingEmployee.getName());
                    crmQbkh.setQfzrid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQfzr(dingdingEmployee2.getName());
                            qbkh.setQfzrid(dingdingEmployee2.getName());
                            //设置负责人
                            crmQbkh.setQfzr(dingdingEmployee2.getName());
                            crmQbkh.setQfzrid(dingdingEmployee2.getUserid());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //创建人
        try {
            if (qbkh.getQcjrid() != null && !"".equals(qbkh.getQcjrid())) {
                String userId1 = qbkh.getQcjrid();
                //针对手机用户提交
                if (!qbkh.getQcjrid().contains("$")) {
                    qbkh.setQcjrid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQcjrid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQcjrid());
                if (dingdingEmployee != null) {
                    qbkh.setQcjr(dingdingEmployee.getName());
                    qbkh.setQcjrid(dingdingEmployee.getUserid());
                    crmQbkh.setQcjr(dingdingEmployee.getName());
                    crmQbkh.setQcjrid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQcjrid(dingdingEmployee2.getUserid());
                            qbkh.setQcjr(dingdingEmployee2.getName());
                            crmQbkh.setQcjrid(dingdingEmployee2.getUserid());
                            crmQbkh.setQcjr(dingdingEmployee2.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //修改人
        try {
            if (qbkh.getQxgrid() != null && !"".equals(qbkh.getQxgrid())) {
                String userId1 = qbkh.getQxgrid();
                //针对手机用户提交
                if (!qbkh.getQxgrid().contains("$")) {
                    qbkh.setQxgrid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQxgrid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQxgrid());
                if (dingdingEmployee != null) {
                    qbkh.setQxgr(dingdingEmployee.getName());
                    qbkh.setQxgrid(dingdingEmployee.getUserid());
                    crmQbkh.setQxgr(dingdingEmployee.getName());
                    crmQbkh.setQxgrid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQxgrid(dingdingEmployee2.getUserid());
                            qbkh.setQxgr(dingdingEmployee2.getName());
                            crmQbkh.setQxgrid(dingdingEmployee2.getUserid());
                            crmQbkh.setQxgr(dingdingEmployee2.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //删除人
        try {
            if (qbkh.getQscrid() != null && !"".equals(qbkh.getQscrid())) {
                String userId1 = qbkh.getQscrid();
                //针对手机用户提交
                if (!qbkh.getQscrid().contains("$")) {
                    qbkh.setQscrid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQscrid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQscrid());
                if (dingdingEmployee != null) {
                    qbkh.setQscr(dingdingEmployee.getName());
                    qbkh.setQscrid(dingdingEmployee.getUserid());
                    crmQbkh.setQscr(dingdingEmployee.getName());
                    crmQbkh.setQscrid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQscrid(dingdingEmployee2.getUserid());
                            qbkh.setQscr(dingdingEmployee2.getName());
                            crmQbkh.setQscrid(dingdingEmployee2.getUserid());
                            crmQbkh.setQscr(dingdingEmployee2.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //移交人id
        try {
            if (qbkh.getQyjuserid() != null && !"".equals(qbkh.getQyjuserid())) {
                String userId1 = qbkh.getQyjuserid();
                //针对手机用户提交
                if (!qbkh.getQyjuserid().contains("$")) {
                    qbkh.setQyjuserid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQyjuserid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQyjuserid());
                if (dingdingEmployee != null) {
                    qbkh.setQyjuserid(dingdingEmployee.getUserid());
                    crmQbkh.setQyjuserid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQyjuserid(dingdingEmployee2.getUserid());
                            crmQbkh.setQyjuserid(dingdingEmployee2.getUserid());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //分配人id
        try {
            if (qbkh.getQfpuserid() != null && !"".equals(qbkh.getQfpuserid())) {
                String userId1 = qbkh.getQfpuserid();
                //针对手机用户提交
                if (!qbkh.getQfpuserid().contains("$")) {
                    qbkh.setQfpuserid("ding968d75cfe0d9045c4ac5d6980864d335$" + qbkh.getQfpuserid());
                }
                DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(qbkh.getQfpuserid());
                if (dingdingEmployee != null) {
                    qbkh.setQfpuserid(dingdingEmployee.getUserid());
                    crmQbkh.setQfpuserid(dingdingEmployee.getUserid());
                } else {
                    DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                    if (dingdingEmployee1 != null) {
                        DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                        if (dingdingEmployee2 != null) {
                            qbkh.setQfpuserid(dingdingEmployee2.getUserid());
                            crmQbkh.setQfpuserid(dingdingEmployee2.getUserid());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //修改全部客户表
        crmQbkh.setQxgsj(qbkh.getQxgsj());
        crmQbkh.setQkhydsj(qbkh.getQkhydsj());
        crmQbkh.setQscsj(qbkh.getQscsj());
        crmQbkh.setQyjsj(qbkh.getQyjsj());
        crmQbkh.setQlqsj(qbkh.getQlqsj());
        crmQbkh.setQfpsj(qbkh.getQfpsj());
        crmQbkh.setQcjsj(qbkh.getQcjsj());
        crmQbkh.setQid(crmQbkh1.getQid());
        dhRjCrmXgjlbService.save(qbkh);
        dhRjCrmQbkhService.updateById(crmQbkh);
        //查询修改后的数据
        CrmQbkh crmQbkh2 = dhRjCrmQbkhService.queryByCrmBh(crmQbkh.getCrmbh());
        DingDingToken dingDingToken = dhRjService.queryDingDingTokenByName("大户人家crm");
        if (dingDingToken != null) {
            if (new Date().getTime() > dingDingToken.getSxtime().getTime()) {
                String token = gxDingDingToken();
                dingDingToken.setToken(token);
            }
            if ("删除".equals(xxtz)) {
                scCrmGzTz(dingDingToken, crmQbkh2.getQscr(), crmQbkh2.getQkhwxh(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), crmQbkh2.getQscrid(), crmQbkh2.getQfzrid());
            } else if ("公海池".equals(xxtz)) {
                ghcCrmGzTz(dingDingToken, crmQbkh2.getQxgr(), crmQbkh2.getQkhwxh(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), crmQbkh2.getQxgrid());
            } else if ("分配".equals(xxtz)) {
                //根据钉钉id查询姓名
                if (crmQbkh2.getQfpuserid() != null && !"".equals(crmQbkh2.getQfpuserid())) {
                    String userId1 = crmQbkh2.getQfpuserid();
                    //针对手机用户提交
                    if (!crmQbkh2.getQfpuserid().contains("$")) {
                        crmQbkh2.setQfpuserid("ding968d75cfe0d9045c4ac5d6980864d335$" + crmQbkh2.getQfpuserid());
                    }
                    DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(crmQbkh2.getQfpuserid());
                    if (dingdingEmployee != null) {
                        fpCrmGzTz(dingDingToken, dingdingEmployee.getName(), crmQbkh1.getQfzr(), crmQbkh1.getQfzrid(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), dingdingEmployee.getUserid(), crmQbkh2.getQfzrid(), crmQbkh2.getQkhwxh());
                    } else {
                        DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                        if (dingdingEmployee1 != null) {
                            DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                            if (dingdingEmployee2 != null) {
                                fpCrmGzTz(dingDingToken, dingdingEmployee2.getName(), crmQbkh1.getQfzr(), crmQbkh1.getQfzrid(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), dingdingEmployee2.getUserid(), crmQbkh2.getQfzrid(), crmQbkh2.getQkhwxh());
                            }
                        }
                    }
                }
            } else if ("捞取".equals(xxtz)) {
                lqCrmGzTz(dingDingToken, crmQbkh2.getQxgr(), crmQbkh2.getQkhwxh(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), crmQbkh2.getQxgrid(), crmQbkh2.getQfzrid());
            } else if ("移交".equals(xxtz)) {
                //根据钉钉id查询姓名
                if (crmQbkh2.getQyjuserid() != null && !"".equals(crmQbkh2.getQyjuserid())) {
                    String userId1 = crmQbkh2.getQfpuserid();
                    //针对手机用户提交
                    if (!crmQbkh2.getQyjuserid().contains("$")) {
                        crmQbkh2.setQyjuserid("ding968d75cfe0d9045c4ac5d6980864d335$" + crmQbkh2.getQyjuserid());
                    }
                    DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(crmQbkh2.getQyjuserid());
                    if (dingdingEmployee != null) {
                        yjCrmGzTz(dingDingToken, dingdingEmployee.getName(), crmQbkh1.getQfzr(), crmQbkh1.getQfzrid(), crmQbkh2.getQkhwxh(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), dingdingEmployee.getUserid(), crmQbkh2.getQfzrid(), crmQbkh2);
                    } else {
                        DingdingEmployee dingdingEmployee1 = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId1);
                        if (dingdingEmployee1 != null) {
                            DingdingEmployee dingdingEmployee2 = dhRjService.queryDhRjDingDingEmployeeByName(dingdingEmployee1.getName());
                            if (dingdingEmployee2 != null) {
                                yjCrmGzTz(dingDingToken, dingdingEmployee2.getName(), crmQbkh1.getQfzr(), crmQbkh1.getQfzrid(), crmQbkh2.getQkhwxh(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), dingdingEmployee2.getUserid(), crmQbkh2.getQfzrid(), crmQbkh2);
                            }
                        }
                    }
                }
            } else if ("修改".equals(xxtz)) {
                xgCrmGzTz(dingDingToken, crmQbkh2.getQxgr(), crmQbkh2.getQkhwxh(), crmQbkh2.getQfzr(), crmQbkh2.getQkhdh(), crmQbkh2.getQxgrid(), crmQbkh2.getQfzrid(), crmQbkh2);
            }
        }
    }


    /**
     * 选人截取
     *
     * @param userid 用户id
     * @return 用户id
     */
    private String xzJq(String userid) {
        String s = userid.substring(0, 1);
        if ("[".equals(s)) {
            userid = userid.substring(2, userid.length() - 2);
        }
        return userid;
    }

    /**
     * 获取星期几
     */
    public static String getWeek(String cc, Date date) {
        String[] weeks = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        if ("晚晚通宵场".equals(cc)) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        int week_index = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (week_index < 0) {
            week_index = 0;
        }

        return weeks[week_index];
    }

    /**
     * 生成电子协议单
     *
     * @param xyd 协议单数据
     */
    private Map<String, Object> dhRjDzXyd(DhrjXyd xyd, DhrjVilla dhrjVilla1) throws Exception {
        DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(dhrjVilla1 != null ? dhrjVilla1.getZbdzid() : "不存在");
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy年MM月dd日");
        String zpTime = "进场：" + DateFormatConfig.df1(xyd.getXjctime()) + "<br />退场：" + DateFormatConfig.df1(xyd.getXtctime());
        //除场地费之外的其他费用
        Map<String, Object> map = new HashMap<>();
        //订单编号
        map.put("ddbh", xyd.getXddbh());
        //别墅名字
        map.put("bsName", xyd.getXbsmc2());
        //房东姓名
        map.put("fdName", xyd.getXfd());
        //房东电话
        map.put("fdTelphone", xyd.getXfddh());
        //租客姓名
        map.put("zkName", xyd.getXzk());
        //租客电话
        map.put("zkTelphone", xyd.getXzkdh());
        //租客身份证号码
        map.put("zksfz", xyd.getXzksfz());
        //公司名称（性质）
        if (xyd.getXdwmc() != null && !"".equals(xyd.getXdwmc())) {
            map.put("dwmc", xyd.getXdwmc() + (xyd.getXtdxz() != null ? "(" + xyd.getXtdxz() + ")" : ""));
        } else {
            map.put("dwmc", (xyd.getXtdxz() != null ? xyd.getXtdxz() : ""));
        }

        //客户来源
        map.put("khly", xyd.getXkhly());
        //租赁时间（进场时间与退场时间）
        map.put("zpTime", zpTime);
        //人数
        map.put("rs", (xyd.getXrs() != null ? xyd.getXrs() : 0) + "人");
        //桌数
        map.put("zs", (xyd.getXzs() != null ? xyd.getXzs() : 0) + "桌");
        //超出人数加收
        map.put("ccMoney", xyd.getXcudrfy());
        //支付宝订单号后六位
        String hlw = "";
        if (xyd.getXzzhlw() != null && !"".equals(xyd.getXzzhlw()) && xyd.getXzzhlw().length() > 0) {
            if (xyd.getXzzhlw().length() > 6) {
                hlw = xyd.getXzzhlw().substring(xyd.getXzzhlw().length() - 6);
            } else {
                hlw = xyd.getXzzhlw();
            }
        }
        //支付宝订单号后两位
        map.put("zfbddh", hlw);
        //支付宝转账定金时间
        map.put("zfbzzTime", xyd.getXzzsj() != null ? DateFormatConfig.df1(xyd.getXzzsj()) : "");
        //场地租赁费原价
        map.put("qkje", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0));
        //场地优惠后的价
        map.put("hfyj", (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0));
        //定金
        map.put("dj", xyd.getXysdj() != null ? xyd.getXysdj() : 0);
        //单点餐
        map.put("dcze", (xyd.getXrs() != null ? xyd.getXrs() : 0) * (xyd.getXcudrfy() != null ? xyd.getXcudrfy() : 0));
        //招待经理服务
        map.put("zdjlfw", xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0);
        //策划布景
        map.put("chbj", xyd.getXchbjje() != null ? xyd.getXchbjje() : 0);
        //轰趴师主持人
        map.put("hps", xyd.getXhpsje() != null ? xyd.getXhpsje() : 0);
        //定做团建横幅
        map.put("tjhf", xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0);
        //酒水商品套餐
        map.put("jssp", xyd.getXjsspje() != null ? xyd.getXjsspje() : 0);
        //延时费
        map.put("ysf", xyd.getXysf() != null ? xyd.getXysf() : 0);
        //总金额(场地费+（人数*人头单价）+招待经理服务+策划布景+轰趴师主持人+定做团建横幅+酒水商品套餐)---去除单点餐
//        double sum = (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) + (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0) + (xyd.getXchbjje() != null ? xyd.getXchbjje() : 0) + (xyd.getXhpsje() != null ? xyd.getXhpsje() : 0) + (xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0) + (xyd.getXjsspje() != null ? xyd.getXjsspje() : 0)+(xyd.getXysf() != null ? xyd.getXysf() : 0);
        double sum = (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) + (xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0) + (xyd.getXchbjje() != null ? xyd.getXchbjje() : 0) + (xyd.getXhpsje() != null ? xyd.getXhpsje() : 0) + (xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0) + (xyd.getXjsspje() != null ? xyd.getXjsspje() : 0) + (xyd.getXysf() != null ? xyd.getXysf() : 0) + (xyd.getXzs() != null ? xyd.getXzs() : 0) * (xyd.getXcudrfy() != null ? xyd.getXcudrfy() : 0);
        map.put("zje", sum);
        //进场前台支付金额
        map.put("qtzfje", xyd.getXkhjczfje() != null ? xyd.getXkhjczfje() : 0);
        //备注
        map.put("bzxx", xyd.getXsxbz() != null && !"".equals(xyd.getXsxbz()) ? xyd.getXsxbz() : "无");
        //代码
        String dm = "";
        if (xyd.getXdm() != null && !"".equals(xyd.getXdm()) && xyd.getXdm().length() > 0) {
            if (xyd.getXdm().length() > 6) {
                dm = xyd.getXdm().substring(0, 6);
            } else {
                dm = xyd.getXdm();
            }
        }
        map.put("dm", dm);
        //付款码
        String fkm = "";
        if (xyd.getXfkm() != null && !"".equals(xyd.getXfkm()) && xyd.getXfkm().length() > 0) {
            if (xyd.getXfkm().length() > 4) {
                fkm = xyd.getXfkm().substring(0, 4);
            } else {
                fkm = xyd.getXfkm();
            }
        }
        map.put("fkm", fkm);
        //值班店长
        map.put("zbdzxm", dingdingEmployee != null ? dingdingEmployee.getName() : "");
        //值班店长电话
        map.put("zbdzdh", dingdingEmployee != null ? dingdingEmployee.getMobile() : "");
        //是否现场成交
        map.put("sfxccj", xyd.getXisxzcj() != null && !"".equals(xyd.getXisxzcj()) ? xyd.getXisxzcj() : "否");
        //是否泳池单
        map.put("sfycd", xyd.getXsfycd() != null && !"".equals(xyd.getXsfycd()) ? xyd.getXsfycd() : "否");
        //甲方姓名
        map.put("jfxm", xyd.getXfd());
        //乙方姓名
        map.put("yfxm", xyd.getXzk());
        //日期
        map.put("rq", formatter1.format(xyd.getXsendtime()));

        //模板
        String tempName = FileConfig.getFileAbsolutePath2("static" + File.separator + "dhRjXyd.html");
        String context = PDFUtil.freeMarkerRender(map, tempName);
        String id = "DHRJXYD" + DateFormatConfig.df2(new Date()) + IdConfig.uuId();
        String pdf = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
        String png = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".png");
        File newPdf = new File(pdf);
        //生成pdf
        PDFUtil.createPdfDhRJXyd(context, newPdf.getPath());
        //生成图片
        PDFUtil.pdfToImg(newPdf.getPath(), 1, png);
        Map<String, Object> map1 = new HashMap<>();
        map1.put("png", id + ".png");
        //删除pdf临时文件
        newPdf.delete();
        return map1;
    }

    /**
     * 生成电子协议单
     *
     * @param xyd 协议单数据
     */
    private Map<String, Object> dhRjYhDzXyd(DhrjXyd xyd,DhrjVilla dhrjVilla1) throws Exception {
        DingdingEmployee dingdingEmployee = dhRjService.queryDhRjDingDingEmployeeByUserId(dhrjVilla1 != null ? dhrjVilla1.getZbdzid() : "不存在");
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy年MM月dd日");
        String zpTime = formatter1.format(xyd.getXjctime()) + ("1".equals(xyd.getCc()) ? " 午餐" : "2".equals(xyd.getCc()) ? "晚餐" : "3".equals(xyd.getCc()) ? "晚晚场" : "其他");
        //除场地费之外的其他费用
        Map<String, Object> map = new HashMap<>();
        //订单编号
        map.put("ddbh", xyd.getXddbh());
        //别墅名字
        map.put("bsName", xyd.getXbsmc2());
        //房东姓名
        map.put("fdName", xyd.getXfd());
        //房东电话
        map.put("fdTelphone", xyd.getXfddh());
        //租客姓名
        map.put("zkName", xyd.getXzk());
        //租客电话
        map.put("zkTelphone", xyd.getXzkdh());
        //租客身份证号码
        map.put("zksfz", xyd.getXzksfz());
        //公司名称（性质）
        if (xyd.getXdwmc() != null && !"".equals(xyd.getXdwmc())) {
            map.put("dwmc", xyd.getXdwmc() + (xyd.getXtdxz() != null ? "(" + xyd.getXtdxz() + ")" : ""));
        } else {
            map.put("dwmc", (xyd.getXtdxz() != null ? xyd.getXtdxz() : ""));
        }

        //客户来源
        map.put("khly", xyd.getXkhly());
        //租赁时间（进场时间与退场时间）
        map.put("zpTime", zpTime);
        //人数
        map.put("rs", (xyd.getXrs() != null ? xyd.getXrs() : 0) + "人");
        //桌数
        map.put("zs", (xyd.getXzs() != null ? xyd.getXzs() : 0) + "桌");
        //超出人数加收
        map.put("ccMoney", xyd.getXcudrfy());
        //支付宝订单号后六位
        String hlw = "";
        if (xyd.getXzzhlw() != null && !"".equals(xyd.getXzzhlw()) && xyd.getXzzhlw().length() > 0) {
            if (xyd.getXzzhlw().length() > 6) {
                hlw = xyd.getXzzhlw().substring(xyd.getXzzhlw().length() - 6);
            } else {
                hlw = xyd.getXzzhlw();
            }
        }
        //支付宝订单号后两位
        map.put("zfbddh", hlw);
        //支付宝转账定金时间
        map.put("zfbzzTime", xyd.getXzzsj() != null ? DateFormatConfig.df1(xyd.getXzzsj()) : "");
        //场地租赁费原价
        map.put("qkje", (xyd.getXqkzj() != null ? xyd.getXqkzj() : 0));
        //场地优惠后的价
        map.put("hfyj", (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0));
        //定金
        map.put("dj", xyd.getXysdj() != null ? xyd.getXysdj() : 0);
        //单点餐
        map.put("dcze", (xyd.getXrs() != null ? xyd.getXrs() : 0) * (xyd.getXcudrfy() != null ? xyd.getXcudrfy() : 0));
        //招待经理服务
        map.put("zdjlfw", xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0);
        //策划布景
        map.put("chbj", xyd.getXchbjje() != null ? xyd.getXchbjje() : 0);
        //轰趴师主持人
        map.put("hps", xyd.getXhpsje() != null ? xyd.getXhpsje() : 0);
        //定做团建横幅
        map.put("tjhf", xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0);
        //酒水商品套餐
        map.put("jssp", xyd.getXjsspje() != null ? xyd.getXjsspje() : 0);
        //延时费
        map.put("ysf", xyd.getXysf() != null ? xyd.getXysf() : 0);
        //总金额(场地费+（人数*人头单价）+招待经理服务+策划布景+轰趴师主持人+定做团建横幅+酒水商品套餐)---去除单点餐
//        double sum = (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) + (xyd.getXdcze() != null ? xyd.getXdcze() : 0) + (xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0) + (xyd.getXchbjje() != null ? xyd.getXchbjje() : 0) + (xyd.getXhpsje() != null ? xyd.getXhpsje() : 0) + (xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0) + (xyd.getXjsspje() != null ? xyd.getXjsspje() : 0)+(xyd.getXysf() != null ? xyd.getXysf() : 0);
        double sum = (xyd.getXhfyj() != null ? xyd.getXhfyj() : 0) + (xyd.getXzdjlfwje() != null ? xyd.getXzdjlfwje() : 0) + (xyd.getXchbjje() != null ? xyd.getXchbjje() : 0) + (xyd.getXhpsje() != null ? xyd.getXhpsje() : 0) + (xyd.getXtjhfje() != null ? xyd.getXtjhfje() : 0) + (xyd.getXjsspje() != null ? xyd.getXjsspje() : 0) + (xyd.getXysf() != null ? xyd.getXysf() : 0) + (xyd.getXzs() != null ? xyd.getXzs() : 0) * (xyd.getXcudrfy() != null ? xyd.getXcudrfy() : 0);
        map.put("zje", sum);
        //进场前台支付金额
        map.put("qtzfje", xyd.getXkhjczfje() != null ? xyd.getXkhjczfje() : 0);
        //备注
        map.put("bzxx", xyd.getXsxbz() != null && !"".equals(xyd.getXsxbz()) ? xyd.getXsxbz() : "无");
        //代码
        String dm = "";
        if (xyd.getXdm() != null && !"".equals(xyd.getXdm()) && xyd.getXdm().length() > 0) {
            if (xyd.getXdm().length() > 6) {
                dm = xyd.getXdm().substring(0, 6);
            } else {
                dm = xyd.getXdm();
            }
        }
        map.put("dm", dm);
        //付款码
        String fkm = "";
        if (xyd.getXfkm() != null && !"".equals(xyd.getXfkm()) && xyd.getXfkm().length() > 0) {
            if (xyd.getXfkm().length() > 4) {
                fkm = xyd.getXfkm().substring(0, 4);
            } else {
                fkm = xyd.getXfkm();
            }
        }
        map.put("fkm", fkm);
        //值班店长
        map.put("zbdzxm", dingdingEmployee != null ? dingdingEmployee.getName() : "");
        //值班店长电话
        map.put("zbdzdh", dingdingEmployee != null ? dingdingEmployee.getMobile() : "");
        //是否现场成交
        map.put("sfxccj", xyd.getXisxzcj() != null && !"".equals(xyd.getXisxzcj()) ? xyd.getXisxzcj() : "否");
        //是否泳池单
        map.put("sfycd", xyd.getXsfycd() != null && !"".equals(xyd.getXsfycd()) ? xyd.getXsfycd() : "否");
        //甲方姓名
        map.put("jfxm", xyd.getXfd());
        //乙方姓名
        map.put("yfxm", xyd.getXzk());
        //日期
        map.put("rq", formatter1.format(xyd.getXsendtime()));

        //模板
        String tempName = FileConfig.getFileAbsolutePath2("static" + File.separator + "dhRjYhXyd.html");
        String context = PDFUtil.freeMarkerRender(map, tempName);
        String id = "DHRJXYD" + DateFormatConfig.df2(new Date()) + IdConfig.uuId();
        String pdf = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
        String png = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".png");
        File newPdf = new File(pdf);
        //生成pdf
        PDFUtil.createPdfDhRJXyd(context, newPdf.getPath());
        //生成图片
        PDFUtil.pdfToImg(newPdf.getPath(), 1, png);
        Map<String, Object> map1 = new HashMap<>();
        map1.put("png", id + ".png");
        //删除pdf临时文件
        newPdf.delete();
        return map1;
    }

    /**
     * 新增crm信息发送工作通知
     */
    public void xzCrmGzTz(DingDingToken dingDingToken, String xzr, String khwxh, String fzr, String khdh, String userid, String fzrid, CrmQbkh crmQbkh) {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        userid = userid.substring(userid.indexOf("$") + 1);
        fzrid = fzrid.substring(fzrid.indexOf("$") + 1);
        if (userid.equals(fzrid)) {
            String text = xzr + "您新增了一条信息";
            text += "\n\n负责人：" + fzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
                text += "\n客户电话：" + (khdh != null ? khdh : "");

            } else {
                text += "\n客户电话：" + (khdh != null ? khdh : "");
                text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = xzr + "您新增了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text2 = xzr + "新增了一条信息给" + fzr;
            text2 += "\n\n负责人：" + fzr;
            text2 += "\n客户电话：" + (khdh != null ? khdh : "");
            text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 修改crm信息发送工作通知
     */
    public void xgCrmGzTz(DingDingToken dingDingToken, String xgr, String khwxh, String fzr, String khdh, String userid, String fzrid, CrmQbkh crmQbkh) {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        userid = userid.substring(userid.indexOf("$") + 1);
        fzrid = fzrid.substring(fzrid.indexOf("$") + 1);
        if (userid.equals(fzrid)) {
            String text = xgr + "您修改了一条信息";
            text += "\n\n负责人：" + fzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
                text += "\n客户电话：" + (khdh != null ? khdh : "");

            } else {
                text += "\n客户电话：" + (khdh != null ? khdh : "");
                text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = xgr + "您修改了一条信息";
            text += "\n\n负责人：" + fzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
                text += "\n客户电话：" + (khdh != null ? khdh : "");

            } else {
                text += "\n客户电话：" + (khdh != null ? khdh : "");
                text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text2 = xgr + "修改了一条" + fzr + "的信息";
            text2 += "\n\n负责人：" + fzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text2 += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
                text2 += "\n客户电话：" + (khdh != null ? khdh : "");

            } else {
                text2 += "\n客户电话：" + (khdh != null ? khdh : "");
                text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 删除crm信息发送工作通知
     */
    public void scCrmGzTz(DingDingToken dingDingToken, String scr, String khwxh, String fzr, String khdh, String userid, String fzrid) {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        userid = userid.substring(userid.indexOf("$") + 1);
        fzrid = fzrid.substring(fzrid.indexOf("$") + 1);
        if (userid.equals(fzrid)) {
            String text = scr + "您删除了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = scr + "您删除了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text2 = scr + "删除了一条" + fzr + "的信息";
            text2 += "\n\n负责人：" + fzr;
            text2 += "\n客户电话：" + (khdh != null ? khdh : "");
            text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 分配crm信息发送工作通知
     */
    public void fpCrmGzTz(DingDingToken dingDingToken, String fpr, String qfzr, String qfzrid, String fzr, String khdh, String userid, String fzrid, String khwxh) {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        userid = userid.substring(userid.indexOf("$") + 1);
        fzrid = fzrid.substring(fzrid.indexOf("$") + 1);
        qfzrid = qfzrid.substring(qfzrid.indexOf("$") + 1);
        if (userid.equals(fzrid) && qfzrid.equals(userid)) {
            String text = fpr + "您分配了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n前负责人：" + qfzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = fpr + "您分配了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n前负责人：" + qfzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text2 = fpr + "分配了一条信息给" + fzr;
            text2 += "\n\n负责人：" + fzr;
            text2 += "\n前负责人：" + qfzr;
            text2 += "\n客户电话：" + (khdh != null ? khdh : "");
            text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text3 = fpr + "分配了一条" + qfzr + "的信息给" + fzr;
            text3 += "\n\n负责人：" + fzr;
            text3 += "\n前负责人：" + qfzr;
            text3 += "\n客户电话：" + (khdh != null ? khdh : "");
            text3 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text3 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, qfzrid, text3);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 捞取crm信息发送工作通知
     */
    public void lqCrmGzTz(DingDingToken dingDingToken, String lqr, String khwxh, String fzr, String khdh, String userid, String fzrid) {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        userid = userid.substring(userid.indexOf("$") + 1);
        fzrid = fzrid.substring(fzrid.indexOf("$") + 1);
        if (userid.equals(fzrid)) {
            String text = lqr + "您捞取了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = lqr + "您捞取了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n客户电话：" + (khdh != null ? khdh : "");
            text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            String text2 = lqr + "捞取了一条信息给" + fzr;
            text2 += "\n\n负责人：" + fzr;
            text2 += "\n客户电话：" + (khdh != null ? khdh : "");
            text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 移交crm信息发送工作通知
     */
    public void yjCrmGzTz(DingDingToken dingDingToken, String yjr, String qfzr, String qfzrid, String khwxh, String fzr, String khdh, String userid, String fzrid, CrmQbkh crmQbkh) {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        userid = userid.substring(userid.indexOf("$") + 1);
        fzrid = fzrid.substring(fzrid.indexOf("$") + 1);
        qfzrid = qfzrid.substring(qfzrid.indexOf("$") + 1);
        if (userid.equals(fzrid) && qfzrid.equals(userid)) {
            String text = yjr + "您移交了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n前负责人：" + qfzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text += "\n客户昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
            } else {
                text += "\n客户电话：" + (khdh != null ? khdh : "");
                text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        } else {
            String text = yjr + "您移交了一条信息";
            text += "\n\n负责人：" + fzr;
            text += "\n前负责人：" + qfzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
            } else {
                text += "\n客户电话：" + (khdh != null ? khdh : "");
                text += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text2 = yjr + "移交了一条信息给" + fzr;
            text2 += "\n\n负责人：" + fzr;
            text2 += "\n前负责人：" + qfzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text2 += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
            } else {
                text2 += "\n客户电话：" + (khdh != null ? khdh : "");
                text2 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text2 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, fzrid, text2);
            } catch (ApiException e) {
                e.printStackTrace();
            }

            String text3 = yjr + "移交了一条" + qfzr + "的信息给" + fzr;
            text3 += "\n\n负责人：" + fzr;
            text3 += "\n前负责人：" + qfzr;
            if (crmQbkh.getExternalUserId() != null && !"".equals(crmQbkh.getExternalUserId())) {
                text3 += "\n客户微信昵称：" + (crmQbkh.getExternalName() != null ? crmQbkh.getExternalName() : "");
            } else {
                text3 += "\n客户电话：" + (khdh != null ? khdh : "");
                text3 += "\n客户微信：" + (khwxh != null ? khwxh : "");
            }
            text3 += "\n送达时间：" + df2.format(new Date());
            try {
                DingDBConfig.sendDhRjGzTzText(dingDingToken, qfzrid, text3);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 公海池crm信息发送工作通知
     */
    public void ghcCrmGzTz(DingDingToken dingDingToken, String thr, String khwxh, String fzr, String khdh, String userid) {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        userid = userid.substring(userid.indexOf("$") + 1);
        String text = thr + "您退回了一条信息至公海池";
        text += "\n\n负责人：" + fzr;
        text += "\n客户电话：" + (khdh != null ? khdh : "");
        text += "\n客户微信：" + (khwxh != null ? khwxh : "");
        text += "\n送达时间：" + df2.format(new Date());
        try {
            DingDBConfig.sendDhRjGzTzText(dingDingToken, userid, text);
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }

    public String gxDingDingToken() {
        String token = null;
        Dingkey dingkey = dhRjService.queryDingKeyById("dgrhcrm");
        if (dingkey != null) {
            try {
                token = DingToken.token(dingkey);
            } catch (ApiException e) {
                e.printStackTrace();
            }
            System.out.println(token);
            DingDingToken dingDingToken = dhRjService.queryDingDingTokenByName(dingkey.getName());
            if (dingDingToken != null) {
                DingDingToken dingDingToken1 = new DingDingToken();
                dingDingToken1.setToken(token);
                dingDingToken1.setId(dingDingToken.getId());
                Date date = new Date();
                date.setMinutes(date.getMinutes() + 20);
                dingDingToken1.setSxtime(date);
                dingDingTokenService.updateById(dingDingToken1);
            } else {
                DingDingToken dingDingToken1 = new DingDingToken();
                BeanUtils.copyProperties(dingkey, dingDingToken1);
                dingDingToken1.setToken(token);
                dingDingTokenService.save(dingDingToken1);
            }
        }

        return token;
    }

    @RequestMapping(value = "queryZxXg")
    public Result queryZxXg() {
        DhrjXyd dhrjXyd = new DhrjXyd();
        dhrjXyd.setXsfsc(0);
        dhrjXyd.setXddbh("DHRJ20231101185545889");
        List<DhrjXyd> list = dhrjXydService.queryList(dhrjXyd);
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setAppkey("APP_WGE8YPM32BUH1BPZD6N4");
        ydAppkey.setToken("E8866MB1GLNB3ME6AG8TX8LNXWV62CUEXQWIL22");
        DingDingToken dingDingToken = dhRjService.queryDingDingTokenByName("大户人家crm");
        list.forEach(l -> {
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("textField_lohdzrsc", l.getXfd());
            if (dingDingToken != null) {
                if (new Date().getTime() > dingDingToken.getSxtime().getTime()) {
                    String token = gxDingDingToken();
                    dingDingToken.setToken(token);
                }
                //发送添加成功通知
                GatewayResult gxbd = null;
                try {
                    gxbd = YdConfig.xgBdSl(dingDingToken.getToken(), ydAppkey, "yida_pub_account", l.getSlid(), jsonObject1.toJSONString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                System.out.println(gxbd);
            }
        });
        DhrjXyd l = list.get(0);


        return Result.OK();
    }
}
