package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "crm_qkh_yjjl")
public class CrmQbkhYjjl {

    /**id*/
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
    /**qid*/
    private java.lang.Integer qid;
    /**cjsj*/
    private java.lang.String cjsj;
    /**cjsj*/
    private java.lang.String yjuserid;
    /**移交人姓名*/
    private java.lang.String yjname;
    /**负责人姓名*/
    private java.lang.String fzrname;
    /**移交人姓名*/
    private java.lang.String fzruserid;
    /**是否删除：0正常，1删除*/
    private java.lang.String sfsc;
    /**负责人姓名*/
    private java.lang.String scsj;
    /**移交人姓名*/
    private java.lang.String scr;
    /**移交人姓名*/
    private java.lang.String scrid;
    /**crm编号*/
    private java.lang.String crmbh;
    /**yjbh*/
    private java.lang.String yjbh;
}
