package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdSpCsft;
import com.wlgb.mapper.WlgbJdSpCsftMapper;
import com.wlgb.service.WlgbJdSpCsftService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 21:36
 */
@Service
public class WlgbJdSpCsftServiceImpl implements WlgbJdSpCsftService {
    @Resource
    private WlgbJdSpCsftMapper wlgbJdSpCsftMapper;

    @Override
    public void save(WlgbJdSpCsft wlgbJdSpCsft) {
        wlgbJdSpCsft.setCreateTime(new Date());
        wlgbJdSpCsft.setId(IdConfig.uuId());
        wlgbJdSpCsftMapper.insertSelective(wlgbJdSpCsft);
    }

    @Override
    public void updateById(WlgbJdSpCsft wlgbJdSpCsft) {
        wlgbJdSpCsft.setUpdateTime(new Date());
        wlgbJdSpCsftMapper.updateByPrimaryKeySelective(wlgbJdSpCsft);
    }

    @Override
    public List<WlgbJdSpCsft> queryByStatueAndSpbh(Integer statu, String spbh) {
        Example example = new Example(WlgbJdSpCsft.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("statu", statu);
        criteria.andEqualTo("spbh", spbh);
        return wlgbJdSpCsftMapper.selectByExample(example);
    }
}
