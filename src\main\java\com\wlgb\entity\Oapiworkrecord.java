package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 待办主体详情
 * @Author: jeecg-boot
 * @Date:   2020-10-29
 * @Version: V1.0
 */
@Data
@Table(name = "oapiworkrecord")
public class Oapiworkrecord {

	/**读取数据id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**发送时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date time;
	/**钉钉用户id*/
    private java.lang.String dingUserId;
	/**钉钉姓名*/
    private java.lang.String dingUserName;
	/**标题*/
    private java.lang.String title;
	/**链接地址*/
    private java.lang.String url;
	/**钉钉待办id*/
    private java.lang.String dingRecord;
	/**来源名称*/
    private java.lang.String sourceName;
	/**读取时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ssd")
    private java.util.Date updateTime;
	/**是否已经读取*/
    private java.lang.Boolean statu;
	/**发送次数*/
    private java.lang.Integer sendCount;
	/**公司*/
    private java.lang.String department;
	/**发送成功状态*/
    private java.lang.Boolean sendStatu;
	/**发送通知*/
    private java.lang.String sendTz;
}
