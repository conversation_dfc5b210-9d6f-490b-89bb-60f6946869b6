package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: wlgb_ywjlhf
 * @Author: jeecg-boot
 * @Date:   2021-07-30
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_ywjlhf")
public class WlgbYwjlhf {
	/**id*/
	@Id
	@KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
	/**协议单id*/
    private java.lang.String xid;
	/**订单编号*/
    private java.lang.String ddbh;
	/**提交人*/
    private java.lang.String tjr;
	/**提交人id*/
    private java.lang.String tjrid;
	/**图片1*/
    private java.lang.String img1;
	/**图片2*/
    private java.lang.String img2;
	/**图片3*/
    private java.lang.String img3;
	/**图片4*/
    private java.lang.String img4;
	/**图片5*/
    private java.lang.String img5;
	/**图片6*/
    private java.lang.String img6;
	/**图片7*/
    private java.lang.String img7;
	/**图片8*/
    private java.lang.String img8;
	/**图片9*/
    private java.lang.String img9;
	/**图片10*/
    private java.lang.String img10;
	/**提交时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date time;
	/**是否异常*/
	private java.lang.String sfyc;
	/**异常类型*/
	private java.lang.String yclx;
	/**备注*/
	private java.lang.String bz;
	/**退场时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private java.util.Date tcsj;
	/**进场时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private java.util.Date jcsj;
	/**回访时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private java.util.Date hfsj;
}
