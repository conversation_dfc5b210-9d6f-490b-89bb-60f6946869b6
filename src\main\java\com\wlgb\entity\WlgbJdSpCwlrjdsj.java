package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 审批——财务录入金蝶数据
 * @Author: jeecg-boot
 * @Date:   2022-04-16
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_sp_cwlrjdsj")
public class WlgbJdSpCwlrjdsj {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**审批编号*/
    private java.lang.String spbh;
	/**审批标题*/
    private java.lang.String spbt;
	/**帐套*/
    private java.lang.String zt;
	/**帐套编码*/
    private java.lang.String ztbm;
	/**费用科目*/
    private java.lang.String fykm;
	/**费用科目编码*/
    private java.lang.String fykmbm;
	/**费用金额*/
    private java.lang.Double fyje;
	/**卡号*/
    private java.lang.String kh;
	/**付款时间*/
    private java.lang.String fksj;
	/**是否上传金蝶*/
    private java.lang.Integer sfscjd;
	/**审批类型：0:报销，1：预支，2：费用付款，3：抵预支，4：资金申请，5：借支审批，6：威廉币提现，7：宿舍退押金申请，8：对账员专用，9：平台刷单款报销，10：一线采购/报修*/
    private java.lang.Integer type;
    /**别墅名称*/
    private java.lang.String bsmc;
    /**别墅编号*/
    private java.lang.String bsbh;

}
