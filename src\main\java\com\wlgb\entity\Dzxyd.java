package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: dzxyd
 * @Author: jeecg-boot
 * @Date:   2020-10-30
 * @Version: V1.0
 */
@Data
@Table(name = "dzxyd")
public class Dzxyd {

	/**电子协议单id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String tip;
	/**电子协议单图片地址*/
    private java.lang.String url;
	/**协议单id*/
    private java.lang.String xid;
	/**时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date time;
}
