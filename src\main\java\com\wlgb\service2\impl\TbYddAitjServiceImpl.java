package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqThbD3c;
import com.wlgb.entity.TbYddAitj;
import com.wlgb.mapper.FwqThbD3cMapper;
import com.wlgb.mapper.TbYddAitjMapper;
import com.wlgb.mapper1.WeiLianMapper;
import com.wlgb.service2.FwqThbD3cService;
import com.wlgb.service2.TbYddAitjService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
@DS(value = "second")
public class TbYddAitjServiceImpl implements TbYddAitjService {
    @Resource
    private TbYddAitjMapper tbYddAitjMapper;
    @Resource
    private WeiLianMapper weiLianMapper;

    @Override
    public void save(TbYddAitj tbdAitj) {
        tbYddAitjMapper.insertSelective(tbdAitj);
    }

    @Override
    public TbYddAitj queryTbYddAitjByuuid(String uuid) {
        return weiLianMapper.queryTbYddAitjByuuid(uuid);
    }

    @Override
    public TbYddAitj queryTbYddAitjByuserid(String userid) {
        return weiLianMapper.queryTbYddAitjByuserid(userid);
    }

    @Override
    public TbYddAitj selectOne(TbYddAitj tbdAitj) {
        return tbYddAitjMapper.selectOne(tbdAitj);
    }

}
