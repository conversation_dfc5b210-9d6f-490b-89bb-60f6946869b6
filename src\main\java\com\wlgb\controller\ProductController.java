package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.WlgbProduct4;
import com.wlgb.service.*;
import com.wlgb.service2.TbXydService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/18 18:34
 */
@RestController
@RequestMapping(value = "/wlgb/product")
public class ProductController {
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private WlgbProduct4Service wlgbProduct4Service;
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private WlgbProductgmService wlgbProductgmService;
    @Autowired
    private WlgbSpslService wlgbSpslService;
    @Autowired
    private WlgbProductService wlgbProductService;


    /**
     * 根据类型查询商品信息
     */
    @RequestMapping(value = "queryProduct/{type}")
    public Result queryProduct(HttpServletRequest request, @PathVariable("type") String type1) {
        String type = request.getParameter("type");
        String pageNo = request.getParameter("pageNo");
        String pageSize = request.getParameter("pageSize");
        String serch = request.getParameter("serch");
        String bsid = request.getParameter("bsid");
        TbVilla villa = weiLianDdXcxService.queryTbVillaById(bsid);
        String city = villa != null ? villa.getCszq() != null ? !"".equals(villa.getCszq()) ? villa.getCszq() : villa.getCity() : villa.getCity() : null;
        PageHelp pageHelp = new PageHelp(pageNo != null ? Integer.parseInt(pageNo) : 1, pageSize != null ? Integer.parseInt(pageSize) : 10);
        pageHelp = PageConfig.pageHelp(pageHelp);
        PageHelpUtil pageHelpUtil = PageConfig.pages(pageHelp);
        Map<String, Object> map = new HashMap<>();
        map.put("type", type1);
        map.put("city", city);
        map.put("help", pageHelpUtil);
        if (serch != null && !"".equals(serch)) {
            map.put("serch", serch);
        }
        PageHelpUtil pageHelpUtil1 = weiLianDdXcxService.queryProductByTypeList(map);
        pageHelpUtil1 = PageConfig.pageHelpUtil(pageHelpUtil1, pageHelp);
        return Result.OK(pageHelpUtil1);
    }


    /**
     * 根据订单编号查询商品购买记录
     */
    @RequestMapping(value = "queryGmSpJl")
    public Result queryGmSpJl(HttpServletRequest request) {
        String ddbh = request.getParameter("ddbh");
        List<WlgbProduct4> product4List = wlgbProduct4Service.queryByDdbhList(ddbh);

        return Result.OK(product4List);
    }


    /**
     * 提交商品出库购买信息
     */
    @RequestMapping(value = "tjSpCk")
    public Result tjSpCk(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String ddbh = jsonObject.getString("ddbh");
        String jcjxfid = jsonObject.getString("jcjxfid");
        String userid = jsonObject.getString("userid");
        JSONArray list = jsonObject.getJSONArray("sj");
        List<WlgbProduct4> list1 = wlgbProduct4Service.queryByDdbhList(ddbh);
        list1.forEach(l -> {
            WlgbProduct4 wlgbProduct4 = new WlgbProduct4();
            wlgbProduct4.setId(l.getId());
            wlgbProduct4.setSfsc(1);
            wlgbProduct4Service.updateById(wlgbProduct4);
        });
        List<WlgbProductgm> list2 = wlgbProductgmService.queryByDdbhList(ddbh);
        list2.forEach(l -> {
            WlgbProductgm wlgbProductgm = new WlgbProductgm();
            wlgbProductgm.setId(l.getId());
            wlgbProductgm.setSfsc(1);
            wlgbProductgmService.updateById(wlgbProductgm);
        });
        TbXyd tbXyd = tbXydService.queryByDdBh(jsonObject.getString("ddbh"));
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        List<WlgbSpsl> spslList = wlgbSpslService.queryByDdbh(tbXyd.getXddbh());
        spslList.forEach(l -> {
            scBdSl(l.getSlid());
            wlgbSpslService.removeById(l.getId());
        });
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        String finalToken = token;
        list.forEach(l1 -> {
            JSONObject l = (JSONObject) l1;
            String spmc = l.getString("spmc");
            String txm = l.getString("txm");
            Double lsj = l.getDouble("price");
            Integer sl = l.getInteger("sl");
            Double spzje = l.getDouble("je");
            String type = l.getString("type");
            String spbh = l.getString("spbh");
            WlgbProduct4 wlgbProduct4 = new WlgbProduct4();
            wlgbProduct4.setId(IdConfig.uuId());
            wlgbProduct4.setDdbh(ddbh);
            wlgbProduct4.setTime(new Date());
            wlgbProduct4.setTxm(txm);
            wlgbProduct4.setNum(sl);
            wlgbProduct4.setSfsc(0);
            wlgbProduct4.setName(spmc);
            wlgbProduct4.setPrice(lsj);
            wlgbProduct4.setJe(spzje);
            wlgbProduct4.setType(type);
            wlgbProduct4.setSpbh(spbh);
            wlgbProduct4Service.save(wlgbProduct4);
            WlgbProduct wlgbProduct = wlgbProductService.queryBySpbhAndTxm(spbh, txm);
            JSONObject json = new JSONObject();
            json.put("spddbh", tbXyd.getXddbh());
//            json.put("sptp", ydlc.setTpList(wlgbProduct.getImg(), wlgbProduct.getName()));
            json.put("spfl", wlgbProduct.getType());
            json.put("spmc", wlgbProduct.getName());
            json.put("sptxm", wlgbProduct.getTxm());
            json.put("smqy", wlgbProduct.getBm());
            json.put("splsj", wlgbProduct.getPrice());
            json.put("spgmsl", sl != null ? sl : 0);
            json.put("spje", spzje != null ? spzje : 0);
            GatewayResult gatewayResult = null;
            try {
                gatewayResult = DingBdLcConfig.xzBdSl(finalToken, ydAppkey, json.toJSONString(), userid, "FORM-IE7664A1DWSSP2NP4Q8D3AHH41YR3W547DFSK7");
            } catch (Exception e) {
                e.printStackTrace();
            }
            WlgbSpsl wlgbSpsl = new WlgbSpsl();
            wlgbSpsl.setId(IdConfig.uuId());
            wlgbSpsl.setTime(new Date());
            wlgbSpsl.setDdbh(tbXyd.getXddbh());
            wlgbSpsl.setBz("商品购买");
            if (gatewayResult != null) {
                wlgbSpsl.setSlid(gatewayResult.getResult());
            }
            wlgbSpslService.save(wlgbSpsl);
        });
        //添加购买商品的详细信息
        WlgbProductgm gm = new WlgbProductgm();
        gm.setId(IdConfig.uuId());
        gm.setDdbh(ddbh);
        gm.setZj(jsonObject.getDouble("zje"));
        gm.setSfsc(0);
        gm.setTime(new Date());
        wlgbProductgmService.save(gm);
        Map<String, Object> map1 = new HashMap<>();
        map1.put("qspxf", jsonObject.getDouble("zje"));
        map1.put("qspxf2", jsonObject.getDouble("zje"));
        GatewayResult gatewayResult = xgLcSl(map1, jcjxfid, "012412221639786136545", token, ydAppkey);
        System.out.println(gatewayResult);
        return Result.OK();
    }


    /**
     * 删除表单
     *
     * @param slid 实例id
     */
    public GatewayResult scBdSl(String slid) {
        YdAppkey ydAppkey = weiLianDdXcxService.queryYdAppKeyByBz("宜搭");
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        String token = null;
        try {
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        GatewayResult gatewayResult = null;
        try {
            gatewayResult = DingBdLcConfig.scBdSl(token, ydAppkey, "012412221639786136545", slid);
        } catch (Exception e) {
            gatewayResult = new GatewayResult();
            e.printStackTrace();
        }

        return gatewayResult;
    }

    /**
     * 修改流程实例
     *
     * @param id     要更新的流程数据ID
     * @param map    表单内容
     * @param userid 用户id
     */
    public GatewayResult xgLcSl(Map<String, Object> map, String id, String userid, String token, YdAppkey ydAppkey) {

        return LcConfig.gxLcSl(userid, map, ydAppkey, id, token);
    }
}
