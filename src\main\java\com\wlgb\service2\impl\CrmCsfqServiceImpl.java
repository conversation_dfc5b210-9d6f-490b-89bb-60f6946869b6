package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmCsfq;
import com.wlgb.mapper.CrmCsfqMapper;
import com.wlgb.service2.CrmCsfqService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 17:52
 */
@Service
@DS(value = "second")
public class CrmCsfqServiceImpl implements CrmCsfqService {
    @Resource
    private CrmCsfqMapper crmCsfqMapper;

    @Override
    public void save(CrmCsfq crmCsfq) {
        crmCsfqMapper.insertSelective(crmCsfq);
    }

    @Override
    public void updateById(CrmCsfq crmCsfq) {
        crmCsfqMapper.updateByPrimaryKeySelective(crmCsfq);
    }

    @Override
    public CrmCsfq queryCrmCsFqByCrmCsFq(CrmCsfq crmCsfq) {
        return crmCsfqMapper.selectOne(crmCsfq);
    }
}
