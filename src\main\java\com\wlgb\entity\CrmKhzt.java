package com.wlgb.entity;


import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: crm_khzt
 * @Author: jeecg-boot
 * @Date:   2020-12-02
 * @Version: V1.0
 */
@Data
@Table(name = "crm_khzt")
public class CrmKhzt {
	/**客户状态id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer zid;
	/**客户状态*/
    private java.lang.String zkhzt;
	/**类型id*/
    private java.lang.Integer lxid;
	/**是否删除：0正常，1删除*/
    private java.lang.String sfsc;
}
