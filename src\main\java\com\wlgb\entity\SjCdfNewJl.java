package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "sj_cdf_new_jl")
public class SjCdfNewJl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    private Date creatime;
    private String czrid;
    private String czrname;
    private String xzkssj;
    private String xzjssj;
    private String url;
    private String bsxz;

}
