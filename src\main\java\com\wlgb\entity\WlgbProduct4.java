package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: wlgb_product4
 * @Author: jeecg-boot
 * @Date:   2021-06-01
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_product4")
public class WlgbProduct4 {

	/**主键*/
	@Id
	@KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**商品名称*/
    private java.lang.String name;
	/**数量*/
    private java.lang.Integer num;
	/**时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date time;
	/**条形码*/
    private java.lang.String txm;
	/**是否删除*/
    private java.lang.Integer sfsc;
	/**订单编号*/
    private java.lang.String ddbh;
	/**订单编号*/
    private java.lang.String spbh;
    /**数量*/
    private java.lang.Double je;
    /**数量*/
    private java.lang.Double price;
    /**数量*/
    private java.lang.String type;
}
