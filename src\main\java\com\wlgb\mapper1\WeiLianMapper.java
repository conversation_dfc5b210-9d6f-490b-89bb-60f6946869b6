package com.wlgb.mapper1;

import com.alibaba.fastjson.JSONObject;
import com.wlgb.config.*;
import com.wlgb.entity.vo.FwqBbb;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface WeiLianMapper {
    List<TbVilla> queryTodayWcMd(Map<String, Object> map);

    Integer queryCountXydSfZd(TbXyd tbXyd);

    Integer queryCountXydSfZd2(TbXyd tbXyd);

    Integer queryCountWlgbYlxdSfZd(WlgbYlxd wlgbYlxd);

    Integer queryCountTbYddXydSfZd(TbYddXyd tbYddXyd);

    Integer queryPdCc(Map<String, Object> map);

    Double queryKdjZe(Map<String, Object> map);

    VillaIDsUtil queryIdsByVid(@Param("vid") String vid);

    TbKhlyxz queryKhLyXzByLidOrLname(@Param("khxz") String khxz);

    FwqBbb2 queryBbb2ByXm(@Param("xm") String xm);

    FwqBbb queryBbbByXfd(@Param("xfd") String xfd);

    void zxZjCc(@Param("xid") String xid);

    String queryKdjZy();

    Map<String, Object> queryKdjXx(Map<String, Object> map);

    Integer queryXydMothCountByXfd(@Param("xfd") String xfd);

    Integer queryXydDateCountByXfd(@Param("xfd") String xfd);

    Integer queryXydYesCountByXfd(@Param("xfd") String xfd);

    Double queryXdYjByXfd(@Param("name") String xfd);

    void zxczgc();

    FwqBbb3 queryBmYj(FwqBbb2 fwqBbb2);

    Double queryKdjJzj(Map<String, Object> map);

    List<String> queryHpsAt(@Param("cs") String cs);

    Integer queryXfdXdZfTs(@Param("xfd") String xfd);

    Integer queryQrdSjZfTs(@Param("xfd") String xfd);

    Integer queryBsSfZn(@Param("vid") String vid);

    Integer queryFdZn(@Param("name") String name);

    TbQrd queryQrdByXidAndSfSc(@Param("xid") String xid);

    FwqQunid queryQun(@Param("city") String city);

    DingdingEmployee queryDingDingByName(@Param("name") String name);

    DingdingEmployee queryDingDingById(@Param("id") String id);

    Integer queryBsUserIdSfJms(@Param("userid") String userId);

    List<Select> queryXdBsXlJms(@Param("userid") String userId);

    List<Select> queryXdBsXl();

    List<Select> queryXdLyOrXzXl(@Param("type") Integer type);

    List<Select> queryXdHdCh();

    List<WlgbYlxd> queryYldList(Map<String, Object> map);

    Integer queryYldCount(Map<String, Object> map);

    List<Select> queryVillaCity();

    List<TbXyd> queryScXydList(Map<String, Object> map);

    Integer queryScXydCount(Map<String, Object> map);

    List<TbQrdVo> queryScQrdList(Map<String, Object> map);

    Integer queryScQrdCount(Map<String, Object> map);

    QyUtil queryQyUtil(Map<String, Object> map);

    List<DkBbJqr> queryDkBb(Map<String, Object> map);

    TbDk queryDkXq(@Param("dkddbh") String dkddbh);

    ZwAndDz queryZwAndDz(@Param("vid") String vid);

    List<BsZw> queryBsMcIdZw(Map<String, Object> map);

    TbVilla queryVillaByVname(@Param("vname") String vname);

    List<TbVilla> queryKyQnMdList(Map<String, Object> map);

    Integer queryKyQnMdCount(Map<String, Object> map);


    List<CrmCs> queryCrmCsList();

    List<CrmKhzt> queryCrmKhZtList(@Param("lxid") Integer lxid);

    Integer queryCrmDhSfcz(Map<String, Object> map);

    Integer queryCrmWxhCount(@Param("qkhwxh") String qkhwxh);

    Integer queryCrmHmCount(@Param("dhhm") String dhhm);

    List<CrmCsfq> queryCrmCsSfqByCsName(@Param("csname") String csName);

    String queryCrmBbh();

    List<CrmQbkh> queryCrmDataImport(Map<String, Object> map);

    Integer queryCrmDataImportCount(Map<String, Object> map);

    Integer queryCmrCsFqIdByFqName(@Param("fqname") String fqName);

    List<CrmBmxq> queryCrmBmCxByBmDa(@Param("bmda") String bmDa);

    CrmBmxq queryCrmBmCxByUserid(@Param("userid") String userid);

    List<CrmQbkh> queryCrmCcSjByCcData(@Param("cc") String cc);

    List<CrmCsfq> queryCrmCsFqList();

    List<Map<String, Object>> querySxDzZbDz();

    List<Map<String, Object>> queryYxbXdWzf();

    List<DingdingEmployee> queryDingdingEmployeeList();

    List<HrMsz> queryHrYcSj();

    List<CrmUser> queryCrmUserList();

    void qkCrmBmXqTable();

    void qkCrmBmTable();

    void qkCrmUserBackupsTable();

    void bfCrmUserInBackups();

    void qkCrmUserCopyTable();

    void saveCrmUserCopy(CrmUser crmUser);

    void delCrmUserCopy();

    void qkCrmUserTable();

    void saveCrmUser();

    void saveDingdingEmployee(Demployee de);

    void updateDingdingEmployee(Demployee de);

    @org.apache.ibatis.annotations.Select("delete from dingding_employee  WHERE userid=#{userid} ")
    void delDingdingEmployee(@Param("userid") String userid);

    Tbperson queryTbpersonById(String id);

    void saveTbperson(Tbperson tp);

    void updateTbperson(Tbperson tp);

    SysUser querySysUserById(String id);

    void saveSysUser(SysUser su);

    void updateSysUser(SysUser su);

    Integer queryFsDzbdSl();

    Integer querySyDdSl();

    Integer queryHrJrDmByYyName(@Param("name") String name);

    Integer queryHrJrTgByYyName(@Param("name") String name);

    List<Select> queryKdjVilla(Map<String, Object> map);

    TbKdj queryTbKdjByBsAndJcAndTc(Map<String, Object> map);

    List<TbKdj> queryYyKdj(Map<String, Object> map);

    Integer querySfMs(@Param("vid") String vid);

    Map<String, Object> zxKdjPlXg(Map<String, Object> map);

    List<Select> queryCsFqSelect();

    List<Select> queryCsJqSelect();

    List<Select> queryBbCsSelect();

    List<Select> queryFgsSelect();

    List<TbVillaVo> queryBsWhList(Map<String, Object> map);

    Integer queryBsWhListCount(Map<String, Object> map);

    List<TbVillaVo> queryBsListXz(Map<String, Object> map);

    List<TbCsfqVo> queryFqWhList(Map<String, Object> map);

    Integer queryFqWhListCount(Map<String, Object> map);

    List<TbCsjqVo> queryJqWhList(Map<String, Object> map);

    Integer queryJqWhListCount(Map<String, Object> map);

    List<JSONObject> queryXhsAndDyXdYj(Map<String, Object> map);

    List<JSONObject> queryXhsAndDyXFYj(Map<String, Object> map);

    List<Select> queryGwLb();

    List<Select> queryGw(@Param("id") String id);

    List<Select> queryQd();

    List<Select> queryWx();

    List<Select> queryJlJd();

    List<Map<String, Object>> queryXyGw(Map<String, Object> map);

    List<Map<String, Object>> queryZtGw(Map<String, Object> map);

    Integer queryXyGwCount(Map<String, Object> map);

    Integer queryZtGwCount(Map<String, Object> map);

    Integer queryJrDm(@Param("name") String name);

    Integer queryJrTg(@Param("name") String name);

    List<Map<String, Object>> queryBmZpQk(Map<String, Object> map);

    List<Map<String, Object>> queryBmFqSg(Map<String, Object> map);

    Map<String, Object> queryBmZpQkByFzr(Map<String, Object> map);

    Map<String, Object> queryBmFqSgByFzr(Map<String, Object> map);

    List<JSONObject> queryXydXxByVidThreeDay(Map<String, Object> map);

    @org.apache.ibatis.annotations.Select("select * from fwq_thb_d3c where lsh = #{lsh}")
    FwqThbD3c queryFwqThbD3cByLsh(@Param("lsh") String lsh);

    @org.apache.ibatis.annotations.Select("UPDATE fwq_thb_d3c SET kyj = #{kyj},jyj = #{jyj},yjy = #{yjy} WHERE lsh=#{lsh} ")
    void updateFwqThbD3cByLsh(Map<String, Object> map);

    @org.apache.ibatis.annotations.Select("delete from fwq_thb_d3c  WHERE lsh=#{lsh} ")
    void deleteFwqThbD3cByLsh(Map<String, Object> map);

    @org.apache.ibatis.annotations.Select("select * from tb_ydd_aitj where uuid = #{uuid}")
    TbYddAitj queryTbYddAitjByuuid(@Param("uuid") String uuid);

    @org.apache.ibatis.annotations.Select("select * from tb_ydd_aitj where userid = #{userid}")
    TbYddAitj queryTbYddAitjByuserid(@Param("userid") String userid);

    @org.apache.ibatis.annotations.Select("select * from tb_ydd_aitj_xx where uuid = #{uuid}")
    TbYddAitjXx queryTbYddAitjXxByuuid(@Param("uuid") String uuid);

    @org.apache.ibatis.annotations.Select("select * from tb_ydd_aitj_xx where userid = #{userid}")
    TbYddAitjXx queryTbYddAitjXxByuserid(@Param("userid") String userid);

    JSONObject queryCyQyOneByQyName(@Param("qy") String qy);

    List<Hpsl> queryHpSlCgMonth();

    DingdingDepartment queryDingdingDeparTment(@Param("bmid") String bmid);

    void saveDingdingDeparTment(Department de);

    void updateDingdingDeparTment(Department de);

    @org.apache.ibatis.annotations.Select("delete from dingding_department  WHERE dingdingid=#{dingdingid} ")
    void delDingdingDeparTment(@Param("dingdingid") String dingdingid);

    Integer queryXdSd(@Param("xddh") String xddh, @Param("xdxm") String xdxm);

    @org.apache.ibatis.annotations.Select("CALL scthbd1c(#{V_QRSJ_S1_14},#{V_QRSJ_S2_14})")
    void queryCCGCscthbd1c(@Param("V_QRSJ_S1_14") String V_QRSJ_S1_14, @Param("V_QRSJ_S2_14") String V_QRSJ_S2_14);

    @org.apache.ibatis.annotations.Select("CALL scthbd2c(#{V_QRSJ_S1_14},#{V_QRSJ_S2_14})")
    void queryCCGCscthbd2c(@Param("V_QRSJ_S1_14") String V_QRSJ_S1_14, @Param("V_QRSJ_S2_14") String V_QRSJ_S2_14);

    List<FwqWnl> queryWnl(@Param("nian") String nian, @Param("yue") String yue);

    void deleteWnlAll();

    List<FwqThbD9c> queryDyDataMonth();

    void saveFwqXdjcDzmrlc(FwqXdjcDzmrlc fc);

    List<FwqXdjcDzmrlc> queryFwqXdjcDzmrlc();

    void saveFwqXdjcDzmrlcFsjl(FwqXdjcDzmrlcFsjl fc);

    List<FwqXdjcDzmrlcFsjl> queryFwqXdjcDzmrlcFsjl(Map<String, Object> map);


}
