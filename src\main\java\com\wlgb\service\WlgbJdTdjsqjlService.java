package com.wlgb.service;

import com.wlgb.entity.WlgbJdTdjsqjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/30 14:14
 */
public interface WlgbJdTdjsqjlService {
    void save(WlgbJdTdjsqjl wlgbJdTdjsqjl);

    void updateById(WlgbJdTdjsqjl wlgbJdTdjsqjl);

    WlgbJdTdjsqjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd);

    List<WlgbJdTdjsqjl> queryListByWlgbJdTdjsqjl(WlgbJdTdjsqjl wlgbJdTdjsqjl);
}
