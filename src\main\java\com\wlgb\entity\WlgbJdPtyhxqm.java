package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 平台已核销券码
 * @Author: jeecg-boot
 * @Date:   2022-04-03
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_ptyhxqm")
public class WlgbJdPtyhxqm {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**券码*/
    private java.lang.String qm;
	/**录入时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date lrsj;
	/**是否删除（0：否，1：是）*/
    private java.lang.Integer sfsc;
	/**订单编号*/
    private java.lang.String ddbh;
	/**来源*/
    private java.lang.String ly;
}
