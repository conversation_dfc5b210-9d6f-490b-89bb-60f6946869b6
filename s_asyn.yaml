# ------------------------------------
#   If you need English case, you can refer to [s_en.yaml] file
# ------------------------------------
#   欢迎您使用阿里云函数计算 FC 组件进行项目开发
#   组件仓库地址：https://github.com/devsapp/fc
#   组件帮助文档：https://www.serverless-devs.com/fc/readme
#   Yaml参考文档：https://www.serverless-devs.com/fc/yaml/readme
#   关于：
#      - Serverless Devs和FC组件的关系、如何声明/部署多个函数、超过50M的代码包如何部署
#      - 关于.fcignore使用方法、工具中.s目录是做什么、函数进行build操作之后如何处理build的产物
#   等问题，可以参考文档：https://www.serverless-devs.com/fc/tips
#   关于如何做CICD等问题，可以参考：https://www.serverless-devs.com/serverless-devs/cicd
#   关于如何进行环境划分等问题，可以参考：https://www.serverless-devs.com/serverless-devs/extend
#   更多函数计算案例，可参考：https://github.com/devsapp/awesome/
#   有问题快来钉钉群问一下吧：33947367
# ------------------------------------
edition: 1.0.0
name: web-framework-app
# access 是当前应用所需要的密钥信息配置：
# 密钥配置可以参考：https://www.serverless-devs.com/serverless-devs/command/config
# 密钥使用顺序可以参考：https://www.serverless-devs.com/serverless-devs/tool#密钥使用顺序与规范
access: 'default'

vars: # 全局变量
  region: 'cn-hangzhou'
  service:
    name: 'yinsheng'
    description: '银盛支付的定金问题'

services:
  framework: # 业务名称/模块名称
    # 如果只想针对 framework 下面的业务进行相关操作，可以在命令行中加上 framework，例如：
    # 只对framework进行构建：s framework build
    # 如果不带有 framework ，而是直接执行 s build，工具则会对当前Yaml下，所有和 framework 平级的业务模块（如有其他平级的模块，例如下面注释的next-function），按照一定顺序进行 build 操作
    component: fc # 组件名称，Serverless Devs 工具本身类似于一种游戏机，不具备具体的业务能力，组件类似于游戏卡，用户通过向游戏机中插入不同的游戏卡实现不同的功能，即通过使用不同的组件实现不同的具体业务能力
    actions: # 自定义执行逻辑，关于actions 的使用，可以参考：https://www.serverless-devs.com/serverless-devs/yaml#行为描述
      pre-deploy: # 在deploy之前运行
        - run: mvn package -DskipTests # 要执行的系统命令，类似于一种钩子的形式
          #- run: mvn clean install -Dmaven.test.skip=true  #这是一个不包含所有依赖的打包方式，需要配合pom.xml的配置一起使用，否则无效
          path: ./ # 执行系统命令/钩子的路径
    #        - component: fc build --use-docker               # 要运行的组件，格式为【component: 组件名 命令 参数】（可以通过s cli registry search --type Component 获取组件列表）
    #        - plugin: myplugin                               # 与运行的插件 （可以通过s cli registry search --type Plugin 获取组件列表）
    #          args:                                          # 插件的参数信息
    #            testKey: testValue
    #      post-deploy: # 在deploy之后运行
    #        - component: fc versions publish # 要运行的命令行
    props: # 组件的属性值
      region: 'cn-hangzhou' # 关于变量的使用方法，可以参考：https://www.serverless-devs.com/serverless-devs/yaml#变量赋值
      service:
        name: 'yinsheng'
        description: '银盛支付的定金问题'
        internetAccess: true
        # 一下是端云联调的配置
      #        vpcconfig:
      #          vpcId: vpc-bp1emrwwftvmldavcdzxq
      #          securityGroupId: sg-bp1h2swzeb5vgjfu****
      #          vswitchIds:
      #            - vsw-bp1kablus0jrcdeth****
      #        nasconfig:
      #          userId:10008
      #          groupId:10008
      #          mountPoints:
      #            - serverAddr: example.com
      #            nasDir: /
      #            fcDir: /mnt/auto
      function:
        name: 'yinsheng2'
        description: '异步函数'
        codeUri: ./target/ysdjtb.jar
        runtime: custom
        memorySize: 1024
        timeout: 30
        customRuntimeConfig:
          command:
            - java
          args:
            - 'org.springframework.boot.loader.JarLauncher'
      triggers:
        - name: httpTrigger
          type: http
          config:
            authType: anonymous
            methods:
              - GET
              - POST
              - PUT
              - DELETE
              - HEAD
              - OPTIONS
      customDomains:
        - domainName: auto
          protocol: HTTP
          routeConfigs:
            - path: /*
#第一次部署请用 s deploy
#第二次部署请用 s deploy --type code

#java org.springframework.boot.loader.JarLauncher


