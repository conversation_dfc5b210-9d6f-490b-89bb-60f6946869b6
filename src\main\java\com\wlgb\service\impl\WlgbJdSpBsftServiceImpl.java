package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdSpBsft;
import com.wlgb.mapper.WlgbJdSpBsftMapper;
import com.wlgb.service.WlgbJdSpBsftService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 21:25
 */
@Service
public class WlgbJdSpBsftServiceImpl implements WlgbJdSpBsftService {
    @Resource
    private WlgbJdSpBsftMapper wlgbJdSpBsftMapper;

    @Override
    public void save(WlgbJdSpBsft wlgbJdSpBsft) {
        wlgbJdSpBsft.setCreateTime(new Date());
        wlgbJdSpBsft.setId(IdConfig.uuId());
        wlgbJdSpBsftMapper.insertSelective(wlgbJdSpBsft);
    }

    @Override
    public void updateById(WlgbJdSpBsft wlgbJdSpBsft) {
        wlgbJdSpBsft.setUpdateTime(new Date());
        wlgbJdSpBsftMapper.updateByPrimaryKeySelective(wlgbJdSpBsft);
    }

    @Override
    public List<WlgbJdSpBsft> queryByStatueAndTypeAndSpBh(Integer statu, Integer type, String spbh) {
        Example example = new Example(WlgbJdSpBsft.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("statu", statu);
        criteria.andEqualTo("type", type);
        criteria.andEqualTo("spbh", spbh);
        return wlgbJdSpBsftMapper.selectByExample(example);
    }
}
