package com.wlgb.entity;

import java.io.Serializable;
import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: hr_gw_city
 * @Author: jeecg-boot
 * @Date:   2021-09-17
 * @Version: V1.0
 */
@Data
@Table(name = "hr_gw_city")
public class HrGwCity {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
	/**父级_岗位id*/
    private java.lang.String fgwid;
	/**父级_岗位名称*/
    private java.lang.String fgw;
	/**子集_城市id*/
    private java.lang.String zcityid;
	/**子集_城市名称*/
    private java.lang.String zcity;
	/**是否删除*/
    private java.lang.String sfsc;
	/**岗位排序*/
    private java.lang.Integer gwpx;
}
