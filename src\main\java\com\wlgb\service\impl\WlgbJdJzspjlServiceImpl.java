package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdJzspjl;
import com.wlgb.mapper.WlgbJdJzspjlMapper;
import com.wlgb.service.WlgbJdJzspjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 23:26
 */
@Service
public class WlgbJdJzspjlServiceImpl implements WlgbJdJzspjlService {
    @Resource
    private WlgbJdJzspjlMapper wlgbJdJzspjlMapper;

    @Override
    public void save(WlgbJdJzspjl wlgbJdJzspjl) {
        wlgbJdJzspjl.setCreateTime(new Date());
        wlgbJdJzspjl.setId(IdConfig.uuId());
        wlgbJdJzspjlMapper.insertSelective(wlgbJdJzspjl);
    }

    @Override
    public void updateById(WlgbJdJzspjl wlgbJdJzspjl) {
        wlgbJdJzspjl.setUpdateTime(new Date());
        wlgbJdJzspjlMapper.updateByPrimaryKeySelective(wlgbJdJzspjl);
    }

    @Override
    public WlgbJdJzspjl queryBySpBhAndSfLrJd(String spbh, Integer sfLrJd) {
        Example example = new Example(WlgbJdJzspjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spbh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdJzspjlMapper.selectOneByExample(example);
    }
}
