package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 预支申请记录
 * @Author: jeecg-boot
 * @Date:   2022-04-15
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_yzsqjl")
public class WlgbJdYzsqjl {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**审批编号*/
    private java.lang.String spbh;
	/**审批标题*/
    private java.lang.String spbt;
	/**申请时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date sqsj;
	/**审批结束时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date spjssj;
	/**申请人*/
    private java.lang.String sqr;
	/**申请人部门*/
    private java.lang.String sqrbm;
    /**申请人部门id*/
    private java.lang.String sqrbmid;
	/**申请人id*/
    private java.lang.String sqrid;
	/**预支类型*/
    private java.lang.String yzlx;
	/**预支款使用方*/
    private java.lang.String yzksyf;
	/**支付费用类别*/
    private java.lang.String zffylb;
	/**预支款用途（原因）*/
    private java.lang.String yzkyt;
	/**预计抵预支时间（不得超过一个月）*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date yjdyzsj;
	/**本人累积所欠预支款*/
    private java.lang.Double brljqk;
	/**本次预支金额（元）*/
    private java.lang.Double bcyzje;
	/**收款账号*/
    private java.lang.String skzh;
	/**开户行*/
    private java.lang.String khh;
	/**帐套*/
    private java.lang.String zt;
	/**帐套编码*/
    private java.lang.String ztbm;
	/**费用科目*/
    private java.lang.String fykm;
	/**费用科目编码*/
    private java.lang.String fykmbm;
	/**是否录入金蝶*/
    private java.lang.Integer sflrjd;
	/**出纳转账时间*/
    private java.util.Date cnzzsj;
	/**出纳实际转账金额*/
    private java.lang.Double cnsjzzje;
    /**审批实例id*/
    private java.lang.String slid;
}
