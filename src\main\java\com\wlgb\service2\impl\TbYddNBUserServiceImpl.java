package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbYddNBUser;
import com.wlgb.mapper.TbYddNBUserMapper;
import com.wlgb.service2.TbYddNBUserService;
import com.wlgb.service2.TbYddNBUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
@Service
@DS(value = "second")
public class TbYddNBUserServiceImpl implements TbYddNBUserService {
    @Resource
    private TbYddNBUserMapper tbYddNBUserMapper;

    @Override
    public void save(TbYddNBUser TbYddNBUser) {
        tbYddNBUserMapper.insertSelective(TbYddNBUser);
    }

    @Override
    public void updateById(TbYddNBUser TbYddNBUser) {
        tbYddNBUserMapper.updateByPrimaryKeySelective(TbYddNBUser);
    }

    @Override
    public void deleteByTbYddNBUser(TbYddNBUser TbYddNBUser) {
        tbYddNBUserMapper.updateByPrimaryKeySelective(TbYddNBUser);
    }

    @Override
    public TbYddNBUser queryTbYddNBUserBySjh(String userid) {
        TbYddNBUser tu = new TbYddNBUser();
        tu.setUserid(userid);
        return tbYddNBUserMapper.selectOne(tu);
    }
}
