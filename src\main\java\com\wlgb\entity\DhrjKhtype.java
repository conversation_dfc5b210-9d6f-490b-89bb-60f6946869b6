package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 16:27
 */
@Data
@Table(name = "dhrj_khtype")
public class DhrjKhtype {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**类型名称*/
    private String lxname;
    /**类型编号*/
    private String lxbh;
    /**备注*/
    private String bz;
    /**是否删除(0:否,1:是)*/
    private Integer sfsc;
}
