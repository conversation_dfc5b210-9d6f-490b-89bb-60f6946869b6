package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 部门信息 钉钉同步
 *
 * <AUTHOR>
 */
@Data
@Table(name = "dingding_department")
public class Department {
    @Id
    @KeySql(useGeneratedKeys = true)
    private String id;
    private String dingdingid;
    private String departname;
    private String parentid;
    private String corpid;

    private String zgid;
}
