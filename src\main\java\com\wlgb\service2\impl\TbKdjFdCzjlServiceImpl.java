package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbKdjFdCzjl;
import com.wlgb.mapper.TbKdjFdCzjlMapper;
import com.wlgb.service2.TbKdjFdCzjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27 15:59
 */
@Service
@DS(value = "second")
public class TbKdjFdCzjlServiceImpl implements TbKdjFdCzjlService {
    @Resource
    private TbKdjFdCzjlMapper tbKdjFdCzjlMapper;

    @Override
    public void save(TbKdjFdCzjl tbKdjFdCzjl) {
        tbKdjFdCzjlMapper.insertSelective(tbKdjFdCzjl);
    }
}
