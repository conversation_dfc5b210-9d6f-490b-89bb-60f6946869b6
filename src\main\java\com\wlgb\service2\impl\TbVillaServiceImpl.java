package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbVilla;
import com.wlgb.mapper.TbVillaMapper;
import com.wlgb.service2.TbVillaService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/7 16:29
 */
@Service
@DS(value = "second")
public class TbVillaServiceImpl implements TbVillaService {
    @Resource
    private TbVillaMapper tbVillaMapper;

    @Override
    public void save(TbVilla tbVilla) {
        tbVillaMapper.insertSelective(tbVilla);
    }

    @Override
    public void updateById(TbVilla tbVilla) {
        tbVillaMapper.updateByPrimaryKeySelective(tbVilla);
    }

    @Override
    public TbVilla getById(String vid) {
        return tbVillaMapper.selectByPrimaryKey(vid);
    }

    @Override
    public List<TbVilla> queryList(TbVilla tbVilla) {
        return tbVillaMapper.select(tbVilla);
    }
}
