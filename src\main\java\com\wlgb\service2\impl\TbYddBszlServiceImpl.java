package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbYddBszl;
import com.wlgb.mapper.TbYddBszlMapper;
import com.wlgb.service2.TbYddBszlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@DS(value = "second")
public class TbYddBszlServiceImpl implements TbYddBszlService {
    @Resource
    private TbYddBszlMapper tbYddBszlMapper;

    @Override
    public void save(TbYddBszl tbYddBszl) {
        tbYddBszlMapper.insertSelective(tbYddBszl);
    }

    @Override
    public void updateById(TbYddBszl tbYddBszl) {
        tbYddBszlMapper.updateByPrimaryKeySelective(tbYddBszl);
    }

    @Override
    public void deleteByTbYddBszl(TbYddBszl tbYddBszl) {
        tbYddBszlMapper.updateByPrimaryKeySelective(tbYddBszl);
    }

    @Override
    public TbYddBszl queryTbYddBszlByVname(String vname) {
        TbYddBszl tu = new TbYddBszl();
        tu.setVname(vname);
        return tbYddBszlMapper.selectOne(tu);
    }
}
