package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdLcxmdyzsqjl;
import com.wlgb.entity.WlgbJdLcxmyzsqjl;
import com.wlgb.mapper.WlgbJdLcxmdyzsqjlMapper;
import com.wlgb.service.WlgbJdLcxmdyzsqjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/20 11:05
 */
@Service
public class WlgbJdLcxmdyzsqjlServiceImpl implements WlgbJdLcxmdyzsqjlService {
    @Resource
    private WlgbJdLcxmdyzsqjlMapper wlgbJdLcxmdyzsqjlMapper;

    @Override
    public void save(WlgbJdLcxmdyzsqjl wlgbJdLcxmdyzsqjl) {
        wlgbJdLcxmdyzsqjl.setCreateTime(new Date());
        wlgbJdLcxmdyzsqjl.setId(IdConfig.uuId());
        wlgbJdLcxmdyzsqjlMapper.insertSelective(wlgbJdLcxmdyzsqjl);
    }

    @Override
    public void updateById(WlgbJdLcxmdyzsqjl wlgbJdLcxmdyzsqjl) {
        wlgbJdLcxmdyzsqjl.setUpdateTime(new Date());
        wlgbJdLcxmdyzsqjlMapper.updateByPrimaryKeySelective(wlgbJdLcxmdyzsqjl);
    }

    @Override
    public WlgbJdLcxmdyzsqjl queryBySpBhAndSfLrJd(String spbh, Integer sfLrJd) {
        Example example = new Example(WlgbJdLcxmdyzsqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spbh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdLcxmdyzsqjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdLcxmdyzsqjl> queryListWlgbJdLcxmdyzsqjl(WlgbJdLcxmdyzsqjl wlgbJdLcxmdyzsqjl) {
        return wlgbJdLcxmdyzsqjlMapper.select(wlgbJdLcxmdyzsqjl);
    }
}
