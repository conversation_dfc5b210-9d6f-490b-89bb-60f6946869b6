package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdLcxmyzsqjl;
import com.wlgb.mapper.WlgbJdLcxmyzsqjlMapper;
import com.wlgb.service.WlgbJdLcxmyzsqjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/20 10:35
 */
@Service
public class WlgbJdLcxmyzsqjlServiceImpl implements WlgbJdLcxmyzsqjlService {
    @Resource
    private WlgbJdLcxmyzsqjlMapper wlgbJdLcxmyzsqjlMapper;

    @Override
    public void save(WlgbJdLcxmyzsqjl wlgbJdLcxmyzsqjl) {
        wlgbJdLcxmyzsqjl.setCreateTime(new Date());
        wlgbJdLcxmyzsqjl.setId(IdConfig.uuId());
        wlgbJdLcxmyzsqjlMapper.insertSelective(wlgbJdLcxmyzsqjl);
    }

    @Override
    public void updateById(WlgbJdLcxmyzsqjl wlgbJdLcxmyzsqjl) {
        wlgbJdLcxmyzsqjl.setUpdateTime(new Date());
        wlgbJdLcxmyzsqjlMapper.updateByPrimaryKeySelective(wlgbJdLcxmyzsqjl);
    }

    @Override
    public WlgbJdLcxmyzsqjl queryBySpBhAndSfLrJd(String spbh, Integer sfLrJd) {
        Example example = new Example(WlgbJdLcxmyzsqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spbh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdLcxmyzsqjlMapper.selectOneByExample(example);
    }
}
