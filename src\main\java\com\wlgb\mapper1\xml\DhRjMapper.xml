<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlgb.mapper1.DhRjMapper">
    <!-- 查询是否撞单 -->
    <select id="queryCountDhRjXydSfCz" resultType="java.lang.Integer" parameterType="com.wlgb.entity.DhrjXyd">
        SELECT count(1) FROM dhrj_xyd
        WHERE
        xsfsc = 0
        and XBSMC = #{xbsmc}
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
        and
        NOT (
            (date_format(XTCTIME,'%Y-%m-%d %H:%i') &lt; date_format(#{xjctime},'%Y-%m-%d %H:%i')
            OR
            ( date_format(XJCTIME,'%Y-%m-%d %H:%i') &gt; date_format(#{xtctime},'%Y-%m-%d %H:%i'))
            )
        )
    </select>

    <!-- 根据userid查询钉钉用户表 -->
    <select id="queryDhRjDingDingEmployeeByUserId" parameterType="java.lang.String" resultType="com.wlgb.config.DingdingEmployee">
        select * from dingding_employee where userid = #{userid} limit 1
    </select>
    <!-- 根据姓名查询钉钉用户表 -->
    <select id="queryDhRjDingDingEmployeeByName" parameterType="java.lang.String" resultType="com.wlgb.config.DingdingEmployee">
        select * from dingding_employee where name = #{name} limit 1
    </select>

    <!--查询房东当前月的下单数量-->
    <select id="queryDhRjXydMothCountByXfd" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            dhrj_xyd
        <where>
            and
                xfd=#{xfd}
            and
                date_format(xsendtime,'%Y-%m') = date_format(now(),'%Y-%m')
            and xsfsc = 0
        </where>
    </select>

    <!--查询房东当日的下单数量-->
    <select id="queryDhRjXydDateCountByXfd" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
        dhrj_xyd
        <where>
            and
                xfd=#{xfd}
            and
                date_format(xsendtime,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
            and xsfsc = 0
        </where>
    </select>
    <!--查询房东昨天的下单数量-->
    <select id="queryDhRjXydYesCountByXfd" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            dhrj_xyd
        <where>
            and
                xfd=#{xfd}
            and
                DATEDIFF(now(),xsendtime) = 1
            and xsfsc = 0
        </where>
    </select>
    <!-- 根据房东查询当前月业绩 -->
    <select id="queryDhRjXdYjByXfd" resultType="java.lang.Double" parameterType="java.lang.String">
        select
            truncate(sum(IFNULL(xhfyj,0) + (IFNULL(xrs,0) * IFNULL(xcudrfy,0)) + IFNULL(xzdjlfwje,0) + IFNULL(xchbjje,0) + IFNULL(xhpsje,0) + IFNULL(xtjhfje,0) + IFNULL(xjsspje,0) + IFNULL(xysf,0)), 2) as cdf
        from
            dhrj_xyd
        where
            xfd = #{xfd}
        and
            xsfsc = 0
        and
            DATE_FORMAT(xsendtime,'%Y%m') =  DATE_FORMAT(now(),'%Y%m')
    </select>

    <!--crm城市下拉-->
    <select id="queryCrmCsList" resultType="com.wlgb.entity.CrmCs">
        select
            csid, csname
        from
            crm_cs
        where
            cszt = 0
        order by
            cspx
        asc
    </select>

    <!--crm客户信息状态-->
    <select id="queryCrmKhZtList" resultType="com.wlgb.entity.CrmKhzt" parameterType="java.lang.Integer">
        select
            *
        from
            crm_khzt
        where
            sfsc = 0
        and
            lxid = #{lxid}
        ORDER BY zid asc
    </select>

    <!-- 判断电话号码去重 -->
    <select id="queryCrmDhSfcz" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT
        count(qid)
        FROM
        crm_qbkh
        where
        qxxzt != 3
        and
        (qkhdh = #{dhhm} or qkhsj = #{dhhm} or qkhhm = #{dhhm})
        <if test="qid != null and qid != '' and qid != '0'">
            and qid != #{qid}
        </if>
    </select>

    <!-- crm微信号查重 -->
    <select id="queryCrmWxhCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(1) from crm_qbkh where qkhwxh = #{qkhwxh} and YEAR(qcjsj) > 2019 and qxxzt in ('1','0', '2')
    </select>

    <!-- crm电话号码查询 -->
    <select id="queryCrmHmCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(1) from crm_qbkh where (qkhdh = #{dhhm} or qkhsj = #{dhhm} or qkhhm = #{dhhm}) and YEAR(qcjsj) > 2019 and qxxzt in ('1','0', '2')
    </select>

    <!-- 根据城市名称获取区域 -->
    <select id="queryCrmCsSfqByCsName" resultType="com.wlgb.entity.CrmCsfq" parameterType="java.lang.String">
        SELECT * from crm_csfq where csname = #{csname}
    </select>

    <!-- 插入crm版本号 -->
    <select id="queryCrmBbh" resultType="java.lang.String">
        select bbh from crm_bbh where bbh=(select max(bbh) from crm_bbh) LIMIT 1;
    </select>

    <!-- 查询Crm全部数据导出使用 -->
    <select id="queryCrmDataImport" resultType="com.wlgb.entity.CrmQbkh" parameterType="java.util.Map">
        select
        *
        from
        crm_qbkh q
        <if test="qcjsj1 != null and qcjsj1 != ''">
            left join crm_qkh_yjjl y ON q.crmbh = y.crmbh
        </if>
        where
        YEAR(qcjsj) > 2019
        <if test="qcjr != null and qcjr != ''">
            and q.qcjr like concat('%', #{qcjr}, '%')
        </if>
        <if test="qkhdh != null and qkhdh != ''">
            and q.qkhdh like concat('%', #{qkhdh}, '%')
        </if>
        <if test="qkhwxm != null and qkhwxm != ''">
            and q.qkhwxm like concat('%', #{qkhwxm}, '%')
        </if>
        <if test="qkhwxh != null and qkhwxh != ''">
            and q.qkhwxh like concat('%', #{qkhwxh}, '%')
        </if>
        <if test="qdhbm != null and qdhbm != ''">
            and q.qdhbm like concat('%', #{qdhbm}, '%')
        </if>


        <if test="qxgsj1 != null and qxgsj1 != ''">
            and q.qxgsj between #{qxgsj1} and #{qxgsj2}
        </if>
        <if test="qcjsj1 != null and qcjsj1 != ''">
            and (q.qcjsj between #{qcjsj1} and #{qcjsj2} OR y.cjsj between #{qcjsj1} and #{qcjsj2})
        </if>
        <if test="qkhydsj1 != null and qkhydsj1 != ''">
            and q.qkhydsj between #{qkhydsj1} and #{qkhydsj2}
        </if>
        <if test="qxxzt != null and qxxzt != ''">
            and q.qxxzt = #{qxxzt}
        </if>
        <if test="qcs != null and qcs != ''">
            and q.qcs = #{qcs}
        </if>
        <if test="qy != null and qy != ''">
            and q.fqname in
            <foreach collection="qy" item="qy" index="index" open="(" close=")" separator=",">
                #{qy}
            </foreach>
        </if>
        <if test="qqdly != null and qqdly != ''">
            and q.qqdly in
            <foreach collection="qqdly" item="qqdly" index="index" open="(" close=")" separator=",">
                #{qqdly}
            </foreach>
        </if>

        <if test="qfzr != null and qfzr != ''">
            and q.qfzr in
            <foreach collection="qfzr" item="qfzr" index="index" open="(" close=")" separator=",">
                #{qfzr}
            </foreach>
        </if>
        <if test="help != null and help != ''">
            limit #{help.pageNum}, #{help.pageSize}
        </if>
    </select>

    <select id="queryCrmDataImportCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select
        count(1)
        from
        crm_qbkh q
        <if test="qcjsj1 != null and qcjsj1 != ''">
            left join crm_qkh_yjjl y ON q.crmbh = y.crmbh
        </if>
        where
        YEAR(qcjsj) > 2019
        <if test="qcjr != null and qcjr != ''">
            and q.qcjr like concat('%', #{qcjr}, '%')
        </if>
        <if test="qkhdh != null and qkhdh != ''">
            and q.qkhdh like concat('%', #{qkhdh}, '%')
        </if>
        <if test="qkhwxm != null and qkhwxm != ''">
            and q.qkhwxm like concat('%', #{qkhwxm}, '%')
        </if>
        <if test="qkhwxh != null and qkhwxh != ''">
            and q.qkhwxh like concat('%', #{qkhwxh}, '%')
        </if>
        <if test="qdhbm != null and qdhbm != ''">
            and q.qdhbm like concat('%', #{qdhbm}, '%')
        </if>

        <if test="qxgsj1 != null and qxgsj1 != ''">
            and q.qxgsj between #{qxgsj1} and #{qxgsj2}
        </if>
        <if test="qcjsj1 != null and qcjsj1 != ''">
            and (q.qcjsj between #{qcjsj1} and #{qcjsj2} OR y.cjsj between #{qcjsj1} and #{qcjsj2})
        </if>
        <if test="qkhydsj1 != null and qkhydsj1 != ''">
            and q.qkhydsj between #{qkhydsj1} and #{qkhydsj2}
        </if>


        <if test="qcs != null and qcs != ''">
            and q.qcs = #{qcs}
        </if>
        <if test="qy != null and qy != ''">
            and q.fqname in
            <foreach collection="qy" item="qy" index="index" open="(" close=")" separator=",">
                #{qy}
            </foreach>
        </if>
        <if test="qqdly != null and qqdly != ''">
            and q.qqdly in
            <foreach collection="qqdly" item="qqdly" index="index" open="(" close=")" separator=",">
                #{qqdly}
            </foreach>
        </if>

        <if test="qfzr != null and qfzr != ''">
            and q.qfzr in
            <foreach collection="qfzr" item="qfzr" index="index" open="(" close=")" separator=",">
                #{qfzr}
            </foreach>
        </if>
        <if test="qxxzt != null and qxxzt != ''">
            and q.qxxzt = #{qxxzt}
        </if>
    </select>

    <!-- 根据区域名称查询区域id -->
    <select id="queryCmrCsFqIdByFqName" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT csfqid FROM crm_csfq WHERE fqname = #{fqname}
    </select>

    <!-- 根据部门id查询下面部门列表 -->
    <select id="queryCrmBmCxByBmDa" parameterType="java.lang.String" resultType="com.wlgb.entity.CrmBmxq">
        select * from crm_bmxq where bmid = #{bmda} or bmid2 = #{bmda} or bmid3 = #{bmda} or bmid4 = #{bmda} or bmid5 = #{bmda} or bmid6 = #{bmda} or bmid7 = #{bmda} or bmid8 = #{bmda} or bmid9 = #{bmda} or bmid10 = #{bmda} or bmid11 = #{bmda} or bmid12 = #{bmda} or bmid13 = #{bmda} or bmid14 = #{bmda} or bmid15 = #{bmda}
    </select>

    <!-- 根据查重条件查询查重记录 -->
    <select id="queryCrmCcSjByCcData" resultType="com.wlgb.entity.CrmQbkh" parameterType="java.lang.String">
        select * from crm_qbkh where (qkhwxh like CONCAT(CONCAT('%', #{cc}), '%')  or qkhdh like CONCAT(CONCAT('%', #{cc}), '%')  or qkhsj like CONCAT(CONCAT('%', #{cc}), '%')  or qkhhm =CONCAT(CONCAT('%', #{cc}), '%'))  and qxxzt in ('1','0', '2') and  YEAR(qcjsj) > 2019
    </select>

    <!-- 查询全部crm分区 -->
    <select id="queryCrmCsFqList" resultType="com.wlgb.entity.CrmCsfq">
        SELECT * FROM crm_csfq
    </select>

    <!-- 根据id查询钉钉应用配置 -->
    <select id="queryDingKeyById" resultType="com.wlgb.config.Dingkey" parameterType="java.lang.String">
        select id,appkey,appsecret,agent_id as agentId,name from dingkey where id = #{id} limit 1
    </select>

    <!-- 获取钉钉token对象 -->
    <select id="queryDingDingTokenByName" resultType="com.wlgb.entity.DingDingToken" parameterType="java.lang.String">
        select id,appkey,appsecret,agent_id as agentId,name,token,sxtime from dingding_token where name = #{name}
    </select>

    <!-- 查询全部crm人员信息表 -->
    <select id="queryCrmUserList" resultType="com.wlgb.entity.CrmUser">
        select * from crm_user
    </select>
    <!-- 清空crm部门详情表 -->
    <select id="qkCrmBmXqTable">
        truncate table crm_bmxq
    </select>
    <!-- 清空crm部门表 -->
    <select id="qkCrmBmTable">
        truncate table crm_bm
    </select>
    <!-- 清空crm用户备份表 -->
    <select id="qkCrmUserBackupsTable">
        truncate table crm_user_backups
    </select>
    <!-- 将crm_user表数据赋值给备份表 方便比对筛选-->
    <select id="bfCrmUserInBackups">
        insert  into crm_user_backups (userid,name,mobile,avatar ) select userid,name,mobile,avatar from crm_user
    </select>
    <!-- 清空crm用户copy表 -->
    <select id="qkCrmUserCopyTable">
        truncate table crm_user_copy
    </select>
    <!-- 保存crm_user_copy表 -->
    <insert id="saveCrmUserCopy" parameterType="com.wlgb.entity.CrmUser">
        insert into crm_user_copy(
        <if test="userid !=null">
            userid
        </if>
        <if test="name !=null">
            ,name
        </if>
        <if test="avatar !=null">
            ,avatar
        </if>
        <if test="mobile !=null">
            ,mobile
        </if>
        )values (
        <if test="userid !=null">
            #{userid,jdbcType =VARCHAR}
        </if>
        <if test="name !=null">
            ,#{name,jdbcType =VARCHAR}
        </if>
        <if test="avatar !=null">
            ,#{avatar,jdbcType =VARCHAR}
        </if>
        <if test="mobile !=null">
            ,#{mobile,jdbcType =VARCHAR}
        </if>
        )
    </insert>
    <!-- 删除crm用户copy重复数据 -->
    <delete id="delCrmUserCopy">
        DELETE FROM crm_user_copy WHERE id NOT IN ( SELECT id FROM ( SELECT min( id ) AS id FROM crm_user_copy GROUP BY userid ) AS b )
    </delete>
    <!-- 清空crm用户表 -->
    <select id="qkCrmUserTable">
        truncate table crm_user
    </select>

    <!--5、将crm_user_copy表的数据赋值给crm_user-->
    <insert id="saveCrmUser">
        insert  into crm_user (userid,name,mobile,avatar ) select userid,name,mobile,avatar from crm_user_copy;
    </insert>
</mapper>