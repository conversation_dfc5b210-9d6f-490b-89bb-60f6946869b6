package com.wlgb.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wlgb.entity.WlgbHxyhYjWkDzjl;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/18 9:11
 */
@Data
public class YjDzVo {
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date dzsj;
    private String jetype;
    private String ysbh;
    private String skbh;
}
