package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/26 21:25
 */
@Data
@Table(name = "wlgb_ydd_yjwkjl")
public class WlgbYddYjWkJl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
    /**协议单id*/
    private String xid;
    /**协议单订单编号*/
    private String xddbh;
    /**是否需要押金*/
    private Integer sfyj;
    /**押金金额*/
    private Double yjje;
    /**押金二维码*/
    private String yjewm;
    /**押金生成时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date yjsctime;
    /**押金失效时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date yjsxtime;
    /**押金到账时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date yjdztime;
    /**押金收款编号*/
    private String yjskbh;
    /**是否需要尾款*/
    private Integer sfwk;
    /**尾款金额*/
    private Double wkje;
    /**尾款二维码*/
    private String wkewm;
    /**尾款生成时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date wksctime;
    /**尾款失效时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date wksxtime;
    /**尾款到账时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date wkdztime;
    /**尾款收款编号*/
    private String wkskbh;
    /**押金是否到账*/
    private Integer yjsfdz;
    /**尾款是否到账*/
    private Integer wksfdz;
    /**商户号*/
    private String shh;
    /**是否删除*/
    private Integer sfsc;
    /**进场及消费实例id*/
    private String slid;
    /**发起人id*/
    private String fqrid;
    /**发起人*/
    private String fqr;
    /**押金是否退款*/
    private Integer yjsftk;
}
