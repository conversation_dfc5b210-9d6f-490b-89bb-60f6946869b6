package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbXydLog;
import com.wlgb.mapper.WlgbXydLogMppaer;
import com.wlgb.service4.DhRjXydLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/20 17:18
 */
@Service
@DS("fourth")
public class DhRjXydLogServiceImpl implements DhRjXydLogService {
    @Resource
    private WlgbXydLogMppaer wlgbXydLogMppaer;

    @Override
    public void save(WlgbXydLog wlgbXydLog) {
        wlgbXydLog.setXltime(new Date());
        wlgbXydLog.setXlid(IdConfig.uuId());
        wlgbXydLogMppaer.insertSelective(wlgbXydLog);
    }
}
