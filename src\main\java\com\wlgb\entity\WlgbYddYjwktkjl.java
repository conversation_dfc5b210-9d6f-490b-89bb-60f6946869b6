package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/25 23:59
 */
@Data
@Table(name = "wlgb_ydd_yjwktkjl")
public class WlgbYddYjwktkjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
    private String xddbh;
    private String type;
    private String skbh;
    private String ysbh;
    private Double tkje;
    private Double tkcgje;
    private Integer tksfcg;
}
