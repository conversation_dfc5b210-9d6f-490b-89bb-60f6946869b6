package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.DhrjKhtype;
import com.wlgb.mapper.DhrjKhtypeMapper;
import com.wlgb.service4.DhrjKhtypeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 16:29
 */
@Service
@DS("fourth")
public class DhrjKhtypeServiceImpl implements DhrjKhtypeService {
    @Resource
    private DhrjKhtypeMapper dhrjKhtypeMapper;

    @Override
    public void save(DhrjKhtype dhrjKhtype) {
        dhrjKhtypeMapper.insertSelective(dhrjKhtype);
    }

    @Override
    public void updateById(DhrjKhtype dhrjKhtype) {
        dhrjKhtypeMapper.updateByPrimaryKeySelective(dhrjKhtype);
    }

    @Override
    public DhrjKhtype queryDhrjKhtypeByDhrjKhtype(DhrjKhtype dhrjKhtype) {
        return dhrjKhtypeMapper.selectOne(dhrjKhtype);
    }
}
