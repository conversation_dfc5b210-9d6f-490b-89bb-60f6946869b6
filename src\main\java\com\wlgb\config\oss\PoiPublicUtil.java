//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.wlgb.config.oss;

import org.apache.poi.POIXMLDocumentPart;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.PictureData;
import org.apache.poi.xssf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.spreadsheetDrawing.CTMarker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ClassUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URISyntaxException;
import java.util.*;

public final class PoiPublicUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(PoiPublicUtil.class);

    private PoiPublicUtil() {
    }

    public static <K, V> Map<K, V> mapFor(Object... mapping) {
        Map<Object, Object> map = new HashMap();

        for(int i = 0; i < mapping.length; i += 2) {
            map.put(mapping[i], mapping[i + 1]);
        }

        return (Map<K, V>) map;
    }

    public static Field[] getClassFields(Class<?> clazz) {
        ArrayList list = new ArrayList();

        Field[] fields;
        do {
            fields = clazz.getDeclaredFields();

            for(int i = 0; i < fields.length; ++i) {
                list.add(fields[i]);
            }

            clazz = clazz.getSuperclass();
        } while(clazz != Object.class && clazz != null);

        return (Field[])list.toArray(fields);
    }

    public static String getFileExtendName(byte[] photoByte) {
        String strFileExtendName = "JPG";
        if (photoByte[0] == 71 && photoByte[1] == 73 && photoByte[2] == 70 && photoByte[3] == 56 && (photoByte[4] == 55 || photoByte[4] == 57) && photoByte[5] == 97) {
            strFileExtendName = "GIF";
        } else if (photoByte[6] == 74 && photoByte[7] == 70 && photoByte[8] == 73 && photoByte[9] == 70) {
            strFileExtendName = "JPG";
        } else if (photoByte[0] == 66 && photoByte[1] == 77) {
            strFileExtendName = "BMP";
        } else if (photoByte[1] == 80 && photoByte[2] == 78 && photoByte[3] == 71) {
            strFileExtendName = "PNG";
        }

        return strFileExtendName;
    }

    public static Method getMethod(String name, Class<?> pojoClass) throws Exception {
        StringBuffer getMethodName = new StringBuffer("get");
        getMethodName.append(name.substring(0, 1).toUpperCase());
        getMethodName.append(name.substring(1));
        Method method = null;

        try {
            method = pojoClass.getMethod(getMethodName.toString());
        } catch (Exception var5) {
            method = pojoClass.getMethod(getMethodName.toString().replace("get", "is"));
        }

        return method;
    }

    public static Method getMethod(String name, Class<?> pojoClass, Class<?> type) throws Exception {
        StringBuffer getMethodName = new StringBuffer("set");
        getMethodName.append(name.substring(0, 1).toUpperCase());
        getMethodName.append(name.substring(1));
        return pojoClass.getMethod(getMethodName.toString(), type);
    }

    public static Method getMethod(String name, Class<?> pojoClass, boolean convert) throws Exception {
        StringBuffer getMethodName = new StringBuffer();
        if (convert) {
            getMethodName.append("convert");
        }

        getMethodName.append("get");
        getMethodName.append(name.substring(0, 1).toUpperCase());
        getMethodName.append(name.substring(1));
        Method method = null;

        try {
            method = pojoClass.getMethod(getMethodName.toString());
        } catch (Exception var6) {
            method = pojoClass.getMethod(getMethodName.toString().replace("get", "is"));
        }

        return method;
    }

    public static Method getMethod(String name, Class<?> pojoClass, Class<?> type, boolean convert) throws Exception {
        StringBuffer setMethodName = new StringBuffer();
        if (convert) {
            setMethodName.append("convert");
        }

        setMethodName.append("set");
        setMethodName.append(name.substring(0, 1).toUpperCase());
        setMethodName.append(name.substring(1));
        return pojoClass.getMethod(setMethodName.toString(), type);
    }

    public static Map<String, PictureData> getSheetPictrues03(HSSFSheet sheet, HSSFWorkbook workbook) {
        Map<String, PictureData> sheetIndexPicMap = new HashMap();
        List<HSSFPictureData> pictures = workbook.getAllPictures();
        if (!pictures.isEmpty()) {
            Iterator var4 = sheet.getDrawingPatriarch().getChildren().iterator();

            while(var4.hasNext()) {
                HSSFShape shape = (HSSFShape)var4.next();
                HSSFClientAnchor anchor = (HSSFClientAnchor)shape.getAnchor();
                if (shape instanceof HSSFPicture) {
                    HSSFPicture pic = (HSSFPicture)shape;
                    int pictureIndex = pic.getPictureIndex() - 1;
                    HSSFPictureData picData = (HSSFPictureData)pictures.get(pictureIndex);
                    String picIndex = anchor.getRow1() + "_" + anchor.getCol1();
                    sheetIndexPicMap.put(picIndex, picData);
                }
            }

            return sheetIndexPicMap;
        } else {
            return null;
        }
    }

    public static Map<String, PictureData> getSheetPictrues07(XSSFSheet sheet, XSSFWorkbook workbook) {
        Map<String, PictureData> sheetIndexPicMap = new HashMap();
        Iterator var3 = sheet.getRelations().iterator();

        while(true) {
            POIXMLDocumentPart dr;
            do {
                if (!var3.hasNext()) {
                    return sheetIndexPicMap;
                }

                dr = (POIXMLDocumentPart)var3.next();
            } while(!(dr instanceof XSSFDrawing));

            XSSFDrawing drawing = (XSSFDrawing)dr;
            List<XSSFShape> shapes = drawing.getShapes();
            Iterator var7 = shapes.iterator();

            while(var7.hasNext()) {
                XSSFShape shape = (XSSFShape)var7.next();
                XSSFPicture pic = (XSSFPicture)shape;
                XSSFClientAnchor anchor = pic.getPreferredSize();
                CTMarker ctMarker = anchor.getFrom();
                String picIndex = ctMarker.getRow() + "_" + ctMarker.getCol();
                sheetIndexPicMap.put(picIndex, pic.getPictureData());
            }
        }
    }

    public static String getWebRootPath(String filePath) {
        try {
            String path = null;

            try {
                path = PoiPublicUtil.class.getClassLoader().getResource("").toURI().getPath();
            } catch (URISyntaxException var3) {
            }

            if (path == null || path == "") {
                path = ClassUtils.getDefaultClassLoader().getResource("").getPath();
            }

            LOGGER.debug("--- getWebRootPath ----filePath--- " + path);
            path = path.replace("WEB-INF/classes/", "");
            path = path.replace("file:/", "");
            LOGGER.debug("--- path---  " + path);
            LOGGER.debug("--- filePath---  " + filePath);
            return path + filePath;
        } catch (Exception var4) {
            throw new RuntimeException(var4);
        }
    }

    public static boolean isCollection(Class<?> clazz) {
        return Collection.class.isAssignableFrom(clazz);
    }

    public static boolean isJavaClass(Field field) {
        Class<?> fieldType = field.getType();
        boolean isBaseClass = false;
        if (fieldType.isArray()) {
            isBaseClass = false;
        } else if (fieldType.isPrimitive() || fieldType.getPackage() == null || fieldType.getPackage().getName().equals("java.lang") || fieldType.getPackage().getName().equals("java.math") || fieldType.getPackage().getName().equals("java.sql") || fieldType.getPackage().getName().equals("java.util")) {
            isBaseClass = true;
        }

        return isBaseClass;
    }

    private static boolean isUseInThis(String exportName, String targetId) {
        return targetId == null || exportName.equals("") || exportName.indexOf("_") < 0 || exportName.indexOf(targetId) != -1;
    }

    private static Integer getImageType(String type) {
        if (!type.equalsIgnoreCase("JPG") && !type.equalsIgnoreCase("JPEG")) {
            if (type.equalsIgnoreCase("GIF")) {
                return 8;
            } else if (type.equalsIgnoreCase("BMP")) {
                return 8;
            } else {
                return type.equalsIgnoreCase("PNG") ? 6 : 5;
            }
        } else {
            return 5;
        }
    }
}
