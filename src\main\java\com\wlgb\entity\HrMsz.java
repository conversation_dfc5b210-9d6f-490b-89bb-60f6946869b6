package com.wlgb.entity;


import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: hr_msz
 * @Author: jeecg-boot
 * @Date: 2021-09-15
 * @Version: V1.0
 */
@Data
@Table(name = "hr_msz")
public class HrMsz {

    /**
     * id
     */
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
    /**
     * 面试者姓名
     */
    private java.lang.String name;
    /**
     * 邀请者姓名
     */
    private java.lang.String yyrname;
    /**
     * 年龄  出生日期
     */
    private java.lang.String age;
    /**
     * 性别 1：男  2：女
     */
    private java.lang.String gender;
    /**
     * 电话
     */
    private java.lang.String tel;
    /**
     * 招聘渠道  ['BOSS直聘', '前程无忧', '招募海报', '前程无忧', '校园宣讲会双选会', '智联招聘', '58招聘', '招聘狗', '朋友介绍', '微信微博']
     */
    private java.lang.String genderqudao;
    /**
     * 招聘微信   [ '暂未添加', '181古堡招聘', '199轰趴招聘', '130福利社']
     */
    private java.lang.String genderweixin;
    /**
     * 到岗日期
     */
    private java.lang.String daogangdate;
    /**
     * 意向岗位
     */
    private java.lang.String yxgw;
    /**
     * 面试时间
     */
    private java.lang.String msdata;
    /**
     * 目前状态
     * 1：暂未面试
     * 2：简历储备
     * 3：不合格
     * 4：放弃面试
     * 5：未通过面试
     * 6：面试通过（考虑）
     * 7：放弃试岗
     * 8：试岗期内离职
     * 9：试岗期内
     * 10：已办理入职
     * 11：已沟通轰趴师项目组
     */
    private java.lang.String jljd;
    /**
     * 提交时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date tjdate;
    /**
     * 目前状态
     * 0：暂未面试
     * 1：简历储备
     * 2：不合格
     * 3：放弃面试
     * 4：未通过面试
     * 5：面试通过（考虑）
     * 6：放弃试岗
     * 7：试岗期内离职
     * 8：试岗期内
     * 9：已办理入职
     * 10：已沟通轰趴师项目组
     */
    private java.lang.String mqzt;
    /**
     * 面试官名称
     */
    private java.lang.String msgname;
    /**
     * 简历进度
     * 0：暂未查阅
     * 1：简历储备
     * 2：岗位暂停招聘
     * 3：简历审核未通过
     * 4：放弃面试
     * 5：未通过面试
     * 6：面试通过(考虑)
     * 7：放弃试岗
     * 8：试岗期内离职
     * 9：试岗期内
     * 10：已办理入职
     * 11：已沟通轰趴师项目组
     */

    private java.lang.String zhusu;
    /**
     * 备注信息
     */
    private java.lang.String beizhu;
    /**
     * 最快到岗日期
     */
    private java.lang.String zkdaogangdate;
    /**
     * 面试结果
     */
    private java.lang.String mianshi;
    /**
     * 是否展示在人事列表中  0：展示   1:不展示
     */
    private java.lang.String sfzs;
    /**
     * 第一张简历图片
     */
    private java.lang.String tupian0;
    /**
     * 第2张简历图片
     */
    private java.lang.String tupian1;
    /**
     * 第3张简历图片
     */
    private java.lang.String tupian2;
    /**
     * 预约面试时间
     */
    private java.lang.String yuyuedate;
    /**
     * 是否删除  0：没有删除   1:删除
     */
    private java.lang.String sfsc;
    /**
     * 信息唯一id
     */
    private java.lang.String xxwyid;
    /**
     * 邀约人钉钉id
     */
    private java.lang.String userid;
    /**
     * 面试官id
     */
    private java.lang.String msgid;
    /**
     * 是否面试：0 否，1：已面试
     */
    private java.lang.String sfms;
    /**
     * 岗位城市
     */
    private java.lang.String gwcity;
    /**
     * 面试结果是否通知: 0 否，1 是
     */
    private java.lang.String msjgtz;
    /**
     * 求职者放弃面试或试岗原因记录
     */
    private java.lang.String fqmsbz;
    /**
     * 批量修改人id
     */
    private java.lang.String plxgrid;
    /**
     * 批量修改时间
     */
    private java.lang.String plxgsj;
    /**
     * 修改人id
     */
    private java.lang.String xgrid;
    /**
     * 修改时间
     */
    private java.lang.String xgsj;
    /**
     * 面试官提交时间
     */
    private java.lang.String msgtjsj;
    /**
     * 放弃试岗原因ids
     */
    private java.lang.String fqyyids;
    /**
     * 面试编号
     */
    private java.lang.String msbh;
    /**
     * 岗位部门负责人
     */
    private java.lang.String gwbmfzr;
    /**
     * 岗位部门负责人id
     */
    private java.lang.String gwbmfzrid;
}
