package com.wlgb.service3.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.Dzxyd;
import com.wlgb.mapper.DzxydMapper;
import com.wlgb.service3.DzxydService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月14日 2:37
 */
@Service
@DS("third")
public class DzxydServiceImpl implements DzxydService {
    @Resource
    private DzxydMapper dzxydMapper;

    @Override
    public void save(Dzxyd dzxyd) {
        dzxydMapper.insertSelective(dzxyd);
    }

    @Override
    public void updateById(Dzxyd dzxyd) {
        dzxydMapper.updateByPrimaryKeySelective(dzxyd);
    }

    @Override
    public Dzxyd queryByXid(String xid) {
        Example example = new Example(Dzxyd.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xid", xid);
        return dzxydMapper.selectOneByExample(example);
    }
}
