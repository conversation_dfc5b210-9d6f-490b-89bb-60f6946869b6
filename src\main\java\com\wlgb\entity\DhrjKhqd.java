package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 16:11
 */
@Data
@Table(name = "dhrj_khqd")
public class DhrjKhqd {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**渠道名称*/
    private String qdname;
    /**渠道编号*/
    private String qdbh;
    /**备注*/
    private String bz;
    /**是否删除(0:否,1:是)*/
    private Integer sfsc;
}
