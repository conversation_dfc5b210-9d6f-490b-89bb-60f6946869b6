package com.wlgb.service.impl;

import com.wlgb.entity.WlgbYddYjWkJl;
import com.wlgb.mapper.WlgbYddYjWkJlMapper;
import com.wlgb.service.WlgbYddYjWkJlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/26 21:33
 */
@Service
public class WlgbYddYjWkJlServiceImpl implements WlgbYddYjWkJlService {
    @Resource
    private WlgbYddYjWkJlMapper wlgbYddYjWkJlMapper;

    @Override
    public void save(WlgbYddYjWkJl wlgbYddYjWkJl) {
        wlgbYddYjWkJl.setCreateTime(new Date());
        wlgbYddYjWkJlMapper.insertSelective(wlgbYddYjWkJl);
    }

    @Override
    public void updateById(WlgbYddYjWkJl wlgbYddYjWkJl) {
        wlgbYddYjWkJl.setUpdateTime(new Date());
        wlgbYddYjWkJlMapper.updateByPrimaryKeySelective(wlgbYddYjWkJl);
    }

    @Override
    public WlgbYddYjWkJl queryWlgbYddYjWkJlByWlgbYddYjWkJl(WlgbYddYjWkJl wlgbYddYjWkJl) {
        return wlgbYddYjWkJlMapper.selectOne(wlgbYddYjWkJl);
    }

    @Override
    public WlgbYddYjWkJl queryById(Integer id) {
        return wlgbYddYjWkJlMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<WlgbYddYjWkJl> queryListByWlgbYddYjWkJl(WlgbYddYjWkJl wlgbYddYjWkJl) {
        return wlgbYddYjWkJlMapper.select(wlgbYddYjWkJl);
    }
}
