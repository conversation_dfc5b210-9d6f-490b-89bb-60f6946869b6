package com.wlgb.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.openapi.sdk.api.tuangou.TuangouReceiptGetConsumed;
import com.dianping.openapi.sdk.api.tuangou.TuangouReceiptPrepare;
import com.dianping.openapi.sdk.api.tuangou.entity.*;
import com.dianping.openapi.sdk.httpclient.DefaultOpenAPIClient;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.entity.vo.FwqQunid;
import com.wlgb.entity.vo.QyUtil;
import com.wlgb.entity.vo.WlgbJdbBo;
import com.wlgb.entity.vo.WlgbQrdDzjl;
import com.wlgb.service.*;
import com.wlgb.service2.*;
import com.wlgb.service3.DzxydService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.wlgb.config.Tools.isEmpty;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年04月22日 19:56
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/task2")
public class TaSk2Controller {
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Autowired
    private TbXydService tbXydService;
    @Autowired
    private FwqWxfddtzService fwqWxfddtzService;
    @Autowired
    private FwqWxfddtzjlService fwqWxfddtzjlService;
    @Autowired
    private WeiLianService weiLianService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;
    @Value("${hanshujisuan.douyinurl}")
    private String douyinurl;


    /**
     * 需要一个新功能，业务经理这边一旦有当天的消费单可以提前弹个系统通知提示，
     * 例如xx城市xx别墅有今天入场单，方便业务经理及时对接一线一些工作
     */
    @RequestMapping(value = "/towxfgztz")
    public void towxfgztz(HttpServletRequest request) throws ApiException {
        //查询今天进场的订单
        List<TbXyd> list = tbXydService.queryByJcTime();
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        //主管id
        Set<String> zglist = new HashSet<>();
        //主管的主管id
        Set<String> zg2list = new HashSet<>();
        //
        List<FwqWxfddtzjl> ffjllist = new ArrayList<>();
        for (TbXyd tbXyd1 : list) {
            if (!isEmpty(tbXyd1.getXddbh())) {
                FwqWxfddtzjl ffjl = new FwqWxfddtzjl();
                FwqWxfddtz ff = fwqWxfddtzService.selectByXddbh(tbXyd1.getXddbh());
                if (ff == null) {
                    //还没有发送过
                    DingdingEmployee employee = weiLianDdXcxService.queryDingdingEmployeeByUserName(tbXyd1.getXfd());
                    TbVilla tbVilla = weiLianDdXcxService.queryTbVillaById(tbXyd1.getXbsmc());
                    if ("直营".equals(tbVilla.getVxz())) {
                        int jctype = tbXyd1.getXjcflag();
                        int tctype = tbXyd1.getXtcflag();
                        String text = tbVilla.getVname();

                        if (jctype == tctype && jctype == 0) {
                            text += "有今天白场入场单";
                            ffjl.setChangci("白场");
                        } else if (jctype == tctype && jctype == 1) {
                            text += "有今天晚场入场单";
                            ffjl.setChangci("晚场");
                        } else if (jctype != tctype && jctype == 0) {
                            text += "有今天全天一入场单";
                            ffjl.setChangci("全天一");
                        } else if (jctype != tctype && jctype == 1) {
                            text += "有今天全天二入场单";
                            ffjl.setChangci("全天二");
                        } else {
                            text += "有今天跨场入场单";
                            ffjl.setChangci("跨场");
                        }

                        text += "\n订单编号:" + tbXyd1.getXddbh()
                                + "\n客户姓名:" + tbXyd1.getXzk()
                                + "\n租客电话:" + tbXyd1.getXzkdh()
                                + "\n有" + tbXyd1.getXhpts() + "条好评," + tbXyd1.getXzfts() + "条朋友圈";
                        if ("1".equals(tbXyd1.getXsfgmsjt())) {
                            text += "\n有" + tbXyd1.getXsynum() + "张手机摄影,横幅内容:" + tbXyd1.getXhfnr() + ",欢迎大屏内容:" + tbXyd1.getXdpnr();
                        } else {
                            text += "\n无手机摄影,无横幅,无订制欢迎大屏";
                        }
                        text += "\n场地费金额:" + tbXyd1.getXhfyj();
                        text += "\n已收定金:" + tbXyd1.getXysdj();
                        text += "\n房东:" + tbXyd1.getXfd();
                        DingDBConfig.sendGztzText(dingkey, employee.getUserid(), text);
                        ff = new FwqWxfddtz();
                        ff.setFstime(new Date().toString());
                        ff.setXfd(tbXyd1.getXfd());
                        ff.setXddbh(tbXyd1.getXddbh());

                        ffjl.setQuyu(tbVilla.getCity());
                        ffjl.setVname(tbVilla.getVname());
                        ffjl.setXddbh(tbXyd1.getXddbh());
                        ffjl.setZkname(tbXyd1.getXzk());
                        ffjl.setZktel(tbXyd1.getXzkdh());
                        ffjl.setTime(new Date());
                        ffjl.setHaoping(tbXyd1.getXhpts() + "");
                        ffjl.setPyq(tbXyd1.getXzfts() + "");
                        ffjl.setSjsy(tbXyd1.getXsynum() + "");
                        ffjl.setXhfnr(tbXyd1.getXhfnr());
                        ffjl.setXdpnr(tbXyd1.getXdpnr());
                        ffjl.setPyq(tbXyd1.getXzfts() + "");
                        ffjl.setRenshu(tbXyd1.getXrs() + "");
                        String khly = tbXyd1.getXkhly();
                        String khxz = tbXyd1.getXtdxz();
                        if (!isEmpty(khly)) {
                            TbKhlyxz tbKhlyxz = weiLianService.queryKhLyXzByLidOrLname(khly);
                            ffjl.setKhly(tbKhlyxz.getLname());
                            tbKhlyxz = weiLianService.queryKhLyXzByLidOrLname(khxz);
                            ffjl.setKhxz(tbKhlyxz.getLname());
                        }
                        String zbdz = tbVilla.getPid();
                        CrmBmxq crmBmxq = new CrmBmxq();
                        if (!zbdz.isEmpty()) {
                            crmBmxq = weiLianService.queryCrmBmCxByUserid(zbdz);
                            if (crmBmxq != null) {
                                ff.setZbdz(crmBmxq.getName());
                            }
                        }
                        fwqWxfddtzService.save(ff);
                        if (!zbdz.isEmpty()) {
                            //接下来是给值班店长发
                            DingDBConfig.sendGztzText(dingkey, zbdz, text);
                            //获取值班店长对应的部门，可能存在多个，974396508|610518439
                            if (crmBmxq != null) {
                                List<String[]> strlist = new ArrayList<>();
                                //一级主管
                                if (!isEmpty(crmBmxq.getBm1zg())) {
                                    //保存所有上级id
                                    strlist.add(crmBmxq.getBm1zg().split(","));
                                }
                                if (!isEmpty(crmBmxq.getBm2zg())) {
                                    strlist.add(crmBmxq.getBm2zg().split(","));
                                }
                                if (!isEmpty(crmBmxq.getBm3zg())) {
                                    strlist.add(crmBmxq.getBm3zg().split(","));
                                }
                                if (!isEmpty(crmBmxq.getBm4zg())) {
                                    strlist.add(crmBmxq.getBm4zg().split(","));
                                }
                                if (!isEmpty(crmBmxq.getBm5zg())) {
                                    strlist.add(crmBmxq.getBm5zg().split(","));
                                }
                                if (!isEmpty(crmBmxq.getBm6zg())) {
                                    strlist.add(crmBmxq.getBm6zg().split(","));
                                }
                                if (!isEmpty(crmBmxq.getBm7zg())) {
                                    strlist.add(crmBmxq.getBm7zg().split(","));
                                }
                                if (!isEmpty(crmBmxq.getBm8zg())) {
                                    strlist.add(crmBmxq.getBm8zg().split(","));
                                }
                                //这样 第一个子数组就是一级主管  第二个子数组就是二级主管
                                //这里存放一级主管
                                if (!strlist.isEmpty()) {
                                    zglist.addAll(Arrays.asList(strlist.get(0)));
                                    List<String> slist = Arrays.asList(strlist.get(0));
                                    for (String str : slist) {
                                        ffjl.setQxuserid(str);
                                        ffjl.setJibie("上级");
                                        fwqWxfddtzjlService.save(ffjl);
                                    }
                                }
                                //这里存放二级主管
                                if (strlist.size() > 1) {
                                    zg2list.addAll(Arrays.asList(strlist.get(1)));
                                    List<String> slist = Arrays.asList(strlist.get(1));
                                    for (String str : slist) {
                                        ffjl.setQxuserid(str);
                                        ffjl.setJibie("上上级");
                                        fwqWxfddtzjlService.save(ffjl);
                                    }
                                }
                            }
                        }
                    }
                    ffjllist.add(ffjl);
                } else {
                    //已经发送过了,没必要重复发送
                }
            }
        }

        //在这里给主管发工作通知
        log.info("***所有的一级主管数组********{}", zglist);
        for (String str : zglist) {
            DingDBConfig.sendGztz1(str, dingkey, "上级专属链接", "上级专属链接,您管辖的区域内今天有进场订单", "https://jztdpp.aliwork.com/APP_ISXER59LSVZ25LJL7JZ3/preview/REPORT-BUF66X619DVTY2IH9U9XFCJGKB5Z26EB47H8M0?isPreview=true&topicId=1974105&formUuid=REPORT-BUF66X619DVTY2IH9U9XFCJGKB5Z26EB47H8M0&navConfig.type=none&navConfig.layout=auto&corpid=ding291dcdb4d2ea030935c2f4657eb6378f");
        }

        //在这里给主管的主管发消息
        log.info("***所有的二级主管数组********{}", zg2list);
        for (String str : zglist) {
            DingDBConfig.sendGztz1(str, dingkey, "上上级专属链接", "上上级专属链接,您管辖的区域内今天有进场订单", "https://jztdpp.aliwork.com/APP_ISXER59LSVZ25LJL7JZ3/preview/REPORT-9T866B91Z7XT4HQ1966LE4P5MSUK29NO8WJ8M2?isPreview=true&topicId=1974105&formUuid=REPORT-9T866B91Z7XT4HQ1966LE4P5MSUK29NO8WJ8M2&navConfig.type=none&navConfig.layout=auto&corpid=ding291dcdb4d2ea030935c2f4657eb6378f");
        }
    }

}
