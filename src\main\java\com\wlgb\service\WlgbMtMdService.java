package com.wlgb.service;

import com.wlgb.entity.WlgbMtMd;

import java.util.List;
import java.util.Map;

public interface WlgbMtMdService {
    void saveBatch(List<WlgbMtMd> list);

    void save(WlgbMtMd wlgbMtMd);

    Integer queryCountByOpenUuId(String openUuId);

    WlgbMtMd queryCountByOpenUuId2(String openUuId);

    WlgbMtMd queryByShopUuId(String shopUuId);

    WlgbMtMd queryByopBizCode(Map<String, Object>... map);

    List<WlgbMtMd> queryAllWlgbMtMd();

    void delete(WlgbMtMd wlgbMtMd);

    List<WlgbMtMd> queryByToken(Map<String, Object>... map);

    List<WlgbMtMd> queryByopBizCodeIsNull(Map<String, Object>... map);


    void updateById(WlgbMtMd wlgbMtMd);
}
