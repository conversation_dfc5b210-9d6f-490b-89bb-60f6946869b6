package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 费用科目
 * @Author: jeecg-boot
 * @Date:   2022-04-11
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_fykm")
public class WlgbJdFykm {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
	/**编码*/
    private java.lang.String bm;
	/**名称*/
    private java.lang.String mc;
	/**全名*/
    private java.lang.String qm;
	/**余额方向*/
    private java.lang.String yefx;
	/**科目类别*/
    private java.lang.String kmlb;
	/**外币核算*/
    private java.lang.String wbhs;
	/**核算维度*/
    private java.lang.String hswd;
	/**数据状态*/
    private java.lang.String sjzt;
	/**是否删除（0：否，1：是）*/
    private java.lang.Integer sfsc;
}
