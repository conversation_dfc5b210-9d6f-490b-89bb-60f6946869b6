package com.wlgb.service;

import com.wlgb.entity.WlgbJdDjLsjlb;
import com.wlgb.entity.WlgbJdDjbbd;

import java.util.List;


public interface WlgbJdDjbbdService {
    Integer queryCountByWybs(String wybs);

    void save(WlgbJdDjbbd wlgbJdDjbbd);

    void updateById(WlgbJdDjbbd wlgbJdDjbbd);

    WlgbJdDjbbd queryByLshAndFormId(String lsh, String formInstId);

    Integer queryCountByLshAndFormId(String lsh, String formInstId);

    WlgbJdDjbbd queryByFormIdAndDjLx(String formId, Integer djlx);

    List<WlgbJdDjbbd> queryByLshAndSfSc(String lsh);

    List<WlgbJdDjbbd> queryByLshAndSfScAndDjLx(String lsh, Integer djLx);

    List<WlgbJdDjbbd> queryByLshAndSfScAndSfXd(String lsh, Integer sfxd);

    WlgbJdDjbbd queryByLshAndSkBhAndSfScAndDjLx(String lsh, String skBh, Integer sfSc, Integer djLx);

    List<WlgbJdDjbbd> queryByDdBhAndSfSc(String ddbh);

    List<WlgbJdDjbbd> queryDjYxdWxgByLsh(String lsh);
}
