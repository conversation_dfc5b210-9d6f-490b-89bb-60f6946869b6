package com.wlgb.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.PostClient;
import com.aliyun.dingtalkyida_1_0.Client;
import com.aliyun.dingtalkyida_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.*;
import com.aliyun.teautil.models.RuntimeOptions;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/12/6 23:04
 * @Version 1.0
 */
public class YdConfig {
    public static Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }

    /**
     * 获取流程实例
     * @param token
     * @param ydAppkey
     * @param formId
     * @param json
     * @return
     * @throws Exception
     */
    public static GatewayResult getLcSlData(String token, YdAppkey ydAppkey, String formId, String json) throws Exception {
        Client client = createClient();
        GetInstancesHeaders getInstancesHeaders = new GetInstancesHeaders();
        getInstancesHeaders.xAcsDingtalkAccessToken = token;
        GetInstancesRequest getInstancesRequest = new GetInstancesRequest()
                .setSystemToken(ydAppkey.getToken())
                .setSearchFieldJson(json)
                .setFormUuid(formId)
                .setUserId("yida_pub_account")
                .setAppType(ydAppkey.getAppkey());

        GatewayResult gatewayResult = new GatewayResult();
        GetInstancesResponse response = null;
        try {
            response = client.getInstancesWithOptions(getInstancesRequest, getInstancesHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        }
        if (response != null) {
            gatewayResult.setSuccess(true);
            if (response.getHeaders() != null) {
                List<GetInstancesResponseBody.GetInstancesResponseBodyData> data = response.getBody().getData();
                Map<String, ?> data1 = data.get(0).getData();
                JSONObject json1 = new JSONObject((Map<String, Object>) data1);
                gatewayResult.setResult(json1.toJSONString());
            }
        }
        return gatewayResult;
    }

    /**
     * 获取宜搭表单数据
     *
     * @param appType     应用编码
     * @param systemToken 应用密钥
     * @return
     */
    public static String hqBdSj(String appType, String systemToken, String formInstId) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", appType);
            param.put("systemToken", systemToken);
            param.put("userId", "yida_pub_account");
            param.put("formInstId", formInstId);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/form/getFormDataById.json");
            boolean ss=result.getSuccess();
            if (!ss){
            }
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * 修改表单实例
     *
     * @param token          钉钉token
     * @param ydAppkey       宜搭应用配置
     * @param userId         修改人
     * @param formInstanceId 实例id
     * @param json           修改数据
     */
    public static GatewayResult xgBdSl(String token, YdAppkey ydAppkey, String userId, String formInstanceId, String json) throws Exception {
        Client client = createClient();
        UpdateFormDataHeaders updateFormDataHeaders = new UpdateFormDataHeaders();
        updateFormDataHeaders.xAcsDingtalkAccessToken = token;
        UpdateFormDataRequest updateFormDataRequest = new UpdateFormDataRequest()
                .setAppType(ydAppkey.getAppkey())
                .setSystemToken(ydAppkey.getToken())
                .setUserId(userId)
                .setLanguage("zh_CN")
                .setFormInstanceId(formInstanceId)
                .setUseLatestVersion(true)
                .setUpdateFormDataJson(json);

        GatewayResult gatewayResult = new GatewayResult();
        UpdateFormDataResponse updateFormDataResponse = null;
        try {
            updateFormDataResponse = client.updateFormDataWithOptions(updateFormDataRequest, updateFormDataHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        }
        if (updateFormDataResponse != null) {
            gatewayResult.setSuccess(true);
            if (updateFormDataResponse.getHeaders() != null) {
                gatewayResult.setResult(updateFormDataResponse.getHeaders().toString());
            }
        }
        return gatewayResult;
    }

    /**
     * 新增表单实例
     *
     * @param token    钉钉token
     * @param ydAppkey 宜搭应用配置
     * @param json     新增数据
     * @param userId   发起人
     * @param formUuId 表单id
     */
    public static GatewayResult xzBdSl(String token, YdAppkey ydAppkey, String json, String userId, String formUuId) throws Exception {
        Client client = createClient();
        SaveFormDataHeaders saveFormDataHeaders = new SaveFormDataHeaders();
        saveFormDataHeaders.xAcsDingtalkAccessToken = token;
        SaveFormDataRequest saveFormDataRequest = new SaveFormDataRequest()
                .setSystemToken(ydAppkey.getToken())
                .setFormUuid(formUuId)
                .setUserId(userId)
                .setAppType(ydAppkey.getAppkey())
                .setFormDataJson(json);

        GatewayResult gatewayResult = new GatewayResult();
        SaveFormDataResponse saveFormDataResponse = null;
        try {
            saveFormDataResponse = client.saveFormDataWithOptions(saveFormDataRequest, saveFormDataHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        }
        if (saveFormDataResponse != null) {
            gatewayResult.setSuccess(true);
            if (saveFormDataResponse.getBody() != null) {
                gatewayResult.setResult(saveFormDataResponse.getBody().getResult());
            }
        }
        System.out.println(gatewayResult);

        return gatewayResult;
    }

    /**
     * 获取表单实例数据
     */
    public static GatewayResult queryBdSlData(Integer page, YdAppkey ydAppkey, String token, String searchString, String formUuId) throws Exception {
        GatewayResult gatewayResult = new GatewayResult();
        Client client = createClient();

        SearchFormDataSecondGenerationNoTableFieldHeaders searchFormDataSecondGenerationNoTableFieldHeaders = new SearchFormDataSecondGenerationNoTableFieldHeaders();
        searchFormDataSecondGenerationNoTableFieldHeaders.xAcsDingtalkAccessToken = token;
        SearchFormDataSecondGenerationNoTableFieldRequest searchFormDataSecondGenerationNoTableFieldRequest = new SearchFormDataSecondGenerationNoTableFieldRequest()
                .setPageNumber(page)
                .setFormUuid(formUuId)
                .setSearchCondition(searchString)
                .setAppType(ydAppkey.getAppkey())
                .setSystemToken(ydAppkey.getToken())
                .setPageSize(100)
                .setUserId("yida_pub_account");
        try {
            SearchFormDataSecondGenerationNoTableFieldResponse searchFormDataSecondGenerationNoTableFieldResponse = client.searchFormDataSecondGenerationNoTableFieldWithOptions(searchFormDataSecondGenerationNoTableFieldRequest, searchFormDataSecondGenerationNoTableFieldHeaders, new RuntimeOptions());
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("data", searchFormDataSecondGenerationNoTableFieldResponse.getBody().getData());
            jsonObject1.put("totalCount", searchFormDataSecondGenerationNoTableFieldResponse.getBody().getTotalCount());
            jsonObject1.put("pageNumber", searchFormDataSecondGenerationNoTableFieldResponse.getBody().getPageNumber());
            gatewayResult.setResult(jsonObject1.toJSONString());
            gatewayResult.setSuccess(true);
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());
        }

        return gatewayResult;
    }

    /**
     * 获取图片免登地址
     */
    public static GatewayResult getFileUrl(String token, YdAppkey ydAppkey, String url) throws Exception {
        Client client = createClient();
        GetOpenUrlHeaders getOpenUrlHeaders = new GetOpenUrlHeaders();
        getOpenUrlHeaders.xAcsDingtalkAccessToken = token;
        GetOpenUrlRequest getOpenUrlRequest = new GetOpenUrlRequest()
                .setSystemToken(ydAppkey.getToken())
                .setLanguage("zh_CN")
                .setFileUrl(url)
                .setUserId("yida_pub_account")
                .setTimeout(60000L);
        GatewayResult gatewayResult = new GatewayResult();
        GetOpenUrlResponse urlResponse = null;
        try {
            urlResponse = client.getOpenUrlWithOptions(ydAppkey.getAppkey(), getOpenUrlRequest, getOpenUrlHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        }
        if (urlResponse != null) {
            gatewayResult.setSuccess(true);
            if (urlResponse.getBody() != null) {
                gatewayResult.setResult(urlResponse.getBody().getResult());
            }
        }
        System.out.println(gatewayResult);

        return gatewayResult;
    }

    public static void main(String[] args) throws Exception {
        YdAppkey ydAppkey = new YdAppkey();
        ydAppkey.setToken("CFYJVHDVC9SM4CAD1K2F3BZP5NMA2Q1W6A2KKBC1");
        ydAppkey.setAppkey("APP_J15SHV6W8MP3BI9SY7TL");
        Dingkey dingkey = new Dingkey();
        dingkey.setAppkey("dingjrnvifk8bmoyzaai");
        dingkey.setAppsecret("3LGENGTZ3_6dX52-37BihNYt1WxYcq2OJyuSBJ3naAfhw0-zBc2vpIKA_DfjU2aI");
        String token = DingToken.token(dingkey);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cybh", "202309171152056454");
        GatewayResult gatewayResult = queryBdSlData(1, ydAppkey, token, jsonObject.toJSONString(), "FORM-6Q866L81621TOWRCYSHTECVS90KY10URT5RSKW5");
        if (gatewayResult.getSuccess()) {
            JSONObject jsonObject1 = JSONObject.parseObject(gatewayResult.getResult());
            System.out.println(jsonObject1);
        }
        System.out.println(gatewayResult.getResult());
    }

    /**
     * 调用宜搭api接口新增表单内容
     *
     * @param formDataJson 表单数据(需要修改的内容)
     * @param userId       执行人ID,也就是店长ID
     * @param appType      应用编码
     * @param systemToken  应用密钥
     * @param formUuid     表单id
     * @return
     */
    public static String xzbd(String formDataJson, String userId, String appType, String systemToken, String formUuid) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", appType);
            param.put("systemToken", systemToken);
            param.put("userId", userId);
            param.put("formUuid", formUuid);
            param.put("formDataJson", formDataJson);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/form/saveFormData.json");
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * 调用宜搭api接口修改表单内容
     *
     * @param jsonString  表单数据(需要修改的内容)
     * @param appType     应用编码
     * @param systemToken 应用密钥
     * @param formInstId  要更新的表单数据ID
     * @return
     */
    public static String gxbd(String jsonString, String appType, String systemToken, String formInstId) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", appType);
            param.put("systemToken", systemToken);
            param.put("userId", "yida_pub_account");
            param.put("formInstId", formInstId);
            param.put("useLatestVersion", "y");
            param.put("updateFormDataJson", jsonString);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/form/updateFormData.json");
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * 调用宜搭api接口删除表单实例
     *
     * @param appType     应用编码
     * @param systemToken 应用密钥
     * @param formInstId  表单id
     * @return
     */
    public static String scBdSl(String appType, String systemToken, String formInstId) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", appType);
            param.put("systemToken", systemToken);
            param.put("userId", "yida_pub_account");
            param.put("formInstId", formInstId);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/form/deleteFormData.json");
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    public static String getDatas(YdAppkey ydAppkey, String searchFieldJson, String formUuid, String currentPage) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", ydAppkey.getAppkey());
            param.put("systemToken", ydAppkey.getToken());
            param.put("userId", "yida_pub_account");
            param.put("searchFieldJson", searchFieldJson);
            param.put("currentPage", currentPage);
            param.put("pageSize", "100");
            param.put("formUuid", formUuid);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/form/searchFormDatas.json");
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    public static String getDatas1(YdAppkey ydAppkey, String searchFieldJson, String formUuid, String currentPage, String pageSize) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", ydAppkey.getAppkey());
            param.put("systemToken", ydAppkey.getToken());
            param.put("userId", "yida_pub_account");
            param.put("searchFieldJson", searchFieldJson);
            param.put("currentPage", currentPage);
            param.put("pageSize", pageSize);
            param.put("formUuid", formUuid);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/form/searchFormDatas.json");
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * 调用宜搭api接口根据条件获取实例详情
     *
     * @param appType     应用编码
     * @param systemToken 应用密钥
     * @return
     */
    public static String zzBdSl(String appType, String systemToken, String formUuid, String searchFieldJson) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", appType);
            param.put("systemToken", systemToken);
            param.put("userId", "yida_pub_account");
            param.put("searchFieldJson", searchFieldJson);
            param.put("pageSize", "100");
            param.put("formUuid", formUuid);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/form/searchFormDatas.json");
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * 调用宜搭api接口根据条件获取实例id
     *
     * @param userId      执行人ID,也就是店长ID
     * @param appType     应用编码
     * @param systemToken 应用密钥
     * @param formUuid    表单id
     * @return
     */
    public static String hqId(String userId, String appType, String systemToken, String formUuid, String searchFieldJson, String currentPage, String pageSize) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", appType);
            param.put("systemToken", systemToken);
            param.put("userId", userId);
            param.put("currentPage", currentPage);
            param.put("pageSize", pageSize);
            param.put("formUuid", formUuid);
            param.put("searchFieldJson", searchFieldJson);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/form/searchFormDataIds.json");
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * 调用宜搭api接口根据条件获取实例详情
     *
     * @param userId      执行人ID,也就是店长ID
     * @param appType     应用编码
     * @param systemToken 应用密钥
     * @param formUuid    表单id
     * @return
     */
    public static String hqLcSlData(String userId, String appType, String systemToken, String formUuid, String json, String currentPage) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", appType);
            param.put("systemToken", systemToken);
            param.put("userId", userId);
            param.put("formUuid", formUuid);
            param.put("instanceStatus", "RUNNING");
            param.put("pageSize", "100");
            param.put("currentPage", currentPage);
            param.put("searchFieldJson", json);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/process/getInstances.json");
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * 调用宜搭api接口根据条件获取表单实例详情-分页
     *
     * @param userId      执行人ID,也就是店长ID
     * @param appType     应用编码
     * @param systemToken 应用密钥
     * @return
     */
    public static String getFromDatas(String userId, String appType, String systemToken, String formUuid, String currentPage, String createFrom, String createTo, String searchFieldJson) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", appType);
            param.put("systemToken", systemToken);
            param.put("userId", userId);
            param.put("searchFieldJson", searchFieldJson);
            param.put("currentPage", currentPage);
            param.put("pageSize", "100");
            param.put("createFrom", createFrom);
            param.put("createTo", createTo);
            param.put("formUuid", formUuid);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/form/searchFormDatas.json");
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * 部分宜搭流程需要协议单图片数组（目前只有一个协议单图片,暂时不需要数据）
     *
     * @param pngurl  协议单图片的阿里云OSS链接
     * @param pbgname 协议单名字
     * @return
     */
    public static List<JSONObject> setTpList(String pngurl, String pbgname) {
        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("downloadUrl", pngurl);
        jsonObject.put("name", pbgname);
        jsonObject.put("previewUrl", pngurl);
        jsonObject.put("url", pngurl);
        jsonObject.put("ext", "png");
        list.add(jsonObject);
        return list;
    }


    /**
     * 宜搭图片上传截取地址
     *
     * @param json 上传的json图片地址
     * @return 截取后的图片地址
     */
    public static String ydTpScJq(String json) {
        JSONArray objects = JSONObject.parseArray(json);
        if (objects.size() > 0) {
            String previewUrl = objects.getJSONObject(0).getString("url");
            if (previewUrl == null) {
                return null;
            } else {
                if (previewUrl.length() > 8) {
                    String substring = previewUrl.substring(0, 8);
                    if ("https://".equals(substring)) {
                        return previewUrl;
                    } else {
                        return "https://jztdpp.aliwork.com" + previewUrl;
                    }
                } else {
                    return "https://jztdpp.aliwork.com" + previewUrl;
                }

            }
        }
        return null;
    }

    /**
     * 根据实例ID获取流程实例详情
     *
     * @param appType           应用编码
     * @param systemToken       应用密钥
     * @param processInstanceId 流程实例ID
     * @return
     */
    public static String hqLcSl(String appType, String systemToken, String processInstanceId) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", appType);
            param.put("systemToken", systemToken);
            param.put("userId", "yida_pub_account");
            param.put("processInstanceId", processInstanceId);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/process/getInstanceById.json");
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }


    /**
     * 获取流程审批记录
     *
     * @param slid        实例id
     * @param appType     应用appType
     * @param systemToken 应用systemToken
     */
    public static String getSpJdJl(String slid, String appType, String systemToken) {
        try {
            ExecutableClient executableClient = new ExecutableClient();
            executableClient.setAccessKey("changshaweilianjiudian-F67nm2m");
            executableClient.setSecretKey("LF2lib16ErOHAV8z8344qhGK2kuFrxMs8EB0K11a");
            executableClient.setDomainName("s-api.alibaba-inc.com");
            executableClient.setProtocal("https");
            executableClient.init();
            PostClient postClient = executableClient.newPostClient("/yida_vpc/process/getOperationRecords.json");
            postClient.addParameter("appType", appType);
            postClient.addParameter("systemToken", systemToken);
            postClient.addParameter("processInstanceId", slid);
            postClient.addParameter("userId", "yida_pub_account");

            String apiResult = postClient.post();
            return apiResult;
        } catch (Throwable e) {
            e.printStackTrace();
            GatewayResult result = new GatewayResult();
            result.setSuccess(false);
            return JSON.toJSONString(result);
        }
    }

    /**
     * 转交流程
     *
     * @param appType           应用编码
     * @param systemToken       应用密钥
     * @param processInstanceId 实例id
     * @param nowActionerId     新的执行人
     * @param remark            转交备注
     * @param taskId            任务id
     * @return
     */
    public static String zjLc(String appType, String systemToken, String processInstanceId, String nowActionerId, String remark, String taskId) {
        GatewayResult result = null;
        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("appType", appType);
            param.put("systemToken", systemToken);
            param.put("userId", "yida_pub_account");
            param.put("processInstanceId", processInstanceId);
            param.put("nowActionerId", nowActionerId);
            param.put("byManager", "y");
            param.put("remark", remark);
            param.put("taskId", taskId);
            result = GatewayRequestUtil.baseRequest(param, "/yida_vpc/task/redirectTask.json");
            return JSON.toJSONString(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }

    /**
     * 根据实例id获取表单实例详情
     */
    public static GatewayResult querySlXqBySlid(String token, YdAppkey ydAppkey, String slid) throws Exception {
        Client client = createClient();
        GetFormDataByIDHeaders getFormDataByIDHeaders = new GetFormDataByIDHeaders();
        getFormDataByIDHeaders.xAcsDingtalkAccessToken = token;
        GetFormDataByIDRequest getFormDataByIDRequest = new GetFormDataByIDRequest()
                .setSystemToken(ydAppkey.getToken())
                .setLanguage("zh_CN")
                .setUserId("yida_pub_account")
                .setAppType(ydAppkey.getAppkey());
        GatewayResult gatewayResult = new GatewayResult();
        GetFormDataByIDResponse formDataByIDWithOptions = null;
        try {
            formDataByIDWithOptions = client.getFormDataByIDWithOptions(slid, getFormDataByIDRequest, getFormDataByIDHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        }
        if (formDataByIDWithOptions != null) {
            gatewayResult.setSuccess(true);
            if (formDataByIDWithOptions.getHeaders() != null) {
                Map<String, ?> formData = formDataByIDWithOptions.getBody().formData;
                JSONObject json1 = new JSONObject((Map<String, Object>) formData);
                gatewayResult.setResult(json1.toJSONString());
            }
        }
        return gatewayResult;
    }

    /**
     * 获取宜搭审批记录
     */
    public static GatewayResult querySpJl(String token, YdAppkey ydAppkey, String slid) throws Exception {
        Client client = createClient();
        GetOperationRecordsHeaders getOperationRecordsHeaders = new GetOperationRecordsHeaders();
        getOperationRecordsHeaders.xAcsDingtalkAccessToken = token;
        GetOperationRecordsRequest getOperationRecordsRequest = new GetOperationRecordsRequest()
                .setSystemToken(ydAppkey.getToken())
                .setProcessInstanceId(slid)
                .setLanguage("zh_CN")
                .setUserId("yida_pub_account")
                .setAppType(ydAppkey.getAppkey());
        GatewayResult gatewayResult = new GatewayResult();
        GetOperationRecordsResponse operationRecordsWithOptions = null;
        try {
            operationRecordsWithOptions = client.getOperationRecordsWithOptions(getOperationRecordsRequest, getOperationRecordsHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        }
        if (operationRecordsWithOptions != null) {
            gatewayResult.setSuccess(true);
            if (operationRecordsWithOptions.getHeaders() != null) {
                List<GetOperationRecordsResponseBody.GetOperationRecordsResponseBodyResult> result = operationRecordsWithOptions.getBody().getResult();
                gatewayResult.setResult(JSONObject.toJSONString(result));
            }
        }
        return gatewayResult;
    }

    /**
     * 执行宜搭审批任务
     */
    public static GatewayResult zxYdSpJdRw(String token, String slid, YdAppkey ydAppkey, String text, String json, long taskId, String userId) throws Exception {
        Client client = createClient();
        ExecuteTaskHeaders executeTaskHeaders = new ExecuteTaskHeaders();
        executeTaskHeaders.xAcsDingtalkAccessToken = token;
        ExecuteTaskRequest executeTaskRequest = new ExecuteTaskRequest()
                //AGREE--同意，DISAGREE--拒绝
                .setOutResult("AGREE")
                .setProcessInstanceId(slid)
                .setAppType(ydAppkey.getAppkey())
                .setFormDataJson(json)
                .setSystemToken(ydAppkey.getToken())
                .setLanguage("zh_CN")
                //审批意见（备注内容）
                .setRemark(text)
                //节点任务id
                .setTaskId(taskId)
                .setUserId(userId);
        ExecuteTaskResponse executeTaskResponse = null;
        GatewayResult gatewayResult = new GatewayResult();
        try {
            executeTaskResponse = client.executeTaskWithOptions(executeTaskRequest, executeTaskHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        }
        if (executeTaskResponse != null) {
            gatewayResult.setSuccess(true);
            gatewayResult.setResult(executeTaskResponse.getHeaders().toString());
        }

        return gatewayResult;
    }

    /**流程终止*/
    public static GatewayResult lcZz(YdAppkey ydAppkey, String token, String slid) throws Exception {
        GatewayResult gatewayResult = new GatewayResult();
        Client client = createClient();
        TerminateInstanceHeaders terminateInstanceHeaders = new TerminateInstanceHeaders();
        terminateInstanceHeaders.xAcsDingtalkAccessToken = token;
        TerminateInstanceRequest terminateInstanceRequest = new TerminateInstanceRequest()
                .setSystemToken(ydAppkey.getToken())
                .setProcessInstanceId(slid)
                .setLanguage("zh_CN")
                .setUserId("yida_pub_account")
                .setAppType(ydAppkey.getAppkey());
        TerminateInstanceResponse terminateInstanceResponse = null;
        try {
            terminateInstanceResponse = client.terminateInstanceWithOptions(terminateInstanceRequest, terminateInstanceHeaders, new RuntimeOptions());
        } catch (TeaException err) {
            gatewayResult.setSuccess(false);
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            gatewayResult.setSuccess(false);
            gatewayResult.setResult(err.getData().toString());
            gatewayResult.setErrorCode(err.getCode());
            gatewayResult.setErrorMsg(err.getMessage());

        }
        if(terminateInstanceResponse != null){
            gatewayResult.setSuccess(true);
            gatewayResult.setResult(terminateInstanceResponse.getHeaders().toString());
        }

        return gatewayResult;
    }

}
