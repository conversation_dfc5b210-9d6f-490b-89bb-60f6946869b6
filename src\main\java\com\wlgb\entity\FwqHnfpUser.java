package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "fwq_hnfpuser")
public class FwqHnfpUser {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    private String name;
    private String tel;
    private java.util.Date rztime;
    private java.util.Date lztime;
}
