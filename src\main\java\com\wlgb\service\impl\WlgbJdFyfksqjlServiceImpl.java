package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdFyfksqjl;
import com.wlgb.mapper.WlgbJdFyfksqjlMapper;
import com.wlgb.service.WlgbJdFyfksqjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 22:33
 */
@Service
public class WlgbJdFyfksqjlServiceImpl implements WlgbJdFyfksqjlService {
    @Resource
    private WlgbJdFyfksqjlMapper wlgbJdFyfksqjlMapper;

    @Override
    public void save(WlgbJdFyfksqjl wlgbJdFyfksqjl) {
        wlgbJdFyfksqjl.setCreateTime(new Date());
        wlgbJdFyfksqjl.setId(IdConfig.uuId());
        wlgbJdFyfksqjlMapper.insertSelective(wlgbJdFyfksqjl);
    }

    @Override
    public void updateById(WlgbJdFyfksqjl wlgbJdFyfksqjl) {
        wlgbJdFyfksqjl.setUpdateTime(new Date());
        wlgbJdFyfksqjlMapper.updateByPrimaryKeySelective(wlgbJdFyfksqjl);
    }

    @Override
    public WlgbJdFyfksqjl queryBySpBhAndSfLrJd(String spBh, Integer sfLrJd) {
        Example example = new Example(WlgbJdFyfksqjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spBh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdFyfksqjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdFyfksqjl> queryListWlgbJdFyfksqjl(WlgbJdFyfksqjl wlgbJdFyfksqjl) {
        return wlgbJdFyfksqjlMapper.select(wlgbJdFyfksqjl);
    }
}
