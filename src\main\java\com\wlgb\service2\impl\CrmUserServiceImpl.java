package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmUser;
import com.wlgb.mapper.CrmUserMapper;
import com.wlgb.service2.CrmUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/16 17:50
 */
@Service
@DS(value = "second")
public class CrmUserServiceImpl implements CrmUserService {
    @Resource
    private CrmUserMapper crmUserMapper;

    @Override
    public void save(CrmUser crmUser) {
        crmUserMapper.insertSelective(crmUser);
    }

    @Override
    public List<CrmUser> select(CrmUser crmUser) {
        return crmUserMapper.select(crmUser);
    }

    @Override
    public void update(CrmUser crmUser) {
        crmUserMapper.updateByPrimaryKey(crmUser);
    }
}
