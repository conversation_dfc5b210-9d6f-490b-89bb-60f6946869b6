package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.HrSljl;
import com.wlgb.mapper.HrSljlMapper;
import com.wlgb.service.HrSljlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/31 21:52
 */
@Service
public class HrSljlServiceImpl implements HrSljlService {
    @Resource
    private HrSljlMapper hrSljlMapper;

    @Override
    public void save(HrSljl hrSljl) {
        hrSljl.setId(IdConfig.uuId());
        hrSljl.setCreateTime(new Date());
        hrSljlMapper.insertSelective(hrSljl);
    }

    @Override
    public HrSljl queryOneByHrSljl(HrSljl hrSljl) {
        return hrSljlMapper.selectOne(hrSljl);
    }

    @Override
    public void removeById(String id) {
        hrSljlMapper.deleteByPrimaryKey(id);
    }
}
