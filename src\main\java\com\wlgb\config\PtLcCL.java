package com.wlgb.config;

import com.alibaba.fastjson.JSONObject;

import java.util.Map;

/**
 * @Description: 普通流程处理
 * @author: xuzhenfei
 * @date: 2023年04月18日 16:33
 */
public class PtLcCL extends LcConfig{
    /**
     * 发送流程表单
     *
     * @param userid 收取人id
     * @param map    表单内容
     */
    public static GatewayResult lcid(String userid, Map<String, Object> map, YdAppkey ydAppkey, YdBd ydBd,
                                    String token) {
        JSONObject json = new JSONObject(map);
        GatewayResult gatewayResult;
        try {
            gatewayResult = DingBdLcConfig.fqXzLcSl(token, ydAppkey, userid, ydBd.getFormid(), ydBd.getCode(), json.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
            gatewayResult = new GatewayResult();
        }
        return gatewayResult;
    }
}
