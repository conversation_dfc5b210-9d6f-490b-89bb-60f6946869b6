package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Description: 乐诚项目报销申请记录
 * @Author: jeecg-boot
 * @Date:   2022-07-30
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_lcxmbxsqjl")
public class WlgbJdLcxmbxsqjl {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private String id;
	/**创建人*/
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;
	/**更新人*/
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
	/**所属部门*/
    private String sysOrgCode;
	/**审批编号*/
    private String spbh;
	/**审批标题*/
    private String spbt;
	/**申请时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date sqsj;
	/**审批结束时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date spjssj;
	/**申请人*/
    private String sqr;
	/**申请人id*/
    private String sqrid;
	/**申请人部门*/
    private String sqrbm;
	/**申请人部门id*/
    private String sqrbmid;
	/**收款账户名*/
    private String skzhh;
	/**收款账号*/
    private String skzh;
	/**收款开户行*/
    private String skkhh;
	/**费用发生项目名称*/
    private String fyfsxmmc;
	/**门店类型*/
    private String mdlx;
	/**报销总金额*/
    private Double bxzje;
	/**出纳转账金额*/
    private Double cnzzje;
	/**出纳转账时间*/
    private String cnzzsj;
	/**是否录入金蝶*/
    private Integer sflrjd;
	/**实例id*/
    private String slid;
}
