package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdWlbtxjl;
import com.wlgb.mapper.WlgbJdWlbtxjlMapper;
import com.wlgb.service.WlgbJdWlbtxjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 22:51
 */
@Service
public class WlgbJdWlbtxjlServiceImpl implements WlgbJdWlbtxjlService {
    @Resource
    private WlgbJdWlbtxjlMapper wlgbJdWlbtxjlMapper;

    @Override
    public void save(WlgbJdWlbtxjl wlgbJdWlbtxjl) {
        wlgbJdWlbtxjl.setCreateTime(new Date());
        wlgbJdWlbtxjl.setId(IdConfig.uuId());
        wlgbJdWlbtxjlMapper.insertSelective(wlgbJdWlbtxjl);
    }

    @Override
    public void updateById(WlgbJdWlbtxjl wlgbJdWlbtxjl) {
        wlgbJdWlbtxjl.setUpdateTime(new Date());
        wlgbJdWlbtxjlMapper.updateByPrimaryKeySelective(wlgbJdWlbtxjl);
    }

    @Override
    public WlgbJdWlbtxjl queryBySpBhAndSfLrJd(String spbh, Integer sfLrJd) {
        Example example = new Example(WlgbJdWlbtxjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("spbh", spbh);
        criteria.andEqualTo("sflrjd", sfLrJd);
        return wlgbJdWlbtxjlMapper.selectOneByExample(example);
    }

    @Override
    public List<WlgbJdWlbtxjl> queryListByWlgbJdWlbtxjl(WlgbJdWlbtxjl wlgbJdWlbtxjl) {
        return wlgbJdWlbtxjlMapper.select(wlgbJdWlbtxjl);
    }
}
