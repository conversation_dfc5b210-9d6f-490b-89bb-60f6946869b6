package com.wlgb.service.impl;

import com.wlgb.entity.WlgbEatBsjl;
import com.wlgb.mapper.WlgbEatBsjlMapper;
import com.wlgb.service.WlgbEatBsjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/12 16:17
 */
@Service
public class WlgbEatBsjlServiceImpl implements WlgbEatBsjlService {
    @Resource
    private WlgbEatBsjlMapper wlgbEatBsjlMapper;

    @Override
    public void save(WlgbEatBsjl wlgbEatBsjl) {
        wlgbEatBsjl.setCreateTime(new Date());
        wlgbEatBsjlMapper.insertSelective(wlgbEatBsjl);
    }
}
