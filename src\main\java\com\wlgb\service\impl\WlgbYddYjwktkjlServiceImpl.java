package com.wlgb.service.impl;

import com.wlgb.entity.WlgbYddYjwktkjl;
import com.wlgb.mapper.WlgbYddYjwktkjlMapper;
import com.wlgb.service.WlgbYddYjwktkjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/26 0:02
 */
@Service
public class WlgbYddYjwktkjlServiceImpl implements WlgbYddYjwktkjlService {
    @Resource
    private WlgbYddYjwktkjlMapper wlgbYddYjwktkjlMapper;

    @Override
    public void save(WlgbYddYjwktkjl wlgbYddYjwktkjl) {
        wlgbYddYjwktkjl.setCreateTime(new Date());
        wlgbYddYjwktkjlMapper.insertSelective(wlgbYddYjwktkjl);
    }

    @Override
    public void updateById(WlgbYddYjwktkjl wlgbYddYjwktkjl) {
        wlgbYddYjwktkjlMapper.updateByPrimaryKeySelective(wlgbYddYjwktkjl);
    }

    @Override
    public WlgbYddYjwktkjl queryWlgbYddYjwktkjlByWlgbYddYjwktkjl(WlgbYddYjwktkjl wlgbYddYjwktkjl) {
        return wlgbYddYjwktkjlMapper.selectOne(wlgbYddYjwktkjl);
    }
}
