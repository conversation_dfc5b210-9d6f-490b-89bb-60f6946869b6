package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 金蝶应收单场地费+对账数据记录
 * @Author: jeecg-boot
 * @Date:   2022-04-20
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_jd_ysd_cdfjl")
public class WlgbJdYsdCdfjl {

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**到账日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date dzrq;
	/**订单编号*/
    private java.lang.String ddbh;
	/**编号*/
    private java.lang.String bh;
	/**门店*/
    private java.lang.String md;
	/**门店id*/
    private java.lang.String mdid;
	/**城市*/
    private java.lang.String city;
	/**业务员*/
    private java.lang.String ywy;
	/**退场时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date tcsj;
	/**场次*/
    private java.lang.String cc;
	/**场数*/
    private java.lang.Integer ccnum;
	/**优惠后场地费*/
    private java.lang.Double yhhcdf;
	/**场地费原价*/
    private java.lang.Double cdfyj;
	/**尾款*/
    private java.lang.Double wk;
	/**电费*/
    private java.lang.Double df;
	/**餐饮收入*/
    private java.lang.Double cy;
	/**转回公司商品金额*/
    private java.lang.Double zhgsspje;
	/**策划收入*/
    private java.lang.Double ch;
	/**增值服务收入*/
    private java.lang.Double zzfw;
	/**其他收入*/
    private java.lang.Double qt;
	/**收入*/
    private java.lang.Double sr;
	/**优惠支出*/
    private java.lang.Double yhzc;
	/**盈余*/
    private java.lang.Double yy;
	/**增值服务支出*/
    private java.lang.Double zzfwzc;
	/**备注*/
    private java.lang.String bz;
	/**线下定金*/
    private java.lang.Double xxdj;
	/**线上定金*/
    private java.lang.Double xsdj;
	/**店长奖金池*/
    private java.lang.Double dzjjc;
	/**公司应收*/
    private java.lang.Double gsys;
	/**值班店长*/
    private java.lang.String zbdz;
	/**是否周末*/
    private java.lang.String sfzm;
	/**券码*/
    private java.lang.String fkm;
	/**补交券码*/
    private java.lang.String bjfkm;
	/**轰趴师费用*/
    private java.lang.Double hps;
	/**剧本杀费用*/
    private java.lang.Double jbs;
	/**保险费用*/
    private java.lang.Double bx;
	/**真人CS费用*/
    private java.lang.Double cs;
	/**厨房使用费*/
    private java.lang.Double cfsy;
	/**卫生费*/
    private java.lang.Double ws;
	/**赔偿费*/
    private java.lang.Double pc;
	/**人头费*/
    private java.lang.Double rt;
	/**延时费*/
    private java.lang.Double ys;
	/**代购费*/
    private java.lang.Double dg;
	/**其他费用*/
    private java.lang.Double qtf;
	/**是否录入金蝶（1：是；0：否）*/
    private java.lang.Integer sflrjd;
	/**是否删除（1：是；0：否）*/
    private java.lang.Integer sfsc;
	/**账套编码*/
    private java.lang.String ztbm;


}
