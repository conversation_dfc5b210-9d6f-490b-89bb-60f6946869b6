package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbJdSpCwlrjdsj;
import com.wlgb.mapper.WlgbJdSpCwlrjdsjMapper;
import com.wlgb.service.WlgbJdSpCwlrjdsjService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 21:20
 */
@Service
public class WlgbJdSpCwlrjdsjServiceImpl implements WlgbJdSpCwlrjdsjService {
    @Resource
    private WlgbJdSpCwlrjdsjMapper wlgbJdSpCwlrjdsjMapper;

    @Override
    public void save(WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj) {
        wlgbJdSpCwlrjdsj.setId(IdConfig.uuId());
        wlgbJdSpCwlrjdsj.setCreateTime(new Date());
        wlgbJdSpCwlrjdsjMapper.insertSelective(wlgbJdSpCwlrjdsj);
    }

    @Override
    public void updateById(WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj) {
        wlgbJdSpCwlrjdsj.setUpdateTime(new Date());
        wlgbJdSpCwlrjdsjMapper.updateByPrimaryKeySelective(wlgbJdSpCwlrjdsj);
    }

    @Override
    public List<WlgbJdSpCwlrjdsj> queryByTypeAndSpBhAndSfScJd(Integer type, String spbh, Integer sfScJd) {
        Example example = new Example(WlgbJdSpCwlrjdsj.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("type", type);
        criteria.andEqualTo("spbh", spbh);
        criteria.andEqualTo("sfscjd", sfScJd);
        return wlgbJdSpCwlrjdsjMapper.selectByExample(example);
    }

    @Override
    public List<WlgbJdSpCwlrjdsj> queryListByWlgbJdSpCwlrjdsj(WlgbJdSpCwlrjdsj wlgbJdSpCwlrjdsj) {
        return wlgbJdSpCwlrjdsjMapper.select(wlgbJdSpCwlrjdsj);
    }

    @Override
    public List<WlgbJdSpCwlrjdsj> queryByTypeAndSpBhAndSfScJdAndBm(Integer type, String spbh, Integer sfScJd, String bm) {
        Example example = new Example(WlgbJdSpCwlrjdsj.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("type", type);
        criteria.andEqualTo("spbh", spbh);
        criteria.andEqualTo("sfscjd", sfScJd);
        criteria.andEqualTo("ztbm", bm);
        return wlgbJdSpCwlrjdsjMapper.selectByExample(example);
    }
}
