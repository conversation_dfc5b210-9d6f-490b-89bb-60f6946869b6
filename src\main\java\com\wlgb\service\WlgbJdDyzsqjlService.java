package com.wlgb.service;

import com.wlgb.entity.WlgbJdDyzsqjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 22:42
 */
public interface WlgbJdDyzsqjlService {
    void save(WlgbJdDyzsqjl wlgbJdDyzsqjl);

    void updateById(WlgbJdDyzsqjl wlgbJdDyzsqjl);

    WlgbJdDyzsqjl queryBySpBhAndSfLrJd(String spbh, Integer sfLrJd);

    List<WlgbJdDyzsqjl> queryListWlgbJdDyzsqjl(WlgbJdDyzsqjl wlgbJdDyzsqjl);
}
