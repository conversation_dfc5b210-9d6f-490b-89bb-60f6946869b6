<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>微信支付</title>
    <script typet="text/javascript" src="js/jquery-3.5.1.js"></script>
    <script type="text/javascript" src="js/wx.js"></script>
</head>
<body>
<div align="center">
    <img src="https://tianshu-vpc.oss-cn-shanghai.aliyuncs.com/dd0dcc5c-bc11-4d3d-be99-ea7e989b76b2.jpg">
</div>
<script>
    $(function () {
		$.ajax({
			method: 'get',
			url: 'https://yinshengdj.qianquan888.com/ysdjtb/wlgb/api/fwqTest',
			success: function (result) {
			    if(result.success){
                    pay()
                }
			},
            error:function (result) {
                alert("服务器繁忙，请稍后再试");
            }
		})
    })

    function pay() {
        if (typeof WeixinJSBridge == "undefined") {
            if (document.addEventListener) {
                document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
            } else if (document.attachEvent) {
                document.attachEvent('WeixinJSBridgeReady', onBridgeReady);
                document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
            }
        } else {
            onBridgeReady();
        }
    }

    function GetQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        console.log(window.location.search)//?id=2
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    function onBridgeReady() {
        // alert(GetQueryString("package"))
        WeixinJSBridge.invoke('getBrandWCPayRequest', {
            "appId": GetQueryString("appId"), //公众号名称，由商户传入
            "timeStamp": GetQueryString("timeStamp"), //时间戳 这里随意使用了一个值
            "nonceStr": GetQueryString("nonceStr"), //随机串
            "package": GetQueryString("package"),
            "signType": "RSA", //签名方式:RSA
            "paySign": GetQueryString("paySign") //微信签名
        }, function (res) {
            if (res.err_msg === "get_brand_wcpay_request:ok") {
                alert("支付成功");
            } else if (res.err_msg === "get_brand_wcpay_request:cancel") {
                alert("用户取消支付");
            } else if (res.err_msg === "get_brand_wcpay_request:fail") {
                alert("支付失败");
            }
        });
    }
</script>
</body>
</html>
