package com.wlgb.service.impl;

import com.wlgb.config.IdConfig;
import com.wlgb.entity.WlgbXsdjbjjl;
import com.wlgb.mapper.WlgbXsdjbjjlMapper;
import com.wlgb.service.WlgbXsdjbjjlService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月14日 2:08
 */
@Service
public class WlgbXsdjbjjlServiceImpl implements WlgbXsdjbjjlService {
    @Resource
    private WlgbXsdjbjjlMapper wlgbXsdjbjjlMapper;

    @Override
    public void save(WlgbXsdjbjjl wlgbXsdjbjjl) {
        wlgbXsdjbjjl.setId(IdConfig.uuId());
        wlgbXsdjbjjl.setCreateTime(new Date());
        wlgbXsdjbjjlMapper.insertSelective(wlgbXsdjbjjl);
    }

    @Override
    public void updateById(WlgbXsdjbjjl wlgbXsdjbjjl) {
        wlgbXsdjbjjl.setUpdateTime(new Date());
        wlgbXsdjbjjlMapper.updateByPrimaryKeySelective(wlgbXsdjbjjl);
    }

    @Override
    public List<WlgbXsdjbjjl> queryByXidAndSfSc(String xid) {
        Example example = new Example(WlgbXsdjbjjl.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xid", xid);
        criteria.andEqualTo("sfsc", 0);
        return wlgbXsdjbjjlMapper.selectByExample(example);
    }
}
