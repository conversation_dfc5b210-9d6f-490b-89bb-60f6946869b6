package com.wlgb.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wlgb.entity.TbQrd;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/03/19 13:51
 */
@Data
public class TbQrdVo extends TbQrd {
    private java.lang.String xid;
    /**别墅名称（ID）*/
    private java.lang.String xbsmc;
    /**进场时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date xjctime;
    /**场次 0:白场、1：夜场*/
    private java.lang.Integer xjcflag;
    /**退场时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date xtctime;
    /**场次 0:白场、1：夜场*/
    private java.lang.Integer xtcflag;
    /**房东姓名*/
    private java.lang.String xfd;
    /**房东联系方式*/
    private java.lang.String xfddh;
    /**租客姓名*/
    private java.lang.String xzk;
    /**租客联系方式*/
    private java.lang.String xzkdh;
    /**租客身份证*/
    private java.lang.String xzksfz;
    /**团队性质 0：学生团体、1：农家乐、2：公司团建、3：同学聚会、4：订婚、5：生日、6：其余*/
    private java.lang.String xtdxz;
    /**单位名称*/
    private java.lang.String xdwmc;
    /**客户来源 0：美团、 1:老客户、2:其他*/
    private java.lang.String xkhly;
    /**人数*/
    private java.lang.Integer xrs;
    /**超出单人费用*/
    private java.lang.Double xcudrfy;
    /**已收定金*/
    private java.lang.Double xysdj;
    /**全款租金*/
    private java.lang.Double xqkzj;
    /**转账时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    private java.util.Date xzzsj;
    /**转账编号后俩位*/
    private java.lang.String xzzhlw;
    /**最低消费人数*/
    private java.lang.String xzdxxrs;
    /**增值预算*/
    private java.lang.Integer xzzys;
    /**付款码*/
    private java.lang.String xfkm;
    /**包餐、订餐*/
    private java.lang.String xbdc;
    /**包/订餐金额*/
    private java.lang.Double xbdje;
    /**包/订餐数量*/
    private java.lang.Integer xbdsl;
    /**烧烤套餐金额*/
    private java.lang.Double xskje;
    /**烧烤套餐数量*/
    private java.lang.Integer xsksl;
    /**主题策划金额*/
    private java.lang.Double xztje;
    /**主题策划数量*/
    private java.lang.Integer xztsl;
    /**是否现场成交 0:是、1：否*/
    private java.lang.Integer xisxzcj;
    /**成交人*/
    private java.lang.String xcjr;
    /**转发条数*/
    private java.lang.Integer xzfts;
    /**好评条数*/
    private java.lang.Integer xhpts;
    /**恢复原价*/
    private java.lang.Double xhfyj;
    /**事项备注*/
    private java.lang.String xsxbz;
    /**订单状态 0:未完成、1：已完成*/
    private java.lang.Integer xstatu;
    /**发送时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date xsendtime;
    /**发送人ID*/
    private java.lang.String xsendder;
    /**店长ID*/
    private java.lang.String xdzid;
    /**订单编号*/
    private java.lang.String xddbh;
    /**赠送会员卡数量*/
    private java.lang.String xzshyksl;
    /**是否确认跟单 0未跟单、1：已跟单*/
    private java.lang.String xisqrgd;
    /**省份*/
    private java.lang.String cshenfen;
    /**公司*/
    private java.lang.String company;
    /**分区*/
    private java.lang.String cfenqu;
    /**协议单电子回执地址*/
    private java.lang.String xpdfpath;
    /**协议单图片地址*/
    private java.lang.String ximagepath;
    /**订单类型*/
    private java.lang.String xddtype;
    /**定金类型*/
    private java.lang.String xdjtype;
    /**是否开发票 0:否、1：是*/
    private java.lang.String xiskfp;
    /**修改人ID*/
    private java.lang.String xxgzid;
    /**修改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date xgsj;
    /**0:否、1:是  是否允许协调*/
    private java.lang.Integer xyxxt;
    /**场次间隔*/
    private java.lang.Integer xccjg;
    /**代码*/
    private java.lang.String xdm;
    /** 是否特殊场次  0：否，1：是*/
    private java.lang.Integer sftscc;
    /**是否需要轰趴师 0：否，1：是*/
    private java.lang.Integer xsfhps;
    /**线上定金*/
    private java.lang.Double xxsdj;
    /**线下定金*/
    private java.lang.Double xxxdj;
    /**订餐总额*/
    private java.lang.Double xdcze;
    /**烧烤总额*/
    private java.lang.Double xskze;
    /**主题总额*/
    private java.lang.Double xztze;
    /**老板（甲方）姓名*/
    private java.lang.String xlbjf;
    /**撞单处理情况 0、无1、双方平分业绩2、双方业绩等同*/
    private java.lang.Integer xzdcl;
    /**验劵人*/
    private java.lang.String xyqr;
    /**补交定金*/
    private java.lang.Double xbjdj;
    /**补交信息*/
    private java.lang.String xbjxx;
    /**是否对账 0否 1是*/
    private java.lang.String xisdz;
    /**校代姓名*/
    private java.lang.String xxdxm;
    /**是否删除 0否1是*/
    private java.lang.String xsfsc;
    /**线上租金*/
    private java.lang.Double xxszj;
    /**删除时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date xscsj;
    /**删除者*/
    private java.lang.String xscz;
    /**删除原因 1订单延期，2 取消退定金 ，3 取消不退定金 ，4 删除订单，5 超时未下单*/
    private java.lang.String xscyy;
    /**修改或协调状态 1修改 2协调*/
    private java.lang.String xxgorxt;
    /**轰趴师费用*/
    private java.lang.Double xhpsfy;
    /**是否需要真人CS 0：否，1：是*/
    private java.lang.Integer xsfzrcs;
    /**真人CS费用*/
    private java.lang.Double xzrcsfy;
    /**CS教官*/
    private java.lang.String xzrcsjg;
    /**真人CS人数*/
    private java.lang.Integer xzrcsrs;
    /**轰趴师费用明细*/
    private java.lang.String xhpsfymx;
    /**电话编码*/
    private java.lang.String xdhbm;
    /**策划经理*/
    private java.lang.String xchjl;
    /**轰趴师姓名*/
    private java.lang.String xhpsxm;
    /**轰趴师评价*/
    private java.lang.String xhpspj;
    /**补交定金类型（）*/
    private java.lang.String xbjdjlx;
    /**线下补交定金*/
    private java.lang.Double xxxbj;
    /**线上补交定金*/
    private java.lang.Double xxsbj;
    /**已收增值定金*/
    private java.lang.String xyszzdj;
    /**增值费用补交*/
    private java.lang.Double xzzfybj;
    /**场地租金补交*/
    private java.lang.Double xcdzjbj;
    /**轰趴师成交人*/
    private java.lang.String xhpscjr;
    /**订单类型 (0：未知 1：工作日 2：周末 3：跨场)*/
    private java.lang.String xddlxxz;
    /**集赞条数*/
    private java.lang.Integer xjzts;
    /**策划成交人*/
    private java.lang.String xchcjr;
    /**是否后续成交 0:是、1：否*/
    private java.lang.Integer xsfhxgjcj;
    /**成交人*/
    private java.lang.String xhxcjr;
    /**占据场次*/
    private java.lang.Integer xzjcc;
    /**校代首单 0：是 1：不是 3：非校代*/
    private java.lang.Integer xxdsd;
    /**校代电话*/
    private java.lang.String xxddh;
    /**校代上级姓名*/
    private java.lang.String xxdsjxm;
    /**是否需要保险(0：否 1：是）*/
    private java.lang.String xsfbx;
    /**人数*/
    private java.lang.Integer xbxrs;
    /**保险单价(0:5 1:10 2:15 3:20 4:0)*/
    private java.lang.Integer xbxdj;
    /**保险总金额*/
    private java.lang.Double xbxzje;
    /**校代所属城市*/
    private java.lang.String xxdsscs;
    /**保险支出*/
    private java.lang.Double xbxzc;
    /**保险成交人*/
    private java.lang.String xbxcjr;
    /**保险补交时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date xbxbjsj;
    /**保险补交金额*/
    private java.lang.Double xbxbjje;
    /**客户来源  0:其他 1：小程序*/
    private java.lang.String xkhxdly;
    /**客户完成状态 0:下单已完成 电脑下单  1：下单完成小程序下单   2:小程序未完成 3:钉钉小程序预留单*/
    private java.lang.String xkhwczt;
    /**真人CS成交人*/
    private java.lang.String xzrcscjr;
    /**订餐成交人*/
    private java.lang.String xdccjr;
    /**烧烤成交人*/
    private java.lang.String xskcjr;
    /**轰趴师支出工资*/
    private java.lang.Double xhpszcgz;
    /**轰趴师支出星级*/
    private java.lang.String xhpsxj;
    /**轰趴师支出其他费用*/
    private java.lang.Double xhpszcqt;
    /**轰趴师支出备注*/
    private java.lang.String xhpsbz;
    /** //小商品优惠券金额*/
    private java.lang.Double xspyhq;
    /**是否已经播报了数据 0：未播报  1：已播报*/
    private java.lang.String xsfbb;
    /**套餐选择：1：保险、2：烧烤、3：订餐、4：轰趴、5：真人CS*/
    private java.lang.String xtcxz;
    /**客户提交时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date xkhtjtime;
    /**房东所属部门（ 1：一部:2：二部:3：综合部）*/
    private java.lang.String xfdssbm;
    /**发送时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date xsendtime1;
    /**策划辅助成交人*/
    private java.lang.String xchfzcjr;
    /**订餐辅助成交人*/
    private java.lang.String xdcfzcjr;
    /**烧烤辅助成交人*/
    private java.lang.String xskfzcjr;
    /**平台入住时间*/
    private java.lang.String xptsjrzsj;
    /**房东工号*/
    private java.lang.String xfdgh;
    /**校代上级工号*/
    private java.lang.String xxdsjxmgh;
    /**保险成交人工号*/
    private java.lang.String xbxcjrgh;
    /**策划成交人工号*/
    private java.lang.String xchcjrgh;
    /**轰趴师成交人工号*/
    private java.lang.String xhpscjrgh;
    /**订餐成交人工号*/
    private java.lang.String xdccjrgh;
    /**烧烤成交人工号*/
    private java.lang.String xskcjrgh;
    /**现场成交人工号*/
    private java.lang.String xcjrgh;
    /**后续成交人工号*/
    private java.lang.String xhxcjrgh;
    /**退定金 0：未退定金  1：已退订金 2：未处理*/
    private java.lang.String xtdj;
    /**平台详情*/
    private java.lang.String xptxx;
    /**订单状态：0:已下单;1:下单及跟单;2:二级跟单;3:进场;4:巡场服务;5:退场;6:客户确认;7:对账;8:客访(已完成);9:已转账;10:对账员已确认;11:已收尾款*/
    private java.lang.Integer status;
    /**倒计时*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date timeOut;
    /**策划经理id*/
    private java.lang.String xchjlid;
    /**是否退还预收金(0:否1:是)*/
    private java.lang.Integer sfthysj;
    /**客户反馈状态(0:未回访1:生成二维码2:已回访未反馈3:已反馈)*/
    private java.lang.Integer khhfzt;
    /**是否完成营销回访（0:否，1:是）*/
    private java.lang.String sfwcyxhf;
    /**咨询id*/
    private java.lang.Integer qqid;
    /**客户是否反馈(0:否1:是)*/
    private java.lang.Integer khsffk;
    /**是否申请开票(0:否，1:是)*/
    private java.lang.Integer sfsqkp;
}
