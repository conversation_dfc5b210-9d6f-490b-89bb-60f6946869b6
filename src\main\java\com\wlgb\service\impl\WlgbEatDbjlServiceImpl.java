package com.wlgb.service.impl;

import com.wlgb.entity.WlgbEatDbjl;
import com.wlgb.entity.WlgbEatRkjl;
import com.wlgb.mapper.WlgbEatDbjlMapper;
import com.wlgb.mapper.WlgbEatRkjlMapper;
import com.wlgb.service.WlgbEatDbjlService;
import com.wlgb.service.WlgbEatRkjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class WlgbEatDbjlServiceImpl implements WlgbEatDbjlService {
    @Resource
    private WlgbEatDbjlMapper wlgbEatDbjlMapper;

    @Override
    public void save(WlgbEatDbjl wlgbEatDbjl) {
        wlgbEatDbjl.setCreateTime(new Date());
        wlgbEatDbjlMapper.insertSelective(wlgbEatDbjl);
    }

    @Override
    public void clearData(String lsh) {
        WlgbEatDbjl wlgbEatDbjl = new WlgbEatDbjl();
        wlgbEatDbjl.setLsh(lsh);
        wlgbEatDbjl.setSfsc(0);
        wlgbEatDbjlMapper.delete(wlgbEatDbjl);
    }


}
