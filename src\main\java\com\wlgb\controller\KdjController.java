package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taobao.api.ApiException;
import com.wlgb.config.*;
import com.wlgb.entity.*;
import com.wlgb.service.TbBslshService;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.WeiLianService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27 13:04
 */
@RestController
@RequestMapping(value = "/wlgb/kdj")
public class KdjController {
    @Autowired
    private WeiLianService weiLianService;
    @Autowired
    private TbBslshService tbBslshService;
    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;


    /**
     * 客单价-客单价的别墅下拉
     * 根据传入的时间查询该时间段内的客单价所有门店下拉列表。参数：进场时间jcsj，退场时间tcsj
     */
    @RequestMapping(value = "queryKdjVilla")
    public Result queryKdjVilla(HttpServletRequest request) {
        String jcsj = request.getParameter("jcsj");
        String xz = request.getParameter("xz");
        if (jcsj == null || "".equals(jcsj)) {
            return Result.error("没有进场时间");
        }
        String tcsj = request.getParameter("tcsj");
        if (tcsj == null || "".equals(tcsj)) {
            return Result.error("没有退场时间");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("jcsj", jcsj);
        map.put("tcsj", tcsj);
        map.put("xz", xz);
        List<Select> list = weiLianService.queryKdjVilla(map);

        return Result.OK(list);
    }

    /**
     * 查询客单价详情
     */
    @RequestMapping(value = "queryKdjFdZdj")
    public Result queryKdjFdZdj(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        JSONArray jsonArray = jsonObject.getJSONArray("bsList");
        List<TbBslsh> list4 = new ArrayList<>();
        JSONObject sj = jsonObject.getJSONObject("sj");
        String start = sj.getString("start");
        String end = sj.getString("end");
        jsonArray.forEach(l -> {
            TbBslsh tbBslsh = new TbBslsh();
            tbBslsh.setVid(l != null ? l.toString() : "");
            list4.add(tbBslsh);
        });
        tbBslshService.saveBatch(list4);
        weiLianDdXcxService.zxKdjSj(start, end);
        List<Map<String, Object>> list = weiLianDdXcxService.queryKdjZsZdj();
        List<Map<String, Object>> list1 = weiLianDdXcxService.queryBsHzbZdj();
        List<Map<String, Object>> list2 = weiLianDdXcxService.queryRqZzbZdj();
        Map<String, Object> map = new HashMap<>();
        map.put("data", list);
        map.put("hzb", list1);
        map.put("zzb", list2);

        return Result.OK(map);
    }

    /**
     * 查询客单价民宿详情
     */
    @RequestMapping(value = "queryKdjFdZdjTs")
    public Result queryKdjFdZdjTs(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        JSONObject jsonObject = JSONObject.parseObject(datas);
        JSONArray jsonArray = jsonObject.getJSONArray("bsList");
        List<TbBslsh> list4 = new ArrayList<>();
        JSONObject sj = jsonObject.getJSONObject("sj");
        String start = sj.getString("start");
        String end = sj.getString("end");
        jsonArray.forEach(l -> {
            TbBslsh tbBslsh = new TbBslsh();
            tbBslsh.setVid(l != null ? l.toString() : "");
            list4.add(tbBslsh);
        });
        tbBslshService.saveBatch(list4);
        weiLianDdXcxService.zxKdjSjMs(start, end);
        List<Map<String, Object>> list = weiLianDdXcxService.queryKdjZsZdj();
        List<Map<String, Object>> list1 = weiLianDdXcxService.queryBsHzbZdj();
        List<Map<String, Object>> list2 = weiLianDdXcxService.queryRqZzbZdj();
        Map<String, Object> map = new HashMap<>();
        map.put("data", list);
        map.put("hzb", list1);
        map.put("zzb", list2);

        return Result.OK(map);
    }

    /**
     * 客单价数据查询
     */
    @RequestMapping("/KdjYsj")
    public Result KdjYsj(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (!(Optional.ofNullable(datas).isPresent())) {
            return Result.OK("datas为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(datas);
        String bs = jsonObject.getString("bs");
        if (!(Optional.ofNullable(bs).isPresent())) {
            return Result.OK("别墅为空!", false);
        }
        JSONObject sj = JSONObject.parseObject(jsonObject.getString("sj"));
        String jctime = sj.getString("jctime");
        String tctime = sj.getString("tctime");


        Map<String, Object> map = new HashMap<>();
        map.put("bs", bs);
        map.put("start", jctime);
        map.put("end", tctime);
        TbKdj map1 = weiLianService.queryTbKdjByBsAndJcAndTc(map);
//        System.out.println(map1);
        return Result.OK(map1);
    }

    /**
     * 流程申请修改底价
     */
    @RequestMapping("kdjSqXgJe")
    public Result kdjSqXgJe(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("userid");


        JSONObject jsonObject = JSONObject.parseObject(datas);
        if (jsonObject == null || jsonObject.size() == 0) {
            return Result.error("数据是空的！");
        }
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userid", userid);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/kdjSqXgJeTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }


        return Result.OK();
    }

    /**
     * 跨场客单价修改审批提交
     */
    @RequestMapping(value = "kdjSpXg")
    public Result kdjSpXg(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        if (datas == null) {
            return Result.error("datas为空");
        }
        String userid = request.getParameter("userid");
        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userid", userid);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/kdjSpXgTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }


    /**
     * 提交底价上传和修改
     */
    @RequestMapping(value = "tjDj")
    public Result tjDj(HttpServletRequest request) {
        String datas = request.getParameter("datas");
        String userid = request.getParameter("userid");
        if (datas == null) {
            return Result.error("数据空的！");
        }

        //调用阿里云函数计算的异步函数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("datas", datas);
        paramMap.put("userid", userid);
        paramMap.put("uuid", IdConfig.uuId());
        System.out.println("uuid====================" + paramMap.get("uuid"));
        //在这里进行异步调用函数计算里面的函数即可
        try {
            HttpClientUtil.postAsync(ddxturl + "/ysdjtb/wlgb/task/tjDjTask", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.OK();
    }

    /**
     * 客单价批量调整提交
     */
    @RequestMapping(value = "kdjPlXg")
    public Result kdjPlXg(HttpServletRequest request) {
        String data = request.getParameter("data");
        String userId = request.getParameter("userId");
        if (data == null || "".equals(data)) {
            return Result.error("data空的");
        }
        JSONObject jsonObject = JSONObject.parseObject(data);
        JSONArray jsonArray = jsonObject.getJSONArray("bs");
        List<TbBslsh> list4 = new ArrayList<>();
        jsonArray.forEach(l -> {
            TbBslsh tbBslsh = new TbBslsh();
            tbBslsh.setVid(l != null ? l.toString() : "");
            list4.add(tbBslsh);
        });
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        tbBslshService.saveBatch(list4);
        String type = jsonObject.getString("bbxz");
        String ms = jsonObject.getString("ms");
        DingdingEmployee ding = weiLianDdXcxService.queryDingdingEmployeeByUserId(userId);
        Map<String, Object> map = new HashMap<>();
        map.put("jcsj", jsonObject.getString("jcsj"));
        map.put("tcsj", jsonObject.getString("tcsj"));
        map.put("je", jsonObject.getDouble("je"));
        map.put("type", type);
        map.put("name", ding.getName());
        map.put("userId", userId);
        map.put("ms", ms);
        Map<String, Object> map1 = weiLianService.zxKdjPlXg(map);
        Integer sum = map1.get("sum") != null ? (Integer) map1.get("sum") : 0;
        Integer sum1 = map1.get("sum1") != null ? (Integer) map1.get("sum1") : 0;
        Integer sum2 = map1.get("sum2") != null ? (Integer) map1.get("sum2") : 0;

        String context = "你提交的客单价批量调整已经完成";
        context += "\n\n修改类型：" + ("0".equals(type) ? "全国全部修改" : "部分别墅修改");
        context += "\n调整模式：" + ("1".equals(ms) ? "升" : "-1".equals(ms) ? "降" : "整体调整");
        context += "\n调整金额：" + (jsonObject.getDouble("je") != null ? jsonObject.getDouble("je") : 0);
        context += "\n时间范围：" + jsonObject.getString("jcsj") + "至" + jsonObject.getString("tcsj");
        context += "\n总客单价数量：" + sum;
        int ddSl = sum - sum1;
        if (ddSl > 0) {
            context += "\n含有订单数量：" + ddSl;
        }
        int bng = sum1 - sum2;
        if (bng > 0) {
            context += "\n不能修改数量（修改之后金额会变成小于0）：" + bng;
        }
        context += "\n修改客单价数量：" + sum2;
        context += "\n\n送达时间：" + df2.format(new Date());
        Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
        try {
            DingDBConfig.sendGztzText(dingkey, userId, context);
        } catch (ApiException e) {
            e.printStackTrace();
        }

        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("sum", sum);
        jsonObject1.put("ddSl", ddSl);
        jsonObject1.put("bng", bng);
        jsonObject1.put("xgSl", sum2);


        return Result.OK(jsonObject1);
    }
}
