package com.wlgb.controller.beike;

import com.alibaba.excel.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.wlgb.entity.beike.FwqHouse;
import com.wlgb.service.OssFileService;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.FwqHouseService;
import com.wlgb.service2.FwqSmileService;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.internal.StringUtil;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static sun.management.ConnectorAddressLink.export;

/**
 * <AUTHOR>
 * @Date 2025/02/02 18:30
 * @Version 1.0
 */
@RestController
@Slf4j
@RequestMapping(value = "/wlgb/beike")
public class FwqHouseController {
    @Autowired
    private FwqHouseService fwqHouseService;
    @Value("${hanshujisuan.ddxturl}")
    private String ddxturl;


    @RequestMapping(value = "getbeike")
    public void getbeike() throws IOException, InterruptedException {
        // 登录后的Cookies
        Map<String, String> cookies = new HashMap<>();
        cookies.put("Cookie", "lianjia_uuid=b18f620b-69b4-432a-a7bc-85ccec66eafb; digv_extends=%7B%22utmTrackId%22%3A%22%22%7D; crosSdkDT2019DeviceId=88yxl3--chkhq4-f7cuje09cny3rj5-y0qnfu6st; ftkrc_=ffd89e44-91e6-4c07-944b-b19c954187da; lfrc_=9538d9b2-d47a-488d-96e9-f7e2d331922b; Hm_lvt_b160d5571570fd63c347b9d4ab5ca610=**********; HMACCOUNT=F05DA9F1E059AC10; _ga=GA1.2.**********.**********; _gid=GA1.2.*********.**********; _jzqc=1; _jzqckmp=1; _qzjc=1; ke_uuid=b0d5b8b44b8994708c72ce5fca53b9c2; select_city=430100; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22194c1e17698c9e-069584e3dae5e8-********-2073600-194c1e176991493%22%2C%22%24device_id%22%3A%22194c1e17698c9e-069584e3dae5e8-********-2073600-194c1e176991493%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%7D; lianjia_ssid=3e806202-408e-4e53-b073-e25380950d97; _jzqa=1.2134173227675798000.**********.**********.**********.3; _jzqx=1.**********.**********.1.jzqsr=cs%2Efang%2Eke%2Ecom|jzqct=/.-; login_ucid=****************; lianjia_token=2.001583832375867385042eaa12e33a0be0; lianjia_token_secure=2.001583832375867385042eaa12e33a0be0; security_ticket=epIyzL4Z9J3nU5LGtqiJJf8UZLgcoZmVoW/4AR+G9dYHuW4qrSBEdiaCROjwEg2RRtNY0ZiY+abYvcRwHZiUWelqqykiTid/JVRMH30BzuN9S/cBOAFv6sc7/lBx2UtUk1P9mDuFJ2jHlMcNheFlcbSqDnpjTCIKoEXVkGAz0ck=; _gat_global=1; _gat_new_global=1; _gat_dianpu_agent=1; Hm_lpvt_b160d5571570fd63c347b9d4ab5ca610=1738428275; digData=%7B%22key%22%3A%22loupan_index%22%7D; _qzja=1.1586804522.1738419261523.1738419261523.1738427869778.1738427869778.1738428274998.********.2; _qzjb=1.1738427869778.*******; _qzjto=2.1.0; _jzqb=1.2.10.**********.1; lj_newh_session=eyJpdiI6IlFNWUlnNmx5ZEk2akF6eWRwNFRnSEE9PSIsInZhbHVlIjoiSnhDS3RcL2VTR2RGWjhUWFwvTTVTa0Z1NUtDMDlLQ2xzTk1jSWZUM2JwTGY3NFRXQmVLNTlNQndET292THBmYVVXUlU4ZFwvNkhSTWpkbWxhMFpsSll0eVE9PSIsIm1hYyI6IjE2NTg2ZjI5MTk4YjhjNTY2ZmExNjg3NWE0Mjg3ZmI5NmVhODE4NWQ0OWIzNWY1M2NkZjRkYzc4MGY3ZTNiOTUifQ%3D%3D; srcid=eyJ0IjoiXCJ7XFxcImRhdGFcXFwiOlxcXCIwM2I5NmQ4YzA3NjA4MDY2MTlkZDRiODE0OGU5NzA1NTY0ZTIxMzVjODNlOTU4MjQxNTNhZDFhMGQxMDMzNDZhNDE3MjE1M2M0NWViZWY1Njg4N2U2NDM2NzhlZjZjZGY4NTc5M2I5NjZmNTE5YjE2OGRiMTdkNWViOTQzMzNiNjAyNWZhNjM5NThhMTljMTBjMTM5Njg0NzU1ZWIyY2M4MDBhNzA5YjZlYjQzODE4NmI1MWViMDJjOGIyYmUwNjFiODQ4ZDYxNzRkMzJlZDMxZDBiMDY3ZmE0ZjJhYWZkN2MxMDVhZTRhYmMzN2I5M2U4YTI0MWRjNWFmMmQyNjcwYWE0OGYyMDdiMmZhYWVkMzQ2YjVhMTM5YmIzN2E2YTY3OWUwMmU0OWYwZmJiNzMzM2QyOGVmYzcxZDEyMmIzZlxcXCIsXFxcImtleV9pZFxcXCI6XFxcIjFcXFwiLFxcXCJzaWduXFxcIjpcXFwiOWI4ZDUwNmZcXFwifVwiIiwiciI6Imh0dHBzOi8vY3MuZmFuZy5rZS5jb20vbG91cGFuL3BnNS8iLCJvcyI6IndlYiIsInYiOiIwLjEifQ==");
        int pageSize = 10;
        List<FwqHouse> dataList = Lists.newArrayList();
        // 贝壳找房长撒谎区域网址
        String beikeUrl = "https://cs.fang.ke.com";
        // 贝壳找房长沙市楼盘展示页地址
        String loupanUrl = "https://cs.fang.ke.com/loupan/pg";
        // 用Jsoup抓取该地址完整网页信息
        Document doc = Jsoup.connect(loupanUrl + 1).get();
        // 分页容器
        Element pageContainer = doc.select("div.page-box").first();
        if (pageContainer == null) {
            return;
        }
        // 楼盘总数
        int totalCount = Integer.parseInt(pageContainer.attr("data-total-count"));
        // 分页执行
        for (int i = 0; i < totalCount / pageSize; i++) {
            log.info("当前页面是 {}", (i + 1));
            // 贝壳网有人机认证，不能短时间频繁访问，每次翻页都让线程休眠10s
            Thread.sleep(8000);
            Connection connection = Jsoup.connect(loupanUrl + (i + 1));
            connection.cookies(cookies);
            doc = connection.get();
            // 获取楼盘列表的ul元素
            Element list = doc.select("ul.resblock-list-wrapper").first();
            if (list == null) {
                System.out.println("需要登录");
                continue;
            }
            // 获取楼盘列表的li元素
            Elements elements = list.select("li.resblock-list");
            System.out.println(elements.size());
            elements.forEach(el -> {
                // 楼盘介绍
                Element introduce = el.child(0);
                // 详情页面
                String detailPageUrl = beikeUrl + introduce.attr("href");
                // 楼盘图片
                String imageUrl = introduce.select("img").attr("data-original");
                // 楼盘详情
                Element childDesc = el.select("div.resblock-desc-wrapper").first();
                Element childName = childDesc.child(0);
                // 楼盘名称
                String title = childName.child(0).text();
                // 楼盘在售状态
                String status = childName.child(1).text();
                // 产权类型
                String propertyType = childName.child(2).text();
                // 楼盘所在地址
                String address = childDesc.child(1).text();
                // 房间属性
                Element room = childDesc.child(2);
                // 户型
                String houseType = "";

                // 户型集合
                Elements houseTypeSpans = room.getElementsByTag("span");
                if (!houseTypeSpans.isEmpty()) {
                    // 剔除文案：【户型：】
                    houseTypeSpans.remove(0);
                    // 剔除文案：【建面：xxx】
                    houseTypeSpans.remove(houseTypeSpans.size() - 1);
                    houseType = StringUtil.join(houseTypeSpans.stream().map(Element::text).collect(Collectors.toList()), "/");
                }

                // 建筑面积
                String buildingArea = room.select("span.area").text();

                // div - 标签
                Element descTag = childDesc.select("div.resblock-tag").first();
                Elements tagSpans = descTag.getElementsByTag("span");
                String tag = "";
                if (!tagSpans.isEmpty()) {
                    tag = StringUtil.join(tagSpans.stream().map(Element::text).collect(Collectors.toList()), " ");
                }

                // div - 价格
                Element descPrice = childDesc.select("div.resblock-price").first();
                String singlePrice = descPrice.select("span.number").text();
                String totalPrice = descPrice.select("div.second").text();

                FwqHouse fwqHouse = new FwqHouse().setTitle(title)
                        .setDetailpageurl(detailPageUrl)
                        .setImageurl(imageUrl)
                        .setSingleprice(singlePrice)
                        .setTotalprice(totalPrice)
                        .setStatus(status)
                        .setPropertytype(propertyType)
                        .setAddress(address)
                        .setHousetype(houseType)
                        .setBuildingarea(buildingArea)
                        .setTag(tag);

                fwqHouseService.save(fwqHouse);
                dataList.add(fwqHouse);
            });
        }
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("dataList is empty returned.");
            return;
        }
        log.info("dataList prepare finished, size = {}", dataList.size());

        // 调用导出逻辑，将数据导出到excel文件
//        export(pageTitle, dataList);

    }


}
