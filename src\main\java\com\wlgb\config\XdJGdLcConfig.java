package com.wlgb.config;

import com.wlgb.entity.TbVilla;
import com.wlgb.entity.TbXyd;
import com.wlgb.entity.WlgbDksljl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/01/09 16:47
 */
public class XdJGdLcConfig extends LcConfig{

    /**
     * 下单及跟单流程发送
     *
     * @param tbXyd1   协议单
     * @param villa    别墅
     */
    public static WlgbDksljl xdJGdLc(TbXyd tbXyd1,
                                     TbVilla villa,
                                     DingdingEmployee employee1,
                                     WlgbDksljl dksljl,
                                     Dingkey dingkey,
                                     DingdingEmployee employee2,
                                     String xydUrl,
                                     YdAppkey ydAppkey,
                                     YdBd ydBd) {
        SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (dksljl == null) {
            Map<String, Object> formDatamap = new HashMap<>();
            //电子协议单
            formDatamap.put("aimg", YdConfig.setTpList(xydUrl, "协议单图片"));
            //订单编号
            formDatamap.put("aid", tbXyd1.getXddbh());
            //协议单id
            formDatamap.put("xid", tbXyd1.getXid());
            //别墅名字
            formDatamap.put("bsmc", villa.getVname());
            //客户姓名
            formDatamap.put("xzk", tbXyd1.getXzk());
            //别墅id
            formDatamap.put("bsid", villa.getVid());
            //城市
            String city = villa.getCszq() != null ? !"".equals(villa.getCszq()) ? villa.getCszq() : villa.getCity() : villa.getCity();
            formDatamap.put("city", city);
            //运营部门
            formDatamap.put("DzSsBm", villa.getVbsssbm() != null ? villa.getVbsssbm().replace("(", "").replace(")", "") : "");
            //值班店长
            formDatamap.put("zbdz", villa.getPid());
            //进场时间
            formDatamap.put("jcsj", tbXyd1.getXjctime());
            //退场时间
            formDatamap.put("tcsj", tbXyd1.getXtctime());

            if (employee2 != null) {
                //流程执行人:改成值班店长ID
                formDatamap.put("xdjgdzxr", villa.getPid());
            }
            //性质（运营查数据）
            formDatamap.put("vxz", villa.getVxz());
            //当前流程的名字
            formDatamap.put("aname",
                    tbXyd1.getXfd() + "发起了'下单及跟单申请',进场:" + DateFormatConfig.df1(tbXyd1.getXjctime()) + ",退场:" + DateFormatConfig.df1(tbXyd1.getXtctime()) + ",别墅:" + villa.getVname() + ",请您立即处理");
            //客户电话
            formDatamap.put("xzkdh", tbXyd1.getXzkdh());
            //第一个参数改成房东ID
            GatewayResult gatewayResult = queryYdLcXzRc(tbXyd1,
                    "下单及跟单申请",
                    formDatamap,
                    "012412221639786136545",
                    dingkey,
                    ydAppkey,
                    ydBd);
            WlgbDksljl wlgbDksljl = new WlgbDksljl();
            if (gatewayResult != null) {
                wlgbDksljl.setSlid(gatewayResult.getResult());
            }
            wlgbDksljl.setBz("下单及跟单");
            wlgbDksljl.setId(IdConfig.uuId());
            wlgbDksljl.setTjsj(new Date());
            wlgbDksljl.setJsr(villa.getPid());
            wlgbDksljl.setDdid(tbXyd1.getXid());
            wlgbDksljl.setBsid(tbXyd1.getXbsmc());
            if (employee1 != null) {
                wlgbDksljl.setTjrid(employee1.getUserid());
                wlgbDksljl.setTjr(employee1.getName());
            }
            return wlgbDksljl;
        }
        return null;
    }
}
