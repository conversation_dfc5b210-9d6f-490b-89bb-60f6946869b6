package com.wlgb.service;

import com.wlgb.entity.WlgbHxyhYjWkFqsk;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/26 22:05
 */
public interface WlgbHxyhYjWkFqskService {
    void save(WlgbHxyhYjWkFqsk wlgbHxyhFqsk);

    void updateById(WlgbHxyhYjWkFqsk wlgbHxyhFqsk);

    WlgbHxyhYjWkFqsk queryByDdBhAndFkFs(String ddbh, String fkfs);

    Integer queryByDdBhAndZfZt(String ddbh, String zfzt);

    List<WlgbHxyhYjWkFqsk> queryByDdBhAndYsBh(String ddBh, String ysBh);
}
