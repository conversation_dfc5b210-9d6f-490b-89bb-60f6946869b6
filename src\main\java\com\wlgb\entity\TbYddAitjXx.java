package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "tb_ydd_aitj_xx")
public class TbYddAitjXx {
    @Id
    @KeySql(useGeneratedKeys = true)
    private int id;
    private String userid;
    private String uuid;
    private String tm;
    private String da;

}
