package com.wlgb.service;

import com.wlgb.entity.WlgbHxyhFqsk;

import java.util.List;

public interface WlgbHxyhFqskService {
    void save(WlgbHxyhFqsk wlgbHxyhFqsk);

    void updateById(WlgbHxyhFqsk wlgbHxyhFqsk);

    WlgbHxyhFqsk queryByDdBhAndFkFs(String ddbh, String fkfs);

    Integer queryByDdBhAndZfZt(String ddbh, String zfzt);

    List<WlgbHxyhFqsk> queryByDdBhAndYsBh(String ddBh, String ysBh);
}
