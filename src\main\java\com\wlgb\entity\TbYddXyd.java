package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/27 15:11
 */
@Table(name = "tb_ydd_xyd")
@Data
public class TbYddXyd {
    @Id
    @KeySql(useGeneratedKeys = true)
    private String xid;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**修改时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**删除时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deleteTime;
    /**预定单编号*/
    private String xddbh;
    /**门店id*/
    private String xbsmc;
    /**门店名称*/
    private String xbsname;
    /**进场时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date xjctime;
    /**退场时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date xtctime;
    /**房东*/
    private String xfd;
    /**房东id*/
    private String xfdid;
    /**房东电话*/
    private String xfddh;
    /**租客*/
    private String xzk;
    /**租客电话*/
    private String xzkdh;
    /**租客身份证*/
    private String xzksfz;
    /**单位名称*/
    private String xdwmc;
    /**客户性质*/
    private String xtdxz;
    /**客户来源*/
    private String xkhly;
    /**人数*/
    private Integer xrs;
    /**全款租金*/
    private Double xqkzj;
    /**场地费优惠价格*/
    private Double xhfyj;
    /**定金金额*/
    private Double xysdj;
    /**轰趴师费用*/
    private Double xhpsfy;
    /**策划费用*/
    private Double xztze;
    /**订餐费用*/
    private Double xdcze;
    /**是否删除*/
    private Integer xsfsc;
    /**定金二维码地址*/
    private String xdjurl;
    /**定金二维码生成时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date xdjurltime;
    /**定金二维码失效时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date xdjsxtime;
    /**定金支付时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date xdjpaytime;
    /**删除人*/
    private String xscer;
    /**删除人id*/
    private String xscerid;
    /**预定单状态*/
    private String xstatu;
    /**实例id*/
    private String xslid;
    /**crm编号*/
    private String qqid;
    /**保险费用金额*/
    private Double xbxzje;
    /**保险人数*/
    private Integer xbxrs;
    /**保险单价*/
    private Double xbxdj;
    /**订餐数量*/
    private Integer xdcsl;
    /**订餐单价*/
    private Double xdcdj;
    /**轰趴师数量*/
    private Integer xhpssl;
    /**轰趴师单价*/
    private Double xhpsdj;
    /**策划数量*/
    private Integer xchsl;
    /**策划单价*/
    private Double xchdj;
    /**策划总额*/
    private Double xchze;
    /**摄影数量*/
    private Integer xsysl;
    /**摄影单价*/
    private Double xsydj;
    /**摄影摄像总额*/
    private Double xsysxze;
    /**收款编号*/
    private String xskbh;
    /**用户微信id*/
    private String accountid;
    /**定金类型(1:线下，2:线上)*/
    private String xdjtype;
    /**券码平台类型*/
    private String qmpttype;
    /**抖音门店id*/
    private String dymdid;
    /**美团门店id*/
    private String mtmdid;
    /**券码*/
    private String qm;
}
