package com.wlgb.config;

import com.alibaba.fastjson.JSONObject;
import com.wlgb.entity.TbQrd;
import com.wlgb.entity.TbVilla;
import com.wlgb.entity.TbXyd;
import com.wlgb.entity.WlgbBdjl;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/01/09 15:18
 */
public class DzBdConfig extends BdConfig {

    /**
     * 上传对账表单
     *
     * @param tbXyd 协议单对象
     */
    public static WlgbBdjl scDzBd(TbXyd tbXyd,
                                  DingdingEmployee employee2,
                                  TbVilla villa,
                                  DingdingEmployee ding,
                                  Dingkey dingkey,
                                  WlgbBdjl wlgbBdjl2,
                                  Double jzj,
                                  String xydUrl,
                                  YdAppkey ydAppkey,
                                  YdBd ydBd, String sfydd) {
        //新增数据表到宜搭---对账
        Map<String, Object> map2 = new HashMap<>();
        //保洁场次
        int i = tbXyd.getXzjcc() != null ? tbXyd.getXzjcc() : 0;
        double b = i / 2.00;
        int a = (int) b;
        int c = b > a ? a + 1 : a;
        map2.put("bjcc", c);
        //基准价
        map2.put("qbcjzj", jzj);
        //活动策划类型
        map2.put("xhdchtype", tbXyd.getXhdchtype());

        //是否预定单
        map2.put("sfydd", sfydd);


        //是否使用未消费的定金
        map2.put("xsfsywxfwdddje", tbXyd.getXsfsywxfwdddje() != null ? tbXyd.getXsfsywxfwdddje() : 0);
        //未消费定金的订单编号
        map2.put("xwsywdddbh", tbXyd.getXwsywdddbh() != null ? tbXyd.getXwsywdddbh() : "");
        //未消费定金的金额
        map2.put("xwsywdddsyje", tbXyd.getXwsywdddsyje() != null ? tbXyd.getXwsywdddsyje() : 0);

        //TODO 包服务开
        map2.put("xsfbdf", tbXyd.getXsfbdf());
        map2.put("xdfje", tbXyd.getXdfje());
        map2.put("xsfbc", tbXyd.getXsfbc());
        map2.put("xcf", tbXyd.getXcf());
        map2.put("xsfbch", tbXyd.getXsfbch());
        map2.put("xchje", tbXyd.getXchje());
        map2.put("xsfbcc", tbXyd.getXsfbcc());
        map2.put("xbcje", tbXyd.getXbcje());
        map2.put("xsfbwsf", tbXyd.getXsfbwsf());
        map2.put("xwsfje", tbXyd.getXwsfje());
        map2.put("xsfbcfsyf", tbXyd.getXsfbcfsyf());
        map2.put("xcfsyfje", tbXyd.getXcfsyfje());
        //TODO 包服务关

        map2.put("qsfys", tbXyd.getXsfys() != null ? !"".equals(tbXyd.getXsfys()) ? tbXyd.getXsfys() : 0 : 0);
        map2.put("qysbz", tbXyd.getXysbz());

        // TODO 有关剧本杀（如果不需要上线要注释）
        map2.put("xsfxyjbs", tbXyd.getXsfxyjbs() != null && !"".equals(tbXyd.getXsfxyjbs()) ? tbXyd.getXsfxyjbs() : "0");
        map2.put("xjbszje", tbXyd.getXjbszje() != null ? tbXyd.getXjbszje() : 0);
        map2.put("xjbscjr", tbXyd.getXjbscjrid());
        map2.put("xjbszxr", tbXyd.getXjbszxr());
        // TODO 有关剧本杀（如果不需要上线要注释）

        //协议单id
        map2.put("qxydid", tbXyd.getXid());
        //订单编号
        map2.put("aid", tbXyd.getXddbh());
        //城市
        map2.put("cs", villa.getCity());
        //别墅名称
        map2.put("bsmc", villa.getVname());
        //别墅性质
        map2.put("bsxz", villa.getVxz());
        //进场时间
        map2.put("qjctime", tbXyd.getXjctime());
        //退场时间
        map2.put("qtctime", tbXyd.getXtctime());
        //电子协议单图片
        map2.put("aimg", YdConfig.setTpList(xydUrl, "协议单图片"));

        // TODO 开始新增字段
        // TODO 开始新增字段
        //进场时间
        map2.put("xjctime", tbXyd.getXjctime());
        //退场时间
        map2.put("xtctime", tbXyd.getXtctime());
        //别墅id
        map2.put("bsmcid", tbXyd.getXbsmc());
        //轰趴师姓名
        map2.put("xhpsxm", tbXyd.getXhpsxm() != null ? !"".equals(tbXyd.getXhpsxm()) ? tbXyd.getXhpsxm() : 0 : 0);
        //人数
        map2.put("xrs", tbXyd.getXrs() != null ? tbXyd.getXrs() : 0);
        //超出人数每/位
        map2.put("xcudrfy", tbXyd.getXcudrfy() != null ? tbXyd.getXcudrfy() : 0);
        //全款租金
        map2.put("xqkzj", tbXyd.getXqkzj() != null ? tbXyd.getXqkzj() : 0);
        //好评条数
        map2.put("xhpts", tbXyd.getXhpts() != null ? tbXyd.getXhpts() : 0);
        //转发条数
        map2.put("xzfts", tbXyd.getXzfts() != null ? tbXyd.getXzfts() : 0);
        //保险总额
        map2.put("xbxzje", tbXyd.getXbxzje() != null ? tbXyd.getXbxzje() : 0);
        //保险单价
        map2.put("xbxdj", tbXyd.getXbxdj() != null ? tbXyd.getXbxdj() : 0);
        //保险人数
        map2.put("xbxrs", tbXyd.getXbxrs() != null ? tbXyd.getXbxrs() : 0);
        //客户姓名
        map2.put("xzk", tbXyd.getXzk());
        //订餐总额
        map2.put("xdcze", tbXyd.getXdcze() != null ? tbXyd.getXdcze() : 0);
        //烧烤总额
        map2.put("xskze", tbXyd.getXskze() != null ? tbXyd.getXskze() : 0);
        //真人cs
        map2.put("xzrcsfy", tbXyd.getXzrcsfy() != null ? tbXyd.getXzrcsfy() : 0);
        //轰趴师费用
        map2.put("xhpsfy", tbXyd.getXhpsfy() != null ? tbXyd.getXhpsfy() : 0);
        //值班店长（仅显示）
        map2.put("zbdz", employee2 != null ? employee2.getName() : "未设置值班店长");
        //恢复原价
        map2.put("xhfyj", tbXyd.getXhfyj() != null ? tbXyd.getXhfyj() : 0);
        //补交备注
        map2.put("xbjxx", tbXyd.getXbjxx());

        //订单类型
        Integer bba = tbXyd.getXzdcl() != null ? tbXyd.getXzdcl() : 0;
        String ddlx = "无(普通订单)";
        switch (bba) {
            case 0:
                break;
            case 1:
                ddlx = "双方平分业绩";
                break;
            case 2:
                ddlx = "双方平分等同";
                break;
            case 3:
                ddlx = "代谈单(老板是业务经理，房东)";
                break;
            case 4:
                ddlx = "校代单";
                break;
            case 5:
                ddlx = "业绩减半";
                break;
            case 6:
                ddlx = "老板三分之一，房东三分之二";
                break;
            case 7:
                ddlx = "老板三分之二,房东三分之一";
                break;
            default:
                break;
        }
        //订单类型
        map2.put("xzdcl", ddlx);
        //协议单备注
        map2.put("xsxbz", tbXyd.getXsxbz());
        //租客身份证
        map2.put("xzksfz", tbXyd.getXzksfz());
        //租客身份证
        map2.put("xlbjf", tbXyd.getXlbjf());
        //房东
        map2.put("xfd", tbXyd.getXfd());
        //房东电话
        map2.put("xfddh", tbXyd.getXfddh());
        //是否需要轰趴师
        map2.put("xsfhps", tbXyd.getXsfhps() != null ? tbXyd.getXsfhps() == 1 ? 1 : tbXyd.getXsfhps() == 0 ? 0 : 0 : 0);
        //CS教官
        map2.put("xzrcsjg", tbXyd.getXzrcsjg());
        //cs人数
        map2.put("xzrcsrs", tbXyd.getXzrcsrs() != null ? tbXyd.getXzrcsrs() : 0);
        //订餐金额
        map2.put("xbdje", tbXyd.getXbdje() != null ? tbXyd.getXbdje() : 0);
        //订餐数量
        map2.put("xbdsl", tbXyd.getXbdsl() != null ? tbXyd.getXbdsl() : 0);
        //烧烤金额
        map2.put("xskje", tbXyd.getXskje() != null ? tbXyd.getXskje() : 0);
        //烧烤数量
        map2.put("xsksl", tbXyd.getXsksl() != null ? tbXyd.getXsksl() : 0);
        //策划经理
        map2.put("xchjl", tbXyd.getXchjl());
        //策划成交人
        map2.put("xchcjr", tbXyd.getXchcjr());

        //校代姓名
        map2.put("xxdxm", tbXyd.getXxdxm());
        //校代城市
        map2.put("xxdsscs", tbXyd.getXxdsscs());
        //校代上级姓名
        map2.put("xxdsjxm", tbXyd.getXxdsjxm());
        //校代电话
        map2.put("xxddh", tbXyd.getXxddh());
        //客户号码
        map2.put("xzkdh", tbXyd.getXzkdh());
        //策划
        map2.put("xztze", tbXyd.getXztze() != null ? tbXyd.getXztze() : 0);
        //策划
        map2.put("qchfw", tbXyd.getXztze() != null ? tbXyd.getXztze() : 0);
        //保险
        map2.put("xbxzje", tbXyd.getXbxzje() != null ? tbXyd.getXbxzje() : 0);
        //cs
        map2.put("xzrcsfy", tbXyd.getXzrcsfy() != null ? tbXyd.getXzrcsfy() : 0);
        //轰趴师
        map2.put("xhpsfy", tbXyd.getXhpsfy() != null ? tbXyd.getXhpsfy() : 0);
        //轰趴师姓名
        map2.put("xhpsxm", tbXyd.getXhpsxm());
        //订餐总额
        map2.put("qdcxf", tbXyd.getXdcze() != null ? tbXyd.getXdcze() : 0);
        //烧烤总额
        map2.put("qsktc", tbXyd.getXskze() != null ? tbXyd.getXskze() : 0);
        //定金类型
        map2.put("xdjtype", tbXyd.getXdjtype());
        //补交类型
        map2.put("xbjdjlx", tbXyd.getXbjdjlx());
        //已收定金
        map2.put("xysdj", tbXyd.getXysdj() != null ? tbXyd.getXysdj() : 0);
        //已收定金
        map2.put("xyszzdj", (tbXyd.getXyszzdj() != null && !"".equals(tbXyd.getXyszzdj()) ? tbXyd.getXyszzdj() : 0));
        //线上定金
        map2.put("xxsdj", tbXyd.getXxsdj() != null ? tbXyd.getXxsdj() : 0);
        //线下定金
        map2.put("xxxdj", tbXyd.getXxxdj() != null ? tbXyd.getXxxdj() : 0);
        //补交定金
        map2.put("xbjdj", tbXyd.getXbjdj() != null ? tbXyd.getXbjdj() : 0);
        //场地费补交
        map2.put("xcdzjbj", tbXyd.getXcdzjbj() != null ? tbXyd.getXcdzjbj() : 0);
        //场地费补交
        map2.put("xzzfybj", tbXyd.getXzzfybj() != null ? tbXyd.getXzzfybj() : 0);
        //线上补交
        map2.put("xxsbj", tbXyd.getXxsbj() != null ? tbXyd.getXxsbj() : 0);
        //线上补交
        map2.put("xxxbj", tbXyd.getXxxbj() != null ? tbXyd.getXxxbj() : 0);
        //客户姓名
        map2.put("qkhxm", tbXyd.getXzk());
        //客户身份证
        map2.put("qkhsfzh", tbXyd.getXzksfz());
        //商品优惠券金额
        map2.put("xspyhq", tbXyd.getXspyhq() != null ? tbXyd.getXspyhq() : 0);

        WlgbBdjl wlgbBdjl = new WlgbBdjl();
        if (wlgbBdjl2 == null) {
            //新增表单容错机制
            GatewayResult gatewayResult1 = zxDzBdXz(tbXyd, map2, ding != null ? ding.getUserid() : "012412221639786136545", dingkey, ydAppkey, ydBd, ding);
            wlgbBdjl.setId(IdConfig.uuId());
            wlgbBdjl.setTime(new Date());
            wlgbBdjl.setXid(tbXyd.getXid());
            if (gatewayResult1 != null) {
                wlgbBdjl.setSlid(gatewayResult1.getResult());
            }
            wlgbBdjl.setBz("对账表单提交");
            wlgbBdjl.setUserid(ding != null ? ding.getUserid() : "012412221639786136545");
        } else {
            if (wlgbBdjl2.getSlid() != null && !"".equals(wlgbBdjl2.getSlid())) {
                //表单容错机制
                queryYdRc(tbXyd,
                        "修改协议单同步对账表单",
                        map2,
                        wlgbBdjl2.getSlid(),
                        ding != null ? ding.getUserid() : "012412221639786136545",
                        dingkey,
                        ding,
                        ydAppkey
                );
                wlgbBdjl = null;
            } else {
                //新增表单容错机制
                GatewayResult gatewayResult1 = zxDzBdXz(tbXyd, map2, ding != null ? ding.getUserid() : "012412221639786136545", dingkey, ydAppkey, ydBd, ding);
                wlgbBdjl.setId(wlgbBdjl2.getId());
                if (gatewayResult1 != null) {
                    wlgbBdjl.setSlid(gatewayResult1.getResult());
                }
            }
        }
        return wlgbBdjl;
    }


    /**
     * 同步至宜搭对账表单
     *
     * @param xyd   协议单数据
     * @param tbQrd 确认单数据
     */
    public static WlgbBdjl tbDz(TbXyd xyd,
                                TbQrd tbQrd,
                                JSONObject jsonObject,
                                TbVilla villa,
                                WlgbBdjl wlgbBdjl2,
                                String xydUrl,
                                DingdingEmployee employee2,
                                Dingkey dingkey,
                                YdAppkey ydAppkey,
                                YdBd ydBd,
                                DingdingEmployee ding) {
        Map<String, Object> map = new HashMap<>();
        //餐饮明细
        map.put("cymx", jsonObject.get("tableField_lp6be3gd"));
        //餐饮保洁
        map.put("cybj", jsonObject.get("cybj"));
        //纸质结算单图片
        map.put("imageField_l6vvhdhn", jsonObject.get("imageField_l6vvd1ll"));
        //进场提前小时数
        map.put("jctqnum", jsonObject.get("jctqnum"));
        //退场延时小时数
        map.put("tcysnum", jsonObject.get("tcysnum"));
        //延时时长
        map.put("qyssc", jsonObject.get("qyssc"));
        //延时单价
        map.put("qysdj", jsonObject.get("qysdj"));
        //代购成本单
        map.put("qdgcbd", jsonObject.get("qdgcbd"));
        //代购付款截图
        map.put("qdgfkjt", jsonObject.get("qdgfkjt"));
        //策划执行人
        map.put("qchzxrs", jsonObject.get("qchzxrs"));
        //店长提交业绩开支
        map.put("dztjyjkz", jsonObject.get("qbcje"));
        //保洁姓名
        map.put("qbj", jsonObject.get("qbj"));
        //保洁姓名
        map.put("qbjs", jsonObject.get("qbjs"));
        //保洁临时人员姓名
        map.put("bjlsryxm", jsonObject.get("bjlsryxm"));
        //保洁临时人员联系方式
        map.put("bjlsrylsfs", jsonObject.get("bjlsrylsfs"));
        //成本单/收据
        map.put("cbdsj", jsonObject.get("cbdsj"));
        //成本单2
        map.put("cbdsj2", jsonObject.get("cbdsj2"));
        //成本单3
        map.put("cbdsj3", jsonObject.get("cbdsj3"));
        //策划执行人
        map.put("qchzxr", tbQrd.getQchzxr() != null ? tbQrd.getQchzxr() : "");
        //扯皮开支
        map.put("qcpkz", tbQrd.getQcpkz() != null ? tbQrd.getQcpkz() : 0);
        //是否店长对账
        map.put("sfdzdz", 1);
        //策划外包金额
        map.put("qchwbje", tbQrd.getQchwbje() != null ? tbQrd.getQchwbje() : 0);
        //策划外包成本
        map.put("qchwbcb", tbQrd.getQchwbcb() != null ? tbQrd.getQchwbcb() : 0);
        //策划外包利润
        map.put("qchwblr", tbQrd.getQchwblr() != null ? tbQrd.getQchwblr() : 0);
        //策划外包成本图片1
        map.put("qchwbcbtp1", jsonObject.get("qchwbcbtp1"));
        //策划外包成本图片2
        map.put("qchwbcbtp2", jsonObject.get("qchwbcbtp2"));
        //自助餐成本
        map.put("qzzccb", tbQrd.getQzzccb() != null ? tbQrd.getQzzccb() : 0);
        //自助餐成本图片1
        map.put("qzzccbtp1", jsonObject.get("qzzccbtp1"));
        //自助餐成本图片2
        map.put("qzzccbtp2", jsonObject.get("qzzccbtp2"));
        //策划转回公司比例
        map.put("qchzhgsbl", tbQrd.getQchzhgsbl() != null ? tbQrd.getQchzhgsbl() : 0);
        //策划转回公司金额
        map.put("qchzhgsje", tbQrd.getQchzhgsje() != null ? tbQrd.getQchzhgsje() : 0);
        //场地费尾款金额
        map.put("cdfwkje", tbQrd.getCdfwkje() != null ? tbQrd.getCdfwkje() : 0);
        //增值尾款金额
        map.put("zzwkje", tbQrd.getZzwkje() != null ? tbQrd.getZzwkje() : 0);
        //实到人数
        map.put("qsdrs", tbQrd.getQsdrs() != null ? tbQrd.getQsdrs() : 0);
        //超出人数费用
        map.put("qccrsfy", tbQrd.getQccrsfy() != null ? tbQrd.getQccrsfy() : 0);
        //实转条数
        map.put("qszts", tbQrd.getQszts() != null ? tbQrd.getQszts() : 0);
        //实评条数
        map.put("qspts", tbQrd.getQspts() != null ? tbQrd.getQspts() : 0);
        //应收场地费
        map.put("qsjcdf", tbQrd.getQsjcdf() != null ? tbQrd.getQsjcdf() : 0);
        //延时费
        map.put("qysf", tbQrd.getQysf() != null ? tbQrd.getQysf() : 0);
        //厨房使用费
        map.put("qcfsyf", tbQrd.getQcfsyf() != null ? tbQrd.getQcfsyf() : 0);
        //分成
        map.put("qspzhgsbl", tbQrd.getQspzhgsbl() != null ? tbQrd.getQspzhgsbl() : 0.2);
        //商品消费
        map.put("qspxf", tbQrd.getQspxf() != null ? tbQrd.getQspxf() : 0);
        //商品消费图片
        map.put("qspqrdF", jsonObject.get("qspqrdF"));
        //商品消费图片2
        map.put("qspqrdF1", jsonObject.get("qspqrdF1"));
        //商品消费图片3
        map.put("qspqrdF2", jsonObject.get("qspqrdF2"));
        //入场电度数
        map.put("qrcdds", tbQrd.getQrcdds() != null ? tbQrd.getQrcdds() : 0);
        //退场电度数
        map.put("qtcdds", tbQrd.getQtcdds() != null ? tbQrd.getQtcdds() : 0);
        //电费
        map.put("qdf", tbQrd.getQdf() != null ? tbQrd.getQdf() : 0);
        //代购食材
        map.put("qdgsc", tbQrd.getQdgsc() != null ? tbQrd.getQdgsc() : 0);
        //订餐消费
        map.put("qdcxf", tbQrd.getQdcxf() != null ? tbQrd.getQdcxf() : 0);
        //烧烤消费
        map.put("qsktc", tbQrd.getQsktc() != null ? tbQrd.getQsktc() : 0);
        //策划服务
        map.put("qchfw", tbQrd.getQchfw() != null ? tbQrd.getQchfw() : 0);
        //自助餐收入
        map.put("qzzcsr", tbQrd.getQzzcsr() != null ? tbQrd.getQzzcsr() : 0);
        //策划其他收入
        map.put("qchqtsr", tbQrd.getQchqtsr() != null ? tbQrd.getQchqtsr() : 0);
        //场地增收
        map.put("qcdzs", tbQrd.getQcdzs() != null ? tbQrd.getQcdzs() : 0);
        //校代金额
        map.put("qxdje", tbQrd.getQxdje() != null ? tbQrd.getQxdje() : 0);
        //卫生费
        map.put("qwsf", tbQrd.getQwsf() != null ? tbQrd.getQwsf() : 0);
        //赔偿费
        map.put("qbcf", tbQrd.getQbcf() != null ? tbQrd.getQbcf() : 0);
        //订餐成本
        map.put("qdccb", tbQrd.getQdccb() != null ? tbQrd.getQdccb() : 0);
        //订餐总成本图
        map.put("qddcbF", jsonObject.get("qddcbF"));
        //订餐总成本图2
        map.put("qddcbF2", jsonObject.get("qddcbF2"));
        //订餐总成本图3
        map.put("qddcbF3", jsonObject.get("qddcbF3"));
        //烧烤成本
        map.put("qskcb", tbQrd.getQskcb() != null ? tbQrd.getQskcb() : 0);
        //烧烤总成本图
        map.put("qskcbF", jsonObject.get("qskcbF"));
        //烧烤总成本图2
        map.put("qskcbF2", jsonObject.get("qskcbF2"));
        //烧烤总成本图3
        map.put("qskcbF3", jsonObject.get("qskcbF3"));
        //转账金额
        map.put("zzje", tbQrd.getZzje() != null ? tbQrd.getZzje() : 0);
        //转账图片1
        map.put("zztp1", jsonObject.get("zztp1"));
        //转账图片2
        map.put("zztp2", jsonObject.get("zztp2"));
        //转账时间
        map.put("qzzsj", jsonObject.get("qzzsj"));
        //应退押金
        map.put("qytyj", tbQrd.getQytyj() != null ? tbQrd.getQytyj() : 0);
        //押金
        map.put("qyj", tbQrd.getQyj() != null ? tbQrd.getQyj() : 0);
        //补差金额
        map.put("qbcje", 0);
        //啤酒开资
        map.put("qpjkz", tbQrd.getQpjkz() != null ? tbQrd.getQpjkz() : 0);
        //调账
        map.put("qtz", tbQrd.getQtz() != null ? tbQrd.getQtz() : 0);
        //共收入
        map.put("qgsr", tbQrd.getQgsr() != null ? tbQrd.getQgsr() : 0);
        //共支出
        map.put("qgzc", tbQrd.getQgzc() != null ? tbQrd.getQgzc() : 0);
        //公司应收
        map.put("qgsys", tbQrd.getQgsys() != null ? tbQrd.getQgsys() : 0);
        //备注信息
        map.put("qremark", tbQrd.getQremark() != null ? tbQrd.getQremark() : "");
        //进场时间
        map.put("qjctime", jsonObject.get("qjctime"));
        //退场时间
        map.put("qtctime", jsonObject.get("qtctime"));
        //应转公司商品金额
        map.put("qjnje", tbQrd.getQjnje() != null ? tbQrd.getQjnje() : 0);
        //分公司经费
        map.put("qfgsjf", jsonObject.getDouble("qfgsjf") != null ? jsonObject.getDouble("qfgsjf") : 0);
        //尾款图片
        map.put("cdfwktp1", jsonObject.get("cdfwktp1"));
        //尾款图片
        map.put("cdfwktp2", jsonObject.get("cdfwktp2"));
        //进场电表图片
        map.put("rcSjxjdbt", jsonObject.get("rcSjxjdbt"));
        //退场电表图片
        map.put("tcSjdbt", jsonObject.get("tcSjdbt"));
        //进场电表图片
        map.put("rcSjxjdbt2", jsonObject.get("rcSjxjdbt2"));
        //退场电表图片
        map.put("tcSjdbt2", jsonObject.get("tcSjdbt2"));
        //进场押金上传图片
        map.put("qjcyjtp", jsonObject.get("qjcyjtp"));
        //代购收入
        map.put("qdgsr", tbQrd.getQdgsr() != null ? tbQrd.getQdgsr() : 0);
        //代购成本
        map.put("qdgcb", tbQrd.getQdgcb() != null ? tbQrd.getQdgcb() : 0);
        //客户开支
        map.put("qkhkz", jsonObject.get("qkhkz"));
        //店长开支
        map.put("qdzkz", jsonObject.get("qkhkz"));
        //卫生费图片
        map.put("wsjzjt", jsonObject.get("wsjzjt"));
        //赔偿费用图片
        map.put("pcjzjt", jsonObject.get("pcjzjt"));
        //订单编号
        map.put("xddbh", xyd.getXddbh());
        //城市
        map.put("city", villa != null ? villa.getCity() : null);
        //保险单价
        map.put("xbxdj", xyd.getXbxdj() != null ? xyd.getXbxdj() : 0);
        //保险人数
        map.put("xbxrs", xyd.getXbxrs() != null ? xyd.getXbxrs() : 0);
        //租客姓名
        map.put("xzk", xyd.getXzk());
        //租客电话
        map.put("xzkdh", xyd.getXzkdh());
        //协议单图片
        map.put("aimg", YdConfig.setTpList(xydUrl, "协议单图片"));
        //订单编号
        map.put("aid", xyd.getXddbh());
        //协议单id
        map.put("qxydid", xyd.getXid());
        //别墅名称
        map.put("bsmc", villa != null ? villa.getVname() : null);
        //进场时间
        map.put("xjctime", xyd.getXjctime());
        //退场时间
        map.put("xtctime", xyd.getXtctime());
        //线上定金
        map.put("xxsdj", xyd.getXxsdj() != null ? xyd.getXxsdj() : 0);
        //线下定金
        map.put("xxxdj", xyd.getXxxdj() != null ? xyd.getXxxdj() : 0);
        //补交定金
        map.put("xbjdj", xyd.getXbjdj() != null ? xyd.getXbjdj() : 0);
        //城市
        map.put("cs", villa != null ? villa.getCity() : null);
        //别墅id
        map.put("bsmcid", xyd.getXbsmc());
        //人数
        map.put("xrs", xyd.getXrs() != null ? xyd.getXrs() : 0);
        //超出人数每/位
        map.put("xcudrfy", xyd.getXcudrfy() != null ? xyd.getXcudrfy() : 0);
        //全款租金
        map.put("xqkzj", xyd.getXqkzj() != null ? xyd.getXqkzj() : 0);
        //恢复原价
        map.put("xcdfyh", xyd.getXhfyj() != null ? xyd.getXhfyj() : 0);
        //好评条数
        map.put("xhpts", xyd.getXhpts() != null ? xyd.getXhpts() : 0);
        //转发条数
        map.put("xzfts", xyd.getXzfts() != null ? xyd.getXzfts() : 0);
        //保险总额
        map.put("xbxzje", xyd.getXbxzje() != null ? xyd.getXbxzje() : 0);
        //订餐总额
        map.put("xdcze", xyd.getXdcze() != null ? xyd.getXdcze() : 0);
        //烧烤总额
        map.put("xskze", xyd.getXskze() != null ? xyd.getXskze() : 0);
        //真人cs
        map.put("xzrcsfy", xyd.getXzrcsfy() != null ? xyd.getXzrcsfy() : 0);
        //轰趴师费用
        map.put("xhpsfy", jsonObject.getDouble("xhpsfy") != null ? jsonObject.getDouble("xhpsfy") : 0);
        //轰趴师姓名
        map.put("xhpsxm", jsonObject.getString("xhpsxm"));
        //轰趴师支出工资
        map.put("xhpszcgz", jsonObject.getDouble("xhpszcgz") != null ? jsonObject.getDouble("xhpszcgz") : 0);
        //轰趴师其他支出
        map.put("xhpszcqt", jsonObject.getDouble("xhpszcqt") != null ? jsonObject.getDouble("xhpszcqt") : 0);
        //保险支出
        map.put("xbxzc", jsonObject.getDouble("xbxzc") != null ? jsonObject.getDouble("xbxzc") : 0);
        //完成优惠证据图片
        map.put("zfjzjt", jsonObject.get("zfjzjt"));
        //值班店长
        map.put("qzbdz", employee2 != null ? employee2.getUserid() : null);
        //恢复原价
        map.put("xhfyj", xyd.getXhfyj() != null ? xyd.getXhfyj() : 0);
        //补交备注
        map.put("xbjxx", xyd.getXbjxx());
        double v = jsonObject.getDouble("xjbszje") != null ? jsonObject.getDouble("xjbszje") : 0;
        //是否需要剧本杀
        map.put("xsfhxgjcj", v > 0 ? 1 : 0);
        //剧本杀金额
        map.put("xjbszje", v);
        //剧本杀支出
        map.put("qjbszc", jsonObject.getDouble("qjbszc") != null ? jsonObject.getDouble("qjbszc") : 0);
        //延时费归属人1
        map.put("qysfgsr1", jsonObject.getString("qysfgsr1") != null ? jsonObject.getString("qysfgsr1") : null);
        Integer bba = xyd.getXzdcl() != null ? xyd.getXzdcl() : 0;
        String ddlx = "无(普通订单)";
        switch (bba) {
            case 0:
                break;
            case 1:
                ddlx = "双方平分业绩";
                break;
            case 2:
                ddlx = "双方平分等同";
                break;
            case 3:
                ddlx = "代谈单(老板是业务经理，房东)";
                break;
            case 4:
                ddlx = "校代单";
                break;
            case 5:
                ddlx = "业绩减半";
                break;
            case 6:
                ddlx = "老板三分之一，房东三分之二";
                break;
            case 7:
                ddlx = "老板三分之二,房东三分之一";
                break;
            default:
                break;
        }
        //订单类型
        map.put("xzdcl", ddlx);
        //协议单备注
        map.put("xsxbz", xyd.getXsxbz());
        //租客身份证
        map.put("xzksfz", xyd.getXzksfz());
        //租客身份证
        map.put("xlbjf", xyd.getXlbjf());
        //房东
        map.put("xfd", xyd.getXfd());
        //房东电话
        map.put("xfddh", xyd.getXfddh());
        //是否需要轰趴师
        map.put("xsfhps", xyd.getXsfhps() != null ? xyd.getXsfhps() == 1 ? 1 : xyd.getXsfhps() == 0 ? 0 : 0 : 0);
        //CS教官
        map.put("xzrcsjg", xyd.getXzrcsjg());
        //cs人数
        map.put("xzrcsrs", xyd.getXzrcsrs() != null ? xyd.getXzrcsrs() : 0);
        //订餐金额
        map.put("xbdje", xyd.getXbdje() != null ? xyd.getXbdje() : 0);
        //订餐数量
        map.put("xbdsl", xyd.getXbdsl() != null ? xyd.getXbdsl() : 0);
        //烧烤金额
        map.put("xskje", xyd.getXskje() != null ? xyd.getXskje() : 0);
        //烧烤数量
        map.put("xsksl", xyd.getXsksl() != null ? xyd.getXsksl() : 0);
        //策划经理
        map.put("xchjl", xyd.getXchjl());
        //策划成交人
        map.put("xchcjr", xyd.getXchcjr());
        //校代姓名
        map.put("xxdxm", xyd.getXxdxm());
        //校代城市
        map.put("xxdsscs", xyd.getXxdsscs());
        //校代上级姓名
        map.put("xxdsjxm", xyd.getXxdsjxm());
        //校代电话
        map.put("xxddh", xyd.getXxddh());
        //客户号码
        map.put("xzkdh", xyd.getXzkdh());
        //策划
        map.put("xztze", xyd.getXztze() != null ? xyd.getXztze() : 0);
        //保险
        map.put("xbxzje", xyd.getXbxzje() != null ? xyd.getXbxzje() : 0);
        //cs
        map.put("xzrcsfy", xyd.getXzrcsfy() != null ? xyd.getXzrcsfy() : 0);
        //已收定金
        map.put("xysdj", xyd.getXysdj() != null ? xyd.getXysdj() : 0);
        //退押金
        if (tbQrd.getQytyj() != null && tbQrd.getQytyj() > 0) {
            map.put("qkhskfs", tbQrd.getQkhskfs());
            map.put("qkhzfb", tbQrd.getQkhskfs() != null ? "0".equals(tbQrd.getQkhskfs()) ? tbQrd.getQkhzfb() : null : null);
            map.put("qkhyhk", tbQrd.getQkhskfs() != null ? "1".equals(tbQrd.getQkhskfs()) ? tbQrd.getQkhyhk() : null : null);
        }
        //成本
        map.put("cyzcb", jsonObject.getDouble("cyzcb"));
        //退店长金额
        map.put("qtdzje", tbQrd.getQspxf() != null ? tbQrd.getQspxf() * 0.7 : 0);
        //备注
        map.put("qremark", tbQrd.getQremark());

        WlgbBdjl wlgbBdjl = new WlgbBdjl();
        if (wlgbBdjl2 == null) {
            //新增表单容错机制
            GatewayResult gatewayResult1 = queryYdXzRc(xyd, "店长对账新增（之前没有）对账表单", map, ding != null ? ding.getUserid() : "012412221639786136545", dingkey, ydAppkey, ydBd, ding);
            wlgbBdjl.setId(IdConfig.uuId());
            wlgbBdjl.setTime(new Date());
            wlgbBdjl.setXid(xyd.getXid());
            if (gatewayResult1 != null) {
                wlgbBdjl.setSlid(gatewayResult1.getResult());
            }
            wlgbBdjl.setBz("对账表单提交");
            wlgbBdjl.setUserid(ding != null ? ding.getUserid() : "012412221639786136545");
        } else {
            if (wlgbBdjl2.getSlid() != null && !"".equals(wlgbBdjl2.getSlid())) {
                //使用店长id（谁对账使用谁id）
                //容错机制修改表单内容
                queryYdRc(xyd, "店长对账修改对账表单", map, wlgbBdjl2.getSlid(), ding != null ? ding.getUserid() : "012412221639786136545", dingkey, ding, ydAppkey);
                wlgbBdjl = null;
            } else {
                //是否完成对账
                map.put("sfwcdz", "0");
                //新增表单容错机制
                GatewayResult gatewayResult1 = queryYdXzRc(xyd, "店长对账新增（之前没有）对账表单", map, ding != null ? ding.getUserid() : "012412221639786136545", dingkey, ydAppkey, ydBd, ding);
                wlgbBdjl.setId(wlgbBdjl2.getId());
                if (gatewayResult1 != null) {
                    wlgbBdjl.setSlid(gatewayResult1.getResult());
                }
            }
        }
        return wlgbBdjl;
    }

    /**
     * 执行对账表单新增
     */
    private static GatewayResult zxDzBdXz(TbXyd tbXyd, Map<String, Object> map2, String userid, Dingkey dingkey, YdAppkey ydAppkey, YdBd ydBd, DingdingEmployee ding) {
        map2.put("qsdrs", tbXyd.getXrs() != null ? tbXyd.getXrs() : 0);
        map2.put("qszts", tbXyd.getXzfts() != null ? tbXyd.getXzfts() : 0);
        map2.put("qspts", tbXyd.getXhpts() != null ? tbXyd.getXhpts() : 0);
        map2.put("qysf", 0);
        map2.put("qcfsyf", 0);
        map2.put("cdfwkje", 0);
        map2.put("zzwkje", 0);
        map2.put("qsjcdf", 0);
        map2.put("xhpszcgz", 0);
        map2.put("qspxf", 0);
        map2.put("qfgsjf", 0);
        map2.put("qyzhgssp", 0);
        map2.put("qrcdds", 0);
        map2.put("qtcdds", 0);
        map2.put("qdf", 0);
        map2.put("qdgsc", 0);
        map2.put("qzzcsr", 0);
        map2.put("qchqtsr", 0);
        map2.put("qyj", 0);
        map2.put("qytyj", 0);
        map2.put("xbxzc", tbXyd.getXbxzc() != null ? tbXyd.getXbxzc() : 0);
        map2.put("qxdje", 0);
        map2.put("qwsf", 0);
        map2.put("qbcf", 0);
        map2.put("qdccb", 0);
        map2.put("qskcb", 0);
        map2.put("qcdzs", 0);
        map2.put("qbcje", 0);
        map2.put("qpjkz", 0);
        map2.put("qtz", 0);
        map2.put("qccrsfy", 0);
        map2.put("xhpszcqt", 0);
        //自助餐利润
        map2.put("qzzclr", 0);
        //延时时长
        map2.put("qyssc", 0);
        //延时单价
        map2.put("qysdj", 0);
        //优惠合计
        map2.put("qyhhj", 0);
        //场地费优惠金额
        map2.put("qcdfyhje", 0);
        //差值
        map2.put("qcz", 0);
        //商品应转回公司金额2
        map2.put("qspyzhgsje2", 0);
        //店长收入
        map2.put("qdzsr", 0);
        //公司应收餐费
        map2.put("qgsyscf", 0);
        //应转回公司布置策划金额
        map2.put("yzhgsbzchje", 0);
        //进场提前小时数
        map2.put("jctqnum", 0);
        //退场延时小时数
        map2.put("tcysnum", 0);
        //扯皮开支
        map2.put("qcpkz", 0);
        //是否店长对账
        map2.put("sfdzdz", 0);
        //客户实际已付定金
        map2.put("qkhsjyfzdj", 0);
        //策划外包金额
        map2.put("qchwbje", 0);
        //策划外包成本
        map2.put("qchwbcb", 0);
        //策划外包利润
        map2.put("qchwblr", 0);
        //策划转回公司金额
        map2.put("qchzhgsje", 0);
        //自助餐成本
        map2.put("qzzccb", 0);
        //代购收入
        map2.put("qdgsr", 0);
        //代购成本
        map2.put("qdgcb", 0);
        //客户业绩开支
        map2.put("qkhkz", 0);
        //店长业绩开支
        map2.put("qdzkz", 0);
        //是否完成对账默认0
        map2.put("sfwcdz", 0);
        //是否异常
        map2.put("qsfyc", 0);
        map2.put("qjbszc", 0);
        map2.put("qjbszxrgz", 0);
        map2.put("qgsr", -1);
        map2.put("qgzc", -1);
        map2.put("qgsys", -1);
        //新增表单容错机制
        return queryYdXzRc(tbXyd,
                "新增对账表单",
                map2,
                userid,
                dingkey,
                ydAppkey,
                ydBd,
                ding
        );
    }

}
