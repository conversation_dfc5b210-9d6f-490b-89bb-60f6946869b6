package com.wlgb.service;

import com.wlgb.entity.WlgbJdLcxmbxsqjl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/19 23:48
 */
public interface WlgbJdLcxmbxsqjlService {
    void save(WlgbJdLcxmbxsqjl wlgbJdLcxmbxsqjl);

    void updateById(WlgbJdLcxmbxsqjl wlgbJdLcxmbxsqjl);

    WlgbJdLcxmbxsqjl queryBySpBhAndSfLrJd(String spbh, Integer sfLrJd);

    List<WlgbJdLcxmbxsqjl> queryListByWlgbJdLcxmbxsqjl(WlgbJdLcxmbxsqjl wlgbJdLcxmbxsqjl);
}
