package com.wlgb.config;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class PageHelpUtil {
    private int pageNum;
    private int pageSize;
    private long total;
    private List list;
    private Map map;

    public PageHelpUtil(int pageNum, int pageSize, long total, List list) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list;
    }

    public PageHelpUtil(int pageNum, int pageSize, long total, List list, Map map) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list;
        this.map=map;
    }

    public PageHelpUtil(int pageNum, int pageSize) {
        this.pageNum = (pageNum - 1) * pageSize ;
        this.pageSize = pageSize;
    }

    public PageHelpUtil() {
    }

    public PageHelpUtil(long total, List list) {
        this.total = total;
        this.list = list;
    }
}
