package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/9 23:29
 */
@Data
@Table(name = "wlgb_ydd_mdxfjl")
public class WlgbYddMdxfjl {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
    /**协议单id*/
    private String xid;
    /**协议单编号*/
    private String xddbh;
    /**收款编号*/
    private String skbh;
    /**金额*/
    private Double je;
    /**生成用户id*/
    private String userid;
    /**生成用户*/
    private String name;
    /**实例id*/
    private String slid;
    /**是否到账(0:否，1:是)*/
    private Integer sfdz;
    /**二维码图片*/
    private String ewm;
    /**生成时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date sctime;
    /**失效时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date sxtime;
    /**是否删除(0:否，1:是)*/
    private Integer sfsc;
    /**商户号*/
    private String shh;
    /**到账时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date dztime;
    /**银盛编号*/
    private String ysbh;
    /**银盛商户流水号*/
    private String shlsh;
}
