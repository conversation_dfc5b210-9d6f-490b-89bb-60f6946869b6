package com.wlgb.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: tb_kdj_fd
 * @Author: jeecg-boot
 * @Date:   2021-10-03
 * @Version: V1.0
 */
@Data
@Table(name = "tb_kdj_fd")
public class TbKdjFd {

	/**id*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.Integer id;
	/**进场时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date xjctime;
	/**退场时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private java.util.Date xtctime;
	/**别墅id*/
    private java.lang.String vid;
	/**别墅名称*/
    private java.lang.String vname;
	/**状态（0：正常，1：上涨，2：下浮）*/
    private java.lang.String statu;
	/**日期*/
    private java.lang.String rq;
	/**场次*/
    private java.lang.String cc;
	/**金额*/
    private java.lang.String je;
    /**原金额*/
    private java.lang.String yje;
}
