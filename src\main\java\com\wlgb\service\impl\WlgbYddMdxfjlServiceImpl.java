package com.wlgb.service.impl;

import com.wlgb.entity.WlgbYddMdxfjl;
import com.wlgb.mapper.WlgbYddMdxfjlMapper;
import com.wlgb.service.WlgbYddMdxfjlService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/10 23:33
 */
@Service
public class WlgbYddMdxfjlServiceImpl implements WlgbYddMdxfjlService {
    @Resource
    private WlgbYddMdxfjlMapper wlgbYddMdxfjlMapper;

    @Override
    public void save(WlgbYddMdxfjl wlgbYddMdxfjl) {
        wlgbYddMdxfjl.setCreateTime(new Date());
        wlgbYddMdxfjlMapper.insertSelective(wlgbYddMdxfjl);
    }

    @Override
    public void updateById(WlgbYddMdxfjl wlgbYddMdxfjl) {
        wlgbYddMdxfjl.setUpdateTime(new Date());
        wlgbYddMdxfjlMapper.updateByPrimaryKeySelective(wlgbYddMdxfjl);
    }

    @Override
    public WlgbYddMdxfjl queryWlgbYddMdxfjlByWlgbYddMdxfjl(WlgbYddMdxfjl wlgbYddMdxfjl) {
        return wlgbYddMdxfjlMapper.selectOne(wlgbYddMdxfjl);
    }
}
