package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.FwqThbD3c;
import com.wlgb.mapper.FwqThbD3cMapper;
import com.wlgb.mapper1.WeiLianMapper;
import com.wlgb.service2.FwqThbD3cService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
@DS(value = "second")
public class FwqThbD3cServiceImpl implements FwqThbD3cService {
    @Resource
    private FwqThbD3cMapper fwqThbD3cMapper;
    @Resource
    private WeiLianMapper weiLianMapper;

    @Override
    public void save(FwqThbD3c fwqThbD3c) {
        fwqThbD3cMapper.insertSelective(fwqThbD3c);
    }

    @Override
    public FwqThbD3c queryFwqThbD3cByLsh(String lsh) {
        return weiLianMapper.queryFwqThbD3cByLsh(lsh);
    }

    @Override
    public FwqThbD3c selectOne(FwqThbD3c fwqThbD3c) {
        return fwqThbD3cMapper.selectOne(fwqThbD3c);
    }

    @Override
    public void update(Map<String, Object> map) {
        weiLianMapper.updateFwqThbD3cByLsh(map);
    }

    @Override
    public void delete(Map<String, Object> map) {
        weiLianMapper.deleteFwqThbD3cByLsh(map);
    }

    @Override
    public void queryCCGCscthbd1c(String V_QRSJ_S1_14, String V_QRSJ_S2_14) {
        weiLianMapper.queryCCGCscthbd1c(V_QRSJ_S1_14,V_QRSJ_S2_14);
    }

    @Override
    public void queryCCGCscthbd2c(String V_QRSJ_S1_14, String V_QRSJ_S2_14) {
        weiLianMapper.queryCCGCscthbd2c(V_QRSJ_S1_14,V_QRSJ_S2_14);

    }
}
