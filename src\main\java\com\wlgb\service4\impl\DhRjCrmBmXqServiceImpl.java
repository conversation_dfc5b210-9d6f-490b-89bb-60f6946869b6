package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmBmxq;
import com.wlgb.mapper.CrmBmxqMapper;
import com.wlgb.service4.DhRjCrmBmXqService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 23:33
 */
@Service
@DS("fourth")
public class DhRjCrmBmXqServiceImpl implements DhRjCrmBmXqService {
    @Resource
    private CrmBmxqMapper crmBmxqMapper;

    @Override
    public void save(CrmBmxq crmBmxq) {
        crmBmxqMapper.insertSelective(crmBmxq);
    }
}
