package com.wlgb.mapper1;

import com.wlgb.config.DingdingEmployee;
import com.wlgb.config.Dingkey;
import com.wlgb.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/19 12:04
 */
@Mapper
public interface DhRjMapper {
    Integer queryCountDhRjXydSfCz(DhrjXyd dhrjXyd);

    DingdingEmployee queryDhRjDingDingEmployeeByUserId(@Param("userid") String userId);

    DingdingEmployee queryDhRjDingDingEmployeeByName(@Param("name") String name);

    Integer queryDhRjXydMothCountByXfd(@Param("xfd") String xfd);

    Integer queryDhRjXydDateCountByXfd(@Param("xfd") String xfd);

    Integer queryDhRjXydYesCountByXfd(@Param("xfd") String xfd);

    Double queryDhRjXdYjByXfd(@Param("xfd") String xfd);

    List<CrmCs> queryCrmCsList();

    List<CrmKhzt> queryCrmKhZtList(@Param("lxid") Integer lxid);

    Integer queryCrmDhSfcz(Map<String, Object> map);

    Integer queryCrmWxhCount(@Param("qkhwxh") String qkhwxh);

    Integer queryCrmHmCount(@Param("dhhm") String dhhm);

    List<CrmCsfq> queryCrmCsSfqByCsName(@Param("csname") String csName);

    String queryCrmBbh();

    List<CrmQbkh> queryCrmDataImport(Map<String, Object> map);

    Integer queryCrmDataImportCount(Map<String, Object> map);

    Integer queryCmrCsFqIdByFqName(@Param("fqname") String fqName);

    List<CrmBmxq> queryCrmBmCxByBmDa(@Param("bmda") String bmDa);

    List<CrmQbkh> queryCrmCcSjByCcData(@Param("cc") String cc);

    List<CrmCsfq> queryCrmCsFqList();

    Dingkey queryDingKeyById(@Param("id") String id);

    DingDingToken queryDingDingTokenByName(@Param("name") String name);

    List<CrmUser> queryCrmUserList();

    void qkCrmBmXqTable();

    void qkCrmBmTable();

    void qkCrmUserBackupsTable();

    void bfCrmUserInBackups();

    void qkCrmUserCopyTable();

    void saveCrmUserCopy(CrmUser crmUser);

    void delCrmUserCopy();

    void qkCrmUserTable();

    void saveCrmUser();
}
