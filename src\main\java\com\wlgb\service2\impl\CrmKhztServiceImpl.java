package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.CrmKhzt;
import com.wlgb.mapper.CrmKhztMapper;
import com.wlgb.service2.CrmKhztService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 17:59
 */
@Service
@DS(value = "second")
public class CrmKhztServiceImpl implements CrmKhztService {
    @Resource
    private CrmKhztMapper crmKhztMapper;

    @Override
    public void save(CrmKhzt crmKhzt) {
        crmKhztMapper.insertSelective(crmKhzt);
    }

    @Override
    public void updateById(CrmKhzt crmKhzt) {
        crmKhztMapper.updateByPrimaryKeySelective(crmKhzt);
    }

    @Override
    public CrmKhzt queryCrmKhZtByCrmKhZt(CrmKhzt crmKhzt) {
        return crmKhztMapper.selectOne(crmKhzt);
    }
}
