package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/26 18:59
 */
@Data
@Table(name = "ldt_bjjl_zbd")
public class BjjlZbd {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Integer id;
    /**笔记记录唯一标识*/
    private String bjjlwybs;
    /**浏览量*/
    private Integer llnum;
    /**子表单唯一标识*/
    private String zbdwybs;
    /**实例id*/
    private String slid;
}
