package com.wlgb.controller;

import com.wlgb.config.DateFormatConfig;
import com.wlgb.config.FileConfig;
import com.wlgb.config.IdConfig;
import com.wlgb.config.PathUtil;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Date;

/**
 * @Description: Class
 * @author: xuzhenfei
 * @date: 2023年05月14日 16:18
 */

public class Test {
    public static void main(String[] args) {
//        String pdf = FileConfig.getFileAbsolutePath2("static" + File.separator + "uploadFiles" + File.separator +
//                "file");
        String id = "DZHC" + DateFormatConfig.df2(new Date()) + IdConfig.uuId();
        String fileAbsolutePath = FileConfig.getFileAbsolutePath("static" + File.separator + "img", id + ".pdf");
        System.out.println(fileAbsolutePath);
    }




}
