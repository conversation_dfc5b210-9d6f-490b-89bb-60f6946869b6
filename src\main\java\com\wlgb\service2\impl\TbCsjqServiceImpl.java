package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.TbCsjq;
import com.wlgb.mapper.TbCsjqMapper;
import com.wlgb.service2.TbCsjqService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/7 16:51
 */
@Service
@DS(value = "second")
public class TbCsjqServiceImpl implements TbCsjqService {
    @Resource
    private TbCsjqMapper tbCsjqMapper;

    @Override
    public void save(TbCsjq tbCsjq) {
        tbCsjqMapper.insertSelective(tbCsjq);
    }

    @Override
    public void updateById(TbCsjq tbCsjq) {
        tbCsjqMapper.updateByPrimaryKeySelective(tbCsjq);
    }

    @Override
    public TbCsjq getById(Integer jid) {
        return tbCsjqMapper.selectByPrimaryKey(jid);
    }
}
