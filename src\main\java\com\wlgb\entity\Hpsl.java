package com.wlgb.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "ldt_hpsl")
public class Hpsl {


	@Id
	@KeySql(useGeneratedKeys = true)
	private Integer id;

	/**
	 * 店长
	 */
	private String dz;

	/**
	 * 大区
	 */
	private String dq;

	/**
	 * 区域
	 */
	private String qy;

	/**
	 * 日期
	 */
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	private Date rq;

	/**
	 * 优质好评数量
	 */
	private Integer sl;
	/**
	 * 简单好评数量
	 */
	private Integer jdsl;
	/**
	 * 失误次数
	 */
	private Integer swcs;
	/**
	 * 留存数量
	 */
	private Integer lcsl;

	/**
	 * 流水号
	 */
	private java.lang.String lsh;
	/**
	 * 备注
	 */
	private java.lang.String bz;
	/**
	 * 好评渠道
	 */
	private java.lang.String hpqd;
	/**
	 * 统计人员
	 */
	private java.lang.String tjry;
	/**
	 * 城市
	 */
	private java.lang.String city;
	/**
	 * 部门
	 */
	private java.lang.String bm;
	/**
	 * 部门id
	 */
	private java.lang.String bmid;
	/**
	 * 唯一标识
	 */
	private java.lang.String wybs;
	/**
	 * 实例id
	 */
	private java.lang.String slid;
	/**
	 * 登记人id
	 */
	private java.lang.String djrid;
	/**
	 * 创建时间
	 */
	private java.util.Date cjtime;
	private java.lang.String sfwc;
}