package com.wlgb.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2DepartmentListsubRequest;
import com.dingtalk.api.request.OapiV2UserListRequest;
import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.taobao.api.ApiException;
import com.wlgb.config.DingToken;
import com.wlgb.config.Dingkey;
import com.wlgb.config.Result;
import com.wlgb.entity.DdconfigInfo;
import com.wlgb.entity.Department;
import com.wlgb.entity.Employee;
import com.wlgb.service.WeiLianDdXcxService;
import com.wlgb.service2.DepartmentService;
import com.wlgb.service2.EmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.UUID;

import static com.wlgb.config.Tools.isEmpty;

@RestController
@RequestMapping(value = "/wlgb/Syndata")
public class SyndataController {

    @Autowired
    private WeiLianDdXcxService weiLianDdXcxService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private EmployeeService employeeService;

    @RequestMapping(value = "/toSyndata")
    @ResponseBody
    public Result toSyndata() throws Exception {
        return tosyndata2();
    }

    public Result tosyndata2() throws Exception {
        String token = null;
        try {
            Dingkey dingkey = weiLianDdXcxService.queryDingKeyById("weilianxcx");
            token = DingToken.token(dingkey);
        } catch (ApiException e) {
            e.printStackTrace();
        }
        //获取部门数据信息，将所有的钉钉用户添加到employeeList，员工信息添加到departmentList
        getAllDepartment(token, 1L);
        return Result.OK();
    }

    /**
     * 通过递归获取所有部门
     *
     * @param token
     * @param deptId
     * @throws Exception
     */
    private void getAllDepartment(String token, Long deptId) throws Exception {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        req.setDeptId(deptId);
        req.setLanguage("zh_CN");
        OapiV2DepartmentListsubResponse rsp = client.execute(req, token);
        JSONObject bodyjson = JSONObject.parseObject(rsp.getBody());
        JSONArray resultArr = JSONArray.parseArray(bodyjson.getString("result"));
        long errcode = Long.parseLong(bodyjson.getString("errcode"));
        if (errcode == 0 && !resultArr.isEmpty()) {
            for (Object o : resultArr) {
                JSONObject jsonpois = (JSONObject) o;
                Department dD = new Department();
                dD.setCorpid("ding291dcdb4d2ea030935c2f4657eb6378f");
                dD.setDepartname(jsonpois.getString("name"));
                dD.setParentid(String.valueOf(jsonpois.getLong("parent_id")));
                dD.setDingdingid(String.valueOf(jsonpois.getLong("dept_id")));
                dD.setId(String.valueOf(UUID.randomUUID()).replaceAll("-", ""));
                Department dd2 = departmentService.queryCountByDingDingId(dD);
                if (dd2 == null) {
                    departmentService.save(dD);
                } else {
                    departmentService.updateById(dD);
                }
                getAllEmployee(token, deptId, 0L);
                deptId = Long.parseLong(String.valueOf(jsonpois.getLong("dept_id")));
                //采用递归来更新部门
                getAllDepartment(token, deptId);
            }
        } else {
            System.out.println("循环完毕errcode=" + errcode);
        }
    }

    /**
     * 获取部门所有成员
     *
     * @param token
     * @param deptId
     * @param next_cursor
     */
    private void getAllEmployee(String token, Long deptId, Long next_cursor) {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
            OapiV2UserListRequest req = new OapiV2UserListRequest();
            req.setDeptId(deptId);
            req.setCursor(next_cursor);
            req.setSize(100L);
            req.setOrderField("modify_desc");
            req.setContainAccessLimit(false);
            req.setLanguage("zh_CN");
            OapiV2UserListResponse rsp = client.execute(req, token);
            JSONObject bodyjson = JSONObject.parseObject(rsp.getBody());
            String errmsg = bodyjson.getString("errmsg");
            if ("ok".equals(errmsg)) {
                JSONObject resultObject = JSONObject.parseObject(bodyjson.getString("result"));
                JSONArray jsonArray = resultObject.getJSONArray("list");
                for (Object o : jsonArray) {
                    JSONObject jsono = (JSONObject) o;
                    System.out.println("jsono=" + jsono);
                    Employee em = new Employee();
                    em.setUserid(jsono.getString("userid"));
                    em.setName(jsono.getString("name"));
                    JSONArray bmlist = jsono.getJSONArray("dept_id_list");
                    String bm = "";
                    for (Object oo : bmlist) {
                        bm += oo + "|";
                    }
                    em.setDepartid(bm.substring(0, bm.length() - 1));
                    em.setActive(jsono.getBoolean("active") ? "1" : "0");
                    em.setAvatar(jsono.getString("avatar"));
                    em.setPosition(jsono.getString("title"));
                    em.setMobile(jsono.getString("mobile"));
                    em.setWorkplace(jsono.getString("work_place"));
                    em.setEmail(jsono.getString("org_email"));
                    em.setJobnumber(jsono.getString("job_number"));
                    Employee em2 = employeeService.queryCountByUserId(em.getUserid());
                    if (em2 == null) {
                        employeeService.save(em);
                    } else {
                        employeeService.updateById(em);
                    }
                }
            }
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }


    @RequestMapping(value = "/getUserBySjhAndName")
    @ResponseBody
    public Result getUserBySjhAndName(HttpServletRequest request) throws Exception {
        String sjh = request.getParameter("sjh");
        String name = request.getParameter("name");
        if (isEmpty(sjh)) {
            return Result.error("手机号不能为空");
        }
        if (isEmpty(name)) {
            return Result.error("姓名不能为空");
        }
        Employee employee = employeeService.selectBySjhAndName(sjh, name);
        return Result.OK(employee);
    }

}

