package com.wlgb.service4.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.DingDingToken;
import com.wlgb.mapper.DingDingTokenMapper;
import com.wlgb.service4.DingDingTokenService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/26 20:25
 */
@Service
@DS("fourth")
public class DingDingTokenServiceImpl implements DingDingTokenService {
    @Resource
    private DingDingTokenMapper dingDingTokenMapper;

    @Override
    public void save(DingDingToken dingDingToken) {
        dingDingToken.setUpdateTime(new Date());
        dingDingTokenMapper.insertSelective(dingDingToken);
    }

    @Override
    public void updateById(DingDingToken dingDingToken) {
        dingDingToken.setUpdateTime(new Date());
        dingDingTokenMapper.updateByPrimaryKeySelective(dingDingToken);
    }
}
