package com.wlgb.entity;

import java.io.Serializable;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 预留下单
 * @Author: jeecg-boot
 * @Date:   2021-08-20
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_ylxd")
public class WlgbYlxd implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
	/**创建人*/
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;
	/**更新人*/
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;
	/**所属部门*/
    private java.lang.String sysOrgCode;
	/**协议单id*/
    private java.lang.String xid;
	/**订单编号*/
    private java.lang.String xddbh;
	/**别墅名称*/
    private java.lang.String xbsname;
	/**别墅id*/
    private java.lang.String xbsmc;
	/**进场时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date xjctime;
	/**退长时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date xtctime;
	/**场次*/
    private java.lang.Integer xcc;
	/**已收定金*/
    private java.lang.Double xysdj;
	/**场地费*/
    private java.lang.Double xqkzj;
	/**提交时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date ytitime;
	/**预留人*/
    private java.lang.String xfd;
	/**预留人id*/
    private java.lang.String ylrid;
	/**是否删除(0:否,1:是)*/
    private java.lang.Integer sfsc;

	/**定金图片*/
	private java.lang.String ydjtp;
    //删除人名字
	private java.lang.String scrname;
    //删除人id
	private java.lang.String scrid;

    /**删除时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private java.util.Date scitime;

    //优惠后价格
    private java.lang.Double xhfyj;

    //包/订餐数量
    private java.lang.Integer xbdsl;

    // 烧烤套餐数量
    private java.lang.Integer xsksl;

    //主题总额
    private java.lang.Integer xztze;

    //修改或协调状态 1修改 2协调
    private java.lang.String xxgorxt;

    //订单类型:5为预留单
    private java.lang.String xddtype;

    //是否需要轰趴师 0：否，1：是
    private java.lang.Integer xsfhps;

    // 是否特殊场次  0：否，1：是
    private java.lang.Integer sftscc;

    // 0:否、1:是  是否允许协调
    private java.lang.Integer xyxxt;

    //补交定金
    private java.lang.Double xbjdj;

    //是否预留金 0：否，1：是
    private java.lang.Integer sfylj;

    //qqid
    private java.lang.String qqid;
    //slid
    private java.lang.String slid;
}
