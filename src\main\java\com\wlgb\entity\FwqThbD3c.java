package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Table(name = "fwq_thb_d3c")
public class FwqThbD3c {
    @Id
    @KeySql(useGeneratedKeys = true)
    private int id;
    private String lx;
    private String xm;
    private String xid;
    private String kyj;
    private String jyj;
    private String yjsm;
    private String yjy;
    private String jctime;
    private Long gsrq;//归属日期
    private String fdxm;
    private String ywy;
    private Long sjssrq;
    private String byj;
    private String lsh;

    private String employeeField_km7687p6; //姓名

    private String numberField_km7687p4; //扣业绩

    private String numberField_km7687p5; //补业绩

    private Long dateField_km7687p8; //时间所属日期

    //唯一标志
    private String wybs;


}
