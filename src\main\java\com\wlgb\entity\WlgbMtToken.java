package com.wlgb.entity;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @Description: 美团token
 * @Author: jeecg-boot
 * @Date: 2020-12-28
 * @Version: V1.0
 */
@Data
@Table(name = "wlgb_mt_token")
public class WlgbMtToken {
    @Id
    @KeySql(useGeneratedKeys = true)
    private java.lang.String id;
    /**
     * 创建人
     */
    private java.lang.String createBy;
    /**
     * 创建日期
     */
    private java.util.Date createTime;
    /**
     * 更新人
     */
    private java.lang.String updateBy;
    /**
     * 更新日期
     */
    private java.util.Date updateTime;
    /**
     * 所属部门
     */
    private java.lang.String sysOrgCode;
    /**
     * token
     */
    private java.lang.String token;
    /**
     * appkey
     */
    private java.lang.String appkey;
    /**
     * secret
     */
    private java.lang.String appsecret;
    /**
     * authorization_code
     */
    private java.lang.String grantType;
    /**
     * 获取session的授权码
     */
    private java.lang.String authCode;
    /**
     * 获取授权码的回调地址
     */
    private java.lang.String redirectUrl;
    /**
     * 失效时间
     */
    private java.util.Date endtime;
    /**
     * 备注
     */
    private java.lang.String remake;
    /**
     * 账号
     */
    private java.lang.String zh;
    /**
     * 客户Id
     */
    private java.lang.String bid;
    /**
     * refresh_session
     */
    private java.lang.String refreshToken;
    /**
     * 次数
     */
    private java.lang.Integer remainRefreshCount;
    /**
     * 是否删除
     */
    private java.lang.String sfsc;
    /**
     * 绑定的营业执照
     */
    private java.lang.String bdyyzz;
    /**
     * 备注
     */
    private java.lang.String bz;
    /**
     * 对应的美团开发者id
     */
    private java.lang.String mtdeveloperid;
    /**
     * 对应的美团开发者singkey
     */
    private java.lang.String mtsingkey;

    /**
     * 对应的美团开发者id
     */
    private java.lang.String mttoken;
    /**
     * 对应的美团开发者id
     */
    private java.lang.String mtrefreshtoken;
    /**
     * token过期时间
     */
    private java.util.Date mttokenendtime;



}
