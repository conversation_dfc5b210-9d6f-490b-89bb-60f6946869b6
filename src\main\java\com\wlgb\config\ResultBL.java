package com.wlgb.config;

import lombok.Data;

@Data
public class ResultBL<T> {
    private String status;
    private Boolean success;
    private T data;
    private String errorInfo;
    private long timestamp = System.currentTimeMillis();

    public ResultBL<T> success(String message) {
        this.errorInfo = message;
        this.status = "OK";
        this.success = true;
        return this;
    }

    @Deprecated
    public static ResultBL<Object> ok() {
        ResultBL<Object> r = new ResultBL<Object>();
        r.setSuccess(true);
        r.setStatus("OK");
        r.setErrorInfo("成功");
        return r;
    }

    @Deprecated
    public static ResultBL<Object> ok(String msg) {
        ResultBL<Object> r = new ResultBL<Object>();
        r.setSuccess(true);
        r.setStatus("OK");
        r.setErrorInfo(msg);
        return r;
    }

    @Deprecated
    public static ResultBL<Object> ok(Object data) {
        ResultBL<Object> r = new ResultBL<Object>();
        r.setSuccess(true);
        r.setStatus("OK");
        r.setData(data);
        return r;
    }

    public static<T> ResultBL<T> OK() {
        ResultBL<T> r = new ResultBL<T>();
        r.setSuccess(true);
        r.setStatus("OK");
        r.setErrorInfo("成功");
        return r;
    }

    public static<T> ResultBL<T> OK(T data) {
        ResultBL<T> r = new ResultBL<T>();
        r.setSuccess(true);
        r.setStatus("OK");
        r.setData(data);
        return r;
    }

    public static<T> ResultBL<T> OK(String msg, T data) {
        ResultBL<T> r = new ResultBL<T>();
        r.setSuccess(true);
        r.setStatus("OK");
        r.setErrorInfo(msg);
        r.setData(data);
        return r;
    }

    public static<T> ResultBL<T> error(String msg, T data) {
        ResultBL<T> r = new ResultBL<T>();
        r.setSuccess(false);
        r.setStatus("500");
        r.setErrorInfo(msg);
        r.setData(data);
        return r;
    }

    public static ResultBL<Object> error(String msg) {
        return error("500", msg);
    }

    public static ResultBL<Object> error(String code, String msg) {
        ResultBL<Object> r = new ResultBL<Object>();
        r.setStatus(code);
        r.setErrorInfo(msg);
        r.setSuccess(false);
        return r;
    }

    public ResultBL<T> error500(String message) {
        this.errorInfo = message;
        this.status = "500";
        this.success = false;
        return this;
    }
}
