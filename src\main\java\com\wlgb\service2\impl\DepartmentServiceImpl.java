package com.wlgb.service2.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wlgb.entity.Department;
import com.wlgb.mapper.DepartmentMapper;
import com.wlgb.service2.DepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/5 21:31
 */
@Service
@DS(value = "second")
public class DepartmentServiceImpl implements DepartmentService {
    @Resource
    private DepartmentMapper departmentMapper;

    @Override
    public void save(Department department) {
        departmentMapper.insertSelective(department);
    }

    @Override
    public Department queryCountByDingDingId(Department department) {
        return departmentMapper.selectOne(department);
    }

    @Override
    public void updateById(Department department) {
        departmentMapper.updateByPrimaryKeySelective(department);
    }

    @Override
    public void delete(Department department) {
        departmentMapper.delete(department);
    }
}
